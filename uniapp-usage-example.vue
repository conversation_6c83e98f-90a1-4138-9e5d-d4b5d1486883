<template>
  <view class="in-progress-tasks">
    <!-- 搜索表单 -->
    <view class="search-form">
      <uni-forms ref="searchForm" :model="queryParams" label-width="100px">
        <uni-forms-item label="工单编号">
          <uni-easyinput
            v-model="queryParams.orderCode"
            placeholder="请输入工单编号"
            @confirm="handleQuery"
          />
        </uni-forms-item>
        <uni-forms-item label="产品名称">
          <uni-easyinput
            v-model="queryParams.productName"
            placeholder="请输入产品名称"
            @confirm="handleQuery"
          />
        </uni-forms-item>
        <uni-forms-item label="工单类型">
          <uni-data-select
            v-model="queryParams.orderType"
            :localdata="orderTypeOptions"
            placeholder="请选择工单类型"
          />
        </uni-forms-item>
        <view class="search-buttons">
          <button type="primary" @click="handleQuery">搜索</button>
          <button @click="resetQuery">重置</button>
        </view>
      </uni-forms>
    </view>

    <!-- 工单列表 -->
    <view class="task-list" v-if="!loading">
      <view v-if="groupedTasks.length === 0" class="empty-state">
        <uni-empty mode="data" text="暂无进行中任务"></uni-empty>
      </view>

      <view
        v-for="order in groupedTasks"
        :key="order.key"
        class="work-order-card"
        @click="showWorkOrderDetail(order)"
      >
        <view class="card-header">
          <text class="order-code">{{ order.orderCode }}</text>
          <uni-tag text="进行中" type="warning" size="small" />
        </view>

        <view class="card-body">
          <view class="product-summary">
            <text>{{ getProductNamesText(order.products) }}</text>
          </view>
          <view class="task-summary">
            <view class="summary-item">
              <text class="label">总工序</text>
              <text class="value">{{ order.totalStepTasks }}</text>
            </view>
            <view class="summary-item my-tasks">
              <text class="label">我的任务</text>
              <text class="value">{{ order.myTasksCount }}</text>
            </view>
          </view>
        </view>

        <view class="card-footer">
          <uni-tag
            :text="getOrderTypeText(order.orderType)"
            :type="getOrderTypeTagType(order.orderType)"
            size="small"
          />
          <text class="creation-time">{{ parseTime(order.orderCreatedTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <!-- 分页 -->
    <view class="pagination" v-if="total > 0">
      <uni-pagination
        :current="queryParams.pageNum"
        :total="total"
        :pageSize="queryParams.pageSize"
        @change="onPageChange"
      />
    </view>

    <!-- 工单详情弹窗 -->
    <uni-popup ref="orderDetailPopup" type="bottom" :safe-area="false">
      <view class="order-detail-popup">
        <view class="popup-header">
          <text class="popup-title">工单详情 - {{ selectedOrder.orderCode }}</text>
          <text class="close-btn" @click="closeOrderDetail">×</text>
        </view>

        <scroll-view scroll-y class="popup-content">
          <!-- 库存信息 -->
          <view class="inventory-section" v-if="inventoryData">
            <view class="section-title">库存信息</view>
            <view class="inventory-grid">
              <view class="warehouse-item">
                <text class="warehouse-name">贴片仓</text>
                <view class="stock-info">
                  <text>上板: {{ inventoryData.smdStock?.upperBoard || 0 }}</text>
                  <text>下板: {{ inventoryData.smdStock?.lowerBoard || 0 }}</text>
                </view>
              </view>
              <view class="warehouse-item">
                <text class="warehouse-name">线边仓</text>
                <view class="stock-info">
                  <text>上板: {{ inventoryData.lineStock?.upperBoard || 0 }}</text>
                  <text>下板: {{ inventoryData.lineStock?.lowerBoard || 0 }}</text>
                </view>
              </view>
              <view class="warehouse-item">
                <text class="warehouse-name">原料仓</text>
                <view class="stock-info">
                  <text>上板: {{ inventoryData.rawStock?.upperBoard || 0 }}</text>
                  <text>下板: {{ inventoryData.rawStock?.lowerBoard || 0 }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 产品任务列表 -->
          <view v-for="product in selectedOrder.products" :key="product.productId" class="product-section">
            <view class="product-header">
              <text class="product-name">{{ product.productName }}</text>
              <text class="product-style">{{ product.styleName }}</text>
              <button size="mini" @click="viewBomList(product.productName, product.styleName)">
                查看BOM清单
              </button>
            </view>

            <!-- 工序任务列表 -->
            <view class="step-tasks">
              <view
                v-for="task in product.stepTasks"
                :key="task.stepTaskId"
                class="task-item"
                :class="{
                  'my-task': task.assignee === currentUser.nickName,
                  'completed': task.isCompleted === 2,
                  'processing': task.isCompleted === 1
                }"
              >
                <view class="task-info">
                  <text class="step-name">{{ task.stepName }}</text>
                  <uni-tag
                    :text="getStepStatusText(task.isCompleted)"
                    :type="getStepStatusTagType(task.isCompleted)"
                    size="small"
                  />
                </view>

                <view class="task-assignee">
                  <text>负责人: {{ task.assignee || '未分配' }}</text>
                </view>

                <view class="task-actions">
                  <button
                    size="mini"
                    type="primary"
                    @click="handleStart(task)"
                    v-if="task.isCompleted === 0 && task.assignee === currentUser.nickName"
                  >
                    开始
                  </button>
                  <button
                    size="mini"
                    type="default"
                    @click="handleComplete(task)"
                    v-if="task.isCompleted === 1 && task.assignee === currentUser.nickName"
                  >
                    完成
                  </button>
                  <button
                    size="mini"
                    @click="handleDefectSubmit(task)"
                    v-if="task.assignee === currentUser.nickName"
                  >
                    提交缺陷
                  </button>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- 缺陷提交弹窗 -->
    <uni-popup ref="defectPopup" type="center">
      <view class="defect-popup">
        <view class="popup-header">
          <text class="popup-title">提交不良品数</text>
        </view>

        <view class="defect-content">
          <view class="step-info">
            <text>工序: {{ currentStepTask.stepName }}</text>
            <text>编码: {{ currentStepTask.stepNumber }}</text>
          </view>

          <view class="defect-list" v-if="defectiveNames.length > 0">
            <view v-for="defectName in defectiveNames" :key="defectName" class="defect-item">
              <text class="defect-name">{{ defectName }}</text>
              <view class="defect-input">
                <button @click="decreaseDefectCount(defectName)">-</button>
                <input
                  type="number"
                  v-model.number="defectQuantities[defectName]"
                  @input="validateDefectCount(defectName, $event)"
                />
                <button @click="increaseDefectCount(defectName)">+</button>
              </view>
            </view>
          </view>

          <view v-else class="no-defects">
            <text>该工序未设置缺陷类型</text>
          </view>
        </view>

        <view class="popup-actions">
          <button @click="closeDefectPopup">取消</button>
          <button type="primary" @click="submitDefects" :disabled="defectiveNames.length === 0">
            提交
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
// 导入 API 接口
import {
  getOrderDetailPage,
  getUserProfile,
  updateStepTaskStatus,
  getProcessRouteInfo,
  addDefects,
  getStepTaskDetail,
  updateOrderStatus,
  queryAllInventory,
  getBomByModelAndStyle,
  parseTime,
  showMessage,
  showConfirm
} from './uniapp-api-implementation.js'

export default {
  name: 'InProgressTasks',
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderCode: '',
        productName: '',
        orderType: '',
        orderStatus: 'IN_PROGRESS', // 固定为进行中
        orderDispatchStatus: 'DISPATCHED', // 固定为已下发
        assignee: '', // 当前用户
        sortOrder: 'desc'
      },

      // 工单类型选项
      orderTypeOptions: [
        { value: '', text: '全部' },
        { value: 'URGENT', text: '紧急' },
        { value: 'NORMAL', text: '普通' }
      ],

      // 数据
      groupedTasks: [],
      total: 0,

      // 当前用户信息
      currentUser: {
        userId: null,
        nickName: null
      },

      // 工单详情相关
      selectedOrder: {},
      inventoryData: null,

      // 缺陷提交相关
      currentStepTask: {},
      defectiveNames: [],
      defectQuantities: {},
      submittingDefects: false
    }
  },

  computed: {
    // 计算总缺陷数量
    totalDefectCount() {
      return Object.values(this.defectQuantities).reduce((sum, count) => sum + (count || 0), 0)
    }
  },

  async onLoad() {
    await this.getCurrentUserInfo()
    await this.getList()
  },

  async onShow() {
    // 页面显示时刷新数据
    await this.getList()
  },

  methods: {
    /** 获取当前登录用户信息 */
    async getCurrentUserInfo() {
      try {
        // 优先从本地存储获取用户信息
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.currentUser = userInfo
          this.queryParams.assignee = userInfo.nickName
          return
        }

        // 调用 API 获取用户信息
        const response = await getUserProfile()
        if (response.code === 200 && response.data.user) {
          const user = response.data.user
          this.currentUser = {
            userId: user.userId,
            nickName: user.nickName
          }
          this.queryParams.assignee = user.nickName

          // 缓存用户信息
          uni.setStorageSync('userInfo', this.currentUser)
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        showMessage('获取用户信息失败', 'error')
      }
    },

    /** 查询任务列表 */
    async getList() {
      if (!this.currentUser.nickName) {
        console.warn('用户信息不存在，无法查询任务')
        return
      }

      this.loading = true

      try {
        const queryParams = {
          ...this.queryParams,
          _t: Date.now()
        }

        const response = await getOrderDetailPage(queryParams)
        console.log('工单详细信息API响应:', response)

        let records = []
        let backendTotal = 0

        if (response.data && response.data.records !== undefined) {
          records = response.data.records || []
          backendTotal = response.data.total || 0
        } else if (response.rows !== undefined) {
          records = response.rows || []
          backendTotal = response.total || 0
        } else if (response.data && Array.isArray(response.data)) {
          records = response.data
          backendTotal = response.data.length
        }

        this.processOrderData(records, backendTotal)
      } catch (error) {
        console.error('获取工单详细信息失败:', error)
        showMessage('获取工单详细信息失败', 'error')
        this.groupedTasks = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    /** 处理后端返回的工单数据 */
    processOrderData(orderRecords, backendTotal) {
      this.groupedTasks = []

      // 过滤只保留进行中且已下发的工单
      const filteredOrders = orderRecords.filter(order =>
        order.orderStatus === 'IN_PROGRESS'
      )

      filteredOrders.forEach(order => {
        let totalStepTasks = 0
        let myTasksCount = 0
        const processedProducts = []
        let hasMyTaskInOrder = false

        if (order.products && order.products.length > 0) {
          order.products.forEach(product => {
            const enrichedStepTasks = []
            let hasMyTask = false

            if (product.stepTasks && product.stepTasks.length > 0) {
              product.stepTasks.forEach(stepTask => {
                const enrichedStepTask = {
                  ...stepTask,
                  orderId: order.orderId,
                  orderCode: order.orderCode,
                  orderType: order.orderType,
                  orderStatus: order.orderStatus,
                  orderCreatedTime: order.orderCreatedTime,
                  productId: product.productId,
                  productName: product.productName,
                  itemId: product.itemId,
                  itemName: product.itemName,
                  processRouteCode: product.processRouteCode,
                  taskLevel: product.taskLevel,
                  taskStatus: product.taskStatus,
                  orderTaskQuantity: product.orderTaskQuantity,
                  orderTaskCreateTime: product.orderTaskCreateTime,
                  expectedTime: product.expectedTime,
                  completedTime: product.completedTime,
                  isDefer: product.isDefer
                }

                enrichedStepTasks.push(enrichedStepTask)
                totalStepTasks++

                if (stepTask.assignee === this.currentUser.nickName) {
                  hasMyTask = true
                  hasMyTaskInOrder = true
                }
              })
            }

            if (hasMyTask) {
              myTasksCount++
            }

            processedProducts.push({
              ...product,
              stepTasks: enrichedStepTasks
            })
          })
        }

        if (hasMyTaskInOrder) {
          const orderKey = `order_${order.orderId}`
          this.groupedTasks.push({
            key: orderKey,
            orderId: order.orderId,
            orderCode: order.orderCode,
            orderType: order.orderType,
            orderStatus: order.orderStatus,
            orderCreatedTime: order.orderCreatedTime,
            products: processedProducts,
            totalStepTasks: totalStepTasks,
            myTasksCount: myTasksCount
          })
        }
      })

      this.total = backendTotal
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        orderCode: '',
        productName: '',
        orderType: '',
        orderStatus: 'IN_PROGRESS',
        orderDispatchStatus: 'DISPATCHED',
        assignee: this.currentUser.nickName,
        sortOrder: 'desc'
      }
      this.handleQuery()
    },

    /** 分页变化 */
    onPageChange(e) {
      this.queryParams.pageNum = e.current
      this.getList()
    },

    /** 显示工单详情 */
    async showWorkOrderDetail(order) {
      this.selectedOrder = order
      this.$refs.orderDetailPopup.open()

      // 查询库存信息
      await this.queryInventoryForOrder(order)
    },

    /** 关闭工单详情 */
    closeOrderDetail() {
      this.$refs.orderDetailPopup.close()
      this.selectedOrder = {}
      this.inventoryData = null
    },

    /** 查询工单中所有产品的库存信息 */
    async queryInventoryForOrder(order) {
      if (!order.products || order.products.length === 0) {
        return
      }

      try {
        // 为每个产品查询库存信息
        const inventoryPromises = order.products.map(product =>
          this.queryInventoryForProduct(product)
        )

        await Promise.all(inventoryPromises)
      } catch (error) {
        console.error('查询库存信息失败:', error)
        showMessage('查询库存信息失败', 'error')
      }
    },

    /** 查询单个产品的库存信息 */
    async queryInventoryForProduct(product) {
      try {
        const productName = product.productName
        const styleId = product.styleId || 0

        const inventoryData = await queryAllInventory(productName, styleId)

        // 使用 Vue.set 或者直接赋值来确保响应式
        if (!this.inventoryData) {
          this.inventoryData = {}
        }
        this.$set(this.inventoryData, product.productId, inventoryData)
      } catch (error) {
        console.error(`查询产品 ${product.productName} 库存信息失败:`, error)
        // 设置默认值
        if (!this.inventoryData) {
          this.inventoryData = {}
        }
        this.$set(this.inventoryData, product.productId, {
          smdStock: { upperBoard: 0, lowerBoard: 0 },
          lineStock: { upperBoard: 0, lowerBoard: 0 },
          rawStock: { upperBoard: 0, lowerBoard: 0 }
        })
      }
    },

    /** 开始任务 */
    async handleStart(task) {
      const confirmed = await showConfirm(`确认开始工序"${task.stepName}"吗？`)
      if (!confirmed) return

      try {
        await updateStepTaskStatus(task.stepTaskId, 1)

        // 如果有关联的工单ID，同时更新工单状态为处理中
        if (task.orderId) {
          await updateOrderStatus(task.orderId, 'IN_PROGRESS').catch(error => {
            console.error('更新工单状态失败:', error)
          })
        }

        showMessage('工序状态更新成功')
        await this.getList()

        // 如果详情弹窗打开，更新详情数据
        if (this.selectedOrder.orderId) {
          const updatedOrder = this.groupedTasks.find(o => o.orderId === this.selectedOrder.orderId)
          if (updatedOrder) {
            this.selectedOrder = updatedOrder
          } else {
            this.closeOrderDetail()
          }
        }
      } catch (error) {
        console.error('更新工序状态失败:', error)
        showMessage('工序状态更新失败', 'error')
      }
    },

    /** 完成任务 */
    async handleComplete(task) {
      const confirmed = await showConfirm(`确认完成工序"${task.stepName}"吗？`)
      if (!confirmed) return

      try {
        await updateStepTaskStatus(task.stepTaskId, 2)
        showMessage('工序状态更新成功')
        await this.getList()

        // 如果详情弹窗打开，更新详情数据
        if (this.selectedOrder.orderId) {
          const updatedOrder = this.groupedTasks.find(o => o.orderId === this.selectedOrder.orderId)
          if (updatedOrder) {
            this.selectedOrder = updatedOrder
          } else {
            this.closeOrderDetail()
          }
        }
      } catch (error) {
        console.error('更新工序状态失败:', error)
        showMessage('工序状态更新失败', 'error')
      }
    },

    /** 提交缺陷 */
    async handleDefectSubmit(task) {
      this.currentStepTask = task
      this.$refs.defectPopup.open()

      // 加载缺陷类型
      await this.loadDefectiveNames(task.stepId)
    },

    /** 关闭缺陷弹窗 */
    closeDefectPopup() {
      this.$refs.defectPopup.close()
      this.resetDefectDialog()
    },

    /** 加载工序可能出现的缺陷类型 */
    async loadDefectiveNames(stepId) {
      if (!stepId) {
        showMessage('工序ID不存在', 'error')
        return
      }

      try {
        const response = await getProcessRouteInfo(stepId)
        if (response.data && response.data.defectiveNames && response.data.defectiveNames.trim()) {
          // 解析defectiveNames字符串，按逗号分割
          const defectNames = response.data.defectiveNames.split(',').map(name => name.trim()).filter(name => name)
          this.defectiveNames = defectNames

          // 初始化数量对象
          this.defectQuantities = {}
          defectNames.forEach(name => {
            this.$set(this.defectQuantities, name, 0)
          })

          // 尝试加载已有缺陷信息
          if (this.currentStepTask.stepTaskId) {
            await this.loadExistingDefects(this.currentStepTask.stepTaskId)
          }
        } else {
          this.defectiveNames = []
          const procedureName = response.data?.procedureName || '未知工序'
          showMessage(`工序"${procedureName}"暂未设置缺陷类型`, 'warning')
        }
      } catch (error) {
        console.error('获取工序缺陷类型失败:', error)
        showMessage('获取工序缺陷类型失败', 'error')
        this.defectiveNames = []
      }
    },

    /** 加载已有的缺陷信息 */
    async loadExistingDefects(stepTaskId) {
      if (!stepTaskId) return

      try {
        const response = await getStepTaskDetail(stepTaskId)
        if (response.data && response.data.defectInfo) {
          const existingDefects = response.data.defectInfo || {}

          // 将已有缺陷信息合并到defectQuantities中
          Object.keys(existingDefects).forEach(defectName => {
            if (this.defectQuantities.hasOwnProperty(defectName)) {
              this.$set(this.defectQuantities, defectName, existingDefects[defectName] || 0)
            }
          })
        }
      } catch (error) {
        console.error('加载已有缺陷信息失败:', error)
        // 这里不显示错误消息，因为可能是首次提交，没有历史数据是正常的
      }
    },

    /** 提交缺陷信息 */
    async submitDefects() {
      // 获取所有缺陷数据（包括数量为0的，用于覆盖之前的数据）
      const allDefects = {}
      Object.keys(this.defectQuantities).forEach(defectName => {
        const quantity = this.defectQuantities[defectName] || 0
        allDefects[defectName] = quantity
      })

      // 提交数据
      const submitData = {
        stepTaskId: this.currentStepTask.stepTaskId,
        defects: allDefects
      }

      this.submittingDefects = true

      try {
        await addDefects(submitData)

        const validDefects = Object.keys(allDefects).filter(key => allDefects[key] > 0)
        if (validDefects.length > 0) {
          showMessage('不良品数提交成功')
        } else {
          showMessage('确认无不良品，提交成功')
        }

        this.closeDefectPopup()
        await this.getList()
      } catch (error) {
        console.error('提交不良品数失败:', error)
        showMessage('提交不良品数失败', 'error')
      } finally {
        this.submittingDefects = false
      }
    },

    /** 重置缺陷对话框 */
    resetDefectDialog() {
      this.currentStepTask = {}
      this.defectiveNames = []
      this.defectQuantities = {}
      this.submittingDefects = false
    },

    /** 减少缺陷数量 */
    decreaseDefectCount(defectName) {
      if (this.defectQuantities[defectName] > 0) {
        this.defectQuantities[defectName]--
      }
    },

    /** 增加缺陷数量 */
    increaseDefectCount(defectName) {
      this.defectQuantities[defectName]++
    },

    /** 验证缺陷数量 */
    validateDefectCount(defectName, event) {
      const value = event.detail.value
      const num = parseInt(value)
      if (isNaN(num) || num < 0) {
        this.$set(this.defectQuantities, defectName, 0)
      } else if (num > 99999) {
        this.$set(this.defectQuantities, defectName, 99999)
      } else {
        this.$set(this.defectQuantities, defectName, num)
      }
    },

    /** 查看BOM清单 */
    async viewBomList(productName, styleName) {
      if (!productName) {
        showMessage('产品名称不能为空', 'warning')
        return
      }

      try {
        const response = await getBomByModelAndStyle(productName, styleName)
        if (response.code === 200) {
          const bomData = response.data || []
          if (bomData.length === 0) {
            showMessage(`产品 ${productName} 暂无BOM清单数据`, 'info')
          } else {
            // 这里可以跳转到BOM清单页面或显示BOM清单弹窗
            uni.navigateTo({
              url: `/pages/bom/bomList?productName=${encodeURIComponent(productName)}&styleName=${encodeURIComponent(styleName)}`
            })
          }
        } else {
          showMessage(response.msg || '获取BOM清单失败', 'error')
        }
      } catch (error) {
        console.error('获取BOM清单失败:', error)
        showMessage('获取BOM清单失败，请稍后重试', 'error')
      }
    },

    /** 工具方法 */
    getOrderTypeText(orderType) {
      const typeMap = {
        'URGENT': '紧急',
        'NORMAL': '普通'
      }
      return typeMap[orderType] || orderType
    },

    getOrderTypeTagType(orderType) {
      const typeMap = {
        'URGENT': 'error',
        'NORMAL': 'primary'
      }
      return typeMap[orderType] || 'default'
    },

    getProductNamesText(products) {
      if (!products || products.length === 0) {
        return '无产品信息'
      }

      const productTexts = products.map(p => {
        if (!p.productName) return null

        if (p.styleName && p.styleName.trim()) {
          return `${p.productName} - ${p.styleName}`
        }
        return p.productName
      }).filter(Boolean)

      if (productTexts.length === 0) {
        return '无产品名称'
      }

      if (productTexts.length === 1) {
        return productTexts[0]
      }

      return `${productTexts[0]} 等 ${productTexts.length} 个产品`
    },

    getStepStatusText(isCompleted) {
      const statusMap = {
        0: '未开始',
        1: '执行中',
        2: '已完成'
      }
      return statusMap[isCompleted] || '未知'
    },

    getStepStatusTagType(isCompleted) {
      const statusMap = {
        0: 'info',
        1: 'warning',
        2: 'success'
      }
      return statusMap[isCompleted] || 'default'
    },

    // 使用导入的工具函数
    parseTime
  }
}
</script>
<style scoped>
.in-progress-tasks {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索表单样式 */
.search-form {
  background: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.search-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.search-buttons button {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
}

/* 工单卡片样式 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.work-order-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #f5a623;
  overflow: hidden;
  transition: all 0.3s ease;
}

.work-order-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #e8eaec;
}

.order-code {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-body {
  padding: 30rpx;
}

.product-summary {
  margin-bottom: 24rpx;
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
}

.task-summary {
  display: flex;
  justify-content: space-around;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.summary-item .label {
  font-size: 24rpx;
  color: #999;
}

.summary-item .value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.summary-item.my-tasks .value {
  color: #f5a623;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e8eaec;
}

.creation-time {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
}

/* 加载状态样式 */
.loading-container {
  padding: 60rpx 0;
  text-align: center;
}

/* 分页样式 */
.pagination {
  margin-top: 40rpx;
  padding: 20rpx 0;
}

/* 弹窗样式 */
.order-detail-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e8eaec;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  max-height: calc(80vh - 120rpx);
  padding: 30rpx;
}

/* 库存信息样式 */
.inventory-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.inventory-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.warehouse-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e8eaec;
}

.warehouse-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.stock-info {
  display: flex;
  gap: 30rpx;
}

.stock-info text {
  font-size: 26rpx;
  color: #666;
}

/* 产品任务样式 */
.product-section {
  margin-bottom: 40rpx;
  border: 1rpx solid #e8eaec;
  border-radius: 12rpx;
  overflow: hidden;
}

.product-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e8eaec;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.product-style {
  font-size: 26rpx;
  color: #666;
}

.product-header button {
  margin-left: auto;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

/* 工序任务样式 */
.step-tasks {
  background: #fff;
}

.task-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item.my-task {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(64, 158, 255, 0.08));
  border-left: 6rpx solid #409eff;
}

.task-item.processing {
  background: linear-gradient(135deg, rgba(245, 166, 35, 0.1), rgba(245, 166, 35, 0.15));
  border-left: 6rpx solid #f5a623;
}

.task-item.completed {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.08), rgba(103, 194, 58, 0.12));
  border-left: 6rpx solid #67c23a;
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.step-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.task-assignee {
  margin-bottom: 16rpx;
}

.task-assignee text {
  font-size: 26rpx;
  color: #666;
}

.task-actions {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.task-actions button {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}

/* 缺陷提交弹窗样式 */
.defect-popup {
  background: #fff;
  border-radius: 12rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.defect-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.step-info {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.step-info text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.defect-list {
  margin-top: 20rpx;
}

.defect-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e8eaec;
}

.defect-item:last-child {
  border-bottom: none;
}

.defect-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.defect-input {
  display: flex;
  align-items: center;
  gap: 0;
}

.defect-input button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 0;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.defect-input input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  border: 1rpx solid #ddd;
  font-size: 28rpx;
}

.no-defects {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #e8eaec;
}

.popup-actions button {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
}
</style>

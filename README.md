# cp-mes-ruoyi

## 📖 目录
- [项目简介](#项目简介)
- [技术栈总览](#技术栈总览)
- [系统模块架构](#系统模块架构)
  - [管理后台模块](#管理后台模块-cp-mes-admin)
  - [系统核心模块](#系统核心模块-cp-mes-system)
  - [框架核心模块](#框架核心模块-cp-mes-framework)
  - [公共工具模块](#公共工具模块-cp-mes-common)
  - [任务调度模块](#任务调度模块-cp-mes-job)
  - [代码生成模块](#代码生成模块-cp-mes-generator)
  - [对象存储模块](#对象存储模块-cp-mes-oss)
  - [短信服务模块](#短信服务模块-cp-mes-sms)
  - [演示模块](#演示模块-cp-mes-demo)
  - [扩展模块组](#扩展模块组-cp-mes-extend)

---

## 🏗️ 项目简介
`cp-mes-ruoyi` 是一个基于 Vue2 和 Spring Boot 的企业级管理系统，提供了丰富的模块化功能，适用于多种业务场景。

---

## 🔧 技术栈总览
- **前端框架**：Vue2 + Element-UI + ECharts
- **后端框架**：Spring Boot + MySQL + Redis
- **其他技术**：Spring Security、MyBatis-Plus、XXL-Job、阿里云 OSS、腾讯云短信

---

## 🧱 系统模块架构

### 🚀 管理后台模块（cp-mes-admin）
**功能概述**：系统主入口和 Web 服务  
**核心职责**：
- 系统启动入口
- 集成所有业务模块
- 提供 Web 服务和 API 接口

**核心文件**：
- `cp-mes-admin/src/main/java/com/cpmes/CpMesApplication.java`
- `cp-mes-admin/pom.xml`

**技术栈**：Spring Boot、Spring MVC

---

### 👥 系统核心模块（cp-mes-system）
**功能概述**：基础系统管理功能  
**核心职责**：
- 用户权限管理
- 部门、角色、菜单管理
- 组织架构和权限控制支撑

**核心文件**：
- `cp-mes-system/src/main/java/com/cpmes/system/`
- `cp-mes-system/domain/`

**技术栈**：Spring Security、MyBatis

---

### ⚙️ 框架核心模块（cp-mes-framework）
**功能概述**：技术框架支撑  
**核心职责**：
- 安全认证
- 数据访问
- 缓存管理
- 异常处理等底层支撑能力

**核心文件**：
- `cp-mes-framework/src/main/java/com/cpmes/framework/`

**技术栈**：Spring Security、Redis、MyBatis-Plus

---

### 🛠️ 公共工具模块（cp-mes-common）
**功能概述**：通用工具和配置  
**核心职责**：
- 提供系统通用工具类
- 常量定义与配置管理
- 为各模块提供通用支撑

**核心文件**：
- `cp-mes-common/src/main/java/com/cpmes/common/`
- `cp-mes-common/config/`

**技术栈**：Jackson、Hutool

---

### ⏰ 任务调度模块（cp-mes-job）
**功能概述**：定时任务管理  
**核心职责**：
- 管理系统定时任务
- 支持报表生成、数据清理等自动任务

**核心文件**：
- `cp-mes-job/src/main/java/com/cpmes/job/`
- `cp-mes-job/pom.xml`

**技术栈**：XXL-Job

---

### 🔧 代码生成模块（cp-mes-generator）
**功能概述**：代码自动生成  
**核心职责**：
- 基于数据库结构自动生成代码
- 快速生成 CRUD，提高开发效率

**核心文件**：
- `cp-mes-generator/src/main/java/com/cpmes/generator/`

**技术栈**：Velocity 模板引擎

---

### 📁 对象存储模块（cp-mes-oss）
**功能概述**：文件存储管理  
**核心职责**：
- 支持上传、下载、图片及文档存储
- 满足生产中报表、图片、文档等存储需求

**核心文件**：
- `cp-mes-oss/src/main/java/com/cpmes/oss/`

**技术栈**：阿里云 OSS、腾讯云 COS

---

### 📱 短信服务模块（cp-mes-sms）
**功能概述**：短信通知服务  
**核心职责**：
- 提供短信发送功能
- 支持异常告警、工单通知等消息推送

**核心文件**：
- `cp-mes-sms/src/main/java/com/cpmes/sms/core/AliyunSmsTemplate.java`
- `cp-mes-sms/src/main/java/com/cpmes/sms/core/TencentSmsTemplate.java`

**技术栈**：阿里云短信、腾讯云短信

---

### 🎯 演示模块（cp-mes-demo）
**功能概述**：功能演示与使用示例  
**核心职责**：
- 提供系统接口和功能使用示例
- 帮助开发者了解系统特性

**核心文件**：
- `cp-mes-demo/src/main/java/com/cpmes/demo/controller/SmsController.java`

**技术栈**：Spring MVC

---

### 🔍 扩展模块组（cp-mes-extend）
**功能概述**：系统监控与功能扩展  
**核心职责**：
- 包含系统监控模块（cp-mes-monitor-admin）
- 定时任务管理后台（cp-mes-xxl-job-admin）

**核心文件**：
- `cp-mes-extend/cp-mes-monitor-admin/`
- `cp-mes-extend/cp-mes-xxl-job-admin/`

**技术栈**：Spring Boot Admin、XXL-Job Admin
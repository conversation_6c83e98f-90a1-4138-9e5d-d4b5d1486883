# 单租户模式配置说明

## 📋 概述
本文档说明如何将cp-mes系统配置为单租户模式，所有数据将使用默认租户 `000000`。

## 🛠️ 配置步骤

### 1. 数据库配置
执行以下SQL脚本来设置单租户模式：

```sql
-- 执行 single-tenant-setup.sql 中的所有语句
```

### 2. 应用程序配置
在 `application.yml` 中已添加默认租户配置：
```yaml
cp-mes:
  defaultTenantId: "000000"
```

### 3. 代码使用
使用 `TenantHelper` 工具类来获取租户ID：

```java
// 获取当前租户ID（单租户模式下总是返回000000）
String tenantId = TenantHelper.getCurrentTenantId();

// 确保租户ID有效
String safeTenantId = TenantHelper.ensureTenantId(someTenantId);
```

## ✅ 验证配置
1. 登录系统时无需选择租户
2. 所有业务数据都属于默认租户
3. 用户管理中所有用户的租户ID都是 `000000`

## 🔄 切换回多租户模式
如果将来需要切换回多租户模式：
1. 修改 `application.yml` 删除或注释 `defaultTenantId` 配置
2. 重新启用租户选择界面
3. 根据需要创建新的租户数据

## ⚠️ 注意事项
- 默认租户 `000000` 不要删除
- 所有新创建的数据都会自动分配给默认租户
- 系统仍保留多租户架构，只是默认使用单一租户 
# 文件上传大小限制修改说明

## 修改内容

### 前端修改
- **文件路径**: `cp-mes-ui/src/views/system/userFileManager/index.vue`
- **修改内容**: 将文件上传组件的 `:file-size` 属性从 `50` (50MB) 修改为 `1024` (1GB)
- **修改位置**: 第392行

```vue
<!-- 修改前 -->
:file-size="50"

<!-- 修改后 -->
:file-size="1024"
```

### 后端修改
- **文件路径**: `cp-mes-admin/src/main/resources/application.yml`
- **修改内容**: 将Spring Boot的文件上传大小限制从10MB修改为1GB
- **修改位置**: 第79-82行

```yaml
# 修改前
servlet:
  multipart:
    max-file-size: 10MB
    max-request-size: 20MB

# 修改后
servlet:
  multipart:
    max-file-size: 1GB
    max-request-size: 1GB
```

## 生效方式

### 前端
- 刷新页面即可生效

### 后端
- 需要重启Spring Boot应用服务器
- 重启后新的配置才会生效

## 注意事项

1. **服务器内存**: 上传1GB文件需要确保服务器有足够的内存
2. **网络超时**: 大文件上传可能需要调整HTTP超时时间
3. **存储空间**: 确保服务器有足够的磁盘空间存储大文件
4. **数据库字段**: 确认数据库中存储文件大小的字段类型足够大（通常BIGINT类型可以存储很大的数值）

## 测试建议

在生产环境部署前，建议：
1. 先在测试环境验证大文件上传功能
2. 测试网络中断后的重新上传机制
3. 验证大文件下载功能是否正常
4. 检查服务器性能在大文件上传时的表现 
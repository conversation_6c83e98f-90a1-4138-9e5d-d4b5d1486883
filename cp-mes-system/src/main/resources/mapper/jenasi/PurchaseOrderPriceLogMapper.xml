<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.PurchaseOrderPriceLogMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.PurchaseOrderPriceLog">
        <id property="id" column="id" />
        <result property="purchaseOrderId" column="purchase_order_id" />
        <result property="purchaseNo" column="purchase_no" />
        <result property="oldActualPrice" column="old_actual_price" />
        <result property="newActualPrice" column="new_actual_price" />
        <result property="operationType" column="operation_type" />
        <result property="operator" column="operator" />
        <result property="operationTime" column="operation_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        id, purchase_order_id, purchase_no, old_actual_price, new_actual_price,
        operation_type, operator, operation_time, remark
    </sql>

    <!-- 根据采购订单ID查询价格变更日志列表（按操作时间倒序） -->
    <select id="selectByPurchaseOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM "storage"."purchase_order_price_log"
        WHERE purchase_order_id = #{purchaseOrderId}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据采购订单号查询价格变更日志列表（按操作时间倒序） -->
    <select id="selectByPurchaseNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM "storage"."purchase_order_price_log"
        WHERE purchase_no = #{purchaseNo}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据操作人查询价格变更日志列表（按操作时间倒序） -->
    <select id="selectByOperator" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM "storage"."purchase_order_price_log"
        WHERE operator = #{operator}
        ORDER BY operation_time DESC
    </select>

    <!-- 批量插入价格变更日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO "storage"."purchase_order_price_log" (
        purchase_order_id, purchase_no, old_actual_price, new_actual_price,
        operation_type, operator, operation_time, remark
        ) VALUES
        <foreach collection="logList" item="item" separator=",">
            (
            #{item.purchaseOrderId},
            #{item.purchaseNo},
            #{item.oldActualPrice},
            #{item.newActualPrice},
            #{item.operationType},
            #{item.operator},
            #{item.operationTime},
            #{item.remark}
            )
        </foreach>
    </insert>

</mapper>

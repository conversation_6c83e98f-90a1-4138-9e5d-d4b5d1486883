<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.SemiFinishedProductMapper">

    <select id="selectPageWithZone" resultType="com.cpmes.system.vo.SemiFinishedProductVO">
        SELECT DISTINCT
            sfp.*,
            wz.zone_code,
            wz.zone_name,
            wz.zone_type,
            wi.warehouse_code,
            wi.warehouse_name,
            CASE 
                WHEN sfp.style_id = 9 THEN '标准款'
                WHEN fs.style_name IS NOT NULL THEN fs.style_name
                ELSE CONCAT('款式', sfp.style_id)
            END as style_name,
            CASE 
                WHEN sfp.board_type = '上板' THEN '上板'
                WHEN sfp.board_type = '下板' THEN '下板'
                WHEN sfp.board_type = '单板' THEN '单板'
                ELSE sfp.board_type
            END as board_type_name
        FROM
            storage.semi_finished_product sfp
        LEFT JOIN
            storage.functional_style fs ON sfp.style_id = fs.style_id AND sfp.style_id != 9
        LEFT JOIN
            storage.inventory_detail id ON sfp.fields3 = id.material_id
        LEFT JOIN
            storage.warehouse_zone wz ON id.zone_code = wz.zone_code
        LEFT JOIN
            storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        ${ew.customSqlSegment}
    </select>

    <!-- 新增：不关联区域信息的查询，避免数据重复 -->
    <select id="selectPageWithoutZone" resultType="com.cpmes.system.vo.SemiFinishedProductVO">
        SELECT 
            sfp.*,
            CASE 
                WHEN sfp.style_id = 9 THEN '标准款'
                WHEN fs.style_name IS NOT NULL THEN fs.style_name
                ELSE CONCAT('款式', sfp.style_id)
            END as style_name,
            CASE 
                WHEN sfp.board_type = '上板' THEN '上板'
                WHEN sfp.board_type = '下板' THEN '下板'
                WHEN sfp.board_type = '单板' THEN '单板'
                ELSE sfp.board_type
            END as board_type_name
        FROM
            storage.semi_finished_product sfp
        LEFT JOIN
            storage.functional_style fs ON sfp.style_id = fs.style_id AND sfp.style_id != 9
        ${ew.customSqlSegment}
    </select>
</mapper> 
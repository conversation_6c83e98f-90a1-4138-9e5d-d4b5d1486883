<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.ComponentWarehouseMapper">

    <resultMap id="ComponentWarehouseResult" type="com.cpmes.system.entity.ComponentWarehouse">
    </resultMap>

    <select id="selectPageWithZone" resultType="com.cpmes.system.vo.ComponentWarehouseVO">
        SELECT DISTINCT
            cw.*,
            wz.zone_code,
            wz.zone_name,
            wz.zone_type,
            wi.warehouse_code,
            wi.warehouse_name
        FROM
            storage.component_warehouse cw
        LEFT JOIN
            storage.inventory_detail id ON cw.fields3 = id.material_id
        LEFT JOIN
            storage.warehouse_zone wz ON id.zone_code = wz.zone_code
        LEFT JOIN
            storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        ${ew.customSqlSegment}
    </select>

    <!-- 新增：不关联区域信息的查询，避免数据重复 -->
    <select id="selectPageWithoutZone" resultType="com.cpmes.system.vo.ComponentWarehouseVO">
        SELECT 
            cw.*
        FROM
            storage.component_warehouse cw
        ${ew.customSqlSegment}
    </select>
</mapper> 
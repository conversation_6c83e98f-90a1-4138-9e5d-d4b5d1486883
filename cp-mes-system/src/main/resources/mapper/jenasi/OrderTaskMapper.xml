<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.OrderTaskMapper">

    <!-- 实体映射 -->
    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.OrderTask">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="itemId" column="item_id"/>
        <result property="itemName" column="item_name"/>
        <result property="processRouteCode" column="process_route_code"/>
        <result property="taskLevel" column="task_level"/>
        <result property="taskStatus" column="task_status"/>
        <result property="createdName" column="created_name"/>
        <result property="updatedName" column="updated_name"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="expectedTime" column="expected_time"/>
        <result property="completedTime" column="completed_time"/>
        <result property="isDefer" column="is_defer"/>
        <result property="quantity" column="quantity"/>
    </resultMap>


    <!-- 列表 -->
    <sql id="Base_Column_List">
        id, order_id, item_id, item_name, process_route_code,
    task_level, task_status, created_name, updated_name,
    remark, create_time, update_time, is_delete,is_defer,completed_time,expected_time,quantity
    </sql>

    <!-- 分页查询详细任务信息 -->
    <select id="selectOrderTaskDetailPage" resultType="com.cpmes.system.entity.vo.OrderTaskDetailVO">
        SELECT
            ot.id AS orderTaskId,
            ot.order_id AS orderId,
            ot.item_id AS itemId,
            ot.item_name AS itemName,
            ot.process_route_code AS processRouteCode,
            ot.task_level AS taskLevel,
            ot.task_status AS taskStatus,

            st.id AS stepTaskId,
            st.step_id AS stepId,
            st.step_number AS stepNumber,
            st.step_name AS stepName,
            st.assignee AS assignee,
            st.is_completed AS isCompleted,
            st.created_at AS stepTaskCreateTime,
            st.completed_at AS completedAt,
            st.expected_at AS expectedAt
        FROM
            "storage"."order_task" ot
        LEFT JOIN
            "storage"."step_task" st ON ot.id = st.task_id AND st.is_deleted = 0
        WHERE
            ot.is_delete = 0
            <!-- 动态查询条件 -->
            <if test="query.orderTaskId != null">
                AND ot.id = #{query.orderTaskId}
            </if>
            <if test="query.orderId != null">
                AND ot.order_id = #{query.orderId}
            </if>
            <if test="query.itemId != null and query.itemId != ''">
                AND ot.item_id = #{query.itemId}
            </if>
            <if test="query.itemName != null and query.itemName != ''">
                AND ot.item_name LIKE '%' || #{query.itemName} || '%'
            </if>
            <if test="query.processRouteCode != null and query.processRouteCode != ''">
                AND ot.process_route_code = #{query.processRouteCode}
            </if>
            <if test="query.taskLevel != null and query.taskLevel != ''">
                AND ot.task_level = #{query.taskLevel}
            </if>
            <if test="query.taskStatus != null and query.taskStatus != ''">
                AND ot.task_status = #{query.taskStatus}
            </if>
            <if test="query.stepTaskId != null">
                AND st.id = #{query.stepTaskId}
            </if>
            <if test="query.stepId != null">
                AND st.step_id = #{query.stepId}
            </if>
            <if test="query.stepName != null and query.stepName != ''">
                AND st.step_name LIKE '%' || #{query.stepName} || '%'
            </if>
            <if test="query.assignee != null and query.assignee != ''">
                AND st.assignee LIKE '%' || #{query.assignee} || '%'
            </if>
            <if test="query.isCompleted != null">
                AND st.is_completed = #{query.isCompleted}
            </if>
        ORDER BY
            ot.id, st.id,st.step_number
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.SupplierMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.Supplier">
            <id property="id" column="id" />
            <result property="supplierName" column="supplier_name" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_name,create_time,update_time,is_deleted
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.OrderItemMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.OrderItem">
            <id property="id" column="id" />
            <result property="orderId" column="order_id" />
            <result property="productId" column="product_id" />
            <result property="productName" column="product_name" />
            <result property="quantity" column="quantity" />
            <result property="inventory" column="inventory" />
            <result property="createTime" column="create_time" />
            <result property="deleted" column="deleted" />
            <result property="styleId" column="style_id" />
            <result property="styleName" column="style_name" />
            <result property="boardType" column="board_type" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,product_id,product_name,quantity,
        inventory,create_time,deleted,style_id,style_name,board_type
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.InventoryDetailJenasiMapper">
    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.InventoryDetailJenasi">
        <id property="detailId" column="detail_id" />
        <result property="zoneCode" column="zone_code" />
        <result property="materialId" column="material_id" />
        <result property="materialType" column="material_type" />
        <result property="materialName" column="material_name" />
        <result property="batchNo" column="batch_no" />
        <result property="currentStock" column="current_stock" />
        <result property="inboundQuantity" column="inbound_quantity" />
        <result property="outboundQuantity" column="outbound_quantity" />
        <result property="stockQuantity" column="stock_quantity" />
        <result property="minStockQuantity" column="min_stock_quantity" />
        <result property="needPurchase" column="need_purchase" />
        <result property="unit" column="unit" />
        <result property="productionDate" column="production_date" />
        <result property="expiryDate" column="expiry_date" />
        <result property="sourceType" column="source_type" />
        <result property="sourceNo" column="source_no" />
        <result property="qrCode" column="qr_code" />
        <result property="entryDate" column="entry_date" />
        <result property="lastCheckDate" column="last_check_date" />
        <result property="status" column="status" />
        <result property="delFlag" column="del_flag" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>


    <select id="queryByTypeAndName" resultType="com.cpmes.system.entity.InventoryDetailJenasi">
        SELECT *
        FROM storage.inventory_detail
        WHERE del_flag = '0'
        <if test="materialType != null and materialType != ''">
            AND material_type LIKE CONCAT('%', #{materialType}, '%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <sql id="Base_Column_List">
        detail_id,zone_code,material_id,material_type,material_name,batch_no,
        current_stock,inbound_quantity,outbound_quantity,stock_quantity,min_stock_quantity,
        need_purchase,unit,production_date,expiry_date,source_type,
        source_no,qr_code,entry_date,last_check_date,status,
        del_flag,create_by,create_time,update_by,update_time,
        remark
    </sql>


</mapper>

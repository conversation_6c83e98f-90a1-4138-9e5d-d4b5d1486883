<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.TrackingNumberMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.TrackingNumber">
        <id property="id" column="id" />
        <result property="trackingNumber" column="tracking_number" />
        <result property="logisticsCompany" column="logistics_company" />
        <result property="logisticsCompanyName" column="logistics_company_name" />
        <result property="purchaseOrderId" column="purchase_order_id" />
        <result property="purchaseOrderNo" column="purchase_order_no" />
        <result property="currentStatus" column="current_status" />
        <result property="statusDescription" column="status_description" />
        <result property="querySuccess" column="query_success" />
        <result property="errorMessage" column="error_message" />
        <result property="lastQueryTime" column="last_query_time" />
        <result property="queryCount" column="query_count" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="reservedField1" column="reserved_field_1" />
        <result property="reservedField2" column="reserved_field_2" />
        <result property="reservedField3" column="reserved_field_3" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tracking_number, logistics_company, logistics_company_name,
        purchase_order_id, purchase_order_no, current_status, status_description,
        query_success, error_message, last_query_time, query_count, remark,
        create_by, create_time, update_by, update_time, del_flag,
        reserved_field_1, reserved_field_2, reserved_field_3
    </sql>

    <!-- 分页查询快递单号列表 -->
    <select id="getTrackingNumberListByPage" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            tn.id,
            tn.tracking_number AS trackingNumber,
            tn.logistics_company AS logisticsCompany,
            tn.logistics_company_name AS logisticsCompanyName,
            tn.purchase_order_id AS purchaseOrderId,
            tn.purchase_order_no AS purchaseOrderNo,
            tn.current_status AS status,
            tn.status_description AS statusDescription,
            tn.query_success AS querySuccess,
            tn.error_message AS errorMessage,
            tn.last_query_time AS lastQueryTime,
            tn.query_count AS queryCount,
            tn.remark,
            tn.create_by AS createBy,
            tn.create_time AS createTime,
            tn.update_by AS updateBy,
            tn.update_time AS lastUpdateTime
        FROM storage.tracking_number tn
        WHERE tn.del_flag = '0'
        <if test="query.trackingNumber != null and query.trackingNumber != ''">
            AND tn.tracking_number LIKE CONCAT('%', #{query.trackingNumber}, '%')
        </if>
        <if test="query.logisticsCompany != null and query.logisticsCompany != ''">
            AND tn.logistics_company = #{query.logisticsCompany}
        </if>
        <if test="query.logisticsCompanyName != null and query.logisticsCompanyName != ''">
            AND tn.logistics_company_name LIKE CONCAT('%', #{query.logisticsCompanyName}, '%')
        </if>
        <if test="query.purchaseOrderId != null">
            AND tn.purchase_order_id = #{query.purchaseOrderId}
        </if>
        <if test="query.purchaseOrderNo != null and query.purchaseOrderNo != ''">
            AND tn.purchase_order_no LIKE CONCAT('%', #{query.purchaseOrderNo}, '%')
        </if>
        <if test="query.currentStatus != null and query.currentStatus != ''">
            AND tn.current_status = #{query.currentStatus}
        </if>
        <if test="query.querySuccess != null">
            AND tn.query_success = #{query.querySuccess}
        </if>
        <if test="query.createBy != null and query.createBy != ''">
            AND tn.create_by = #{query.createBy}
        </if>
        <if test="query.createTimeStart != null">
            AND tn.create_time >= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            AND tn.create_time &lt;= #{query.createTimeEnd}
        </if>
        <if test="query.lastQueryTimeStart != null">
            AND tn.last_query_time >= #{query.lastQueryTimeStart}
        </if>
        <if test="query.lastQueryTimeEnd != null">
            AND tn.last_query_time &lt;= #{query.lastQueryTimeEnd}
        </if>
        <if test="query.queryCountMin != null">
            AND tn.query_count >= #{query.queryCountMin}
        </if>
        <if test="query.queryCountMax != null">
            AND tn.query_count &lt;= #{query.queryCountMax}
        </if>
        <if test="query.remark != null and query.remark != ''">
            AND tn.remark LIKE CONCAT('%', #{query.remark}, '%')
        </if>
        ORDER BY 
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                ${query.orderBy}
                <if test="query.orderDirection != null and query.orderDirection != ''">
                    ${query.orderDirection}
                </if>
            </when>
            <otherwise>
                tn.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据快递单号获取详情 -->
    <select id="getTrackingNumberDetail" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            tn.id,
            tn.tracking_number AS trackingNumber,
            tn.logistics_company AS logisticsCompany,
            tn.logistics_company_name AS logisticsCompanyName,
            tn.purchase_order_id AS purchaseOrderId,
            tn.purchase_order_no AS purchaseOrderNo,
            tn.current_status AS status,
            tn.status_description AS statusDescription,
            tn.query_success AS querySuccess,
            tn.error_message AS errorMessage,
            tn.last_query_time AS lastQueryTime,
            tn.query_count AS queryCount,
            tn.remark,
            tn.create_by AS createBy,
            tn.create_time AS createTime,
            tn.update_by AS updateBy,
            tn.update_time AS lastUpdateTime
        FROM storage.tracking_number tn
        WHERE tn.tracking_number = #{trackingNumber}
          AND tn.del_flag = '0'
    </select>

    <!-- 根据采购订单ID获取快递单号列表 -->
    <select id="getTrackingNumbersByPurchaseOrderId" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            tn.id,
            tn.tracking_number AS trackingNumber,
            tn.logistics_company AS logisticsCompany,
            tn.logistics_company_name AS logisticsCompanyName,
            tn.purchase_order_id AS purchaseOrderId,
            tn.purchase_order_no AS purchaseOrderNo,
            tn.current_status AS status,
            tn.status_description AS statusDescription,
            tn.query_success AS querySuccess,
            tn.error_message AS errorMessage,
            tn.last_query_time AS lastQueryTime,
            tn.query_count AS queryCount,
            tn.remark,
            tn.create_by AS createBy,
            tn.create_time AS createTime,
            tn.update_by AS updateBy,
            tn.update_time AS lastUpdateTime
        FROM storage.tracking_number tn
        WHERE tn.purchase_order_id = #{purchaseOrderId}
          AND tn.del_flag = '0'
        ORDER BY tn.create_time DESC
    </select>

    <!-- 根据采购订单号获取快递单号列表 -->
    <select id="getTrackingNumbersByPurchaseOrderNo" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            tn.id,
            tn.tracking_number AS trackingNumber,
            tn.logistics_company AS logisticsCompany,
            tn.logistics_company_name AS logisticsCompanyName,
            tn.purchase_order_id AS purchaseOrderId,
            tn.purchase_order_no AS purchaseOrderNo,
            tn.current_status AS status,
            tn.status_description AS statusDescription,
            tn.query_success AS querySuccess,
            tn.error_message AS errorMessage,
            tn.last_query_time AS lastQueryTime,
            tn.query_count AS queryCount,
            tn.remark,
            tn.create_by AS createBy,
            tn.create_time AS createTime,
            tn.update_by AS updateBy,
            tn.update_time AS lastUpdateTime
        FROM storage.tracking_number tn
        WHERE tn.purchase_order_no = #{purchaseOrderNo}
          AND tn.del_flag = '0'
        ORDER BY tn.create_time DESC
    </select>

    <!-- 获取需要刷新的快递单号列表 -->
    <select id="getTrackingNumbersNeedRefresh" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM storage.tracking_number
        WHERE del_flag = '0'
          AND (last_query_time IS NULL 
               OR last_query_time &lt; NOW() - INTERVAL '#{minutesAgo} minutes')
          AND current_status NOT IN ('DELIVERED', 'EXCEPTION', 'REJECTED')
        ORDER BY last_query_time ASC NULLS FIRST
        LIMIT 100
    </select>

    <!-- 获取状态统计 -->
    <select id="getStatusStatistics" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            current_status AS status,
            COUNT(*) AS queryCount
        FROM storage.tracking_number
        WHERE del_flag = '0'
        GROUP BY current_status
        ORDER BY COUNT(*) DESC
    </select>

    <!-- 搜索快递单号 -->
    <select id="searchTrackingNumbers" resultType="com.cpmes.system.entity.vo.TrackingNumberVO">
        SELECT
            tn.id,
            tn.tracking_number AS trackingNumber,
            tn.logistics_company AS logisticsCompany,
            tn.logistics_company_name AS logisticsCompanyName,
            tn.purchase_order_id AS purchaseOrderId,
            tn.purchase_order_no AS purchaseOrderNo,
            tn.current_status AS status,
            tn.status_description AS statusDescription,
            tn.query_success AS querySuccess,
            tn.error_message AS errorMessage,
            tn.last_query_time AS lastQueryTime,
            tn.query_count AS queryCount,
            tn.remark,
            tn.create_by AS createBy,
            tn.create_time AS createTime,
            tn.update_by AS updateBy,
            tn.update_time AS lastUpdateTime
        FROM storage.tracking_number tn
        WHERE tn.del_flag = '0'
          AND (tn.tracking_number LIKE CONCAT('%', #{keyword}, '%')
               OR tn.logistics_company_name LIKE CONCAT('%', #{keyword}, '%')
               OR tn.purchase_order_no LIKE CONCAT('%', #{keyword}, '%')
               OR tn.remark LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY tn.create_time DESC
        LIMIT 50
    </select>

</mapper>

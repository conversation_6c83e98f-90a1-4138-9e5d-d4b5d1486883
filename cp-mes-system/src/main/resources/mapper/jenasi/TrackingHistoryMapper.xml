<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.TrackingHistoryMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.TrackingHistory">
        <id property="id" column="id" />
        <result property="trackingNumber" column="tracking_number" />
        <result property="trackingTime" column="tracking_time" />
        <result property="description" column="description" />
        <result property="location" column="location" />
        <result property="operator" column="operator" />
        <result property="operatorPhone" column="operator_phone" />
        <result property="status" column="status" />
        <result property="timeStr" column="time_str" />
        <result property="sortOrder" column="sort_order" />
        <result property="isLatest" column="is_latest" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="reservedField1" column="reserved_field_1" />
        <result property="reservedField2" column="reserved_field_2" />
        <result property="reservedField3" column="reserved_field_3" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tracking_number, tracking_time, description, location, operator, operator_phone,
        status, time_str, sort_order, is_latest, remark, create_by, create_time,
        update_by, update_time, del_flag, reserved_field_1, reserved_field_2, reserved_field_3
    </sql>

    <!-- 根据快递单号删除历史记录 -->
    <delete id="deleteTrackingHistoryByNumber">
        DELETE FROM storage.tracking_history
        WHERE tracking_number = #{trackingNumber}
    </delete>

    <!-- 根据快递单号获取历史记录 -->
    <select id="getTrackingHistoryByNumber" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM storage.tracking_history
        WHERE tracking_number = #{trackingNumber}
        ORDER BY tracking_time DESC, create_time DESC
    </select>

    <!-- 批量插入历史记录 -->
    <insert id="batchInsertTrackingHistory">
        INSERT INTO storage.tracking_history (
            tracking_number, tracking_time, description, location, operator, operator_phone,
            status, time_str, sort_order, is_latest, remark, create_by, create_time
        ) VALUES
        <foreach collection="historyList" item="item" separator=",">
            (
                #{item.trackingNumber},
                #{item.trackingTime},
                #{item.description},
                #{item.location},
                #{item.operator},
                #{item.operatorPhone},
                #{item.status},
                #{item.timeStr},
                #{item.sortOrder},
                #{item.isLatest},
                #{item.remark},
                #{item.createBy},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 插入单条历史记录 -->
    <insert id="insertTrackingHistory">
        INSERT INTO storage.tracking_history (
            tracking_number, tracking_time, description, location, operator, operator_phone,
            status, time_str, sort_order, is_latest, remark, create_by, create_time
        ) VALUES (
            #{trackingNumber},
            #{trackingTime},
            #{description},
            #{location},
            #{operator},
            #{operatorPhone},
            #{status},
            #{timeStr},
            #{sortOrder},
            #{isLatest},
            #{remark},
            #{createBy},
            #{createTime}
        )
    </insert>

    <!-- 获取最新的历史记录 -->
    <select id="getLatestTrackingHistory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM storage.tracking_history
        WHERE tracking_number = #{trackingNumber}
        ORDER BY tracking_time DESC, create_time DESC
        LIMIT 1
    </select>

    <!-- 清理过期的历史记录（保留最近30天） -->
    <delete id="cleanExpiredTrackingHistory">
        DELETE FROM storage.tracking_history
        WHERE create_time &lt; NOW() - INTERVAL '30 days'
    </delete>

    <!-- 统计历史记录数量 -->
    <select id="countTrackingHistoryByNumber" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM storage.tracking_history
        WHERE tracking_number = #{trackingNumber}
    </select>

</mapper>

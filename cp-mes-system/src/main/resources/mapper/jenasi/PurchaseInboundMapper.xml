<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.PurchaseInboundMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.PurchaseInbound">
            <id property="id" column="id" />
            <result property="purchaseOrderNo" column="purchase_order_no" />
            <result property="status" column="status" />
            <result property="qrCode" column="qr_code" />
            <result property="zoneCode" column="zone_code" />
            <result property="operator" column="operator" />
            <result property="remark" column="remark" />
            <result property="batchNo" column="batch_no" />
            <result property="boardType" column="board_type" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="isDeleted" column="is_deleted" />
            <result property="materialType" column="material_type" />
            <result property="materialName" column="material_name" />
    </resultMap>

    <sql id="Base_Column_List">
        id,purchase_order_no,status,qr_code,zone_code,operator,
        remark,create_time,update_time,is_deleted,material_type,material_name
    </sql>
</mapper>

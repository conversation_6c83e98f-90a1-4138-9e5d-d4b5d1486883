<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.BomPickRecordMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.BomPickRecord">
            <id property="id" column="id" />
            <result property="stepTaskId" column="step_task_id" />
            <result property="itemName" column="item_name" />
            <result property="quantity" column="quantity" />
    </resultMap>

    <sql id="Base_Column_List">
        id,step_task_id,item_name,quantity
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.PurchaseOrderMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.PurchaseOrder">
            <id property="id" column="id" />
            <result property="purchaseNo" column="purchase_no" />
            <result property="itemId" column="item_id" />
            <result property="supplierId" column="supplier_id" />
            <result property="status" column="status" />
            <result property="quantity" column="quantity" />
            <result property="unit" column="unit" />
            <result property="price" column="price" />
            <result property="subtotal" column="subtotal" />
            <result property="applicant" column="applicant" />
            <result property="approver" column="approver" />
            <result property="expectedDate" column="expected_date" />
            <result property="applyTime" column="apply_time" />
            <result property="approveTime" column="approve_time" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="remark" column="remark" />
            <result property="imagesFolderPath" column="images_folder_path" />
            <result property="purchaseLinks" column="purchase_links" />
    </resultMap>

    <sql id="Base_Column_List">
        id,purchase_no,item_id,item_name,item_type,board_type,supplier_id,status,quantity,
        unit,price,subtotal,applicant,approver,
        expected_date,apply_time,approve_time,create_time,update_time,
        remark,images_folder_path,purchase_links,actual_price,actual_subtotal,
        actual_price_time,actual_price_recorder
    </sql>
    <select id="getPurchaseOrderDetail" resultType="com.cpmes.system.entity.vo.PurchaseOrderVO">
        SELECT
        po.id,
        po.purchase_no AS purchaseNo,
        po.item_id AS itemId,
        po.supplier_id AS supplierId,
        -- 双模式支持：直接使用po.item_name（已在保存时填充）
        po.item_name AS itemName,
        po.item_type AS itemType,
        po.board_type AS boardType,
        s.supplier_name AS supplierName,
        po.status,
        po.quantity,
        po.unit,
        po.price,
        po.subtotal,
        po.applicant,
        po.approver,
        po.expected_date AS expectedDate,
        po.apply_time AS applyTime,
        po.approve_time AS approveTime,
        po.create_time AS createTime,
        po.update_time AS updateTime,
        po.remark,
        po.images_folder_path AS imagesFolderPath,
        po.purchase_links AS purchaseLinks,
        po.actual_price AS actualPrice,
        po.actual_subtotal AS actualSubtotal,
        po.actual_price_time AS actualPriceTime,
        po.actual_price_recorder AS actualPriceRecorder,
        -- 从库存明细表获取批次号（取最新的一条记录）
        (SELECT id.batch_no FROM storage.inventory_detail id
         WHERE id.material_id = po.item_id
         AND id.source_no = po.purchase_no
         ORDER BY id.create_time DESC LIMIT 1) AS batchNo
        FROM
        storage.purchase_order po
        JOIN
        storage.supplier s ON po.supplier_id = s.id AND s.is_deleted = 0
        WHERE
        po.is_deleted = 0
        <if test="purchaseNo != null and purchaseNo != ''">
            AND po.purchase_no LIKE CONCAT('%', #{purchaseNo}, '%')
        </if>
        <if test="status != null">
            AND po.status = #{status}
        </if>
        <if test="itemName != null and itemName != ''">
            AND po.item_name LIKE CONCAT('%', #{itemName}, '%')
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND s.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <if test="applicant != null and applicant != ''">
            AND po.applicant LIKE CONCAT('%', #{applicant}, '%')
        </if>
        <if test="approver != null and approver != ''">
            AND po.approver LIKE CONCAT('%', #{approver}, '%')
        </if>
        ORDER BY
        po.create_time DESC
    </select>


    <select id="getPurchaseOrderDetailByCondition" resultType="com.cpmes.system.entity.vo.PurchaseOrderVO">
        SELECT
        po.id,
        po.purchase_no AS purchaseNo,
        po.item_id AS itemId,
        po.supplier_id AS supplierId,
        po.item_name AS itemName,
        po.item_type AS itemType,
        po.board_type AS boardType,
        s.supplier_name AS supplierName,
        po.status,
        po.quantity,
        po.unit,
        po.price,
        po.subtotal,
        po.applicant,
        po.approver,
        po.expected_date AS expectedDate,
        po.apply_time AS applyTime,
        po.approve_time AS approveTime,
        po.create_time AS createTime,
        po.update_time AS updateTime,
        po.remark,
        po.images_folder_path AS imagesFolderPath,
        po.purchase_links AS purchaseLinks,
        po.actual_price AS actualPrice,
        po.actual_subtotal AS actualSubtotal,
        po.actual_price_time AS actualPriceTime,
        po.actual_price_recorder AS actualPriceRecorder,
        -- 从库存明细表获取批次号（取最新的一条记录）
        (SELECT id.batch_no FROM storage.inventory_detail id
         WHERE id.material_id = po.item_id
         AND id.source_no = po.purchase_no
         ORDER BY id.create_time DESC LIMIT 1) AS batchNo
        FROM
        storage.purchase_order po
        JOIN
        storage.supplier s ON po.supplier_id = s.id AND s.is_deleted = 0
        WHERE
        po.is_deleted = 0
        <if test="purchaseNo != null and purchaseNo != ''">
            AND po.purchase_no LIKE CONCAT('%', #{purchaseNo}, '%')
        </if>
        <if test="status != null">
            AND po.status = #{status}
        </if>
        <if test="itemName != null and itemName != ''">
            AND i.item_name LIKE CONCAT('%', #{itemName}, '%')
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND s.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <if test="applicant != null and applicant != ''">
            AND po.applicant LIKE CONCAT('%', #{applicant}, '%')
        </if>
        <if test="approver != null and approver != ''">
            AND po.approver LIKE CONCAT('%', #{approver}, '%')
        </if>
        ORDER BY
        po.create_time DESC
    </select>

    <!-- 根据ID获取采购订单详情（包含供应商名称） -->
    <select id="getPurchaseOrderVOById" resultType="com.cpmes.system.entity.vo.PurchaseOrderVO">
        SELECT
        po.id,
        po.purchase_no AS purchaseNo,
        po.item_id AS itemId,
        po.supplier_id AS supplierId,
        po.item_name AS itemName,
        po.item_type AS itemType,
        po.board_type AS boardType,
        s.supplier_name AS supplierName,
        po.status,
        po.quantity,
        po.unit,
        po.price,
        po.subtotal,
        po.applicant,
        po.approver,
        po.expected_date AS expectedDate,
        po.apply_time AS applyTime,
        po.approve_time AS approveTime,
        po.create_time AS createTime,
        po.update_time AS updateTime,
        po.remark,
        po.images_folder_path AS imagesFolderPath,
        po.purchase_links AS purchaseLinks,
        po.actual_price AS actualPrice,
        po.actual_subtotal AS actualSubtotal,
        po.actual_price_time AS actualPriceTime,
        po.actual_price_recorder AS actualPriceRecorder,
        -- 从库存明细表获取批次号（取最新的一条记录）
        (SELECT id.batch_no FROM storage.inventory_detail id
         WHERE id.material_id = po.item_id
         AND id.source_no = po.purchase_no
         ORDER BY id.create_time DESC LIMIT 1) AS batchNo
        FROM
        storage.purchase_order po
        LEFT JOIN
        storage.supplier s ON po.supplier_id = s.id AND s.is_deleted = 0
        WHERE
        po.is_deleted = 0
        AND po.id = #{id}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.StepTaskMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.StepTask">
            <id property="id" column="id" />
            <result property="taskId" column="task_id" />
            <result property="stepId" column="step_id" />
            <result property="stepNumber" column="step_number" />
            <result property="stepName" column="step_name" />
            <result property="assignee" column="assignee" />
            <result property="isDeleted" column="is_deleted" />
            <result property="isCompleted" column="is_completed" />
            <result property="createdAt" column="created_at" />
            <result property="completedAt" column="completed_at" />
            <result property="expectedAt" column="expected_at" />
            <result property="defectInfo" column="defect_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
            <result property="onBoard" column="on_board" />
            <result property="downBoard" column="down_board" />
            <result property="oneBoard" column="one_board" />
    </resultMap>


    <sql id="Base_Column_List">
        id,task_id,step_id,step_number,step_name,assignee,is_deleted,
        is_completed,created_at,completed_at,expected_at,defect_info,on_board,down_board,one_board
    </sql>
    <select id="getDefectStats" resultType="com.cpmes.system.entity.dto.stepTask.DefectStatDTO">
        SELECT
            kv.key AS defect_name,
            SUM(CAST(kv.value AS INTEGER)) AS defect_count
        FROM
            storage.step_task t,
            LATERAL (
                     SELECT * FROM jsonb_each_text(
                         CASE
                             WHEN t.defect_info ~ '^\s*\{.*\}\s*$'
                    THEN t.defect_info::jsonb
                             ELSE '{}'::jsonb
                             END
                                   )
                ) AS kv
        WHERE
            t.is_completed = 2
          AND t.defect_info IS NOT NULL
          AND t.defect_info != ''
        AND t.defect_info ~ '^\s*\{.*\}\s*$'  -- 确保是合法 JSON
        AND CAST(kv.value AS INTEGER) > 0
        AND t.completed_at BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        GROUP BY
            kv.key
        ORDER BY
            defect_count DESC
    </select>
</mapper>

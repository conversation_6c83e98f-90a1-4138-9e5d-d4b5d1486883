<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.ProductWarehouseMapper">

    <select id="selectPageWithZone" resultType="com.cpmes.system.vo.ProductWarehouseVO">
        SELECT
            pw.product_id,
            pw.product_name,
            pw.material_type,
            pw.board_type,
            SUM(COALESCE(pw.current_stock, 0)) as current_stock,
            SUM(COALESCE(pw.outbound_quantity, 0)) as outbound_quantity,
            SUM(COALESCE(pw.inbound_quantity, 0)) as inbound_quantity,
            SUM(COALESCE(pw.production_quantity, 0)) as production_quantity,
            SUM(COALESCE(pw.aging_quantity, 0)) as aging_quantity,
            SUM(COALESCE(pw.stock_quantity, 0)) as stock_quantity,
            pw.style_id,
            pw.product_number,
            MAX(pw.updated_time) as updated_time,
            pw.series_id,
            pw.unit,
            pw.fields1,
            pw.fields2,
            pw.fields3,
            NULL as zone_code,
            NULL as zone_name,
            NULL as zone_type,
            NULL as warehouse_code,
            NULL as warehouse_name,
            CASE
                WHEN pw.style_id = 9 THEN '标准款'
                WHEN fs.style_name IS NOT NULL THEN fs.style_name
                ELSE CONCAT('款式', pw.style_id)
            END as style_name,
            sp.series_name as series_name
        FROM
            storage.product_warehouse pw
        LEFT JOIN
            storage.functional_style fs ON pw.style_id = fs.style_id AND pw.style_id != 9
        LEFT JOIN
            storage.series_products sp ON pw.series_id = sp.id
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                AND ${ew.sqlSegment}
            </if>
        </where>
        GROUP BY
            pw.product_id, pw.product_name, pw.material_type, pw.style_id,
            pw.product_number, pw.series_id, pw.unit, pw.fields1, pw.fields2, pw.fields3, fs.style_name, sp.series_name,board_type
        ORDER BY MAX(pw.updated_time) DESC
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.OrdersMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.Orders">
        <id property="id" column="id" />
        <result property="orderCode" column="order_code" />
        <result property="orderType" column="order_type" />
        <result property="status" column="status" />
        <result property="createdTime" column="created_time" />
        <result property="expectedTime" column="expected_time" />
        <result property="isDeleted" column="is_deleted" />
        <result property="dispatchStatus" column="dispatch_status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_code,order_type,status,created_time,is_deleted,dispatch_status,expected_time
    </sql>

    <!-- 分页查询工单详细信息 -->
    <select id="selectOrderDetailRawPage" resultType="com.cpmes.system.entity.vo.OrderDetailRawVO">
        SELECT
        o.id AS orderId,
        o.order_code AS orderCode,
        o.order_type AS orderType,
        o.status AS orderStatus,
        o.created_time AS orderCreatedTime,
        o.expected_time AS orderExpectedTime,
        o.dispatch_status AS orderDispatchStatus,

        oi.id AS orderItemId,
        oi.product_id AS productId,
        oi.product_name AS productName,
        oi.quantity AS orderItemQuantity,
        oi.inventory AS inventory,
        oi.create_time AS orderItemCreateTime,
        oi.style_id AS styleId,
        oi.style_name AS styleName,
        oi.board_type AS boardType,

        ot.id AS orderTaskId,
        ot.item_id AS itemId,
        ot.item_name AS itemName,
        ot.process_route_code AS processRouteCode,
        ot.task_level AS taskLevel,
        ot.task_status AS taskStatus,
        ot.created_name AS createdName,
        ot.updated_name AS updatedName,
        ot.remark AS remark,
        ot.create_time AS orderTaskCreateTime,
        ot.update_time AS orderTaskUpdateTime,
        ot.is_defer AS isDefer,
        ot.expected_time AS expectedTime,
        ot.completed_time AS completedTime,
        ot.quantity AS orderTaskQuantity,

        st.id AS stepTaskId,
        st.step_id AS stepId,
        st.step_name AS stepName,
        st.assignee AS assignee,
        st.defect_info AS defectInfo,
        st.on_board AS onBoard,
        st.down_board AS downBoard,
        st.one_board AS oneBoard,
        st.is_completed AS isCompleted,
        st.created_at AS stepTaskCreateTime,
        st.completed_at AS completedAt,
        st.expected_at AS expectedAt,
        st.step_number AS stepNumber

        FROM "storage"."orders" o
        LEFT JOIN "storage"."order_item" oi ON oi.order_id = o.id AND oi.deleted = 0
        LEFT JOIN "storage"."order_task" ot ON ot.order_id = o.id AND ot.is_delete = 0
        AND ot.item_id = oi.product_id
        LEFT JOIN "storage"."step_task" st ON st.task_id = ot.id AND st.is_deleted = 0
        WHERE o.is_deleted = 0
        <if test="req != null">
            <if test="req.orderId != null">
                AND o.id = #{req.orderId}
            </if>
            <if test="req.orderCode != null and req.orderCode != ''">
                AND o.order_code LIKE CONCAT('%', #{req.orderCode}, '%')
            </if>
            <if test="req.orderType != null and req.orderType != ''">
                AND o.order_type = #{req.orderType}
            </if>
            <if test="req.orderStatus != null and req.orderStatus != ''">
                AND o.status = #{req.orderStatus}
            </if>
            <if test="req.orderDispatchStatus != null and req.orderDispatchStatus != ''">
                AND o.dispatch_status = #{req.orderDispatchStatus}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND oi.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.itemName != null and req.itemName != ''">
                AND ot.item_name LIKE CONCAT('%', #{req.itemName}, '%')
            </if>
            <if test="req.taskLevel != null and req.taskLevel != ''">
                AND ot.task_level = #{req.taskLevel}
            </if>
            <if test="req.taskStatus != null and req.taskStatus != ''">
                AND ot.task_status = #{req.taskStatus}
            </if>
            <if test="req.stepName != null and req.stepName != ''">
                AND st.step_name LIKE CONCAT('%', #{req.stepName}, '%')
            </if>
            <if test="req.assignee != null and req.assignee != ''">
                AND st.assignee LIKE CONCAT('%', #{req.assignee}, '%')
            </if>
            <if test="req.isCompleted != null">
                AND st.is_completed = #{req.isCompleted}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                AND o.created_time BETWEEN #{req.startTime} AND #{req.endTime}
            </if>
            <if test="req.dueType != null">
                <choose>
                    <!-- 临期 -->
                    <when test="req.dueType == 1">
                        AND o.expected_time &gt;= NOW()
                        AND o.expected_time &lt;= NOW() + interval '1 day'
                        AND o.status != 'COMPLETED'
                    </when>

                    <!-- 逾期 -->
                    <when test="req.dueType == 2">
                        AND o.expected_time &lt; NOW()
                        AND o.status != 'COMPLETED'
                    </when>
                </choose>
            </if>
        </if>
        ORDER BY
        <!-- 动态排序支持 -->
        <choose>
            <when test="req != null and req.sortField != null and req.sortField != ''">
                <choose>
                    <when test="req.sortField == 'orderCode'">
                        o.order_code <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'orderType'">
                        o.order_type <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'orderStatus'">
                        o.status <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'orderCreatedTime'">
                        o.created_time <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'productName'">
                        oi.product_name <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'itemName'">
                        ot.item_name <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'taskLevel'">
                        ot.task_level <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'taskStatus'">
                        ot.task_status <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <when test="req.sortField == 'expectedTime'">
                        ot.expected_time <if test="req.sortOrder == 'asc'">ASC</if><if test="req.sortOrder != 'asc'">DESC</if>
                    </when>
                    <otherwise>
                        <!-- 默认按优先级排序 -->
                        CASE
                        WHEN o.order_type = 'URGENT' AND o.status = 'NEW' THEN 0
                        WHEN o.order_type = 'URGENT' AND o.status = 'IN_PROGRESS' THEN 1
                        WHEN o.order_type = 'NORMAL' AND o.status = 'NEW' THEN 2
                        WHEN o.order_type = 'NORMAL' AND o.status = 'IN_PROGRESS' THEN 3
                        WHEN o.order_type = 'URGENT' AND o.status = 'COMPLETED' THEN 4
                        WHEN o.order_type = 'NORMAL' AND o.status = 'COMPLETED' THEN 5
                        ELSE 6
                        END
                    </otherwise>
                </choose>,
                o.created_time DESC,
                o.id,
                oi.product_id,
                ot.item_id,
                st.step_number
            </when>
            <otherwise>
                <!-- 默认排序：按优先级 -->
                CASE
                WHEN o.order_type = 'URGENT' AND o.status = 'NEW' THEN 0
                WHEN o.order_type = 'URGENT' AND o.status = 'IN_PROGRESS' THEN 1
                WHEN o.order_type = 'NORMAL' AND o.status = 'NEW' THEN 2
                WHEN o.order_type = 'NORMAL' AND o.status = 'IN_PROGRESS' THEN 3
                WHEN o.order_type = 'URGENT' AND o.status = 'COMPLETED' THEN 4
                WHEN o.order_type = 'NORMAL' AND o.status = 'COMPLETED' THEN 5
                ELSE 6
                END,
                o.created_time DESC,
                o.id,
                oi.product_id,
                ot.item_id,
                st.step_number
            </otherwise>
        </choose>
    </select>

    <!-- 根据工单ID查询详细信息 -->
    <select id="selectOrderDetailRawByOrderId" resultType="com.cpmes.system.entity.vo.OrderDetailRawVO">
        SELECT
            o.id AS orderId,
            o.order_code AS orderCode,
            o.order_type AS orderType,
            o.status AS orderStatus,
            o.created_time AS orderCreatedTime,
            o.dispatch_status AS orderDispatchStatus,

            oi.id AS orderItemId,
            oi.product_id AS productId,
            oi.product_name AS productName,
            oi.quantity AS orderItemQuantity,
            oi.inventory AS inventory,
            oi.create_time AS orderItemCreateTime,
            oi.style_id AS styleId,
            oi.style_name AS styleName,

            ot.id AS orderTaskId,
            ot.item_id AS itemId,
            ot.item_name AS itemName,
            ot.process_route_code AS processRouteCode,
            ot.task_level AS taskLevel,
            ot.task_status AS taskStatus,
            ot.created_name AS createdName,
            ot.updated_name AS updatedName,
            ot.remark AS remark,
            ot.create_time AS orderTaskCreateTime,
            ot.update_time AS orderTaskUpdateTime,
            ot.is_defer AS isDefer,
            ot.expected_time AS expectedTime,
            ot.completed_time AS completedTime,
            ot.quantity AS orderTaskQuantity,

            st.id AS stepTaskId,
            st.step_id AS stepId,
            st.step_name AS stepName,
            st.assignee AS assignee,
            st.defect_info AS defectInfo,
            st.is_completed AS isCompleted,
            st.created_at AS stepTaskCreateTime,
            st.completed_at AS completedAt,
            st.expected_at AS expectedAt,
            st.step_number AS stepNumber

        FROM "storage"."orders" o
        LEFT JOIN "storage"."order_item" oi ON oi.order_id = o.id AND oi.deleted = 0
        LEFT JOIN "storage"."order_task" ot ON ot.order_id = o.id AND ot.is_delete = 0
            AND ot.item_id = oi.product_id
        LEFT JOIN "storage"."step_task" st ON st.task_id = ot.id AND st.is_deleted = 0
        WHERE o.is_deleted = 0 AND o.id = #{orderId}
        ORDER BY o.created_time DESC, o.id, oi.product_id, ot.item_id, st.step_number
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.ItemMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.Item">
            <id property="id" column="id" />
            <result property="itemName" column="item_name" />
            <result property="specification" column="specification" />
            <result property="category" column="category" />
            <result property="brand" column="brand" />
            <result property="unit" column="unit" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="isDeleted" column="is_deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,item_name,specification,category,brand,unit,
        create_time,update_time,is_deleted
    </sql>
</mapper>

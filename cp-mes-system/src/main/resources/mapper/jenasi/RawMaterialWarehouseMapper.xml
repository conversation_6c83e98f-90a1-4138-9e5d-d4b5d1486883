<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.RawMaterialWarehouseMapper">

    <select id="selectPageWithZone" resultType="com.cpmes.system.vo.RawMaterialWarehouseVO">
        SELECT DISTINCT
            rmw.*,
            wz.zone_code,
            wz.zone_name,
            wz.zone_type,
            wi.warehouse_code,
            wi.warehouse_name,
            CASE
                WHEN rmw.board_type = '上板' THEN '上板'
                WHEN rmw.board_type = '下板' THEN '下板'
                WHEN rmw.board_type = '单板' THEN '单板'
                ELSE rmw.board_type
            END as board_type_name
        FROM
            storage.raw_material_warehouse rmw
        LEFT JOIN
            storage.inventory_detail id ON rmw.fields3 = id.material_id
        LEFT JOIN
            storage.warehouse_zone wz ON id.zone_code = wz.zone_code
        LEFT JOIN
            storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        ${ew.customSqlSegment}
    </select>

    <!-- 新增：不关联区域信息的查询，避免数据重复 -->
    <select id="selectPageWithoutZone" resultType="com.cpmes.system.vo.RawMaterialWarehouseVO">
        SELECT
            rmw.*,
            CASE
                WHEN rmw.board_type = '上板' THEN '上板'
                WHEN rmw.board_type = '下板' THEN '下板'
                WHEN rmw.board_type = '单板' THEN '单板'
                ELSE rmw.board_type
            END as board_type_name
        FROM
            storage.raw_material_warehouse rmw
        ${ew.customSqlSegment}
    </select>
</mapper>

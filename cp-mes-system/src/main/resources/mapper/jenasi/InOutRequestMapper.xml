<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapperJenasi.InOutRequestMapper">

    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.InOutRequest">
            <id property="requestId" column="request_id" />
            <result property="applicationType" column="application_type" />
            <result property="materialType" column="material_type" />
            <result property="materialId" column="material_id" />
            <result property="materialName" column="material_name" />
            <result property="quantity" column="quantity" />
            <result property="applicantName" column="applicant_name" />
            <result property="requestTime" column="request_time" />
            <result property="fields1" column="fields1" />
            <result property="fields2" column="fields2" />
            <result property="fields3" column="fields3" />
    </resultMap>
    <select id="selectFilteredMaterialSummary" resultType="com.cpmes.system.entity.vo.InOutCensusVO">
        SELECT
        material_name AS materialName,
        material_type AS materialType,
        application_type AS applicationType,
        SUM(quantity) AS totalQuantity
        FROM storage.in_out_request
        WHERE EXTRACT(YEAR FROM request_time) = #{year}
        AND EXTRACT(MONTH FROM request_time) = #{month}
        AND material_name ILIKE CONCAT('%', #{materialName}, '%')
        <if test="materialType != null and materialType != ''">
            AND material_type = #{materialType}
        </if>
        GROUP BY material_name, material_type, application_type
        ORDER BY material_name, material_type, application_type
    </select>



    <sql id="Base_Column_List">
        request_id,application_type,material_type,material_id,material_name,quantity,
        applicant_name,request_time,fields1,fields2,fields3
    </sql>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.BinOperationLogMapper">

<resultMap type="BinOperationLog" id="BinOperationLogResult">
<result property="logId" column="log_id"/>
<result property="zoneCode" column="zone_code"/>
<result property="operationType" column="operation_type"/>
<result property="materialId" column="material_id"/>
<result property="materialName" column="material_name"/>
<result property="quantityBefore" column="quantity_before"/>
<result property="quantityChange" column="quantity_change"/>
<result property="quantityAfter" column="quantity_after"/>
<result property="batchNo" column="batch_no"/>
<result property="sourceDocument" column="source_document"/>
<result property="operator" column="operator"/>
<result property="operationTime" column="operation_time"/>
<result property="operationReason" column="operation_reason"/>
<result property="verificationStatus" column="verification_status"/>
<result property="delFlag" column="del_flag"/>
<result property="createBy" column="create_by"/>
<result property="createTime" column="create_time"/>
<result property="updateBy" column="update_by"/>
<result property="updateTime" column="update_time"/>
<result property="remark" column="remark"/>
</resultMap>

<resultMap type="BinOperationLogVo" id="BinOperationLogVoResult">
<result property="logId" column="log_id"/>
<result property="zoneCode" column="zone_code"/>
<result property="zoneName" column="zone_name"/>
<result property="warehouseCode" column="warehouse_code"/>
<result property="warehouseType" column="warehouse_type"/>
<result property="operationType" column="operation_type"/>
<result property="materialId" column="material_id"/>
<result property="materialName" column="material_name"/>
<result property="quantityBefore" column="quantity_before"/>
<result property="quantityChange" column="quantity_change"/>
<result property="quantityAfter" column="quantity_after"/>
<result property="batchNo" column="batch_no"/>
<result property="sourceDocument" column="source_document"/>
<result property="operator" column="operator"/>
<result property="operationTime" column="operation_time"/>
<result property="operationReason" column="operation_reason"/>
<result property="verificationStatus" column="verification_status"/>
<result property="createBy" column="create_by"/>
<result property="createTime" column="create_time"/>
<result property="updateBy" column="update_by"/>
<result property="updateTime" column="update_time"/>
<result property="remark" column="remark"/>
</resultMap>

<sql id="selectBinOperationLogVo">
SELECT
    bol.log_id,
    bol.zone_code,
    wz.zone_name,
    wz.warehouse_code,
    wz.warehouse_type,
    bol.operation_type,
    bol.material_id,
    bol.material_name,
    bol.quantity_before,
    bol.quantity_change,
    bol.quantity_after,
    bol.batch_no,
    bol.source_document,
    bol.operator,
    bol.operation_time,
    bol.operation_reason,
    bol.verification_status,
    bol.create_by,
    bol.create_time,
    bol.update_by,
    bol.update_time,
    bol.remark
FROM storage.bin_operation_log bol
LEFT JOIN storage.warehouse_zone wz ON bol.zone_code = wz.zone_code
WHERE bol.del_flag = '0'
</sql>

<select id="selectBinOperationLogById" parameterType="Long" resultMap="BinOperationLogVoResult">
<include refid="selectBinOperationLogVo"/>
AND bol.log_id = #{logId}
LIMIT 1
</select>

<select id="selectBinOperationLogList" parameterType="BinOperationLog" resultMap="BinOperationLogVoResult">
<include refid="selectBinOperationLogVo"/>
<if test="zoneCode != null and zoneCode != ''">
    AND bol.zone_code LIKE '%' || #{zoneCode} || '%'
</if>
<if test="operationType != null and operationType != ''">
    AND bol.operation_type = #{operationType}
</if>
<if test="materialId != null and materialId != ''">
    AND bol.material_id LIKE '%' || #{materialId} || '%'
</if>
<if test="materialName != null and materialName != ''">
    AND bol.material_name LIKE '%' || #{materialName} || '%'
</if>
<if test="operator != null and operator != ''">
    AND bol.operator LIKE '%' || #{operator} || '%'
</if>
<if test="verificationStatus != null and verificationStatus != ''">
    AND bol.verification_status = #{verificationStatus}
</if>
<if test="params.beginOperationTime != null and params.beginOperationTime != ''">
    AND bol.operation_time >= #{params.beginOperationTime}::timestamp
</if>
<if test="params.endOperationTime != null and params.endOperationTime != ''">
    AND bol.operation_time &lt;= #{params.endOperationTime}::timestamp
</if>
ORDER BY bol.operation_time DESC, bol.log_id DESC
</select>

<select id="selectByZoneCode" parameterType="String" resultMap="BinOperationLogVoResult">
<include refid="selectBinOperationLogVo"/>
AND bol.zone_code = #{zoneCode}
ORDER BY bol.operation_time DESC, bol.log_id DESC
</select>

<select id="selectByOperationType" parameterType="String" resultMap="BinOperationLogVoResult">
<include refid="selectBinOperationLogVo"/>
AND bol.operation_type = #{operationType}
ORDER BY bol.operation_time DESC, bol.log_id DESC
</select>

<select id="selectByTimeRange" resultMap="BinOperationLogVoResult">
<include refid="selectBinOperationLogVo"/>
<where>
<if test="startTime != null">
AND bol.operation_time >= #{startTime}
</if>
<if test="endTime != null">
AND bol.operation_time &lt;= #{endTime}
</if>
</where>
ORDER BY bol.operation_time DESC, bol.log_id DESC
</select>

<insert id="insertOperationLog">
INSERT INTO storage.bin_operation_log (
    zone_code, operation_type, material_id, material_name,
    quantity_before, quantity_change, quantity_after, batch_no,
    source_document, operator, operation_time, operation_reason,
    verification_status, del_flag, create_time, remark
)
VALUES (
    #{zoneCode}, #{operationType}, #{materialId}, #{materialName},
    #{quantityBefore}, #{quantityChange}, #{quantityAfter}, #{batchNo},
    #{sourceDocument}, #{operator}, CURRENT_TIMESTAMP, #{operationReason},
    '0', '0', CURRENT_TIMESTAMP, #{remark}
)
</insert>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.SysOssMapper">

    <resultMap type="com.cpmes.system.domain.SysOss" id="SysOssResult">
        <result property="ossId" column="oss_id"/>
        <result property="fileName" column="file_name"/>
        <result property="originalName" column="original_name"/>
        <result property="fileSuffix" column="file_suffix"/>
        <result property="url" column="url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="service" column="service"/>
        <result property="userId" column="user_id"/>
        <result property="fileType" column="file_type"/>
        <result property="storageStrategy" column="storage_strategy"/>
        <result property="folderId" column="folder_id"/>
        <result property="fileSize" column="file_size"/>
    </resultMap>


</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.WarehouseZoneMapper">

    <resultMap type="WarehouseZoneVo" id="WarehouseZoneResult">
        <result property="zoneId"           column="zone_id"          />
        <result property="zoneCode"         column="zone_code"        />
        <result property="zoneName"         column="zone_name"        />
        <result property="warehouseCode"    column="warehouse_code"   />
        <result property="warehouseType"    column="warehouse_type"   />
        <result property="zoneType"         column="zone_type"        />
        <result property="zoneAttributes"   column="zone_attributes"  />
        <result property="zoneCapacity"     column="zone_capacity"    />
        <result property="currentUsage"     column="current_usage"    />
        <result property="maxWeight"        column="max_weight"       />
        <result property="currentWeight"    column="current_weight"   />
        <result property="zoneDescription"  column="zone_description" />
        <result property="zoneRules"        column="zone_rules"       />
        <result property="zoneQrCode"       column="zone_qr_code"     />
        <result property="qrCodePrintTime"  column="qr_code_print_time" />
        <result property="qrCodeVersion"    column="qr_code_version"  />
        <result property="status"           column="status"           />
        <result property="warehouseStatus"  column="warehouse_status" />

        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
    </resultMap>

    <sql id="selectWarehouseZoneVo">
        SELECT
            wz.zone_id,
            wz.zone_code,
            wz.zone_name,
            wz.warehouse_code,
            wz.warehouse_type,
            wz.zone_type,
            wz.zone_attributes,
            wz.zone_capacity,
            wz.current_usage,
            wz.max_weight,
            wz.current_weight,
            wz.zone_description,
            wz.zone_rules,
            wz.zone_qr_code,
            wz.qr_code_print_time,
            wz.qr_code_version,
            wz.status,
            COALESCE(wi.status, '1') as warehouse_status,
            wz.create_by,
            wz.create_time,
            wz.update_by,
            wz.update_time,
            wz.remark
        FROM storage.warehouse_zone wz
        LEFT JOIN storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        WHERE wz.del_flag = '0'
    </sql>

    <!-- 根据仓库编码查询区域列表 -->
    <select id="selectZonesByWarehouseCode" parameterType="String" resultMap="WarehouseZoneResult">
        <include refid="selectWarehouseZoneVo"/>
        AND wz.warehouse_code = #{warehouseCode}
        ORDER BY wz.zone_code
    </select>

    <!-- 根据区域编码查询区域信息 -->
    <select id="selectByZoneCode" parameterType="String" resultMap="WarehouseZoneResult">
        <include refid="selectWarehouseZoneVo"/>
        AND wz.zone_code = #{zoneCode}
        LIMIT 1
    </select>

</mapper>

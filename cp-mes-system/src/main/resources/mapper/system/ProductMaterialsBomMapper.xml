<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.ProductMaterialsBomMapper">

    <resultMap type="ProductMaterialsBom" id="ProductMaterialsBomResult">
        <id property="id" column="id"/>
        <result property="model" column="model"/>
        <result property="module" column="module"/>
        <result property="boardType" column="board_type"/>
        <result property="material" column="material"/>
        <result property="quantity" column="quantity"/>
        <result property="unit" column="unit"/>
        <result property="version" column="version"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="reservedField1" column="reserved_field1"/>
        <result property="reservedField2" column="reserved_field2"/>
        <result property="reservedField3" column="reserved_field3"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <resultMap type="com.cpmes.system.domain.vo.ProductMaterialsBomVo" id="ProductMaterialsBomVoResult">
        <id property="id" column="id"/>
        <result property="model" column="model"/>
        <result property="module" column="module"/>
        <result property="boardType" column="board_type"/>
        <result property="material" column="material"/>
        <result property="quantity" column="quantity"/>
        <result property="unit" column="unit"/>
        <result property="version" column="version"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="reservedField1" column="reserved_field1"/>
        <result property="reservedField2" column="reserved_field2"/>
        <result property="reservedField3" column="reserved_field3"/>
        <result property="productNumber" column="product_number"/>
        <result property="styleName" column="style_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <!-- 关联查询BOM和产品信息，包含款式名称 -->
    <select id="selectVoPageWithStyle" resultMap="ProductMaterialsBomVoResult">
        SELECT
            bom.id,
            bom.model,
            bom.module,
            bom.board_type,
            bom.material,
            bom.quantity,
            bom.unit,
            bom.version,
            bom.updated_at,
            bom.updated_by,
            bom.reserved_field1,
            bom.reserved_field2,
            bom.reserved_field3,
            bom.create_by,
            bom.create_time,
            bom.del_flag,
            bom.remark,
            bom.tenant_id,
            p.product_number,
            p.style_name
        FROM product_materials_bom bom
        LEFT JOIN product p ON bom.model = p.product_name AND p.del_flag = '0'
        ${ew.customSqlSegment}
    </select>

    <!-- 关联查询BOM和产品信息列表，包含款式名称 -->
    <select id="selectVoListWithStyle" resultMap="ProductMaterialsBomVoResult">
        SELECT
            bom.id,
            bom.model,
            bom.module,
            bom.board_type,
            bom.material,
            bom.quantity,
            bom.unit,
            bom.version,
            bom.updated_at,
            bom.updated_by,
            bom.reserved_field1,
            bom.reserved_field2,
            bom.reserved_field3,
            bom.create_by,
            bom.create_time,
            bom.del_flag,
            bom.remark,
            bom.tenant_id,
            p.product_number,
            p.style_name
        FROM product_materials_bom bom
        LEFT JOIN product p ON bom.model = p.product_name AND p.del_flag = '0'
        ${ew.customSqlSegment}
    </select>

</mapper>

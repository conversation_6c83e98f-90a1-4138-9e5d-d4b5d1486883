<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.WarehouseInfoMapper">

    <resultMap type="WarehouseInfo" id="WarehouseInfoResult">
        <result property="warehouseId"      column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"  />
        <result property="warehouseName"    column="warehouse_name"  />
        <result property="warehouseType"    column="warehouse_type"  />
        <result property="warehouseAddress" column="warehouse_address" />
        <result property="manager"          column="manager"         />
        <result property="contactPhone"     column="contact_phone"   />
        <result property="status"           column="status"          />
        <result property="description"      column="description"     />
        <result property="delFlag"          column="del_flag"        />
        <result property="createBy"         column="create_by"       />
        <result property="createTime"       column="create_time"     />
        <result property="updateBy"         column="update_by"       />
        <result property="updateTime"       column="update_time"     />
        <result property="remark"           column="remark"          />
    </resultMap>

    <sql id="selectWarehouseInfoVo">
        select warehouse_id, warehouse_code, warehouse_name, warehouse_type, warehouse_address, 
               manager, contact_phone, status, description, del_flag, create_by, create_time, 
               update_by, update_time, remark 
        from storage.warehouse_info
    </sql>

    <select id="selectWarehouseInfoList" parameterType="WarehouseInfo" resultMap="WarehouseInfoResult">
        <include refid="selectWarehouseInfoVo"/>
        <where>
            <if test="warehouseCode != null and warehouseCode != ''">
                AND warehouse_code ILIKE CONCAT('%', #{warehouseCode}, '%')
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                AND warehouse_name ILIKE CONCAT('%', #{warehouseName}, '%')
            </if>
            <if test="warehouseType != null and warehouseType != ''">
                AND warehouse_type = #{warehouseType}
            </if>
            <if test="manager != null and manager != ''">
                AND manager ILIKE CONCAT('%', #{manager}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            AND del_flag = '0'
        </where>
        ORDER BY warehouse_id
    </select>

    <select id="selectWarehouseInfoByWarehouseId" parameterType="Long" resultMap="WarehouseInfoResult">
        <include refid="selectWarehouseInfoVo"/>
        where warehouse_id = #{warehouseId} and del_flag = '0'
    </select>

    <select id="selectWarehouseInfoByWarehouseCode" parameterType="String" resultMap="WarehouseInfoResult">
        <include refid="selectWarehouseInfoVo"/>
        where warehouse_code = #{warehouseCode} and del_flag = '0'
    </select>

    <select id="checkWarehouseCodeUnique" parameterType="WarehouseInfo" resultType="int">
        select count(1) from storage.warehouse_info 
        where warehouse_code = #{warehouseCode} and del_flag = '0'
        <if test="warehouseId != null and warehouseId != 0">
            and warehouse_id != #{warehouseId}
        </if>
    </select>

    <select id="selectWarehouseInfoByType" parameterType="String" resultMap="WarehouseInfoResult">
        <include refid="selectWarehouseInfoVo"/>
        where warehouse_type = #{warehouseType} and status = '1' and del_flag = '0'
        ORDER BY warehouse_id
    </select>

    <insert id="insertWarehouseInfo" parameterType="WarehouseInfo" useGeneratedKeys="true" keyProperty="warehouseId">
        insert into storage.warehouse_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="warehouseType != null and warehouseType != ''">warehouse_type,</if>
            <if test="warehouseAddress != null">warehouse_address,</if>
            <if test="manager != null">manager,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">#{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="warehouseType != null and warehouseType != ''">#{warehouseType},</if>
            <if test="warehouseAddress != null">#{warehouseAddress},</if>
            <if test="manager != null">#{manager},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateWarehouseInfo" parameterType="WarehouseInfo">
        update storage.warehouse_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="warehouseType != null and warehouseType != ''">warehouse_type = #{warehouseType},</if>
            <if test="warehouseAddress != null">warehouse_address = #{warehouseAddress},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where warehouse_id = #{warehouseId} and del_flag = '0'
    </update>

    <update id="deleteWarehouseInfoByWarehouseIds" parameterType="String">
        update storage.warehouse_info set del_flag = '2' where warehouse_id in 
        <foreach item="warehouseId" collection="array" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>
    </update>

    <update id="updateWarehouseInfoStatus" parameterType="WarehouseInfo">
        update storage.warehouse_info set status = #{status}, update_by = #{updateBy}, update_time = NOW()
        where warehouse_id = #{warehouseId} and del_flag = '0'
    </update>

</mapper> 
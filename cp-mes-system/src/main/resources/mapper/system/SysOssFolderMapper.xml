<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.SysOssFolderMapper">

    <resultMap type="com.cpmes.system.domain.vo.SysOssFolderVo" id="SysOssFolderResult">
        <result property="folderId"        column="folder_id"        />
        <result property="folderName"      column="folder_name"      />
        <result property="folderPath"      column="folder_path"      />
        <result property="parentId"        column="parent_id"        />
        <result property="parentName"      column="parent_name"      />
        <result property="level"           column="level"            />
        <result property="userId"          column="user_id"          />
        <result property="storageStrategy" column="storage_strategy" />
        <result property="orderNum"        column="order_num"        />
        <result property="remark"          column="remark"           />
        <result property="createTime"      column="create_time"      />
        <result property="createBy"        column="create_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="fileCount"       column="file_count"       />
    </resultMap>

    <sql id="selectSysOssFolderVo">
        select f.folder_id, f.folder_name, f.folder_path, f.parent_id, f.level, 
               f.user_id, f.storage_strategy, f.order_num, f.remark,
               f.create_time, f.create_by, f.update_time, f.update_by,
               p.folder_name as parent_name
        from sys_oss_folder f
        left join sys_oss_folder p on f.parent_id = p.folder_id
    </sql>

    <select id="selectFolderTreeByUser" parameterType="map" resultMap="SysOssFolderResult">
        <include refid="selectSysOssFolderVo"/>
        where f.user_id = #{userId}
        <if test="storageStrategy != null and storageStrategy != ''">
            and f.storage_strategy = #{storageStrategy}
        </if>
        order by f.level asc, f.order_num asc, f.create_time asc
    </select>

    <select id="countFilesByFolder" parameterType="map" resultType="java.lang.Long">
        select count(1) from sys_oss 
        where folder_id = #{folderId} 
        and user_id = #{userId}
    </select>

</mapper> 
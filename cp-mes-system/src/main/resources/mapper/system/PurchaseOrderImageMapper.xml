<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.PurchaseOrderImageMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cpmes.system.entity.PurchaseOrderImage">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="purchaseOrderId" column="purchase_order_id" jdbcType="BIGINT"/>
        <result property="originalName" column="original_name" jdbcType="VARCHAR"/>
        <result property="storagePath" column="storage_path" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="contentType" column="content_type" jdbcType="VARCHAR"/>
        <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
        <result property="uploadUserId" column="upload_user_id" jdbcType="BIGINT"/>
        <result property="uploadUserName" column="upload_user_name" jdbcType="VARCHAR"/>
        <result property="imageDescription" column="image_description" jdbcType="VARCHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 包含用户名的结果映射 -->
    <resultMap id="ResultMapWithUserName" type="com.cpmes.system.entity.PurchaseOrderImage" extends="BaseResultMap">
        <result property="uploadUserName" column="upload_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, purchase_order_id, original_name, storage_path, file_size, content_type,
        upload_time, upload_user_id, upload_user_name, image_description, sort_order, status,
        create_time, update_time
    </sql>

    <!-- 根据采购订单ID查询图片列表（按排序序号排序） -->
    <select id="selectByPurchaseOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM "storage"."purchase_order_images"
        WHERE purchase_order_id = #{purchaseOrderId}
        AND status = 0
        ORDER BY sort_order ASC, upload_time ASC
    </select>

    <!-- 根据采购订单ID查询图片列表（包含上传人姓名） -->
    <select id="selectByPurchaseOrderIdWithUserName" resultMap="ResultMapWithUserName">
        SELECT
        id, purchase_order_id, original_name, storage_path, file_size,
        content_type, upload_time, upload_user_id, image_description,
        sort_order, status, create_time, update_time,
        upload_user_name
        FROM "storage"."purchase_order_images"
        WHERE purchase_order_id = #{purchaseOrderId}
        AND status = 0
        ORDER BY sort_order ASC, upload_time ASC
    </select>

    <!-- 批量插入图片记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO "storage"."purchase_order_images"
        (purchase_order_id, original_name, storage_path, file_size, content_type,
        upload_time, upload_user_id, upload_user_name, image_description, sort_order, status,
        create_time, update_time)
        VALUES
        <foreach collection="imageList" item="item" separator=",">
            (#{item.purchaseOrderId}, #{item.originalName}, #{item.storagePath},
            #{item.fileSize}, #{item.contentType}, #{item.uploadTime}, #{item.uploadUserId},
            #{item.uploadUserName}, #{item.imageDescription}, #{item.sortOrder}, #{item.status},
            #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据采购订单ID删除所有图片（逻辑删除） -->
    <update id="deleteByPurchaseOrderId">
        UPDATE "storage"."purchase_order_images"
        SET status = 1, update_time = CURRENT_TIMESTAMP
        WHERE purchase_order_id = #{purchaseOrderId}
        AND status = 0
    </update>

    <!-- 更新图片排序序号 -->
    <update id="updateSortOrder">
        UPDATE "storage"."purchase_order_images"
        SET sort_order = #{sortOrder}, update_time = CURRENT_TIMESTAMP
        WHERE id = #{imageId}
    </update>

</mapper>
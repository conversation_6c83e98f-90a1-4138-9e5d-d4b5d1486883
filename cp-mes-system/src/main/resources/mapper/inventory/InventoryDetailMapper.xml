<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpmes.system.mapper.InventoryDetailMapper">

    <resultMap type="InventoryDetailVo" id="InventoryDetailResult">
        <result property="detailId"         column="detail_id"        />
        <result property="zoneCode"         column="zone_code"        />
        <result property="zoneName"         column="zone_name"        />
        <result property="warehouseCode"    column="warehouse_code"   />
        <result property="warehouseName"    column="warehouse_name"   />
        <result property="warehouseType"    column="warehouse_type"   />
        <result property="materialId"       column="material_id"      />
        <result property="materialType"     column="material_type"    />
        <result property="materialName"     column="material_name"    />
        <result property="batchNo"          column="batch_no"         />
        <result property="currentStock"     column="current_stock"    />
        <result property="inboundQuantity"  column="inbound_quantity" />
        <result property="outboundQuantity" column="outbound_quantity"/>
        <result property="stockQuantity"    column="stock_quantity"   />
        <result property="minStockQuantity" column="min_stock_quantity"/>
        <result property="needPurchase"     column="need_purchase"    />
        <result property="unit"             column="unit"             />
        <result property="productionDate"   column="production_date"  />
        <result property="expiryDate"       column="expiry_date"      />
        <result property="sourceType"       column="source_type"      />
        <result property="sourceNo"         column="source_no"        />
        <result property="qrCode"           column="qr_code"          />
        <result property="entryDate"        column="entry_date"       />
        <result property="lastCheckDate"    column="last_check_date"  />
        <result property="status"           column="status"           />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
        <result property="boardType"        column="board_type"       />
        <result property="styleId"          column="style_id"         />
        <result property="styleName"        column="style_name"       />
        <result property="productNumber"    column="product_number"   />
        <result property="seriesId"         column="series_id"        />
        <result property="seriesName"       column="series_name"      />
        <result property="supplierId"       column="supplier_id"      />
        <result property="supplierName"     column="supplier_name"    />
    </resultMap>

    <!-- InventoryDetailVo结果映射 -->
    <resultMap type="InventoryDetailVo" id="InventoryDetailVoResult">
        <result property="detailId"         column="detail_id"        />
        <result property="zoneCode"         column="zone_code"        />
        <result property="zoneName"         column="zone_name"        />
        <result property="warehouseCode"    column="warehouse_code"   />
        <result property="warehouseName"    column="warehouse_name"   />
        <result property="warehouseType"    column="warehouse_type"   />
        <result property="materialId"       column="material_id"      />
        <result property="materialType"     column="material_type"    />
        <result property="materialName"     column="material_name"    />
        <result property="batchNo"          column="batch_no"         />
        <result property="currentStock"     column="current_stock"    />
        <result property="inboundQuantity"  column="inbound_quantity" />
        <result property="outboundQuantity" column="outbound_quantity"/>
        <result property="stockQuantity"    column="stock_quantity"   />
        <result property="minStockQuantity" column="min_stock_quantity"/>
        <result property="needPurchase"     column="need_purchase"    />
        <result property="unit"             column="unit"             />
        <result property="productionDate"   column="production_date"  />
        <result property="expiryDate"       column="expiry_date"      />
        <result property="sourceType"       column="source_type"      />
        <result property="sourceNo"         column="source_no"        />
        <result property="qrCode"           column="qr_code"          />
        <result property="entryDate"        column="entry_date"       />
        <result property="lastCheckDate"    column="last_check_date"  />
        <result property="status"           column="status"           />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
        <result property="boardType"        column="board_type"       />
        <result property="styleId"          column="style_id"         />
        <result property="styleName"        column="style_name"       />
        <result property="productNumber"    column="product_number"   />
        <result property="seriesId"         column="series_id"        />
        <result property="seriesName"       column="series_name"      />
        <result property="supplierId"       column="supplier_id"      />
        <result property="supplierName"     column="supplier_name"    />
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="selectInventoryDetailVo">
        SELECT
            id.detail_id,
            id.zone_code,
            wz.zone_name,
            wz.warehouse_code,
            wi.warehouse_name,
            wi.warehouse_type,
            id.material_id,
            id.material_type,
            id.material_name,
            id.batch_no,
            id.current_stock,
            id.inbound_quantity,
            id.outbound_quantity,
            id.stock_quantity,
            id.min_stock_quantity,
            id.need_purchase,
            id.unit,
            id.production_date,
            id.expiry_date,
            id.source_type,
            id.source_no,
            id.qr_code,
            id.entry_date,
            id.last_check_date,
            id.status,
            id.create_by,
            id.create_time,
            id.update_by,
            id.update_time,
            id.remark,
            id.board_type,
            id.style_id,
            fs.style_name,
            id.product_number,
            id.series_id,
            sp.series_name,
            id.supplier_id,
            id.supplier_name
        FROM storage.inventory_detail id
        LEFT JOIN storage.warehouse_zone wz ON id.zone_code = wz.zone_code
        LEFT JOIN storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        LEFT JOIN storage.functional_style fs ON id.style_id = fs.style_id
        LEFT JOIN storage.series_products sp ON id.series_id = sp.id
        WHERE id.del_flag = '0'
    </sql>

    <!-- 查询库存明细列表 -->
    <select id="selectInventoryDetailList" parameterType="InventoryDetail" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        <where>
            <if test="zoneCode != null and zoneCode != ''">
                AND id.zone_code = #{zoneCode}
            </if>
            <if test="materialId != null">
                AND id.material_id = #{materialId}
            </if>
            <if test="materialType != null and materialType != ''">
                AND id.material_type LIKE '%' || #{materialType} || '%'
            </if>
            <if test="materialName != null and materialName != ''">
                AND id.material_name LIKE '%' || #{materialName} || '%'
            </if>
            <if test="batchNo != null and batchNo != ''">
                AND id.batch_no LIKE '%' || #{batchNo} || '%'
            </if>
            <if test="sourceType != null and sourceType != ''">
                AND id.source_type = #{sourceType}
            </if>
            <if test="status != null and status != ''">
                AND id.status = #{status}
            </if>
        </where>
        ORDER BY id.update_time DESC
    </select>

    <!-- 查询库存明细详细信息 -->
    <select id="selectInventoryDetailById" parameterType="Long" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.detail_id = #{detailId}
    </select>

    <!-- 根据区域编码查询库存明细列表 -->
    <select id="selectInventoryDetailByZoneCode" parameterType="String" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.zone_code = #{zoneCode}
        ORDER BY id.update_time DESC
    </select>

    <!-- 获取库存统计信息 -->
    <select id="selectInventoryStatistics" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT id.detail_id) as total_items,
            COALESCE(SUM(id.current_stock), 0) as total_stock,
            COALESCE(SUM(CASE WHEN id.current_stock &lt;= id.min_stock_quantity THEN 1 ELSE 0 END), 0) as low_stock_items,
            COUNT(DISTINCT id.material_id) as total_materials,
            COUNT(DISTINCT wz.zone_id) as total_zones
        FROM storage.inventory_detail id
        LEFT JOIN storage.warehouse_zone wz ON id.zone_code = wz.zone_code
        LEFT JOIN storage.warehouse_info wi ON wz.warehouse_code = wi.warehouse_code
        WHERE id.del_flag = '0'
        <if test="params.materialType != null and params.materialType != ''">
            AND id.material_type = #{params.materialType}
        </if>
        <if test="params.zoneCode != null and params.zoneCode != ''">
            AND id.zone_code = #{params.zoneCode}
        </if>
        <if test="params.warehouseType != null and params.warehouseType != ''">
            AND wi.warehouse_type = #{params.warehouseType}
        </if>
    </select>

    <!-- 获取低库存预警列表 -->
    <select id="selectLowStockAlert" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.current_stock &lt;= id.min_stock_quantity
        AND id.status = '1'
        ORDER BY (id.current_stock - id.min_stock_quantity) ASC
    </select>

    <!-- 根据区域编码查询库存明细列表 -->
    <select id="selectInventoryDetailByZoneCode" parameterType="String" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.zone_code = #{zoneCode}
        ORDER BY id.update_time DESC
    </select>

    <!-- 根据物料ID和区域编码查询库存明细 -->
    <select id="selectByMaterialAndZoneCode" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.material_id = #{materialId}
        AND id.zone_code = #{zoneCode}
        <if test="batchNo != null and batchNo != ''">
            AND id.batch_no = #{batchNo}
        </if>
        LIMIT 1
    </select>

    <!-- 根据物料名称、物料类型、批次号和区域编码查询库存明细（用于唯一性校验） -->
    <select id="selectByMaterialNameTypeAndZoneCode" resultMap="InventoryDetailResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.material_name = #{materialName}
        AND id.material_type = #{materialType}
        AND id.zone_code = #{zoneCode}
        <if test="batchNo != null and batchNo != ''">
            AND id.batch_no = #{batchNo}
        </if>
        <if test="batchNo == null or batchNo == ''">
            AND (id.batch_no IS NULL OR id.batch_no = '')
        </if>
        LIMIT 1
    </select>

    <!-- 库存调整（参考成品仓库逻辑：结存数量每日重置） -->
    <update id="adjustInventoryStock">
        UPDATE storage.inventory_detail
        SET
            <!-- 根据调整类型和是否当日首次操作来处理入库数量 -->
            inbound_quantity = CASE
                WHEN #{adjustQuantity} > 0 THEN
                    CASE
                        WHEN DATE(update_time) = CURRENT_DATE THEN inbound_quantity + #{adjustQuantity}
                        ELSE #{adjustQuantity}
                    END
                ELSE
                    CASE
                        WHEN DATE(update_time) = CURRENT_DATE THEN inbound_quantity
                        ELSE 0
                    END
            END,
            <!-- 根据调整类型和是否当日首次操作来处理出库数量 -->
            outbound_quantity = CASE
                WHEN #{adjustQuantity} &lt; 0 THEN
                    CASE
                        WHEN DATE(update_time) = CURRENT_DATE THEN outbound_quantity + ABS(#{adjustQuantity})
                        ELSE ABS(#{adjustQuantity})
                    END
                ELSE
                    CASE
                        WHEN DATE(update_time) = CURRENT_DATE THEN outbound_quantity
                        ELSE 0
                    END
            END,
            <!-- 结存数量：如果不是当日首次操作则保持不变，否则设为调整前的当前库存 -->
            stock_quantity = CASE
                WHEN DATE(update_time) = CURRENT_DATE THEN stock_quantity
                ELSE current_stock
            END,
            <!-- 直接调整当前库存 -->
            current_stock = current_stock + #{adjustQuantity},
            <!-- 更新时间和操作信息 -->
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE detail_id = #{detailId} AND del_flag = '0'
    </update>

    <!-- 库存移库：扣减源库存 -->
    <update id="decreaseSourceStock">
        UPDATE storage.inventory_detail
        SET current_stock = current_stock - #{moveQuantity},
            outbound_quantity = outbound_quantity + #{moveQuantity},
            update_by = #{operator},
            update_time = CURRENT_TIMESTAMP,
            remark = COALESCE(remark, '') || '; 移出(数量:' || #{moveQuantity} || ')至区域ID:' || #{targetZoneId}
        WHERE detail_id = #{sourceDetailId}
          AND current_stock >= #{moveQuantity}
    </update>

    <!-- 库存移库：增加现有目标库存 -->
    <update id="increaseTargetStock">
        UPDATE storage.inventory_detail
        SET current_stock = current_stock + #{moveQuantity},
            inbound_quantity = inbound_quantity + #{moveQuantity},
            update_by = #{operator},
            update_time = CURRENT_TIMESTAMP,
            remark = COALESCE(remark, '') || '; 移入(数量:' || #{moveQuantity} || ')从库存明细ID:' || #{sourceDetailId}
        WHERE detail_id = #{targetDetailId}
    </update>

    <!-- 库存移库：插入新的目标库存明细 -->
    <insert id="insertNewTargetStock" parameterType="com.cpmes.system.domain.InventoryDetail">
        INSERT INTO storage.inventory_detail (
            zone_id, material_id, material_type, material_name, batch_no,
            current_stock, inbound_quantity, outbound_quantity, stock_quantity,
            min_stock_quantity, need_purchase, unit, production_date, expiry_date,
            source_type, source_no, qr_code, entry_date, status, del_flag,
            create_by, create_time, update_by, update_time, remark
        )
        VALUES (
            #{zoneId}, #{materialId}, #{materialType}, #{materialName}, #{batchNo},
            #{currentStock}, #{inboundQuantity}, 0, #{stockQuantity},
            #{minStockQuantity}, #{needPurchase}, #{unit}, #{productionDate}, #{expiryDate},
            #{sourceType}, #{sourceNo}, #{qrCode}, CURRENT_TIMESTAMP, '1', '0',
            #{createBy}, CURRENT_TIMESTAMP, #{updateBy}, CURRENT_TIMESTAMP, #{remark}
        )
    </insert>

    <!-- 批量更新库存状态 -->
    <update id="batchUpdateStatus">
        UPDATE storage.inventory_detail
        SET status = #{status},
            update_by = #{operator},
            update_time = CURRENT_TIMESTAMP
        WHERE detail_id IN
        <foreach collection="detailIds" item="detailId" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </update>

    <!-- 获取库存追溯信息 -->
    <select id="selectInventoryTrace" parameterType="Long" resultType="java.util.Map">
        SELECT
            'inventory_detail' as table_name,
            'current' as operation_type,
            id.create_time as operation_time,
            id.create_by as operator,
            ('当前库存: ' || id.current_stock || ' ' || COALESCE(id.unit, '件')) as operation_desc,
            COALESCE(id.remark, '初始库存记录') as remark
        FROM storage.inventory_detail id
        WHERE id.detail_id = #{detailId}

        UNION ALL

        SELECT
            'inventory_detail' as table_name,
            'update' as operation_type,
            id.update_time as operation_time,
            id.update_by as operator,
            ('最近更新: 入库(' || COALESCE(id.inbound_quantity, 0) || ') 出库(' || COALESCE(id.outbound_quantity, 0) || ') 结存(' || COALESCE(id.stock_quantity, 0) || ')') as operation_desc,
            COALESCE(id.remark, '库存更新') as remark
        FROM storage.inventory_detail id
        WHERE id.detail_id = #{detailId}
          AND id.update_time IS NOT NULL
          AND id.update_time != id.create_time

        UNION ALL

        SELECT
            'operation_history' as table_name,
            'history' as operation_type,
            CURRENT_TIMESTAMP as operation_time,
            'system' as operator,
            ('来源: ' || COALESCE(id.source_type, '未知') ||
             CASE
                WHEN id.source_no IS NOT NULL THEN (' - 单号: ' || id.source_no)
                ELSE ''
             END
            ) as operation_desc,
            ('入库日期: ' || COALESCE(TO_CHAR(id.entry_date, 'YYYY-MM-DD'), '未知') ||
             CASE
                WHEN id.production_date IS NOT NULL THEN (' | 生产日期: ' || TO_CHAR(id.production_date, 'YYYY-MM-DD'))
                ELSE ''
             END ||
             CASE
                WHEN id.expiry_date IS NOT NULL THEN (' | 到期日期: ' || TO_CHAR(id.expiry_date, 'YYYY-MM-DD'))
                ELSE ''
             END
            ) as remark
        FROM storage.inventory_detail id
        WHERE id.detail_id = #{detailId}

        ORDER BY operation_time DESC
    </select>

    <!-- 根据产品编码查询库存明细列表 -->
    <select id="selectInventoryDetailByProductNumber" parameterType="String" resultMap="InventoryDetailVoResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.product_number = #{productNumber}
        ORDER BY id.update_time DESC
    </select>

    <!-- 根据系列ID查询库存明细列表 -->
    <select id="selectInventoryDetailBySeriesId" parameterType="Integer" resultMap="InventoryDetailVoResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.series_id = #{seriesId}
        ORDER BY id.update_time DESC
    </select>

    <!-- 根据供应商ID查询库存明细列表 -->
    <select id="selectInventoryDetailBySupplierId" parameterType="Integer" resultMap="InventoryDetailVoResult">
        <include refid="selectInventoryDetailVo"/>
        AND id.supplier_id = #{supplierId}
        ORDER BY id.update_time DESC
    </select>

</mapper>

# 临时订单配置
temp-order:
  # 临时数据过期时间（分钟）
  expire-minutes: 30
  
  # 清理任务配置
  cleanup:
    # 是否启用自动清理
    enabled: true
    # 清理任务执行间隔（cron表达式）
    cron: "0 0 * * * ?"
    # 深度清理任务执行间隔
    deep-cleanup-cron: "0 0 2 * * ?"
  
  # Redis存储配置
  redis:
    # key前缀
    key-prefix: "temp:order:"
    # 是否启用Redis存储
    enabled: true
  
  # 文件存储配置
  file:
    # 临时文件存储路径 - 使用相对路径
    temp-path: "test"
    # 是否自动创建目录
    auto-create-dir: true
    # 文件清理策略
    cleanup-strategy: "time-based"  # time-based 或 size-based

  # 服务器访问配置
  server:
    # 外部访问主机地址（用于生成完整URL）
    # 如果为空，将自动检测本机IP地址
    # 开发环境建议设置为具体IP地址，如：************
    external-host: ""
    # 是否强制使用HTTPS
    force-https: false
    # 自定义端口（如果与server.port不同）
    external-port: 0
    
  # 功能开关
  features:
    # 是否启用临时链接管理
    temp-links-enabled: true
    # 是否启用临时图片管理
    temp-images-enabled: true
    # 是否启用数据迁移
    data-migration-enabled: true
    # 是否启用统计功能
    statistics-enabled: true
    
  # 限制配置
  limits:
    # 单个临时订单最大链接数
    max-links-per-order: 50
    # 单个临时订单最大图片数
    max-images-per-order: 20
    # 单个图片最大大小（MB）
    max-image-size-mb: 10
    # 临时数据最大存储时间（小时）
    max-storage-hours: 24

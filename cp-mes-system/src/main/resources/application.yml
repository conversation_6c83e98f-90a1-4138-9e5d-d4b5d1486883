spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 30分钟
      cache-null-values: false
      use-key-prefix: true
      key-prefix: "cp-mes:"
    # 确保缓存配置与framework模块的PlusSpringCacheManager兼容
    cache-names:
      - logistics:tracking#30m  # 物流追踪缓存，30分钟过期
      - logistics:companies#1h  # 物流公司列表缓存，1小时过期

    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 阿里云物流API调试日志配置 -->
    <appender name="LOGISTICS_API_DEBUG" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [LOGISTICS-API] %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="LOGISTICS_API_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/logistics-api-debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/logistics-api-debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- LogisticsQueryServiceImpl专用日志配置 - 强制DEBUG级别 -->
    <logger name="com.cpmes.system.service.impl.LogisticsQueryServiceImpl" level="DEBUG" additivity="true">
        <appender-ref ref="LOGISTICS_API_DEBUG"/>
        <appender-ref ref="LOGISTICS_API_FILE"/>
    </logger>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

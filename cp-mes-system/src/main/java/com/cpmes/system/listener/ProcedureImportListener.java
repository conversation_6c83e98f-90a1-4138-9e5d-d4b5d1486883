package com.cpmes.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cpmes.common.core.domain.entity.SysUser;
import com.cpmes.common.excel.ExcelListener;
import com.cpmes.common.excel.ExcelResult;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.ValidatorUtils;
import com.cpmes.common.utils.spring.SpringUtils;
import com.cpmes.system.domain.Defect;
import com.cpmes.system.domain.Procedure;
import com.cpmes.system.domain.bo.ProcedureImportBo;
import com.cpmes.system.mapper.DefectMapper;
import com.cpmes.system.mapper.ProcedureMapper;
import com.cpmes.system.mapper.SysUserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工序导入监听器
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Slf4j
public class ProcedureImportListener extends AnalysisEventListener<ProcedureImportBo> implements ExcelListener<ProcedureImportBo> {

    private final Boolean isUpdateSupport;
    private final ProcedureMapper procedureMapper;
    private final SysUserMapper sysUserMapper;
    private final DefectMapper defectMapper;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public ProcedureImportListener(Boolean isUpdateSupport) {
        this.isUpdateSupport = isUpdateSupport;
        this.procedureMapper = SpringUtils.getBean(ProcedureMapper.class);
        this.sysUserMapper = SpringUtils.getBean(SysUserMapper.class);
        this.defectMapper = SpringUtils.getBean(DefectMapper.class);
    }

    @Override
    public void invoke(ProcedureImportBo procedureImportBo, AnalysisContext context) {
        try {
            // 数据校验
            ValidatorUtils.validate(procedureImportBo);

            // 检查工序编号是否存在
            Procedure existProcedure = procedureMapper.selectOne(
                new LambdaQueryWrapper<Procedure>()
                    .eq(Procedure::getProcedureNumber, procedureImportBo.getProcedureNumber())
            );

            if (ObjectUtil.isNotNull(existProcedure)) {
                if (!isUpdateSupport) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、工序编号 ")
                        .append(procedureImportBo.getProcedureNumber()).append(" 已存在");
                    return;
                } else {
                    // 更新现有记录
                    updateProcedure(existProcedure, procedureImportBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、工序编号 ")
                        .append(procedureImportBo.getProcedureNumber()).append(" 更新成功");
                }
            } else {
                // 创建新记录
                insertProcedure(procedureImportBo);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、工序编号 ")
                    .append(procedureImportBo.getProcedureNumber()).append(" 导入成功");
            }

        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、工序编号 " + procedureImportBo.getProcedureNumber() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！");
    }

    @Override
    public ExcelResult<ProcedureImportBo> getExcelResult() {
        return new ExcelResult<ProcedureImportBo>() {
            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<ProcedureImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }

    /**
     * 插入新的工序记录
     */
    private void insertProcedure(ProcedureImportBo importBo) {
        Procedure procedure = new Procedure();
        BeanUtil.copyProperties(importBo, procedure);
        
        // 处理报工权限
        processReportingAuthority(procedure, importBo.getReportingName());
        
        // 处理不良品列表
        processDefectiveProducts(procedure, importBo.getDefectiveNames());
        
        // 设置默认值
        setDefaultValues(procedure);
        
        procedureMapper.insert(procedure);
    }

    /**
     * 更新现有工序记录
     */
    private void updateProcedure(Procedure existProcedure, ProcedureImportBo importBo) {
        BeanUtil.copyProperties(importBo, existProcedure, "procedureId", "createBy", "createTime");
        
        // 处理报工权限
        processReportingAuthority(existProcedure, importBo.getReportingName());
        
        // 处理不良品列表
        processDefectiveProducts(existProcedure, importBo.getDefectiveNames());
        
        // 处理默认值（包括是否打印二维码的转换）
        setDefaultValues(existProcedure);
        
        procedureMapper.updateById(existProcedure);
    }

    /**
     * 处理报工权限
     */
    private void processReportingAuthority(Procedure procedure, String reportingNames) {
        if (StrUtil.isBlank(reportingNames)) {
            procedure.setReportingAuthority("all");
            procedure.setReportingName("不限制");
            return;
        }

        if ("不限制".equals(reportingNames.trim())) {
            procedure.setReportingAuthority("all");
            procedure.setReportingName("不限制");
            return;
        }

        // 根据姓名查找用户ID
        String[] nameArray = reportingNames.split(",");
        List<Long> userIds = new ArrayList<>();
        List<String> validNames = new ArrayList<>();

        for (String name : nameArray) {
            if (StrUtil.isBlank(name)) continue;
            
            SysUser user = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getNickName, name.trim())
            );
            
            if (ObjectUtil.isNotNull(user)) {
                userIds.add(user.getUserId());
                validNames.add(user.getNickName());
            } else {
                throw new ServiceException("未找到姓名为 " + name.trim() + " 的用户");
            }
        }

        if (!userIds.isEmpty()) {
            procedure.setReportingAuthority(userIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            procedure.setReportingName(String.join(",", validNames));
        } else {
            procedure.setReportingAuthority("all");
            procedure.setReportingName("不限制");
        }
    }

    /**
     * 处理不良品列表
     */
    private void processDefectiveProducts(Procedure procedure, String defectiveNames) {
        if (StrUtil.isBlank(defectiveNames)) {
            return;
        }

        String[] nameArray = defectiveNames.split(",");
        List<Long> defectIds = new ArrayList<>();
        List<String> validNames = new ArrayList<>();

        for (String name : nameArray) {
            if (StrUtil.isBlank(name)) continue;
            
            Defect defect = defectMapper.selectOne(
                new LambdaQueryWrapper<Defect>()
                    .eq(Defect::getDefectName, name.trim())
            );
            
            if (ObjectUtil.isNotNull(defect)) {
                defectIds.add(defect.getDefectId());
                validNames.add(defect.getDefectName());
            } else {
                log.warn("未找到名称为 {} 的不良品项，将忽略该项", name.trim());
            }
        }

        if (!defectIds.isEmpty()) {
            procedure.setDefectiveProducts(defectIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            procedure.setDefectiveNames(String.join(",", validNames));
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(Procedure procedure) {
        if (StrUtil.isBlank(procedure.getPlannedQuantity())) {
            procedure.setPlannedQuantity("1");
        }
        
        if (ObjectUtil.isNull(procedure.getReportingRatio())) {
            procedure.setReportingRatio(1);
        }
        
        // 处理是否打印二维码字段：支持是/否和Y/N
        if (StrUtil.isNotBlank(procedure.getIsPrint())) {
            String isPrint = procedure.getIsPrint().trim();
            if ("是".equals(isPrint) || "Y".equalsIgnoreCase(isPrint)) {
                procedure.setIsPrint("Y");
            } else if ("否".equals(isPrint) || "N".equalsIgnoreCase(isPrint)) {
                procedure.setIsPrint("N");
            } else {
                procedure.setIsPrint("N"); // 默认为否
            }
        } else {
            procedure.setIsPrint("N");
        }
        
        // 处理空值字段的默认值
        if (StrUtil.isBlank(procedure.getReportingAuthority())) {
            procedure.setReportingAuthority("");
        }
        if (StrUtil.isBlank(procedure.getDefectiveProducts())) {
            procedure.setDefectiveProducts("");
        }
    }
} 
package com.cpmes.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cpmes.common.excel.ExcelListener;
import com.cpmes.common.excel.ExcelResult;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.ValidatorUtils;
import com.cpmes.common.utils.spring.SpringUtils;
import com.cpmes.system.domain.Product;
import com.cpmes.system.domain.bo.ProductImportBo;
import com.cpmes.system.mapper.ProductMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 产品导入监听器
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Slf4j
public class ProductImportListener extends AnalysisEventListener<ProductImportBo> implements ExcelListener<ProductImportBo> {

    private final ProductMapper productMapper;
    private final Boolean isUpdateSupport;
    private int successNum = 0;
    private int failureNum = 0;
    private StringBuilder successMsg = new StringBuilder();
    private StringBuilder failureMsg = new StringBuilder();

    public ProductImportListener(Boolean isUpdateSupport) {
        this.productMapper = SpringUtils.getBean(ProductMapper.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(ProductImportBo productImportBo, AnalysisContext context) {
        try {
            ValidatorUtils.validate(productImportBo);

            Product existProduct = productMapper.selectOne(
                new LambdaQueryWrapper<Product>()
                    .eq(Product::getProductNumber, productImportBo.getProductNumber())
            );

            if (ObjectUtil.isNotNull(existProduct)) {
                if (!isUpdateSupport) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、产品编号 ")
                        .append(productImportBo.getProductNumber()).append(" 已存在");
                    return;
                } else {
                    updateProduct(existProduct, productImportBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、产品编号 ")
                        .append(productImportBo.getProductNumber()).append(" 更新成功");
                }
            } else {
                insertProduct(productImportBo);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、产品编号 ")
                    .append(productImportBo.getProductNumber()).append(" 导入成功");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、产品编号 " + productImportBo.getProductNumber() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！");
    }

    @Override
    public ExcelResult<ProductImportBo> getExcelResult() {
        return new ExcelResult<ProductImportBo>() {
            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<ProductImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }

    private void insertProduct(ProductImportBo importBo) {
        Product product = new Product();
        BeanUtil.copyProperties(importBo, product);

        // 设置默认值
        setDefaultValues(product, importBo);

        productMapper.insert(product);
    }

    private void updateProduct(Product existProduct, ProductImportBo importBo) {
        BeanUtil.copyProperties(importBo, existProduct, "productId", "createBy", "createTime");

        // 设置默认值（仅在字段为空时）
        setDefaultValues(existProduct, importBo);

        productMapper.updateById(existProduct);
    }

    /**
     * 设置默认值
     * 库存单位默认为"个"
     * 产品规格默认为"无"
     * 产品属性默认为"无"
     */
    private void setDefaultValues(Product product, ProductImportBo importBo) {
        // 库存单位默认为"个"
        if (StrUtil.isBlank(product.getProductUnit()) && StrUtil.isBlank(importBo.getProductUnit())) {
            product.setProductUnit("个");
        }

//        // 产品规格默认为"无"
//        if (StrUtil.isBlank(product.getSpecification()) && StrUtil.isBlank(importBo.getSpecification())) {
//            product.setSpecification("无");
//        }

        // 产品属性默认为"无"
        if (StrUtil.isBlank(product.getProductAttribute()) && StrUtil.isBlank(importBo.getProductAttribute())) {
            product.setProductAttribute("无");
        }
    }
}

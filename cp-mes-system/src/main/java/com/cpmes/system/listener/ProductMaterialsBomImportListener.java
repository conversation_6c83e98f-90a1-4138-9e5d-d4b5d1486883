package com.cpmes.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cpmes.common.excel.ExcelListener;
import com.cpmes.common.excel.ExcelResult;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.common.utils.spring.SpringUtils;
import com.cpmes.common.helper.LoginHelper;
import com.cpmes.system.domain.ProductMaterialsBom;
import com.cpmes.system.domain.bo.ProductMaterialsBomImportBo;
import com.cpmes.system.domain.bo.ProductMaterialsBomBo;
import com.cpmes.system.domain.vo.ProductMaterialsBomVo;
import com.cpmes.system.domain.vo.ImportProcessResult;
import com.cpmes.system.service.IProductMaterialsBomService;
import com.cpmes.system.mapper.ProductMaterialsBomMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cpmes.system.serviceJenasi.RawMaterialWarehouseService;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品所需原材料明细导入监听器
 * 参考ExcelToMySQL.java的逻辑实现版本管理和批量导入
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Slf4j
public class ProductMaterialsBomImportListener extends AnalysisEventListener<ProductMaterialsBomImportBo> implements ExcelListener<ProductMaterialsBomImportBo> {

    private final IProductMaterialsBomService productMaterialsBomService;
    private final Boolean isUpdateSupport;
    private final String versionStrategy;    // 版本策略：auto=自动生成，custom=指定版本
    private final String targetVersion;      // 目标版本号（当versionStrategy=custom时使用）

    private int successNum = 0;
    private int failureNum = 0;
    private int skippedIdenticalGroups = 0;
    private int newVersionsCreated = 0;
    private StringBuilder successMsg = new StringBuilder();
    private StringBuilder failureMsg = new StringBuilder();

    /**
     * 收集所有数据，确保完整读取所有分页数据
     */
    private List<ProductMaterialsBomImportBo> allDataList = new ArrayList<>();

    public ProductMaterialsBomImportListener(Boolean isUpdateSupport) {
        this(isUpdateSupport, "auto", "");
    }

    public ProductMaterialsBomImportListener(Boolean isUpdateSupport, String versionStrategy, String targetVersion) {
        this.productMaterialsBomService = SpringUtils.getBean(IProductMaterialsBomService.class);
        this.isUpdateSupport = isUpdateSupport;
        this.versionStrategy = StringUtils.isNotBlank(versionStrategy) ? versionStrategy : "auto";
        this.targetVersion = StringUtils.isNotBlank(targetVersion) ? targetVersion.trim() : "";
    }

    @Override
    public void invoke(ProductMaterialsBomImportBo data, AnalysisContext context) {
        // 数据预验证，过滤明显无效的数据
        if (data == null) {
            log.debug("跳过null数据行");
            return;
        }

        // 调试Excel读取情况 - 检查物料编码字段
        if (log.isDebugEnabled()) {
            String rowInfo = "未知行";
            if (context != null && context.readRowHolder() != null) {
                rowInfo = "第" + (context.readRowHolder().getRowIndex() + 1) + "行";
            }
            log.debug("Excel读取调试 - {}: 型号=[{}], 原料=[{}], 物料编码=[{}]",
                rowInfo, data.getModel(), data.getMaterial(), data.getMaterialCode());
        }

        // 收集所有数据，不进行分批处理，确保完整读取所有分页数据
        allDataList.add(data);

        // 安全地获取工作表和行信息，兼容多数据块导入时context可能为null的情况
        String contextInfo = "未知位置";
        if (context != null && context.readSheetHolder() != null && context.readRowHolder() != null) {
            try {
                int sheetIndex = context.readSheetHolder().getSheetNo();
                int rowIndex = context.readRowHolder().getRowIndex();
                String sheetName = context.readSheetHolder().getSheetName();
                contextInfo = String.format("工作表[%s]第%d行", sheetName, rowIndex + 1);
            } catch (Exception e) {
                contextInfo = "位置信息获取失败";
            }
        }

        // 详细日志记录，帮助调试Excel读取过程
        if (log.isDebugEnabled()) {
            log.debug("读取{}数据: 型号={}, 模块={}, 板类型={}, 原料={}",
                contextInfo,
                data.getModel(), data.getModule(), data.getBoardType(), data.getMaterial());
        }

        // 实时统计，便于监控大文件导入进度
        if (allDataList.size() % 1000 == 0) {
            log.info("已读取 {} 条数据...", allDataList.size());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel解析完成，共读取{}条数据，开始处理...", allDataList.size());

        // 调试Excel读取结果 - 重点检查物料编码字段
        if (log.isDebugEnabled() && !allDataList.isEmpty()) {
            log.debug("=== Excel物料编码读取调试 ===");

            // 检查前3条记录的物料编码情况
            int checkCount = Math.min(3, allDataList.size());
            for (int i = 0; i < checkCount; i++) {
                ProductMaterialsBomImportBo bo = allDataList.get(i);
                log.debug("记录{}: 型号=[{}], 原料=[{}], 物料编码=[{}]",
                    i + 1, bo.getModel(), bo.getMaterial(), bo.getMaterialCode());

                if (bo.getMaterialCode() != null) {
                    log.debug("  物料编码长度: {}, 内容: '{}'", bo.getMaterialCode().length(), bo.getMaterialCode());
                }
            }

            // 统计物料编码情况
            long nullCount = allDataList.stream().mapToLong(bo -> bo.getMaterialCode() == null ? 1 : 0).sum();
            long emptyCount = allDataList.stream().mapToLong(bo ->
                bo.getMaterialCode() != null && bo.getMaterialCode().trim().isEmpty() ? 1 : 0).sum();
            long validCount = allDataList.stream().mapToLong(bo ->
                bo.getMaterialCode() != null && !bo.getMaterialCode().trim().isEmpty() ? 1 : 0).sum();

            log.debug("物料编码统计: null={}, 空字符串={}, 有效值={}, 总数={}",
                nullCount, emptyCount, validCount, allDataList.size());

            if (nullCount == allDataList.size()) {
                log.error("❌ 所有记录的物料编码都为null！Excel列名可能不匹配！");
                log.error("请检查Excel文件中是否有名为'物料编码'的列（注意：必须完全匹配，包括标点符号）");
            } else if (validCount > 0) {
                log.debug("✅ 检测到{}条有效的物料编码记录", validCount);
            }

            log.debug("===============================");
        }

        // 添加详细的数据统计日志
        logDataStatistics();

        processAllData();
        log.info("所有数据处理完成！");
    }

    /**
     * 记录数据统计信息，帮助调试
     */
    private void logDataStatistics() {
        log.info("=== 数据统计信息 ===");
        log.info("总读取行数: {}", allDataList.size());

        int validModelCount = 0;
        int emptyModelCount = 0;
        int validMaterialCount = 0;
        int emptyMaterialCount = 0;

        for (ProductMaterialsBomImportBo data : allDataList) {
            if (StringUtils.isNotBlank(data.getModel())) {
                validModelCount++;
            } else {
                emptyModelCount++;
            }

            if (StringUtils.isNotBlank(data.getMaterial())) {
                validMaterialCount++;
            } else {
                emptyMaterialCount++;
            }
        }

        log.info("有效型号数据: {}, 空型号数据: {}", validModelCount, emptyModelCount);
        log.info("有效原料数据: {}, 空原料数据: {}", validMaterialCount, emptyMaterialCount);
        log.info("==================");
    }

    @Override
    public ExcelResult<ProductMaterialsBomImportBo> getExcelResult() {
        return new ExcelResult<ProductMaterialsBomImportBo>() {
            @Override
            public String getAnalysis() {
                StringBuilder result = new StringBuilder();

                if (failureNum > 0) {
                    result.append("导入完成，但存在错误！");
                    result.append("<br/>总处理数据：").append(successNum + failureNum).append(" 条");
                    result.append("<br/>成功导入：").append(successNum).append(" 条");
                    result.append("<br/>失败记录：").append(failureNum).append(" 条");
                    result.append("<br/>创建新版本：").append(newVersionsCreated).append(" 个");
                    result.append("<br/>跳过重复配置：").append(skippedIdenticalGroups).append(" 个");
                    result.append("<br/><br/>错误详情：");
                    result.append(failureMsg.toString());
                } else {
                    result.append("恭喜您，数据已全部导入成功！");
                    result.append("<br/>总处理数据：").append(successNum).append(" 条");
                    result.append("<br/>创建新版本：").append(newVersionsCreated).append(" 个");
                    if (skippedIdenticalGroups > 0) {
                        result.append("<br/>跳过重复配置：").append(skippedIdenticalGroups).append(" 个");
                    }
                    result.append("<br/><br/>成功详情：");
                    result.append(successMsg.toString());
                }

                return result.toString();
            }

            @Override
            public List<ProductMaterialsBomImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }

    /**
     * 简化的数据处理方法
     * 委托给Service层进行实际的数据处理，确保事务管理正确
     */
    public void processAllData() {
        if (allDataList.isEmpty()) {
            log.warn("没有读取到任何数据");
            failureMsg.append("<br/>没有读取到任何数据");
            return;
        }

        log.info("监听器收集完成，共{}条数据，开始委托Service层处理...", allDataList.size());

        try {
            // 委托给Service层处理，确保事务管理正确
            // 注意：确保参数类型匹配，Boolean转换为boolean
            ImportProcessResult result = productMaterialsBomService.processBomImportData(
                allDataList,
                isUpdateSupport != null ? isUpdateSupport.booleanValue() : false,
                versionStrategy,
                targetVersion);

            // 复制结果到监听器的状态变量中
            successNum = result.getSuccessNum();
            failureNum = result.getFailureNum();
            skippedIdenticalGroups = result.getSkippedIdenticalGroups();
            newVersionsCreated = result.getNewVersionsCreated();
            successMsg = result.getSuccessMsg();
            failureMsg = result.getFailureMsg();

            log.info("Service层处理完成！成功: {}, 失败: {}, 新版本: {}, 跳过: {}",
                successNum, failureNum, newVersionsCreated, skippedIdenticalGroups);

        } catch (Exception e) {
            log.error("委托Service层处理数据时发生异常", e);
            failureNum = allDataList.size();
            failureMsg.append("<br/>数据处理失败：").append(e.getMessage());

            // 重新抛出异常，确保事务回滚
            throw new RuntimeException("BOM数据处理失败：" + e.getMessage(), e);
        }
    }

    /**
     * 处理用户指定版本号的导入逻辑
     */
    private void handleCustomVersionImport(String model, String module, String boardType,
                                         List<MaterialDetail> excelMaterials, String currentUserName,
                                         Map<String, List<MaterialDetail>> dbVersionMaterials) {

        // 检查目标版本是否已存在
        boolean targetVersionExists = dbVersionMaterials.containsKey(targetVersion);

        if (targetVersionExists) {
            // 覆盖现有版本
            List<MaterialDetail> existingMaterials = dbVersionMaterials.get(targetVersion);

            // 检查是否完全相同
            if (areMaterialListsIdentical(excelMaterials, existingMaterials)) {
                log.info("BOM组 [{}, {}, {}] 与目标版本 '{}' 配置完全相同，跳过导入",
                    model, module, boardType, targetVersion);
                skippedIdenticalGroups++;
                successNum += excelMaterials.size();
                successMsg.append("<br/>").append("BOM组 [")
                    .append(model).append(", ").append(module).append(", ").append(boardType)
                    .append("] 与目标版本 '").append(targetVersion).append("' 配置相同，跳过导入");
                return;
            }

            // 删除现有版本的数据
            try {
                deleteVersionData(model, module, boardType, targetVersion);
                log.info("已删除BOM组 [{}, {}, {}] 版本 '{}' 的现有数据",
                    model, module, boardType, targetVersion);
            } catch (Exception e) {
                log.error("删除现有版本数据失败", e);
                failureNum += excelMaterials.size();
                failureMsg.append("<br/>删除现有版本数据失败：").append(e.getMessage());
                return;
            }

            // 插入新数据到指定版本
            insertNewBomVersion(model, module, boardType, excelMaterials, targetVersion, currentUserName);
            successNum += excelMaterials.size();

            successMsg.append("<br/>").append("BOM组 [")
                .append(model).append(", ").append(module).append(", ").append(boardType)
                .append("] 已覆盖版本: ").append(targetVersion)
                .append("，共 ").append(excelMaterials.size()).append(" 种原材料");

            log.info("BOM组 [{}, {}, {}] 已覆盖版本 '{}', 原材料数量: {}",
                model, module, boardType, targetVersion, excelMaterials.size());
        } else {
            // 创建新版本
            insertNewBomVersion(model, module, boardType, excelMaterials, targetVersion, currentUserName);
            newVersionsCreated++;
            successNum += excelMaterials.size();

            successMsg.append("<br/>").append("BOM组 [")
                .append(model).append(", ").append(module).append(", ").append(boardType)
                .append("] 已创建新版本: ").append(targetVersion)
                .append("，共 ").append(excelMaterials.size()).append(" 种原材料");

            log.info("BOM组 [{}, {}, {}] 已创建新版本 '{}', 原材料数量: {}",
                model, module, boardType, targetVersion, excelMaterials.size());
        }
    }

    /**
     * 删除指定版本的BOM数据
     */
    private void deleteVersionData(String model, String module, String boardType, String version) {
        try {
            ProductMaterialsBomMapper mapper = SpringUtils.getBean(ProductMaterialsBomMapper.class);
            LambdaQueryWrapper<ProductMaterialsBom> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.eq(ProductMaterialsBom::getModel, model);
            deleteWrapper.eq(ProductMaterialsBom::getModule, module);
            deleteWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
            deleteWrapper.eq(ProductMaterialsBom::getVersion, version);

            int deletedCount = mapper.delete(deleteWrapper);
            log.debug("删除BOM数据：model={}, module={}, boardType={}, version={}, 删除数量={}",
                model, module, boardType, version, deletedCount);

        } catch (Exception e) {
            log.error("删除版本数据失败: model={}, module={}, boardType={}, version={}",
                model, module, boardType, version, e);
            throw new ServiceException("删除版本数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName() {
        try {
            return LoginHelper.getUsername();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户：{}", e.getMessage());
            return "系统导入";
        }
    }

    /**
     * 数据预处理和分组，支持BOM引用记录
     */
    private Map<String, List<MaterialDetail>> groupAndValidateData() {
        Map<String, List<MaterialDetail>> materialGroups = new HashMap<>();
        int processedCount = 0;
        int skippedCount = 0;

        log.info("开始处理和分组数据，共{}条", allDataList.size());

        // 获取RawMaterialWarehouseService实例
        RawMaterialWarehouseService rawMaterialWarehouseService = SpringUtils.getBean(RawMaterialWarehouseService.class);

        for (ProductMaterialsBomImportBo importBo : allDataList) {
            try {
                // 完全按照ExcelToMySQL.java的逻辑进行验证
                // 1. 检查必需字段完整性，参考ExcelToMySQL.java中的NUM_COLUMNS_PER_BLOCK检查
                if (StringUtils.isBlank(importBo.getModel())) {
                    skippedCount++;
                    log.debug("跳过空型号数据行");
                    continue;
                }

                String model = importBo.getModel().trim();
                // 对于module和boardType，参考ExcelToMySQL.java：如果为空则使用空字符串，而不是默认值
                String module = StringUtils.isNotBlank(importBo.getModule()) ? importBo.getModule().trim() : "";
                String boardType = StringUtils.isNotBlank(importBo.getBoardType()) ? importBo.getBoardType().trim() : "";
                String material = StringUtils.isNotBlank(importBo.getMaterial()) ? importBo.getMaterial().trim() : "";
                String unit = StringUtils.isNotBlank(importBo.getUnit()) ? importBo.getUnit().trim() : "";
                // 获取物料编码
                String materialCode = StringUtils.isNotBlank(importBo.getMaterialCode()) ? importBo.getMaterialCode().trim() : "";
                // 获取成品编码
                String productNumber = StringUtils.isNotBlank(importBo.getProductNumber()) ? importBo.getProductNumber().trim() : "";

                // 调试日志：检查物料编码和成品编码获取情况
                if (log.isDebugEnabled()) {
                    log.debug("编码获取调试: 物料编码原始值=[{}], 处理后=[{}], 成品编码原始值=[{}], 处理后=[{}]",
                        importBo.getMaterialCode(), materialCode, importBo.getProductNumber(), productNumber);
                }

                /*
                // 检查BOM引用相关字段（已废弃，注释掉）
                String referencedModel = StringUtils.isNotBlank(importBo.getReferencedModel()) ? importBo.getReferencedModel().trim() : "";
                String referencedModule = StringUtils.isNotBlank(importBo.getReferencedModule()) ? importBo.getReferencedModule().trim() : "";
                String referencedBoardType = StringUtils.isNotBlank(importBo.getReferencedBoardType()) ? importBo.getReferencedBoardType().trim() : "";

                // 判断是否为BOM引用记录
                boolean isBomReference = material.isEmpty() && !referencedModel.isEmpty();
                */

                // 判断是否为通用板块记录（material为null）
                boolean isGenericBomRecord = material.isEmpty();

                // 2. 根据记录类型进行不同的验证逻辑
                /*
                if (isBomReference) {
                    // BOM引用记录：暂不支持通过导入设置，跳过
                    skippedCount++;
                    log.debug("跳过BOM引用记录（暂不支持导入）：model={}, referencedModel={}", model, referencedModel);
                    continue;

                } else
                */
                if (isGenericBomRecord) {
                    // 通用板块记录：创建material为null的记录
                    handleGenericBomRecord(model, module, boardType);

                    processedCount++;

                    if (log.isDebugEnabled()) {
                        log.debug("处理通用板块记录: 型号={}, 模块={}, 板型={}", model, module, boardType);
                    }

                } else {
                    // 普通材料记录验证：material和unit为空则跳过，quantity <= 0 也跳过
                    if (material.isEmpty() || unit.isEmpty()) {
                        skippedCount++;
                        log.debug("跳过材料或单位为空的数据行：material={}, unit={}", material, unit);
                        continue;
                    }

                    // 处理物料编码逻辑（包含单位和板型信息）
                    if (StringUtils.isNotBlank(materialCode)) {
                        try {
                            // 调用Service处理物料编码、名称、单位与板型的关系
                            boolean processResult = rawMaterialWarehouseService.processRawMaterialByCodeWithUnitAndBoardType(materialCode, material, unit, boardType);
                            if (!processResult) {
                                log.warn("处理物料编码[{}]返回失败", materialCode);
                                // 记录警告但继续处理
                            } else {
                                log.info("成功处理物料编码[{}]与物料[{}]的关联，单位: {}, 板型: {}", materialCode, material, unit, boardType);
                            }
                        } catch (Exception e) {
                            log.error("处理物料编码[{}]时发生错误: {}", materialCode, e.getMessage(), e);
                            // 继续处理，不跳过
                        }
                    }

                    // 3. 数量处理，严格参考ExcelToMySQL.java的parseIntSafe方法
                    int quantity = parseQuantitySafe(importBo.getQuantity());
                    if (quantity <= 0) {
                        skippedCount++;
                        log.debug("跳过数量不大于0的数据行：quantity={}", quantity);
                        continue;
                    }

                    // 4. 对于module和boardType为空的情况，使用合适的默认值确保数据库完整性
                    if (module.isEmpty()) {
                        module = "标准款";  // 数据库not null约束
                    }
                    if (boardType.isEmpty()) {
                        boardType = "单层板";  // 数据库not null约束
                    }

                    // 5. 创建分组key，与ExcelToMySQL.java完全一致
                    String groupKey = model + "||" + module + "||" + boardType;

                    // 创建MaterialDetail对象（包含物料编码和成品编码）
                    ProductMaterialsBomImportListener.MaterialDetail materialDetail =
                        new MaterialDetail(material, quantity, unit, materialCode, productNumber);

                    // 调试日志：检查MaterialDetail中的编码信息
                    if (log.isDebugEnabled()) {
                        log.debug("MaterialDetail创建调试: 原料=[{}], 物料编码=[{}], 成品编码=[{}]",
                            materialDetail.getMaterial(), materialDetail.getMaterialCode(), materialDetail.getProductNumber());
                    }

                    materialGroups.computeIfAbsent(groupKey, k -> new ArrayList<>())
                        .add(materialDetail);

                    processedCount++;

                    // 详细日志记录，帮助调试
                    if (log.isDebugEnabled()) {
                        log.debug("处理数据行: 型号={}, 模块={}, 板型={}, 原料={}, 数量={}, 单位={}, 物料编码={}, 成品编码={}",
                            model, module, boardType, material, quantity, unit, materialCode, productNumber);
                    }
                }

            } catch (Exception e) {
                log.error("数据处理失败: {}", e.getMessage(), e);
                failureNum++;
                failureMsg.append("<br/>数据处理失败：").append(e.getMessage());
                skippedCount++;
            }
        }

        log.info("数据分组完成：总数据{}条，有效数据{}条，跳过{}条，分组{}个",
            allDataList.size(), processedCount, skippedCount, materialGroups.size());

        return materialGroups;
    }

    /**
     * 处理通用板块记录，创建material为null的记录
     */
    private void handleGenericBomRecord(String model, String module, String boardType) {
        try {
            // 获取当前用户名
            String currentUserName = getCurrentUserName();

            // 调用Service创建通用板块记录
            Boolean result = productMaterialsBomService.createGenericBomRecord(
                model, module, boardType, currentUserName
            );

            if (result) {
                successNum++;
                successMsg.append("<br/>").append("成功导入通用板块记录：")
                    .append(model).append("-").append(module).append("-").append(boardType);
                log.info("成功导入通用板块记录：{}-{}-{}", model, module, boardType);
            } else {
                failureNum++;
                failureMsg.append("<br/>").append("导入通用板块记录失败：")
                    .append(model).append("-").append(module).append("-").append(boardType);
            }
        } catch (Exception e) {
            log.error("处理通用板块记录失败", e);
            failureNum++;
            failureMsg.append("<br/>").append("处理通用板块记录失败：").append(e.getMessage());
        }
    }

    /**
     * 安全解析数量，完全参考ExcelToMySQL.java的parseIntSafe方法
     */
    private int parseQuantitySafe(String quantityStr) {
        if (StringUtils.isBlank(quantityStr)) {
            return 0; // ExcelToMySQL.java中空值返回0
        }

        try {
            // 参考ExcelToMySQL.java：先转double再转int
            return (int) Double.parseDouble(quantityStr.trim());
        } catch (NumberFormatException e) {
            log.warn("数量格式错误: '{}', 默认为0", quantityStr);
            return 0;
        }
    }

    /**
     * 获取数据库中已存在的版本，完全参考ExcelToMySQL.java的fetchExistingVersions方法
     * 使用精确匹配查询，避免Service层的模糊查询影响结果
     */
    private Map<String, List<MaterialDetail>> fetchExistingVersions(String model, String module, String boardType) {
        Map<String, List<MaterialDetail>> versions = new HashMap<>();

        try {
            // 直接使用Mapper进行精确查询，避免Service层的like查询
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getModel, model);
            lqw.eq(ProductMaterialsBom::getModule, module);
            lqw.eq(ProductMaterialsBom::getBoardType, boardType);
            lqw.orderBy(true, true, ProductMaterialsBom::getVersion);
            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            // 通过SpringUtils获取Mapper
            ProductMaterialsBomMapper mapper = SpringUtils.getBean(ProductMaterialsBomMapper.class);
            List<ProductMaterialsBomVo> existingBomVos = mapper.selectVoList(lqw);

            // 按版本分组，完全按照ExcelToMySQL.java的逻辑
            for (ProductMaterialsBomVo vo : existingBomVos) {
                String version = vo.getVersion();
                String material = vo.getMaterial();
                Integer quantity = vo.getQuantity();
                String unit = vo.getUnit();
                // 获取物料编码（从预留字段3）
                String materialCode = vo.getReservedField3();
                // 获取成品编码
                String productNumber = vo.getProductNumber();

                // 确保数据完整性
                if (StringUtils.isBlank(version) || StringUtils.isBlank(material) ||
                    quantity == null || StringUtils.isBlank(unit)) {
                    continue;
                }

                versions.computeIfAbsent(version, k -> new ArrayList<>())
                    .add(new MaterialDetail(material, quantity, unit, materialCode, productNumber));
            }

            // 对每个版本的材料列表排序，与ExcelToMySQL.java保持一致
            for (List<MaterialDetail> materialList : versions.values()) {
                Collections.sort(materialList);
            }

            log.debug("查询到现有版本: model={}, module={}, boardType={}, 版本数={}",
                model, module, boardType, versions.size());

        } catch (Exception e) {
            log.error("查询现有版本失败: model={}, module={}, boardType={}", model, module, boardType, e);
        }

        return versions;
    }

    /**
     * 比较两个材料列表是否完全相同，参考ExcelToMySQL.java的areMaterialListsIdentical方法
     * 注意：此方法不考虑物料编码，只比较材料、数量和单位，因为物料编码是辅助信息
     */
    private boolean areMaterialListsIdentical(List<MaterialDetail> list1, List<MaterialDetail> list2) {
        if (list1.size() != list2.size()) {
            return false;
        }

        // 对列表排序，确保比较的一致性
        Collections.sort(list1);
        Collections.sort(list2);

        for (int i = 0; i < list1.size(); i++) {
            MaterialDetail m1 = list1.get(i);
            MaterialDetail m2 = list2.get(i);

            if (!m1.getMaterial().equals(m2.getMaterial()) ||
                m1.getQuantity() != m2.getQuantity() ||
                !m1.getUnit().equals(m2.getUnit())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取最新版本，参考ExcelToMySQL.java的getLatestAppVersion方法
     */
    private Version getLatestVersion(Set<String> versionStrings) {
        if (versionStrings == null || versionStrings.isEmpty()) {
            return null;
        }

        Version latest = null;
        for (String versionStr : versionStrings) {
            try {
                Version currentVersion = new Version(versionStr);
                if (latest == null || currentVersion.compareTo(latest) > 0) {
                    latest = currentVersion;
                }
            } catch (IllegalArgumentException e) {
                log.warn("跳过无效版本号: {}", versionStr);
            }
        }

        return latest;
    }

    /**
     * 插入新版本的BOM数据，参考ExcelToMySQL.java的insertNewBomVersion方法
     * 使用真正的批量插入提高性能
     */
    private void insertNewBomVersion(String model, String module, String boardType,
                                   List<MaterialDetail> materials, String version, String updatedBy) {
        try {
            // 批量构建BO对象
            List<ProductMaterialsBomBo> boList = new ArrayList<>();
            for (MaterialDetail material : materials) {
                ProductMaterialsBomBo bo = new ProductMaterialsBomBo();
                bo.setModel(model);
                bo.setModule(module);
                bo.setBoardType(boardType);
                bo.setMaterial(material.getMaterial());
                bo.setQuantity(material.getQuantity());
                bo.setUnit(material.getUnit());
                bo.setVersion(version);
                bo.setUpdatedBy(updatedBy);

                // 保存物料编码到预留字段3（确保字段总是被设置，避免MyBatis-Plus的NOT_NULL策略跳过）
                if (StringUtils.isNotBlank(material.getMaterialCode())) {
                    bo.setMaterialCode(material.getMaterialCode());
                } else {
                    // 即使物料编码为空，也设置空字符串，确保字段被包含在INSERT语句中
                    bo.setMaterialCode("");
                }

                // 保存成品编码到productNumber字段
                if (StringUtils.isNotBlank(material.getProductNumber())) {
                    bo.setProductNumber(material.getProductNumber());
                } else {
                    // 即使成品编码为空，也设置空字符串，确保字段被包含在INSERT语句中
                    bo.setProductNumber("");
                }

                // 调试日志：检查编码保存情况
                if (log.isDebugEnabled()) {
                    log.debug("编码保存调试: 原料=[{}], 物料编码=[{}], 成品编码=[{}]",
                        material.getMaterial(), material.getMaterialCode(), material.getProductNumber());
                }

                // 注释：移除成品编码同步功能
                // 原料表同步逻辑已移除，仅保留物料编码和物料名称的同步功能
                // 成品编码不再同步到原料仓库表中

                boList.add(bo);
            }

            // 使用批量插入提高性能，参考ExcelToMySQL.java的批量处理逻辑
            // 分批插入，每批1000条，避免单次操作数据量过大
            int batchSize = 1000;
            for (int i = 0; i < boList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, boList.size());
                List<ProductMaterialsBomBo> batchList = boList.subList(i, endIndex);

                for (ProductMaterialsBomBo bo : batchList) {
                    boolean saveResult = productMaterialsBomService.insertByBo(bo);
                    if (!saveResult) {
                        throw new ServiceException("保存材料 " + bo.getMaterial() + " 失败");
                    }
                }
            }

            log.debug("成功插入BOM版本: model={}, module={}, boardType={}, version={}, 材料数量={}",
                model, module, boardType, version, materials.size());
        } catch (Exception e) {
            log.error("插入BOM版本失败", e);
            throw new ServiceException("插入BOM版本失败：" + e.getMessage());
        }
    }

    /**
     * 材料详情类，参考ExcelToMySQL.java的MaterialDetail类
     */
    public static class MaterialDetail implements Comparable<MaterialDetail> {
        private final String material;
        private final int quantity;
        private final String unit;
        private final String materialCode;
        private final String productNumber;  // 新增：成品编码字段

        // 3参数构造函数，兼容旧代码
        public MaterialDetail(String material, int quantity, String unit) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = "";  // 默认为空字符串
            this.productNumber = "";  // 默认为空字符串
        }

        // 4参数构造函数，支持物料编码（兼容现有代码）
        public MaterialDetail(String material, int quantity, String unit, String materialCode) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = materialCode != null ? materialCode : "";
            this.productNumber = "";  // 默认为空字符串
        }

        // 5参数构造函数，完整支持物料编码和成品编码
        public MaterialDetail(String material, int quantity, String unit, String materialCode, String productNumber) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = materialCode != null ? materialCode : "";
            this.productNumber = productNumber != null ? productNumber : "";
        }

        public String getMaterial() { return material; }
        public int getQuantity() { return quantity; }
        public String getUnit() { return unit; }
        public String getMaterialCode() { return materialCode; }
        public String getProductNumber() { return productNumber; }  // 新增：获取成品编码

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MaterialDetail that = (MaterialDetail) o;
            return quantity == that.quantity &&
                   Objects.equals(material, that.material) &&
                   Objects.equals(unit, that.unit);
            // 注意：物料编码不参与equals比较，因为它是辅助信息
        }

        @Override
        public int hashCode() {
            return Objects.hash(material, quantity, unit);
        }

        @Override
        public int compareTo(MaterialDetail o) {
            int materialCompare = this.material.compareTo(o.material);
            if (materialCompare != 0) return materialCompare;
            int quantityCompare = Integer.compare(this.quantity, o.quantity);
            if (quantityCompare != 0) return quantityCompare;
            return this.unit.compareTo(o.unit);
        }

        @Override
        public String toString() {
            return String.format("MaterialDetail{material='%s', quantity=%d, unit='%s', materialCode='%s', productNumber='%s'}",
                material, quantity, unit, materialCode, productNumber);
        }
    }

    /**
     * 版本类，参考ExcelToMySQL.java的Version类
     */
    private static class Version implements Comparable<Version> {
        private final int major;
        private final int minor;

        public Version(String versionStr) {
            if (StringUtils.isBlank(versionStr)) {
                this.major = 1;
                this.minor = 0;
                return;
            }

            if ("1.0".equals(versionStr) || "1".equals(versionStr.trim())) {
                this.major = 1;
                this.minor = 0;
                return;
            }

            String[] parts = versionStr.split("\\.");
            if (parts.length == 2) {
                try {
                    this.major = Integer.parseInt(parts[0]);
                    this.minor = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("版本号格式错误: " + versionStr, e);
                }
            } else {
                throw new IllegalArgumentException("版本号必须是 'major.minor' 格式: " + versionStr);
            }
        }

        public Version(int major, int minor) {
            this.major = major;
            this.minor = minor;
        }

        /**
         * 自动递增版本号
         */
        public static Version autoIncrement(Version latestVersion) {
            if (latestVersion == null) {
                return new Version(1, 0); // 默认初始版本 "1.0"
            }

            if (latestVersion.minor == 9) { // 例如 1.9 -> 2.0
                return new Version(latestVersion.major + 1, 0);
            } else { // 例如 1.0 -> 1.1
                return new Version(latestVersion.major, latestVersion.minor + 1);
            }
        }

        @Override
        public String toString() {
            return major + "." + minor;
        }

        @Override
        public int compareTo(Version other) {
            if (this.major != other.major) {
                return Integer.compare(this.major, other.major);
            }
            return Integer.compare(this.minor, other.minor);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Version version = (Version) o;
            return major == version.major && minor == version.minor;
        }

        @Override
        public int hashCode() {
            return Objects.hash(major, minor);
        }
    }
}

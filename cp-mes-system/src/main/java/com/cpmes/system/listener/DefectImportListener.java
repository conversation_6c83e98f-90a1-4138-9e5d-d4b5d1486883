package com.cpmes.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cpmes.common.excel.ExcelListener;
import com.cpmes.common.excel.ExcelResult;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.ValidatorUtils;
import com.cpmes.common.utils.spring.SpringUtils;
import com.cpmes.system.domain.Defect;
import com.cpmes.system.domain.bo.DefectImportBo;
import com.cpmes.system.mapper.DefectMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 不良项导入监听器
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Slf4j
public class DefectImportListener extends AnalysisEventListener<DefectImportBo> implements ExcelListener<DefectImportBo> {

    private final DefectMapper defectMapper;

    private final Boolean isUpdateSupport;

    private int successNum = 0;
    private int failureNum = 0;
    private StringBuilder successMsg = new StringBuilder();
    private StringBuilder failureMsg = new StringBuilder();

    public DefectImportListener(Boolean isUpdateSupport) {
        this.defectMapper = SpringUtils.getBean(DefectMapper.class);
        this.isUpdateSupport = isUpdateSupport;
    }

    @Override
    public void invoke(DefectImportBo defectImportBo, AnalysisContext context) {
        try {
            // 数据校验
            ValidatorUtils.validate(defectImportBo);

            // 检查不良项编号是否存在
            Defect existDefect = defectMapper.selectOne(
                new LambdaQueryWrapper<Defect>()
                    .eq(Defect::getDefectNumber, defectImportBo.getDefectNumber())
            );

            if (ObjectUtil.isNotNull(existDefect)) {
                if (!isUpdateSupport) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、不良项编号 ")
                        .append(defectImportBo.getDefectNumber()).append(" 已存在");
                    return;
                } else {
                    // 更新现有记录
                    updateDefect(existDefect, defectImportBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、不良项编号 ")
                        .append(defectImportBo.getDefectNumber()).append(" 更新成功");
                }
            } else {
                // 创建新记录
                insertDefect(defectImportBo);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、不良项编号 ")
                    .append(defectImportBo.getDefectNumber()).append(" 导入成功");
            }

        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、不良项编号 " + defectImportBo.getDefectNumber() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！");
    }

    @Override
    public ExcelResult<DefectImportBo> getExcelResult() {
        return new ExcelResult<DefectImportBo>() {
            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<DefectImportBo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }

    /**
     * 插入新的不良项记录
     */
    private void insertDefect(DefectImportBo importBo) {
        Defect defect = new Defect();
        BeanUtil.copyProperties(importBo, defect);
        defectMapper.insert(defect);
    }

    /**
     * 更新现有不良项记录
     */
    private void updateDefect(Defect existDefect, DefectImportBo importBo) {
        BeanUtil.copyProperties(importBo, existDefect, "defectId", "createBy", "createTime");
        defectMapper.updateById(existDefect);
    }
} 
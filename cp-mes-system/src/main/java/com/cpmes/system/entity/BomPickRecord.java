package com.cpmes.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BOM领料记录表
 * @TableName bom_pick_record
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("\"storage\".\"bom_pick_record\"")
//@TableName(value ="bom_pick_record")
public class BomPickRecord {
    /**
     * 主键，自增
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联工序任务ID
     */
    @TableField(value = "step_task_id")
    private Long stepTaskId;

    /**
     * 原料名称
     */
    @TableField(value = "item_name")
    private String itemName;

    /**
     * 领料数量
     */
    @TableField(value = "quantity")
    private Integer quantity;
}

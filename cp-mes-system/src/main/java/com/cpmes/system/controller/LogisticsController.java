package com.cpmes.system.controller;

import com.cpmes.common.core.domain.R;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.vo.LogisticsCompanyVO;
import com.cpmes.system.entity.vo.TrackingNumberVO;
import com.cpmes.system.service.LogisticsQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 物流查询控制器
 *
 * <AUTHOR> System
 * @date 2024-01-01
 */
@Tag(name = "物流查询管理")
@RestController
@RequestMapping("/system/logistics")
@Slf4j
@Validated
public class LogisticsController {

    @Autowired
    private LogisticsQueryService logisticsQueryService;

    /**
     * 查询物流轨迹信息 - 物流公司参数可选
     */
    @Operation(summary = "查询物流轨迹信息")
    @PostMapping("/query")
    public R<LogisticsTrackingDto> queryLogistics(
            @Parameter(description = "快递单号", required = true)
            @RequestParam String trackingNumber,
            @Parameter(description = "物流公司（可选，系统可自动识别）", required = false)
            @RequestParam(required = false) String logisticsCompany) {

        if (StringUtils.isEmpty(trackingNumber)) {
            return R.fail("快递单号不能为空");
        }

        try {
            log.info("查询物流轨迹: trackingNumber={}, logisticsCompany={}", trackingNumber, logisticsCompany);

            // 物流公司参数为可选，可以为null或空字符串
            String company = (logisticsCompany != null && !logisticsCompany.trim().isEmpty())
                ? logisticsCompany.trim()
                : null;

            LogisticsTrackingDto result = logisticsQueryService.queryLogistics(
                    trackingNumber.trim().toUpperCase(),
                    company
            );

            if (result.isQuerySuccess()) {
                return R.ok("查询成功", result);
            } else {
                // 即使查询失败，也返回基本信息
                String message = result.getErrorMessage() != null ? result.getErrorMessage() : "查询失败";
                return R.ok(message, result);
            }

        } catch (Exception e) {
            log.error("查询物流轨迹异常: trackingNumber={}, logisticsCompany={}, error={}",
                    trackingNumber, logisticsCompany, e.getMessage(), e);
            return R.fail("查询物流信息失败: " + e.getMessage());
        }
    }
    /**
     * 紧急清除指定单号缓存
     */
    @PostMapping("/emergencyClearCache/{trackingNumber}")
    public R<Map<String, Object>> emergencyClearCache(@PathVariable String trackingNumber) {
        try {
            log.info("=== 紧急清除缓存操作开始 ===");
            log.info("目标快递单号: {}", trackingNumber);

            // 1. 清除Spring Cache
            logisticsQueryService.clearCache(trackingNumber);
            log.info("Spring Cache清除完成");

            // 2. 如果使用Redis，也清除Redis缓存
            try {
                // 这里可以添加Redis缓存清除逻辑
                log.info("Redis缓存清除完成（如果配置了Redis）");
            } catch (Exception e) {
                log.warn("Redis缓存清除失败，可能未配置Redis: {}", e.getMessage());
            }

            // 3. 等待确保缓存清除生效
            Thread.sleep(2000);

            Map<String, Object> result = new HashMap<>();
            result.put("trackingNumber", trackingNumber);
            result.put("cacheCleared", true);
            result.put("timestamp", new Date());
            result.put("message", "缓存已清除，可以重新查询");

            log.info("=== 紧急清除缓存操作完成 ===");

            return R.ok(result);

        } catch (Exception e) {
            log.error("紧急清除缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            return R.fail("清除缓存失败: " + e.getMessage());
        }
    }
    /**
     * 获取物流公司列表
     */
    @Operation(summary = "获取物流公司列表")
    @GetMapping("/companies")
    public R<List<LogisticsCompanyVO>> getLogisticsCompanies() {
        try {
            List<LogisticsCompanyVO> companies = logisticsQueryService.getLogisticsCompanyList();
            return R.ok("获取成功", companies);
        } catch (Exception e) {
            log.error("获取物流公司列表失败: {}", e.getMessage(), e);
            return R.fail("获取物流公司列表失败");
        }
    }

    /**
     * 清除物流查询缓存
     */
    @Operation(summary = "清除物流查询缓存")
    @DeleteMapping("/cache/{trackingNumber}")
    public R<Void> clearLogisticsCache(
            @Parameter(description = "快递单号", required = true)
            @PathVariable String trackingNumber) {

        if (StringUtils.isEmpty(trackingNumber)) {
            return R.fail("快递单号不能为空");
        }

        try {
            logisticsQueryService.clearCache(trackingNumber);
            return R.ok("缓存清除成功");
        } catch (Exception e) {
            log.error("清除物流缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            return R.fail("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取快递单号管理列表
     */
    @Operation(summary = "获取快递单号管理列表")
    @GetMapping("/tracking-numbers")
    public R<List<TrackingNumberVO>> getTrackingNumbers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {

        try {
            log.info("获取快递单号管理列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

            // 这里可以实现从数据库查询快递单号列表的逻辑
            // List<TrackingNumberVO> result = trackingNumberService.getTrackingNumbers(pageNum, pageSize, keyword);

            // 暂时返回模拟数据，实际项目中应该从数据库查询
            List<TrackingNumberVO> result = createMockTrackingNumbers();

            // 如果有搜索关键词，进行过滤
            if (StringUtils.isNotEmpty(keyword)) {
                result = result.stream()
                    .filter(item ->
                        item.getTrackingNumber().toLowerCase().contains(keyword.toLowerCase()) ||
                        (item.getLogisticsCompany() != null && item.getLogisticsCompany().toLowerCase().contains(keyword.toLowerCase())) ||
                        (item.getRemark() != null && item.getRemark().toLowerCase().contains(keyword.toLowerCase()))
                    )
                    .collect(java.util.stream.Collectors.toList());
            }

            // 简单分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, result.size());
            if (start < result.size()) {
                result = result.subList(start, end);
            } else {
                result = new ArrayList<>();
            }

            log.info("返回快递单号列表，共 {} 条记录", result.size());
            return R.ok("查询成功", result);

        } catch (Exception e) {
            log.error("获取快递单号列表失败: {}", e.getMessage(), e);
            return R.fail("获取快递单号列表失败: " + e.getMessage());
        }
    }

    /**
     * 保存快递单号
     */
    @Operation(summary = "保存快递单号")
    @PostMapping("/tracking-numbers")
    public R<TrackingNumberVO> saveTrackingNumber(@Valid @RequestBody TrackingNumberVO trackingNumber) {
        try {
            log.info("保存快递单号: {}", trackingNumber.getTrackingNumber());

            // 验证快递单号格式
            if (StringUtils.isEmpty(trackingNumber.getTrackingNumber())) {
                return R.fail("快递单号不能为空");
            }

            // 设置默认值
            if (trackingNumber.getCreateTime() == null) {
                trackingNumber.setCreateTime(new Date());
            }
            if (trackingNumber.getStatus() == null) {
                trackingNumber.setStatus("PENDING");
            }
            if (trackingNumber.getQuerySuccess() == null) {
                trackingNumber.setQuerySuccess(false);
            }
            if (trackingNumber.getQueryCount() == null) {
                trackingNumber.setQueryCount(0);
            }
            if (trackingNumber.getAutoQuery() == null) {
                trackingNumber.setAutoQuery(true);
            }
            if (trackingNumber.getPriority() == null) {
                trackingNumber.setPriority(1);
            }

            // 这里可以实现保存到数据库的逻辑
            // TrackingNumberVO savedVO = trackingNumberService.saveTrackingNumber(trackingNumber);

            // 暂时直接返回输入的对象（模拟保存成功）
            trackingNumber.setId(System.currentTimeMillis()); // 模拟生成ID

            log.info("快递单号保存成功: id={}, trackingNumber={}", trackingNumber.getId(), trackingNumber.getTrackingNumber());
            return R.ok("保存成功", trackingNumber);

        } catch (Exception e) {
            log.error("保存快递单号失败: {}", e.getMessage(), e);
            return R.fail("保存快递单号失败: " + e.getMessage());
        }
    }

    /**
     * 更新快递单号
     */
    @Operation(summary = "更新快递单号")
    @PutMapping("/tracking-numbers/{id}")
    public R<TrackingNumberVO> updateTrackingNumber(
            @Parameter(description = "快递单号ID") @PathVariable Long id,
            @Valid @RequestBody TrackingNumberVO trackingNumber) {
        try {
            log.info("更新快递单号: id={}, trackingNumber={}", id, trackingNumber.getTrackingNumber());

            if (id == null) {
                return R.fail("快递单号ID不能为空");
            }

            // 设置ID和更新时间
            trackingNumber.setId(id);
            trackingNumber.setLastUpdateTime(new Date());

            // 这里可以实现更新数据库的逻辑
            // TrackingNumberVO updatedVO = trackingNumberService.updateTrackingNumber(trackingNumber);

            log.info("快递单号更新成功: id={}", id);
            return R.ok("更新成功", trackingNumber);

        } catch (Exception e) {
            log.error("更新快递单号失败: id={}, error={}", id, e.getMessage(), e);
            return R.fail("更新快递单号失败: " + e.getMessage());
        }
    }

    /**
     * 删除快递单号
     */
    @Operation(summary = "删除快递单号")
    @DeleteMapping("/tracking-numbers/{trackingNumber}")
    public R<String> deleteTrackingNumber(
            @Parameter(description = "快递单号") @PathVariable @NotBlank(message = "快递单号不能为空") String trackingNumber) {
        try {
            log.info("删除快递单号: {}", trackingNumber);

            if (StringUtils.isEmpty(trackingNumber)) {
                return R.fail("快递单号不能为空");
            }

            // 这里可以实现从数据库删除的逻辑
            // boolean deleted = trackingNumberService.deleteTrackingNumber(trackingNumber);
            // if (!deleted) {
            //     return R.fail("快递单号不存在或删除失败");
            // }

            log.info("快递单号删除成功: {}", trackingNumber);
            return R.ok("删除成功");

        } catch (Exception e) {
            log.error("删除快递单号失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            return R.fail("删除快递单号失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除快递单号
     */
    @Operation(summary = "根据ID删除快递单号")
    @DeleteMapping("/tracking-numbers/by-id/{id}")
    public R<String> deleteTrackingNumberById(
            @Parameter(description = "快递单号ID") @PathVariable Long id) {
        try {
            log.info("根据ID删除快递单号: {}", id);

            if (id == null) {
                return R.fail("快递单号ID不能为空");
            }

            // 这里可以实现从数据库删除的逻辑
            // boolean deleted = trackingNumberService.deleteTrackingNumberById(id);
            // if (!deleted) {
            //     return R.fail("快递单号不存在或删除失败");
            // }

            log.info("快递单号删除成功: id={}", id);
            return R.ok("删除成功");

        } catch (Exception e) {
            log.error("删除快递单号失败: id={}, error={}", id, e.getMessage(), e);
            return R.fail("删除快递单号失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除快递单号
     */
    @Operation(summary = "批量删除快递单号")
    @DeleteMapping("/tracking-numbers/batch")
    public R<String> batchDeleteTrackingNumbers(@RequestBody List<String> trackingNumbers) {
        try {
            log.info("批量删除快递单号: {}", trackingNumbers);

            if (trackingNumbers == null || trackingNumbers.isEmpty()) {
                return R.fail("快递单号列表不能为空");
            }

            // 这里可以实现批量删除的逻辑
            // int deletedCount = trackingNumberService.batchDeleteTrackingNumbers(trackingNumbers);

            int deletedCount = trackingNumbers.size(); // 模拟删除成功

            log.info("批量删除快递单号成功，共删除 {} 条记录", deletedCount);
            return R.ok("批量删除成功，共删除 " + deletedCount + " 条记录");

        } catch (Exception e) {
            log.error("批量删除快递单号失败: {}", e.getMessage(), e);
            return R.fail("批量删除快递单号失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个快递单号详情
     */
    @Operation(summary = "获取快递单号详情")
    @GetMapping("/tracking-numbers/{trackingNumber}")
    public R<TrackingNumberVO> getTrackingNumberDetail(
            @Parameter(description = "快递单号") @PathVariable String trackingNumber) {
        try {
            log.info("获取快递单号详情: {}", trackingNumber);

            if (StringUtils.isEmpty(trackingNumber)) {
                return R.fail("快递单号不能为空");
            }

            // 这里可以实现从数据库查询的逻辑
            // TrackingNumberVO result = trackingNumberService.getTrackingNumberDetail(trackingNumber);
            // if (result == null) {
            //     return R.fail("快递单号不存在");
            // }

            // 暂时返回模拟数据
            TrackingNumberVO result = new TrackingNumberVO();
            result.setId(1L);
            result.setTrackingNumber(trackingNumber);
            result.setLogisticsCompany("申通快递");
            result.setStatus("IN_TRANSIT");
            result.setStatusDescription("运输中");
            result.setQuerySuccess(true);
            result.setCreateTime(new Date());
            result.setLastUpdateTime(new Date());
            result.setQueryCount(3);
            result.setAutoQuery(true);
            result.setPriority(1);

            return R.ok("查询成功", result);

        } catch (Exception e) {
            log.error("获取快递单号详情失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            return R.fail("获取快递单号详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建模拟数据（用于测试）
     */
    private List<TrackingNumberVO> createMockTrackingNumbers() {
        List<TrackingNumberVO> list = new ArrayList<>();

        // 模拟数据1
        TrackingNumberVO vo1 = new TrackingNumberVO();
        vo1.setId(1L);
        vo1.setTrackingNumber("773320599100571");
        vo1.setLogisticsCompany("申通快递");
        vo1.setActualCompany("申通快递");
        vo1.setStatus("SIGNED");
        vo1.setStatusDescription("已签收");
        vo1.setQuerySuccess(true);
        vo1.setCreateTime(new Date(System.currentTimeMillis() - 86400000)); // 1天前
        vo1.setLastUpdateTime(new Date());
        vo1.setQueryCount(5);
        vo1.setAutoQuery(true);
        vo1.setPriority(1);
        vo1.setRemark("重要包裹");
        list.add(vo1);

        // 模拟数据2
        TrackingNumberVO vo2 = new TrackingNumberVO();
        vo2.setId(2L);
        vo2.setTrackingNumber("JT5386938842478");
        vo2.setLogisticsCompany("极兔速递");
        vo2.setActualCompany("极兔速递");
        vo2.setStatus("IN_TRANSIT");
        vo2.setStatusDescription("运输中");
        vo2.setQuerySuccess(true);
        vo2.setCreateTime(new Date(System.currentTimeMillis() - 43200000)); // 12小时前
        vo2.setLastUpdateTime(new Date(System.currentTimeMillis() - 3600000)); // 1小时前
        vo2.setQueryCount(3);
        vo2.setAutoQuery(true);
        vo2.setPriority(2);
        list.add(vo2);

        // 模拟数据3
        TrackingNumberVO vo3 = new TrackingNumberVO();
        vo3.setId(3L);
        vo3.setTrackingNumber("1234567890123");
        vo3.setLogisticsCompany("顺丰速运");
        vo3.setStatus("QUERY_FAILED");
        vo3.setStatusDescription("查询失败");
        vo3.setQuerySuccess(false);
        vo3.setErrorMessage("快递单号不存在");
        vo3.setCreateTime(new Date(System.currentTimeMillis() - 7200000)); // 2小时前
        vo3.setLastUpdateTime(new Date(System.currentTimeMillis() - 1800000)); // 30分钟前
        vo3.setQueryCount(2);
        vo3.setAutoQuery(false);
        vo3.setPriority(1);
        vo3.setRemark("测试单号");
        list.add(vo3);

        return list;
    }
}

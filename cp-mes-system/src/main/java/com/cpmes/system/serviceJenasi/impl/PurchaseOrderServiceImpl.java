package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.helper.LoginHelper;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.entity.Item;
import com.cpmes.system.entity.PurchaseInbound;
import com.cpmes.system.entity.PurchaseOrder;
import com.cpmes.system.entity.PurchaseOrderImage;
import com.cpmes.system.entity.Supplier;
import com.cpmes.system.entity.dto.purcgaseInbound.PurchaseInboundAddRequest;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseLinkDto;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderAddRequest;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderAuditRequest;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderQueryRequest;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberAddRequest;
import com.cpmes.system.entity.vo.PurchaseOrderVO;
import com.cpmes.system.entity.vo.TrackingNumberVO;
import com.cpmes.system.mapperJenasi.PurchaseOrderMapper;
import com.cpmes.system.service.LogisticsQueryService;
import com.cpmes.system.service.IPurchaseOrderImageService;
import com.cpmes.system.serviceJenasi.ItemService;
import com.cpmes.system.serviceJenasi.PurchaseInboundService;
import com.cpmes.system.serviceJenasi.PurchaseOrderService;
import com.cpmes.system.serviceJenasi.SupplierService;
import com.cpmes.system.serviceJenasi.TrackingNumberService;
import com.cpmes.system.utils.PurchaseMinioUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Date;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderActualPriceRequest;
import com.cpmes.system.entity.PurchaseOrderPriceLog;
import com.cpmes.system.mapperJenasi.PurchaseOrderPriceLogMapper;

/**
* <AUTHOR>
* @description 针对表【purchase_order(采购订单表)】的数据库操作Service实现
* @createDate 2025-06-11 10:33:37
*/
@Slf4j
@Service
@DS("slave")
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder>
    implements PurchaseOrderService {

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private ItemService itemService;

    @Resource
    private SupplierService supplierService;

    // 移除直接依赖，使用ApplicationContext获取Bean以避免循环依赖
    // @Resource
    // private PurchaseInboundService purchaseInboundService;

    @Resource
    private LogisticsQueryService logisticsQueryService;

    @Resource
    private TrackingNumberService trackingNumberService;

    @Resource
    private IPurchaseOrderImageService purchaseOrderImageService;

    @Resource
    private PurchaseOrderPriceLogMapper purchaseOrderPriceLogMapper;

    /**
     * 添加或更新采购订单
     * @param purchaseOrderAddRequest 采购订单请求参数
     * @return 处理后的采购订单实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrder addOrUpdatePurchaseOrder(PurchaseOrderAddRequest purchaseOrderAddRequest) {
        // 数据校验
        validatePurchaseOrderRequest(purchaseOrderAddRequest);

        // 校验供应商是否存在
        Long supplierId = purchaseOrderAddRequest.getSupplierId();
        boolean existsSupplier = supplierService.lambdaQuery().eq(Supplier::getId, supplierId).exists();
        if (!existsSupplier) {
            throw new ServiceException("供应商不存在");
        }

        // 判断是新增还是更新
        Long purchaseOrderId = purchaseOrderAddRequest.getId();
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        if (purchaseOrderId != null) {
            // 更新操作
            purchaseOrder = this.getById(purchaseOrderId);
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单不存在");
            }
            purchaseOrder.setUpdateTime(new Date());
        } else {
            purchaseOrder.setCreateTime(new Date());
            purchaseOrder.setStatus(0); // 初始状态为待审核
        }

        String itemId = purchaseOrderAddRequest.getItemId();
        if (itemId != null && !itemId.trim().isEmpty()) {
            // 路径A：仓储原料采购 - itemId存储原料编码(fields3)
            purchaseOrder.setItemId(itemId);
            purchaseOrder.setItemName(purchaseOrderAddRequest.getItemName());
            purchaseOrder.setItemType(purchaseOrderAddRequest.getItemType());
            purchaseOrder.setBoardType(purchaseOrderAddRequest.getBoardType());
            purchaseOrder.setUnit(purchaseOrderAddRequest.getUnit());
        } else {
            // 路径B：其他类型采购
            if (StringUtils.isBlank(purchaseOrderAddRequest.getItemName()) || StringUtils.isBlank(purchaseOrderAddRequest.getItemType())) {
                throw new ServiceException("手动采购模式下，物品名称和类型不能为空");
            }
            // 直接使用请求中的数据
            purchaseOrder.setItemId(null);
            purchaseOrder.setItemName(purchaseOrderAddRequest.getItemName());
            purchaseOrder.setItemType(purchaseOrderAddRequest.getItemType());
            purchaseOrder.setBoardType(null); // 其他类型采购不设置板型
            purchaseOrder.setUnit(purchaseOrderAddRequest.getUnit());
        }

        setPurchaseOrderCommonData(purchaseOrder, purchaseOrderAddRequest);

        // 保存或更新到数据库
        boolean success = this.saveOrUpdate(purchaseOrder);
        if (!success) {
            throw new ServiceException("保存采购订单失败");
        }
        return this.getById(purchaseOrder.getId());
    }

    /**
     * 采购订单审核
     * @param request 采购订单审核请求参数
     * @return 处理后的采购订单实体
     */
    @Override
    public PurchaseOrder auditPurchaseOrder(PurchaseOrderAuditRequest request) {
        PurchaseOrder purchaseOrder = this.getById(request.getId());
        if (purchaseOrder == null || purchaseOrder.getIsDeleted() == 1) {
            throw new ServiceException("采购订单不存在");
        }
//        if (purchaseOrder.getStatus() != 0) {
//            throw new ServiceException("该采购订单已审核，无法重复审核");
//        }
        String itemId = purchaseOrder.getItemId();
        purchaseOrder.setStatus(request.getStatus());
        //状态为3时添加入到入库信息任务（确认收货时创建采购入库记录并生成批次号）
        if (request.getStatus() == 3) {
            PurchaseInboundAddRequest inboundAddRequest = new PurchaseInboundAddRequest();
            inboundAddRequest.setPurchaseOrderNo(purchaseOrder.getPurchaseNo());
            // 使用采购订单中已存储的物品信息
            inboundAddRequest.setMaterialType(purchaseOrder.getItemType());
            inboundAddRequest.setMaterialName(purchaseOrder.getItemName());
            // 同步板型类型信息
            inboundAddRequest.setBoardType(purchaseOrder.getBoardType());
            // 设置操作人为当前审核人
            inboundAddRequest.setOperator(getCurrentUsername());
            // 设置状态为待入库（0）
            inboundAddRequest.setStatus(0);
            // 设置备注
            inboundAddRequest.setRemark("确认收货时自动创建，批次号和板型已同步");

            // 使用ApplicationContext获取Bean以避免循环依赖
            PurchaseInboundService purchaseInboundService =
                com.cpmes.common.utils.spring.SpringUtils.getBean(PurchaseInboundService.class);
            PurchaseInbound createdInbound = purchaseInboundService.addPurchaseInbound(inboundAddRequest);

            System.out.println("确认收货时创建采购入库记录成功: purchaseOrderNo=" + purchaseOrder.getPurchaseNo() +
                             ", batchNo=" + createdInbound.getBatchNo() + ", boardType=" + createdInbound.getBoardType());
        }
        //审批人
        purchaseOrder.setApprover(getCurrentUsername());
        purchaseOrder.setApproveTime(new Date());
        if (StringUtils.isNotBlank(request.getRemark())) {
            purchaseOrder.setRemark(request.getRemark());
        }
        purchaseOrder.setUpdateTime(new Date());
        boolean success = this.updateById(purchaseOrder);
        if (!success) {
            throw new ServiceException("审核失败");
        }
        return this.getById(purchaseOrder.getId());
    }

    /**
     * 获取查询条件
     */
    @Override
    public QueryWrapper<PurchaseOrder> getQueryWrapper(PurchaseOrderQueryRequest request) {
        QueryWrapper<PurchaseOrder> queryWrapper = new QueryWrapper<>();
        String purchaseNo = request.getPurchaseNo();
        Integer status = request.getStatus();
        String itemId = request.getItemId();
        Long supplierId = request.getSupplierId();
        String approver = request.getApprover();
        String applicant = request.getApplicant();
        queryWrapper.like(StringUtils.isNotBlank(purchaseNo),"purchase_no", purchaseNo);
        queryWrapper.eq(status != null, "status", status);
        queryWrapper.eq(StringUtils.isNotBlank(itemId), "item_id", itemId);
        queryWrapper.eq(supplierId != null, "supplier_id", supplierId);
        queryWrapper.like(StringUtils.isNotBlank(approver), "approver", approver);
        queryWrapper.like(StringUtils.isNotBlank(applicant), "applicant", applicant);
        // 添加逻辑删除过滤
        queryWrapper.eq("is_deleted", 0);
        return queryWrapper;
    }

    /**
     * 分页获取采购订单列表（增强版本，包含板类型字段）
     */
    @Override
    public Page<PurchaseOrder> getPurchaseOrderList(Page<PurchaseOrder> page, PurchaseOrderQueryRequest request) {
        Page<PurchaseOrder> result = this.page(page, getQueryWrapper(request));

        // 增强日志：记录板类型字段数据
        if (result != null && result.getRecords() != null) {
            log.debug("采购订单列表查询结果: 共{}条记录", result.getRecords().size());
            for (PurchaseOrder order : result.getRecords()) {
                if (order.getBoardType() != null && !order.getBoardType().isEmpty()) {
                    log.debug("采购订单ID: {}, 采购单号: {}, 板类型: {}",
                        order.getId(), order.getPurchaseNo(), order.getBoardType());
                }
            }
        }

        return result;
    }

    /**
     * 分页获取采购订单详情
     */
    @Override
    public Page<PurchaseOrderVO> getPurchaseOrderDetail(Page<PurchaseOrder> page, String purchaseNo, Integer status, String itemName, String supplierName, String applicant, String approver) {
        //将查询结果封装到page对象中
        Page<PurchaseOrderVO> purchaseOrderDetail = purchaseOrderMapper.getPurchaseOrderDetail(page, purchaseNo, status, itemName, supplierName, applicant, approver);

        // 调试日志：检查返回的数据是否包含新增字段
        if (purchaseOrderDetail != null && purchaseOrderDetail.getRecords() != null) {
            for (PurchaseOrderVO vo : purchaseOrderDetail.getRecords()) {
                if (vo.getPurchaseLinks() != null || vo.getImagesFolderPath() != null
//                    || vo.getTrackingNumber() != null || vo.getLogisticsCompany() != null
                ) {
                    log.info("采购订单ID: {}, 增强字段数据: purchaseLinks={}, imagesFolderPath={}"
//                        +    ", trackingNumber={}, logisticsCompany={}"
                        , vo.getId(), vo.getPurchaseLinks(), vo.getImagesFolderPath()
//                        , vo.getTrackingNumber(), vo.getLogisticsCompany()
                    );
                }
            }
        }

        return purchaseOrderDetail;
    }

    /**
     * 按条件获取采购订单详情
     */
    @Override
    public List<PurchaseOrderVO> getPurchaseOrderDetailByCondition(String purchaseNo, Integer status, String itemName, String supplierName, String applicant, String approver) {
        return purchaseOrderMapper.getPurchaseOrderDetailByCondition(purchaseNo, status, itemName, supplierName, applicant, approver);
    }

    /**
     * 根据ID获取采购订单详情（包含供应商名称）
     */
    @Override
    public PurchaseOrderVO getPurchaseOrderVOById(Long id) {
        if (id == null) {
            throw new ServiceException("采购订单ID不能为空");
        }

        // 使用Mapper直接查询包含供应商名称的VO
        PurchaseOrderVO vo = purchaseOrderMapper.getPurchaseOrderVOById(id);
        if (vo == null) {
            throw new ServiceException("采购订单不存在");
        }

        return vo;
    }


    /**
     * 数据校验
     */
    private void validatePurchaseOrderRequest(PurchaseOrderAddRequest request) {
        if (request == null) {
            throw new ServiceException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getPurchaseNo())) {
            throw new ServiceException("采购单号不能为空");
        }

        // 双模式验证：要么有itemId（仓储原料采购），要么有itemName和itemType（其他类型采购）
        if (StringUtils.isBlank(request.getItemId())) {
            // 其他类型采购模式：需要手动填写物品信息
            if (StringUtils.isBlank(request.getItemName())) {
                throw new ServiceException("物品名称不能为空");
            }
            if (StringUtils.isBlank(request.getItemType())) {
                throw new ServiceException("物品类型不能为空");
            }
            if (StringUtils.isBlank(request.getUnit())) {
                throw new ServiceException("计量单位不能为空");
            }
        }

        if (request.getSupplierId() == null) {
            throw new ServiceException("供应商ID不能为空");
        }
        if (request.getQuantity() == null || request.getQuantity().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new ServiceException("采购数量必须大于0");
        }
        if (request.getPrice() == null || request.getPrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            throw new ServiceException("单价必须大于0");
        }
    }


    /**
     * 设置采购订单通用数据
     */
    private void setPurchaseOrderCommonData(PurchaseOrder purchaseOrder, PurchaseOrderAddRequest request) {
        purchaseOrder.setPurchaseNo(request.getPurchaseNo());
        purchaseOrder.setItemId(request.getItemId());
        purchaseOrder.setSupplierId(request.getSupplierId());
        purchaseOrder.setQuantity(request.getQuantity());
        purchaseOrder.setUnit(request.getUnit());
        purchaseOrder.setPrice(request.getPrice());
        purchaseOrder.setSubtotal(request.getQuantity().multiply(request.getPrice()));
        purchaseOrder.setApplicant(request.getApplicant());
        purchaseOrder.setApplyTime(new Date());
        purchaseOrder.setExpectedDate(request.getExpectedDate());
    }

    /**
     * 获取当前登录用户名
     */
    private String getCurrentUsername() {
        try {
            return LoginHelper.getUsername();
        } catch (Exception e) {
            return "系统";
        }
    }

    /**
     * 根据采购单号获取现有的批次号
     * 暂时简化实现，直接返回null让系统生成新批次号
     */
    private String getExistingBatchNoByPurchaseNo(String purchaseNo) {
        // TODO: 后续可以实现从库存明细表查询现有批次号的逻辑
        // 目前直接返回null，让系统生成新的批次号
        return null;
    }

    /**
     * 打印采购入库二维码
     */
    @Override
    public Boolean printPurchaseInboundQrCode(java.util.Map<String, Object> printData) {
        try {
            // 处理批次号：如果前端没有传递批次号，尝试从数据库获取或生成新的
            String purchaseNo = (String) printData.get("purchase_no");
            String batchNo = (String) printData.get("batch_no");

            if (batchNo == null || batchNo.trim().isEmpty()) {
                // 尝试从库存明细表获取已存在的批次号
                batchNo = getExistingBatchNoByPurchaseNo(purchaseNo);

                if (batchNo == null || batchNo.trim().isEmpty()) {
                    // 如果没有找到现有批次号，生成新的批次号
                    com.cpmes.system.service.IBatchNumberGeneratorService batchService =
                        com.cpmes.common.utils.spring.SpringUtils.getBean(com.cpmes.system.service.IBatchNumberGeneratorService.class);
                    batchNo = batchService.generateNextBatchNumber();
                    System.out.println("为采购单 " + purchaseNo + " 生成新批次号: " + batchNo);
                } else {
                    System.out.println("为采购单 " + purchaseNo + " 使用现有批次号: " + batchNo);
                }

                // 将批次号添加到打印数据中
                printData.put("batch_no", batchNo);
            }

            // 获取打印机配置
            com.cpmes.system.service.IPrintInterfaceConfigService printConfigService =
                com.cpmes.common.utils.spring.SpringUtils.getBean(com.cpmes.system.service.IPrintInterfaceConfigService.class);

            java.util.Map<String, Object> printerConfig = printConfigService.getBestPrinterConfig("purchase");
            if (printerConfig == null || printerConfig.isEmpty()) {
                throw new ServiceException("未找到可用的采购标签打印机配置");
            }

            // 从数据库返回的Map中获取值（使用下划线格式的key）
            String printerIp = (String) printerConfig.get("printer_ip");
            Object printerPortObj = printerConfig.get("printer_port");
            String apiEndpoint = (String) printerConfig.get("api_endpoint");

            // 检查关键配置是否存在
            if (printerIp == null || printerPortObj == null || apiEndpoint == null) {
                System.err.println("打印机配置不完整: " + printerConfig);
                throw new ServiceException("打印机配置不完整，请检查IP、端口和API端点设置");
            }

            // 转换端口为整数
            Integer printerPort;
            if (printerPortObj instanceof Integer) {
                printerPort = (Integer) printerPortObj;
            } else if (printerPortObj instanceof String) {
                try {
                    printerPort = Integer.parseInt((String) printerPortObj);
                } catch (NumberFormatException e) {
                    throw new ServiceException("打印机端口格式错误：" + printerPortObj);
                }
            } else {
                throw new ServiceException("打印机端口类型错误：" + printerPortObj.getClass().getSimpleName());
            }

            // 构建打印机API地址
            String apiUrl = String.format("http://%s:%d%s", printerIp, printerPort, apiEndpoint);
            System.out.println("使用打印机配置 - IP: " + printerIp + ", Port: " + printerPort + ", Endpoint: " + apiEndpoint);

            // 发送打印请求
            Boolean printResult = sendToPrinterAPI(apiUrl, printData);

            // 如果打印成功，保存二维码内容到数据库
            if (printResult && printData.containsKey("purchase_no") && printData.containsKey("label_type")) {
                try {
                    savePrintedQrCodeToDatabase(printData);
                } catch (Exception e) {
                    // 打印成功但保存二维码失败，记录日志但不影响打印结果
                    System.err.println("打印成功但保存二维码内容失败: " + e.getMessage());
                }
            }

            return printResult;

        } catch (Exception e) {
            System.err.println("打印采购入库二维码失败: " + e.getMessage());
            throw new ServiceException("打印失败：" + e.getMessage());
        }
    }

    /**
     * 保存打印的二维码内容到数据库
     * @param printData 打印数据
     */
    private void savePrintedQrCodeToDatabase(java.util.Map<String, Object> printData) {
        try {
            // 获取采购入库服务
            com.cpmes.system.serviceJenasi.PurchaseInboundService purchaseInboundService =
                com.cpmes.common.utils.spring.SpringUtils.getBean(com.cpmes.system.serviceJenasi.PurchaseInboundService.class);

            // 构建二维码内容（与前端和打印机保持一致的格式）
            String labelType = (String) printData.get("label_type");
            String purchaseNo = (String) printData.get("purchase_no");
            String qrContent = String.format("label_type:%s|purchase_no:%s", labelType, purchaseNo);

            // 获取当前操作人
            String operator = getCurrentUsername();

            // 调用更新二维码内容的方法
            purchaseInboundService.updateQrCode(purchaseNo, qrContent, operator);

            System.out.println("二维码内容已保存到数据库: " + qrContent);

        } catch (Exception e) {
            System.err.println("保存二维码内容到数据库失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 发送数据到打印机API
     */
    private Boolean sendToPrinterAPI(String apiUrl, java.util.Map<String, Object> printerData) {
        try {
            // 构建JSON字符串
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");
            boolean first = true;
            for (java.util.Map.Entry<String, Object> entry : printerData.entrySet()) {
                if (!first) jsonBuilder.append(",");
                jsonBuilder.append("\"").append(entry.getKey()).append("\":");
                if (entry.getValue() instanceof String) {
                    jsonBuilder.append("\"").append(entry.getValue()).append("\"");
                } else {
                    jsonBuilder.append(entry.getValue());
                }
                first = false;
            }
            jsonBuilder.append("}");

            String jsonData = jsonBuilder.toString();
            System.out.println("发送到打印机的数据: " + jsonData);
            System.out.println("打印机API地址: " + apiUrl);

            // 使用Java原生HTTP客户端发送请求到打印机驱动
            java.net.URL url = new java.net.URL(apiUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

            // 设置连接参数
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(30000); // 连接超时30秒
            connection.setReadTimeout(30000);    // 读取超时30秒
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 发送数据
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("打印机响应代码: " + responseCode);

            // 判断打印是否成功
            return responseCode >= 200 && responseCode < 300;

        } catch (java.net.ConnectException e) {
            System.err.println("无法连接到打印机服务: " + e.getMessage());
            return false;
        } catch (java.net.SocketTimeoutException e) {
            System.err.println("连接打印机超时: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("发送打印请求异常: " + e.getMessage());
            return false;
        }
    }

    // ========================================
    // 新增功能实现：图片上传与链接管理
    // ========================================

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 删除原有的MinIO配置常量，现在使用PurchaseMinioUtils中的配置

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> uploadPurchaseImages(Long purchaseOrderId, MultipartFile[] files) {
        // 验证采购订单是否存在
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单不存在");
        }

        try {
            // 使用新的图片管理服务上传图片
            List<PurchaseOrderImage> uploadedImages = purchaseOrderImageService.uploadImages(purchaseOrderId, files);

            // 提取图片URL列表
            List<String> imageUrls = uploadedImages.stream()
                    .map(PurchaseOrderImage::getStoragePath)
                    .collect(java.util.stream.Collectors.toList());

            // 更新采购订单的图片文件夹路径（保持兼容性）
            String folderPath = "purchase/" + purchaseOrderId + "/images/";
            purchaseOrder.setImagesFolderPath(folderPath);
            this.updateById(purchaseOrder);

            return imageUrls;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("图片上传失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> getPurchaseImages(Long purchaseOrderId) {
        try {
            // 使用新的图片管理服务获取图片列表
            List<PurchaseOrderImage> images = purchaseOrderImageService.getImagesByPurchaseOrderId(purchaseOrderId);

            // 提取图片URL列表
            return images.stream()
                    .map(PurchaseOrderImage::getStoragePath)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("获取采购订单{}图片列表失败", purchaseOrderId, e);
            throw new ServiceException("获取图片列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePurchaseImage(Long purchaseOrderId, String imageName) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单不存在");
        }

        try {
            // 从URL中提取文件名（如果传入的是完整URL）
            String actualImageName = PurchaseMinioUtils.extractImageNameFromUrl(imageName);
            if (StringUtils.isEmpty(actualImageName)) {
                actualImageName = imageName;
            }

            // 使用MinIO工具类删除图片
            return PurchaseMinioUtils.deletePurchaseImage(purchaseOrderId, actualImageName);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("删除图片失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePurchaseLinks(Long purchaseOrderId, List<PurchaseLinkDto> links) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单不存在");
        }

        try {
            String linksJson = objectMapper.writeValueAsString(links);
            purchaseOrder.setPurchaseLinks(linksJson);
            return this.updateById(purchaseOrder);
        } catch (Exception e) {
            throw new ServiceException("保存链接失败：" + e.getMessage());
        }
    }

    @Override
    public List<PurchaseLinkDto> getPurchaseLinks(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw new ServiceException("采购订单不存在");
        }

        String linksJson = purchaseOrder.getPurchaseLinks();
        if (StringUtils.isEmpty(linksJson)) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(linksJson, new TypeReference<List<PurchaseLinkDto>>() {});
        } catch (Exception e) {
            throw new ServiceException("解析链接数据失败：" + e.getMessage());
        }
    }

    // ========================================
    // 新增功能实现：物流追踪
    // ========================================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveLogisticsInfo(Long purchaseOrderId, String trackingNumber, String logisticsCompany) {
        try {
            // 验证采购订单是否存在
            PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 使用TrackingNumberService保存物流信息
            TrackingNumberAddRequest addRequest = new TrackingNumberAddRequest();
            addRequest.setTrackingNumber(trackingNumber);
            addRequest.setLogisticsCompany(logisticsCompany);
            addRequest.setPurchaseOrderId(purchaseOrderId);
            addRequest.setPurchaseOrderNo(purchaseOrder.getPurchaseNo());
            addRequest.setRemark("通过采购订单保存");

            return trackingNumberService.addTrackingNumber(addRequest);
        } catch (Exception e) {
            log.error("保存物流信息失败: purchaseOrderId={}, trackingNumber={}", purchaseOrderId, trackingNumber, e);
            throw new ServiceException("保存物流信息失败: " + e.getMessage());
        }
    }

    @Override
    public LogisticsTrackingDto getLogisticsTracking(String trackingNumber) {
        try {
            // 使用LogisticsQueryService查询物流信息
            return logisticsQueryService.queryLogistics(trackingNumber, null);
        } catch (Exception e) {
            log.error("获取物流追踪信息失败: trackingNumber={}", trackingNumber, e);
            throw new ServiceException("获取物流追踪信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteLogisticsInfo(Long purchaseOrderId) {
        try {
            // 根据采购订单ID获取相关的快递单号并删除
            List<TrackingNumberVO> trackingNumbers = trackingNumberService.getTrackingNumbersByPurchaseOrderId(purchaseOrderId);

            if (trackingNumbers != null && !trackingNumbers.isEmpty()) {
                List<Long> ids = trackingNumbers.stream()
                        .map(TrackingNumberVO::getId)
                        .collect(Collectors.toList());
                return trackingNumberService.batchDeleteTrackingNumbers(ids);
            }

            return true; // 没有相关快递单号，视为删除成功
        } catch (Exception e) {
            log.error("删除物流信息失败: purchaseOrderId={}", purchaseOrderId, e);
            throw new ServiceException("删除物流信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<LogisticsTrackingDto> getAllLogisticsTracking(Long purchaseOrderId) {
        try {
            // 获取采购订单的所有快递单号
            List<TrackingNumberVO> trackingNumbers = trackingNumberService.getTrackingNumbersByPurchaseOrderId(purchaseOrderId);

            if (trackingNumbers == null || trackingNumbers.isEmpty()) {
                return new ArrayList<>();
            }

            List<LogisticsTrackingDto> result = new ArrayList<>();

            // 为每个快递单号查询物流信息
            for (TrackingNumberVO trackingNumber : trackingNumbers) {
                try {
                    LogisticsTrackingDto logisticsInfo = logisticsQueryService.queryLogistics(
                            trackingNumber.getTrackingNumber(),
                            trackingNumber.getLogisticsCompany()
                    );

                    // 补充数据库中的信息
                    if (logisticsInfo != null) {
                        logisticsInfo.setTrackingNumber(trackingNumber.getTrackingNumber());
                        if (logisticsInfo.getCompany() == null) {
                            logisticsInfo.setCompany(trackingNumber.getLogisticsCompany());
                        }
                        result.add(logisticsInfo);
                    }
                } catch (Exception e) {
                    log.warn("查询快递单号物流信息失败: trackingNumber={}, error={}",
                            trackingNumber.getTrackingNumber(), e.getMessage());

                    // 创建失败的物流信息记录
                    LogisticsTrackingDto failedInfo = new LogisticsTrackingDto();
                    failedInfo.setTrackingNumber(trackingNumber.getTrackingNumber());
                    failedInfo.setCompany(trackingNumber.getLogisticsCompany());
                    failedInfo.setStatus("QUERY_FAILED");
                    failedInfo.setStatusDescription("查询失败");
                    failedInfo.setQuerySuccess(false);
                    failedInfo.setErrorMessage(e.getMessage());
                    result.add(failedInfo);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("获取采购订单物流追踪信息失败: purchaseOrderId={}", purchaseOrderId, e);
            throw new ServiceException("获取物流追踪信息失败: " + e.getMessage());
        }
    }

    @Override
    public void clearLogisticsCache(String trackingNumber) {
        if (logisticsQueryService != null) {
            logisticsQueryService.clearCache(trackingNumber);
        }
    }

    // ========================================
    // 私有辅助方法
    // ========================================

    /**
     * 验证图片文件的逻辑已移至PurchaseMinioUtils.validateImageFile()
    */
//    @Override
//    public void clearLogisticsCache(String trackingNumber) {
//        if (logisticsQueryService != null) {
//            logisticsQueryService.clearCache(trackingNumber);
//        }
//    }

    // Remove this duplicate:
    // @Override
    // public void clearLogisticsCache(String trackingNumber) {
    //     if (logisticsQueryService != null) {
    //         logisticsQueryService.clearCache(trackingNumber);
    //     }
    // }

    /**
     * 生成下一个采购订单号
     * 格式：PO + YYYYMMDD + 序号（3位）
     * 示例：PO20241222001
     * @return 新的采购订单号
     */
    @Override
    @DS("slave")
    public String generateNextPurchaseNo() {
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();
            String dateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String prefix = "PO" + dateStr;

            // 查询当天最大的订单号
            QueryWrapper<PurchaseOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.likeRight("purchase_no", prefix)
                       .orderByDesc("purchase_no")
                       .last("LIMIT 1");

            PurchaseOrder lastOrder = this.getOne(queryWrapper);

            int nextSequence = 1;
            if (lastOrder != null && lastOrder.getPurchaseNo() != null) {
                String lastPurchaseNo = lastOrder.getPurchaseNo();
                // 提取序号部分（最后3位）
                if (lastPurchaseNo.length() >= 3) {
                    String sequenceStr = lastPurchaseNo.substring(lastPurchaseNo.length() - 3);
                    try {
                        nextSequence = Integer.parseInt(sequenceStr) + 1;
                    } catch (NumberFormatException e) {
                        log.warn("解析订单号序号失败: {}, 使用默认序号1", lastPurchaseNo);
                        nextSequence = 1;
                    }
                }
            }

            // 格式化序号为3位数字
            String sequenceStr = String.format("%03d", nextSequence);
            String newPurchaseNo = prefix + sequenceStr;

            log.info("生成新的采购订单号: {}", newPurchaseNo);
            return newPurchaseNo;

        } catch (Exception e) {
            log.error("生成采购订单号失败", e);
            throw new ServiceException("生成采购订单号失败：" + e.getMessage());
        }
    }

    /**
     * 录入采购订单实际价格
     * @param request 实际价格录入请求
     * @param operator 操作人
     * @return 更新后的采购订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrder updateActualPrice(PurchaseOrderActualPriceRequest request, String operator) {
        try {
            // 1. 参数校验
            if (request == null || request.getId() == null) {
                throw new ServiceException("请求参数不能为空");
            }

            if (request.getActualPrice() == null || request.getActualPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("实际单价不能为空且不能为负数");
            }

            // 2. 查询采购订单
            PurchaseOrder purchaseOrder = this.getById(request.getId());
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 3. 状态校验 - 只有已通过或已完成的订单才能录入实际价格
            if (purchaseOrder.getStatus() != 1 && purchaseOrder.getStatus() != 3) {
                throw new ServiceException("只有状态为'已通过'或'已完成'的订单才能录入实际价格");
            }

            // 4. 检查是否已有实际价格（如果不是强制更新）
            if (purchaseOrder.getActualPrice() != null && !Boolean.TRUE.equals(request.getForceUpdate())) {
                throw new ServiceException("该订单已录入实际价格，如需修改请使用强制更新模式");
            }

            // 5. 记录原始价格（用于日志）
            BigDecimal oldActualPrice = purchaseOrder.getActualPrice();
            BigDecimal oldActualSubtotal = purchaseOrder.getActualSubtotal();

            // 6. 计算实际总额
            BigDecimal actualSubtotal = request.getActualSubtotal();
            if (actualSubtotal == null) {
                // 如果没有传入实际总额，则自动计算：实际单价 × 数量
                actualSubtotal = request.getActualPrice().multiply(purchaseOrder.getQuantity());
            }

            // 7. 更新采购订单
            purchaseOrder.setActualPrice(request.getActualPrice());
            purchaseOrder.setActualSubtotal(actualSubtotal);
            purchaseOrder.setActualPriceTime(new Date());
            purchaseOrder.setActualPriceRecorder(operator);

            boolean updateResult = this.updateById(purchaseOrder);
            if (!updateResult) {
                throw new ServiceException("更新采购订单失败");
            }

            // 8. 记录实际价格录入操作日志（每次录入都记录）
            recordPriceChangeLog(purchaseOrder, oldActualPrice, request.getActualPrice(),
                               operator, request.getRemark());

            log.info("采购订单实际价格录入成功 - 订单号: {}, 实际单价: {}, 实际总额: {}, 操作人: {}",
                    purchaseOrder.getPurchaseNo(), request.getActualPrice(), actualSubtotal, operator);

            return purchaseOrder;

        } catch (Exception e) {
            log.error("录入采购订单实际价格失败", e);
            throw new ServiceException("录入实际价格失败：" + e.getMessage());
        }
    }

    /**
     * 记录实际价格录入操作日志
     *
     * 业务规则：
     * 1. 每次调用updateActualPrice接口都记录一条操作日志
     * 2. 首次录入价格时操作类型为"CREATE"
     * 3. 后续录入价格时操作类型为"UPDATE"（无论价格是否变化）
     * 4. 日志记录失败不影响主流程
     *
     * 说明：
     * - 这里记录的是实际价格录入操作的完整历史
     * - 不是价格变更历史，而是操作历史
     * - 可用于审计、追踪每次价格录入的时间和操作人
     *
     * @param purchaseOrder 采购订单
     * @param oldActualPrice 原实际价格（首次录入时为null）
     * @param newActualPrice 新录入的实际价格
     * @param operator 操作人
     * @param remark 备注
     */
    private void recordPriceChangeLog(PurchaseOrder purchaseOrder, BigDecimal oldActualPrice,
                                    BigDecimal newActualPrice, String operator, String remark) {
        try {
            // 确定操作类型：首次录入为CREATE，后续录入为UPDATE
            String operationType = (oldActualPrice == null) ? "CREATE" : "UPDATE";

            // 创建价格录入操作日志
            PurchaseOrderPriceLog priceLog = new PurchaseOrderPriceLog();
            priceLog.setPurchaseOrderId(purchaseOrder.getId());
            priceLog.setPurchaseNo(purchaseOrder.getPurchaseNo());
            priceLog.setOldActualPrice(oldActualPrice);
            priceLog.setNewActualPrice(newActualPrice);
            priceLog.setOperationType(operationType);
            priceLog.setOperator(operator);
            priceLog.setOperationTime(new Date());
            priceLog.setRemark(remark);

            // 保存价格录入操作日志到数据库
            int insertResult = purchaseOrderPriceLogMapper.insert(priceLog);
            if (insertResult > 0) {
                log.info("价格录入操作日志保存成功 - 订单号: {}, 操作类型: {}, 旧价格: {}, 新价格: {}, 操作人: {}",
                        purchaseOrder.getPurchaseNo(), operationType, oldActualPrice, newActualPrice, operator);
            } else {
                log.warn("价格录入操作日志保存失败 - 订单号: {}", purchaseOrder.getPurchaseNo());
            }

        } catch (Exception e) {
            log.error("记录价格录入操作日志失败 - 订单号: {}, 错误信息: {}",
                    purchaseOrder.getPurchaseNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取采购订单价格变更日志
     * @param purchaseOrderId 采购订单ID
     * @return 价格变更日志列表
     */
    @Override
    public List<PurchaseOrderPriceLog> getPriceChangeLogs(Long purchaseOrderId) {
        try {
            if (purchaseOrderId == null) {
                log.warn("采购订单ID不能为空");
                return new ArrayList<>();
            }

            List<PurchaseOrderPriceLog> priceLogs = purchaseOrderPriceLogMapper.selectByPurchaseOrderId(purchaseOrderId);
            log.info("查询采购订单价格变更日志成功 - 订单ID: {}, 日志数量: {}", purchaseOrderId, priceLogs.size());
            return priceLogs;

        } catch (Exception e) {
            log.error("查询采购订单价格变更日志失败 - 订单ID: {}, 错误信息: {}", purchaseOrderId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取价格差异分析数据
     * @param purchaseOrderId 采购订单ID
     * @return 价格差异分析结果
     */
    @Override
    public Map<String, Object> getPriceDifferenceAnalysis(Long purchaseOrderId) {
        try {
            PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单不存在");
            }

            Map<String, Object> analysis = new HashMap<>();
            analysis.put("purchaseNo", purchaseOrder.getPurchaseNo());
            analysis.put("estimatedPrice", purchaseOrder.getPrice());
            analysis.put("estimatedSubtotal", purchaseOrder.getSubtotal());
            analysis.put("actualPrice", purchaseOrder.getActualPrice());
            analysis.put("actualSubtotal", purchaseOrder.getActualSubtotal());
            analysis.put("quantity", purchaseOrder.getQuantity());

            // 计算差异
            if (purchaseOrder.getPrice() != null && purchaseOrder.getActualPrice() != null) {
                BigDecimal priceDifference = purchaseOrder.getActualPrice().subtract(purchaseOrder.getPrice());
                BigDecimal priceVarianceRate = priceDifference.divide(purchaseOrder.getPrice(), 4, RoundingMode.HALF_UP)
                                                             .multiply(new BigDecimal("100"));
                analysis.put("priceDifference", priceDifference);
                analysis.put("priceVarianceRate", priceVarianceRate);
            }

            if (purchaseOrder.getSubtotal() != null && purchaseOrder.getActualSubtotal() != null) {
                BigDecimal subtotalDifference = purchaseOrder.getActualSubtotal().subtract(purchaseOrder.getSubtotal());
                BigDecimal subtotalVarianceRate = subtotalDifference.divide(purchaseOrder.getSubtotal(), 4, RoundingMode.HALF_UP)
                                                                   .multiply(new BigDecimal("100"));
                analysis.put("subtotalDifference", subtotalDifference);
                analysis.put("subtotalVarianceRate", subtotalVarianceRate);
            }

            return analysis;

        } catch (Exception e) {
            log.error("获取价格差异分析失败", e);
            throw new ServiceException("获取价格差异分析失败：" + e.getMessage());
        }
    }
}

package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.entity.OrderTask;
import com.cpmes.system.entity.StepTask;
import com.cpmes.system.entity.dto.stepTask.*;
import com.cpmes.system.mapperJenasi.StepTaskMapper;
import com.cpmes.system.serviceJenasi.OrderTaskService;
import com.cpmes.system.serviceJenasi.StepTaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
* <AUTHOR>
* @description 针对表【step_task(任务详细表：记录每个任务对应的工序执行信息)】的数据库操作Service实现
* @createDate 2025-06-25 13:59:44
*/
@Service
@DS("slave")
public class StepTaskServiceImpl extends ServiceImpl<StepTaskMapper, StepTask>
    implements StepTaskService {

    @Resource
    private OrderTaskService orderTaskService;

    /**
     * 获取工序状态为已完成的不良项并统计
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<DefectStatDTO> getDefectStats(LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.getDefectStats(startTime, endTime);
    }

    /**
     * 添加缺陷信息
     * 已使用
     * @param stepTaskId 工序任务ID
     * @param defects 缺陷信息映射（缺陷名称->数量）
     * @return 是否成功
     */
    @Override
    public boolean addDefectInfo(Long stepTaskId, Map<String, Integer> defects) {
        // 1. 参数校验
        if (stepTaskId == null) {
            throw new ServiceException("工序任务ID不能为空");
        }

        if (defects == null || defects.isEmpty()) {
            throw new ServiceException("缺陷信息不能为空");
        }
        StepTask stepTask = this.getById(stepTaskId);
        if (stepTask == null) {
            throw new ServiceException("工序任务不存在");
        }
        try {
            // 使用 fastjson 将 Map 转成 JSON 字符串
            String json = com.alibaba.fastjson.JSON.toJSONString(defects);
            // 假设 defectInfo 是 String 类型
            stepTask.setDefectInfo(json);
            boolean result = this.updateById(stepTask);
            if (!result) {
                throw new ServiceException("更新缺陷信息失败");
            }
            return true;
        } catch (Exception e) {
            throw new ServiceException("缺陷信息转换失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StepTask> createStepTasksByTaskId(Long taskId) {
        // 验证任务是否存在
        OrderTask orderTask = orderTaskService.getById(taskId);
        if (orderTask == null) {
            throw new ServiceException("任务不存在");
        }

        // TODO: 这里可以根据工艺路线自动创建工序任务详细
        // 目前先返回空列表，实际业务中需要根据工艺路线获取工序信息
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStepTask(StepTaskUpdateRequest updateRequest) {
        StepTask existingStepTask = this.getById(updateRequest.getId());
        if (existingStepTask == null) {
            throw new ServiceException("任务详细不存在");
        }

        StepTask stepTask = new StepTask();
        BeanUtils.copyProperties(updateRequest, stepTask);

        // 如果完成状态变为已完成，设置完成时间
        if (updateRequest.getIsCompleted() != null && updateRequest.getIsCompleted() == 1
            && !Integer.valueOf(1).equals(existingStepTask.getIsCompleted())) {
            stepTask.setCompletedAt(new Date());
        }

        return this.updateById(stepTask);
    }

    @Override
    public Page<StepTask> getStepTaskByPage(Page<StepTask> page, StepTaskQueryRequest queryRequest) {
        LambdaQueryWrapper<StepTask> queryWrapper = buildQueryWrapper(queryRequest);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<StepTask> getStepTasksByTaskId(Long taskId) {
        LambdaQueryWrapper<StepTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StepTask::getTaskId, taskId)
                    .orderByAsc(StepTask::getStepId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStepTaskStatus(Long id, Integer isCompleted) {
        StepTask stepTask = this.getById(id);
        if (stepTask == null) {
            throw new ServiceException("任务详细不存在");
        }

        stepTask.setIsCompleted(isCompleted);
        if (isCompleted == 1) {
            stepTask.setCreatedAt(new Date());
        } else {
            stepTask.setCompletedAt(new Date());
        }

        return this.updateById(stepTask);
    }

    /**
     * 更新任务详细状态
     *
     * @param stepId
     * @return
     */
    @Override
    public boolean updateStepTaskStatus(Long stepId) {

        if (stepId == null) {
           throw new ServiceException("ID不能为空");
        }
        //查询当前任务
        StepTask stepTask = this.getById(stepId);
        //更新任务详细状态
        stepTask.setIsCompleted(1);
        stepTask.setCreatedAt(new Date());
        boolean result = this.updateById(stepTask);
        if (! result){
            throw new ServiceException("更新任务详细状态失败");
        }
        return true;
    }

    /**
     * 获取未完成的任务详细
     * @param taskId
     * @return
     */
    @Override
    public List<StepTask> getUncompletedStepsByTaskId(Long taskId) {
        QueryWrapper<StepTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId).ne("is_completed", 2);
        return this.baseMapper.selectList(queryWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StepTask> createStepTasksBatch(StepTaskBatchCreateRequest batchCreateRequest) {
        // 验证所属任务是否存在
        OrderTask orderTask = orderTaskService.getById(batchCreateRequest.getTaskId());
        if (orderTask == null) {
            throw new ServiceException("所属任务不存在");
        }

        List<StepTask> stepTasks = new ArrayList<>();
        Date currentTime = new Date();

        for (StepTaskBatchCreateRequest.StepInfo stepInfo : batchCreateRequest.getStepInfos()) {
            // 检查是否已存在相同的工序任务详细
            LambdaQueryWrapper<StepTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StepTask::getTaskId, batchCreateRequest.getTaskId())
                        .eq(StepTask::getStepId, stepInfo.getStepId());
            StepTask existingStepTask = this.getOne(queryWrapper);
            if (existingStepTask != null) {
                throw new ServiceException("工序【" + stepInfo.getStepName() + "】的任务详细已存在");
            }

            // 创建新的任务详细
            StepTask stepTask = new StepTask();
            stepTask.setTaskId(batchCreateRequest.getTaskId());
            stepTask.setStepId(stepInfo.getStepId());
            stepTask.setStepNumber(stepInfo.getStepNumber());
            stepTask.setStepName(stepInfo.getStepName());
            stepTask.setAssignee(stepInfo.getAssignee());

            // 设置期望完成时间
            stepTask.setExpectedAt(stepInfo.getExpectedAt());

           // stepTask.setCreatedAt(currentTime);
            stepTask.setIsDeleted(0);
            stepTask.setIsCompleted(0);

            stepTasks.add(stepTask);
        }

        // 批量保存
        if (!this.saveBatch(stepTasks)) {
            throw new ServiceException("批量创建任务详细失败");
        }

        return stepTasks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StepTask> updateStepTasksBatch(StepTaskBatchUpdateRequest batchUpdateRequest) {
        List<StepTask> updatedStepTasks = new ArrayList<>();
        Date currentTime = new Date();

        for (StepTaskBatchUpdateRequest.StepTaskUpdateInfo updateInfo : batchUpdateRequest.getStepTaskUpdateInfos()) {
            // 验证任务详细是否存在
            StepTask existingStepTask = this.getById(updateInfo.getId());
            if (existingStepTask == null) {
                throw new ServiceException("任务详细ID【" + updateInfo.getId() + "】不存在");
            }

            StepTask stepTask = new StepTask();
            BeanUtils.copyProperties(updateInfo, stepTask);

            // 如果完成状态变为已完成，自动设置完成时间
            if (updateInfo.getIsCompleted() != null && updateInfo.getIsCompleted() == 1
                && !Integer.valueOf(1).equals(existingStepTask.getIsCompleted())) {
                stepTask.setCompletedAt(currentTime);
            }
            // 如果完成状态从已完成变为未完成，清除完成时间
            else if (updateInfo.getIsCompleted() != null && updateInfo.getIsCompleted() == 0
                && Integer.valueOf(1).equals(existingStepTask.getIsCompleted())) {
                stepTask.setCompletedAt(null);
            }

            // 更新任务详细
            if (!this.updateById(stepTask)) {
                throw new ServiceException("更新任务详细ID【" + updateInfo.getId() + "】失败");
            }

            // 获取更新后的数据
            StepTask updatedStepTask = this.getById(updateInfo.getId());
            updatedStepTasks.add(updatedStepTask);
        }

        return updatedStepTasks;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<StepTask> buildQueryWrapper(StepTaskQueryRequest queryRequest) {
        LambdaQueryWrapper<StepTask> queryWrapper = new LambdaQueryWrapper<>();

        if (queryRequest.getTaskId() != null) {
            queryWrapper.eq(StepTask::getTaskId, queryRequest.getTaskId());
        }

        if (queryRequest.getStepId() != null) {
            queryWrapper.eq(StepTask::getStepId, queryRequest.getStepId());
        }

        if (StringUtils.isNotBlank(queryRequest.getStepName())) {
            queryWrapper.like(StepTask::getStepName, queryRequest.getStepName());
        }

        if (StringUtils.isNotBlank(queryRequest.getAssignee())) {
            queryWrapper.like(StepTask::getAssignee, queryRequest.getAssignee());
        }

        if (queryRequest.getIsCompleted() != null) {
            queryWrapper.eq(StepTask::getIsCompleted, queryRequest.getIsCompleted());
        }

        queryWrapper.orderByDesc(StepTask::getCreatedAt);

        return queryWrapper;
    }
}





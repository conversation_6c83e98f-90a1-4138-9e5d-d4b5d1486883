package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.BomPickRecord;
import com.cpmes.system.mapperJenasi.BomPickRecordMapper;
import com.cpmes.system.serviceJenasi.BomPickRecordService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【bom_pick_record(BOM领料记录表)】的数据库操作Service实现
* @createDate 2025-08-02 08:42:22
*/
@Service
@DS("slave")
public class BomPickRecordServiceImpl extends ServiceImpl<BomPickRecordMapper, BomPickRecord>
    implements BomPickRecordService {

}





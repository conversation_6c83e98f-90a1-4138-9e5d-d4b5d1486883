package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.ProductWarehouse;
import com.cpmes.system.vo.ProductWarehouseVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface ProductWarehouseService extends IService<ProductWarehouse> {
    /**
     * 分页查询成品信息（包含区域）
     * @param page 分页对象
     * @param productWarehouse 查询条件
     * @param zoneCode 区域代码
     * @param zoneName 区域名称
     * @param sortField 排序字段
     * @param sortOrder 排序顺序
     * @return
     */
    Page<ProductWarehouseVO> selectProductWarehousePage(Page<ProductWarehouseVO> page, ProductWarehouse productWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder);

    /**
     * 批量删除产品
     * @param ids 产品ID列表
     * @return 是否成功
     */
    boolean batchDeleteByIds(List<Integer> ids);

    /**
     * 成品出库
     */
    boolean outbound(Integer productId,Integer quantity,String userName);
    /**
     * 成品入库
     */
    boolean inbound(Integer productId,Integer quantity,String userName);

    /**
     * 获取成品仓库统计信息
     * @return 统计数据
     */
    Object getStatistics();
}

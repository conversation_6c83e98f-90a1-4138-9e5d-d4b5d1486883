package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cpmes.system.entity.MaterialSummary;
import com.cpmes.system.mapperJenasi.MaterialSummaryMapper;
import com.cpmes.system.serviceJenasi.MaterialSummaryService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料库存汇总表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Service
@DS("slave")
public class MaterialSummaryServiceImpl extends ServiceImpl<MaterialSummaryMapper, MaterialSummary> implements MaterialSummaryService {

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 预先验证所有ID是否存在，只删除存在的ID（使用主键id字段）
        List<Integer> existingIds = this.lambdaQuery()
                .in(MaterialSummary::getId, ids)  // 使用主键id字段
                .list()
                .stream()
                .map(MaterialSummary::getId)      // 使用主键id字段
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false; // 没有找到任何匹配的记录
        }

        // 只删除存在的记录
        return this.removeByIds(existingIds);
    }

    @Override
    public MaterialSummary getByMaterialAndDate(Integer materialId, String materialType, LocalDate date) {
        return baseMapper.findByMaterialAndDate(materialId, materialType, date);
    }

    @Override
    public List<MaterialSummary> getByDate(LocalDate date) {
        LambdaQueryWrapper<MaterialSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialSummary::getStockDate, date);
        return this.list(queryWrapper);
    }

    @Override
    public List<MaterialSummary> getLatestInventory() {
        LambdaQueryWrapper<MaterialSummary> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询最新记录
        queryWrapper.eq(MaterialSummary::getIsLatest, true);
        // 按物料类型和ID排序，方便查看
        queryWrapper.orderByAsc(MaterialSummary::getMaterialType)
                   .orderByAsc(MaterialSummary::getMaterialId);

        List<MaterialSummary> result = this.list(queryWrapper);
        // 记录调试信息
        System.out.println("查询到最新记录数量: " + (result != null ? result.size() : 0));
        return result;
    }

    @Override
    public MaterialSummary getLatestByMaterialId(Integer materialId, String materialType) {
        LambdaQueryWrapper<MaterialSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialSummary::getMaterialId, materialId)
                   .eq(MaterialSummary::getMaterialType, materialType)
                   .eq(MaterialSummary::getIsLatest, true);
        return this.getOne(queryWrapper);
    }

}

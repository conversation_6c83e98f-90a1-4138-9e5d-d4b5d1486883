package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.StepTask;
import com.cpmes.system.entity.dto.stepTask.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【step_task(任务详细表：记录每个任务对应的工序执行信息)】的数据库操作Service
* @createDate 2025-06-25 13:59:44
*/
@DS("slave")
public interface StepTaskService extends IService<StepTask> {

    /**
     * 获取工序状态为已完成的不良项并统计
     * @param startTime
     * @param endTime
     * @return
     */
    List<DefectStatDTO> getDefectStats(LocalDateTime startTime, LocalDateTime endTime);

    /**已使用
     * 添加不良项，数量
     */
    boolean addDefectInfo(Long stepTaskId, Map<String, Integer> defects);

    /**  已使用
     * 批量创建任务详细（同一任务下的多个工序）
     * @param batchCreateRequest 批量创建请求对象
     * @return 创建的任务详细信息列表
     */
    List<StepTask> createStepTasksBatch(StepTaskBatchCreateRequest batchCreateRequest);

    /**
     * 批量修改任务详细
     * @param batchUpdateRequest 批量修改请求对象
     * @return 修改的任务详细信息列表
     */
    List<StepTask> updateStepTasksBatch(StepTaskBatchUpdateRequest batchUpdateRequest);

    /**
     * 根据任务ID创建工序任务详细
     * @param taskId 任务ID
     * @return 创建的任务详细信息列表
     */
    List<StepTask> createStepTasksByTaskId(Long taskId);

    /**
     * 更新任务详细
     * @param updateRequest 更新请求对象
     * @return 是否更新成功
     */
    boolean updateStepTask(StepTaskUpdateRequest updateRequest);

    /**
     * 分页查询任务详细
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<StepTask> getStepTaskByPage(Page<StepTask> page, StepTaskQueryRequest queryRequest);

    /**
     * 根据任务ID获取任务详细列表
     * @param taskId 任务ID
     * @return 任务详细列表
     */
    List<StepTask> getStepTasksByTaskId(Long taskId);

    /**  已使用
     * 更新任务详细完成状态
     * @param id 任务详细ID
     * @param isCompleted 是否完成（0=未完成，1=已完成）
     * @return 是否更新成功
     */
    boolean updateStepTaskStatus(Long id, Integer isCompleted);

    /**
     * 更新任务详细完成状态
     * @param id 更新请求
     * @return 是否更新成功
     */
    boolean updateStepTaskStatus(Long id);

    /**
     * 查询未完成的工序任务
     */
    List<StepTask> getUncompletedStepsByTaskId(Long taskId);
}

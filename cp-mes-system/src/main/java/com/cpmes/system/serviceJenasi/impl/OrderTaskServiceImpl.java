package com.cpmes.system.serviceJenasi.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.entity.OrderTask;
import com.cpmes.system.entity.Orders;
import com.cpmes.system.entity.dto.orderTask.OrderTaskCreateRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskDetailQueryRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskQueryRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskUpdateRequest;
import com.cpmes.system.entity.dto.orderTask.OrderWithTasksQueryRequest;
import com.cpmes.system.entity.dto.orderTask.TaskDetail;
import com.cpmes.system.entity.vo.OrderTaskDetailVO;
import com.cpmes.system.entity.vo.OrderWithTasksVO;
import com.cpmes.system.entity.vo.GroupedOrderTaskVO;
import com.cpmes.system.mapperJenasi.OrderTaskMapper;
import com.cpmes.system.serviceJenasi.OrderTaskService;
import com.cpmes.system.serviceJenasi.OrdersService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【order_task(订单任务表)】的数据库操作Service实现
* @createDate 2025-06-21 11:08:59
*/
@Service
@DS("slave")
public class OrderTaskServiceImpl extends ServiceImpl<OrderTaskMapper, OrderTask>
    implements OrderTaskService {

    @Resource
    private OrdersService ordersService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OrderTask> createOrderTask(OrderTaskCreateRequest orderTaskCreateRequest) {
        //校验订单是否存在
        Orders orders = ordersService.getById(orderTaskCreateRequest.getOrderId());
        if (orders == null) {
            throw new ServiceException("订单不存在");
        }
        //遍历构建任务列表
        ArrayList<OrderTask> taskList = new ArrayList<>();
        for (TaskDetail detail : orderTaskCreateRequest.getTasks()) {
            OrderTask task = new OrderTask();
            //设置订单物品信息
            task.setOrderId(orders.getId());
            task.setItemId(orderTaskCreateRequest.getItemId());
            task.setItemName(orderTaskCreateRequest.getItemName());
             task.setQuantity(orderTaskCreateRequest.getQuantity());
            //设置任务信息
            task.setProcessRouteCode(detail.getRouteCode());
            task.setRemark(detail.getRemark());
            //任务级别（NORMAL=普通，URGENT=加急）
            task.setTaskLevel(detail.getTaskLevel());
            // 默认任务状态 → 待处理 任务状态（PENDING=待处理，PROCESSING=处理中，COMPLETED=已完成）
            task.setTaskStatus("PENDING");
            task.setCreatedName(detail.getCreateName());
            task.setUpdatedName(detail.getUpdateName());
            task.setCreateTime(new Date());
            task.setUpdateTime(new Date());
            task.setExpectedTime(detail.getExpectedTime());
            task.setIsDelete(0);
            //添加到集合中
            taskList.add(task);
        }
        // 3. 批量保存
        this.saveBatch(taskList);
        return taskList;
    }

    /**
     * 分页获取任务列表
     * @param page
     * @param orderTaskQueryRequest
     * @return
     */
    @Override
    public Page<OrderTask> orderTaskByPage(Page<OrderTask> page, OrderTaskQueryRequest orderTaskQueryRequest) {
        return this.page(page,this.getQueryWrapper(orderTaskQueryRequest));
    }

    /**
     * 获取查询条件
     * @param request
     * @return
     */
    @Override
    public QueryWrapper<OrderTask> getQueryWrapper(OrderTaskQueryRequest request) {
        QueryWrapper<OrderTask> queryWrapper = new QueryWrapper<>();
        Long orderId = request.getOrderId();
        String taskLevel = request.getTaskLevel();
        String taskStatus = request.getTaskStatus();
        String itemName = request.getItemName();
        String processRouteCode = request.getProcessRouteCode();
        queryWrapper.eq(orderId!=null,"order_id", orderId);
        queryWrapper.like(StrUtil.isNotBlank(itemName),"item_name", itemName);
        queryWrapper.like(StrUtil.isNotBlank(taskStatus),"task_status", taskStatus);
        queryWrapper.eq(StrUtil.isNotBlank(taskLevel),"task_level", taskLevel);
        queryWrapper.like(StrUtil.isNotBlank(processRouteCode),"process_route_code", processRouteCode);
        return queryWrapper;
    }

    /**
     * 修改任务状态
     * @param id
     * @param taskStatus
     * @return
     */
    @Override
    public boolean updateTaskStatus(Long id, String taskStatus) {
        if (id == null || StrUtil.isBlank(taskStatus) || id <= 0){
            throw new ServiceException("参数错误");
        }
        //查询当前任务
        OrderTask orderTask = this.getById(id);
        if (orderTask == null){
          throw new ServiceException("任务不存在");
        }
        if (taskStatus.equals("COMPLETED")){
           orderTask.setCompletedTime(new Date());
        }
        orderTask.setTaskStatus(taskStatus);
        boolean result = this.updateById(orderTask);
        if (!result){
            throw new ServiceException("修改任务状态失败");
        }
        return true;
    }

    /**
     * 修改任务级别
     * @param id
     * @param taskLevel
     * @return
     */
    @Override
    public boolean updateTaskLevel(Long id, String taskLevel) {
        if (id == null || StrUtil.isBlank(taskLevel) || id <= 0){
            throw new ServiceException("参数错误");
        }
        //查询当前任务
        OrderTask orderTask = this.getById(id);
        if (orderTask == null){
            throw new ServiceException("任务不存在");
        }
        orderTask.setTaskLevel(taskLevel);
        boolean result = this.updateById(orderTask);
        if (!result){
            throw new ServiceException("修改任务级别失败");
        }
        return true;
    }

    /**
     * 设置任务延期
     * @param id
     * @return
     */
    @Override
    public boolean setTaskDefer(Long id) {
        if (id == null  || id <= 0){
            throw new ServiceException("参数错误");
        }
        //查询当前任务
        OrderTask orderTask = this.getById(id);
        if (orderTask == null){
            throw new ServiceException("任务不存在");
        }
        orderTask.setIsDefer(1);
        boolean result = this.updateById(orderTask);
        if (!result){
            throw new ServiceException("设置任务延期失败");
        }
        return true;
    }

    /**
     * 修改订单任务
     * @param orderTaskUpdateRequest
     * @return
     */
    @Override
    public boolean updateOrderTask(OrderTaskUpdateRequest orderTaskUpdateRequest) {
        if (orderTaskUpdateRequest == null || orderTaskUpdateRequest.getId() == null || orderTaskUpdateRequest.getId() <= 0) {
            throw new ServiceException("参数错误");
        }
        //查询当前任务
        OrderTask orderTask = this.getById(orderTaskUpdateRequest.getId());
        if (orderTask == null) {
            throw new ServiceException("任务不存在");
        }
        //更新任务信息
        if (orderTaskUpdateRequest.getOrderId() != null) {
            orderTask.setOrderId(orderTaskUpdateRequest.getOrderId());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getItemId())) {
            orderTask.setItemId(orderTaskUpdateRequest.getItemId());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getItemName())) {
            orderTask.setItemName(orderTaskUpdateRequest.getItemName());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getProcessRouteCode())) {
            orderTask.setProcessRouteCode(orderTaskUpdateRequest.getProcessRouteCode());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getTaskLevel())) {
            orderTask.setTaskLevel(orderTaskUpdateRequest.getTaskLevel());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getTaskStatus())) {
            orderTask.setTaskStatus(orderTaskUpdateRequest.getTaskStatus());
        }

        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getCreatedName())) {
            orderTask.setCreatedName(orderTaskUpdateRequest.getCreatedName());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getUpdatedName())) {
            orderTask.setUpdatedName(orderTaskUpdateRequest.getUpdatedName());
        }
        if (StrUtil.isNotBlank(orderTaskUpdateRequest.getRemark())) {
            orderTask.setRemark(orderTaskUpdateRequest.getRemark());
        }
        if (orderTaskUpdateRequest.getExpectedTime() != null) {
            orderTask.setExpectedTime(orderTaskUpdateRequest.getExpectedTime());
        }
        if (orderTaskUpdateRequest.getCompletedTime() != null) {
            orderTask.setCompletedTime(orderTaskUpdateRequest.getCompletedTime());
        }
        if (orderTaskUpdateRequest.getIsDefer() != null) {
            orderTask.setIsDefer(orderTaskUpdateRequest.getIsDefer());
        }
        orderTask.setUpdateTime(new Date());
        boolean result = this.updateById(orderTask);
        if (!result) {
            throw new ServiceException("修改订单任务失败");
        }
        return true;
    }

    /**
     * 分页查询详细任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    @Override
    public Page<OrderTaskDetailVO> getOrderTaskDetailPage(Page<OrderTaskDetailVO> page, OrderTaskDetailQueryRequest queryRequest) {
        // 调用Mapper中的自定义分页查询方法
        return baseMapper.selectOrderTaskDetailPage(page, queryRequest);
    }

    /**
     * 分页查询分组后的详细任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    @Override
    public Page<GroupedOrderTaskVO> getGroupedOrderTaskDetailPage(Page<GroupedOrderTaskVO> page, OrderTaskDetailQueryRequest queryRequest) {
        // 为了正确处理分页，我们需要先获取所有匹配的数据进行分组，然后再进行分页
        // 这里暂时使用一个较大的页面大小来获取所有数据
        Page<OrderTaskDetailVO> detailPage = new Page<>(1, Integer.MAX_VALUE);
        Page<OrderTaskDetailVO> detailResult = baseMapper.selectOrderTaskDetailPage(detailPage, queryRequest);

        // 将OrderTaskDetailVO转换为GroupedOrderTaskVO
        List<GroupedOrderTaskVO> groupedList = convertToGroupedOrderTaskVO(detailResult.getRecords());

        // 手动进行分页处理
        int current = (int) page.getCurrent();
        int size = (int) page.getSize();
        int total = groupedList.size();
        int startIndex = (current - 1) * size;
        int endIndex = Math.min(startIndex + size, total);

        List<GroupedOrderTaskVO> pagedList;
        if (startIndex >= total) {
            pagedList = Collections.emptyList();
        } else {
            pagedList = groupedList.subList(startIndex, endIndex);
        }

        // 构建分页结果
        Page<GroupedOrderTaskVO> result = new Page<>();
        result.setCurrent(current);
        result.setSize(size);
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));
        result.setRecords(pagedList);

        return result;
    }

    /**
     * 将OrderTaskDetailVO列表转换为GroupedOrderTaskVO列表
     * @param detailList 详细任务列表
     * @return 分组后的任务列表
     */
    private List<GroupedOrderTaskVO> convertToGroupedOrderTaskVO(List<OrderTaskDetailVO> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按工单ID和物品ID分组（orderId + itemId）
        Map<String, List<OrderTaskDetailVO>> groupedByOrderAndItem = detailList.stream()
                .collect(Collectors.groupingBy(detail -> detail.getOrderId() + "_" + detail.getItemId()));

        List<GroupedOrderTaskVO> result = new ArrayList<>();

        for (Map.Entry<String, List<OrderTaskDetailVO>> entry : groupedByOrderAndItem.entrySet()) {
            List<OrderTaskDetailVO> taskDetails = entry.getValue();
            if (taskDetails.isEmpty()) {
                continue;
            }
            // 取第一个作为基础信息（因为同一个工单+物品的基础信息是相同的）
            OrderTaskDetailVO firstDetail = taskDetails.get(0);
            GroupedOrderTaskVO grouped = new GroupedOrderTaskVO();
            grouped.setOrderId(firstDetail.getOrderId());
            grouped.setItemId(firstDetail.getItemId());
            grouped.setItemName(firstDetail.getItemName());
            grouped.setProcessRouteCode(firstDetail.getProcessRouteCode());
            grouped.setTaskLevel(firstDetail.getTaskLevel());
            grouped.setTaskStatus(firstDetail.getTaskStatus());

            // 转换工序任务列表
            List<GroupedOrderTaskVO.StepTaskVO> stepTasks = taskDetails.stream()
                    .filter(detail -> detail.getStepTaskId() != null) // 过滤掉没有工序任务的记录
                    .map(this::convertToStepTaskVO)
                    .collect(Collectors.toList());

            grouped.setStepTasks(stepTasks);
            result.add(grouped);
        }

        return result;
    }

    /**
     * 将OrderTaskDetailVO转换为StepTaskVO
     * @param detail 详细任务信息
     * @return 工序任务VO
     */
    private GroupedOrderTaskVO.StepTaskVO convertToStepTaskVO(OrderTaskDetailVO detail) {
        GroupedOrderTaskVO.StepTaskVO stepTask = new GroupedOrderTaskVO.StepTaskVO();
        // 设置工单任务ID
        stepTask.setOrderTaskId(detail.getOrderTaskId());
        stepTask.setStepTaskId(detail.getStepTaskId());
        stepTask.setStepId(detail.getStepId());
        stepTask.setStepNumber(detail.getStepNumber());
        stepTask.setStepName(detail.getStepName());
        stepTask.setAssignee(detail.getAssignee());
        stepTask.setIsCompleted(detail.getIsCompleted());
        stepTask.setStepTaskCreateTime(detail.getStepTaskCreateTime());
        stepTask.setCompletedAt(detail.getCompletedAt());
        stepTask.setExpectedAt(detail.getExpectedAt());
        return stepTask;
    }

    /**
     * 根据订单ID获取订单及其关联的任务信息
     * @param orderId 订单ID
     * @return 订单及任务信息
     */
    @Override
    public OrderWithTasksVO getOrderWithTasks(Long orderId) {
        if (orderId == null || orderId <= 0) {
            throw new ServiceException("订单ID不能为空");
        }
        // 查询订单信息
        Orders order = ordersService.getById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        // 查询该订单的所有任务
        List<OrderTask> tasks = this.getTasksByOrderId(orderId);
        // 构建响应对象
        OrderWithTasksVO result = new OrderWithTasksVO();
        BeanUtils.copyProperties(order, result);
        result.setTasks(tasks);

        // 计算任务统计信息
        this.calculateTaskStatistics(result, tasks);

        return result;
    }

    /**
     * 分页查询订单及其关联的任务信息
     * @param page 分页对象
     * @return 分页结果
     */
    @Override
    public Page<OrderWithTasksVO> getOrdersWithTasksPage(Page<OrderWithTasksVO> page) {
        return this.getOrdersWithTasksPage(page, null);
    }

    /**
     * 带条件分页查询订单及其关联的任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    @Override
    public Page<OrderWithTasksVO> getOrdersWithTasksPage(Page<OrderWithTasksVO> page, OrderWithTasksQueryRequest queryRequest) {
        // 构建订单查询条件
        QueryWrapper<Orders> orderQueryWrapper = this.buildOrderQueryWrapper(queryRequest);
        // 先分页查询订单
        Page<Orders> ordersPage = new Page<>(page.getCurrent(), page.getSize());
        Page<Orders> orderResult = ordersService.page(ordersPage, orderQueryWrapper);
        // 转换为OrderWithTasksVO
        List<OrderWithTasksVO> orderWithTasksList = new ArrayList<>();
        for (Orders order : orderResult.getRecords()) {
            OrderWithTasksVO orderWithTasks = new OrderWithTasksVO();
            BeanUtils.copyProperties(order, orderWithTasks);
            // 查询该订单的任务（根据任务条件过滤）
            List<OrderTask> tasks = this.getTasksByOrderIdWithCondition(order.getId(), queryRequest);
            orderWithTasks.setTasks(tasks);
            // 计算任务统计信息
            this.calculateTaskStatistics(orderWithTasks, tasks);
            orderWithTasksList.add(orderWithTasks);
        }
        // 构建分页结果
        Page<OrderWithTasksVO> result = new Page<>();
        result.setCurrent(orderResult.getCurrent());
        result.setSize(orderResult.getSize());
        result.setTotal(orderResult.getTotal());
        result.setPages(orderResult.getPages());
        result.setRecords(orderWithTasksList);
        return result;
    }

    /**
     * 获取指定订单的任务列表
     * @param orderId 订单ID
     * @return 任务列表
     */
    @Override
    public List<OrderTask> getTasksByOrderId(Long orderId) {
        if (orderId == null || orderId <= 0) {
            return Collections.emptyList();
        }
        QueryWrapper<OrderTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    /**
     * 构建订单查询条件
     * @param queryRequest 查询请求
     * @return 查询条件
     */
    private QueryWrapper<Orders> buildOrderQueryWrapper(OrderWithTasksQueryRequest queryRequest) {
        QueryWrapper<Orders> queryWrapper = new QueryWrapper<>();
        if (queryRequest == null) {
            return queryWrapper;
        }
        // 订单ID
        if (queryRequest.getOrderId() != null) {
            queryWrapper.eq("id", queryRequest.getOrderId());
        }
        // 订单状态
        if (StrUtil.isNotBlank(queryRequest.getOrderStatus())) {
            queryWrapper.eq("status", queryRequest.getOrderStatus());
        }
        //queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }

    /**
     * 根据订单ID和条件获取任务列表
     * @param orderId 订单ID
     * @param queryRequest 查询条件
     * @return 任务列表
     */
    private List<OrderTask> getTasksByOrderIdWithCondition(Long orderId, OrderWithTasksQueryRequest queryRequest) {
        if (orderId == null || orderId <= 0) {
            return Collections.emptyList();
        }
        QueryWrapper<OrderTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_id", orderId);
        // 如果有任务相关的查询条件，则应用这些条件
        if (queryRequest != null) {
            // 任务等级
            if (StrUtil.isNotBlank(queryRequest.getTaskLevel())) {
                queryWrapper.eq("task_level", queryRequest.getTaskLevel());
            }
            // 任务状态
            if (StrUtil.isNotBlank(queryRequest.getTaskStatus())) {
                queryWrapper.eq("task_status", queryRequest.getTaskStatus());
            }
            // 物品名称（模糊查询）
            if (StrUtil.isNotBlank(queryRequest.getItemName())) {
                queryWrapper.like("item_name", queryRequest.getItemName());
            }
        }
      //  queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    /**
     * 计算任务统计信息
     * @param orderWithTasks 订单任务VO对象
     * @param tasks 任务列表
     */
    private void calculateTaskStatistics(OrderWithTasksVO orderWithTasks, List<OrderTask> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            orderWithTasks.setTaskCount(0);
            orderWithTasks.setCompletedTaskCount(0);
            orderWithTasks.setPendingTaskCount(0);
            orderWithTasks.setProcessingTaskCount(0);
            return;
        }

        orderWithTasks.setTaskCount(tasks.size());

        // 统计各状态任务数量
        int completedCount = 0;
        int pendingCount = 0;
        int processingCount = 0;

        for (OrderTask task : tasks) {
            String status = task.getTaskStatus();
            if ("COMPLETED".equals(status)) {
                completedCount++;
            } else if ("PENDING".equals(status)) {
                pendingCount++;
            } else if ("PROCESSING".equals(status)) {
                processingCount++;
            }
        }

        orderWithTasks.setCompletedTaskCount(completedCount);
        orderWithTasks.setPendingTaskCount(pendingCount);
        orderWithTasks.setProcessingTaskCount(processingCount);
    }
}





package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.entity.MaterialSummary;
import com.cpmes.system.entity.RawMaterialWarehouse;
import com.cpmes.system.mapperJenasi.RawMaterialWarehouseMapper;
import com.cpmes.system.serviceJenasi.InOutRequestService;
import com.cpmes.system.serviceJenasi.MaterialSummaryService;
import com.cpmes.system.serviceJenasi.RawMaterialWarehouseService;
import com.cpmes.system.vo.RawMaterialWarehouseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("slave")
public class RawMaterialWarehouseServiceImpl extends ServiceImpl<RawMaterialWarehouseMapper, RawMaterialWarehouse> implements RawMaterialWarehouseService {

    @Resource
    @Lazy
    private InOutRequestService inOutRequestService;

    @Resource
    private MaterialSummaryService materialSummaryService;

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 预先验证所有ID是否存在，只删除存在的ID
        List<Integer> existingIds = this.lambdaQuery()
                .in(RawMaterialWarehouse::getMaterialId, ids)
                .list()
                .stream()
                .map(RawMaterialWarehouse::getMaterialId)
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false; // 没有找到任何匹配的记录
        }

        // 只删除存在的记录
        return this.removeByIds(existingIds);
    }

    /**
     * 物料出库
     * @param materialId
     * @param quantity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean outbound(Integer materialId,Integer quantity, String userName) {
        RawMaterialWarehouse rawMaterialWarehouse = this.getById(materialId);
        if (rawMaterialWarehouse == null){
            throw new RuntimeException("未找到该物料");
        }
        //新增出库记录
        String materialName = rawMaterialWarehouse.getMaterialName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 1;
        boolean result = inOutRequestService.inOutbound(materialId, quantity, userName, materialName, "原料",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        //添加库存总览记录
        //获取当前日期
//        LocalDate today = LocalDate.now();
//        LocalDateTime updatedTime = rawMaterialWarehouse.getUpdatedTime();
//        if (updatedTime.toLocalDate().isBefore(today)) {
//            System.out.println("更新时间的日期早于今天");
//            MaterialSummary materialSummary = new MaterialSummary();
//            materialSummary.setMaterialId(materialId);
//            materialSummary.setMaterialName(materialName);
//            materialSummary.setMaterialType("原料");
//        }
        //出库
        int outbound = this.baseMapper.outbound(materialId, quantity);
        if (outbound <  0){
            throw new RuntimeException("出库失败");
        }

        return true;
    }

    /**
     * 物料入库
     * @param materialId
     * @param quantity
     * @return
     */
    @Override
    public boolean inbound(Integer materialId, Integer quantity, String userName) {
        RawMaterialWarehouse rawMaterialWarehouse = this.getById(materialId);
        if (rawMaterialWarehouse == null){
            throw new RuntimeException("未找到该物料");
        }
        //新增出入库记录
        String materialName = rawMaterialWarehouse.getMaterialName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 0;
        boolean result = inOutRequestService.inOutbound(materialId, quantity, userName, materialName, "原料",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        int outbound = this.baseMapper.inbound(materialId, quantity);
        if (outbound <  0){
            throw new RuntimeException("入库失败");
        }
        return true;
    }

    /**
     * 获取原料仓库统计信息
     */
    @Override
    public Object getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 获取所有原料记录
        List<RawMaterialWarehouse> allMaterials = this.list();

        // 总库存数量（所有物料的库存总和）
        Long totalStock = allMaterials.stream()
                .mapToLong(material -> material.getCurrentStock() != null ? material.getCurrentStock() : 0L)
                .sum();

        // 物料种类数（物料总数）
        int totalMaterials = allMaterials.size();

        // 低库存物料数（currentStock <= fields2安全库存）
        long lowStock = allMaterials.stream()
                .filter(material -> {
                    Integer currentStock = material.getCurrentStock();
                    String fields2 = material.getFields2(); // 安全库存
                    if (currentStock == null || fields2 == null || fields2.isEmpty()) {
                        return false;
                    }
                    try {
                        Integer safetyStock = Integer.parseInt(fields2);
                        return currentStock <= safetyStock;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .count();

        // 需要采购的物料数（如果有needPurchase字段则使用，否则与低库存数相同）
        long needPurchase;
        try {
            needPurchase = allMaterials.stream()
                    .filter(material -> {
                        Boolean needPurchaseFlag = material.getNeedPurchase();
                        return needPurchaseFlag != null && needPurchaseFlag;
                    })
                    .count();
        } catch (Exception e) {
            // 如果needPurchase字段不可用，则使用低库存数
            needPurchase = lowStock;
        }

        statistics.put("totalStock", totalStock);
        statistics.put("totalMaterials", totalMaterials);
        statistics.put("lowStock", lowStock);
        statistics.put("needPurchase", needPurchase);

        return statistics;
    }

    @Override
    public Page<RawMaterialWarehouseVO> selectRawMaterialWarehousePage(Page<RawMaterialWarehouseVO> page, RawMaterialWarehouse rawMaterialWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder) {
        QueryWrapper<RawMaterialWarehouse> wrapper = new QueryWrapper<>();
        wrapper.like(org.springframework.util.StringUtils.hasText(rawMaterialWarehouse.getMaterialName()), "rmw.material_name", rawMaterialWarehouse.getMaterialName());
        wrapper.eq(org.springframework.util.StringUtils.hasText(rawMaterialWarehouse.getFields3()), "rmw.fields3", rawMaterialWarehouse.getFields3());

        // 当区域参数为空时，使用不关联区域的查询避免数据重复
        if (org.springframework.util.StringUtils.hasText(zoneCode) || org.springframework.util.StringUtils.hasText(zoneName)) {
            wrapper.like(org.springframework.util.StringUtils.hasText(zoneCode), "wz.zone_code", zoneCode);
            wrapper.like(org.springframework.util.StringUtils.hasText(zoneName), "wz.zone_name", zoneName);
        }

        if (rawMaterialWarehouse.getNeedPurchase() != null) {
            wrapper.eq("rmw.need_purchase", rawMaterialWarehouse.getNeedPurchase());
        }

        // 处理排序
        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            String column = StringUtils.camelToUnderline(sortField);
            String sortColumn = "rmw." + column;

            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortColumn);
            } else {
                wrapper.orderByDesc(sortColumn);
            }
        } else {
            // 默认排序
            wrapper.orderByDesc("rmw.updated_time");
        }

        // 根据是否有区域条件选择不同的查询方法
        if (org.springframework.util.StringUtils.hasText(zoneCode) || org.springframework.util.StringUtils.hasText(zoneName)) {
            return this.baseMapper.selectPageWithZone(page, wrapper);
        } else {
            // 使用不关联区域的查询方法，避免数据重复
            return this.baseMapper.selectPageWithoutZone(page, wrapper);
        }
    }

    /**
     * 根据物料编码处理原料仓库记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processRawMaterialByCode(String materialCode, String materialName) {
        log.debug("开始处理物料编码: {} -> {}", materialCode, materialName);

        try {
            // 根据物料编码查询原料仓库表（使用fields3字段存储物料编码）
            LambdaQueryWrapper<RawMaterialWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RawMaterialWarehouse::getFields3, materialCode);
            RawMaterialWarehouse existingMaterial = this.getOne(queryWrapper);

            if (existingMaterial != null) {
                // 存在记录，检查物料名称是否相同
                String existingName = existingMaterial.getMaterialName();
                if (!materialName.equals(existingName)) {
                    // 物料名称不相同，更新名称
                    existingMaterial.setMaterialName(materialName);
                    existingMaterial.setUpdatedTime(LocalDateTime.now());
                    boolean updateResult = this.updateById(existingMaterial);
                    if (updateResult) {
                        log.info("已更新物料编码[{}]的名称: {} -> {}", materialCode, existingName, materialName);
                    } else {
                        log.warn("更新物料编码[{}]的名称失败", materialCode);
                        return false;
                    }
                } else {
                    // 物料名称相同，不需要操作
                    log.debug("物料编码[{}]已存在且名称相同，无需操作", materialCode);
                }
            } else {
                // 不存在记录，创建新的物料记录
                RawMaterialWarehouse newMaterial = new RawMaterialWarehouse();
                newMaterial.setMaterialName(materialName);
                newMaterial.setMaterialType("原料");
                newMaterial.setCurrentStock(0);
                newMaterial.setInboundQuantity(0);
                newMaterial.setOutboundQuantity(0);
                newMaterial.setMinStockQuantity(0);
                newMaterial.setNeedPurchase(false);
                newMaterial.setStockQuantity(0);
                newMaterial.setUnit("个"); // 设置默认单位
                newMaterial.setUpdatedTime(LocalDateTime.now());
                newMaterial.setFields1("BOM导入自动创建");
                newMaterial.setFields2("0");
                newMaterial.setFields3(materialCode);

                boolean saveResult = this.save(newMaterial);
                if (saveResult) {
                    log.info("已创建新的物料记录: 编码[{}] -> 名称[{}]", materialCode, materialName);
                } else {
                    log.warn("创建物料编码[{}]的记录失败", materialCode);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("处理物料编码[{}]时发生异常: 类型={}, 消息={}", materialCode, e.getClass().getSimpleName(), e.getMessage(), e);

            // 提供更详细的错误信息
            String detailedMessage = String.format("处理物料编码[%s]失败", materialCode);
            if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
                detailedMessage += "：数据完整性约束冲突，可能是主键或唯一约束冲突";
            } else if (e instanceof java.sql.SQLException) {
                detailedMessage += "：数据库SQL执行错误 - " + e.getMessage();
            } else if (e.getMessage() != null && e.getMessage().contains("Connection")) {
                detailedMessage += "：数据库连接错误 - " + e.getMessage();
            } else {
                detailedMessage += "：" + e.getMessage();
            }

            throw new RuntimeException(detailedMessage, e);
        }
    }

    /**
     * 根据物料编码处理原料仓库记录（包含板类型信息）
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processRawMaterialByCode(String materialCode, String materialName, String boardType) {
        log.debug("开始处理物料编码（含板类型）: {} -> {}, 板类型: {}", materialCode, materialName, boardType);

        try {
            // 根据物料编码查询原料仓库表（使用fields3字段存储物料编码）
            LambdaQueryWrapper<RawMaterialWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RawMaterialWarehouse::getFields3, materialCode);
            RawMaterialWarehouse existingMaterial = this.getOne(queryWrapper);

            if (existingMaterial != null) {
                // 存在记录，检查物料名称和板类型是否需要更新
                String existingName = existingMaterial.getMaterialName();
                String existingBoardType = existingMaterial.getBoardType();

                boolean needUpdate = false;
                StringBuilder updateInfo = new StringBuilder();

                // 检查物料名称
                if (!materialName.equals(existingName)) {
                    existingMaterial.setMaterialName(materialName);
                    needUpdate = true;
                    updateInfo.append(String.format("名称: %s -> %s", existingName, materialName));
                }

                // 检查板类型
                if (!Objects.equals(boardType, existingBoardType)) {
                    existingMaterial.setBoardType(boardType);
                    needUpdate = true;
                    if (updateInfo.length() > 0) updateInfo.append(", ");
                    updateInfo.append(String.format("板类型: %s -> %s", existingBoardType, boardType));
                }

                if (needUpdate) {
                    existingMaterial.setUpdatedTime(LocalDateTime.now());
                    boolean updateResult = this.updateById(existingMaterial);
                    if (updateResult) {
                        log.info("已更新物料编码[{}]的信息: {}", materialCode, updateInfo.toString());
                    } else {
                        log.warn("更新物料编码[{}]的信息失败", materialCode);
                        return false;
                    }
                } else {
                    // 物料名称和板类型都相同，不需要操作
                    log.debug("物料编码[{}]已存在且信息相同，无需操作", materialCode);
                }
            } else {
                // 不存在记录，创建新的物料记录
                RawMaterialWarehouse newMaterial = new RawMaterialWarehouse();
                newMaterial.setMaterialName(materialName);
                newMaterial.setMaterialType("原料");
                newMaterial.setBoardType(boardType);
                newMaterial.setCurrentStock(0);
                newMaterial.setInboundQuantity(0);
                newMaterial.setOutboundQuantity(0);
                newMaterial.setMinStockQuantity(0);
                newMaterial.setNeedPurchase(false);
                newMaterial.setStockQuantity(0);
                newMaterial.setUpdatedTime(LocalDateTime.now());
                newMaterial.setFields1("BOM导入自动创建");
                newMaterial.setFields2("0");
                newMaterial.setFields3(materialCode);

                boolean saveResult = this.save(newMaterial);
                if (saveResult) {
                    log.info("已创建新的物料记录: 编码[{}] -> 名称[{}], 板类型[{}]", materialCode, materialName, boardType);
                } else {
                    log.warn("创建物料编码[{}]的记录失败", materialCode);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("处理物料编码[{}]时发生异常: 类型={}, 消息={}", materialCode, e.getClass().getSimpleName(), e.getMessage(), e);

            // 提供更详细的错误信息
            String detailedMessage = String.format("处理物料编码[%s]失败", materialCode);
            if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
                detailedMessage += "：数据完整性约束冲突，可能是主键或唯一约束冲突";
            } else if (e instanceof java.sql.SQLException) {
                detailedMessage += "：数据库SQL执行错误 - " + e.getMessage();
            } else if (e.getMessage() != null && e.getMessage().contains("Connection")) {
                detailedMessage += "：数据库连接错误 - " + e.getMessage();
            } else {
                detailedMessage += "：" + e.getMessage();
            }

            throw new RuntimeException(detailedMessage, e);
        }
    }

    @Override
    @DS("slave")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean updateProductNumberByMaterialName(String materialName, String productNumber) {
        log.debug("开始根据物料名称更新产品编码: 物料名称={}, 产品编码={}", materialName, productNumber);

        try {
            // 查询所有名称匹配的原料记录
            LambdaQueryWrapper<RawMaterialWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RawMaterialWarehouse::getMaterialName, materialName);
            List<RawMaterialWarehouse> materials = this.list(queryWrapper);

            if (materials.isEmpty()) {
                log.debug("未找到名称为[{}]的原料记录", materialName);
                return true; // 没有找到记录不算失败
            }

            int updateCount = 0;
            for (RawMaterialWarehouse material : materials) {
                String currentProductNumber = material.getProductNumber();

                // 如果产品编码不同，则更新
                if (!Objects.equals(productNumber, currentProductNumber)) {
                    material.setProductNumber(productNumber);
                    material.setUpdatedTime(LocalDateTime.now());

                    boolean updateResult = this.updateById(material);
                    if (updateResult) {
                        updateCount++;
                        log.debug("已更新原料记录[ID={}]的产品编码: {} -> {}",
                            material.getMaterialId(), currentProductNumber, productNumber);
                    } else {
                        log.warn("更新原料记录[ID={}]的产品编码失败", material.getMaterialId());
                    }
                } else {
                    log.debug("原料记录[ID={}]的产品编码已是[{}]，无需更新",
                        material.getMaterialId(), productNumber);
                }
            }

            log.info("物料名称[{}]的产品编码更新完成：找到{}条记录，更新{}条",
                materialName, materials.size(), updateCount);

            return true;

        } catch (Exception e) {
            log.error("根据物料名称更新产品编码失败: materialName={}, productNumber={}",
                materialName, productNumber, e);
            throw new ServiceException("更新原料表产品编码失败: " + e.getMessage());
        }
    }

    /**
     * 根据物料编码处理原料仓库记录（包含单位信息）
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processRawMaterialByCodeWithUnit(String materialCode, String materialName, String unit) {
        log.debug("开始处理物料编码（含单位）: {} -> {}, 单位: {}", materialCode, materialName, unit);

        try {
            // 根据物料编码查询原料仓库表（使用fields3字段存储物料编码）
            LambdaQueryWrapper<RawMaterialWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RawMaterialWarehouse::getFields3, materialCode);
            RawMaterialWarehouse existingMaterial = this.getOne(queryWrapper);

            if (existingMaterial != null) {
                // 存在记录，检查物料名称和单位是否需要更新
                String existingName = existingMaterial.getMaterialName();
                String existingUnit = existingMaterial.getUnit();
                boolean needUpdate = false;

                if (!materialName.equals(existingName)) {
                    // 物料名称不相同，更新名称
                    existingMaterial.setMaterialName(materialName);
                    needUpdate = true;
                    log.info("物料编码[{}]名称需要更新: {} -> {}", materialCode, existingName, materialName);
                }

                // 处理单位字段更新
                String targetUnit = StringUtils.isNotBlank(unit) ? unit : "个";
                if (!targetUnit.equals(existingUnit)) {
                    existingMaterial.setUnit(targetUnit);
                    needUpdate = true;
                    log.info("物料编码[{}]单位需要更新: {} -> {}", materialCode, existingUnit, targetUnit);
                }

                if (needUpdate) {
                    existingMaterial.setUpdatedTime(LocalDateTime.now());
                    boolean updateResult = this.updateById(existingMaterial);
                    if (updateResult) {
                        log.info("已更新物料编码[{}]的信息", materialCode);
                    } else {
                        log.warn("更新物料编码[{}]的信息失败", materialCode);
                        return false;
                    }
                } else {
                    // 名称和单位都相同，不需要操作
                    log.debug("物料编码[{}]已存在且信息相同，无需操作", materialCode);
                }
            } else {
                // 不存在记录，创建新的物料记录
                RawMaterialWarehouse newMaterial = new RawMaterialWarehouse();
                newMaterial.setMaterialName(materialName);
                newMaterial.setMaterialType("原料");
                newMaterial.setCurrentStock(0);
                newMaterial.setInboundQuantity(0);
                newMaterial.setOutboundQuantity(0);
                newMaterial.setMinStockQuantity(0);
                newMaterial.setNeedPurchase(false);
                newMaterial.setStockQuantity(0);
                newMaterial.setUnit(StringUtils.isNotBlank(unit) ? unit : "个"); // 设置单位，默认为"个"
                newMaterial.setUpdatedTime(LocalDateTime.now());
                newMaterial.setFields1("BOM导入自动创建");
                newMaterial.setFields2("0");
                newMaterial.setFields3(materialCode);

                boolean saveResult = this.save(newMaterial);
                if (saveResult) {
                    log.info("已创建新的物料记录: 编码[{}] -> 名称[{}], 单位[{}]", materialCode, materialName,
                            StringUtils.isNotBlank(unit) ? unit : "个");
                } else {
                    log.warn("创建物料编码[{}]的记录失败", materialCode);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("处理物料编码[{}]时发生异常: 类型={}, 消息={}", materialCode, e.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据物料编码处理原料仓库记录（包含单位和板型信息）
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean processRawMaterialByCodeWithUnitAndBoardType(String materialCode, String materialName, String unit, String boardType) {
        log.debug("开始处理物料编码（含单位和板型）: {} -> {}, 单位: {}, 板型: {}", materialCode, materialName, unit, boardType);

        try {
            // 根据物料编码查询原料仓库表（使用fields3字段存储物料编码）
            LambdaQueryWrapper<RawMaterialWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RawMaterialWarehouse::getFields3, materialCode);
            RawMaterialWarehouse existingMaterial = this.getOne(queryWrapper);

            if (existingMaterial != null) {
                // 存在记录，检查物料名称、单位和板型是否需要更新
                String existingName = existingMaterial.getMaterialName();
                String existingUnit = existingMaterial.getUnit();
                String existingBoardType = existingMaterial.getBoardType();
                boolean needUpdate = false;

                if (!materialName.equals(existingName)) {
                    // 物料名称不相同，更新名称
                    existingMaterial.setMaterialName(materialName);
                    needUpdate = true;
                    log.info("物料编码[{}]名称需要更新: {} -> {}", materialCode, existingName, materialName);
                }

                // 处理单位字段更新
                String targetUnit = StringUtils.isNotBlank(unit) ? unit : "个";
                if (!targetUnit.equals(existingUnit)) {
                    existingMaterial.setUnit(targetUnit);
                    needUpdate = true;
                    log.info("物料编码[{}]单位需要更新: {} -> {}", materialCode, existingUnit, targetUnit);
                }

                // 处理板型字段更新
                String targetBoardType = StringUtils.isNotBlank(boardType) ? boardType : "单板";
                if (!targetBoardType.equals(existingBoardType)) {
                    existingMaterial.setBoardType(targetBoardType);
                    needUpdate = true;
                    log.info("物料编码[{}]板型需要更新: {} -> {}", materialCode, existingBoardType, targetBoardType);
                }

                if (needUpdate) {
                    existingMaterial.setUpdatedTime(LocalDateTime.now());
                    boolean updateResult = this.updateById(existingMaterial);
                    if (updateResult) {
                        log.info("已更新物料编码[{}]的信息", materialCode);
                    } else {
                        log.warn("更新物料编码[{}]的信息失败", materialCode);
                        return false;
                    }
                } else {
                    // 名称、单位和板型都相同，不需要操作
                    log.debug("物料编码[{}]已存在且信息相同，无需操作", materialCode);
                }
            } else {
                // 不存在记录，创建新的物料记录
                RawMaterialWarehouse newMaterial = new RawMaterialWarehouse();
                newMaterial.setMaterialName(materialName);
                newMaterial.setMaterialType("原料");
                newMaterial.setCurrentStock(0);
                newMaterial.setInboundQuantity(0);
                newMaterial.setOutboundQuantity(0);
                newMaterial.setMinStockQuantity(0);
                newMaterial.setNeedPurchase(false);
                newMaterial.setStockQuantity(0);
                newMaterial.setUnit(StringUtils.isNotBlank(unit) ? unit : "个"); // 设置单位，默认为"个"
                newMaterial.setBoardType(StringUtils.isNotBlank(boardType) ? boardType : "单板"); // 设置板型，默认为"单板"
                newMaterial.setUpdatedTime(LocalDateTime.now());
                newMaterial.setFields1("BOM导入自动创建");
                newMaterial.setFields2("0");
                newMaterial.setFields3(materialCode);

                boolean saveResult = this.save(newMaterial);
                if (saveResult) {
                    log.info("已创建新的物料记录: 编码[{}] -> 名称[{}], 单位[{}], 板型[{}]", materialCode, materialName,
                            StringUtils.isNotBlank(unit) ? unit : "个", StringUtils.isNotBlank(boardType) ? boardType : "单板");
                } else {
                    log.warn("创建物料编码[{}]的记录失败", materialCode);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("处理物料编码[{}]时发生异常: 类型={}, 消息={}", materialCode, e.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        }
    }
}

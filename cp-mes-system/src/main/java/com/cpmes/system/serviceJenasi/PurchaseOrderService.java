package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.PurchaseOrder;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseLinkDto;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderAddRequest;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderAuditRequest;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderQueryRequest;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderActualPriceRequest;
import com.cpmes.system.entity.PurchaseOrderPriceLog;
import com.cpmes.system.entity.vo.PurchaseOrderVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【purchase_order(采购订单表)】的数据库操作Service
* @createDate 2025-06-11 10:33:37
*/
public interface PurchaseOrderService extends IService<PurchaseOrder> {

    /**
     * 添加或修改采购订单
     * @param purchaseOrderAddRequest 采购订单请求参数
     * @return 处理后的采购订单实体
     */
    PurchaseOrder addOrUpdatePurchaseOrder(PurchaseOrderAddRequest purchaseOrderAddRequest);


    /**
     * 审核采购订单
     * @param request 审核请求
     * @return 审核后的采购订单
     */
    PurchaseOrder auditPurchaseOrder(PurchaseOrderAuditRequest request);

    /**
     * 获取查询条件
     */
    QueryWrapper<PurchaseOrder> getQueryWrapper(PurchaseOrderQueryRequest request);

    /**
     * 分页获取采购订单列表
     * @param request 查询参数
     * @return 采购订单列表
     */
    Page<PurchaseOrder> getPurchaseOrderList(Page<PurchaseOrder> page, PurchaseOrderQueryRequest request);

    /**
     * 生成下一个采购订单号
     * 格式：PO + YYYYMMDD + 序号（3位）
     * 示例：PO20241222001
     * @return 新的采购订单号
     */
    String generateNextPurchaseNo();

    /**
     * 录入采购订单实际价格
     * @param request 实际价格录入请求
     * @param operator 操作人
     * @return 更新后的采购订单
     */
    PurchaseOrder updateActualPrice(PurchaseOrderActualPriceRequest request, String operator);

    /**
     * 获取采购订单价格变更日志
     * @param purchaseOrderId 采购订单ID
     * @return 价格变更日志列表
     */
    List<PurchaseOrderPriceLog> getPriceChangeLogs(Long purchaseOrderId);

    /**
     * 获取价格差异分析数据
     * @param purchaseOrderId 采购订单ID
     * @return 价格差异分析结果
     */
    Map<String, Object> getPriceDifferenceAnalysis(Long purchaseOrderId);

    /**
     * 分页获取采购订单详情
     */
    Page<PurchaseOrderVO> getPurchaseOrderDetail(Page<PurchaseOrder> page,String purchaseNo, Integer status, String itemName, String supplierName, String applicant, String approver);

    /**
     * 根据ID获取采购订单详情（包含供应商名称）
     * @param id 采购订单ID
     * @return 采购订单详情VO
     */
    PurchaseOrderVO getPurchaseOrderVOById(Long id);

    /**
     * 按条件获取采购订单详情列表（不分页）
     */
    List<PurchaseOrderVO> getPurchaseOrderDetailByCondition(String purchaseNo, Integer status, String itemName, String supplierName, String applicant, String approver);

    /**
     * 打印采购入库二维码
     * @param printData 打印数据
     * @return 打印结果
     */
    Boolean printPurchaseInboundQrCode(java.util.Map<String, Object> printData);

    // ========================================
    // 新增功能：图片上传与链接管理
    // ========================================

    /**
     * 上传采购申请图片
     * @param purchaseOrderId 采购订单ID
     * @param files 图片文件数组
     * @return 图片URL列表
     */
    List<String> uploadPurchaseImages(Long purchaseOrderId, MultipartFile[] files);

    /**
     * 获取采购申请图片列表
     * @param purchaseOrderId 采购订单ID
     * @return 图片URL列表
     */
    List<String> getPurchaseImages(Long purchaseOrderId);

    /**
     * 删除采购申请图片
     * @param purchaseOrderId 采购订单ID
     * @param imageName 图片名称
     * @return 删除结果
     */
    Boolean deletePurchaseImage(Long purchaseOrderId, String imageName);

    /**
     * 保存采购链接
     * @param purchaseOrderId 采购订单ID
     * @param links 链接列表
     * @return 保存结果
     */
    Boolean savePurchaseLinks(Long purchaseOrderId, List<PurchaseLinkDto> links);

    /**
     * 获取采购链接
     * @param purchaseOrderId 采购订单ID
     * @return 链接列表
     */
    List<PurchaseLinkDto> getPurchaseLinks(Long purchaseOrderId);

    // ========================================
    // 新增功能：物流追踪
    // ========================================

    /**
     * 保存物流信息
     * @param purchaseOrderId 采购订单ID
     * @param trackingNumber 快递单号
     * @param logisticsCompany 物流公司
     * @return 是否保存成功
     */
    Boolean saveLogisticsInfo(Long purchaseOrderId, String trackingNumber, String logisticsCompany);

    /**
     * 获取物流追踪信息
     * @param trackingNumber 快递单号
     * @return 物流追踪信息
     */
    LogisticsTrackingDto getLogisticsTracking(String trackingNumber);

    /**
     * 删除物流信息
     * @param purchaseOrderId 采购订单ID
     * @return 是否删除成功
     */
    Boolean deleteLogisticsInfo(Long purchaseOrderId);

    /**
     * 获取采购订单的所有物流追踪信息
     * @param purchaseOrderId 采购订单ID
     * @return 物流追踪信息列表
     */
    List<LogisticsTrackingDto> getAllLogisticsTracking(Long purchaseOrderId);

    /**
     * 清除物流查询缓存
     * @param trackingNumber 快递单号
     */
    void clearLogisticsCache(String trackingNumber);

}

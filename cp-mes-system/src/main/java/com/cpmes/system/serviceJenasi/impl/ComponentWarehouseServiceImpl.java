package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.ComponentWarehouse;
import com.cpmes.system.mapperJenasi.ComponentWarehouseMapper;
import com.cpmes.system.serviceJenasi.ComponentWarehouseService;
import com.cpmes.system.serviceJenasi.InOutRequestService;
import com.cpmes.system.vo.ComponentWarehouseVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS("slave")
public class ComponentWarehouseServiceImpl extends ServiceImpl<ComponentWarehouseMapper, ComponentWarehouse> implements ComponentWarehouseService {

    @Resource
    @Lazy
    private InOutRequestService inOutRequestService;

    @Override
    public List<ComponentWarehouse> findByName(String componentName) {
        // 使用LIKE实现不区分大小写的模糊查询
        return this.baseMapper.selectList(
            new QueryWrapper<ComponentWarehouse>()
                .like("component_name", componentName)  // MyBatis-Plus的like方法会自动添加%
        );
    }

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 方法1：预先验证所有ID是否存在，只删除存在的ID
        List<Integer> existingIds = this.lambdaQuery()
                .in(ComponentWarehouse::getComponentId, ids)
                .list()
                .stream()
                .map(ComponentWarehouse::getComponentId)
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false; // 没有找到任何匹配的记录
        }

        // 只删除存在的记录
        return this.removeByIds(existingIds);
    }

    /**
     * 出库
     * @param componentId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean outbound(Integer componentId, Integer quantity, String userName) {
        ComponentWarehouse componentWarehouse = this.getById(componentId);
        if (componentWarehouse == null){
            throw new RuntimeException("未找到该零部件");
        }
        //新增出库记录
        String componentName = componentWarehouse.getComponentName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 1;
        boolean result = inOutRequestService.inOutbound(componentId, quantity, userName, componentName, "零部件",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        int outbound = this.baseMapper.outbound(componentId, quantity);
        if (outbound <  0){
            throw new RuntimeException("出库失败");
        }
        return true;
    }

    /**
     * 入库
     * @param componentId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean inbound(Integer componentId, Integer quantity, String userName) {
        ComponentWarehouse componentWarehouse = this.getById(componentId);
        if (componentWarehouse == null){
            throw new RuntimeException("未找到该零部件");
        }
        //新增出库记录
        String componentName = componentWarehouse.getComponentName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 0;
        boolean result = inOutRequestService.inOutbound(componentId, quantity, userName, componentName, "零部件",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        int inbound = this.baseMapper.inbound(componentId, quantity);
        if (inbound <  0){
            throw new RuntimeException("入库失败");
        }
        return true;
    }

    /**
     * 获取零部件仓库统计信息
     */
    @Override
    public Object getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取所有零部件记录
        List<ComponentWarehouse> allComponents = this.list();
        
        // 总库存数量（所有零部件的库存总和）
        Long totalStock = allComponents.stream()
                .mapToLong(component -> component.getCurrentStock() != null ? component.getCurrentStock() : 0L)
                .sum();
        
        // 物料种类数（零部件总数）
        int totalMaterials = allComponents.size();
        
        // 低库存零部件数（currentStock <= fields2安全库存）
        long lowStock = allComponents.stream()
                .filter(component -> {
                    Integer currentStock = component.getCurrentStock();
                    String fields2 = component.getFields2(); // 安全库存
                    if (currentStock == null || fields2 == null || fields2.isEmpty()) {
                        return false;
                    }
                    try {
                        Integer safetyStock = Integer.parseInt(fields2);
                        return currentStock <= safetyStock;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .count();
        
        // 需要采购的零部件数（如果有needPurchase字段则使用，否则与低库存数相同）
        long needPurchase;
        try {
            needPurchase = allComponents.stream()
                    .filter(component -> {
                        Boolean needPurchaseFlag = component.getNeedPurchase();
                        return needPurchaseFlag != null && needPurchaseFlag;
                    })
                    .count();
        } catch (Exception e) {
            // 如果needPurchase字段不可用，则使用低库存数
            needPurchase = lowStock;
        }
        
        statistics.put("totalStock", totalStock);
        statistics.put("totalMaterials", totalMaterials);
        statistics.put("lowStock", lowStock);
        statistics.put("needPurchase", needPurchase);
        
        return statistics;
    }

    @Override
    public Page<ComponentWarehouseVO> selectComponentWarehousePage(Page<ComponentWarehouseVO> page, ComponentWarehouse componentWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder) {
        QueryWrapper<ComponentWarehouse> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(componentWarehouse.getComponentName()), "cw.component_name", componentWarehouse.getComponentName());
        wrapper.eq(StringUtils.isNotBlank(componentWarehouse.getFields3()), "cw.fields3", componentWarehouse.getFields3());
        
        // 当区域参数为空时，使用不关联区域的查询避免数据重复
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            wrapper.like(StringUtils.isNotBlank(zoneCode), "wz.zone_code", zoneCode);
            wrapper.like(StringUtils.isNotBlank(zoneName), "wz.zone_name", zoneName);
        }

        if (componentWarehouse.getNeedPurchase() != null) {
            wrapper.eq("cw.need_purchase", componentWarehouse.getNeedPurchase());
        }

        // 处理排序
        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            // 将驼峰转下划线
            String column = StringUtils.camelToUnderline(sortField);
            // 防范SQL注入，只允许对特定表的字段排序
            String sortColumn = "cw." + column;

            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortColumn);
            } else {
                wrapper.orderByDesc(sortColumn);
            }
        } else {
            // 默认排序
            wrapper.orderByDesc("cw.updated_time");
        }
        
        // 根据是否有区域条件选择不同的查询方法
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            return this.baseMapper.selectPageWithZone(page, wrapper);
        } else {
            // 使用不关联区域的查询方法，避免数据重复
            return this.baseMapper.selectPageWithoutZone(page, wrapper);
        }
    }
}

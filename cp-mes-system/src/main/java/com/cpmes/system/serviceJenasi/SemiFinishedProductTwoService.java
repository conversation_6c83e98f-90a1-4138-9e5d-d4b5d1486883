package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.SemiFinishedProductTwo;
import com.cpmes.system.vo.SemiFinishedProductTwoVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface SemiFinishedProductTwoService extends IService<SemiFinishedProductTwo> {
    /**
     * 分页查询二级半成品信息（包含区域）
     * @param page 分页对象
     * @param semiFinishedProductTwo 查询条件
     * @param zoneCode 区域代码
     * @param zoneName 区域名称
     * @param sortField 排序字段
     * @param sortOrder 排序顺序
     * @return
     */
    Page<SemiFinishedProductTwoVO> selectSemiFinishedProductTwoPage(Page<SemiFinishedProductTwoVO> page, SemiFinishedProductTwo semiFinishedProductTwo, String zoneCode, String zoneName, String sortField, String sortOrder);

    /**
     * 批量删除半成品
     * @param ids 二级半成品ID列表
     * @return 是否成功
     */
    boolean batchDeleteByIds(List<Integer> ids);

    /**
     * 二级半成品出库
     */
    boolean outbound(Integer semiProductTwoId,Integer quantity,String userName);
    /**
     * 二级半成品入库
     */
    boolean inbound(Integer semiProductTwoId,Integer quantity,String userName);

    /**
     * 获取二级半成品仓库统计信息
     * @return 统计数据
     */
    Object getStatistics();

    Page<SemiFinishedProductTwoVO> selectSemiFinishedProductTwoPage(Page<SemiFinishedProductTwoVO> page, SemiFinishedProductTwo semiFinishedProductTwo, String sortField, String sortOrder);
}

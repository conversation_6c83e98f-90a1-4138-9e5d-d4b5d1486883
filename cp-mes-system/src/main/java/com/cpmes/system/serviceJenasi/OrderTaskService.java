package com.cpmes.system.serviceJenasi;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.OrderTask;
import com.cpmes.system.entity.dto.orderTask.OrderTaskCreateRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskDetailQueryRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskQueryRequest;
import com.cpmes.system.entity.dto.orderTask.OrderTaskUpdateRequest;
import com.cpmes.system.entity.dto.orderTask.OrderWithTasksQueryRequest;
import com.cpmes.system.entity.vo.OrderTaskDetailVO;
import com.cpmes.system.entity.vo.OrderWithTasksVO;
import com.cpmes.system.entity.vo.GroupedOrderTaskVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_task(订单任务表)】的数据库操作Service
* @createDate 2025-06-21 11:08:59
*/
public interface OrderTaskService extends IService<OrderTask> {

    /**
     * 根据订单设置任务
     * @param orderTaskCreateRequest
     * @return
     */
    List<OrderTask> createOrderTask(OrderTaskCreateRequest orderTaskCreateRequest);

    /**
     * 分页获取订单任务列表
     */
    Page<OrderTask> orderTaskByPage(Page<OrderTask> page, OrderTaskQueryRequest orderTaskQueryRequest);

    /**
     * 获取查询条件
     */
    QueryWrapper<OrderTask> getQueryWrapper(OrderTaskQueryRequest orderTaskQueryRequest);

    /**
     * 修改任务状态
     */
    boolean updateTaskStatus(Long id, String taskStatus);

    /**
     * 修改任务级别
     */
    boolean updateTaskLevel(Long id, String taskLevel);

    /**
     * 设置任务延期
     */
    boolean setTaskDefer(Long id);

    /**
     * 修改订单任务
     */
    boolean updateOrderTask(OrderTaskUpdateRequest orderTaskUpdateRequest);

    /**
     * 分页查询详细任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<OrderTaskDetailVO> getOrderTaskDetailPage(Page<OrderTaskDetailVO> page, OrderTaskDetailQueryRequest queryRequest);

    /**
     * 分页查询分组后的详细任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<GroupedOrderTaskVO> getGroupedOrderTaskDetailPage(Page<GroupedOrderTaskVO> page, OrderTaskDetailQueryRequest queryRequest);

    /**
     * 根据订单ID获取订单及其关联的任务信息
     * @param orderId 订单ID
     * @return 订单及任务信息
     */
    OrderWithTasksVO getOrderWithTasks(Long orderId);

    /**
     * 分页查询订单及其关联的任务信息
     * @param page 分页对象
     * @return 分页结果
     */
    Page<OrderWithTasksVO> getOrdersWithTasksPage(Page<OrderWithTasksVO> page);

    /**
     * 带条件分页查询订单及其关联的任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<OrderWithTasksVO> getOrdersWithTasksPage(Page<OrderWithTasksVO> page, OrderWithTasksQueryRequest queryRequest);

    /**
     * 获取指定订单的任务列表
     * @param orderId 订单ID
     * @return 任务列表
     */
    List<OrderTask> getTasksByOrderId(Long orderId);

}

package com.cpmes.system.serviceJenasi.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.domain.vo.WarehouseZoneVo;
import com.cpmes.system.entity.PurchaseInbound;
import com.cpmes.system.entity.dto.purcgaseInbound.PurchaseInboundAddRequest;
import com.cpmes.system.entity.dto.purcgaseInbound.PurchaseInboundQueryRequest;
import com.cpmes.system.mapperJenasi.PurchaseInboundMapper;
import com.cpmes.system.service.IWarehouseZoneService;
import com.cpmes.system.serviceJenasi.PurchaseInboundService;
import com.cpmes.system.serviceJenasi.PurchaseOrderService;
import com.cpmes.system.service.IInventoryDetailService;
import com.cpmes.system.service.IBatchNumberGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【purchase_inbound(采购订单入库表)】的数据库操作Service实现
* @createDate 2025-06-17 09:23:30
*/
@Slf4j
@Service
@DS("slave")
public class PurchaseInboundServiceImpl extends ServiceImpl<PurchaseInboundMapper, PurchaseInbound>
    implements PurchaseInboundService {

    @Resource
    private IWarehouseZoneService warehouseZoneService;

    // 移除直接依赖，使用ApplicationContext获取Bean以避免循环依赖
    // @Resource
    // private PurchaseOrderService purchaseOrderService;

    @Resource
    private IInventoryDetailService inventoryDetailService;

    @Resource
    private IBatchNumberGeneratorService batchNumberGeneratorService;


    /**
     * 添加采购订单入库
     * @param request
     * @return
     */
    @Override
    public PurchaseInbound addPurchaseInbound(PurchaseInboundAddRequest request) {
        Long purchaseInboundId = null;;
        if (request.getId() != null){
            purchaseInboundId = request.getId();
        }
        if (StrUtil.isBlank(request.getPurchaseOrderNo())){
            throw new RuntimeException("参数错误");
        }
        //校验是否存在相同的单号
            PurchaseInbound oldPurchaseInBound = this.lambdaQuery().eq(PurchaseInbound::getPurchaseOrderNo, request.getPurchaseOrderNo()).one();
            if (oldPurchaseInBound != null){
                throw new RuntimeException("订单号已存在！");
            }
        PurchaseInbound purchaseInbound = new PurchaseInbound();
        if (purchaseInboundId != null){
            purchaseInbound.setUpdateTime(new Date());
        }
        purchaseInbound.setPurchaseOrderNo(request.getPurchaseOrderNo());
        purchaseInbound.setStatus(request.getStatus());
        purchaseInbound.setQrCode(request.getQrCode());
        purchaseInbound.setZoneCode(request.getZoneCode());
        purchaseInbound.setOperator(request.getOperator());
        purchaseInbound.setRemark(request.getRemark());
        purchaseInbound.setMaterialType(request.getMaterialType());
        purchaseInbound.setMaterialName(request.getMaterialName());

        // 处理批次号：如果请求中没有批次号，则生成新的批次号
        if (StrUtil.isBlank(request.getBatchNo())) {
            String batchNo = batchNumberGeneratorService.generateNextBatchNumber();
            purchaseInbound.setBatchNo(batchNo);
            log.info("为采购入库记录生成新批次号: purchaseOrderNo={}, batchNo={}",
                    request.getPurchaseOrderNo(), batchNo);
        } else {
            purchaseInbound.setBatchNo(request.getBatchNo());
        }

        // 设置板型类型
        purchaseInbound.setBoardType(request.getBoardType());
        boolean result = this.saveOrUpdate(purchaseInbound);
        if (!result){
            throw new RuntimeException("添加失败");
        }
        return this.getById(purchaseInbound.getId());
    }


    /**
     * 获取采购入库列表
     * @param page
     * @param request
     * @return
     */
    @Override
    public Page<PurchaseInbound> getPurchaseInboundList(Page<PurchaseInbound> page, PurchaseInboundQueryRequest request) {
        return this.page(page,this.getQueryWrapper(request));
    }


    /**
     * 获取查询条件
     * @param request
     * @return
     */
    @Override
    public QueryWrapper<PurchaseInbound> getQueryWrapper(PurchaseInboundQueryRequest request) {
        QueryWrapper<PurchaseInbound> queryWrapper = new QueryWrapper<>();
        String purchaseOrderNo = request.getPurchaseOrderNo();
        String operator = request.getOperator();
        Integer status = request.getStatus();
        String zoneCode = request.getZoneCode();
        String qrCode = request.getQrCode();
        queryWrapper.like(StringUtils.isNotBlank(purchaseOrderNo), "purchase_order_no", purchaseOrderNo);
        queryWrapper.like(StringUtils.isNotBlank(operator), "operator", operator);
        queryWrapper.eq(status != null, "status", status);
        queryWrapper.eq(StringUtils.isNotBlank(zoneCode), "zone_code", zoneCode);
        queryWrapper.like(StringUtils.isNotBlank(qrCode), "qr_code", qrCode);
        queryWrapper.eq("is_deleted", 0);
        return queryWrapper;
    }

    /**
     * 绑定采购单号和区域编码
     * 将已存在的采购单号与区域编码进行绑定（更新操作）
     * @param purchaseOrderNo 采购单号（数据库中已存在）
     * @param zoneCode 区域编码（需要绑定的新区域）
     * @param operator 操作人
     * @return 更新后的采购入库记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseInbound bindPurchaseZone(String purchaseOrderNo, String zoneCode, String operator) {
        try {
            log.info("开始绑定采购单号到区域: purchaseOrderNo={}, zoneCode={}, operator={}",
                    purchaseOrderNo, zoneCode, operator);

            // 参数校验
            validateBindParameters(purchaseOrderNo, zoneCode, operator);

            // 验证区域编码是否存在且启用
            validateZoneCode(zoneCode);

            // 查找已存在的采购入库记录
            PurchaseInbound existingRecord = this.lambdaQuery()
                    .eq(PurchaseInbound::getPurchaseOrderNo, purchaseOrderNo)
                    .eq(PurchaseInbound::getIsDeleted, 0)
                    .one();

            if (existingRecord == null) {
                throw new ServiceException("采购单号[" + purchaseOrderNo + "]不存在，无法进行绑定");
            }

            log.info("找到采购入库记录: id={}, 当前状态={}, 原区域编码={}",
                    existingRecord.getId(), existingRecord.getStatus(), existingRecord.getZoneCode());

            // 保存原有的二维码内容，确保不丢失
            String originalQrCode = existingRecord.getQrCode();

            // 更新区域编码、状态和操作人，同时保持二维码内容
            existingRecord.setZoneCode(zoneCode);
            existingRecord.setStatus(1); // 绑定成功后状态变为已入库
            existingRecord.setOperator(operator); // 设置操作人
            existingRecord.setUpdateTime(new Date());
            // 确保二维码内容不丢失
            if (StrUtil.isNotBlank(originalQrCode)) {
                existingRecord.setQrCode(originalQrCode);
            }

            // 执行更新操作
            boolean updateResult = this.updateById(existingRecord);
            if (!updateResult) {
                throw new ServiceException("绑定失败，更新数据库记录失败");
            }

            log.info("采购单号绑定区域成功: purchaseOrderNo={}, zoneCode={}, 状态已更新为已入库",
                    purchaseOrderNo, zoneCode);

            // 返回更新后的记录
            return this.getById(existingRecord.getId());

        } catch (ServiceException e) {
            log.error("绑定采购单号到区域失败: purchaseOrderNo={}, zoneCode={}, 错误信息={}",
                    purchaseOrderNo, zoneCode, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("绑定采购单号到区域发生未知异常: purchaseOrderNo={}, zoneCode={}",
                    purchaseOrderNo, zoneCode, e);
            throw new ServiceException("绑定失败: " + e.getMessage());
        }
    }

    /**
     * 验证绑定参数
     * @param purchaseOrderNo 采购单号
     * @param zoneCode 区域编码
     * @param operator 操作人
     */
    private void validateBindParameters(String purchaseOrderNo, String zoneCode, String operator) {
        if (StrUtil.isBlank(purchaseOrderNo)) {
            throw new ServiceException("采购单号不能为空");
        }
        if (StrUtil.isBlank(zoneCode)) {
            throw new ServiceException("区域编码不能为空");
        }
        if (StrUtil.isBlank(operator)) {
            throw new ServiceException("操作人不能为空");
        }

        // 验证采购单号格式（可根据实际业务规则调整）
        if (purchaseOrderNo.length() > 50) {
            throw new ServiceException("采购单号长度不能超过50个字符");
        }

        // 验证区域编码格式
        if (zoneCode.length() > 20) {
            throw new ServiceException("区域编码长度不能超过20个字符");
        }

        // 验证操作人格式
        if (operator.length() > 50) {
            throw new ServiceException("操作人名称长度不能超过50个字符");
        }
    }

    /**
     * 验证区域编码是否存在且启用
     * @param zoneCode 区域编码
     */
    private void validateZoneCode(String zoneCode) {
        try {
            log.debug("验证区域编码: {}", zoneCode);

            WarehouseZoneVo zoneInfo = warehouseZoneService.queryByZoneCode(zoneCode);
            if (zoneInfo == null) {
                throw new ServiceException("区域编码[" + zoneCode + "]不存在");
            }

            // 验证区域状态是否启用
            if (!"1".equals(zoneInfo.getStatus())) {
                throw new ServiceException("区域编码[" + zoneCode + "]已禁用，无法进行绑定操作");
            }

            log.debug("区域编码验证通过: zoneCode={}, zoneName={}, status={}",
                    zoneCode, zoneInfo.getZoneName(), zoneInfo.getStatus());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证区域编码时发生异常: zoneCode={}", zoneCode, e);
            throw new ServiceException("验证区域编码失败: " + e.getMessage());
        }
    }

    /**
     * 更新采购入库记录的二维码内容
     * 在打印二维码后调用此方法保存二维码内容到数据库
     * @param purchaseOrderNo 采购单号（数据库中已存在）
     * @param qrCode 二维码内容
     * @param operator 操作人
     * @return 更新后的采购入库记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseInbound updateQrCode(String purchaseOrderNo, String qrCode, String operator) {
        try {
            log.info("开始更新采购入库记录的二维码内容: purchaseOrderNo={}, operator={}",
                    purchaseOrderNo, operator);

            // 参数校验
            validateQrCodeParameters(purchaseOrderNo, qrCode, operator);

            // 查找已存在的采购入库记录
            PurchaseInbound existingRecord = this.lambdaQuery()
                    .eq(PurchaseInbound::getPurchaseOrderNo, purchaseOrderNo)
                    .eq(PurchaseInbound::getIsDeleted, 0)
                    .one();

            if (existingRecord == null) {
                throw new ServiceException("采购单号[" + purchaseOrderNo + "]不存在，无法更新二维码内容");
            }

            log.info("找到采购入库记录: id={}, 当前状态={}, 原二维码内容={}",
                    existingRecord.getId(), existingRecord.getStatus(),
                    existingRecord.getQrCode() != null ? "已存在" : "空");

            // 更新二维码内容和操作人
            existingRecord.setQrCode(qrCode);
            existingRecord.setOperator(operator);
            existingRecord.setUpdateTime(new Date());

            // 执行更新操作
            boolean updateResult = this.updateById(existingRecord);
            if (!updateResult) {
                throw new ServiceException("更新二维码内容失败，数据库更新失败");
            }

            log.info("二维码内容更新成功: purchaseOrderNo={}, 二维码长度={}",
                    purchaseOrderNo, qrCode.length());

            // 返回更新后的记录
            return this.getById(existingRecord.getId());

        } catch (ServiceException e) {
            log.error("更新二维码内容失败: purchaseOrderNo={}, 错误信息={}",
                    purchaseOrderNo, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新二维码内容发生未知异常: purchaseOrderNo={}", purchaseOrderNo, e);
            throw new ServiceException("更新二维码内容失败: " + e.getMessage());
        }
    }

    /**
     * 验证二维码更新参数
     * @param purchaseOrderNo 采购单号
     * @param qrCode 二维码内容
     * @param operator 操作人
     */
    private void validateQrCodeParameters(String purchaseOrderNo, String qrCode, String operator) {
        if (StrUtil.isBlank(purchaseOrderNo)) {
            throw new ServiceException("采购单号不能为空");
        }
        if (StrUtil.isBlank(qrCode)) {
            throw new ServiceException("二维码内容不能为空");
        }
        if (StrUtil.isBlank(operator)) {
            throw new ServiceException("操作人不能为空");
        }

        // 验证采购单号格式
        if (purchaseOrderNo.length() > 50) {
            throw new ServiceException("采购单号长度不能超过50个字符");
        }

        // 验证二维码内容长度（根据数据库字段长度限制）
        if (qrCode.length() > 500) {
            throw new ServiceException("二维码内容长度不能超过500个字符");
        }

        // 验证操作人格式
        if (operator.length() > 50) {
            throw new ServiceException("操作人名称长度不能超过50个字符");
        }
    }

    /**
     * 采购入库并自动增加库存
     * 集成区域绑定和库存入库的完整业务流程
     * @param purchaseOrderNo 采购单号
     * @param zoneCode 区域编码
     * @param operator 操作人
     * @return 操作结果，包含采购入库记录和库存操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> purchaseInboundWithInventory(String purchaseOrderNo, String zoneCode, String operator) {
        try {
            log.info("开始执行采购入库并自动增加库存: purchaseOrderNo={}, zoneCode={}, operator={}",
                    purchaseOrderNo, zoneCode, operator);

            Map<String, Object> result = new HashMap<>();

            // 第一步：执行区域绑定
            log.info("步骤1：执行区域绑定");
            PurchaseInbound purchaseInbound = this.bindPurchaseZone(purchaseOrderNo, zoneCode, operator);
            result.put("purchaseInbound", purchaseInbound);

            // 第二步：获取采购订单信息
            log.info("步骤2：获取采购订单信息");
            com.cpmes.system.entity.vo.PurchaseOrderVO purchaseOrder = getPurchaseOrderByNo(purchaseOrderNo);
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单[" + purchaseOrderNo + "]不存在");
            }

            // 第三步：构建库存入库参数并执行库存入库
            log.info("步骤3：执行库存入库操作");
            Map<String, Object> inventoryResult = executeInventoryInbound(purchaseOrder, zoneCode, operator);
            result.put("inventoryResult", inventoryResult);

            log.info("采购入库并自动增加库存完成: purchaseOrderNo={}, 库存明细ID={}",
                    purchaseOrderNo, inventoryResult.get("detailId"));

            return result;

        } catch (Exception e) {
            log.error("采购入库并自动增加库存失败: purchaseOrderNo={}, zoneCode={}",
                    purchaseOrderNo, zoneCode, e);
            throw new ServiceException("采购入库失败: " + e.getMessage());
        }
    }

    /**
     * 根据采购单号获取采购订单信息
     * @param purchaseOrderNo 采购单号
     * @return 采购订单信息
     */
    private com.cpmes.system.entity.vo.PurchaseOrderVO getPurchaseOrderByNo(String purchaseOrderNo) {
        try {
            // 使用ApplicationContext获取Bean以避免循环依赖
            PurchaseOrderService purchaseOrderService =
                com.cpmes.common.utils.spring.SpringUtils.getBean(PurchaseOrderService.class);

            // 查询采购订单详情
            java.util.List<com.cpmes.system.entity.vo.PurchaseOrderVO> orders =
                purchaseOrderService.getPurchaseOrderDetailByCondition(purchaseOrderNo, null, null, null, null, null);

            if (orders != null && !orders.isEmpty()) {
                return orders.get(0);
            }
            return null;
        } catch (Exception e) {
            log.error("获取采购订单信息失败: purchaseOrderNo={}", purchaseOrderNo, e);
            throw new ServiceException("获取采购订单信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行库存入库操作
     * @param purchaseOrder 采购订单信息
     * @param zoneCode 区域编码
     * @param operator 操作人
     * @return 库存操作结果
     */
    private Map<String, Object> executeInventoryInbound(com.cpmes.system.entity.vo.PurchaseOrderVO purchaseOrder,
                                                       String zoneCode, String operator) {
        try {
            // 构建库存入库参数
            Long detailId = null; // 新建库存明细
            Integer quantity = purchaseOrder.getQuantity().intValue(); // 入库数量
            String reason = "采购入库操作"; // 入库原因
            String sourceDocument = purchaseOrder.getPurchaseNo(); // 来源单据号
            String remark = "采购入库：" + purchaseOrder.getPurchaseNo(); // 备注
            String itemCode = purchaseOrder.getItemId(); // 物料编码
            String itemName = purchaseOrder.getItemName(); // 物料名称
            String unitOfMeasure = purchaseOrder.getUnit(); // 计量单位

            // 从采购入库记录获取已生成的批次号
            String lotNumber = null;
            PurchaseInbound inboundRecord = this.getByPurchaseOrderNo(purchaseOrder.getPurchaseNo());
            if (inboundRecord != null && StrUtil.isNotBlank(inboundRecord.getBatchNo())) {
                lotNumber = inboundRecord.getBatchNo();
                log.info("使用采购入库记录中的批次号: purchaseNo={}, batchNo={}",
                        purchaseOrder.getPurchaseNo(), lotNumber);
            } else {
                // 如果没有找到批次号，生成新的（兜底逻辑）
                lotNumber = batchNumberGeneratorService.generateNextBatchNumber();
                log.warn("未找到采购入库记录中的批次号，生成新批次号: purchaseNo={}, batchNo={}",
                        purchaseOrder.getPurchaseNo(), lotNumber);
            }

            // 新增字段映射
            String boardType = purchaseOrder.getBoardType(); // 上下板类型
            String materialType = purchaseOrder.getItemType(); // 物料类型
            Integer supplierId = purchaseOrder.getSupplierId() != null ? purchaseOrder.getSupplierId().intValue() : null; // 供应商ID
            String supplierName = purchaseOrder.getSupplierName(); // 供应商名称

            log.info("构建完整库存入库参数: itemCode={}, itemName={}, quantity={}, zoneCode={}, boardType={}, materialType={}, supplierId={}, supplierName={}",
                    itemCode, itemName, quantity, zoneCode, boardType, materialType, supplierId, supplierName);

            // 调用完整扩展库存入库接口
            Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtendedFull(
                detailId, quantity, "increase", reason, remark, "inbound", sourceDocument,
                zoneCode, itemCode, itemName, unitOfMeasure, lotNumber,
                boardType, materialType, supplierId, supplierName,null
            );

            log.info("完整库存入库操作完成: result={}", result);
            return result;

        } catch (Exception e) {
            log.error("执行库存入库操作失败: purchaseOrderNo={}", purchaseOrder.getPurchaseNo(), e);
            throw new ServiceException("库存入库操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据采购单号获取采购入库记录
     * 用于打印二维码时获取批次号等信息
     * @param purchaseOrderNo 采购单号
     * @return 采购入库记录，如果不存在返回null
     */
    @Override
    public PurchaseInbound getByPurchaseOrderNo(String purchaseOrderNo) {
        if (StrUtil.isBlank(purchaseOrderNo)) {
            return null;
        }

        try {
            // 查询采购入库记录
            PurchaseInbound result = this.lambdaQuery()
                    .eq(PurchaseInbound::getPurchaseOrderNo, purchaseOrderNo)
                    .eq(PurchaseInbound::getIsDeleted, 0)
                    .orderByDesc(PurchaseInbound::getCreateTime)
                    .one();

            log.info("根据采购单号查询采购入库记录: purchaseOrderNo={}, found={}",
                    purchaseOrderNo, result != null);

            return result;
        } catch (Exception e) {
            log.error("根据采购单号查询采购入库记录失败: purchaseOrderNo={}", purchaseOrderNo, e);
            return null;
        }
    }
}





package com.cpmes.system.serviceJenasi.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.Item;
import com.cpmes.system.entity.dto.item.ItemAddRequest;
import com.cpmes.system.entity.dto.item.ItemQueryRequest;
import com.cpmes.system.mapperJenasi.ItemMapper;
import com.cpmes.system.serviceJenasi.ItemService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【item(物品表)】的数据库操作Service实现
* @createDate 2025-06-11 10:33:37
*/
@Service
@DS("slave")
public class ItemServiceImpl extends ServiceImpl<ItemMapper, Item>
    implements ItemService {

    /**
     * 获取查询条件
     * @param itemQueryRequest
     * @return
     */
    @Override
    public QueryWrapper<Item> getQueryWrapper(ItemQueryRequest itemQueryRequest) {
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        String itemName = itemQueryRequest.getItemName();
        String brand = itemQueryRequest.getBrand();
        String specification = itemQueryRequest.getSpecification();
        String category = itemQueryRequest.getCategory();
        String unit = itemQueryRequest.getUnit();
        queryWrapper.like(StrUtil.isNotBlank(itemName),"item_name", itemName);
        queryWrapper.like(StrUtil.isNotBlank(brand),"brand", brand);
        queryWrapper.like(StrUtil.isNotBlank(specification),"specification", specification);
        queryWrapper.like(StrUtil.isNotBlank(category),"category", category);
        queryWrapper.like(StrUtil.isNotBlank(unit),"unit", unit);
        // 添加逻辑删除过滤
        queryWrapper.eq("is_deleted", 0);
        return queryWrapper;
    }

    /**
     * 新增物品
     * @param itemAddRequest
     * @return
     */
    @Override
    public Item addItem(ItemAddRequest itemAddRequest) {
        //简单校验
        if(itemAddRequest == null){
            throw new RuntimeException("新增物品参数错误");
        }
        //查看是否存在相同的数据,名称和类型同时存在则不允许新增
        String itemName = itemAddRequest.getItemName();
        String category = itemAddRequest.getCategory();
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(itemName),"item_name", itemName);
        queryWrapper.eq(StrUtil.isNotBlank(category),"category", category);
        Item existingItem = this.getOne(queryWrapper);
        if (existingItem != null) {
            throw new RuntimeException("该名称和类型的物品已存在，无法重复新增");
        }
        //通过id判断是新增还是修改
        Long itemId = null;
        if (itemAddRequest.getId() != null){
            itemId = itemAddRequest.getId();
        }
        Item item = new Item();
        item.setItemName(itemAddRequest.getItemName());
        item.setSpecification(itemAddRequest.getSpecification());
        item.setCategory(itemAddRequest.getCategory());
        item.setBrand(itemAddRequest.getBrand());
        item.setUnit(itemAddRequest.getUnit());
        if (itemId != null){
            item.setId(itemId);
        }
        boolean result = this.saveOrUpdate(item);
        if (!result){
           throw new RuntimeException("数据操作失败");
        }
        return item;
    }

    /**
     * 分页获取物品列表
     * @param page
     * @param itemQueryRequest
     * @return
     */
    @Override
    public Page<Item> getItemList(Page<Item> page, ItemQueryRequest itemQueryRequest) {
        return this.page(page, this.getQueryWrapper(itemQueryRequest));
    }

    /**
     * 按条件获取物品列表（不分页）
     * @param itemQueryRequest
     * @return
     */
    @Override
    public List<Item> getItemListByCondition(ItemQueryRequest itemQueryRequest) {
        return this.list(this.getQueryWrapper(itemQueryRequest));
    }
}





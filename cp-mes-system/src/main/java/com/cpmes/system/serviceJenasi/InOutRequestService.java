package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.InOutRequest;
import com.cpmes.system.entity.vo.InOutCensusVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 出入库申请记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface InOutRequestService extends IService<InOutRequest> {

    /**
     * 分页获取查询记录列表
     * @param page
     * @param queryWrapper
     * @return
     */
    Page<InOutRequest> page(Page<InOutRequest> page, QueryWrapper<InOutRequest> queryWrapper);


    /**
     * 新增出库记录
     */
    boolean inOutbound(Integer materialId, Integer quantity, String userName,String materialName,String materialType,Integer type);

    /**
     * 按月统计入出库
     */
    List<InOutCensusVO > getCurrentMonthAnalysis(int year, int month);

    /**
     * 按月统计入出库分页
     */
    Page<InOutCensusVO> getCurrentMonthAnalysis(Integer year, Integer month, Integer pageNum, Integer pageSize,String materialName,String materialType);
}

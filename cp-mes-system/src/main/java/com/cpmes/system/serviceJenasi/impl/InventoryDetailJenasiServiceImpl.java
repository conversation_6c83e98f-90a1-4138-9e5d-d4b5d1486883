package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.InventoryDetailJenasi;
import com.cpmes.system.mapperJenasi.InventoryDetailJenasiMapper;
import com.cpmes.system.serviceJenasi.InventoryDetailJenasiService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【inventory_detail(库存明细表)】的数据库操作Service实现
* @createDate 2025-06-18 16:21:46
*/
@Service
@DS("slave")
public class InventoryDetailJenasiServiceImpl extends ServiceImpl<InventoryDetailJenasiMapper, InventoryDetailJenasi>
    implements InventoryDetailJenasiService {

    /**
     * 搜索库存
     * @param materialType
     * @param materialName
     * @return
     */
    @Override
    public List<InventoryDetailJenasi> searchInventory(String materialType, String materialName) {
        return this.baseMapper.queryByTypeAndName(materialType, materialName);
    }

    /**
     * 根据物品编号查询库存
     * @param materialId
     * @return
     */
    @Override
    public InventoryDetailJenasi getInventoryByItemId(String materialId) {
        if (materialId == null || materialId.trim().isEmpty()) {
            throw new IllegalArgumentException("物品编号不能为空");
        }

        // 使用 MyBatis-Plus 提供的 BaseMapper 查询方式
        QueryWrapper<InventoryDetailJenasi> wrapper = new QueryWrapper<>();
        wrapper.eq("material_id", materialId);
        return this.baseMapper.selectOne(wrapper);
    }
}





package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.Item;
import com.cpmes.system.entity.dto.item.ItemAddRequest;
import com.cpmes.system.entity.dto.item.ItemQueryRequest;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【item(物品表)】的数据库操作Service
* @createDate 2025-06-11 10:33:37
*/
public interface ItemService extends IService<Item> {

    /**
     * 获取查询条件
     */
   QueryWrapper<Item> getQueryWrapper(ItemQueryRequest  itemQueryRequest);

    /**
     * 新增物品
     */
    Item addItem(ItemAddRequest  itemAddRequest);

    /**
     * 分页获取物品列表
     * @param page
     * @param itemQueryRequest
     * @return
     */
    Page<Item> getItemList(Page<Item> page, ItemQueryRequest itemQueryRequest);

    /**
     * 按条件获取物品列表（不分页）
     * @param itemQueryRequest
     * @return
     */
    List<Item> getItemListByCondition(ItemQueryRequest itemQueryRequest);

    /**
     * 数据导出
     */
    //void exportItem(ItemQueryRequest itemQueryRequest);
}

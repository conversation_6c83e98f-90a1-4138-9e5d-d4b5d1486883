package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.Orders;
import com.cpmes.system.entity.dto.orders.AssigneeOrderCountDTO;
import com.cpmes.system.entity.dto.orders.OrderDetailQueryRequest;
import com.cpmes.system.entity.dto.orders.OrderDueCountDTO;
import com.cpmes.system.entity.dto.orders.OrderStatusByDayDTO;
import com.cpmes.system.entity.vo.OrderDetailVO;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【orders(工单表，记录生产任务的基本信息)】的数据库操作Service
* @createDate 2025-06-30 08:33:27
*/
public interface OrdersService extends IService<Orders> {

    /**
     * 创建工单
     * @param orderCode 工单编号
     * @param orderType 工单类型
     * @return 创建的工单
     */
    Orders createOrder(String orderCode, String orderType, Date expectedTime);

    /**
     * 工单状态统计
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderStatusByDayDTO> getStatusStatsByDay(LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 查询某个 assignee 指派人对应的 NEW 和 COMPLETED 工单数(已使用)
     */
    AssigneeOrderCountDTO getOrderCountByAssignee(String assignee);

    /**
     * 按月获取指定 assignee 指派人对应的工单数(已使用)
     * @param assignee
     * @return
     */
    List<AssigneeOrderCountDTO> getMonthlyOrderStatsByAssignee(String assignee);

    /**
     * 工单下发
     * @param id 工单ID
     * @return 更新后的工单(已使用)
     */
    Orders issueOrder(Long id);

    /**
     * 更新工单状态
     * @param id 工单ID
     * @param status 新状态
     * @return 更新后的工单(已使用)
     */
    Orders updateOrderStatus(Long id, String status);

    /**
     * 统计工单逾期和即将到期的工单数量
     * @return 工单逾期和即将到期的工单数量(已使用)
     */
    OrderDueCountDTO countDueAndUpcoming();

    /**
     * 更新工单类型
     * @param id 工单ID
     * @param orderType 工单类型
     * @return 更新后的工单  (已使用)
     */
    Orders updateOrderType(Long id, String orderType);

    /**
     * 根据工单编号查询工单
     * @param orderCode 工单编号
     * @return 工单信息
     */
    Orders getByOrderCode(String orderCode);

    /**
     * 分页查询工单
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param orderType 工单类型（可选）
     * @param status 工单状态（可选）
     * @param orderCode 工单编号（模糊查询，可选）
     * @return 分页结果
     */
    IPage<Orders> queryOrdersPage(int pageNum, int pageSize, String orderType, String status, String orderCode);

    /**
     * 开始执行工单
     * @param id 工单ID
     * @return 更新后的工单
     */
    Orders startOrder(Long id);

    /**
     * 完成工单
     * @param id 工单ID
     * @return 更新后的工单
     */
    Orders completeOrder(Long id);

    /**
     * 批量删除工单（软删除）
     * @param ids 工单ID列表
     * @return 是否成功
     */
    boolean batchDeleteOrders(List<Long> ids);

    /**
     * 根据状态统计工单数量
     * @param status 工单状态
     * @return 统计数量
     */
    long countByStatus(String status);

    /**
     * 分页查询工单详细信息
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param queryRequest 查询条件
     * @return 工单详细信息分页结果
     */
    Page<OrderDetailVO> getOrderDetailPage(int pageNum, int pageSize, OrderDetailQueryRequest queryRequest);

    /**
     * 根据工单ID获取工单详细信息
     * @param orderId 工单ID
     * @return 工单详细信息
     */
    OrderDetailVO getOrderDetailById(Long orderId);
}

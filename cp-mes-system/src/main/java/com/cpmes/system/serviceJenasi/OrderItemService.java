package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.OrderItem;
import com.cpmes.system.entity.dto.orderItem.OrderItemCreateRequest;
import com.cpmes.system.entity.dto.orderItem.ProductStyleCountDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_item(订单明细表)】的数据库操作Service
* @createDate 2025-06-18 15:28:44
*/
public interface OrderItemService extends IService<OrderItem> {

    /**
     * 批量创建订单明细
     * @param request 创建请求
     * @return 创建后的订单明细列表
     */
    List<OrderItem> createOrderItems(OrderItemCreateRequest request);


    /**
     * 统计工单中产品品款式的数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果列表
     */
    List<ProductStyleCountDTO> countByProductAndStyle(String startTime, String endTime);
}

package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.RawMaterialWarehouse;
import com.cpmes.system.vo.RawMaterialWarehouseVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface RawMaterialWarehouseService extends IService<RawMaterialWarehouse> {
    /**
     * 分页查询原料信息（包含区域）
     * @param page 分页对象
     * @param rawMaterialWarehouse 查询条件
     * @param zoneCode 区域代码
     * @param zoneName 区域名称
     * @param sortField 排序字段
     * @param sortOrder 排序顺序
     * @return
     */
    Page<RawMaterialWarehouseVO> selectRawMaterialWarehousePage(Page<RawMaterialWarehouseVO> page, RawMaterialWarehouse rawMaterialWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder);

    /**
     * 批量删除原材料
     * @param ids 原材料ID列表
     * @return 是否成功
     */
    boolean batchDeleteByIds(List<Integer> ids);

    /**
     * 物料出库
     * @param materialId
     * @param quantity
     * @param userName
     * @return
     */
    boolean outbound(Integer materialId, Integer quantity, String userName);

    /**
     * 物料入库
     * @param materialId
     * @param quantity
     * @param userName
     * @return
     */
    boolean inbound(Integer materialId, Integer quantity, String userName);

    /**
     * 获取原料仓库统计信息
     * @return 统计数据
     */
    Object getStatistics();

    /**
     * 根据物料编码处理原料仓库记录
     * 存在则检查名称是否相同，不同则更新；不存在则创建新记录
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @return 处理结果（true=成功，false=失败）
     */
    boolean processRawMaterialByCode(String materialCode, String materialName);

    /**
     * 根据物料编码处理原料仓库记录（包含板类型信息）
     * 存在则检查名称和板类型是否相同，不同则更新；不存在则创建新记录
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param boardType 上下板类型
     * @return 处理结果（true=成功，false=失败）
     */
    boolean processRawMaterialByCode(String materialCode, String materialName, String boardType);

    /**
     * 根据物料编码处理原料仓库记录（包含单位信息）
     * 存在则检查名称和单位是否相同，不同则更新；不存在则创建新记录
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param unit 原料单位
     * @return 处理结果（true=成功，false=失败）
     */
    boolean processRawMaterialByCodeWithUnit(String materialCode, String materialName, String unit);

    /**
     * 根据物料编码处理原料仓库记录（包含单位和板型信息）
     * 存在则检查名称、单位和板型是否相同，不同则更新；不存在则创建新记录
     *
     * @param materialCode 物料编码
     * @param materialName 物料名称
     * @param unit 原料单位
     * @param boardType 板型信息
     * @return 处理结果（true=成功，false=失败）
     */
    boolean processRawMaterialByCodeWithUnitAndBoardType(String materialCode, String materialName, String unit, String boardType);

    /**
     * 根据物料名称更新原料仓库记录的产品编码
     * 查找所有名称匹配的原料记录，并更新其产品编码字段
     *
     * @param materialName 物料名称
     * @param productNumber 产品编码
     * @return 处理结果（true=成功，false=失败）
     */
    boolean updateProductNumberByMaterialName(String materialName, String productNumber);
}

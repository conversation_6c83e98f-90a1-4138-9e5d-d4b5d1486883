package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.SemiFinishedProductTwo;
import com.cpmes.system.mapperJenasi.SemiFinishedProductTwoMapper;
import com.cpmes.system.serviceJenasi.InOutRequestService;
import com.cpmes.system.serviceJenasi.SemiFinishedProductTwoService;
import com.cpmes.system.vo.SemiFinishedProductTwoVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS("slave")
public class SemiFinishedProductTwoServiceImpl extends ServiceImpl<SemiFinishedProductTwoMapper, SemiFinishedProductTwo> implements SemiFinishedProductTwoService {

    @Resource
    @Lazy
    private InOutRequestService inOutRequestService;

    @Override
    public Page<SemiFinishedProductTwoVO> selectSemiFinishedProductTwoPage(Page<SemiFinishedProductTwoVO> page, SemiFinishedProductTwo semiFinishedProductTwo, String zoneCode, String zoneName, String sortField, String sortOrder) {
        QueryWrapper<SemiFinishedProductTwo> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(semiFinishedProductTwo.getSemiProductTwoName()), "sfp2.semi_product_two_name", semiFinishedProductTwo.getSemiProductTwoName());
        wrapper.eq(StringUtils.isNotBlank(semiFinishedProductTwo.getFields3()), "sfp2.fields3", semiFinishedProductTwo.getFields3());
        
        // 当区域参数为空时，使用不关联区域的查询避免数据重复
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            wrapper.like(StringUtils.isNotBlank(zoneCode), "wz.zone_code", zoneCode);
            wrapper.like(StringUtils.isNotBlank(zoneName), "wz.zone_name", zoneName);
        }

        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            String column = StringUtils.camelToUnderline(sortField);
            String sortColumn = "sfp2." + column;

            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortColumn);
            } else {
                wrapper.orderByDesc(sortColumn);
            }
        } else {
            wrapper.orderByDesc("sfp2.updated_time");
        }

        // 根据是否有区域条件选择不同的查询方法
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            return this.baseMapper.selectPageWithZone(page, wrapper);
        } else {
            // 使用不关联区域的查询方法，避免数据重复
            return this.baseMapper.selectPageWithoutZone(page, wrapper);
        }
    }

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        List<Integer> existingIds = this.lambdaQuery()
                .in(SemiFinishedProductTwo::getSemiProductTwoId, ids)
                .list()
                .stream()
                .map(SemiFinishedProductTwo::getSemiProductTwoId)
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false;
        }

        return this.removeByIds(existingIds);
    }

    @Override
    public boolean outbound(Integer semiProductTwoId, Integer quantity, String userName) {
        SemiFinishedProductTwo semiFinishedProductTwo = this.getById(semiProductTwoId);
        if (semiFinishedProductTwo == null){
            throw new RuntimeException("未找到该半成品");
        }
        int outbound = this.baseMapper.outbound(semiProductTwoId, quantity);
        if (outbound <  0){
            throw new RuntimeException("出库失败");
        }
        String semiProductTwoName = semiFinishedProductTwo.getSemiProductTwoName();
        Integer type = 1;
        boolean result = inOutRequestService.inOutbound(semiProductTwoId, quantity, userName, semiProductTwoName, "二级半成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    @Override
    public boolean inbound(Integer semiProductTwoId, Integer quantity, String userName) {
        SemiFinishedProductTwo semiFinishedProductTwo = this.getById(semiProductTwoId);
        if (semiFinishedProductTwo == null){
            throw new RuntimeException("未找到该半成品");
        }
        int inbound = this.baseMapper.inbound(semiProductTwoId, quantity);
        if (inbound <  0){
            throw new RuntimeException("入库失败");
        }
        String semiProductTwoName = semiFinishedProductTwo.getSemiProductTwoName();
        Integer type = 0;
        boolean result = inOutRequestService.inOutbound(semiProductTwoId, quantity, userName, semiProductTwoName, "二级半成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    @Override
    public Object getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        List<SemiFinishedProductTwo> allMaterials = this.list();
        Long totalStock = allMaterials.stream()
                .mapToLong(material -> material.getCurrentStock() != null ? material.getCurrentStock() : 0L)
                .sum();
        int totalMaterials = allMaterials.size();
        long lowStock = allMaterials.stream()
                .filter(material -> {
                    Integer currentStock = material.getCurrentStock();
                    String fields2 = material.getFields2();
                    if (currentStock == null || fields2 == null || fields2.isEmpty()) {
                        return false;
                    }
                    try {
                        Integer safetyStock = Integer.parseInt(fields2);
                        return currentStock <= safetyStock;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .count();
        long needPurchase = lowStock;
        statistics.put("totalStock", totalStock);
        statistics.put("totalMaterials", totalMaterials);
        statistics.put("lowStock", lowStock);
        statistics.put("needPurchase", needPurchase);
        return statistics;
    }

    @Override
    public Page<SemiFinishedProductTwoVO> selectSemiFinishedProductTwoPage(Page<SemiFinishedProductTwoVO> page, SemiFinishedProductTwo semiFinishedProductTwo, String sortField, String sortOrder) {
        QueryWrapper<SemiFinishedProductTwo> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(semiFinishedProductTwo.getSemiProductTwoName()), "sfp2.semi_product_two_name", semiFinishedProductTwo.getSemiProductTwoName());
        wrapper.eq(StringUtils.isNotBlank(semiFinishedProductTwo.getFields3()), "sfp2.fields3", semiFinishedProductTwo.getFields3());

        // 处理排序
        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            String column = StringUtils.camelToUnderline(sortField);
            String sortColumn = "sfp2." + column;

            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortColumn);
            } else {
                wrapper.orderByDesc(sortColumn);
            }
        } else {
            // 默认排序
            wrapper.orderByDesc("sfp2.updated_time");
        }

        // 使用不关联区域的查询方法，避免数据重复
        return this.baseMapper.selectPageWithoutZone(page, wrapper);
    }
}

package com.cpmes.system.serviceJenasi.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.entity.OrderTask;
import com.cpmes.system.entity.Orders;
import com.cpmes.system.entity.StepTask;
import com.cpmes.system.entity.dto.orders.AssigneeOrderCountDTO;
import com.cpmes.system.entity.dto.orders.OrderDetailQueryRequest;
import com.cpmes.system.entity.dto.orders.OrderDueCountDTO;
import com.cpmes.system.entity.dto.orders.OrderStatusByDayDTO;
import com.cpmes.system.entity.vo.OrderDetailRawVO;
import com.cpmes.system.entity.vo.OrderDetailVO;
import com.cpmes.system.mapperJenasi.OrderTaskMapper;
import com.cpmes.system.mapperJenasi.OrdersMapper;
import com.cpmes.system.mapperJenasi.StepTaskMapper;
import com.cpmes.system.serviceJenasi.OrderTaskService;
import com.cpmes.system.serviceJenasi.OrdersService;
import com.cpmes.system.serviceJenasi.StepTaskService;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【orders(工单表，记录生产任务的基本信息)】的数据库操作Service实现
 * @createDate 2025-06-18 15:26:58
 */
@Service
@DS("slave")
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements OrdersService {

    @Resource
    private StepTaskMapper stepTaskMapper;

    @Resource
    private OrderTaskMapper orderTaskMapper;

    // 工单类型常量
    private static final String ORDER_TYPE_NORMAL = "NORMAL";
    private static final String ORDER_TYPE_URGENT = "URGENT";

    // 工单状态常量
    private static final String STATUS_NEW = "NEW";
    private static final String STATUS_IN_PROGRESS = "IN_PROGRESS";
    private static final String STATUS_COMPLETED = "COMPLETED";
    private static final String STATUS_PAUSED = "PAUSED";

    // 任务下发状态常量
    private static final String STATUS_NOT_DISPATCHED = "NOT_DISPATCHED";
    private static final String STATUS_DISPATCHED = "DISPATCHED";

    /**
     * 创建工单
     * @param orderCode 工单编号
     * @param orderType 工单类型
     * @return 创建的工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Orders createOrder(String orderCode, String orderType, Date expectedTime) {
        //去除所有空格
        String orderCodeTrim = orderCode.replaceAll("\\s+", "");
        //String orderCodeTrim = StringUtils.trim(orderCode);
        // 参数校验
        validateCreateOrderParams(orderCodeTrim, orderType);
        // 检查工单编号是否已存在
        if (existsByOrderCode(orderCodeTrim)) {
            throw new ServiceException("工单编号已存在：" + orderCode);
        }
        // 创建工单
        Orders order = new Orders();
        order.setOrderCode(orderCode);
        order.setOrderType(orderType);
        order.setExpectedTime(expectedTime);
        order.setStatus(STATUS_NEW);
        order.setDispatchStatus(STATUS_NOT_DISPATCHED);
        order.setCreatedTime(new Date());
        order.setIsDeleted(0);
        // 保存工单
        boolean success = this.save(order);
        if (!success) {
            throw new ServiceException("工单创建失败");
        }
        return order;
    }

    /**
     * 按天获取工单状态统计
     */
    @Override
    public List<OrderStatusByDayDTO> getStatusStatsByDay(LocalDateTime startTime, LocalDateTime endTime) {
        // 查询 raw 数据（注意 selectMaps 是 Map<String, Object>）
        List<Map<String, Object>> raw = this.baseMapper.selectMaps(
            new QueryWrapper<Orders>()
                .select("TO_CHAR(created_time, 'YYYY-MM-DD') AS day", "status", "COUNT(*) AS cnt")
                .eq("is_deleted", 0)
                .between("created_time", startTime, endTime)
                .groupBy("day", "status")
                .orderByAsc("day", "status")
        );
        // 构造 Map<day, Map<status, count>>
        Map<String, Map<String, Integer>> grouped = new TreeMap<>();
        for (Map<String, Object> row : raw) {
            String day = String.valueOf(row.get("day"));
            String status = String.valueOf(row.get("status"));
            Integer count = ((Number) row.get("cnt")).intValue();
            grouped.computeIfAbsent(day, k -> new HashMap<>()).put(status, count);
        }

        // 构造最终结果并补全 0
        List<OrderStatusByDayDTO> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, Integer>> entry : grouped.entrySet()) {
            String day = entry.getKey();
            Map<String, Integer> statusMap = entry.getValue();

            OrderStatusByDayDTO dto = new OrderStatusByDayDTO();
            dto.setDay(day);
            dto.setNewCount(statusMap.getOrDefault("NEW", 0));
            dto.setInProgressCount(statusMap.getOrDefault("IN_PROGRESS", 0));
            dto.setPausedCount(statusMap.getOrDefault("PAUSED", 0));
            dto.setCompletedCount(statusMap.getOrDefault("COMPLETED", 0));
            result.add(dto);
        }
        return result;
    }

    /**
     查询某个 assignee 指派人对应的 NEW 和 COMPLETED 工单数(已使用)
     */
    @Override
    public AssigneeOrderCountDTO getOrderCountByAssignee(String assignee) {
        // 查询该指派人的 step_task 列表
        LambdaQueryWrapper<StepTask> stWrapper = new LambdaQueryWrapper<>();
        stWrapper.eq(StepTask::getIsDeleted, 0)
            .eq(StepTask::getAssignee, assignee);

        List<StepTask> stepTasks = stepTaskMapper.selectList(stWrapper);

        if (stepTasks.isEmpty()) {
            return new AssigneeOrderCountDTO(null,assignee, 0, 0);
        }

        // 获取 taskId
        Set<Long> taskIds = stepTasks.stream()
            .map(StepTask::getTaskId)
            .collect(Collectors.toSet());

        // 查询 order_task
        LambdaQueryWrapper<OrderTask> otWrapper = new LambdaQueryWrapper<>();
        otWrapper.eq(OrderTask::getIsDelete, 0)
            .in(OrderTask::getId, taskIds);

        List<OrderTask> orderTasks = orderTaskMapper.selectList(otWrapper);
        if (orderTasks.isEmpty()) {
            return new AssigneeOrderCountDTO(null,assignee, 0, 0);
        }

        Set<Long> orderIds = orderTasks.stream()
            .map(OrderTask::getOrderId)
            .collect(Collectors.toSet());

        // 查询 orders，并根据状态分组
        LambdaQueryWrapper<Orders> oWrapper = new LambdaQueryWrapper<>();
        oWrapper.eq(Orders::getIsDeleted, 0)
            .in(Orders::getId, orderIds);

        List<Orders> orders = this.baseMapper.selectList(oWrapper);

        int newCount = 0;
        int completedCount = 0;
        for (Orders o : orders) {
            if ("NEW".equalsIgnoreCase(o.getStatus())) {
                newCount++;
            } else if ("COMPLETED".equalsIgnoreCase(o.getStatus())) {
                completedCount++;
            }
        }

        return new AssigneeOrderCountDTO(null, assignee,newCount, completedCount);

    }

    /**
     * 按月获取指定 assignee 指派人对应的工单数
     * @param assignee
     * @return
     */
    @Override
    public List<AssigneeOrderCountDTO> getMonthlyOrderStatsByAssignee(String assignee) {
        List<AssigneeOrderCountDTO> result = new ArrayList<>();

        // 获取当前时间对应的 YearMonth
        YearMonth currentMonth = YearMonth.now();

        for (int i = 4; i >= 0; i--) {
            // 从当前月份往前推
            YearMonth targetMonth = currentMonth.minusMonths(i);
            LocalDateTime startTime = targetMonth.atDay(1).atStartOfDay();
            LocalDateTime endTime = targetMonth.atEndOfMonth().atTime(23, 59, 59);

            // 1. 查询 step_task 中该 assignee 的任务
            List<StepTask> stepTasks = stepTaskMapper.selectList(new LambdaQueryWrapper<StepTask>()
                .eq(StepTask::getIsDeleted, 0)
                .eq(StepTask::getAssignee, assignee));

            if (stepTasks.isEmpty()) {
                result.add(new AssigneeOrderCountDTO(targetMonth.toString(), assignee, 0, 0));
                continue;
            }

            // 2. 找到对应的 order_task
            Set<Long> taskIds = stepTasks.stream()
                .map(StepTask::getTaskId)
                .collect(Collectors.toSet());

            List<OrderTask> orderTasks = orderTaskMapper.selectList(new LambdaQueryWrapper<OrderTask>()
                .eq(OrderTask::getIsDelete, 0)
                .in(OrderTask::getId, taskIds));

            if (orderTasks.isEmpty()) {
                result.add(new AssigneeOrderCountDTO(targetMonth.toString(), assignee, 0, 0));
                continue;
            }

            // 3. 根据 order_id 查询 orders 表，过滤出当前月份的数据
            Set<Long> orderIds = orderTasks.stream()
                .map(OrderTask::getOrderId)
                .collect(Collectors.toSet());

            List<Orders> orders = this.baseMapper.selectList(new LambdaQueryWrapper<Orders>()
                .eq(Orders::getIsDeleted, 0)
                .in(Orders::getId, orderIds)
                .ge(Orders::getCreatedTime, startTime)
                .le(Orders::getCreatedTime, endTime));

            // 4. 统计完成与未完成
            int completedCount = 0;
            int notCompletedCount = 0;

            for (Orders order : orders) {
                if ("COMPLETED".equalsIgnoreCase(order.getStatus())) {
                    completedCount++;
                } else {
                    notCompletedCount++;
                }
            }

            result.add(new AssigneeOrderCountDTO(targetMonth.toString(), assignee, notCompletedCount, completedCount));
        }

        return result;
    }

    /**
     * 工单下发
     * @param id 工单ID
     * @return 启动后的工单
     */
    @Override
    public Orders issueOrder(Long id) {
        // 1. 校验工单是否存在
        Orders order = this.getById(id);
        if (order == null) {
            throw new ServiceException("工单不存在");
        }
        // 2. 校验是否已下发
        if (!STATUS_NOT_DISPATCHED.equals(order.getDispatchStatus())) {
            throw new ServiceException("工单已下发或状态异常，无法重复下发");
        }
        // 3. 修改状态并更新
        order.setDispatchStatus(STATUS_DISPATCHED);
        boolean success = this.updateById(order);
        if (!success) {
            throw new ServiceException("工单下发失败，请重试");
        }
        return order;
    }

    /**
     * 更新工单状态
     * @param id 工单ID
     * @param status 新状态
     * @return 更新后的工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Orders updateOrderStatus(Long id, String status) {
        // 参数校验
        validateUpdateStatusParams(id, status);
        // 获取工单
        Orders order = this.getById(id);
        if (order == null) {
            throw new ServiceException("工单不存在");
        }
        // 验证状态流转
        validateStatusTransition(order.getStatus(), status);
        // 更新状态
        order.setStatus(status);
        boolean success = this.updateById(order);
        if (!success) {
            throw new ServiceException("工单状态更新失败");
        }

        return order;
    }

    /**
     * 统计工单逾期和即将到期的工单数量
     * @return 工单逾期和即将到期的工单数量
     */
    @Override
    public OrderDueCountDTO countDueAndUpcoming() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime next24h = now.plusHours(24);

        // 逾期工单数量
        long overdueCount = this.count(
            new LambdaQueryWrapper<Orders>()
                .lt(Orders::getExpectedTime, now)
                .ne(Orders::getStatus, "COMPLETED")
                .eq(Orders::getIsDeleted, 0)
        );

        // 临期工单数量（24小时内）
        long upcomingDueCount = this.count(
            new LambdaQueryWrapper<Orders>()
                .ge(Orders::getExpectedTime, now)
                .le(Orders::getExpectedTime, next24h)
                .ne(Orders::getStatus, "COMPLETED")
                .eq(Orders::getIsDeleted, 0)
        );
        // 生产工单数量
        long productionCount = this.count(
            new LambdaQueryWrapper<Orders>()
                .eq(Orders::getStatus, "IN_PROGRESS")
                .eq(Orders::getIsDeleted, 0)
        );

        OrderDueCountDTO dto = new OrderDueCountDTO();
        dto.setOverdueCount(overdueCount);
        dto.setUpcomingDueCount(upcomingDueCount);
        dto.setProductionCount(productionCount);
        return dto;
    }

    /**
     * 更新工单类型
     * @param id 工单ID
     * @param orderType 工单类型
     * @return 更新后的工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Orders updateOrderType(Long id, String orderType) {
        // 参数校验
        validateUpdateOrderTypeParams(id, orderType);
        // 获取工单
        Orders order = this.getById(id);
        if (order == null) {
            throw new ServiceException("工单不存在");
        }
        // 检查工单状态，已完成的工单不能修改类型
        if (STATUS_COMPLETED.equals(order.getStatus())) {
            throw new ServiceException("已完成的工单不能修改类型");
        }
        // 更新工单类型
        order.setOrderType(orderType);
        boolean success = this.updateById(order);
        if (!success) {
            throw new ServiceException("工单类型更新失败");
        }

        return order;
    }

    /**
     * 根据工单编号查询工单
     * @param orderCode 工单编号
     * @return 工单信息
     */
    @Override
    public Orders getByOrderCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            throw new ServiceException("工单编号不能为空");
        }

        return this.lambdaQuery()
                .eq(Orders::getOrderCode, orderCode)
                .eq(Orders::getIsDeleted, 0)
                .one();
    }

    /**
     * 分页查询工单
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param orderType 工单类型（可选）
     * @param status 工单状态（可选）
     * @param orderCode 工单编号（模糊查询，可选）
     * @return 分页结果
     */
    @Override
    public IPage<Orders> queryOrdersPage(int pageNum, int pageSize, String orderType, String status, String orderCode) {
        // 参数校验
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        // 构建查询条件
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getIsDeleted, 0)
                .eq(StringUtils.isNotBlank(orderType), Orders::getOrderType, orderType)
                .eq(StringUtils.isNotBlank(status), Orders::getStatus, status)
                .like(StringUtils.isNotBlank(orderCode), Orders::getOrderCode, orderCode)
                .orderByDesc(Orders::getCreatedTime);

        // 执行分页查询
        Page<Orders> page = new Page<>(pageNum, pageSize);
        return this.page(page, queryWrapper);
    }

    /**
     * 开始执行工单
     * @param id 工单ID
     * @return 更新后的工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Orders startOrder(Long id) {
        if (id == null) {
            throw new ServiceException("工单ID不能为空");
        }

        Orders order = this.getById(id);
        if (order == null) {
            throw new ServiceException("工单不存在");
        }
        // 只有未开始状态的工单才能开始执行
        if (!STATUS_NEW.equals(order.getStatus())) {
            throw new ServiceException("只有未开始状态的工单才能开始执行，当前状态：" + getStatusName(order.getStatus()));
        }
        // 更新状态为执行中
        order.setStatus(STATUS_IN_PROGRESS);
        boolean success = this.updateById(order);
        if (!success) {
            throw new ServiceException("工单开始执行失败");
        }
        return order;
    }

    /**
     * 完成工单
     * @param id 工单ID
     * @return 更新后的工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Orders completeOrder(Long id) {
        if (id == null) {
            throw new ServiceException("工单ID不能为空");
        }
        Orders order = this.getById(id);
        if (order == null) {
            throw new ServiceException("工单不存在");
        }
        // 只有执行中状态的工单才能完成
        if (!STATUS_IN_PROGRESS.equals(order.getStatus())) {
            throw new ServiceException("只有执行中状态的工单才能完成，当前状态：" + getStatusName(order.getStatus()));
        }
        // 更新状态为已完成
        order.setStatus(STATUS_COMPLETED);
        boolean success = this.updateById(order);
        if (!success) {
            throw new ServiceException("工单完成失败");
        }

        return order;
    }

    /**
     * 批量删除工单（软删除）
     * @param ids 工单ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteOrders(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("工单ID列表不能为空");
        }

        // 检查是否存在已完成的工单，已完成的工单不能删除
        List<Orders> orders = this.listByIds(ids);
        for (Orders order : orders) {
            if (STATUS_COMPLETED.equals(order.getStatus())) {
                throw new ServiceException("已完成的工单不能删除，工单编号：" + order.getOrderCode());
            }
        }

        // 执行软删除
        return this.lambdaUpdate()
                .in(Orders::getId, ids)
                .set(Orders::getIsDeleted, 1)
                .update();
    }

    /**
     * 根据状态统计工单数量
     * @param status 工单状态
     * @return 统计数量
     */
    @Override
    public long countByStatus(String status) {
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getIsDeleted, 0);

        if (StringUtils.isNotBlank(status)) {
            queryWrapper.eq(Orders::getStatus, status);
        }

        return this.count(queryWrapper);
    }

    // ==================== 私有方法 ====================

    /**
     * 验证创建工单参数
     */
    private void validateCreateOrderParams(String orderCode, String orderType) {
        if (StringUtils.isBlank(orderCode)) {
            throw new ServiceException("工单编号不能为空");
        }
        if (StringUtils.isBlank(orderType)) {
            throw new ServiceException("工单类型不能为空");
        }
        if (!ORDER_TYPE_NORMAL.equals(orderType) && !ORDER_TYPE_URGENT.equals(orderType)) {
            throw new ServiceException("无效的工单类型，支持的类型：NORMAL（普通）、URGENT（加急）");
        }
    }

    /**
     * 验证更新状态参数
     */
    private void validateUpdateStatusParams(Long id, String status) {
        if (id == null) {
            throw new ServiceException("工单ID不能为空");
        }
        if (StringUtils.isBlank(status)) {
            throw new ServiceException("工单状态不能为空");
        }
        if (!STATUS_NEW.equals(status) && !STATUS_IN_PROGRESS.equals(status) && !STATUS_COMPLETED.equals(status) && !STATUS_PAUSED.equals(status)) {
            throw new ServiceException("无效的工单状态，支持的状态：NEW（未开始）、IN_PROGRESS（执行中）、COMPLETED（已完成）、COMPLETED（已暂停）");
        }
    }

    /**
     * 验证更新工单类型参数
     */
    private void validateUpdateOrderTypeParams(Long id, String orderType) {
        if (id == null) {
            throw new ServiceException("工单ID不能为空");
        }
        if (StringUtils.isBlank(orderType)) {
            throw new ServiceException("工单类型不能为空");
        }
        if (!ORDER_TYPE_NORMAL.equals(orderType) && !ORDER_TYPE_URGENT.equals(orderType)) {
            throw new ServiceException("无效的工单类型，支持的类型：NORMAL（普通）、URGENT（加急）");
        }
    }

    /**
     * 验证状态流转
     */
    private void validateStatusTransition(String fromStatus, String toStatus) {
        if (StringUtils.isBlank(fromStatus) || StringUtils.isBlank(toStatus)) {
            return;
        }

        // 已完成的工单不能再变更状态
        if (STATUS_COMPLETED.equals(fromStatus)) {
            throw new ServiceException("已完成的工单不能再变更状态");
        }

        // 不允许从执行中直接变更为未开始
        if (STATUS_IN_PROGRESS.equals(fromStatus) && STATUS_NEW.equals(toStatus)) {
            throw new ServiceException("不能从执行中状态回退到未开始状态");
        }
    }

    /**
     * 检查工单编号是否已存在
     */
    private boolean existsByOrderCode(String orderCode) {
        return this.lambdaQuery()
                .eq(Orders::getOrderCode, orderCode)
                .eq(Orders::getIsDeleted, 0)
                .count() > 0;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        if (StringUtils.isBlank(status)) {
            return "";
        }
        switch (status) {
            case STATUS_NEW:
                return "未开始";
            case STATUS_IN_PROGRESS:
                return "执行中";
            case STATUS_COMPLETED:
                return "已完成";
            case STATUS_PAUSED:
                return "已暂停";
            default:
                return status;
        }
    }

    /**
     * 分页查询工单详细信息
     * 参考GroupedOrderTaskVO的分页处理方式
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param queryRequest 查询条件
     * @return 工单详细信息分页结果
     */
    @Override
    public Page<OrderDetailVO> getOrderDetailPage(int pageNum, int pageSize, OrderDetailQueryRequest queryRequest) {
        // 为了正确处理分页，我们需要先获取所有匹配的数据进行分组，然后再进行分页
        // 这里使用一个较大的页面大小来获取所有数据（类似GroupedOrderTaskVO的处理方式）
        Page<OrderDetailRawVO> rawPage = new Page<>(1, Integer.MAX_VALUE);
        Page<OrderDetailRawVO> rawResult = baseMapper.selectOrderDetailRawPage(rawPage, queryRequest);

        // 将原始数据转换为分组数据
        List<OrderDetailVO> orderDetails = convertRawDataToOrderDetail(rawResult.getRecords());

        // 手动进行分页处理
        int current = pageNum;
        int size = pageSize;
        int total = orderDetails.size();
        int startIndex = (current - 1) * size;
        int endIndex = Math.min(startIndex + size, total);

        List<OrderDetailVO> pagedList;
        if (startIndex >= total) {
            pagedList = Collections.emptyList();
        } else {
            pagedList = orderDetails.subList(startIndex, endIndex);
        }

        // 构建分页结果
        Page<OrderDetailVO> result = new Page<>();
        result.setCurrent(current);
        result.setSize(size);
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));
        result.setRecords(pagedList);

        return result;
    }

    /**
     * 根据工单ID获取工单详细信息
     * @param orderId 工单ID
     * @return 工单详细信息
     */
    @Override
    public OrderDetailVO getOrderDetailById(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("工单ID不能为空");
        }
        // 查询原始数据
        List<OrderDetailRawVO> rawData = baseMapper.selectOrderDetailRawByOrderId(orderId);
        if (rawData.isEmpty()) {
            throw new ServiceException("工单不存在");
        }

        // 对于详情查看，使用详细的转换方法
        List<OrderDetailVO> orderDetails = convertRawDataToOrderDetailForDetail(rawData);
        if (orderDetails.isEmpty()) {
            throw new ServiceException("工单详细信息转换失败");
        }

        return orderDetails.get(0);
    }

    /**
     * 将原始查询数据转换为分组的工单详细信息
     * 嵌套结构：按工单ID分组，每个工单包含多个产品，每个产品有自己的工序信息
     * @param rawDataList 原始数据列表
     * @return 分组后的工单详细信息列表
     */
    private List<OrderDetailVO> convertRawDataToOrderDetail(List<OrderDetailRawVO> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按工单ID分组，保持原始数据的顺序
        Map<Long, List<OrderDetailRawVO>> groupedByOrder = rawDataList.stream()
                .filter(raw -> raw.getOrderId() != null)
                .collect(Collectors.groupingBy(
                    OrderDetailRawVO::getOrderId,
                    // 使用LinkedHashMap保持插入顺序
                    LinkedHashMap::new,
                    Collectors.toList()
                ));

        List<OrderDetailVO> result = new ArrayList<>();

        for (Map.Entry<Long, List<OrderDetailRawVO>> orderEntry : groupedByOrder.entrySet()) {
            List<OrderDetailRawVO> orderData = orderEntry.getValue();
            if (orderData.isEmpty()) {
                continue;
            }

            // 取第一个作为工单基础信息（因为同一工单的基础信息都相同）
            OrderDetailRawVO firstRecord = orderData.get(0);
            OrderDetailVO orderDetail = new OrderDetailVO();

            // 设置工单基本信息
            orderDetail.setOrderId(firstRecord.getOrderId());
            orderDetail.setOrderCode(firstRecord.getOrderCode());
            orderDetail.setOrderType(firstRecord.getOrderType());
            orderDetail.setOrderStatus(firstRecord.getOrderStatus());
            orderDetail.setOrderCreatedTime(firstRecord.getOrderCreatedTime());
            orderDetail.setOrderExpectedTime(firstRecord.getOrderExpectedTime());
            orderDetail.setOrderDispatchStatus(firstRecord.getOrderDispatchStatus());

            // 按产品ID分组，构建产品列表
            Map<String, List<OrderDetailRawVO>> groupedByProduct = orderData.stream()
                    .filter(raw -> raw.getProductId() != null && raw.getStyleId() != null)
                  // 使用LinkedHashMap保持插入顺序
                    .collect(Collectors.groupingBy(
                        raw -> raw.getProductId() + "_" + raw.getStyleId()
                    ));
            //  .collect(Collectors.groupingBy(OrderDetailRawVO::getProductId));

            List<OrderDetailVO.ProductDetail> products = new ArrayList<>();

            for (Map.Entry<String, List<OrderDetailRawVO>> productEntry : groupedByProduct.entrySet()) {
                List<OrderDetailRawVO> productData = productEntry.getValue();
                if (productData.isEmpty()) {
                    continue;
                }

                // 取第一个作为产品基础信息
                OrderDetailRawVO productFirstRecord = productData.get(0);
                OrderDetailVO.ProductDetail product = new OrderDetailVO.ProductDetail();

                // 设置产品信息
                product.setOrderItemId(productFirstRecord.getOrderItemId());
                product.setProductId(productFirstRecord.getProductId());
                product.setProductName(productFirstRecord.getProductName());
                product.setOrderItemQuantity(productFirstRecord.getOrderItemQuantity());
                product.setInventory(productFirstRecord.getInventory());
                product.setOrderItemCreateTime(productFirstRecord.getOrderItemCreateTime());
                product.setStyleId(productFirstRecord.getStyleId());
                product.setStyleName(productFirstRecord.getStyleName());
                product.setBoardType(productFirstRecord.getBoardType());

                // 设置任务信息
                product.setItemId(productFirstRecord.getItemId());
                product.setItemName(productFirstRecord.getItemName());
                product.setProcessRouteCode(productFirstRecord.getProcessRouteCode());
                product.setTaskLevel(productFirstRecord.getTaskLevel());
                product.setTaskStatus(productFirstRecord.getTaskStatus());
                product.setCreatedName(productFirstRecord.getCreatedName());
                product.setUpdatedName(productFirstRecord.getUpdatedName());
                product.setRemark(productFirstRecord.getRemark());
                product.setOrderTaskCreateTime(productFirstRecord.getOrderTaskCreateTime());
                product.setOrderTaskUpdateTime(productFirstRecord.getOrderTaskUpdateTime());
                product.setIsDefer(productFirstRecord.getIsDefer());
                product.setExpectedTime(productFirstRecord.getExpectedTime());
                product.setCompletedTime(productFirstRecord.getCompletedTime());
                product.setOrderTaskQuantity(productFirstRecord.getOrderTaskQuantity());

                // 处理该产品的工序任务列表
                List<OrderDetailVO.StepTaskDetail> stepTasks = productData.stream()
                        .filter(raw -> raw.getStepTaskId() != null) // 过滤掉没有工序任务的记录
                        .collect(Collectors.toMap(
                            OrderDetailRawVO::getStepTaskId, // 按工序任务ID去重
                            this::convertToStepTaskDetail,
                            (existing, replacement) -> existing // 如果重复，保留第一个
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(OrderDetailVO.StepTaskDetail::getStepNumber)) // 按工序编号排序
                        .collect(Collectors.toList());

                product.setStepTasks(stepTasks);
                products.add(product);
            }

            // 按产品ID排序
            products.sort(Comparator.comparing(OrderDetailVO.ProductDetail::getProductId));
            orderDetail.setProducts(products);

            result.add(orderDetail);
        }

        // 不再进行额外排序，保持数据库查询结果的顺序
        // 排序逻辑已经在SQL查询中处理

        return result;
    }

    /**
     * 将原始查询数据转换为详情查看的工单详细信息
     * 详情查看：使用与分页查询相同的嵌套结构
     * @param rawDataList 原始数据列表
     * @return 分组后的工单详细信息列表
     */
    private List<OrderDetailVO> convertRawDataToOrderDetailForDetail(List<OrderDetailRawVO> rawDataList) {
        // 详情查看使用与分页查询相同的转换逻辑
        return convertRawDataToOrderDetail(rawDataList);
    }

    /**
     * 将原始数据转换为工序任务详情
     * @param raw 原始数据
     * @return 工序任务详情
     */
    private OrderDetailVO.StepTaskDetail convertToStepTaskDetail(OrderDetailRawVO raw) {
        OrderDetailVO.StepTaskDetail stepTask = new OrderDetailVO.StepTaskDetail();
        // 添加工单任务ID，类似GroupedOrderTaskVO
        stepTask.setOrderTaskId(raw.getOrderTaskId());
        stepTask.setStepTaskId(raw.getStepTaskId());
        stepTask.setStepId(raw.getStepId());
        stepTask.setStepNumber(raw.getStepNumber());
        stepTask.setStepName(raw.getStepName());
        stepTask.setAssignee(raw.getAssignee());
        // String 类型转json
        String json = raw.getDefectInfo();
        Map<String, Integer> defectInfo = JSONObject.parseObject(json, new TypeReference<Map<String, Integer>>() {});
        stepTask.setDefectInfo(defectInfo);
        stepTask.setIsCompleted(raw.getIsCompleted());
        stepTask.setStepTaskCreateTime(raw.getStepTaskCreateTime());
        stepTask.setCompletedAt(raw.getCompletedAt());
        stepTask.setExpectedAt(raw.getExpectedAt());
        return stepTask;
    }
}





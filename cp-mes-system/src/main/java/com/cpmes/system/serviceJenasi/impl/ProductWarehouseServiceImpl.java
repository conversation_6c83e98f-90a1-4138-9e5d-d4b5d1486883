package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.ProductWarehouse;
import com.cpmes.system.mapperJenasi.ProductWarehouseMapper;
import com.cpmes.system.serviceJenasi.InOutRequestService;
import com.cpmes.system.serviceJenasi.ProductWarehouseService;
import com.cpmes.system.vo.ProductWarehouseVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS("slave")
public class ProductWarehouseServiceImpl extends ServiceImpl<ProductWarehouseMapper, ProductWarehouse> implements ProductWarehouseService {

    @Resource
    @Lazy
    private InOutRequestService inOutRequestService;

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 预先验证所有ID是否存在，只删除存在的ID
        List<Integer> existingIds = this.lambdaQuery()
                .in(ProductWarehouse::getProductId, ids)
                .list()
                .stream()
                .map(ProductWarehouse::getProductId)
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false; // 没有找到任何匹配的记录
        }

        // 只删除存在的记录
        return this.removeByIds(existingIds);
    }

    /**
     * 出库
     * @param productId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean outbound(Integer productId, Integer quantity, String userName) {
        ProductWarehouse productWarehouse = this.getById(productId);
        if (productWarehouse == null){
            throw new RuntimeException("未找到该成品");
        }
        //成品出库
        int outbound = this.baseMapper.outbound(productId, quantity);
        if (outbound <  0){
            throw new RuntimeException("出库失败");
        }
        //新增出库记录
        String productName = productWarehouse.getProductName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 1;
        boolean result = inOutRequestService.inOutbound(productId, quantity, userName, productName, "成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    /**
     * 入库
     * @param productId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean inbound(Integer productId, Integer quantity, String userName) {
        ProductWarehouse productWarehouse = this.getById(productId);
        if (productWarehouse == null){
            throw new RuntimeException("未找到该成品");
        }
        //成品入库
        int inbound = this.baseMapper.inbound(productId, quantity);
        if (inbound <  0){
            throw new RuntimeException("入库失败");
        }
        //新增出库记录
        String productName = productWarehouse.getProductName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 0;
        boolean result = inOutRequestService.inOutbound(productId, quantity, userName, productName, "成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    /**
     * 获取成品仓库统计信息
     */
    @Override
    public Object getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 获取所有成品记录
        List<ProductWarehouse> allProducts = this.list();

        // 总库存数量（所有成品的库存总和）
        Long totalStock = allProducts.stream()
                .mapToLong(product -> product.getCurrentStock() != null ? product.getCurrentStock() : 0L)
                .sum();

        // 物料种类数（成品总数）
        int totalMaterials = allProducts.size();

        // 低库存成品数（currentStock <= fields2安全库存）
        long lowStock = allProducts.stream()
                .filter(product -> {
                    Integer currentStock = product.getCurrentStock();
                    String fields2 = product.getFields2(); // 安全库存
                    if (currentStock == null || fields2 == null || fields2.isEmpty()) {
                        return false;
                    }
                    try {
                        Integer safetyStock = Integer.parseInt(fields2);
                        return currentStock <= safetyStock;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .count();

        // 需要采购的成品数（currentStock <= fields2安全库存）
        long needPurchase = lowStock; // 与低库存数相同

        statistics.put("totalStock", totalStock);
        statistics.put("totalMaterials", totalMaterials);
        statistics.put("lowStock", lowStock);
        statistics.put("needPurchase", needPurchase);

        return statistics;
    }

    @Override
    public Page<ProductWarehouseVO> selectProductWarehousePage(Page<ProductWarehouseVO> page, ProductWarehouse productWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder) {
        QueryWrapper<ProductWarehouse> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(productWarehouse.getProductName()), "pw.product_name", productWarehouse.getProductName());
        wrapper.eq(productWarehouse.getSeriesId() != null, "pw.series_id", productWarehouse.getSeriesId());
        wrapper.eq(productWarehouse.getStyleId() != null, "pw.style_id", productWarehouse.getStyleId());
        wrapper.eq(StringUtils.isNotBlank(productWarehouse.getFields3()), "pw.fields3", productWarehouse.getFields3());

        // 移除区域相关的查询条件，因为现在不再关联区域表
        // wrapper.like(StringUtils.isNotBlank(zoneCode), "wz.zone_code", zoneCode);
        // wrapper.like(StringUtils.isNotBlank(zoneName), "wz.zone_name", zoneName);

        // 移除排序逻辑，因为GROUP BY查询中ORDER BY需要在XML中手动处理
        // 排序将在XML的GROUP BY之后添加

        return this.baseMapper.selectPageWithZone(page, wrapper);
    }
}

package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.ComponentWarehouse;
import com.cpmes.system.vo.ComponentWarehouseVO;
import org.springframework.stereotype.Service;

import java.util.List;


public interface ComponentWarehouseService extends IService<ComponentWarehouse> {
    List<ComponentWarehouse> findByName(String componentName);

    /**
     * 批量删除组件
     * @param ids 组件ID列表
     * @return 删除成功的记录数
     */
    boolean batchDeleteByIds(List<Integer> ids);

    /**
     * 零部件出库
     */
    boolean outbound(Integer componentId,Integer quantity,String userName);
    /**
     * 零部件入库
     */
    boolean inbound(Integer componentId,Integer quantity,String userName);

    /**
     * 获取零部件仓库统计信息
     * @return 统计数据
     */
    Object getStatistics();

    /**
     * 分页查询零部件信息（包含区域）
     * @param page 分页对象
     * @param componentWarehouse 查询条件
     * @param zoneCode 区域代码
     * @param zoneName 区域名称
     * @param sortField 排序字段
     * @param sortOrder 排序顺序
     * @return
     */
    Page<ComponentWarehouseVO> selectComponentWarehousePage(Page<ComponentWarehouseVO> page, ComponentWarehouse componentWarehouse, String zoneCode, String zoneName, String sortField, String sortOrder);
}

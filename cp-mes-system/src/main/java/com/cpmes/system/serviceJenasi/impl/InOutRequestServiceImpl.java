package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.*;
import com.cpmes.system.entity.vo.InOutCensusVO;
import com.cpmes.system.mapperJenasi.InOutRequestMapper;
import com.cpmes.system.serviceJenasi.*;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <p>
 * 出入库申请记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
@DS("slave")
public class InOutRequestServiceImpl extends ServiceImpl<InOutRequestMapper, InOutRequest> implements InOutRequestService {

    private static final Logger log = LoggerFactory.getLogger(InOutRequestServiceImpl.class);

    // 注入所有库存相关服务
    @Resource
    private RawMaterialWarehouseService rawMaterialWarehouseService;

    @Resource
    private ComponentWarehouseService componentWarehouseService;

    @Resource
    private SemiFinishedProductService semiFinishedProductService;

    @Resource
    private ProductWarehouseService productWarehouseService;


    /**
     * 重写page方法，查询后填充物料名称
     */
    @Override
    public Page<InOutRequest> page(Page<InOutRequest> page, QueryWrapper<InOutRequest> queryWrapper) {
        Page<InOutRequest> resultPage = super.page(page, queryWrapper);
        List<InOutRequest> records = resultPage.getRecords();

        // 填充物料名称
        for (InOutRequest request : records) {
            fillMaterialName(request);
        }

        return resultPage;
    }

    /**
     * 填充物料名称
     * 根据物料类型和ID查询对应的名称
     */
    private void fillMaterialName(InOutRequest request) {
        if (request == null || request.getMaterialId() == null || request.getMaterialType() == null) {
            return;
        }

        // 根据物料类型和ID，从对应的表中查询物料名称
        String materialType = request.getMaterialType();
        Integer materialId = request.getMaterialId();
        String materialName = null;

        try {
            switch (materialType) {
                case "原料":
                    // 从原料表查询名称
                    RawMaterialWarehouse rawMaterial = rawMaterialWarehouseService.getById(materialId);
                    if (rawMaterial != null) {
                        materialName = rawMaterial.getMaterialName();
                    }
                    break;
                case "零部件":
                    // 从零部件表查询名称
                    ComponentWarehouse component = componentWarehouseService.getById(materialId);
                    if (component != null) {
                        materialName = component.getComponentName();
                    }
                    break;
                case "成品":
                    // 从成品表查询名称
                    ProductWarehouse product = productWarehouseService.getById(materialId);
                    if (product != null) {
                        materialName = product.getProductName();
                    }
                    break;
                case "半成品":
                    // 从半成品表查询名称
                    SemiFinishedProduct semifinished = semiFinishedProductService.getById(materialId);
                    if (semifinished != null) {
                        materialName = semifinished.getSemiProductName(); // 假设字段名是materialName
                    }
                    break;
                default:
                    break;
            }

            if (materialName != null) {
                request.setMaterialName(materialName);
            } else {
                request.setMaterialName("未知物料");
            }
        } catch (Exception e) {
            // 日志记录异常
            request.setMaterialName("未知物料");
        }
    }




    /**
     * 新增出库记录
     */
    @Override
    public boolean inOutbound(Integer materialId, Integer quantity, String userName,String materialName,String materialType,Integer type) {
        if(!StringUtil.isNotBlank(userName) || !StringUtil.isNotBlank(materialType) || !StringUtil.isNotBlank(materialName) || materialId < 0) {
            throw new RuntimeException("参数错误，记录失败");
        }
        InOutRequest inOutRequest = new InOutRequest();
        inOutRequest.setMaterialId(materialId);
        inOutRequest.setQuantity(quantity);
        // type用来判断类型，1为出库，0为入库
        if (type == 1){
            inOutRequest.setApplicationType("出库记录");
        }else {
            inOutRequest.setApplicationType("入库记录");
        }
        inOutRequest.setApplicantName(userName);
        inOutRequest.setMaterialType(materialType);
        inOutRequest.setMaterialName(materialName);
        inOutRequest.setRequestTime(LocalDateTime.now());
        boolean save = this.save(inOutRequest);
        if (!save) {
            return false;
        }
        return true;
    }

    /**
     * 按月统计出入库记录
     */
    @Override
    public List<InOutCensusVO> getCurrentMonthAnalysis(int year, int month) {
        return this.baseMapper.analyzeCurrentMonthInOut(year,month);
    }

    /**
     * 按月统计出入库记录
     * 分页及模糊查询
     */
    @Override
    public Page<InOutCensusVO> getCurrentMonthAnalysis(Integer year, Integer month, Integer pageNum, Integer pageSize, String materialName, String materialType) {
        //创建mybatis_plus的分页对象
        Page<InOutCensusVO> page = new Page<>(pageNum,pageSize);
        //调用mapper层方法自动处理分页逻辑
        return this.baseMapper.selectFilteredMaterialSummary(page, year, month, materialName, materialType);
    }
}

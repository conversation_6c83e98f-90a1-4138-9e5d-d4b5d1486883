package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.helper.LoginHelper;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.entity.TrackingHistory;
import com.cpmes.system.entity.TrackingNumber;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberAddRequest;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberQueryRequest;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberUpdateRequest;
import com.cpmes.system.entity.vo.TrackingNumberVO;
import com.cpmes.system.mapperJenasi.TrackingHistoryMapper;
import com.cpmes.system.mapperJenasi.TrackingNumberMapper;
import com.cpmes.system.service.LogisticsQueryService;
import com.cpmes.system.serviceJenasi.TrackingNumberService;
import com.cpmes.system.utils.LogisticsCacheKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快递单号管理服务实现类
 *
 * <AUTHOR> System
 * @since 2025-01-21
 */
@Slf4j
@Service
@DS("slave")
public class TrackingNumberServiceImpl extends ServiceImpl<TrackingNumberMapper, TrackingNumber>
        implements TrackingNumberService {

    @Resource
    private TrackingNumberMapper trackingNumberMapper;

    @Resource
    private TrackingHistoryMapper trackingHistoryMapper;

    @Autowired
    private LogisticsQueryService logisticsQueryService;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public IPage<TrackingNumberVO> getTrackingNumberListByPage(Page<TrackingNumberVO> page,
                                                              TrackingNumberQueryRequest queryRequest) {
        try {
            log.info("分页查询快递单号列表: page={}, size={}, query={}",
                    page.getCurrent(), page.getSize(), queryRequest);

            return trackingNumberMapper.getTrackingNumberListByPage(page, queryRequest);
        } catch (Exception e) {
            log.error("分页查询快递单号列表失败", e);
            throw new ServiceException("查询快递单号列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addTrackingNumber(TrackingNumberAddRequest addRequest) {
        try {
            log.info("新增快递单号: {}", addRequest);

            // 检查快递单号是否已存在
            LambdaQueryWrapper<TrackingNumber> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TrackingNumber::getTrackingNumber, addRequest.getTrackingNumber());
            TrackingNumber existingTrackingNumber = this.getOne(queryWrapper);
            if (existingTrackingNumber != null) {
                throw new ServiceException("快递单号已存在: " + addRequest.getTrackingNumber());
            }

            // 创建快递单号记录
            TrackingNumber trackingNumber = new TrackingNumber();
            BeanUtils.copyProperties(addRequest, trackingNumber);
            trackingNumber.setCurrentStatus("NO_INFO");
            trackingNumber.setQuerySuccess(false);
            trackingNumber.setCreateBy(LoginHelper.getUsername());
            trackingNumber.setUpdateBy(LoginHelper.getUsername());

            // 保存到数据库
            boolean saved = this.save(trackingNumber);
            if (!saved) {
                throw new ServiceException("保存快递单号失败");
            }

            // 立即查询一次物流信息
            try {
                LogisticsTrackingDto logisticsInfo = logisticsQueryService.queryLogistics(
                        addRequest.getTrackingNumber(),
                        addRequest.getLogisticsCompany()
                );
                updateTrackingNumberFromLogistics(trackingNumber.getId(), logisticsInfo);
            } catch (Exception e) {
                log.warn("新增快递单号后查询物流信息失败: {}", e.getMessage());
                // 不影响主流程，继续执行
            }

            log.info("新增快递单号成功: id={}", trackingNumber.getId());
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("新增快递单号失败", e);
            throw new ServiceException("新增快递单号失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTrackingNumber(TrackingNumberUpdateRequest updateRequest) {
        try {
            log.info("更新快递单号: {}", updateRequest);

            TrackingNumber trackingNumber = this.getById(updateRequest.getId());
            if (trackingNumber == null) {
                throw new ServiceException("快递单号不存在");
            }

            // 更新字段
            if (StringUtils.isNotEmpty(updateRequest.getLogisticsCompany())) {
                trackingNumber.setLogisticsCompany(updateRequest.getLogisticsCompany());
            }
            if (updateRequest.getPurchaseOrderId() != null) {
                trackingNumber.setPurchaseOrderId(updateRequest.getPurchaseOrderId());
            }
            if (StringUtils.isNotEmpty(updateRequest.getPurchaseOrderNo())) {
                trackingNumber.setPurchaseOrderNo(updateRequest.getPurchaseOrderNo());
            }

            trackingNumber.setUpdateBy(LoginHelper.getUsername());

            // 更新数据库
            boolean updated = this.updateById(trackingNumber);
            if (!updated) {
                throw new ServiceException("更新快递单号失败");
            }

            log.info("更新快递单号成功: id={}", updateRequest.getId());
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新快递单号失败", e);
            throw new ServiceException("更新快递单号失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTrackingNumber(Long id) {
        try {
            log.info("删除快递单号: id={}", id);

            TrackingNumber trackingNumber = this.getById(id);
            if (trackingNumber == null) {
                throw new ServiceException("快递单号不存在");
            }

            String trackingNumberStr = trackingNumber.getTrackingNumber();

            // 1. 删除相关的历史记录
            trackingHistoryMapper.deleteTrackingHistoryByNumber(trackingNumberStr);

            // 2. 清理所有相关缓存（新增）
            clearAllRelatedCache(trackingNumberStr);

            // 3. 删除快递单号记录
            boolean deleted = this.removeById(id);
            if (!deleted) {
                throw new ServiceException("删除快递单号失败");
            }

            log.info("删除快递单号成功: id={}, trackingNumber={}", id, trackingNumberStr);
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除快递单号失败", e);
            throw new ServiceException("删除快递单号失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTrackingNumberByNumber(String trackingNumber) {
        try {
            log.info("根据快递单号删除: trackingNumber={}", trackingNumber);

            if (StringUtils.isEmpty(trackingNumber)) {
                throw new ServiceException("快递单号不能为空");
            }

            // 查找快递单号记录
            LambdaQueryWrapper<TrackingNumber> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TrackingNumber::getTrackingNumber, trackingNumber);
            TrackingNumber trackingNumberEntity = this.getOne(queryWrapper);

            if (trackingNumberEntity == null) {
                throw new ServiceException("快递单号不存在: " + trackingNumber);
            }

            // 1. 删除相关的历史记录
            trackingHistoryMapper.deleteTrackingHistoryByNumber(trackingNumber);

            // 2. 清理所有相关缓存
            clearAllRelatedCache(trackingNumber);

            // 3. 删除快递单号记录
            boolean deleted = this.removeById(trackingNumberEntity.getId());
            if (!deleted) {
                throw new ServiceException("删除快递单号失败");
            }

            log.info("根据快递单号删除成功: trackingNumber={}", trackingNumber);
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("根据快递单号删除失败", e);
            throw new ServiceException("根据快递单号删除失败: " + e.getMessage());
        }
    }

    /**
     * 清理所有相关缓存
     */
    private void clearAllRelatedCache(String trackingNumber) {
        try {
            log.info("开始清理所有相关缓存: trackingNumber={}", trackingNumber);

            // 1. 清理Spring Cache（通过LogisticsQueryService）
            if (logisticsQueryService != null) {
                try {
                    logisticsQueryService.clearCache(trackingNumber);
                    log.debug("清理Spring Cache完成: trackingNumber={}", trackingNumber);
                } catch (Exception e) {
                    log.warn("清理Spring Cache失败: trackingNumber={}, error={}", trackingNumber, e.getMessage());
                }
            }

            // 2. 清理其他可能的缓存工具类缓存（如果存在）
            try {
                // 这里可以添加其他缓存工具类的清理逻辑
                log.debug("其他缓存工具清理完成: trackingNumber={}", trackingNumber);
            } catch (Exception e) {
                log.warn("其他缓存工具清理失败: trackingNumber={}, error={}", trackingNumber, e.getMessage());
            }

            // 3. 清理所有可能的缓存键格式
            clearCacheByAllFormats(trackingNumber);

            log.info("清理所有相关缓存完成: trackingNumber={}", trackingNumber);
        } catch (Exception e) {
            log.error("清理缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
        }
    }

    /**
     * 按所有可能的格式清理缓存
     */
    private void clearCacheByAllFormats(String trackingNumber) {
        if (redisTemplate != null) {
            try {
                // 使用统一的缓存键生成器获取所有可能的键
                List<String> possibleKeys = LogisticsCacheKeyGenerator.generateAllPossibleCacheKeys(trackingNumber);

                for (String key : possibleKeys) {
                    try {
                        Boolean deleted = redisTemplate.delete(key);
                        if (Boolean.TRUE.equals(deleted)) {
                            log.debug("成功清理缓存键: {}", key);
                        } else {
                            log.debug("缓存键不存在或已清理: {}", key);
                        }
                    } catch (Exception e) {
                        log.warn("清理缓存键失败: key={}, error={}", key, e.getMessage());
                    }
                }

                log.info("按所有格式清理缓存完成: trackingNumber={}, totalKeys={}", trackingNumber, possibleKeys.size());
            } catch (Exception e) {
                log.error("按所有格式清理缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage());
            }
        } else {
            log.warn("RedisTemplate未配置，跳过Redis缓存清理: trackingNumber={}", trackingNumber);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteTrackingNumbers(List<Long> ids) {
        try {
            log.info("批量删除快递单号: ids={}", ids);

            if (ids == null || ids.isEmpty()) {
                throw new ServiceException("删除ID列表不能为空");
            }

            // 获取要删除的快递单号
            List<TrackingNumber> trackingNumbers = this.listByIds(ids);
            if (trackingNumbers.isEmpty()) {
                throw new ServiceException("未找到要删除的快递单号");
            }

            // 删除相关的历史记录和缓存
            for (TrackingNumber trackingNumber : trackingNumbers) {
                String trackingNumberStr = trackingNumber.getTrackingNumber();

                // 删除历史记录
                trackingHistoryMapper.deleteTrackingHistoryByNumber(trackingNumberStr);

                // 清理缓存
                clearAllRelatedCache(trackingNumberStr);
            }

            // 批量删除快递单号记录
            boolean deleted = this.removeByIds(ids);
            if (!deleted) {
                throw new ServiceException("批量删除快递单号失败");
            }

            log.info("批量删除快递单号成功: count={}", ids.size());
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量删除快递单号失败", e);
            throw new ServiceException("批量删除快递单号失败: " + e.getMessage());
        }
    }

    @Override
    public TrackingNumberVO getTrackingNumberById(Long id) {
        try {
            log.info("根据ID获取快递单号详情: id={}", id);

            TrackingNumber trackingNumber = this.getById(id);
            if (trackingNumber == null) {
                throw new ServiceException("快递单号不存在");
            }

            return convertToVO(trackingNumber);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取快递单号详情失败", e);
            throw new ServiceException("获取快递单号详情失败: " + e.getMessage());
        }
    }

    @Override
    public TrackingNumberVO getTrackingNumberByNumber(String trackingNumber) {
        try {
            log.info("根据快递单号获取详情: trackingNumber={}", trackingNumber);

            return trackingNumberMapper.getTrackingNumberDetail(trackingNumber);
        } catch (Exception e) {
            log.error("根据快递单号获取详情失败", e);
            throw new ServiceException("获取快递单号详情失败: " + e.getMessage());
        }
    }

    @Override
    public LogisticsTrackingDto refreshTrackingNumber(Long id) {
        try {
            log.info("刷新快递单号物流信息: id={}", id);

            TrackingNumber trackingNumber = this.getById(id);
            if (trackingNumber == null) {
                throw new ServiceException("快递单号不存在");
            }

            // 查询最新的物流信息
            LogisticsTrackingDto logisticsInfo = logisticsQueryService.queryLogistics(
                    trackingNumber.getTrackingNumber(),
                    trackingNumber.getLogisticsCompany()
            );

            // 更新数据库记录
            updateTrackingNumberFromLogistics(id, logisticsInfo);

            log.info("刷新快递单号物流信息成功: id={}", id);
            return logisticsInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("刷新快递单号物流信息失败", e);
            throw new ServiceException("刷新物流信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据物流信息更新快递单号记录
     */
    private void updateTrackingNumberFromLogistics(Long id, LogisticsTrackingDto logisticsInfo) {
        try {
            TrackingNumber trackingNumber = this.getById(id);
            if (trackingNumber == null) {
                log.warn("快递单号不存在，无法更新: id={}", id);
                return;
            }

            // 更新基本信息
            trackingNumber.setCurrentStatus(logisticsInfo.getStatus());
            trackingNumber.setStatusDescription(logisticsInfo.getStatusDescription());
            trackingNumber.setQuerySuccess(logisticsInfo.isQuerySuccess());
            trackingNumber.setErrorMessage(logisticsInfo.getErrorMessage());
            trackingNumber.setLastQueryTime(new Date());
            trackingNumber.setQueryCount(trackingNumber.getQueryCount() != null ? trackingNumber.getQueryCount() + 1 : 1);
            trackingNumber.setUpdateBy(LoginHelper.getUsername());

            // 更新数据库
            this.updateById(trackingNumber);

            // 保存物流轨迹历史
            saveTrackingHistory(trackingNumber.getTrackingNumber(), logisticsInfo);

        } catch (Exception e) {
            log.error("更新快递单号失败: id={}", id, e);
        }
    }

    /**
     * 保存物流轨迹历史
     */
    private void saveTrackingHistory(String trackingNumber, LogisticsTrackingDto logisticsInfo) {
        try {
            if (logisticsInfo == null || logisticsInfo.getTrackingDetails() == null ||
                logisticsInfo.getTrackingDetails().isEmpty()) {
                return;
            }

            // 先删除旧的历史记录
            trackingHistoryMapper.deleteTrackingHistoryByNumber(trackingNumber);

            // 保存新的历史记录
            List<TrackingHistory> historyList = new ArrayList<>();
            int sortOrder = 1;
            for (LogisticsTrackingDto.TrackingDetail detail : logisticsInfo.getTrackingDetails()) {
                TrackingHistory history = new TrackingHistory();
                history.setTrackingNumber(trackingNumber);
                history.setTrackingTime(detail.getTime());
                history.setDescription(detail.getDescription());
                history.setLocation(detail.getLocation());
                history.setOperator(detail.getOperator());
                history.setOperatorPhone(detail.getOperatorPhone());
                history.setStatus(detail.getStatus());
                history.setTimeStr(detail.getTimeStr());
                history.setSortOrder(sortOrder++);
                history.setIsLatest(sortOrder == 2); // 第一条记录为最新
                history.setCreateBy(LoginHelper.getUsername());
                history.setCreateTime(new Date());
                historyList.add(history);
            }

            if (!historyList.isEmpty()) {
                trackingHistoryMapper.batchInsertTrackingHistory(historyList);
            }
        } catch (Exception e) {
            log.error("保存物流轨迹历史失败: trackingNumber={}", trackingNumber, e);
        }
    }

    @Override
    public List<LogisticsTrackingDto> batchRefreshTrackingNumbers(List<Long> ids) {
        try {
            log.info("批量刷新快递单号物流信息: ids={}", ids);

            if (ids == null || ids.isEmpty()) {
                throw new ServiceException("ID列表不能为空");
            }

            List<LogisticsTrackingDto> results = new ArrayList<>();
            for (Long id : ids) {
                try {
                    LogisticsTrackingDto result = refreshTrackingNumber(id);
                    results.add(result);
                } catch (Exception e) {
                    log.warn("刷新快递单号失败: id={}, error={}", id, e.getMessage());
                    // 创建失败结果
                    LogisticsTrackingDto failedResult = new LogisticsTrackingDto();
                    failedResult.setQuerySuccess(false);
                    failedResult.setErrorMessage("刷新失败: " + e.getMessage());
                    results.add(failedResult);
                }
            }

            log.info("批量刷新快递单号完成: total={}, success={}",
                    ids.size(), results.stream().mapToLong(r -> r.isQuerySuccess() ? 1 : 0).sum());
            return results;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量刷新快递单号失败", e);
            throw new ServiceException("批量刷新失败: " + e.getMessage());
        }
    }

    @Override
    public List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderId(Long purchaseOrderId) {
        try {
            log.info("根据采购订单ID获取快递单号列表: purchaseOrderId={}", purchaseOrderId);
            return trackingNumberMapper.getTrackingNumbersByPurchaseOrderId(purchaseOrderId);
        } catch (Exception e) {
            log.error("根据采购订单ID获取快递单号列表失败", e);
            throw new ServiceException("获取快递单号列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderNo(String purchaseOrderNo) {
        try {
            log.info("根据采购订单号获取快递单号列表: purchaseOrderNo={}", purchaseOrderNo);
            return trackingNumberMapper.getTrackingNumbersByPurchaseOrderNo(purchaseOrderNo);
        } catch (Exception e) {
            log.error("根据采购订单号获取快递单号列表失败", e);
            throw new ServiceException("获取快递单号列表失败: " + e.getMessage());
        }
    }

    @Override
    public Integer autoRefreshTrackingNumbers() {
        try {
            log.info("自动刷新需要更新的快递单号");

            // 获取需要刷新的快递单号（30分钟内未查询过的）
            List<TrackingNumber> needRefreshList = trackingNumberMapper.getTrackingNumbersNeedRefresh(30);
            if (needRefreshList.isEmpty()) {
                log.info("没有需要刷新的快递单号");
                return 0;
            }

            int successCount = 0;
            for (TrackingNumber trackingNumber : needRefreshList) {
                try {
                    LogisticsTrackingDto logisticsInfo = logisticsQueryService.queryLogistics(
                            trackingNumber.getTrackingNumber(),
                            trackingNumber.getLogisticsCompany()
                    );
                    updateTrackingNumberFromLogistics(trackingNumber.getId(), logisticsInfo);
                    successCount++;
                } catch (Exception e) {
                    log.warn("自动刷新快递单号失败: trackingNumber={}, error={}",
                            trackingNumber.getTrackingNumber(), e.getMessage());
                }
            }

            log.info("自动刷新快递单号完成: total={}, success={}", needRefreshList.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("自动刷新快递单号失败", e);
            throw new ServiceException("自动刷新失败: " + e.getMessage());
        }
    }

    @Override
    public List<TrackingNumberVO> getStatusStatistics() {
        try {
            log.info("获取快递单号状态统计");
            return trackingNumberMapper.getStatusStatistics();
        } catch (Exception e) {
            log.error("获取状态统计失败", e);
            throw new ServiceException("获取状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<TrackingNumberVO> searchTrackingNumbers(String keyword) {
        try {
            log.info("搜索快递单号: keyword={}", keyword);

            if (StringUtils.isEmpty(keyword)) {
                return new ArrayList<>();
            }

            return trackingNumberMapper.searchTrackingNumbers(keyword);
        } catch (Exception e) {
            log.error("搜索快递单号失败", e);
            throw new ServiceException("搜索失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncFromPurchaseOrder(Long purchaseOrderId) {
        try {
            log.info("从采购订单同步快递单号信息: purchaseOrderId={}", purchaseOrderId);

            // 这里可以调用采购订单服务获取物流信息
            // 暂时返回成功，具体实现可以根据需要补充

            log.info("从采购订单同步快递单号信息成功: purchaseOrderId={}", purchaseOrderId);
            return true;
        } catch (Exception e) {
            log.error("从采购订单同步快递单号信息失败", e);
            throw new ServiceException("同步失败: " + e.getMessage());
        }
    }

    @Override
    public List<TrackingNumberVO> exportTrackingNumbers(TrackingNumberQueryRequest queryRequest) {
        try {
            log.info("导出快递单号列表: query={}", queryRequest);

            // 查询所有符合条件的数据（不分页）
            Page<TrackingNumberVO> page = new Page<>(1, Integer.MAX_VALUE);
            IPage<TrackingNumberVO> result = trackingNumberMapper.getTrackingNumberListByPage(page, queryRequest);

            log.info("导出快递单号列表成功: count={}", result.getRecords().size());
            return result.getRecords();
        } catch (Exception e) {
            log.error("导出快递单号列表失败", e);
            throw new ServiceException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private TrackingNumberVO convertToVO(TrackingNumber trackingNumber) {
        TrackingNumberVO vo = new TrackingNumberVO();
        BeanUtils.copyProperties(trackingNumber, vo);
        return vo;
    }
}

package com.cpmes.system.serviceJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;

import com.cpmes.system.entity.MaterialSummary;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 物料库存汇总表 服务接口
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
public interface MaterialSummaryService extends IService<MaterialSummary> {

    /**
     * 根据物料ID、类型和日期查询库存记录
     */
    MaterialSummary getByMaterialAndDate(Integer materialId, String materialType, LocalDate date);

    /**
     * 根据日期获取所有物料库存记录
     */
    List<MaterialSummary> getByDate(LocalDate date);

    /**
     * 批量删除材料
     * @param ids 材料ID列表
     * @return 是否成功
     */
    boolean batchDeleteByIds(List<Integer> ids);

    /**
     * 获取所有物料的最新库存记录
     */
    List<MaterialSummary> getLatestInventory();

    /**
     * 获取指定物料的最新库存记录
     */
    MaterialSummary getLatestByMaterialId(Integer materialId, String materialType);


}

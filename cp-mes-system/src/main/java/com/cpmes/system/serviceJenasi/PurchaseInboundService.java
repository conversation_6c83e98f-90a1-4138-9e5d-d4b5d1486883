package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.PurchaseInbound;
import com.cpmes.system.entity.dto.purcgaseInbound.PurchaseInboundAddRequest;
import com.cpmes.system.entity.dto.purcgaseInbound.PurchaseInboundQueryRequest;

/**
* <AUTHOR>
* @description 针对表【purchase_inbound(采购订单入库表)】的数据库操作Service
* @createDate 2025-06-17 09:23:30
*/
public interface PurchaseInboundService extends IService<PurchaseInbound> {

    /**
     * 添加采购订单入库信息
     */
    PurchaseInbound addPurchaseInbound(PurchaseInboundAddRequest request);

    /**
     * 分页获取采购订单入库信息
     */
    Page<PurchaseInbound> getPurchaseInboundList(Page<PurchaseInbound> page, PurchaseInboundQueryRequest request);

    /**
     * 获取插叙条件
     */
    QueryWrapper<PurchaseInbound> getQueryWrapper(PurchaseInboundQueryRequest request);

    /**
     * 绑定采购单号和区域编码
     * 将已存在的采购单号与区域编码进行绑定（更新操作）
     * 绑定成功后状态从0（待入库）变为1（已入库）
     * @param purchaseOrderNo 采购单号（数据库中已存在）
     * @param zoneCode 区域编码（需要绑定的新区域）
     * @param operator 操作人（记录操作人员）
     * @return 更新后的采购入库记录
     */
    PurchaseInbound bindPurchaseZone(String purchaseOrderNo, String zoneCode, String operator);

    /**
     * 更新采购入库记录的二维码内容
     * 在打印二维码后调用此方法保存二维码内容到数据库
     * @param purchaseOrderNo 采购单号（数据库中已存在）
     * @param qrCode 二维码内容
     * @param operator 操作人
     * @return 更新后的采购入库记录
     */
    PurchaseInbound updateQrCode(String purchaseOrderNo, String qrCode, String operator);

    /**
     * 采购入库并自动增加库存
     * 集成区域绑定和库存入库的完整业务流程
     * @param purchaseOrderNo 采购单号
     * @param zoneCode 区域编码
     * @param operator 操作人
     * @return 操作结果，包含采购入库记录和库存操作结果
     */
    java.util.Map<String, Object> purchaseInboundWithInventory(String purchaseOrderNo, String zoneCode, String operator);

    /**
     * 根据采购单号获取采购入库记录
     * 用于打印二维码时获取批次号等信息
     * @param purchaseOrderNo 采购单号
     * @return 采购入库记录，如果不存在返回null
     */
    PurchaseInbound getByPurchaseOrderNo(String purchaseOrderNo);

}

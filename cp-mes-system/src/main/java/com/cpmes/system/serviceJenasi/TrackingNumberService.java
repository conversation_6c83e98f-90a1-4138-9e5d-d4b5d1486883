package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.TrackingNumber;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberAddRequest;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberQueryRequest;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberUpdateRequest;
import com.cpmes.system.entity.vo.TrackingNumberVO;

import java.util.List;

/**
 * 快递单号管理服务接口
 *
 * <AUTHOR> System
 * @since 2025-01-21
 */
public interface TrackingNumberService extends IService<TrackingNumber> {

    /**
     * 分页查询快递单号列表
     *
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    IPage<TrackingNumberVO> getTrackingNumberListByPage(Page<TrackingNumberVO> page,
                                                       TrackingNumberQueryRequest queryRequest);

    /**
     * 新增快递单号
     *
     * @param addRequest 新增请求
     * @return 是否成功
     */
    Boolean addTrackingNumber(TrackingNumberAddRequest addRequest);

    /**
     * 更新快递单号
     *
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    Boolean updateTrackingNumber(TrackingNumberUpdateRequest updateRequest);

    /**
     * 删除快递单号
     *
     * @param id 主键ID
     * @return 是否成功
     */
    Boolean deleteTrackingNumber(Long id);

    /**
     * 根据快递单号删除
     *
     * @param trackingNumber 快递单号
     * @return 是否成功
     */
    Boolean deleteTrackingNumberByNumber(String trackingNumber);

    /**
     * 批量删除快递单号
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    Boolean batchDeleteTrackingNumbers(List<Long> ids);

    /**
     * 根据ID获取快递单号详情
     *
     * @param id 主键ID
     * @return 快递单号详情
     */
    TrackingNumberVO getTrackingNumberById(Long id);

    /**
     * 根据快递单号获取详情
     *
     * @param trackingNumber 快递单号
     * @return 快递单号详情
     */
    TrackingNumberVO getTrackingNumberByNumber(String trackingNumber);

    /**
     * 刷新快递单号物流信息
     *
     * @param id 主键ID
     * @return 物流信息
     */
    LogisticsTrackingDto refreshTrackingNumber(Long id);

    /**
     * 批量刷新快递单号物流信息
     *
     * @param ids 主键ID列表
     * @return 物流信息列表
     */
    List<LogisticsTrackingDto> batchRefreshTrackingNumbers(List<Long> ids);

    /**
     * 根据采购订单ID获取快递单号列表
     *
     * @param purchaseOrderId 采购订单ID
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 根据采购订单号获取快递单号列表
     *
     * @param purchaseOrderNo 采购订单号
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * 自动刷新需要更新的快递单号
     *
     * @return 刷新成功的数量
     */
    Integer autoRefreshTrackingNumbers();

    /**
     * 获取状态统计
     *
     * @return 状态统计列表
     */
    List<TrackingNumberVO> getStatusStatistics();

    /**
     * 搜索快递单号
     *
     * @param keyword 关键词
     * @return 搜索结果
     */
    List<TrackingNumberVO> searchTrackingNumbers(String keyword);

    /**
     * 从采购订单同步快递单号信息
     *
     * @param purchaseOrderId 采购订单ID
     * @return 是否成功
     */
    Boolean syncFromPurchaseOrder(Long purchaseOrderId);

    /**
     * 导出快递单号列表
     *
     * @param queryRequest 查询条件
     * @return 导出数据
     */
    List<TrackingNumberVO> exportTrackingNumbers(TrackingNumberQueryRequest queryRequest);
}

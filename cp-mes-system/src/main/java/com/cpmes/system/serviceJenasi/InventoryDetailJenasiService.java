package com.cpmes.system.serviceJenasi;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.InventoryDetailJenasi;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【inventory_detail(库存明细表)】的数据库操作Service
* @createDate 2025-06-18 16:21:46
*/
public interface InventoryDetailJenasiService extends IService<InventoryDetailJenasi> {

    /**
     * 查询库存信息
     * @param materialType
     * @param materialName
     * @return
     */
    List<InventoryDetailJenasi> searchInventory(String materialType, String materialName);

    /**
     * 根据物品ID查询库存信息
     */
    InventoryDetailJenasi getInventoryByItemId(String materialId);

}

package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cpmes.system.entity.RawMaterialWarehouse;
import com.cpmes.system.entity.SemiFinishedProduct;
import com.cpmes.system.mapperJenasi.SemiFinishedProductMapper;
import com.cpmes.system.serviceJenasi.InOutRequestService;
import com.cpmes.system.serviceJenasi.SemiFinishedProductService;
import com.cpmes.system.vo.SemiFinishedProductVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS("slave")
public class SemiFinishedProductServiceImpl extends ServiceImpl<SemiFinishedProductMapper, SemiFinishedProduct> implements SemiFinishedProductService {

    @Resource
    @Lazy
    private InOutRequestService inOutRequestService;

    @Override
    public boolean batchDeleteByIds(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 预先验证所有ID是否存在，只删除存在的ID
        List<Integer> existingIds = this.lambdaQuery()
                .in(SemiFinishedProduct::getSemiProductId, ids)
                .list()
                .stream()
                .map(SemiFinishedProduct::getSemiProductId)
                .collect(Collectors.toList());

        if (existingIds.isEmpty()) {
            return false; // 没有找到任何匹配的记录
        }

        // 只删除存在的记录
        return this.removeByIds(existingIds);
    }

    /**
     * 出库
     * @param semiProductId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean outbound(Integer semiProductId, Integer quantity, String userName) {
        SemiFinishedProduct semiFinishedProduct = this.getById(semiProductId);
        if (semiFinishedProduct == null){
            throw new RuntimeException("未找到该半成品");
        }
        //出库
        int outbound = this.baseMapper.outbound(semiProductId, quantity);
        if (outbound <  0){
            throw new RuntimeException("出库失败");
        }
        //新增出库记录
        String semiProductName = semiFinishedProduct.getSemiProductName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 1;
        boolean result = inOutRequestService.inOutbound(semiProductId, quantity, userName, semiProductName, "一级半成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    /**
     * 入库
     * @param semiProductId
     * @param quantity
     * @param userName
     * @return
     */
    @Override
    public boolean inbound(Integer semiProductId, Integer quantity, String userName) {
        SemiFinishedProduct semiFinishedProduct = this.getById(semiProductId);
        if (semiFinishedProduct == null){
            throw new RuntimeException("未找到该半成品");
        }
        //入库
        int inbound = this.baseMapper.inbound(semiProductId, quantity);
        if (inbound <  0){
            throw new RuntimeException("出库失败");
        }
        //新增出库记录
        String semiProductName = semiFinishedProduct.getSemiProductName();
        // type用来判断类型，1为出库，0为入库
        Integer type = 0;
        boolean result = inOutRequestService.inOutbound(semiProductId, quantity, userName, semiProductName, "一级半成品",type);
        if (!result){
            throw new RuntimeException("添加记录失败");
        }
        return true;
    }

    /**
     * 获取一级半成品仓库统计信息
     */
    @Override
    public Object getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取所有一级半成品记录
        List<SemiFinishedProduct> allMaterials = this.list();
        
        // 总库存数量（所有物料的库存总和）
        Long totalStock = allMaterials.stream()
                .mapToLong(material -> material.getCurrentStock() != null ? material.getCurrentStock() : 0L)
                .sum();
        
        // 物料种类数（物料总数）
        int totalMaterials = allMaterials.size();
        
        // 低库存物料数（currentStock <= fields2安全库存）
        long lowStock = allMaterials.stream()
                .filter(material -> {
                    Integer currentStock = material.getCurrentStock();
                    String fields2 = material.getFields2(); // 安全库存
                    if (currentStock == null || fields2 == null || fields2.isEmpty()) {
                        return false;
                    }
                    try {
                        Integer safetyStock = Integer.parseInt(fields2);
                        return currentStock <= safetyStock;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                })
                .count();
        
        // 需要采购的物料数（与低库存数相同）
        long needPurchase = lowStock;
        
        statistics.put("totalStock", totalStock);
        statistics.put("totalMaterials", totalMaterials);
        statistics.put("lowStock", lowStock);
        statistics.put("needPurchase", needPurchase);
        
        return statistics;
    }

    @Override
    public Page<SemiFinishedProductVO> selectSemiFinishedProductPage(Page<SemiFinishedProductVO> page, SemiFinishedProduct semiFinishedProduct, String zoneCode, String zoneName, String sortField, String sortOrder) {
        QueryWrapper<SemiFinishedProduct> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(semiFinishedProduct.getSemiProductName()), "sfp.semi_product_name", semiFinishedProduct.getSemiProductName());
        wrapper.eq(StringUtils.isNotBlank(semiFinishedProduct.getFields3()), "sfp.fields3", semiFinishedProduct.getFields3());
        
        // 当区域参数为空时，使用不关联区域的查询避免数据重复
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            wrapper.like(StringUtils.isNotBlank(zoneCode), "wz.zone_code", zoneCode);
            wrapper.like(StringUtils.isNotBlank(zoneName), "wz.zone_name", zoneName);
        }
        
        // 处理排序
        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            String column = StringUtils.camelToUnderline(sortField);
            String sortColumn = "sfp." + column;

            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortColumn);
            } else {
                wrapper.orderByDesc(sortColumn);
            }
        } else {
            // 默认排序
            wrapper.orderByDesc("sfp.updated_time");
        }

        // 根据是否有区域条件选择不同的查询方法
        if (StringUtils.isNotBlank(zoneCode) || StringUtils.isNotBlank(zoneName)) {
            return this.baseMapper.selectPageWithZone(page, wrapper);
        } else {
            // 使用不关联区域的查询方法，避免数据重复
            return this.baseMapper.selectPageWithoutZone(page, wrapper);
        }
    }
}

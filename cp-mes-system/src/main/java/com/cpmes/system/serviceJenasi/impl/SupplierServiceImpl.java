package com.cpmes.system.serviceJenasi.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.system.entity.Supplier;
import com.cpmes.system.entity.dto.supplier.SupplierAddRequest;
import com.cpmes.system.mapperJenasi.SupplierMapper;
import com.cpmes.system.serviceJenasi.SupplierService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【supplier(供应商表)】的数据库操作Service实现
* @createDate 2025-06-11 10:33:37
*/
@Service
@DS("slave")
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper, Supplier>
    implements SupplierService {

    /**
     * 添加或修改供应商
     * @param supplierAddRequest
     * @return
     */
    @Override
    public Supplier addSupplier(SupplierAddRequest supplierAddRequest) {
        //简单校验参数
        if(supplierAddRequest == null){
            throw new RuntimeException("参数错误");
        }
        //通过id判断是添加还是修改
        Long supplierId = null;
        if(supplierAddRequest.getId() != null){
            supplierId = supplierAddRequest.getId();
        }
        Supplier supplier = new Supplier();
        //判断已有的供应商是否存在
        Supplier existingSupplier  = this.lambdaQuery().eq(Supplier::getSupplierName, supplierAddRequest.getSupplierName()).one();
        if (existingSupplier != null){
            throw new RuntimeException("供应商已存在");
        }
        supplier.setSupplierName(supplierAddRequest.getSupplierName());
        if (supplierId != null){
            supplier.setId(supplierId);
            supplier.setUpdateTime(new Date());
        }
        boolean result = this.saveOrUpdate(supplier);
        if(!result){
            throw new RuntimeException("数据操作失败");
        }
        return this.getById(supplier.getId());
    }


    /**
     * 分页获取供应商列表
     * @param page
     * @param supplierName
     * @return
     */
    @Override
    public Page<Supplier> getSupplierList(Page<Supplier> page, String supplierName) {
        QueryWrapper<Supplier> supplierQueryWrapper = new QueryWrapper<>();
        supplierQueryWrapper.like(StrUtil.isNotBlank(supplierName),"supplier_name", supplierName);
        return this.page(page,supplierQueryWrapper);
    }
}





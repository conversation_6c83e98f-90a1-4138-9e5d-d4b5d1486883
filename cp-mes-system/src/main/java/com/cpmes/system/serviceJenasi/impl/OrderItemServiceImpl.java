package com.cpmes.system.serviceJenasi.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.entity.OrderItem;
import com.cpmes.system.entity.Orders;
import com.cpmes.system.entity.dto.orderItem.OrderItemCreateRequest;
import com.cpmes.system.entity.dto.orderItem.ProductStyleCountDTO;
import com.cpmes.system.mapperJenasi.OrderItemMapper;
import com.cpmes.system.serviceJenasi.OrderItemService;
import com.cpmes.system.serviceJenasi.OrdersService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【order_item(订单明细表)】的数据库操作Service实现
* @createDate 2025-06-18 15:28:44
*/
@Service
@DS("slave")
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem>
    implements OrderItemService {

    @Resource
    private OrdersService ordersService;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OrderItem> createOrderItems(OrderItemCreateRequest request) {
        // 1. 校验订单是否存在
        Orders order = ordersService.getById(request.getOrderId());
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 2. 构建订单明细列表
        List<OrderItem> orderItems = request.getItems().stream().map(itemDetail -> {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(request.getOrderId());
            orderItem.setProductId(itemDetail.getProductId());
            orderItem.setProductName(itemDetail.getProductName());
            orderItem.setStyleId(itemDetail.getStyleId());
            orderItem.setStyleName(itemDetail.getStyleName());
            orderItem.setBoardType(itemDetail.getBoardType());
            orderItem.setQuantity(itemDetail.getQuantity());
            orderItem.setInventory(itemDetail.getInventory());
            orderItem.setCreateTime(new Date());
            orderItem.setDeleted(0); // 默认未删除
            return orderItem;
        }).collect(Collectors.toList());

        // 3. 批量保存
        this.saveBatch(orderItems);

        return orderItems;
    }


    /**
     * 统计工单中产品品款式的数量
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ProductStyleCountDTO> countByProductAndStyle(String startTime, String endTime) {
        QueryWrapper<OrderItem> wrapper = new QueryWrapper<>();
        wrapper.eq("deleted", 0);
        if (startTime != null && !startTime.isEmpty() && endTime != null && !endTime.isEmpty()) {
            LocalDateTime start = LocalDateTime.parse(startTime, FORMATTER);
            LocalDateTime end = LocalDateTime.parse(endTime, FORMATTER);
            wrapper.between("create_time", start, end);
        }
        wrapper.select("product_name", "style_name", "SUM(quantity) AS totalQuantity")
            .groupBy("product_name", "style_name")
            .orderByDesc("totalQuantity");

        List<Map<String, Object>> maps = this.baseMapper.selectMaps(wrapper);

        return maps.stream().map(map -> {
            ProductStyleCountDTO dto = new ProductStyleCountDTO();
            dto.setProductName((String) map.get("product_name"));
            dto.setStyleName((String) map.get("style_name"));
            //在 PostgreSQL 里，字段别名默认会变成小写。换成totalQuantity出错
            Object qtyObj = map.get("totalquantity");
            if (qtyObj == null) {
                dto.setTotalQuantity(0);
            } else if (qtyObj instanceof Number) {
                dto.setTotalQuantity(((Number) qtyObj).intValue());
            } else {
                dto.setTotalQuantity(Integer.parseInt(qtyObj.toString()));
            }
            return dto;
        }).collect(Collectors.toList());
    }

}





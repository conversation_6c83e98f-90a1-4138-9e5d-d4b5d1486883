package com.cpmes.system.serviceJenasi;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.Supplier;
import com.cpmes.system.entity.dto.supplier.SupplierAddRequest;

/**
* <AUTHOR>
* @description 针对表【supplier(供应商表)】的数据库操作Service
* @createDate 2025-06-11 10:33:37
*/
public interface SupplierService extends IService<Supplier> {

    /**
     * 新增或修改供应商
     */
    Supplier addSupplier(SupplierAddRequest supplierAddRequest);

    /**
     * 分页获取供应商列表
     */
    Page<Supplier> getSupplierList(Page<Supplier> page,String supplierName);

}

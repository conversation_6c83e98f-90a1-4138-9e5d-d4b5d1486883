package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.ProductWarehouse;
import com.cpmes.system.vo.ProductWarehouseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
@DS("slave") // 确保使用slave数据源(PostgreSQL)
public interface ProductWarehouseMapper extends BaseMapper<ProductWarehouse> {

    /**
     * 分页查询成品仓库信息，并关联区域信息
     *
     * @param page          分页参数
     * @param queryWrapper  查询条件
     * @return 分页结果
     */
    Page<ProductWarehouseVO> selectPageWithZone(Page<ProductWarehouseVO> page, @Param(Constants.WRAPPER) Wrapper<ProductWarehouse> queryWrapper);

    /**
     * 入库
     * @param productId
     * @param inboundQty
     * @return
     */
    @Update(
        "UPDATE storage.product_warehouse " +
            "SET " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity + #{inboundQty} " +
            "    ELSE #{inboundQty} " +
            "  END, " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock + #{inboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE product_id = #{productId}"
    )
    int inbound(@Param("productId") Integer productId, @Param("inboundQty") Integer inboundQty);


    /**
     *  出库
     * @param productId
     * @param outboundQty
     * @return
     */
    @Update(
        "UPDATE storage.product_warehouse " +
            "SET " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity + #{outboundQty} " +
            "    ELSE #{outboundQty} " +
            "  END, " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock - #{outboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE product_id = #{productId}"
    )
    int outbound(@Param("productId") Integer productId, @Param("outboundQty") Integer outboundQty);
}

package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.BomPickRecord;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【bom_pick_record(BOM领料记录表)】的数据库操作Mapper
* @createDate 2025-08-02 08:42:22
* @Entity generator.domain.BomPickRecord
*/
@Mapper
@DS("slave")
public interface BomPickRecordMapper extends BaseMapper<BomPickRecord> {

}





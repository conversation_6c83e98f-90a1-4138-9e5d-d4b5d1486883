package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.Item;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【item(物品表)】的数据库操作Mapper
* @createDate 2025-06-11 10:33:37
* @Entity generator.domain.Item
*/
@Mapper
@DS("slave")
public interface ItemMapper extends BaseMapper<Item> {

}





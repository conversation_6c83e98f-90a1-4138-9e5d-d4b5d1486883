package com.cpmes.system.mapperJenasi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.TrackingHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物流轨迹历史Mapper接口
 *
 * <AUTHOR> System
 * @since 2025-01-21
 */
@Mapper
public interface TrackingHistoryMapper extends BaseMapper<TrackingHistory> {

    /**
     * 根据快递单号获取物流轨迹历史
     *
     * @param trackingNumber 快递单号
     * @return 物流轨迹历史列表
     */
    List<TrackingHistory> getTrackingHistoryByNumber(@Param("trackingNumber") String trackingNumber);

    /**
     * 根据快递单号删除物流轨迹历史
     *
     * @param trackingNumber 快递单号
     * @return 删除数量
     */
    int deleteTrackingHistoryByNumber(@Param("trackingNumber") String trackingNumber);

    /**
     * 批量插入物流轨迹历史
     *
     * @param historyList 物流轨迹历史列表
     * @return 插入数量
     */
    int batchInsertTrackingHistory(@Param("historyList") List<TrackingHistory> historyList);

    /**
     * 获取最新的物流轨迹
     *
     * @param trackingNumber 快递单号
     * @return 最新的物流轨迹
     */
    TrackingHistory getLatestTrackingHistory(@Param("trackingNumber") String trackingNumber);

    /**
     * 更新最新状态标记
     *
     * @param trackingNumber 快递单号
     * @param latestId 最新记录ID
     * @return 更新数量
     */
    int updateLatestFlag(@Param("trackingNumber") String trackingNumber, @Param("latestId") Long latestId);

    /**
     * 根据快递单号和状态获取轨迹数量
     *
     * @param trackingNumber 快递单号
     * @param status 状态
     * @return 数量
     */
    int countByTrackingNumberAndStatus(@Param("trackingNumber") String trackingNumber,
                                      @Param("status") String status);

    /**
     * 获取快递单号的轨迹总数
     *
     * @param trackingNumber 快递单号
     * @return 轨迹总数
     */
    int countByTrackingNumber(@Param("trackingNumber") String trackingNumber);

    /**
     * 清理过期的轨迹历史
     *
     * @param daysAgo 多少天前
     * @return 清理数量
     */
    int cleanupExpiredHistory(@Param("daysAgo") Integer daysAgo);

    /**
     * 批量删除多个快递单号的轨迹历史
     *
     * @param trackingNumbers 快递单号列表
     * @return 删除数量
     */
    int batchDeleteTrackingHistory(@Param("trackingNumbers") List<String> trackingNumbers);
}

package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.InOutRequest;
import com.cpmes.system.entity.vo.InOutCensusVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 出入库申请记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Mapper
@DS("slave") // 确保使用slave数据源(PostgresSQL)
public interface InOutRequestMapper extends BaseMapper<InOutRequest> {

    @Insert("INSERT INTO storage.in_out_request (material_id, material_name, material_type, quantity, type, user_name, created_time) " +
            "VALUES (#{materialId}, #{materialName}, #{materialType}, #{quantity}, #{type}, #{userName}, #{createdTime})")
    int insertInOutRequest(@Param("materialId") Integer materialId,
                          @Param("materialName") String materialName,
                          @Param("materialType") String materialType,
                          @Param("quantity") Integer quantity,
                          @Param("type") String type,
                          @Param("userName") String userName,
                          @Param("createdTime") String createdTime);


    @Select("SELECT " +
        "material_name AS materialName, " +
        "material_type AS materialType, " +
        "application_type AS applicationType, " +
        "SUM(quantity) AS totalQuantity " +
        "FROM storage.in_out_request " +
        "WHERE EXTRACT(YEAR FROM request_time) = #{year} " +
        "AND EXTRACT(MONTH FROM request_time) = #{month} " +
        "GROUP BY material_name, material_type, application_type " +
        "ORDER BY material_name, material_type, application_type")
    List<InOutCensusVO> analyzeCurrentMonthInOut(@Param("year") int year, @Param("month") int month);


    Page<InOutCensusVO> selectFilteredMaterialSummary(
        Page<InOutCensusVO> page,
        @Param("year") Integer year,
        @Param("month") Integer month,
        @Param("materialName") String materialName,
        @Param("materialType") String materialType
    );

}

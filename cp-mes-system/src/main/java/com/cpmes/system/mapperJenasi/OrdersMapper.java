package com.cpmes.system.mapperJenasi;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.Orders;
import com.cpmes.system.entity.dto.orders.OrderDetailQueryRequest;
import com.cpmes.system.entity.vo.OrderDetailRawVO;
import com.cpmes.system.entity.vo.OrderDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【orders(订单主表)】的数据库操作Mapper
* @createDate 2025-06-18 15:26:58
* @Entity generator.domain.Orders
*/

@Mapper
@DS("slave")
public interface OrdersMapper extends BaseMapper<Orders> {

    /**
     * 分页查询工单详细信息（原始数据）
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 原始查询结果
     */
    Page<OrderDetailRawVO> selectOrderDetailRawPage(Page<OrderDetailRawVO> page, @Param("req") OrderDetailQueryRequest queryRequest);

    /**
     * 根据工单ID查询详细信息（原始数据）
     * @param orderId 工单ID
     * @return 原始查询结果列表
     */
    List<OrderDetailRawVO> selectOrderDetailRawByOrderId(@Param("orderId") Long orderId);
}





package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.InventoryDetailJenasi;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【inventory_detail(库存明细表)】的数据库操作Mapper
* @createDate 2025-06-18 16:21:46
* @Entity generator.domain.InventoryDetail
*/

@Mapper
@DS("slave")
public interface InventoryDetailJenasiMapper extends BaseMapper<InventoryDetailJenasi> {

    /**
     * 根据类型和名称查询库存明细
     * @param materialType
     * @param materialName
     * @return
     */
    List<InventoryDetailJenasi> queryByTypeAndName(@Param("materialType") String materialType,
                                             @Param("materialName") String materialName);
}





package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cpmes.system.entity.ComponentWarehouse;
import com.cpmes.system.vo.ComponentWarehouseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
@DS("slave") // 确保使用slave数据源(PostgreSQL)
public interface ComponentWarehouseMapper extends BaseMapper<ComponentWarehouse> {
    // 不添加任何自定义方法，使用BaseMapper已有的方法

    /**
     * 分页查询零部件仓库信息，并关联区域信息
     *
     * @param page          分页参数
     * @param queryWrapper  查询条件
     * @return 分页结果
     */
    Page<ComponentWarehouseVO> selectPageWithZone(Page<ComponentWarehouseVO> page, @Param(Constants.WRAPPER) Wrapper<ComponentWarehouse> queryWrapper);

    /**
     * 分页查询零部件仓库信息，不关联区域信息（避免数据重复）
     *
     * @param page          分页参数
     * @param queryWrapper  查询条件
     * @return 分页结果
     */
    Page<ComponentWarehouseVO> selectPageWithoutZone(Page<ComponentWarehouseVO> page, @Param(Constants.WRAPPER) Wrapper<ComponentWarehouse> queryWrapper);

    /**
     * 入库
     * @param componentId
     * @param inboundQty
     * @return
     */
    @Update(
        "UPDATE storage.component_warehouse " +
            "SET " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity + #{inboundQty} " +
            "    ELSE #{inboundQty} " +
            "  END, " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock + #{inboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE component_id = #{componentId}"
    )
    int inbound(@Param("componentId") Integer componentId, @Param("inboundQty") Integer inboundQty);


    /**
     *  出库
     * @param componentId
     * @param outboundQty
     * @return
     */
    @Update(
        "UPDATE storage.component_warehouse " +
            "SET " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity + #{outboundQty} " +
            "    ELSE #{outboundQty} " +
            "  END, " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock - #{outboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE component_id = #{componentId}"
    )
    int outbound(@Param("componentId") Integer componentId, @Param("outboundQty") Integer outboundQty);
}

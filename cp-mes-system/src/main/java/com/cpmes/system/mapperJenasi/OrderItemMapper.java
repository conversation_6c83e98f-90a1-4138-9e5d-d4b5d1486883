package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.OrderItem;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【order_item(订单明细表)】的数据库操作Mapper
* @createDate 2025-06-18 15:28:44
* @Entity generator.domain.OrderItem
*/

@Mapper
@DS("slave")
public interface OrderItemMapper extends BaseMapper<OrderItem> {

}





package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.PurchaseOrder;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseOrderQueryRequest;
import com.cpmes.system.entity.vo.PurchaseOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【purchase_order(采购订单表)】的数据库操作Mapper
* @createDate 2025-06-11 10:33:37
* @Entity generator.domain.PurchaseOrder
*/
@Mapper
@DS("slave")
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrder> {

    /**
     * 获取采购订单详情
     */
    Page<PurchaseOrderVO> getPurchaseOrderDetail(Page<PurchaseOrder> page, @Param("purchaseNo") String purchaseNo,
                                                 @Param("status") Integer status,
                                                 @Param("itemName") String itemName,
                                                 @Param("supplierName") String supplierName,
                                                 @Param("applicant") String applicant,
                                                 @Param("approver") String approver);

    /**
     * 按条件获取采购订单详情列表（不分页）
     */
    List<PurchaseOrderVO> getPurchaseOrderDetailByCondition(@Param("purchaseNo") String purchaseNo,
                                                            @Param("status") Integer status,
                                                            @Param("itemName") String itemName,
                                                            @Param("supplierName") String supplierName,
                                                            @Param("applicant") String applicant,
                                                            @Param("approver") String approver);

    /**
     * 根据ID获取采购订单详情（包含供应商名称）
     */
    PurchaseOrderVO getPurchaseOrderVOById(@Param("id") Long id);
}





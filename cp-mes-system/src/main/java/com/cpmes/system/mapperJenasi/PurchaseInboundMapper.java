package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.PurchaseInbound;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【purchase_inbound(采购订单入库表)】的数据库操作Mapper
* @createDate 2025-06-17 09:23:30
* @Entity generator.domain.PurchaseInbound
*/
@Mapper
@DS("slave")
public interface PurchaseInboundMapper extends BaseMapper<PurchaseInbound> {

}





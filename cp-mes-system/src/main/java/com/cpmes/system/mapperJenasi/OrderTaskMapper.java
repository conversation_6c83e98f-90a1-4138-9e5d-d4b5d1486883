package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.OrderTask;
import com.cpmes.system.entity.dto.orderTask.OrderTaskDetailQueryRequest;
import com.cpmes.system.entity.vo.OrderTaskDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【order_task(订单任务表)】的数据库操作Mapper
* @createDate 2025-06-21 11:08:59
* @Entity generator.domain.OrderTask
*/
@DS("slave")
@Mapper
public interface OrderTaskMapper extends BaseMapper<OrderTask> {

    /**
     * 分页查询详细任务信息
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<OrderTaskDetailVO> selectOrderTaskDetailPage(Page<OrderTaskDetailVO> page, 
                                                      @Param("query") OrderTaskDetailQueryRequest queryRequest);

}





package com.cpmes.system.mapperJenasi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.TrackingNumber;
import com.cpmes.system.entity.dto.trackingNumber.TrackingNumberQueryRequest;
import com.cpmes.system.entity.vo.TrackingNumberVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 快递单号Mapper接口
 * 
 * <AUTHOR> System
 * @since 2025-01-21
 */
@Mapper
public interface TrackingNumberMapper extends BaseMapper<TrackingNumber> {

    /**
     * 分页查询快递单号列表
     * 
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    IPage<TrackingNumberVO> getTrackingNumberListByPage(Page<TrackingNumberVO> page, 
                                                       @Param("query") TrackingNumberQueryRequest queryRequest);

    /**
     * 根据快递单号获取详情
     * 
     * @param trackingNumber 快递单号
     * @return 快递单号详情
     */
    TrackingNumberVO getTrackingNumberDetail(@Param("trackingNumber") String trackingNumber);

    /**
     * 根据采购订单ID获取快递单号列表
     * 
     * @param purchaseOrderId 采购订单ID
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderId(@Param("purchaseOrderId") Long purchaseOrderId);

    /**
     * 根据采购订单号获取快递单号列表
     * 
     * @param purchaseOrderNo 采购订单号
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbersByPurchaseOrderNo(@Param("purchaseOrderNo") String purchaseOrderNo);

    /**
     * 获取需要刷新的快递单号列表
     * 
     * @param minutesAgo 多少分钟前
     * @return 需要刷新的快递单号列表
     */
    List<TrackingNumber> getTrackingNumbersNeedRefresh(@Param("minutesAgo") Integer minutesAgo);

    /**
     * 获取状态统计
     * 
     * @return 状态统计列表
     */
    List<TrackingNumberVO> getStatusStatistics();

    /**
     * 搜索快递单号
     * 
     * @param keyword 关键词
     * @return 搜索结果
     */
    List<TrackingNumberVO> searchTrackingNumbers(@Param("keyword") String keyword);

    /**
     * 批量更新查询状态
     * 
     * @param trackingNumbers 快递单号列表
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 更新数量
     */
    int batchUpdateQueryStatus(@Param("trackingNumbers") List<String> trackingNumbers,
                              @Param("status") String status,
                              @Param("errorMessage") String errorMessage);

    /**
     * 根据状态获取快递单号数量
     * 
     * @param status 状态
     * @return 数量
     */
    int countByStatus(@Param("status") String status);

    /**
     * 获取最近查询的快递单号
     * 
     * @param limit 限制数量
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getRecentTrackingNumbers(@Param("limit") Integer limit);

    /**
     * 清理过期的快递单号记录
     * 
     * @param daysAgo 多少天前
     * @return 清理数量
     */
    int cleanupExpiredTrackingNumbers(@Param("daysAgo") Integer daysAgo);
}

package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.cpmes.system.entity.MaterialSummary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 物料库存汇总表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-04-03
 */
@Mapper
@DS("slave")
public interface MaterialSummaryMapper extends BaseMapper<MaterialSummary> {

    /**
     * 根据物料ID、物料类型和日期查询库存记录
     */
    @Select("SELECT * FROM storage.material_summary WHERE material_id = #{materialId} " +
            "AND material_type = #{materialType} AND stock_date = #{stockDate} LIMIT 1")
    MaterialSummary findByMaterialAndDate(@Param("materialId") Integer materialId,
                                         @Param("materialType") String materialType,
                                         @Param("stockDate") LocalDate stockDate);

    @Select("SELECT * FROM storage.material_summary WHERE material_name LIKE CONCAT('%', #{materialName}, '%')")
    List<MaterialSummary> findByMaterialNameLike(@Param("materialName") String materialName);
}

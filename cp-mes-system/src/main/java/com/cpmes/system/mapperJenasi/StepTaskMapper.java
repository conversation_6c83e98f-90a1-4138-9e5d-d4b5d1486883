package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.StepTask;
import com.cpmes.system.entity.dto.stepTask.DefectStatDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【step_task(任务详细表：记录每个任务对应的工序执行信息)】的数据库操作Mapper
* @createDate 2025-06-25 13:59:44
* @Entity generator.domain.StepTask
*/
@Mapper
@DS("slave")
public interface StepTaskMapper extends BaseMapper<StepTask> {

    List<DefectStatDTO> getDefectStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

}





package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.Supplier;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【supplier(供应商表)】的数据库操作Mapper
* @createDate 2025-06-11 10:33:37
* @Entity generator.domain.Supplier
*/
@Mapper
@DS("slave")
public interface SupplierMapper extends BaseMapper<Supplier> {

}





package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.system.entity.SemiFinishedProductTwo;
import com.cpmes.system.vo.SemiFinishedProductTwoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
@DS( "slave")
public interface SemiFinishedProductTwoMapper extends BaseMapper<SemiFinishedProductTwo> {
    /**
     * 分页查询二级半成品仓库信息，并关联区域信息
     *
     * @param page          分页参数
     * @param queryWrapper  查询条件
     * @return 分页结果
     */
    Page<SemiFinishedProductTwoVO> selectPageWithZone(Page<SemiFinishedProductTwoVO> page, @Param(Constants.WRAPPER) Wrapper<SemiFinishedProductTwo> queryWrapper);

    /**
     * 分页查询二级半成品仓库信息，不关联区域信息（避免数据重复）
     *
     * @param page          分页参数
     * @param queryWrapper  查询条件
     * @return 分页结果
     */
    Page<SemiFinishedProductTwoVO> selectPageWithoutZone(Page<SemiFinishedProductTwoVO> page, @Param(Constants.WRAPPER) Wrapper<SemiFinishedProductTwo> queryWrapper);

    /**
     * 入库
     * @param semiProductTwoId
     * @param inboundQty
     * @return
     */
    @Update(
        "UPDATE storage.semi_finished_product_two " +
            "SET " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity + #{inboundQty} " +
            "    ELSE #{inboundQty} " +
            "  END, " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock + #{inboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE semi_product_two_id = #{semiProductTwoId}"
    )
    int inbound(@Param("semiProductTwoId") Integer semiProductTwoId, @Param("inboundQty") Integer inboundQty);

    /**
     * 出库
     * @param semiProductTwoId
     * @param outboundQty
     * @return
     */
    @Update(
        "UPDATE storage.semi_finished_product_two " +
            "SET " +
            "  outbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN outbound_quantity + #{outboundQty} " +
            "    ELSE #{outboundQty} " +
            "  END, " +
            "  inbound_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN inbound_quantity " +
            "    ELSE 0 " +
            "  END, " +
            "  stock_quantity = CASE " +
            "    WHEN DATE(updated_time) = CURRENT_DATE THEN stock_quantity " +
            "    ELSE current_stock " +
            "  END, " +
            "  current_stock = current_stock - #{outboundQty}, " +
            "  updated_time = NOW() " +
            "WHERE semi_product_two_id = #{semiProductTwoId} AND current_stock >= #{outboundQty}"
    )
    int outbound(@Param("semiProductTwoId") Integer semiProductTwoId, @Param("outboundQty") Integer outboundQty);
}

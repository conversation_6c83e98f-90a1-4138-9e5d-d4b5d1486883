package com.cpmes.system.mapperJenasi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpmes.system.entity.PurchaseOrderPriceLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购订单价格变更日志Mapper接口
 * 使用PostgreSQL从库存储价格变更日志
 * 
 * <AUTHOR>
 * @date 2024-12-22
 */
@Mapper
@DS("slave")  // 指定使用PostgreSQL从库
public interface PurchaseOrderPriceLogMapper extends BaseMapper<PurchaseOrderPriceLog> {

    /**
     * 根据采购订单ID查询价格变更日志列表（按操作时间倒序）
     *
     * @param purchaseOrderId 采购订单ID
     * @return 价格变更日志列表
     */
    List<PurchaseOrderPriceLog> selectByPurchaseOrderId(@Param("purchaseOrderId") Long purchaseOrderId);

    /**
     * 根据采购订单号查询价格变更日志列表（按操作时间倒序）
     *
     * @param purchaseNo 采购订单号
     * @return 价格变更日志列表
     */
    List<PurchaseOrderPriceLog> selectByPurchaseNo(@Param("purchaseNo") String purchaseNo);

    /**
     * 根据操作人查询价格变更日志列表（按操作时间倒序）
     *
     * @param operator 操作人
     * @return 价格变更日志列表
     */
    List<PurchaseOrderPriceLog> selectByOperator(@Param("operator") String operator);

    /**
     * 批量插入价格变更日志
     *
     * @param logList 日志列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("logList") List<PurchaseOrderPriceLog> logList);
}

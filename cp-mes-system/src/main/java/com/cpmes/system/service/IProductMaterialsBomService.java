package com.cpmes.system.service;

import com.cpmes.system.domain.ProductMaterialsBom;
import com.cpmes.system.domain.vo.ProductMaterialsBomVo;
import com.cpmes.system.domain.bo.ProductMaterialsBomBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.system.domain.vo.GenericBomMappingVo;
import com.cpmes.system.domain.bo.ProductMaterialsBomImportBo;
import com.cpmes.system.domain.vo.ImportProcessResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 产品所需原材料明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
public interface IProductMaterialsBomService {

    /**
     * 查询产品所需原材料明细
     */
    ProductMaterialsBomVo queryById(Long id);

    /**
     * 查询产品所需原材料明细列表
     */
    TableDataInfo<ProductMaterialsBomVo> queryPageList(ProductMaterialsBomBo bo, PageQuery pageQuery);

    /**
     * 查询产品所需原材料明细列表
     */
    List<ProductMaterialsBomVo> queryList(ProductMaterialsBomBo bo);

    /**
     * 新增产品所需原材料明细
     */
    Boolean insertByBo(ProductMaterialsBomBo bo);

    /**
     * 修改产品所需原材料明细
     */
    Boolean updateByBo(ProductMaterialsBomBo bo);

    /**
     * 校验并批量删除产品所需原材料明细信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取所有版本列表
     */
    List<String> getAllVersions();

    /**
     * 根据型号、模块、板类型获取版本列表
     */
    List<String> getVersionsByGroup(String model, String module, String boardType);

    /**
     * 批量导入BOM数据
     */
    String importBomData(String filePath, String updatedBy);

    /**
     * 获取所有型号列表
     */
    List<String> getAllModels();

    /**
     * 根据型号获取BOM列表
     */
    List<ProductMaterialsBomVo> getBomByModel(String model);

    /**
     * 批量处理导入的BOM数据（事务管理）
     * @param importDataList 导入的数据列表
     * @param isUpdateSupport 是否支持更新
     * @param versionStrategy 版本策略
     * @param targetVersion 目标版本
     * @return 处理结果信息
     */
    ImportProcessResult processBomImportData(List<ProductMaterialsBomImportBo> importDataList,
                                           boolean isUpdateSupport,
                                           String versionStrategy,
                                           String targetVersion);

    /**
     * 根据型号获取BOM列表（支持显示已删除记录）
     */
    List<ProductMaterialsBomVo> getBomByModel(String model, Boolean showDeleted);

    /**
     * 批量修改型号和产品编码
     * @param ids 要更新的记录ID列表
     * @param targetModel 目标型号
     * @param productNumber 产品编码（可选）
     * @param updatedBy 更新人
     * @return 更新结果
     */
    Boolean batchUpdateModel(List<Long> ids, String targetModel, String productNumber, String updatedBy);

    /**
     * 批量修改型号和产品编码（支持按款式分别设置编码）
     * @param ids 要更新的BOM记录ID列表
     * @param targetModel 目标型号
     * @param styleCodeMapping 款式编码映射（key: 款式名称, value: 产品编码）
     * @param updatedBy 更新人
     * @return 更新结果
     */
    Boolean batchUpdateModelWithStyle(List<Long> ids, String targetModel,
                                    java.util.Map<String, String> styleCodeMapping, String updatedBy);

    /**
     * 获取所有功能模块列表
     */
    List<String> getAllModules();

    /**
     * 获取所有上下板类型列表
     */
    List<String> getAllBoardTypes();

    // ============= 通用板块功能相关方法 =============

    /*
     * 注意：BOM引用功能已被废弃，以下方法已注释掉
     */

    /*
    /**
     * 获取指定型号可以引用的通用板块列表（动态查找）
     * @param currentModel 当前型号
     * @param module 功能模块（可选）
     * @param boardType 上下板类型（可选）
     * @return 可引用的通用板块列表
     * @deprecated BOM引用功能已废弃
     */
    /*
    List<ProductMaterialsBomVo> getAvailableGenericBomList(String currentModel, String module, String boardType);

    /**
     * 获取指定条件的材料清单（用于动态展示引用内容）
     * @param referencedModel 引用的型号
     * @param module 模块
     * @param boardType 板类型
     * @return 材料清单
     * @deprecated BOM引用功能已废弃
     */
    /*
    List<ProductMaterialsBomVo> getMaterialListForReference(String referencedModel, String module, String boardType);
    */

    /**
     * 创建通用板块记录（material为null的记录）
     * @param model 型号
     * @param module 模块（可选）
     * @param boardType 板类型（可选）
     * @param updatedBy 创建人
     * @return 是否成功
     */
    Boolean createGenericBomRecord(String model, String module, String boardType, String updatedBy);

    // ============= 通用映射功能相关方法 =============

    /**
     * 根据通用ID获取映射列表
     * @param genericId 通用ID
     * @return 映射列表（按sortOrder排序）
     */
    List<GenericBomMappingVo> getGenericMappingList(String genericId);

    /**
     * 获取通用ID的默认映射
     * @param genericId 通用ID
     * @return 默认映射，如果没有默认映射则返回第一个
     */
    GenericBomMappingVo getDefaultGenericMapping(String genericId);

    /**
     * 根据映射信息获取具体的材料清单
     * @param mappedModel 映射的型号
     * @param mappedModule 映射的模块
     * @param mappedBoardType 映射的板类型
     * @return 具体材料清单
     */
    List<ProductMaterialsBomVo> getMaterialListByMapping(String mappedModel, String mappedModule, String mappedBoardType);

    /**
     * 创建通用板块记录（使用预留字段）
     * @param model 型号
     * @param module 模块
     * @param boardType 板类型
     * @param genericId 通用ID
     * @param genericName 通用名称
     * @param updatedBy 创建人
     * @return 是否成功
     */
    Boolean createGenericBomWithMapping(String model, String module, String boardType,
                                       String genericId, String genericName, String updatedBy);

    /**
     * 根据型号和款式获取BOM列表
     */
    List<ProductMaterialsBomVo> getBomByModelAndStyle(String model, String styleName);

    // ============= 通用设置功能相关方法 =============

    /**
     * 检查原料是否可以设置为通用（按原料名称）
     * @param materialName 原料名称
     * @return 检查结果，包含是否可设置、原因说明等信息
     * @deprecated 使用 checkGenericEligibilityByGroup 替代
     */
    @Deprecated
    Map<String, Object> checkGenericEligibility(String materialName);

    /**
     * 检查BOM记录是否可以设置为通用（按分组）
     * @param model 产品型号
     * @param module 款式
     * @param boardType 板型
     * @return 检查结果，包含是否可设置、原因说明等信息
     */
    Map<String, Object> checkGenericEligibilityByGroup(String model, String module, String boardType);

    /**
     * 设置原料为通用类型（按原料名称）
     * @param materialName 原料名称
     * @param updatedBy 更新人
     * @return 操作结果信息
     * @deprecated 使用 setMaterialAsGenericByGroup 替代
     */
    @Deprecated
    String setMaterialAsGeneric(String materialName, String updatedBy);

    /**
     * 设置BOM记录为通用类型（按分组）
     * @param model 产品型号
     * @param module 款式
     * @param boardType 板型
     * @param updatedBy 更新人
     * @return 操作结果信息
     */
    String setMaterialAsGenericByGroup(String model, String module, String boardType, String updatedBy);
}

package com.cpmes.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.domain.BatchNumberSequence;
import com.cpmes.system.mapper.BatchNumberSequenceMapper;
import com.cpmes.system.service.IBatchNumberGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 批次号生成服务实现类
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@Service
@DS("slave") // 使用PostgreSQL从数据库
public class BatchNumberGeneratorServiceImpl implements IBatchNumberGeneratorService {

    @Autowired
    private BatchNumberSequenceMapper batchNumberSequenceMapper;

    /**
     * 批次号格式：YYYYMMDD-XXX
     */
    private static final String BATCH_NUMBER_FORMAT = "yyyyMMdd";
    private static final String BATCH_NUMBER_PATTERN = "^\\d{8}-\\d{3}$";
    private static final Pattern PATTERN = Pattern.compile("^(\\d{8})-(\\d{3})$");
    private static final int MAX_SEQUENCE_PER_DAY = 999;
    private static final int MAX_RETRY_TIMES = 3;

    /**
     * 使用ReentrantLock确保线程安全
     */
    private final ReentrantLock lock = new ReentrantLock();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateNextBatchNumber() {
        lock.lock();
        try {
            // 使用数据库时间确保一致性
            String dateStr = batchNumberSequenceMapper.getCurrentDateStr();
            log.debug("开始生成批次号，日期: {}", dateStr);

            // 重试机制处理并发冲突
            for (int retry = 0; retry < MAX_RETRY_TIMES; retry++) {
                try {
                    Integer currentMaxSequence = batchNumberSequenceMapper.getMaxSequenceByDate(dateStr);
                    int nextSequence = (currentMaxSequence == null) ? 1 : currentMaxSequence + 1;

                    // 检查是否超过每日最大限制
                    if (nextSequence > MAX_SEQUENCE_PER_DAY) {
                        throw new ServiceException(String.format("当日批次号已达上限%d，无法生成新的批次号", MAX_SEQUENCE_PER_DAY));
                    }

                    // 使用CAS更新序号，避免并发冲突
                    if (currentMaxSequence == null) {
                        // 首次插入
                        int insertResult = batchNumberSequenceMapper.updateOrInsertSequence(dateStr, nextSequence);
                        if (insertResult > 0) {
                            String batchNumber = formatBatchNumber(dateStr, nextSequence);
                            log.info("成功生成新批次号: {} (首次插入)", batchNumber);
                            return batchNumber;
                        }
                    } else {
                        // CAS更新
                        int updateResult = batchNumberSequenceMapper.compareAndSwapSequence(dateStr, currentMaxSequence, nextSequence);
                        if (updateResult > 0) {
                            String batchNumber = formatBatchNumber(dateStr, nextSequence);
                            log.info("成功生成新批次号: {} (CAS更新)", batchNumber);
                            return batchNumber;
                        }
                    }

                    // CAS失败，重试
                    log.warn("批次号生成CAS操作失败，重试第{}次", retry + 1);
                    Thread.sleep(10); // 短暂等待后重试

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new ServiceException("批次号生成被中断");
                } catch (Exception e) {
                    log.error("批次号生成失败，重试第{}次: {}", retry + 1, e.getMessage());
                    if (retry == MAX_RETRY_TIMES - 1) {
                        throw new ServiceException("批次号生成失败: " + e.getMessage());
                    }
                }
            }

            throw new ServiceException("批次号生成失败，已达最大重试次数");

        } finally {
            lock.unlock();
        }
    }

    @Override
    public LocalDate parseDateFromBatchNumber(String batchNumber) {
        if (!isValidBatchNumber(batchNumber)) {
            log.warn("批次号格式不正确: {}", batchNumber);
            return null;
        }

        try {
            String dateStr = batchNumber.substring(0, 8);
            return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(BATCH_NUMBER_FORMAT));
        } catch (Exception e) {
            log.error("解析批次号日期失败: {}", batchNumber, e);
            return null;
        }
    }

    @Override
    public Integer parseSequenceFromBatchNumber(String batchNumber) {
        if (!isValidBatchNumber(batchNumber)) {
            log.warn("批次号格式不正确: {}", batchNumber);
            return null;
        }

        try {
            Matcher matcher = PATTERN.matcher(batchNumber);
            if (matcher.matches()) {
                return Integer.parseInt(matcher.group(2));
            }
        } catch (Exception e) {
            log.error("解析批次号序号失败: {}", batchNumber, e);
        }
        return null;
    }

    @Override
    public boolean isValidBatchNumber(String batchNumber) {
        if (batchNumber == null || batchNumber.trim().isEmpty()) {
            return false;
        }
        return batchNumber.matches(BATCH_NUMBER_PATTERN);
    }

    @Override
    @DS("slave")
    public Integer getCurrentMaxSequence(LocalDate date) {
        try {
            String dateStr = date.format(DateTimeFormatter.ofPattern(BATCH_NUMBER_FORMAT));
            Integer maxSequence = batchNumberSequenceMapper.getMaxSequenceByDate(dateStr);
            return maxSequence != null ? maxSequence : 0;
        } catch (Exception e) {
            log.error("获取当前最大序号失败: {}", date, e);
            return 0;
        }
    }

    @Override
    @DS("slave")
    public List<String> getBatchNumbersByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            String startDateStr = startDate.format(DateTimeFormatter.ofPattern(BATCH_NUMBER_FORMAT));
            String endDateStr = endDate.format(DateTimeFormatter.ofPattern(BATCH_NUMBER_FORMAT));
            
            List<String> batchNumbers = new ArrayList<>();
            List<BatchNumberSequence> sequences = batchNumberSequenceMapper.selectByDateRange(startDateStr, endDateStr);

            for (BatchNumberSequence sequence : sequences) {
                for (int i = 1; i <= sequence.getMaxSequence(); i++) {
                    batchNumbers.add(formatBatchNumber(sequence.getDateStr(), i));
                }
            }
            
            return batchNumbers;
        } catch (Exception e) {
            log.error("查询日期范围内批次号失败: {} - {}", startDate, endDate, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetSequenceCounter(LocalDate date, Integer newMaxSequence) {
        try {
            if (newMaxSequence < 0 || newMaxSequence > MAX_SEQUENCE_PER_DAY) {
                throw new ServiceException("序号必须在0-" + MAX_SEQUENCE_PER_DAY + "范围内");
            }

            String dateStr = date.format(DateTimeFormatter.ofPattern(BATCH_NUMBER_FORMAT));
            int updateResult = batchNumberSequenceMapper.resetSequenceCounter(dateStr, newMaxSequence);
            
            if (updateResult > 0) {
                log.info("成功重置序号计数器: 日期={}, 新序号={}", dateStr, newMaxSequence);
                return true;
            } else {
                log.warn("重置序号计数器失败，可能是日期不存在: {}", dateStr);
                return false;
            }
        } catch (Exception e) {
            log.error("重置序号计数器失败: 日期={}, 新序号={}", date, newMaxSequence, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> generateBatchNumbers(int count) {
        if (count <= 0) {
            throw new ServiceException("批次号生成数量必须大于0");
        }
        if (count > MAX_SEQUENCE_PER_DAY) {
            throw new ServiceException("单次生成批次号数量不能超过" + MAX_SEQUENCE_PER_DAY);
        }

        List<String> batchNumbers = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            batchNumbers.add(generateNextBatchNumber());
        }
        
        log.info("批量生成批次号完成，数量: {}", count);
        return batchNumbers;
    }

    /**
     * 格式化批次号
     *
     * @param dateStr 日期字符串
     * @param sequence 序号
     * @return 格式化的批次号
     */
    private String formatBatchNumber(String dateStr, int sequence) {
        return String.format("%s-%03d", dateStr, sequence);
    }
}

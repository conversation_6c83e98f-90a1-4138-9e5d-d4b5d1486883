package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.domain.WarehouseInfo;
import com.cpmes.system.domain.WarehouseZone;
import com.cpmes.system.domain.bo.WarehouseZoneBo;
import com.cpmes.system.domain.vo.WarehouseZoneVo;
import com.cpmes.system.mapper.WarehouseZoneMapper;
import com.cpmes.system.service.IPrintInterfaceConfigService;
import com.cpmes.system.service.IWarehouseInfoService;
import com.cpmes.system.service.IWarehouseZoneService;
import com.cpmes.system.domain.vo.WarehouseInfoVo;
import com.cpmes.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库区域Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RequiredArgsConstructor
@Service
@DS("slave") // 使用PostgreSQL从数据库
public class WarehouseZoneServiceImpl implements IWarehouseZoneService {

    private final WarehouseZoneMapper baseMapper;
    private final IPrintInterfaceConfigService printInterfaceConfigService;
    private final IWarehouseInfoService warehouseInfoService;

    /**
     * 查询仓库区域
     */
    @Override
    public WarehouseZoneVo queryById(Long zoneId) {
        return baseMapper.selectVoById(zoneId);
    }

    /**
     * 查询仓库区域列表
     */
    @Override
    public TableDataInfo<WarehouseZoneVo> queryPageList(WarehouseZoneBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WarehouseZone> lqw = buildQueryWrapper(bo);
        Page<WarehouseZoneVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询仓库区域列表
     */
    @Override
    public List<WarehouseZoneVo> queryList(WarehouseZoneBo bo) {
        LambdaQueryWrapper<WarehouseZone> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WarehouseZone> buildQueryWrapper(WarehouseZoneBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getZoneCode()), WarehouseZone::getZoneCode, bo.getZoneCode());
        lqw.like(StringUtils.isNotBlank(bo.getZoneName()), WarehouseZone::getZoneName, bo.getZoneName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), WarehouseZone::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), WarehouseZone::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getZoneType()), WarehouseZone::getZoneType, bo.getZoneType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WarehouseZone::getStatus, bo.getStatus());
        lqw.orderByDesc(WarehouseZone::getCreateTime);
        return lqw;
    }

    /**
     * 新增仓库区域
     */
    @Override
    public Boolean insertByBo(WarehouseZoneBo bo) {
        WarehouseZone add = BeanUtil.toBean(bo, WarehouseZone.class);
        validEntityBeforeSave(add);
        // 默认设置为启用状态
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("1");
        }
        // 初始化使用量和重量
        if (add.getCurrentUsage() == null) {
            add.setCurrentUsage(0);
        }
        if (add.getCurrentWeight() == null) {
            add.setCurrentWeight(java.math.BigDecimal.ZERO);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setZoneId(add.getZoneId());
        }
        return flag;
    }

    /**
     * 修改仓库区域
     */
    @Override
    public Boolean updateByBo(WarehouseZoneBo bo) {
        WarehouseZone update = BeanUtil.toBean(bo, WarehouseZone.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WarehouseZone entity) {
        // 校验仓库状态
        if (StringUtils.isNotBlank(entity.getWarehouseCode())) {
            WarehouseInfoVo warehouse = warehouseInfoService.queryByWarehouseCode(entity.getWarehouseCode());
            if (warehouse == null) {
                throw new ServiceException("所选仓库不存在");
            }
            if ("0".equals(warehouse.getStatus())) {
                throw new ServiceException("所选仓库已停用，无法进行区域操作");
            }
        }

        // 校验区域编码唯一性
        if (StringUtils.isNotBlank(entity.getZoneCode())) {
            Boolean isUnique = checkZoneCodeUnique(entity.getZoneCode(), entity.getZoneId());
            if (!isUnique) {
                throw new ServiceException("区域编码已存在");
            }
        }

        // 校验区域名称、仓库、类型组合唯一性
        if (StringUtils.isNotBlank(entity.getZoneName())
            && StringUtils.isNotBlank(entity.getWarehouseCode())
            && StringUtils.isNotBlank(entity.getZoneType())) {
            Boolean isInfoUnique = checkZoneInfoUniqueByCode(
                entity.getZoneName(),
                entity.getWarehouseCode(),
                entity.getZoneType(),
                entity.getZoneId()
            );
            if (!isInfoUnique) {
                throw new ServiceException("相同名称、仓库、类型的区域已存在，请检查后重新提交");
            }
        }

        // 校验容量（可选字段）
        if (entity.getZoneCapacity() != null && entity.getZoneCapacity() < 0) {
            throw new ServiceException("区域容量不能为负数");
        }
        // 校验当前使用量不能超过容量（如果设置了容量）
        if (entity.getCurrentUsage() != null && entity.getZoneCapacity() != null
            && entity.getZoneCapacity() > 0 && entity.getCurrentUsage() > entity.getZoneCapacity()) {
            throw new ServiceException("当前使用量不能超过区域容量");
        }
    }

    /**
     * 批量删除仓库区域
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否还有关联的库存数据
            for (Long id : ids) {
                WarehouseZoneVo zone = queryById(id);
                if (zone == null) {
                    throw new RuntimeException("区域不存在");
                }
                // TODO: 检查是否还有关联的库存数据
                // 建议检查 inventory 表中是否还有该区域的库存记录
                // 如果有库存数据，应该阻止删除操作
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量启用/停用区域
     */
    @Override
    public Boolean updateStatus(Collection<Long> ids, String status) {
        if (ObjectUtil.isEmpty(ids) || StringUtils.isBlank(status)) {
            return false;
        }

        // 如果是启用操作，需要校验仓库状态
        if ("1".equals(status)) {
            for (Long id : ids) {
                WarehouseZoneVo zone = queryById(id);
                if (zone == null) {
                    throw new ServiceException("区域不存在，ID: " + id);
                }

                // 校验仓库状态
                WarehouseInfoVo warehouse = warehouseInfoService.queryByWarehouseCode(zone.getWarehouseCode());
                if (warehouse == null) {
                    throw new ServiceException("区域所属仓库不存在，区域: " + zone.getZoneName());
                }
                if ("0".equals(warehouse.getStatus())) {
                    throw new ServiceException("无法启用区域 [" + zone.getZoneName() + "]，因为所属仓库 [" + warehouse.getWarehouseName() + "] 已停用");
                }
            }
        }

        List<WarehouseZone> updateList = ids.stream()
            .map(id -> {
                WarehouseZone zone = new WarehouseZone();
                zone.setZoneId(id);
                zone.setStatus(status);
                return zone;
            })
            .collect(Collectors.toList());

        return baseMapper.updateBatchById(updateList);
    }

    /**
     * 根据仓库编码批量更新区域状态（级联停用）
     */
    @Override
    public Boolean updateStatusByWarehouseCode(String warehouseCode, String status) {
        if (StringUtils.isBlank(warehouseCode) || StringUtils.isBlank(status)) {
            return false;
        }

        // 查询该仓库下的所有区域
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseZone::getWarehouseCode, warehouseCode);
        lqw.eq(WarehouseZone::getDelFlag, "0");
        List<WarehouseZone> zones = baseMapper.selectList(lqw);

        if (ObjectUtil.isEmpty(zones)) {
            return true; // 没有区域需要更新，返回成功
        }

        // 批量更新区域状态
        List<WarehouseZone> updateList = zones.stream()
            .map(zone -> {
                WarehouseZone updateZone = new WarehouseZone();
                updateZone.setZoneId(zone.getZoneId());
                updateZone.setStatus(status);
                return updateZone;
            })
            .collect(Collectors.toList());

        return baseMapper.updateBatchById(updateList);
    }

    /**
     * 根据仓库类型查询区域列表
     */
    @Override
    public List<WarehouseZoneVo> queryByWarehouseType(String warehouseType) {
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(warehouseType), WarehouseZone::getWarehouseType, warehouseType);
        lqw.eq(WarehouseZone::getStatus, "1"); // 只查询启用的区域
        lqw.orderByAsc(WarehouseZone::getZoneCode);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据仓库编码查询区域列表
     */
    @Override
    public List<WarehouseZoneVo> queryByWarehouseCode(String warehouseCode) {
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(warehouseCode), WarehouseZone::getWarehouseCode, warehouseCode);
        lqw.eq(WarehouseZone::getStatus, "1"); // 只查询启用的区域
        lqw.orderByAsc(WarehouseZone::getZoneCode);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据区域编码查询区域信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public WarehouseZoneVo queryByZoneCode(String zoneCode) {
        if (StringUtils.isBlank(zoneCode)) {
            return null;
        }
        // 修复：使用自定义SQL查询，确保正确获取仓库状态（通过LEFT JOIN warehouse_info表）
        return baseMapper.selectByZoneCode(zoneCode);
    }

    /**
     * 根据仓库ID查询区域列表（兼容旧接口）
     */
    @Override
    public List<WarehouseZoneVo> queryByWarehouseId(Long warehouseId) {
        // TODO: 需要将warehouseId转换为warehouseCode
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(String.valueOf(warehouseId)), WarehouseZone::getWarehouseCode, String.valueOf(warehouseId));
        lqw.eq(WarehouseZone::getStatus, "1"); // 只查询启用的区域
        lqw.orderByAsc(WarehouseZone::getZoneCode);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 校验区域编码是否唯一
     *
     * @param zoneCode 区域编码
     * @param zoneId 区域ID
     * @return 结果 true-唯一，false-不唯一
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean checkZoneCodeUnique(String zoneCode, Long zoneId) {
        if (StringUtils.isBlank(zoneCode)) {
            return false;
        }

        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseZone::getZoneCode, zoneCode);
        if (zoneId != null) {
            lqw.ne(WarehouseZone::getZoneId, zoneId);
        }
        lqw.eq(WarehouseZone::getDelFlag, "0");

        long count = baseMapper.selectCount(lqw);
        return count == 0;
    }

    /**
     * 校验区域名称、仓库编码、类型组合是否唯一
     *
     * @param zoneName 区域名称
     * @param warehouseCode 仓库编码
     * @param zoneType 区域类型
     * @param zoneId 区域ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    public Boolean checkZoneInfoUniqueByCode(String zoneName, String warehouseCode, String zoneType, Long zoneId) {
        if (StringUtils.isBlank(zoneName) || StringUtils.isBlank(warehouseCode) || StringUtils.isBlank(zoneType)) {
            return false;
        }

        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseZone::getZoneName, zoneName);
        lqw.eq(WarehouseZone::getWarehouseCode, warehouseCode);
        lqw.eq(WarehouseZone::getZoneType, zoneType);
        if (zoneId != null) {
            lqw.ne(WarehouseZone::getZoneId, zoneId);
        }
        lqw.eq(WarehouseZone::getDelFlag, "0");

        long count = baseMapper.selectCount(lqw);
        return count == 0;
    }

    /**
     * 校验区域名称、仓库、类型组合是否唯一（兼容旧接口）
     *
     * @param zoneName 区域名称
     * @param warehouseId 仓库ID
     * @param zoneType 区域类型
     * @param zoneId 区域ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean checkZoneInfoUnique(String zoneName, Long warehouseId, String zoneType, Long zoneId) {
        // TODO: 需要将warehouseId转换为warehouseCode
        return checkZoneInfoUniqueByCode(zoneName, String.valueOf(warehouseId), zoneType, zoneId);
    }

    /**
     * 生成区域编码
     *
     * @param warehouseId 仓库ID（兼容参数）
     * @param warehouseCode 仓库编码
     * @param zoneType 区域类型
     * @return 生成的区域编码
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public String generateZoneCode(Long warehouseId, String warehouseCode, String zoneType) {
        if (StringUtils.isBlank(warehouseCode)) {
            throw new RuntimeException("仓库编码不能为空");
        }

        // 使用仓库编码的前两位作为前缀
        String warehousePrefix = warehouseCode.length() >= 2 ? warehouseCode.substring(0, 2) : warehouseCode;
        String prefix = warehousePrefix + "Q";
        String datePart = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));

        // 查询当月该仓库下区域的最大序号
        String likePattern = prefix + datePart + "%";
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.like(WarehouseZone::getZoneCode, likePattern);
        lqw.eq(WarehouseZone::getWarehouseCode, warehouseCode);
        lqw.eq(WarehouseZone::getDelFlag, "0");
        lqw.orderByDesc(WarehouseZone::getZoneCode);
        lqw.last("LIMIT 1");

        WarehouseZone lastZone = baseMapper.selectOne(lqw);

        int nextSequence = 1;
        if (lastZone != null && StringUtils.isNotBlank(lastZone.getZoneCode())) {
            String lastCode = lastZone.getZoneCode();
            String expectedPrefix = prefix + datePart;
            if (lastCode.startsWith(expectedPrefix) && lastCode.length() >= expectedPrefix.length() + 2) {
                try {
                    String sequenceStr = lastCode.substring(expectedPrefix.length());
                    int lastSequence = Integer.parseInt(sequenceStr);
                    nextSequence = lastSequence + 1;
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用默认序号1
                    nextSequence = 1;
                }
            }
        }

        String newCode = prefix + datePart + String.format("%02d", nextSequence);

        // 再次校验生成的编码是否唯一，如果不唯一则递增序号
        while (!checkZoneCodeUnique(newCode, null)) {
            nextSequence++;
            newCode = prefix + datePart + String.format("%02d", nextSequence);
        }

        return newCode;
    }

    //     /**
    //  * 根据仓库ID获取区域编码前缀
    //  *
    //  * @param warehouseId 仓库ID
    //  * @return 编码前缀
    //  */
    // private String getZoneCodePrefix(Long warehouseId) {
    //     // 获取仓库编码的前缀
    //     String warehouseCodePrefix = getWarehouseCodePrefix(warehouseId);
    //     return warehouseCodePrefix + "Q";
    // }

    // /**
    //  * 获取仓库编码前缀
    //  *
    //  * @param warehouseId 仓库ID
    //  * @return 仓库编码前缀
    //  */
    // private String getWarehouseCodePrefix(Long warehouseId) {
    //     try {
    //         // 这里应该查询仓库表获取仓库编码，但由于架构限制
    //         // 暂时根据仓库ID推导前缀
    //         // 实际项目中应该注入WarehouseInfoService或直接查询仓库表

    //         // 根据仓库ID推导可能的前缀
    //         long idMod = warehouseId % 4;
    //         switch ((int) idMod) {
    //             case 0:
    //                 return "RM"; // 原料仓库
    //             case 1:
    //                 return "SF"; // 半成品仓库
    //             case 2:
    //                 return "FG"; // 成品仓库
    //             case 3:
    //                 return "CP"; // 零件仓库
    //             default:
    //                 return "WH"; // 通用仓库
    //         }
    //     } catch (Exception e) {
    //         // 查询失败时使用默认前缀
    //         return "WH";
    //     }
    // }

    /**
     * 获取区域统计信息
     */
    @Override
    public Map<String, Object> getZoneStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总区域数
        Long totalZones = baseMapper.selectCount(null);
        statistics.put("totalZones", totalZones);

        // 启用区域数
        LambdaQueryWrapper<WarehouseZone> enabledQuery = Wrappers.lambdaQuery();
        enabledQuery.eq(WarehouseZone::getStatus, "1");
        Long enabledZones = baseMapper.selectCount(enabledQuery);
        statistics.put("enabledZones", enabledZones);

        // 停用区域数
        Long disabledZones = totalZones - enabledZones;
        statistics.put("disabledZones", disabledZones);

        // 按仓库类型统计（支持数字和字符串两种类型）
        Map<String, Long> typeStatistics = new HashMap<>();
        // 数字类型
        List<String> warehouseTypesNumeric = Arrays.asList("1", "2", "3", "4");
        for (String type : warehouseTypesNumeric) {
            LambdaQueryWrapper<WarehouseZone> typeQuery = Wrappers.lambdaQuery();
            typeQuery.eq(WarehouseZone::getWarehouseType, type);
            Long count = baseMapper.selectCount(typeQuery);
            typeStatistics.put(type, count);
        }
        // 字符串类型
        List<String> warehouseTypesString = Arrays.asList("raw_material", "component", "semi_finished", "product");
        for (String type : warehouseTypesString) {
            LambdaQueryWrapper<WarehouseZone> typeQuery = Wrappers.lambdaQuery();
            typeQuery.eq(WarehouseZone::getWarehouseType, type);
            Long count = baseMapper.selectCount(typeQuery);
            if (count > 0) {
                // 如果有字符串类型的数据，也加入统计
                typeStatistics.put(type, count);
            }
        }
        statistics.put("typeStatistics", typeStatistics);

        // 平均使用率
        List<WarehouseZoneVo> allZones = baseMapper.selectVoList(null);
        double avgUsageRate = allZones.stream()
            .filter(zone -> zone.getZoneCapacity() != null && zone.getZoneCapacity() > 0)
            .mapToDouble(zone -> {
                int usage = zone.getCurrentUsage() != null ? zone.getCurrentUsage() : 0;
                return (double) usage / zone.getZoneCapacity() * 100;
            })
            .average()
            .orElse(0.0);
        statistics.put("avgUsageRate", Math.round(avgUsageRate * 100.0) / 100.0);

        return statistics;
    }

    /**
     * 生成区域二维码
     */
    @Override
    public Boolean generateQrCode(Long zoneId) {
        WarehouseZone zone = baseMapper.selectById(zoneId);
        if (zone == null) {
            throw new RuntimeException("区域不存在");
        }

        // 根据新的二维码打印文档设计，只需要传递两个字段
        String qrCodeContent;
        try {
            // 构建符合打印文档要求的JSON格式
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");
            jsonBuilder.append("\"label_type\":\"warehouse_zone\",");
            jsonBuilder.append("\"zone_code\":\"").append(zone.getZoneCode()).append("\"");
            jsonBuilder.append("}");
            qrCodeContent = jsonBuilder.toString();
        } catch (Exception e) {
            // 如果JSON生成失败，降级使用区域编码
            qrCodeContent = zone.getZoneCode();
        }

        // 更新区域信息
        WarehouseZone updateZone = new WarehouseZone();
        updateZone.setZoneId(zoneId);
        updateZone.setZoneQrCode(qrCodeContent);
        updateZone.setQrCodePrintTime(new java.util.Date());
        updateZone.setQrCodeVersion((zone.getQrCodeVersion() != null ? zone.getQrCodeVersion() : 0) + 1);

        return baseMapper.updateById(updateZone) > 0;
    }

    /**
     * 批量生成区域二维码
     */
    @Override
    public Boolean batchGenerateQrCode(Collection<Long> zoneIds) {
        if (ObjectUtil.isEmpty(zoneIds)) {
            return false;
        }

        boolean allSuccess = true;
        for (Long zoneId : zoneIds) {
            try {
                generateQrCode(zoneId);
            } catch (Exception e) {
                allSuccess = false;
                // 记录日志但继续处理其他区域
                System.err.println("生成区域二维码失败，区域ID: " + zoneId + ", 错误: " + e.getMessage());
            }
        }

        return allSuccess;
    }

    /**
     * 获取区域二维码信息
     */
    @Override
    public Map<String, Object> getQrCodeInfo(Long zoneId) {
        WarehouseZone zone = baseMapper.selectById(zoneId);
        if (zone == null) {
            throw new RuntimeException("区域不存在");
        }

        Map<String, Object> qrCodeInfo = new HashMap<>();
        qrCodeInfo.put("zoneId", zone.getZoneId());
        qrCodeInfo.put("zoneCode", zone.getZoneCode());
        qrCodeInfo.put("zoneName", zone.getZoneName());
        qrCodeInfo.put("zoneQrCode", zone.getZoneQrCode());
        qrCodeInfo.put("qrCodePrintTime", zone.getQrCodePrintTime());
        qrCodeInfo.put("qrCodeVersion", zone.getQrCodeVersion());
        qrCodeInfo.put("hasQrCode", StringUtils.isNotBlank(zone.getZoneQrCode()));

        return qrCodeInfo;
    }

    /**
     * 打印区域二维码
     */
    @Override
    public Boolean printQrCode(Map<String, Object> printData, String clientIp) {
        try {
            // 从数据库获取仓库区域标签的打印机配置
            Map<String, Object> printerConfig = getPrinterConfig("warehouse_zone");
            if (printerConfig == null) {
                System.err.println("未找到warehouse_zone类型的打印机配置");
                return false;
            }

            // 构建打印机API URL
            String printerIp = (String) printerConfig.get("printer_ip");
            Integer printerPort = (Integer) printerConfig.get("printer_port");
            String apiEndpoint = (String) printerConfig.get("api_endpoint");
            String apiUrl = String.format("http://%s:%d%s", printerIp, printerPort, apiEndpoint);

            // 根据二维码打印文档设计，只需要传递两个字段
            Map<String, Object> printerData = new HashMap<>();
            printerData.put("label_type", "warehouse_zone");
            printerData.put("zone_code", printData.get("zoneCode"));

            // 发送到打印机驱动接口
            return sendToPrinterAPI(apiUrl, printerData);

        } catch (Exception e) {
            System.err.println("打印区域二维码失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取打印机配置
     * 注意：打印机配置存储在MySQL主库中，需要切换数据源
     */
    private Map<String, Object> getPrinterConfig(String labelType) {
        try {
            // 使用专门的Service查询打印机配置，该Service会自动切换到主库
            return printInterfaceConfigService.getBestPrinterConfig(labelType);
        } catch (Exception e) {
            System.err.println("获取打印机配置失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 发送数据到打印机API
     */
    private Boolean sendToPrinterAPI(String apiUrl, Map<String, Object> printerData) {
        try {
            // 构建JSON字符串
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : printerData.entrySet()) {
                if (!first) jsonBuilder.append(",");
                jsonBuilder.append("\"").append(entry.getKey()).append("\":");
                if (entry.getValue() instanceof String) {
                    jsonBuilder.append("\"").append(entry.getValue()).append("\"");
                } else {
                    jsonBuilder.append(entry.getValue());
                }
                first = false;
            }
            jsonBuilder.append("}");

            String jsonData = jsonBuilder.toString();
            System.out.println("发送到打印机的数据: " + jsonData);
            System.out.println("打印机API地址: " + apiUrl);

            // 使用Java原生HTTP客户端发送请求到打印机驱动
            java.net.URL url = new java.net.URL(apiUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

            // 设置连接参数
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(30000); // 连接超时30秒
            connection.setReadTimeout(30000);    // 读取超时30秒
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 发送数据
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应
            int responseCode = connection.getResponseCode();

            // 读取响应内容
            String responseBody = "";
            try (java.io.BufferedReader br = new java.io.BufferedReader(
                    new java.io.InputStreamReader(
                        responseCode >= 200 && responseCode < 300
                            ? connection.getInputStream()
                            : connection.getErrorStream(),
                        "utf-8"))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                responseBody = response.toString();
            }

            System.out.println("打印机响应代码: " + responseCode);
            System.out.println("打印机响应内容: " + responseBody);

            // 判断打印是否成功
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("区域二维码打印成功");
                return true;
            } else {
                System.err.println("区域二维码打印失败，HTTP状态码: " + responseCode);
                return false;
            }

        } catch (java.net.ConnectException e) {
            System.err.println("无法连接到打印机服务: " + e.getMessage());
            return false;
        } catch (java.net.SocketTimeoutException e) {
            System.err.println("连接打印机超时: " + e.getMessage());
            return false;
        } catch (java.net.UnknownHostException e) {
            System.err.println("无法解析打印机主机地址: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("发送到打印机API失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有启用状态的区域编码列表
     */
    @Override
    public List<String> getAllZoneCodes() {
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseZone::getStatus, "1"); // 只查询启用的区域
        lqw.isNotNull(WarehouseZone::getZoneCode); // 确保区域编码不为空
        lqw.orderByAsc(WarehouseZone::getZoneCode); // 按区域编码升序排列

        List<WarehouseZone> zones = baseMapper.selectList(lqw);
        return zones.stream()
            .map(WarehouseZone::getZoneCode)
            .filter(Objects::nonNull) // 过滤掉可能的null值
            .collect(Collectors.toList());
    }

    /**
     * 获取所有启用状态的区域编码和名称列表
     */
    @Override
    public List<com.cpmes.system.entity.vo.PurchaseZoneDataVo.ZoneInfo> getAllZoneCodesWithNames() {
        LambdaQueryWrapper<WarehouseZone> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseZone::getStatus, "1"); // 只查询启用的区域
        lqw.isNotNull(WarehouseZone::getZoneCode); // 确保区域编码不为空
        lqw.orderByAsc(WarehouseZone::getZoneCode); // 按区域编码升序排列

        List<WarehouseZone> zones = baseMapper.selectList(lqw);
        return zones.stream()
            .filter(zone -> Objects.nonNull(zone.getZoneCode())) // 过滤掉可能的null值
            .map(zone -> new com.cpmes.system.entity.vo.PurchaseZoneDataVo.ZoneInfo(
                zone.getZoneCode(),
                zone.getZoneName()
            ))
            .collect(Collectors.toList());
    }
}

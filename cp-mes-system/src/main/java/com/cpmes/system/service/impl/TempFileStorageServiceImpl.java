package com.cpmes.system.service.impl;

import com.cpmes.common.utils.redis.RedisUtils;
import com.cpmes.system.service.ITempFileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 临时文件存储服务实现类
 * 使用本地文件系统存储临时文件，Redis存储元数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TempFileStorageServiceImpl implements ITempFileStorageService {

    /**
     * 临时文件存储根目录 - 使用相对路径
     */
    @Value("${temp-order.file.temp-path:test}")
    private String tempBasePath;

    /**
     * 临时文件过期时间（分钟）
     */
    @Value("${temp-order.expire-minutes:30}")
    private int expireMinutes;

    /**
     * 服务器访问基础URL
     */
    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 服务器主机地址
     */
    @Value("${server.address:localhost}")
    private String serverHost;

    /**
     * 服务器端口
     */
    @Value("${server.port:8090}")
    private int serverPort;

    /**
     * 外部访问主机地址（用于生成URL）
     */
    @Value("${temp-order.server.external-host:}")
    private String externalHost;

    /**
     * 是否强制使用HTTPS
     */
    @Value("${temp-order.server.force-https:false}")
    private boolean forceHttps;

    /**
     * 外部访问端口
     */
    @Value("${temp-order.server.external-port:0}")
    private int externalPort;

    /**
     * Redis key前缀
     */
    private static final String TEMP_FILE_META_PREFIX = "temp:file:meta:";

    @Override
    public String saveTempFile(String tempOrderId, MultipartFile file) {
        // 参数验证
        if (tempOrderId == null || tempOrderId.trim().isEmpty()) {
            throw new IllegalArgumentException("临时订单ID不能为空");
        }
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String originalFileName = file.getOriginalFilename();
        long fileSize = file.getSize();

        try {
            log.info("开始保存临时文件: tempOrderId={}, originalFileName={}, size={}",
                    tempOrderId, originalFileName, fileSize);

            // 文件大小检查（5MB限制）
            if (fileSize > 5 * 1024 * 1024) {
                throw new IllegalArgumentException("文件大小不能超过5MB");
            }

            // 创建临时订单目录
            Path orderDir = createTempOrderDirectory(tempOrderId);

            // 生成唯一文件名
            String fileName = generateUniqueFileName(originalFileName);
            Path filePath = orderDir.resolve(fileName);

            // 记录详细的路径信息用于调试
            log.info("目录创建路径: {}", orderDir.toAbsolutePath());
            log.info("目标文件路径: {}", filePath.toAbsolutePath());
            log.info("工作目录: {}", Paths.get("").toAbsolutePath());

            // 检查磁盘空间（简单检查）
            try {
                long usableSpace = Files.getFileStore(orderDir).getUsableSpace();
                if (usableSpace < fileSize * 2) { // 保留2倍文件大小的空间
                    throw new IOException("磁盘空间不足");
                }
            } catch (Exception e) {
                log.warn("无法检查磁盘空间: {}", e.getMessage());
            }

            // 使用Files.copy()替代transferTo()以确保路径一致性
            // 这样可以避免Undertow临时目录的路径解析问题
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, filePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("文件复制完成: {} -> {}", originalFileName, filePath.toAbsolutePath());
            } catch (IOException e) {
                log.error("文件复制失败: {} -> {}, error: {}", originalFileName, filePath.toAbsolutePath(), e.getMessage());
                throw new IOException("文件复制失败: " + e.getMessage(), e);
            }

            // 验证文件是否保存成功
            if (!Files.exists(filePath)) {
                throw new IOException("文件保存失败，文件不存在: " + filePath.toAbsolutePath());
            }

            long savedFileSize = Files.size(filePath);
            if (savedFileSize != fileSize) {
                log.warn("文件大小不匹配: 期望={}, 实际={}", fileSize, savedFileSize);
            }

            // 保存文件元数据到Redis
            TempFileMetadata metadata = new TempFileMetadata(
                fileName,
                originalFileName,
                fileSize,
                file.getContentType(),
                System.currentTimeMillis(),
                generateAccessUrl(tempOrderId, fileName)
            );
            saveFileMetadata(tempOrderId, fileName, metadata);

            log.info("临时文件保存成功: tempOrderId={}, fileName={}, originalFileName={}, size={}",
                    tempOrderId, fileName, originalFileName, fileSize);

            return metadata.getAccessUrl();

        } catch (IllegalArgumentException e) {
            log.error("参数错误: tempOrderId={}, fileName={}, error={}",
                    tempOrderId, originalFileName, e.getMessage());
            throw e;
        } catch (IOException e) {
            log.error("IO错误: tempOrderId={}, fileName={}, error={}",
                    tempOrderId, originalFileName, e.getMessage(), e);
            throw new RuntimeException("文件保存失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("保存临时文件失败: tempOrderId={}, fileName={}",
                    tempOrderId, originalFileName, e);
            throw new RuntimeException("保存临时文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> saveTempFiles(String tempOrderId, MultipartFile[] files) {
        List<String> urls = new ArrayList<>();
        for (MultipartFile file : files) {
            String url = saveTempFile(tempOrderId, file);
            urls.add(url);
        }
        return urls;
    }

    @Override
    public InputStream getTempFileStream(String tempOrderId, String fileName) {
        try {
            log.debug("尝试获取临时文件流: tempOrderId={}, fileName={}", tempOrderId, fileName);

            // 策略1：直接使用提供的文件名
            Path filePath = getTempFilePath(tempOrderId, fileName);
            log.debug("尝试路径1: {}", filePath);

            if (Files.exists(filePath) && Files.isReadable(filePath)) {
                long fileSize = Files.size(filePath);
                log.info("成功获取临时文件流(直接匹配): {}, 大小: {} bytes", filePath, fileSize);
                return Files.newInputStream(filePath);
            }

            // 策略2：如果文件名包含URL编码，尝试解码后查找
            if (fileName.contains("%")) {
                try {
                    String decodedFileName = java.net.URLDecoder.decode(fileName, "UTF-8");
                    Path decodedFilePath = getTempFilePath(tempOrderId, decodedFileName);
                    log.debug("尝试路径2(URL解码): {}", decodedFilePath);

                    if (Files.exists(decodedFilePath) && Files.isReadable(decodedFilePath)) {
                        long fileSize = Files.size(decodedFilePath);
                        log.info("成功获取临时文件流(URL解码匹配): {}, 大小: {} bytes", decodedFilePath, fileSize);
                        return Files.newInputStream(decodedFilePath);
                    }
                } catch (java.io.UnsupportedEncodingException e) {
                    log.warn("URL解码失败: {}", fileName, e);
                }
            }

            // 策略3：如果文件名不包含URL编码，尝试编码后查找
            if (!fileName.contains("%")) {
                try {
                    String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8");
                    Path encodedFilePath = getTempFilePath(tempOrderId, encodedFileName);
                    log.debug("尝试路径3(URL编码): {}", encodedFilePath);

                    if (Files.exists(encodedFilePath) && Files.isReadable(encodedFilePath)) {
                        long fileSize = Files.size(encodedFilePath);
                        log.info("成功获取临时文件流(URL编码匹配): {}, 大小: {} bytes", encodedFilePath, fileSize);
                        return Files.newInputStream(encodedFilePath);
                    }
                } catch (java.io.UnsupportedEncodingException e) {
                    log.warn("URL编码失败: {}", fileName, e);
                }
            }

            // 策略4：模糊匹配 - 列出目录中的所有文件，寻找相似的文件名
            Path orderDir = Paths.get(tempBasePath, tempOrderId);
            if (Files.exists(orderDir)) {
                log.debug("尝试模糊匹配，目录: {}", orderDir);
                try {
                    java.util.Optional<Path> matchedFile = Files.list(orderDir)
                        .filter(Files::isRegularFile)
                        .filter(path -> {
                            String actualFileName = path.getFileName().toString();
                            // 检查原始文件名、编码文件名、解码文件名是否匹配
                            return actualFileName.equals(fileName) ||
                                   actualFileName.equals(urlDecode(fileName)) ||
                                   actualFileName.equals(urlEncode(fileName));
                        })
                        .findFirst();

                    if (matchedFile.isPresent()) {
                        Path matchedPath = matchedFile.get();
                        long fileSize = Files.size(matchedPath);
                        log.info("成功获取临时文件流(模糊匹配): {} -> {}, 大小: {} bytes",
                            fileName, matchedPath, fileSize);
                        return Files.newInputStream(matchedPath);
                    }
                } catch (Exception e) {
                    log.warn("模糊匹配失败", e);
                }
            }

            log.warn("所有策略都失败，临时文件不存在: tempOrderId={}, fileName={}", tempOrderId, fileName);
            return null;

        } catch (Exception e) {
            log.error("获取临时文件流失败: tempOrderId={}, fileName={}", tempOrderId, fileName, e);
            return null;
        }
    }

    @Override
    public String getTempFileUrl(String tempOrderId, String fileName) {
        TempFileMetadata metadata = getFileMetadata(tempOrderId, fileName);
        return metadata != null ? metadata.getAccessUrl() : null;
    }

    @Override
    public List<String> getTempFileUrls(String tempOrderId) {
        try {
            Path orderDir = Paths.get(tempBasePath, tempOrderId);
            if (!Files.exists(orderDir)) {
                return new ArrayList<>();
            }

            return Files.list(orderDir)
                    .filter(Files::isRegularFile)
                    .map(path -> generateAccessUrl(tempOrderId, path.getFileName().toString()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取临时文件URL列表失败: tempOrderId={}", tempOrderId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean deleteTempFile(String tempOrderId, String fileName) {
        try {
            Path filePath = getTempFilePath(tempOrderId, fileName);
            boolean deleted = Files.deleteIfExists(filePath);

            // 删除Redis中的元数据
            deleteFileMetadata(tempOrderId, fileName);

            log.info("删除临时文件: tempOrderId={}, fileName={}, success={}",
                    tempOrderId, fileName, deleted);
            return deleted;

        } catch (Exception e) {
            log.error("删除临时文件失败: tempOrderId={}, fileName={}", tempOrderId, fileName, e);
            return false;
        }
    }

    @Override
    public boolean deleteTempFiles(String tempOrderId) {
        if (tempOrderId == null || tempOrderId.trim().isEmpty()) {
            log.warn("临时订单ID为空，跳过删除操作");
            return false;
        }

        boolean success = true;
        int deletedFileCount = 0;

        try {
            Path orderDir = Paths.get(tempBasePath, tempOrderId);

            // 如果目录不存在，只需要清理Redis元数据
            if (!Files.exists(orderDir)) {
                log.debug("临时订单{}目录不存在，只清理元数据", tempOrderId);
                deleteAllFileMetadata(tempOrderId);
                return true;
            }

            // 删除目录下的所有文件
            try (Stream<Path> files = Files.list(orderDir)) {
                List<Path> fileList = files.collect(Collectors.toList());

                for (Path filePath : fileList) {
                    try {
                        if (Files.isRegularFile(filePath)) {
                            boolean deleted = Files.deleteIfExists(filePath);
                            if (deleted) {
                                deletedFileCount++;
                                log.debug("删除临时文件: {}", filePath.getFileName());
                            } else {
                                log.warn("文件删除失败: {}", filePath);
                                success = false;
                            }
                        }
                    } catch (Exception e) {
                        log.error("删除文件失败: {}", filePath, e);
                        success = false;
                    }
                }
            }

            // 删除目录
            try {
                boolean dirDeleted = Files.deleteIfExists(orderDir);
                if (!dirDeleted && Files.exists(orderDir)) {
                    log.warn("目录删除失败: {}", orderDir);
                    success = false;
                }
            } catch (Exception e) {
                log.error("删除目录失败: {}", orderDir, e);
                success = false;
            }

            // 删除Redis中的所有元数据
            deleteAllFileMetadata(tempOrderId);

            if (success) {
                log.info("删除临时订单{}所有文件成功，共删除{}个文件", tempOrderId, deletedFileCount);
            } else {
                log.warn("删除临时订单{}文件部分失败，已删除{}个文件", tempOrderId, deletedFileCount);
            }

            return success;

        } catch (Exception e) {
            log.error("删除临时订单{}文件失败，已删除{}个文件", tempOrderId, deletedFileCount, e);

            // 即使文件删除失败，也要尝试清理Redis元数据
            try {
                deleteAllFileMetadata(tempOrderId);
            } catch (Exception metaException) {
                log.error("清理临时订单{}元数据失败", tempOrderId, metaException);
            }

            return false;
        }
    }

    @Override
    public int cleanExpiredTempFiles() {
        // 这里可以实现基于文件创建时间的清理逻辑
        // 由于Redis会自动过期，主要清理文件系统中的孤儿文件
        log.info("执行临时文件清理任务");
        return 0;
    }

    @Override
    public boolean tempFileExists(String tempOrderId, String fileName) {
        try {
            Path filePath = getTempFilePath(tempOrderId, fileName);
            boolean exists = Files.exists(filePath);

            log.debug("检查临时文件是否存在: tempOrderId={}, fileName={}, path={}, exists={}",
                    tempOrderId, fileName, filePath.toAbsolutePath(), exists);

            if (!exists) {
                // 检查父目录是否存在
                Path parentDir = filePath.getParent();
                boolean parentExists = Files.exists(parentDir);
                log.debug("父目录存在检查: path={}, exists={}", parentDir.toAbsolutePath(), parentExists);

                if (parentExists) {
                    // 列出父目录中的文件
                    try {
                        String[] files = parentDir.toFile().list();
                        log.debug("父目录中的文件: {}", files != null ? String.join(", ", files) : "null");
                    } catch (Exception e) {
                        log.warn("无法列出父目录文件: {}", e.getMessage());
                    }
                }
            }

            return exists;
        } catch (Exception e) {
            log.error("检查临时文件存在性失败: tempOrderId={}, fileName={}", tempOrderId, fileName, e);
            return false;
        }
    }

    @Override
    public TempFileMetadata getTempFileMetadata(String tempOrderId, String fileName) {
        return getFileMetadata(tempOrderId, fileName);
    }

    // ========================================
    // 私有辅助方法
    // ========================================

    /**
     * 创建临时订单目录
     * 改进版本：确保目录创建成功，添加详细的错误处理和日志
     * 特别处理Undertow环境下的路径解析问题
     */
    private Path createTempOrderDirectory(String tempOrderId) throws IOException {
        try {
            // 确保使用当前工作目录作为基准，避免Undertow临时目录问题
            Path workingDir = Paths.get("").toAbsolutePath();
            Path baseDir = workingDir.resolve(tempBasePath);
            Path orderDir = baseDir.resolve(tempOrderId);

            log.info("路径解析详情:");
            log.info("  工作目录: {}", workingDir);
            log.info("  配置路径: {}", tempBasePath);
            log.info("  基础目录: {}", baseDir);
            log.info("  订单目录: {}", orderDir);

            // 检查并创建基础目录
            if (!Files.exists(baseDir)) {
                log.info("创建基础目录: {}", baseDir);
                Files.createDirectories(baseDir);

                // 验证基础目录创建成功
                if (!Files.exists(baseDir)) {
                    throw new IOException("基础目录创建失败: " + baseDir);
                }
            }

            // 创建临时订单目录
            if (!Files.exists(orderDir)) {
                log.info("创建临时订单目录: {}", orderDir);
                Files.createDirectories(orderDir);

                // 验证目录是否创建成功
                if (!Files.exists(orderDir)) {
                    throw new IOException("目录创建失败: " + orderDir);
                }

                // 验证目录是否可写
                if (!Files.isWritable(orderDir)) {
                    throw new IOException("目录不可写: " + orderDir);
                }

                log.info("临时订单目录创建成功: {}", orderDir);
            } else {
                log.debug("临时订单目录已存在: {}", orderDir);
            }

            return orderDir;

        } catch (IOException e) {
            log.error("创建临时订单目录失败: tempOrderId={}, tempBasePath={}, error={}",
                    tempOrderId, tempBasePath, e.getMessage());
            throw new IOException("创建临时订单目录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成唯一文件名
     * 改进版本：处理Unicode文件名，确保文件名安全
     */
    private String generateUniqueFileName(String originalFileName) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String extension = "";
            String safeName = "";

            if (originalFileName != null && !originalFileName.trim().isEmpty()) {
                // 提取文件扩展名
                int lastDotIndex = originalFileName.lastIndexOf(".");
                if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
                    extension = originalFileName.substring(lastDotIndex).toLowerCase();
                    safeName = originalFileName.substring(0, lastDotIndex);
                } else {
                    safeName = originalFileName;
                }

                // 清理文件名，移除不安全字符，保留中文字符
                safeName = safeName.replaceAll("[\\\\/:*?\"<>|]", "_")
                                  .replaceAll("\\s+", "_")
                                  .trim();

                // 限制文件名长度，避免路径过长
                if (safeName.length() > 50) {
                    safeName = safeName.substring(0, 50);
                }

                // 如果清理后文件名为空，使用默认名称
                if (safeName.isEmpty()) {
                    safeName = "file";
                }
            } else {
                safeName = "file";
            }

            // 生成最终文件名：时间戳_随机字符_原始文件名.扩展名
            String uniqueId = UUID.randomUUID().toString().substring(0, 8);
            String finalFileName = timestamp + "_" + uniqueId + "_" + safeName + extension;

            log.debug("生成文件名: {} -> {}", originalFileName, finalFileName);
            return finalFileName;

        } catch (Exception e) {
            // 如果文件名处理失败，使用纯时间戳和随机字符
            log.warn("文件名处理失败，使用默认命名: originalFileName={}, error={}",
                    originalFileName, e.getMessage());
            return System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + ".tmp";
        }
    }

    /**
     * 生成文件访问URL
     * 修复版本：生成完整的URL（包含协议、主机、端口），确保前端可以正确访问
     */
    private String generateAccessUrl(String tempOrderId, String fileName) {
        try {
            // 对文件名进行URL编码，处理中文字符
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8");

            // 构建访问路径
            String accessPath = "/api/temp/files/" + tempOrderId + "/" + encodedFileName;

            // 处理contextPath
            if (contextPath != null && !contextPath.isEmpty()) {
                String cleanContextPath = contextPath.endsWith("/") ?
                    contextPath.substring(0, contextPath.length() - 1) : contextPath;
                accessPath = cleanContextPath + accessPath;
            }

            // 生成完整的URL
            String fullUrl = generateFullUrl(accessPath);

            log.debug("生成访问URL: {} -> {} (host: {}, port: {})", fileName, fullUrl, serverHost, serverPort);
            return fullUrl;

        } catch (Exception e) {
            log.error("生成访问URL失败: tempOrderId={}, fileName={}, error={}",
                    tempOrderId, fileName, e.getMessage());
            // 降级处理：使用简单格式
            return generateFullUrl("/api/temp/files/" + tempOrderId + "/" + fileName);
        }
    }

    /**
     * 生成完整的URL（包含协议、主机、端口）
     */
    private String generateFullUrl(String path) {
        try {
            // 确定主机地址
            String host;
            if (externalHost != null && !externalHost.trim().isEmpty()) {
                // 优先使用配置的外部主机地址
                host = externalHost.trim();
                log.debug("使用配置的外部主机地址: {}", host);
            } else if ("0.0.0.0".equals(serverHost)) {
                // 如果配置为0.0.0.0，尝试获取本机IP
                host = getLocalHostAddress();
                log.debug("自动检测到主机地址: {}", host);
            } else {
                host = serverHost;
                log.debug("使用服务器配置的主机地址: {}", host);
            }

            // 确定端口
            int port = externalPort > 0 ? externalPort : serverPort;

            // 确定协议
            String protocol = forceHttps ? "https" : "http";

            // 构建完整URL
            String fullUrl = protocol + "://" + host + ":" + port + path;

            log.debug("生成完整URL: {} -> {} (protocol: {}, host: {}, port: {})",
                    path, fullUrl, protocol, host, port);
            return fullUrl;

        } catch (Exception e) {
            log.error("生成完整URL失败: path={}", path, e);
            // 降级处理：返回相对路径
            return path;
        }
    }

    /**
     * 获取本机IP地址
     */
    private String getLocalHostAddress() {
        try {
            // 优先使用配置的IP地址
            String configuredHost = System.getProperty("server.host");
            if (configuredHost != null && !configuredHost.isEmpty() && !"0.0.0.0".equals(configuredHost)) {
                return configuredHost;
            }

            // 尝试获取本机IP地址
            java.net.InetAddress localHost = java.net.InetAddress.getLocalHost();
            String hostAddress = localHost.getHostAddress();

            // 避免返回回环地址
            if (!"127.0.0.1".equals(hostAddress) && !"localhost".equals(hostAddress)) {
                return hostAddress;
            }

            // 如果是回环地址，尝试获取网络接口地址
            java.util.Enumeration<java.net.NetworkInterface> networkInterfaces =
                java.net.NetworkInterface.getNetworkInterfaces();

            while (networkInterfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                java.util.Enumeration<java.net.InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    java.net.InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address instanceof java.net.Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }

            // 如果都失败了，返回localhost
            return "localhost";

        } catch (Exception e) {
            log.warn("获取本机IP地址失败，使用localhost: {}", e.getMessage());
            return "localhost";
        }
    }

    /**
     * 获取临时文件路径
     */
    private Path getTempFilePath(String tempOrderId, String fileName) {
        return Paths.get(tempBasePath, tempOrderId, fileName);
    }

    /**
     * 保存文件元数据到Redis
     */
    private void saveFileMetadata(String tempOrderId, String fileName, TempFileMetadata metadata) {
        String key = TEMP_FILE_META_PREFIX + tempOrderId + ":" + fileName;
        RedisUtils.setCacheObject(key, metadata, Duration.ofMinutes(expireMinutes));
    }

    /**
     * 从Redis获取文件元数据
     */
    private TempFileMetadata getFileMetadata(String tempOrderId, String fileName) {
        String key = TEMP_FILE_META_PREFIX + tempOrderId + ":" + fileName;
        return RedisUtils.getCacheObject(key);
    }

    /**
     * 删除文件元数据
     */
    private void deleteFileMetadata(String tempOrderId, String fileName) {
        String key = TEMP_FILE_META_PREFIX + tempOrderId + ":" + fileName;
        RedisUtils.deleteObject(key);
    }

    /**
     * 删除所有文件元数据
     */
    private void deleteAllFileMetadata(String tempOrderId) {
        try {
            String pattern = TEMP_FILE_META_PREFIX + tempOrderId + ":*";
            Collection<String> keys = RedisUtils.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                RedisUtils.deleteObject(keys);
                log.info("删除临时订单{}的{}个文件元数据", tempOrderId, keys.size());
            } else {
                log.debug("临时订单{}没有文件元数据需要删除", tempOrderId);
            }
        } catch (Exception e) {
            log.error("删除临时订单{}文件元数据失败", tempOrderId, e);
        }
    }

    /**
     * URL解码辅助方法
     */
    private String urlDecode(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        try {
            return java.net.URLDecoder.decode(str, "UTF-8");
        } catch (java.io.UnsupportedEncodingException e) {
            log.warn("URL解码失败: {}", str, e);
            return str;
        }
    }

    /**
     * URL编码辅助方法
     */
    private String urlEncode(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        try {
            return java.net.URLEncoder.encode(str, "UTF-8");
        } catch (java.io.UnsupportedEncodingException e) {
            log.warn("URL编码失败: {}", str, e);
            return str;
        }
    }
}

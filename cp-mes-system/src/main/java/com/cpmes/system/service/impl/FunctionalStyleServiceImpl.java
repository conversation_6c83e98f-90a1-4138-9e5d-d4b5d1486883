package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.cpmes.common.core.domain.BaseEntity;
import com.cpmes.common.core.domain.entity.SysUser;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cpmes.system.domain.bo.FunctionalStyleBo;
import com.cpmes.system.domain.vo.FunctionalStyleVo;
import com.cpmes.system.domain.FunctionalStyle;
import com.cpmes.system.mapper.FunctionalStyleMapper;
import com.cpmes.system.service.IFunctionalStyleService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能系列款式Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class FunctionalStyleServiceImpl implements IFunctionalStyleService {

    private final FunctionalStyleMapper baseMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 查询功能系列款式
     */
    @Override
    @DS("slave")
    public FunctionalStyleVo queryById(Integer styleId) {
        return baseMapper.selectVoById(styleId);
    }

    /**
     * 查询功能系列款式列表
     */
    @Override
    @DS("slave")
    public TableDataInfo<FunctionalStyleVo> queryPageList(FunctionalStyleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FunctionalStyle> lqw = buildQueryWrapper(bo);
        Page<FunctionalStyleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }
    
    /**
     * 查询用户信息（使用主库）
     */
    @DS("master")
    private List<SysUser> getUsersByUsernames(List<String> usernames) {
        if (ObjectUtil.isEmpty(usernames)) {
            return new ArrayList<>();
        }
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, usernames));
    }

    /**
     * 查询功能系列款式列表
     */
    @Override
    @DS("slave")
    public List<FunctionalStyleVo> queryList(FunctionalStyleBo bo) {
        LambdaQueryWrapper<FunctionalStyle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FunctionalStyle> buildQueryWrapper(FunctionalStyleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FunctionalStyle> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getStyleName()), FunctionalStyle::getStyleName, bo.getStyleName());
        lqw.eq(StringUtils.isNotBlank(bo.getField1()), FunctionalStyle::getField1, bo.getField1());
        lqw.eq(StringUtils.isNotBlank(bo.getField2()), FunctionalStyle::getField2, bo.getField2());
        lqw.eq(StringUtils.isNotBlank(bo.getField3()), FunctionalStyle::getField3, bo.getField3());
        lqw.between(bo.getParams().get("startTime") != null && bo.getParams().get("endTime") != null, FunctionalStyle::getCreateTime, bo.getParams().get("startTime"), bo.getParams().get("endTime"));
        return lqw;
    }

    /**
     * 新增功能系列款式
     */
    @Override
    @DS("slave")
    public Boolean insertByBo(FunctionalStyleBo bo) {
        FunctionalStyle add = BeanUtil.toBean(bo, FunctionalStyle.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStyleId(add.getStyleId());
        }
        return flag;
    }

    /**
     * 修改功能系列款式
     */
    @Override
    @DS("slave")
    public Boolean updateByBo(FunctionalStyleBo bo) {
        FunctionalStyle update = BeanUtil.toBean(bo, FunctionalStyle.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FunctionalStyle entity) {
        // 校验款式名称唯一性
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<FunctionalStyle>().eq(FunctionalStyle::getStyleName, entity.getStyleName())
            .ne(ObjectUtil.isNotNull(entity.getStyleId()), FunctionalStyle::getStyleId, entity.getStyleId()));
        if (exist) {
            throw new ServiceException("款式名称已存在");
        }
    }

    /**
     * 批量删除功能系列款式
     */
    @Override
    @DS("slave")
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
} 
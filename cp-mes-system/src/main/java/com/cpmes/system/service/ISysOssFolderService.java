package com.cpmes.system.service;

import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.system.domain.bo.SysOssFolderBo;
import com.cpmes.system.domain.vo.SysOssFolderVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件夹管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysOssFolderService {

    /**
     * 查询文件夹列表
     */
    TableDataInfo<SysOssFolderVo> queryPageList(SysOssFolderBo bo, PageQuery pageQuery);

    /**
     * 查询文件夹树（用户私有）
     */
    List<SysOssFolderVo> queryPrivateFolderTree(Long userId);

    /**
     * 查询文件夹树（用户共享）
     */
    List<SysOssFolderVo> querySharedFolderTree(Long userId);

    /**
     * 根据主键查询文件夹
     */
    SysOssFolderVo queryById(Long folderId);

    /**
     * 新增文件夹
     */
    Boolean insertByBo(SysOssFolderBo bo);

    /**
     * 修改文件夹
     */
    Boolean updateByBo(SysOssFolderBo bo);

    /**
     * 校验并删除数据
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据路径查询文件夹
     */
    SysOssFolderVo queryByPath(String folderPath, Long userId, String storageStrategy);

    /**
     * 创建文件夹（包括在对象存储中创建）
     */
    Boolean createFolder(SysOssFolderBo bo, Long userId);

} 
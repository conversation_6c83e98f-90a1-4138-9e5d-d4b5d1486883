package com.cpmes.system.service;

import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.system.domain.bo.InventoryDetailBo;
import com.cpmes.system.domain.vo.InventoryDetailVo;
import com.cpmes.system.domain.dto.StorageLocationValidationDto;
import com.cpmes.system.domain.vo.StorageLocationValidationVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 库存明细Service接口
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public interface IInventoryDetailService {

    /**
     * 查询库存明细
     */
    InventoryDetailVo queryById(Long detailId);

    /**
     * 查询库存明细列表
     */
    TableDataInfo<InventoryDetailVo> queryPageList(InventoryDetailBo bo, PageQuery pageQuery);

    /**
     * 查询库存明细列表
     */
    List<InventoryDetailVo> queryList(InventoryDetailBo bo);

    /**
     * 根据区域编码查询库存明细列表
     */
    TableDataInfo<InventoryDetailVo> queryPageListByZoneCode(String zoneCode, PageQuery pageQuery);

    /**
     * 根据区域ID查询库存明细列表（保留兼容性）
     */
    TableDataInfo<InventoryDetailVo> queryPageListByZone(Long zoneId, PageQuery pageQuery);

    /**
     * 新增库存明细
     */
    Boolean insertByBo(InventoryDetailBo bo);

    /**
     * 修改库存明细
     */
    Boolean updateByBo(InventoryDetailBo bo);

    /**
     * 校验并批量删除库存明细信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取库存统计信息
     */
    Map<String, Object> getInventoryStatistics(InventoryDetailBo bo);

    /**
     * 获取低库存预警列表
     */
    List<InventoryDetailVo> getLowStockAlert();

    /**
     * 库存调整
     */
    Boolean adjustInventoryStock(Long detailId, Integer adjustQuantity, String adjustType, String adjustReason, String remark);

    /**
     * 库存调整（支持指定操作类型和来源单据号）
     */
    Boolean adjustInventoryStock(Long detailId, Integer adjustQuantity, String adjustType, String adjustReason, String remark, String operationType, String sourceDocument);

    /**
     * 库存移库操作（使用区域编码）
     */
    Boolean moveInventoryStockByCode(Long sourceDetailId, String targetZoneCode, Integer moveQuantity, String moveReason);

    /**
     * 库存移库操作（保留兼容性）
     */
    Boolean moveInventoryStock(Long sourceDetailId, Long targetZoneId, Integer moveQuantity, String moveReason);

    /**
     * 批量更新库存状态
     */
    Boolean batchUpdateStatus(List<Long> detailIds, String status);

    /**
     * 获取库存追溯信息
     */
    List<Map<String, Object>> getInventoryTrace(Long detailId);

    /**
     * 根据物料和区域查询库存明细
     */
    InventoryDetailVo queryByMaterialAndZone(String materialId, Long zoneId, String batchNo);

    /**
     * 校验库存明细唯一性（基于区域ID）
     *
     * @param materialName 物料名称
     * @param materialType 物料类型
     * @param batchNo 批次号
     * @param zoneId 存储区域ID
     * @param detailId 明细ID（用于更新时排除自身）
     * @return 校验结果，true表示唯一，false表示重复
     */
    boolean checkInventoryDetailUnique(String materialName, String materialType, String batchNo, Long zoneId, Long detailId);

    /**
     * 校验库存明细唯一性（基于区域编码）
     *
     * @param materialName 物料名称
     * @param materialType 物料类型
     * @param batchNo 批次号
     * @param zoneCode 存储区域编码
     * @param detailId 明细ID（用于更新时排除自身）
     * @return 校验结果，true表示唯一，false表示重复
     */
    boolean checkInventoryDetailUniqueByCode(String materialName, String materialType, String batchNo, String zoneCode, Long detailId);

    /**
     * 校验物料ID唯一性（全局唯一性）
     *
     * @param materialId 物料ID
     * @return 校验结果，true表示唯一，false表示重复
     */
    Boolean checkMaterialIdUnique(String materialId);

    /**
     * 校验物料ID在特定上下文中的唯一性
     * 基于物料ID + 区域编码 + 批次号的组合唯一性校验
     *
     * @param materialId 物料ID
     * @param zoneCode 区域编码
     * @param batchNo 批次号（可选）
     * @param detailId 当前记录ID（更新时排除自身）
     * @return true表示唯一，false表示重复
     */
    Boolean checkMaterialIdUniqueInContext(String materialId, String zoneCode, String batchNo, Long detailId);

    /**
     * 生成下一个可用的物料ID
     *
     * @param materialType 物料类型
     * @return 下一个可用的物料ID
     */
    String generateNextMaterialId(String materialType);

    /**
     * 根据物料和区域编码查询库存明细
     */
    InventoryDetailVo queryByMaterialAndZoneCode(String materialId, String zoneCode, String batchNo);

    /**
     * 根据产品编码查询库存明细列表
     */
    List<InventoryDetailVo> queryByProductNumber(String productNumber);

    /**
     * 根据系列ID查询库存明细列表
     */
    List<InventoryDetailVo> queryBySeriesId(Integer seriesId);

    /**
     * 根据供应商ID查询库存明细列表
     */
    List<InventoryDetailVo> queryBySupplierId(Integer supplierId);

    /**
     * 检查数据同步状态
     */
    Map<String, Object> checkSyncStatus();

    /**
     * 判断物料是否适合存放在指定区域（使用区域编码）
     *
     * @param materialId 物料ID
     * @param materialType 物料类型
     * @param zoneCode 区域编码
     * @param batchNo 批次号（可选）
     * @return true-适合存放，false-不适合存放
     */
    Boolean validateMaterialZoneCompatibilityByCode(String materialId, String materialType, String zoneCode, String batchNo);

    /**
     * 判断物料是否适合存放在指定区域（保留兼容性）
     *
     * @param materialId 物料ID
     * @param materialType 物料类型
     * @param zoneId 区域ID
     * @param batchNo 批次号（可选）
     * @return true-适合存放，false-不适合存放
     */
    Boolean validateMaterialZoneCompatibility(String materialId, String materialType, Long zoneId, String batchNo);

    /**
     * 库存调整（扩展方法，支持文档规范中的所有参数）
     *
     * @param detailId 库存明细ID（保留原参数，确保向后兼容）
     * @param adjustQuantity 调整数量
     * @param adjustType 调整类型（increase/decrease）
     * @param adjustReason 调整原因
     * @param remark 备注
     * @param operationType 操作类型（inbound/outbound/transfer/adjust/check）
     * @param sourceDocument 来源单据号
     * @param zoneCode 区域编码（扩展参数，可为空时从detailId获取）
     * @param itemCode 物料编码（扩展参数，可为空时从detailId获取）
     * @param itemName 物料名称（扩展参数，可为空时从detailId获取）
     * @param unitOfMeasure 计量单位（扩展参数）
     * @param lotNumber 批次号（扩展参数，可为空时从detailId获取）
     * @return 操作结果，包含操作记录ID、区域编码、操作后库存数量等信息
     */
    Map<String, Object> adjustInventoryStockExtended(Long detailId, Integer adjustQuantity, String adjustType,
                                                     String adjustReason, String remark, String operationType,
                                                     String sourceDocument, String zoneCode, String itemCode,
                                                     String itemName, String unitOfMeasure, String lotNumber);

    /**
     * 库存调整（完整扩展方法，支持采购入库的所有字段映射）
     *
     * @param detailId 库存明细ID（保留原参数，确保向后兼容）
     * @param adjustQuantity 调整数量
     * @param adjustType 调整类型（increase/decrease）
     * @param adjustReason 调整原因
     * @param remark 备注
     * @param operationType 操作类型（inbound/outbound/transfer/adjust/check）
     * @param sourceDocument 来源单据号
     * @param zoneCode 区域编码（扩展参数，可为空时从detailId获取）
     * @param itemCode 物料编码（扩展参数，可为空时从detailId获取）
     * @param itemName 物料名称（扩展参数，可为空时从detailId获取）
     * @param unitOfMeasure 计量单位（扩展参数）
     * @param lotNumber 批次号（扩展参数，可为空时从detailId获取）
     * @param boardType 上下板类型（扩展参数，从采购订单获取）
     * @param materialType 物料类型（扩展参数，从采购订单获取）
     * @param supplierId 供应商ID（扩展参数，从采购订单获取）
     * @param supplierName 供应商名称（扩展参数，从采购订单获取）
     * @return 操作结果，包含操作记录ID、区域编码、操作后库存数量等信息
     */
    Map<String, Object> adjustInventoryStockExtendedFull(Long detailId, Integer adjustQuantity, String adjustType,
                                                         String adjustReason, String remark, String operationType,
                                                         String sourceDocument, String zoneCode, String itemCode,
                                                         String itemName, String unitOfMeasure, String lotNumber,
                                                         String boardType, String materialType, Integer supplierId, String supplierName);

    /**
     * 根据物料ID查询物料在各个区域的分布情况
     * 用于"查看分布"功能
     *
     * @param materialId 物料ID，必填，用于精确匹配
     * @param materialType 物料类型，选填
     * @param pageQuery 分页查询条件
     * @return 分页结果
     */
    TableDataInfo<InventoryDetailVo> queryMaterialZoneDistribution(String materialId, String materialType, PageQuery pageQuery);

    /**
     * 校验物料存放位置是否合规
     * 根据物料类型与仓库类型的匹配关系进行校验
     *
     * @param dto 校验请求参数
     * @return 校验结果
     */
    StorageLocationValidationVo validateStorageLocation(StorageLocationValidationDto dto);

}

package com.cpmes.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.domain.InventoryDetail;
import com.cpmes.system.domain.Material;
import com.cpmes.system.entity.*;
import com.cpmes.system.mapper.InventoryDetailMapper;
import com.cpmes.system.service.IInventoryWarehouseSyncService;
import com.cpmes.system.serviceJenasi.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 库存仓储同步服务实现类（使用直接主键关联）
 *
 * 关联关系：
 * - inventory_detail.material_id = raw_material_warehouse.material_id (主键)
 * - inventory_detail.material_id = component_warehouse.component_id (主键)
 * - inventory_detail.material_id = semi_finished_product.semi_product_id (主键)
 * - inventory_detail.material_id = semi_finished_product_two.semi_product_two_id (主键)
 * - inventory_detail.material_id = product_warehouse.product_id (主键)
 *
 * <AUTHOR>
 * @date 2024-12-22
 */
@RequiredArgsConstructor
@Service
public class InventoryWarehouseSyncServiceImpl implements IInventoryWarehouseSyncService {

    private static final Logger log = LoggerFactory.getLogger(InventoryWarehouseSyncServiceImpl.class);

    // 同步失败计数器
    private final AtomicLong syncFailureCount = new AtomicLong(0);

    // 注入仓储管理服务
    @Resource
    private RawMaterialWarehouseService rawMaterialWarehouseService;
    @Resource
    private ComponentWarehouseService componentWarehouseService;
    @Resource
    private SemiFinishedProductService semiFinishedProductService;
    @Resource
    private SemiFinishedProductTwoService semiFinishedProductTwoService;
    @Resource
    private ProductWarehouseService productWarehouseService;
    @Resource
    private InventoryDetailMapper inventoryDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncInventoryDetailOperation(InventoryDetail inventoryDetail, String operation) {
        if (inventoryDetail == null || StringUtils.isBlank(inventoryDetail.getMaterialType())) {
            log.warn("库存明细信息不完整，跳过同步操作");
            return false;
        }

        try {
            String materialType = inventoryDetail.getMaterialType();
            log.info("开始同步{}操作到仓储管理表 - 物料类型: {}, 物料名称: {}, 物料ID: {}",
                operation, materialType, inventoryDetail.getMaterialName(), inventoryDetail.getMaterialId());

            // 对于新增/入库操作，检查是否已存在相同物料ID的记录，如果存在则转为UPDATE操作
            if ("INSERT".equals(operation) || "MOVE_IN".equals(operation) || "ADJUST_INCREASE".equals(operation)) {
                boolean exists = checkMaterialIdExists(inventoryDetail.getMaterialId(), materialType);
                if (exists) {
                    log.info("发现相同物料ID的记录，将{}操作转换为UPDATE操作 - 物料ID: {}", operation, inventoryDetail.getMaterialId());
                    operation = "UPDATE";
                }
            }

            // 根据物料类型调用对应的仓储管理接口
            switch (materialType) {
                case "原料":
                    return syncToRawMaterialWarehouse(inventoryDetail, operation);
                case "零部件":
                    return syncToComponentWarehouse(inventoryDetail, operation);
                case "一级半成品":
                    return syncToSemiFinishedProduct(inventoryDetail, operation);
                case "二级半成品":
                    return syncToSemiFinishedProductTwo(inventoryDetail, operation);
                case "成品":
                    return syncToProductWarehouse(inventoryDetail, operation);
                default:
                    log.warn("未知的物料类型: {}, 跳过同步", materialType);
                    return false;
            }
        } catch (Exception e) {
            syncFailureCount.incrementAndGet();
            log.error("同步到仓储管理表失败 - 物料类型: {}, 操作: {}",
                    inventoryDetail.getMaterialType(), operation, e);
            throw new RuntimeException("同步到仓储管理表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查指定物料ID是否已存在于对应的仓储管理表中
     */
    private boolean checkMaterialIdExists(String materialId, String materialType) {
        if (StringUtils.isBlank(materialId) || StringUtils.isBlank(materialType)) {
            return false;
        }

        try {
            switch (materialType) {
                case "原料":
                    LambdaQueryWrapper<RawMaterialWarehouse> rawWrapper = new LambdaQueryWrapper<>();
                    rawWrapper.eq(RawMaterialWarehouse::getFields3, materialId);
                    return rawMaterialWarehouseService.count(rawWrapper) > 0;

                case "零部件":
                    LambdaQueryWrapper<ComponentWarehouse> componentWrapper = new LambdaQueryWrapper<>();
                    componentWrapper.eq(ComponentWarehouse::getFields3, materialId);
                    return componentWarehouseService.count(componentWrapper) > 0;

                case "一级半成品":
                    LambdaQueryWrapper<SemiFinishedProduct> semiWrapper = new LambdaQueryWrapper<>();
                    semiWrapper.eq(SemiFinishedProduct::getFields3, materialId);
                    return semiFinishedProductService.count(semiWrapper) > 0;

                case "二级半成品":
                    LambdaQueryWrapper<SemiFinishedProductTwo> semiTwoWrapper = new LambdaQueryWrapper<>();
                    semiTwoWrapper.eq(SemiFinishedProductTwo::getFields3, materialId);
                    return semiFinishedProductTwoService.count(semiTwoWrapper) > 0;

                case "成品":
                    LambdaQueryWrapper<ProductWarehouse> productWrapper = new LambdaQueryWrapper<>();
                    productWrapper.eq(ProductWarehouse::getFields3, materialId);
                    return productWarehouseService.count(productWrapper) > 0;

                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("检查物料ID是否存在失败 - 物料ID: {}, 物料类型: {}", materialId, materialType, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncMaterialOperation(Material material, String operation) {
        if (material == null || StringUtils.isBlank(material.getMaterialName())) {
            log.warn("物料信息不完整，跳过同步操作");
            return false;
        }

        try {
            log.info("开始同步物料{}操作到仓储管理表 - 物料名称: {}", operation, material.getMaterialName());

            // 根据物料属性确定物料类型并同步
            String materialType = material.getMaterialAttribute();
            if (StringUtils.isBlank(materialType)) {
                log.warn("物料属性为空，无法确定同步目标表");
                return false;
            }

            // 创建临时库存明细对象用于同步
            InventoryDetail tempDetail = new InventoryDetail();
            tempDetail.setMaterialId(String.valueOf(material.getMaterialId()));
            tempDetail.setMaterialName(material.getMaterialName());
            tempDetail.setMaterialType(materialType);
            tempDetail.setCurrentStock(0); // 物料操作时库存为0

            return syncInventoryDetailOperation(tempDetail, operation);
        } catch (Exception e) {
            syncFailureCount.incrementAndGet();
            log.error("同步物料操作失败 - 物料名称: {}, 操作: {}", material.getMaterialName(), operation, e);
            throw new RuntimeException("同步物料操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSyncInventoryDetailOperation(List<InventoryDetail> inventoryDetails, String operation) {
        if (inventoryDetails == null || inventoryDetails.isEmpty()) {
            log.warn("库存明细列表为空，跳过批量同步操作");
            return false;
        }

        try {
            log.info("开始批量同步{}操作到仓储管理表 - 数量: {}", operation, inventoryDetails.size());

            for (InventoryDetail detail : inventoryDetails) {
                syncInventoryDetailOperation(detail, operation);
            }

            log.info("批量同步完成 - 操作: {}, 数量: {}", operation, inventoryDetails.size());
            return true;
        } catch (Exception e) {
            syncFailureCount.addAndGet(inventoryDetails.size());
            log.error("批量同步失败 - 操作: {}, 数量: {}", operation, inventoryDetails.size(), e);
            throw new RuntimeException("批量同步失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean validateMaterialTypeCompatibility(String materialType, String materialName) {
        if (StringUtils.isBlank(materialType) || StringUtils.isBlank(materialName)) {
            return false;
        }

        try {
            // 验证物料类型是否支持
            switch (materialType) {
                case "原料":
                case "零部件":
                case "一级半成品":
                case "二级半成品":
                case "成品":
                    return true;
                default:
                    log.warn("不支持的物料类型: {}", materialType);
                    return false;
            }
        } catch (Exception e) {
            log.error("验证物料类型兼容性失败 - 物料类型: {}, 物料名称: {}", materialType, materialName, e);
            return false;
        }
    }

    @Override
    public Integer getWarehouseStockQuantity(String materialType, String materialName) {
        if (StringUtils.isBlank(materialType) || StringUtils.isBlank(materialName)) {
            return 0;
        }

        try {
            switch (materialType) {
                case "原料":
                    RawMaterialWarehouse rawMaterial = findRawMaterialByName(materialName);
                    return rawMaterial != null && rawMaterial.getCurrentStock() != null ? rawMaterial.getCurrentStock() : 0;
                case "零部件":
                    List<ComponentWarehouse> components = componentWarehouseService.findByName(materialName);
                    return !components.isEmpty() && components.get(0).getCurrentStock() != null ? components.get(0).getCurrentStock() : 0;
                case "一级半成品":
                    SemiFinishedProduct semiProduct = findSemiFinishedProductByName(materialName);
                    return semiProduct != null && semiProduct.getCurrentStock() != null ? semiProduct.getCurrentStock() : 0;
                case "二级半成品":
                    SemiFinishedProductTwo semiProductTwo = findSemiFinishedProductTwoByName(materialName);
                    return semiProductTwo != null && semiProductTwo.getCurrentStock() != null ? semiProductTwo.getCurrentStock() : 0;
                case "成品":
                    ProductWarehouse product = findProductWarehouseByName(materialName);
                    return product != null && product.getCurrentStock() != null ? product.getCurrentStock() : 0;
                default:
                    return 0;
            }
        } catch (Exception e) {
            log.error("获取仓储库存数量失败 - 物料类型: {}, 物料名称: {}", materialType, materialName, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncStockAdjustment(String materialType, String materialName, Integer adjustQuantity,
                                      String adjustReason, String operator) {
        try {
            log.info("同步库存调整 - 物料类型: {}, 物料名称: {}, 调整数量: {}",
                    materialType, materialName, adjustQuantity);

            // 创建临时库存明细对象
            InventoryDetail tempDetail = new InventoryDetail();
            tempDetail.setMaterialType(materialType);
            tempDetail.setMaterialName(materialName);
            tempDetail.setCurrentStock(Math.abs(adjustQuantity));
            tempDetail.setRemark(adjustReason + " (操作员: " + operator + ")");

            // 根据调整数量确定操作类型
            String operation = adjustQuantity > 0 ? "ADJUST_INCREASE" : "ADJUST_DECREASE";

            return syncInventoryDetailOperation(tempDetail, operation);
        } catch (Exception e) {
            syncFailureCount.incrementAndGet();
            log.error("同步库存调整失败 - 物料类型: {}, 物料名称: {}, 调整数量: {}",
                    materialType, materialName, adjustQuantity, e);
            throw new RuntimeException("同步库存调整失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncInOutboundOperation(String materialType, String materialName, Integer quantity,
                                          String operationType, String operator, String remark) {
        try {
            log.info("同步出入库操作 - 物料类型: {}, 物料名称: {}, 数量: {}, 操作类型: {}",
                    materialType, materialName, quantity, operationType);

            // 创建临时库存明细对象
            InventoryDetail tempDetail = new InventoryDetail();
            tempDetail.setMaterialType(materialType);
            tempDetail.setMaterialName(materialName);
            tempDetail.setCurrentStock(quantity);
            tempDetail.setRemark(remark + " (操作员: " + operator + ")");

            // 根据操作类型确定同步操作 - 移除移库操作，只支持基本增减操作
            String operation = "INBOUND".equals(operationType) ? "INSERT" : "ADJUST_DECREASE";

            return syncInventoryDetailOperation(tempDetail, operation);
        } catch (Exception e) {
            syncFailureCount.incrementAndGet();
            log.error("同步出入库操作失败 - 物料类型: {}, 物料名称: {}, 操作类型: {}",
                    materialType, materialName, operationType, e);
            throw new RuntimeException("同步出入库操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean checkWarehouseSystemConnection() {
        try {
            // 检查各个仓储服务是否可用
            boolean rawMaterialOk = rawMaterialWarehouseService != null;
            boolean componentOk = componentWarehouseService != null;
            boolean semiProductOk = semiFinishedProductService != null;
            boolean semiProductTwoOk = semiFinishedProductTwoService != null;
            boolean productOk = productWarehouseService != null;

            boolean allOk = rawMaterialOk && componentOk && semiProductOk && semiProductTwoOk && productOk;

            log.info("仓储系统连接检查 - 原料: {}, 零部件: {}, 一级半成品: {}, 二级半成品: {}, 成品: {}, 整体: {}",
                    rawMaterialOk, componentOk, semiProductOk, semiProductTwoOk, productOk, allOk);

            return allOk;
        } catch (Exception e) {
            log.error("检查仓储系统连接失败", e);
            return false;
        }
    }

    @Override
    public Long getSyncFailureCount() {
        return syncFailureCount.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean retrySyncFailures() {
        try {
            log.info("开始重试失败的同步操作，当前失败数量: {}", syncFailureCount.get());

            // 重置失败计数器
            long previousFailureCount = syncFailureCount.getAndSet(0);

            log.info("重试完成，重置失败计数: {} -> 0", previousFailureCount);
            return true;
        } catch (Exception e) {
            log.error("重试失败的同步操作出错", e);
            return false;
        }
    }

    // ==================== 私有方法：具体同步实现（直接主键关联） ====================

    /**
     * 同步到原料仓库表
     * 关联关系：inventory_detail.material_id = raw_material_warehouse.fields3 (物料ID关联)
     */
    private Boolean syncToRawMaterialWarehouse(InventoryDetail inventoryDetail, String operation) {
        try {
            switch (operation) {
                case "INSERT":
                case "MOVE_IN":
                case "ADJUST_INCREASE":
                    // 注意：由于主方法已进行操作转换，到这里的INSERT操作应该是真正的新增
                    // 直接新增原料记录，使用聚合后的库存信息
                    AggregatedStockInfo insertStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                    RawMaterialWarehouse newRaw = new RawMaterialWarehouse();
                    newRaw.setFields3(inventoryDetail.getMaterialId().toString()); // 设置物料ID关联
                    newRaw.setMaterialName(inventoryDetail.getMaterialName());
                    newRaw.setMaterialType(inventoryDetail.getMaterialType());
                    newRaw.setCurrentStock(insertStockInfo.getCurrentStock()); // 使用聚合总库存
                    newRaw.setInboundQuantity(insertStockInfo.getInboundQuantity()); // 使用聚合总入库量
                    newRaw.setOutboundQuantity(insertStockInfo.getOutboundQuantity()); // 使用聚合总出库量
                    newRaw.setFields2(insertStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                    newRaw.setRemark("从库存明细同步: " + (inventoryDetail.getRemark() != null ? inventoryDetail.getRemark() : ""));
                    newRaw.setUpdatedTime(LocalDateTime.now());
                    log.info("物料ID[{}]新增仓储系统记录，聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                            inventoryDetail.getMaterialId(), insertStockInfo.getCurrentStock(), 
                            insertStockInfo.getInboundQuantity(), insertStockInfo.getOutboundQuantity());
                    return rawMaterialWarehouseService.save(newRaw);

                case "UPDATE":
                case "ADJUST":
                    // 更新：使用fields3字段查找并同步聚合后的库存信息
                    LambdaQueryWrapper<RawMaterialWarehouse> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(RawMaterialWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    RawMaterialWarehouse updateRaw = rawMaterialWarehouseService.getOne(updateWrapper);

                    if (updateRaw != null) {
                        // 计算该物料ID的聚合库存信息并同步
                        AggregatedStockInfo updateStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                        updateRaw.setCurrentStock(updateStockInfo.getCurrentStock());
                        updateRaw.setInboundQuantity(updateStockInfo.getInboundQuantity());
                        updateRaw.setOutboundQuantity(updateStockInfo.getOutboundQuantity());
                        updateRaw.setFields2(updateStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                        updateRaw.setMaterialName(inventoryDetail.getMaterialName()); // 同步名称变更
                        updateRaw.setUpdatedTime(LocalDateTime.now());
                        log.info("物料ID[{}]更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                inventoryDetail.getMaterialId(), updateStockInfo.getCurrentStock(),
                                updateStockInfo.getInboundQuantity(), updateStockInfo.getOutboundQuantity());
                        return rawMaterialWarehouseService.updateById(updateRaw);
                    }
                    return false;

                case "DELETE":
                case "MOVE_OUT":
                case "ADJUST_DECREASE":
                    // 删除或移出：智能处理库存数量同步
                    LambdaQueryWrapper<RawMaterialWarehouse> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.eq(RawMaterialWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    RawMaterialWarehouse deleteRaw = rawMaterialWarehouseService.getOne(deleteWrapper);

                    if (deleteRaw != null) {
                        if ("DELETE".equals(operation)) {
                            // 删除操作：检查该物料ID是否还有其他库存明细记录
                            boolean hasOtherRecords = hasOtherInventoryRecords(inventoryDetail.getMaterialId(), inventoryDetail.getDetailId());
                            if (hasOtherRecords) {
                                // 还有其他记录，计算聚合后的库存信息并更新
                                AggregatedStockInfo deleteStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                                deleteRaw.setCurrentStock(deleteStockInfo.getCurrentStock());
                                deleteRaw.setInboundQuantity(deleteStockInfo.getInboundQuantity());
                                deleteRaw.setOutboundQuantity(deleteStockInfo.getOutboundQuantity());
                                deleteRaw.setFields2(deleteStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                                deleteRaw.setMaterialName(inventoryDetail.getMaterialName());
                                deleteRaw.setUpdatedTime(LocalDateTime.now());
                                log.info("物料ID[{}]删除库存明细后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                        inventoryDetail.getMaterialId(), deleteStockInfo.getCurrentStock(),
                                        deleteStockInfo.getInboundQuantity(), deleteStockInfo.getOutboundQuantity());
                                return rawMaterialWarehouseService.updateById(deleteRaw);
                            } else {
                                // 没有其他记录，删除仓储管理记录
                                log.info("物料ID[{}]无其他库存明细记录，删除仓储系统记录", inventoryDetail.getMaterialId());
                                return rawMaterialWarehouseService.removeById(deleteRaw.getWarehouseMaterialId());
                            }
                        } else {
                            // 移出或调减：使用聚合策略
                            AggregatedStockInfo moveStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                            deleteRaw.setCurrentStock(moveStockInfo.getCurrentStock());
                            deleteRaw.setInboundQuantity(moveStockInfo.getInboundQuantity());
                            deleteRaw.setOutboundQuantity(moveStockInfo.getOutboundQuantity());
                            deleteRaw.setFields2(moveStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                            deleteRaw.setMaterialName(inventoryDetail.getMaterialName());
                            deleteRaw.setUpdatedTime(LocalDateTime.now());
                            log.info("物料ID[{}]移出/调减后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                    inventoryDetail.getMaterialId(), moveStockInfo.getCurrentStock(),
                                    moveStockInfo.getInboundQuantity(), moveStockInfo.getOutboundQuantity());
                            return rawMaterialWarehouseService.updateById(deleteRaw);
                        }
                    }
                    return false;

                default:
                    log.warn("未知的操作类型: {}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("同步到原料仓库表失败 - 物料ID: {}, 操作: {}", inventoryDetail.getMaterialId(), operation, e);
            throw new RuntimeException("同步到原料仓库表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步到零部件仓库表
     * 关联关系：inventory_detail.material_id = component_warehouse.fields3 (物料ID关联)
     */
    private Boolean syncToComponentWarehouse(InventoryDetail inventoryDetail, String operation) {
        try {
            switch (operation) {
                case "INSERT":
                case "MOVE_IN":
                case "ADJUST_INCREASE":
                    // 使用fields3字段进行查找（物料ID关联）
                    LambdaQueryWrapper<ComponentWarehouse> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ComponentWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ComponentWarehouse existingComponent = componentWarehouseService.getOne(queryWrapper);

                    if (existingComponent != null) {
                        // 参考原有仓储系统入库逻辑：使用每日重置机制
                        LocalDateTime lastUpdate = existingComponent.getUpdatedTime();
                        LocalDate today = LocalDate.now();
                        boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                        // 计算本次操作的增量
                        Integer warehouseCurrentStock = existingComponent.getCurrentStock() != null ? existingComponent.getCurrentStock() : 0;
                        Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                        Integer adjustmentQuantity = inventoryCurrentStock - warehouseCurrentStock;

                        if (adjustmentQuantity > 0) {
                            // 入库逻辑：参考原有仓储系统的每日重置机制
                            if (isSameDay) {
                                // 当日已操作过，累加入库数量
                                Integer currentInbound = existingComponent.getInboundQuantity() != null ? existingComponent.getInboundQuantity() : 0;
                                existingComponent.setInboundQuantity(currentInbound + adjustmentQuantity);
                            } else {
                                // 当日首次操作，重置数据
                                existingComponent.setStockQuantity(warehouseCurrentStock);
                                existingComponent.setInboundQuantity(adjustmentQuantity);
                                existingComponent.setOutboundQuantity(0);
                            }
                            existingComponent.setCurrentStock(warehouseCurrentStock + adjustmentQuantity);
                        } else {
                            existingComponent.setCurrentStock(inventoryCurrentStock);
                        }

                        existingComponent.setComponentName(inventoryDetail.getMaterialName());
                        existingComponent.setUpdatedTime(LocalDateTime.now());
                        return componentWarehouseService.updateById(existingComponent);
                    } else {
                        // 新增零部件记录，不设置主键，让数据库自动生成
                        ComponentWarehouse newComponent = new ComponentWarehouse();
                        newComponent.setFields3(inventoryDetail.getMaterialId().toString()); // 设置物料ID关联
                        newComponent.setComponentName(inventoryDetail.getMaterialName());
                        newComponent.setMaterialType(inventoryDetail.getMaterialType());
                        newComponent.setCurrentStock(inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0);
                        newComponent.setInboundQuantity(inventoryDetail.getInboundQuantity() != null ? inventoryDetail.getInboundQuantity() : 0);
                        newComponent.setOutboundQuantity(inventoryDetail.getOutboundQuantity() != null ? inventoryDetail.getOutboundQuantity() : 0);
                        newComponent.setFields2(inventoryDetail.getMinStockQuantity() != null ? inventoryDetail.getMinStockQuantity().toString() : "0"); // 使用fields2字段
                        newComponent.setRemark("从库存明细同步: " + (inventoryDetail.getRemark() != null ? inventoryDetail.getRemark() : ""));
                        return componentWarehouseService.save(newComponent);
                    }

                case "UPDATE":
                case "ADJUST":
                    // 更新：同步聚合后的库存信息
                    LambdaQueryWrapper<ComponentWarehouse> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(ComponentWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ComponentWarehouse updateComponent = componentWarehouseService.getOne(updateWrapper);

                    if (updateComponent != null) {
                        // 计算该物料ID的聚合库存信息并同步
                        AggregatedStockInfo updateStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                        updateComponent.setCurrentStock(updateStockInfo.getCurrentStock());
                        updateComponent.setInboundQuantity(updateStockInfo.getInboundQuantity());
                        updateComponent.setOutboundQuantity(updateStockInfo.getOutboundQuantity());
                        updateComponent.setFields2(updateStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                        updateComponent.setComponentName(inventoryDetail.getMaterialName()); // 同步名称变更
                        updateComponent.setUpdatedTime(LocalDateTime.now());
                        log.info("物料ID[{}]更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                inventoryDetail.getMaterialId(), updateStockInfo.getCurrentStock(),
                                updateStockInfo.getInboundQuantity(), updateStockInfo.getOutboundQuantity());
                        return componentWarehouseService.updateById(updateComponent);
                    }
                    return false;

                case "DELETE":
                case "MOVE_OUT":
                case "ADJUST_DECREASE":
                    // 删除或移出：智能处理库存数量同步
                    LambdaQueryWrapper<ComponentWarehouse> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.eq(ComponentWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ComponentWarehouse deleteComponent = componentWarehouseService.getOne(deleteWrapper);

                    if (deleteComponent != null) {
                        if ("DELETE".equals(operation)) {
                            // 删除操作：检查该物料ID是否还有其他库存明细记录
                            boolean hasOtherRecords = hasOtherInventoryRecords(inventoryDetail.getMaterialId(), inventoryDetail.getDetailId());
                            if (hasOtherRecords) {
                                // 还有其他记录，计算聚合后的库存信息并更新
                                AggregatedStockInfo deleteStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                                deleteComponent.setCurrentStock(deleteStockInfo.getCurrentStock());
                                deleteComponent.setInboundQuantity(deleteStockInfo.getInboundQuantity());
                                deleteComponent.setOutboundQuantity(deleteStockInfo.getOutboundQuantity());
                                deleteComponent.setFields2(deleteStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                                deleteComponent.setComponentName(inventoryDetail.getMaterialName());
                                deleteComponent.setUpdatedTime(LocalDateTime.now());
                                log.info("物料ID[{}]删除库存明细后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                        inventoryDetail.getMaterialId(), deleteStockInfo.getCurrentStock(),
                                        deleteStockInfo.getInboundQuantity(), deleteStockInfo.getOutboundQuantity());
                                return componentWarehouseService.updateById(deleteComponent);
                            } else {
                                // 没有其他记录，删除仓储管理记录
                                log.info("物料ID[{}]无其他库存明细记录，删除仓储系统记录", inventoryDetail.getMaterialId());
                                return componentWarehouseService.removeById(deleteComponent.getComponentId());
                            }
                        } else {
                            // 参考原有仓储系统出库逻辑：使用每日重置机制
                            LocalDateTime lastUpdate = deleteComponent.getUpdatedTime();
                            LocalDate today = LocalDate.now();
                            boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                            // 计算本次操作的减量
                            Integer warehouseCurrentStock = deleteComponent.getCurrentStock() != null ? deleteComponent.getCurrentStock() : 0;
                            Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                            Integer adjustmentQuantity = warehouseCurrentStock - inventoryCurrentStock;

                            if (adjustmentQuantity > 0) {
                                // 出库逻辑：参考原有仓储系统的每日重置机制
                                if (isSameDay) {
                                    // 当日已操作过，累加出库数量
                                    Integer currentOutbound = deleteComponent.getOutboundQuantity() != null ? deleteComponent.getOutboundQuantity() : 0;
                                    deleteComponent.setOutboundQuantity(currentOutbound + adjustmentQuantity);
                                } else {
                                    // 当日首次操作，重置数据
                                    deleteComponent.setStockQuantity(warehouseCurrentStock);
                                    deleteComponent.setOutboundQuantity(adjustmentQuantity);
                                    deleteComponent.setInboundQuantity(0);
                                }
                                deleteComponent.setCurrentStock(warehouseCurrentStock - adjustmentQuantity);
                            } else {
                                deleteComponent.setCurrentStock(inventoryCurrentStock);
                            }

                            deleteComponent.setComponentName(inventoryDetail.getMaterialName());
                            deleteComponent.setUpdatedTime(LocalDateTime.now());
                            return componentWarehouseService.updateById(deleteComponent);
                        }
                    }
                    return false;

                default:
                    log.warn("未知的操作类型: {}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("同步到零部件仓库表失败 - 物料ID: {}, 操作: {}", inventoryDetail.getMaterialId(), operation, e);
            throw new RuntimeException("同步到零部件仓库表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步到一级半成品仓库表
     * 关联关系：inventory_detail.material_id = semi_finished_product.fields3 (物料ID关联)
     */
    private Boolean syncToSemiFinishedProduct(InventoryDetail inventoryDetail, String operation) {
        try {
            switch (operation) {
                case "INSERT":
                case "MOVE_IN":
                case "ADJUST_INCREASE":
                    // 使用fields3字段进行查找（物料ID关联）
                    LambdaQueryWrapper<SemiFinishedProduct> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(SemiFinishedProduct::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProduct existingSemi = semiFinishedProductService.getOne(queryWrapper);

                    if (existingSemi != null) {
                        // 参考原有仓储系统入库逻辑：使用每日重置机制
                        LocalDateTime lastUpdate = existingSemi.getUpdatedTime();
                        LocalDate today = LocalDate.now();
                        boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                        // 计算本次操作的增量
                        Integer warehouseCurrentStock = existingSemi.getCurrentStock() != null ? existingSemi.getCurrentStock() : 0;
                        Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                        Integer adjustmentQuantity = inventoryCurrentStock - warehouseCurrentStock;

                        if (adjustmentQuantity > 0) {
                            // 入库逻辑：参考原有仓储系统的每日重置机制
                            if (isSameDay) {
                                // 当日已操作过，累加入库数量
                                Integer currentInbound = existingSemi.getInboundQuantity() != null ? existingSemi.getInboundQuantity() : 0;
                                existingSemi.setInboundQuantity(currentInbound + adjustmentQuantity);
                            } else {
                                // 当日首次操作，重置数据
                                existingSemi.setStockQuantity(warehouseCurrentStock);
                                existingSemi.setInboundQuantity(adjustmentQuantity);
                                existingSemi.setOutboundQuantity(0);
                            }
                            existingSemi.setCurrentStock(warehouseCurrentStock + adjustmentQuantity);
                        } else {
                            existingSemi.setCurrentStock(inventoryCurrentStock);
                        }

                        existingSemi.setSemiProductName(inventoryDetail.getMaterialName());
                        existingSemi.setUpdatedTime(LocalDateTime.now());
                        return semiFinishedProductService.updateById(existingSemi);
                    } else {
                        // 新增一级半成品记录，不设置主键，让数据库自动生成
                        SemiFinishedProduct newSemi = new SemiFinishedProduct();
                        newSemi.setFields3(inventoryDetail.getMaterialId().toString()); // 设置物料ID关联
                        newSemi.setSemiProductName(inventoryDetail.getMaterialName());
                        newSemi.setMaterialType(inventoryDetail.getMaterialType());
                        newSemi.setCurrentStock(inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0);
                        newSemi.setInboundQuantity(inventoryDetail.getInboundQuantity() != null ? inventoryDetail.getInboundQuantity() : 0);
                        newSemi.setOutboundQuantity(inventoryDetail.getOutboundQuantity() != null ? inventoryDetail.getOutboundQuantity() : 0);
                        newSemi.setStockQuantity(inventoryDetail.getStockQuantity() != null ? inventoryDetail.getStockQuantity() : 0);
                        newSemi.setRemark("从库存明细同步: " + (inventoryDetail.getRemark() != null ? inventoryDetail.getRemark() : ""));
                        // 设置安全库存到预留字段2
                        if (inventoryDetail.getMinStockQuantity() != null) {
                            newSemi.setFields2(inventoryDetail.getMinStockQuantity().toString());
                        }
                        return semiFinishedProductService.save(newSemi);
                    }

                case "UPDATE":
                case "ADJUST":
                    // 更新：同步聚合后的总库存数量
                    LambdaQueryWrapper<SemiFinishedProduct> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(SemiFinishedProduct::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProduct updateSemi = semiFinishedProductService.getOne(updateWrapper);

                    if (updateSemi != null) {
                        // 计算该物料ID的聚合库存信息并同步
                        AggregatedStockInfo updateStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                        updateSemi.setCurrentStock(updateStockInfo.getCurrentStock());
                        updateSemi.setInboundQuantity(updateStockInfo.getInboundQuantity());
                        updateSemi.setOutboundQuantity(updateStockInfo.getOutboundQuantity());
                        updateSemi.setFields2(updateStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                        updateSemi.setSemiProductName(inventoryDetail.getMaterialName()); // 同步名称变更
                        updateSemi.setUpdatedTime(LocalDateTime.now());
                        log.info("物料ID[{}]更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                inventoryDetail.getMaterialId(), updateStockInfo.getCurrentStock(),
                                updateStockInfo.getInboundQuantity(), updateStockInfo.getOutboundQuantity());
                        return semiFinishedProductService.updateById(updateSemi);
                    }
                    return false;

                case "DELETE":
                case "MOVE_OUT":
                case "ADJUST_DECREASE":
                    // 删除或移出：智能处理库存数量同步
                    LambdaQueryWrapper<SemiFinishedProduct> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.eq(SemiFinishedProduct::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProduct deleteSemi = semiFinishedProductService.getOne(deleteWrapper);

                    if (deleteSemi != null) {
                        if ("DELETE".equals(operation)) {
                            // 删除操作：检查该物料ID是否还有其他库存明细记录
                            boolean hasOtherRecords = hasOtherInventoryRecords(inventoryDetail.getMaterialId(), inventoryDetail.getDetailId());
                            if (hasOtherRecords) {
                                // 还有其他记录，计算聚合后的库存信息并更新
                                AggregatedStockInfo deleteStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                                deleteSemi.setCurrentStock(deleteStockInfo.getCurrentStock());
                                deleteSemi.setInboundQuantity(deleteStockInfo.getInboundQuantity());
                                deleteSemi.setOutboundQuantity(deleteStockInfo.getOutboundQuantity());
                                deleteSemi.setFields2(deleteStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                                deleteSemi.setSemiProductName(inventoryDetail.getMaterialName());
                                deleteSemi.setUpdatedTime(LocalDateTime.now());
                                log.info("物料ID[{}]删除库存明细后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                        inventoryDetail.getMaterialId(), deleteStockInfo.getCurrentStock(),
                                        deleteStockInfo.getInboundQuantity(), deleteStockInfo.getOutboundQuantity());
                                return semiFinishedProductService.updateById(deleteSemi);
                            } else {
                                // 没有其他记录，删除仓储管理记录
                                log.info("物料ID[{}]无其他库存明细记录，删除仓储系统记录", inventoryDetail.getMaterialId());
                                return semiFinishedProductService.removeById(deleteSemi.getSemiProductId());
                            }
                        } else {
                            // 参考原有仓储系统出库逻辑：使用每日重置机制
                            LocalDateTime lastUpdate = deleteSemi.getUpdatedTime();
                            LocalDate today = LocalDate.now();
                            boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                            // 计算本次操作的减量
                            Integer warehouseCurrentStock = deleteSemi.getCurrentStock() != null ? deleteSemi.getCurrentStock() : 0;
                            Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                            Integer adjustmentQuantity = warehouseCurrentStock - inventoryCurrentStock;

                            if (adjustmentQuantity > 0) {
                                // 出库逻辑：参考原有仓储系统的每日重置机制
                                if (isSameDay) {
                                    // 当日已操作过，累加出库数量
                                    Integer currentOutbound = deleteSemi.getOutboundQuantity() != null ? deleteSemi.getOutboundQuantity() : 0;
                                    deleteSemi.setOutboundQuantity(currentOutbound + adjustmentQuantity);
                                } else {
                                    // 当日首次操作，重置数据
                                    deleteSemi.setStockQuantity(warehouseCurrentStock);
                                    deleteSemi.setOutboundQuantity(adjustmentQuantity);
                                    deleteSemi.setInboundQuantity(0);
                                }
                                deleteSemi.setCurrentStock(warehouseCurrentStock - adjustmentQuantity);
                            } else {
                                deleteSemi.setCurrentStock(inventoryCurrentStock);
                            }

                            deleteSemi.setSemiProductName(inventoryDetail.getMaterialName());
                            deleteSemi.setUpdatedTime(LocalDateTime.now());
                            return semiFinishedProductService.updateById(deleteSemi);
                        }
                    }
                    return false;

                default:
                    log.warn("未知的操作类型: {}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("同步到一级半成品仓库表失败 - 物料ID: {}, 操作: {}", inventoryDetail.getMaterialId(), operation, e);
            throw new RuntimeException("同步到一级半成品仓库表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步到二级半成品仓库表
     * 关联关系：inventory_detail.material_id = semi_finished_product_two.fields3 (物料ID关联)
     */
    private Boolean syncToSemiFinishedProductTwo(InventoryDetail inventoryDetail, String operation) {
        try {
            switch (operation) {
                case "INSERT":
                case "MOVE_IN":
                case "ADJUST_INCREASE":
                    // 使用fields3字段进行查找（物料ID关联）
                    LambdaQueryWrapper<SemiFinishedProductTwo> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(SemiFinishedProductTwo::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProductTwo existingSemiTwo = semiFinishedProductTwoService.getOne(queryWrapper);

                    if (existingSemiTwo != null) {
                        // 参考原有仓储系统入库逻辑：使用每日重置机制
                        LocalDateTime lastUpdate = existingSemiTwo.getUpdatedTime();
                        LocalDate today = LocalDate.now();
                        boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                        // 计算本次操作的增量
                        Integer warehouseCurrentStock = existingSemiTwo.getCurrentStock() != null ? existingSemiTwo.getCurrentStock() : 0;
                        Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                        Integer adjustmentQuantity = inventoryCurrentStock - warehouseCurrentStock;

                        if (adjustmentQuantity > 0) {
                            // 入库逻辑：参考原有仓储系统的每日重置机制
                            if (isSameDay) {
                                // 当日已操作过，累加入库数量
                                Integer currentInbound = existingSemiTwo.getInboundQuantity() != null ? existingSemiTwo.getInboundQuantity() : 0;
                                existingSemiTwo.setInboundQuantity(currentInbound + adjustmentQuantity);
                            } else {
                                // 当日首次操作，重置数据
                                existingSemiTwo.setStockQuantity(warehouseCurrentStock);
                                existingSemiTwo.setInboundQuantity(adjustmentQuantity);
                                existingSemiTwo.setOutboundQuantity(0);
                            }
                            existingSemiTwo.setCurrentStock(warehouseCurrentStock + adjustmentQuantity);
                        } else {
                            existingSemiTwo.setCurrentStock(inventoryCurrentStock);
                        }

                        existingSemiTwo.setSemiProductTwoName(inventoryDetail.getMaterialName());
                        existingSemiTwo.setUpdatedTime(LocalDateTime.now());
                        return semiFinishedProductTwoService.updateById(existingSemiTwo);
                    } else {
                        // 新增二级半成品记录，不设置主键，让数据库自动生成
                        SemiFinishedProductTwo newSemiTwo = new SemiFinishedProductTwo();
                        newSemiTwo.setFields3(inventoryDetail.getMaterialId().toString()); // 设置物料ID关联
                        newSemiTwo.setSemiProductTwoName(inventoryDetail.getMaterialName());
                        newSemiTwo.setMaterialType(inventoryDetail.getMaterialType());
                        newSemiTwo.setCurrentStock(inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0);
                        newSemiTwo.setInboundQuantity(inventoryDetail.getInboundQuantity() != null ? inventoryDetail.getInboundQuantity() : 0);
                        newSemiTwo.setOutboundQuantity(inventoryDetail.getOutboundQuantity() != null ? inventoryDetail.getOutboundQuantity() : 0);
                        newSemiTwo.setStockQuantity(inventoryDetail.getStockQuantity() != null ? inventoryDetail.getStockQuantity() : 0);
                        newSemiTwo.setRemark("从库存明细同步: " + (inventoryDetail.getRemark() != null ? inventoryDetail.getRemark() : ""));
                        // 设置安全库存到预留字段2
                        if (inventoryDetail.getMinStockQuantity() != null) {
                            newSemiTwo.setFields2(inventoryDetail.getMinStockQuantity().toString());
                        }
                        return semiFinishedProductTwoService.save(newSemiTwo);
                    }

                case "UPDATE":
                case "ADJUST":
                    // 更新：同步聚合后的总库存数量
                    LambdaQueryWrapper<SemiFinishedProductTwo> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(SemiFinishedProductTwo::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProductTwo updateSemiTwo = semiFinishedProductTwoService.getOne(updateWrapper);

                    if (updateSemiTwo != null) {
                        // 计算该物料ID的聚合库存信息并同步
                        AggregatedStockInfo updateStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                        updateSemiTwo.setCurrentStock(updateStockInfo.getCurrentStock());
                        updateSemiTwo.setInboundQuantity(updateStockInfo.getInboundQuantity());
                        updateSemiTwo.setOutboundQuantity(updateStockInfo.getOutboundQuantity());
                        updateSemiTwo.setFields2(updateStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                        updateSemiTwo.setSemiProductTwoName(inventoryDetail.getMaterialName()); // 同步名称变更
                        updateSemiTwo.setUpdatedTime(LocalDateTime.now());
                        log.info("物料ID[{}]更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                inventoryDetail.getMaterialId(), updateStockInfo.getCurrentStock(),
                                updateStockInfo.getInboundQuantity(), updateStockInfo.getOutboundQuantity());
                        return semiFinishedProductTwoService.updateById(updateSemiTwo);
                    }
                    return false;

                case "DELETE":
                case "MOVE_OUT":
                case "ADJUST_DECREASE":
                    // 删除或移出：智能处理库存数量同步
                    LambdaQueryWrapper<SemiFinishedProductTwo> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.eq(SemiFinishedProductTwo::getFields3, inventoryDetail.getMaterialId().toString());
                    SemiFinishedProductTwo deleteSemiTwo = semiFinishedProductTwoService.getOne(deleteWrapper);

                    if (deleteSemiTwo != null) {
                        if ("DELETE".equals(operation)) {
                            // 删除操作：检查该物料ID是否还有其他库存明细记录
                            boolean hasOtherRecords = hasOtherInventoryRecords(inventoryDetail.getMaterialId(), inventoryDetail.getDetailId());
                            if (hasOtherRecords) {
                                // 还有其他记录，计算聚合后的库存信息并更新
                                AggregatedStockInfo deleteStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                                deleteSemiTwo.setCurrentStock(deleteStockInfo.getCurrentStock());
                                deleteSemiTwo.setInboundQuantity(deleteStockInfo.getInboundQuantity());
                                deleteSemiTwo.setOutboundQuantity(deleteStockInfo.getOutboundQuantity());
                                deleteSemiTwo.setFields2(deleteStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                                deleteSemiTwo.setSemiProductTwoName(inventoryDetail.getMaterialName());
                                deleteSemiTwo.setUpdatedTime(LocalDateTime.now());
                                log.info("物料ID[{}]删除库存明细后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                        inventoryDetail.getMaterialId(), deleteStockInfo.getCurrentStock(),
                                        deleteStockInfo.getInboundQuantity(), deleteStockInfo.getOutboundQuantity());
                                return semiFinishedProductTwoService.updateById(deleteSemiTwo);
                            } else {
                                // 没有其他记录，删除仓储管理记录
                                log.info("物料ID[{}]无其他库存明细记录，删除仓储系统记录", inventoryDetail.getMaterialId());
                                return semiFinishedProductTwoService.removeById(deleteSemiTwo.getSemiProductTwoId());
                            }
                        } else {
                            // 参考原有仓储系统出库逻辑：使用每日重置机制
                            LocalDateTime lastUpdate = deleteSemiTwo.getUpdatedTime();
                            LocalDate today = LocalDate.now();
                            boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                            // 计算本次操作的减量
                            Integer warehouseCurrentStock = deleteSemiTwo.getCurrentStock() != null ? deleteSemiTwo.getCurrentStock() : 0;
                            Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                            Integer adjustmentQuantity = warehouseCurrentStock - inventoryCurrentStock;

                            if (adjustmentQuantity > 0) {
                                // 出库逻辑：参考原有仓储系统的每日重置机制
                                if (isSameDay) {
                                    // 当日已操作过，累加出库数量
                                    Integer currentOutbound = deleteSemiTwo.getOutboundQuantity() != null ? deleteSemiTwo.getOutboundQuantity() : 0;
                                    deleteSemiTwo.setOutboundQuantity(currentOutbound + adjustmentQuantity);
                                } else {
                                    // 当日首次操作，重置数据
                                    deleteSemiTwo.setStockQuantity(warehouseCurrentStock);
                                    deleteSemiTwo.setOutboundQuantity(adjustmentQuantity);
                                    deleteSemiTwo.setInboundQuantity(0);
                                }
                                deleteSemiTwo.setCurrentStock(warehouseCurrentStock - adjustmentQuantity);
                            } else {
                                deleteSemiTwo.setCurrentStock(inventoryCurrentStock);
                            }

                            deleteSemiTwo.setSemiProductTwoName(inventoryDetail.getMaterialName());
                            deleteSemiTwo.setUpdatedTime(LocalDateTime.now());
                            return semiFinishedProductTwoService.updateById(deleteSemiTwo);
                        }
                    }
                    return false;

                default:
                    log.warn("未知的操作类型: {}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("同步到二级半成品仓库表失败 - 物料ID: {}, 操作: {}", inventoryDetail.getMaterialId(), operation, e);
            throw new RuntimeException("同步到二级半成品仓库表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步到成品仓库表
     * 关联关系：inventory_detail.material_id = product_warehouse.fields3 (物料ID关联)
     */
    private Boolean syncToProductWarehouse(InventoryDetail inventoryDetail, String operation) {
        try {
            switch (operation) {
                case "INSERT":
                case "MOVE_IN":
                case "ADJUST_INCREASE":
                    // 使用fields3字段进行查找（物料ID关联）
                    LambdaQueryWrapper<ProductWarehouse> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ProductWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ProductWarehouse existingProduct = productWarehouseService.getOne(queryWrapper);

                    if (existingProduct != null) {
                        // 参考原有仓储系统入库逻辑：使用每日重置机制
                        LocalDateTime lastUpdate = existingProduct.getUpdatedTime();
                        LocalDate today = LocalDate.now();
                        boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                        // 计算本次操作的增量
                        Integer warehouseCurrentStock = existingProduct.getCurrentStock() != null ? existingProduct.getCurrentStock() : 0;
                        Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                        Integer adjustmentQuantity = inventoryCurrentStock - warehouseCurrentStock;

                        if (adjustmentQuantity > 0) {
                            // 入库逻辑：参考原有仓储系统的每日重置机制
                            if (isSameDay) {
                                // 当日已操作过，累加入库数量
                                Integer currentInbound = existingProduct.getInboundQuantity() != null ? existingProduct.getInboundQuantity() : 0;
                                existingProduct.setInboundQuantity(currentInbound + adjustmentQuantity);
                            } else {
                                // 当日首次操作，重置数据
                                existingProduct.setStockQuantity(warehouseCurrentStock);
                                existingProduct.setInboundQuantity(adjustmentQuantity);
                                existingProduct.setOutboundQuantity(0);
                            }
                            existingProduct.setCurrentStock(warehouseCurrentStock + adjustmentQuantity);
                        } else {
                            existingProduct.setCurrentStock(inventoryCurrentStock);
                        }

                        existingProduct.setProductName(inventoryDetail.getMaterialName());
                        existingProduct.setUpdatedTime(LocalDateTime.now());
                        return productWarehouseService.updateById(existingProduct);
                    } else {
                        // 新增成品记录，不设置主键，让数据库自动生成
                        ProductWarehouse newProduct = new ProductWarehouse();
                        newProduct.setFields3(inventoryDetail.getMaterialId().toString()); // 设置物料ID关联
                        newProduct.setProductName(inventoryDetail.getMaterialName());
                        newProduct.setMaterialType(inventoryDetail.getMaterialType());
                        newProduct.setCurrentStock(inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0);
                        newProduct.setInboundQuantity(inventoryDetail.getInboundQuantity() != null ? inventoryDetail.getInboundQuantity() : 0);
                        newProduct.setOutboundQuantity(inventoryDetail.getOutboundQuantity() != null ? inventoryDetail.getOutboundQuantity() : 0);
                        newProduct.setStockQuantity(inventoryDetail.getStockQuantity() != null ? inventoryDetail.getStockQuantity() : 0);
                        newProduct.setRemark("从库存明细同步: " + (inventoryDetail.getRemark() != null ? inventoryDetail.getRemark() : ""));
                        // 设置安全库存到预留字段2
                        if (inventoryDetail.getMinStockQuantity() != null) {
                            newProduct.setFields2(inventoryDetail.getMinStockQuantity().toString());
                        }
                        return productWarehouseService.save(newProduct);
                    }

                case "UPDATE":
                case "ADJUST":
                    // 更新：同步聚合后的总库存数量
                    LambdaQueryWrapper<ProductWarehouse> updateWrapper = new LambdaQueryWrapper<>();
                    updateWrapper.eq(ProductWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ProductWarehouse updateProduct = productWarehouseService.getOne(updateWrapper);

                    if (updateProduct != null) {
                        // 计算该物料ID的聚合库存信息并同步
                        AggregatedStockInfo updateStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                        updateProduct.setCurrentStock(updateStockInfo.getCurrentStock());
                        updateProduct.setInboundQuantity(updateStockInfo.getInboundQuantity());
                        updateProduct.setOutboundQuantity(updateStockInfo.getOutboundQuantity());
                        updateProduct.setFields2(updateStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                        updateProduct.setProductName(inventoryDetail.getMaterialName()); // 同步名称变更
                        updateProduct.setUpdatedTime(LocalDateTime.now());
                        log.info("物料ID[{}]更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                inventoryDetail.getMaterialId(), updateStockInfo.getCurrentStock(),
                                updateStockInfo.getInboundQuantity(), updateStockInfo.getOutboundQuantity());
                        return productWarehouseService.updateById(updateProduct);
                    }
                    return false;

                case "DELETE":
                case "MOVE_OUT":
                case "ADJUST_DECREASE":
                    // 删除或移出：智能处理库存数量同步
                    LambdaQueryWrapper<ProductWarehouse> deleteWrapper = new LambdaQueryWrapper<>();
                    deleteWrapper.eq(ProductWarehouse::getFields3, inventoryDetail.getMaterialId().toString());
                    ProductWarehouse deleteProduct = productWarehouseService.getOne(deleteWrapper);

                    if (deleteProduct != null) {
                        if ("DELETE".equals(operation)) {
                            // 删除操作：检查该物料ID是否还有其他库存明细记录
                            boolean hasOtherRecords = hasOtherInventoryRecords(inventoryDetail.getMaterialId(), inventoryDetail.getDetailId());
                            if (hasOtherRecords) {
                                // 还有其他记录，计算聚合后的库存信息并更新
                                AggregatedStockInfo deleteStockInfo = calculateAggregatedStockInfo(inventoryDetail.getMaterialId());
                                deleteProduct.setCurrentStock(deleteStockInfo.getCurrentStock());
                                deleteProduct.setInboundQuantity(deleteStockInfo.getInboundQuantity());
                                deleteProduct.setOutboundQuantity(deleteStockInfo.getOutboundQuantity());
                                deleteProduct.setFields2(deleteStockInfo.getMinStockQuantity().toString()); // 使用聚合安全库存 -> fields2字段
                                deleteProduct.setProductName(inventoryDetail.getMaterialName());
                                deleteProduct.setUpdatedTime(LocalDateTime.now());
                                log.info("物料ID[{}]删除库存明细后更新仓储系统聚合数据 - 库存: {}, 入库: {}, 出库: {}", 
                                        inventoryDetail.getMaterialId(), deleteStockInfo.getCurrentStock(),
                                        deleteStockInfo.getInboundQuantity(), deleteStockInfo.getOutboundQuantity());
                                return productWarehouseService.updateById(deleteProduct);
                            } else {
                                // 没有其他记录，删除仓储管理记录
                                log.info("物料ID[{}]无其他库存明细记录，删除仓储系统记录", inventoryDetail.getMaterialId());
                                return productWarehouseService.removeById(deleteProduct.getProductId());
                            }
                        } else {
                            // 参考原有仓储系统出库逻辑：使用每日重置机制
                            LocalDateTime lastUpdate = deleteProduct.getUpdatedTime();
                            LocalDate today = LocalDate.now();
                            boolean isSameDay = lastUpdate != null && lastUpdate.toLocalDate().equals(today);

                            // 计算本次操作的减量
                            Integer warehouseCurrentStock = deleteProduct.getCurrentStock() != null ? deleteProduct.getCurrentStock() : 0;
                            Integer inventoryCurrentStock = inventoryDetail.getCurrentStock() != null ? inventoryDetail.getCurrentStock() : 0;
                            Integer adjustmentQuantity = warehouseCurrentStock - inventoryCurrentStock;

                            if (adjustmentQuantity > 0) {
                                // 出库逻辑：参考原有仓储系统的每日重置机制
                                if (isSameDay) {
                                    // 当日已操作过，累加出库数量
                                    Integer currentOutbound = deleteProduct.getOutboundQuantity() != null ? deleteProduct.getOutboundQuantity() : 0;
                                    deleteProduct.setOutboundQuantity(currentOutbound + adjustmentQuantity);
                                } else {
                                    // 当日首次操作，重置数据
                                    deleteProduct.setStockQuantity(warehouseCurrentStock);
                                    deleteProduct.setOutboundQuantity(adjustmentQuantity);
                                    deleteProduct.setInboundQuantity(0);
                                }
                                deleteProduct.setCurrentStock(warehouseCurrentStock - adjustmentQuantity);
                            } else {
                                deleteProduct.setCurrentStock(inventoryCurrentStock);
                            }

                            deleteProduct.setProductName(inventoryDetail.getMaterialName());
                            deleteProduct.setUpdatedTime(LocalDateTime.now());
                            return productWarehouseService.updateById(deleteProduct);
                        }
                    }
                    return false;

                default:
                    log.warn("未知的操作类型: {}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("同步到成品仓库表失败 - 物料ID: {}, 操作: {}", inventoryDetail.getMaterialId(), operation, e);
            throw new RuntimeException("同步到成品仓库表失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有方法：查找方法（兼容旧接口） ====================

    /**
     * 根据物料名称查找原料仓库记录（兼容旧方法）
     */
    private RawMaterialWarehouse findRawMaterialByName(String materialName) {
        if (StringUtils.isBlank(materialName)) {
            return null;
        }
        try {
            LambdaQueryWrapper<RawMaterialWarehouse> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RawMaterialWarehouse::getMaterialName, materialName);
            return rawMaterialWarehouseService.getOne(wrapper);
        } catch (Exception e) {
            log.error("查找原料仓库记录失败 - materialName: {}", materialName, e);
            return null;
        }
    }

    /**
     * 根据物料名称查找一级半成品记录（兼容旧方法）
     */
    private SemiFinishedProduct findSemiFinishedProductByName(String materialName) {
        if (StringUtils.isBlank(materialName)) {
            return null;
        }
        try {
            LambdaQueryWrapper<SemiFinishedProduct> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SemiFinishedProduct::getSemiProductName, materialName);
            return semiFinishedProductService.getOne(wrapper);
        } catch (Exception e) {
            log.error("查找一级半成品记录失败 - materialName: {}", materialName, e);
            return null;
        }
    }

    /**
     * 根据物料名称查找二级半成品记录
     */
    private SemiFinishedProductTwo findSemiFinishedProductTwoByName(String materialName) {
        if (StringUtils.isBlank(materialName)) {
            return null;
        }
        try {
            LambdaQueryWrapper<SemiFinishedProductTwo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SemiFinishedProductTwo::getSemiProductTwoName, materialName);
            return semiFinishedProductTwoService.getOne(wrapper);
        } catch (Exception e) {
            log.error("查找二级半成品记录失败 - materialName: {}", materialName, e);
            return null;
        }
    }

    /**
     * 根据物料名称查找成品仓库记录（兼容旧方法）
     */
    private ProductWarehouse findProductWarehouseByName(String materialName) {
        if (StringUtils.isBlank(materialName)) {
            return null;
        }
        try {
            LambdaQueryWrapper<ProductWarehouse> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductWarehouse::getProductName, materialName);
            return productWarehouseService.getOne(wrapper);
        } catch (Exception e) {
            log.error("查找成品仓库记录失败 - materialName: {}", materialName, e);
            return null;
        }
    }

    /**
     * 计算指定物料ID在库存明细表中的聚合库存信息
     * 用于聚合同步到仓储管理系统
     */
    private AggregatedStockInfo calculateAggregatedStockInfo(String materialId) {
        if (StringUtils.isBlank(materialId)) {
            return new AggregatedStockInfo(0, 0, 0, 0);
        }

        try {
            // 注入库存明细Mapper来查询
            LambdaQueryWrapper<InventoryDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InventoryDetail::getMaterialId, materialId);
            wrapper.eq(InventoryDetail::getDelFlag, "0"); // 只查询未删除的记录
            
            List<InventoryDetail> details = inventoryDetailMapper.selectList(wrapper);
            if (details == null || details.isEmpty()) {
                return new AggregatedStockInfo(0, 0, 0, 0);
            }

            // 聚合计算所有数量
            Integer totalCurrentStock = details.stream()
                .mapToInt(detail -> detail.getCurrentStock() != null ? detail.getCurrentStock() : 0)
                .sum();

            Integer totalInboundQuantity = details.stream()
                .mapToInt(detail -> detail.getInboundQuantity() != null ? detail.getInboundQuantity() : 0)
                .sum();

            Integer totalOutboundQuantity = details.stream()
                .mapToInt(detail -> detail.getOutboundQuantity() != null ? detail.getOutboundQuantity() : 0)
                .sum();

            // 聚合安全库存：取最大值（最严格的安全库存要求）
            Integer maxMinStockQuantity = details.stream()
                .mapToInt(detail -> detail.getMinStockQuantity() != null ? detail.getMinStockQuantity() : 0)
                .max()
                .orElse(0);

            log.debug("物料ID[{}]聚合库存统计 - 总当前库存: {}, 总入库: {}, 总出库: {}, 安全库存: {}", 
                materialId, totalCurrentStock, totalInboundQuantity, totalOutboundQuantity, maxMinStockQuantity);

            return new AggregatedStockInfo(totalCurrentStock, totalInboundQuantity, totalOutboundQuantity, maxMinStockQuantity);
        } catch (Exception e) {
            log.error("计算物料ID聚合库存失败 - 物料ID: {}", materialId, e);
            return new AggregatedStockInfo(0, 0, 0, 0);
        }
    }

    /**
     * 聚合库存信息内部类
     */
    private static class AggregatedStockInfo {
        private final Integer currentStock;
        private final Integer inboundQuantity;
        private final Integer outboundQuantity;
        private final Integer minStockQuantity;

        public AggregatedStockInfo(Integer currentStock, Integer inboundQuantity, Integer outboundQuantity, Integer minStockQuantity) {
            this.currentStock = currentStock;
            this.inboundQuantity = inboundQuantity;
            this.outboundQuantity = outboundQuantity;
            this.minStockQuantity = minStockQuantity;
        }

        public Integer getCurrentStock() {
            return currentStock;
        }

        public Integer getInboundQuantity() {
            return inboundQuantity;
        }

        public Integer getOutboundQuantity() {
            return outboundQuantity;
        }

        public Integer getMinStockQuantity() {
            return minStockQuantity;
        }
    }

    /**
     * 计算指定物料ID在库存明细表中的总库存数量（兼容旧方法）
     * 用于聚合同步到仓储管理系统
     */
    private Integer calculateTotalStockByMaterialId(String materialId) {
        return calculateAggregatedStockInfo(materialId).getCurrentStock();
    }

    /**
     * 检查删除库存明细后，该物料ID是否还有其他记录
     * 用于决定是删除仓储管理记录还是只减少数量
     */
    private boolean hasOtherInventoryRecords(String materialId, Long excludeDetailId) {
        if (StringUtils.isBlank(materialId)) {
            return false;
        }

        try {
            LambdaQueryWrapper<InventoryDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InventoryDetail::getMaterialId, materialId);
            wrapper.eq(InventoryDetail::getDelFlag, "0"); // 只查询未删除的记录
            if (excludeDetailId != null) {
                wrapper.ne(InventoryDetail::getDetailId, excludeDetailId); // 排除当前要删除的记录
            }
            
            long count = inventoryDetailMapper.selectCount(wrapper);
            log.debug("物料ID[{}]检查其他记录数量: {}", materialId, count);
            return count > 0;
        } catch (Exception e) {
            log.error("检查物料ID其他记录失败 - 物料ID: {}", materialId, e);
            return false; // 出错时保守处理，认为没有其他记录
        }
    }
}

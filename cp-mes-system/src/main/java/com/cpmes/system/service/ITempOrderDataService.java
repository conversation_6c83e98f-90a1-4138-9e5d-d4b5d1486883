package com.cpmes.system.service;

import com.cpmes.system.entity.dto.purchaseOrder.PurchaseLinkDto;
import com.cpmes.system.entity.PurchaseOrderImage;

import java.util.List;

/**
 * 临时订单数据管理服务接口
 * 用于处理采购申请表单中的临时数据存储和管理
 * 
 * <AUTHOR>
 */
public interface ITempOrderDataService {

    // ========================================
    // 链接管理相关方法
    // ========================================

    /**
     * 保存临时订单的链接数据
     * 
     * @param tempOrderId 临时订单ID
     * @param links 链接列表
     * @return 是否保存成功
     */
    boolean saveTempLinks(String tempOrderId, List<PurchaseLinkDto> links);

    /**
     * 获取临时订单的链接数据
     * 
     * @param tempOrderId 临时订单ID
     * @return 链接列表
     */
    List<PurchaseLinkDto> getTempLinks(String tempOrderId);

    /**
     * 删除临时订单的链接数据
     * 
     * @param tempOrderId 临时订单ID
     * @return 是否删除成功
     */
    boolean deleteTempLinks(String tempOrderId);

    // ========================================
    // 图片管理相关方法
    // ========================================

    /**
     * 保存临时订单的图片数据
     * 
     * @param tempOrderId 临时订单ID
     * @param images 图片列表
     * @return 是否保存成功
     */
    boolean saveTempImages(String tempOrderId, List<PurchaseOrderImage> images);

    /**
     * 获取临时订单的图片数据
     * 
     * @param tempOrderId 临时订单ID
     * @return 图片列表
     */
    List<PurchaseOrderImage> getTempImages(String tempOrderId);

    /**
     * 获取临时订单的图片URL列表
     * 
     * @param tempOrderId 临时订单ID
     * @return 图片URL列表
     */
    List<String> getTempImageUrls(String tempOrderId);

    /**
     * 添加临时图片到临时订单
     * 
     * @param tempOrderId 临时订单ID
     * @param image 图片信息
     * @return 是否添加成功
     */
    boolean addTempImage(String tempOrderId, PurchaseOrderImage image);

    /**
     * 删除临时订单的图片数据
     * 
     * @param tempOrderId 临时订单ID
     * @return 是否删除成功
     */
    boolean deleteTempImages(String tempOrderId);

    /**
     * 删除临时订单的特定图片
     * 
     * @param tempOrderId 临时订单ID
     * @param imagePath 图片路径
     * @return 是否删除成功
     */
    boolean deleteTempImage(String tempOrderId, String imagePath);

    // ========================================
    // 数据迁移相关方法
    // ========================================

    /**
     * 将临时订单数据迁移到正式订单
     * 
     * @param tempOrderId 临时订单ID
     * @param realOrderId 正式订单ID
     * @return 是否迁移成功
     */
    boolean migrateTempDataToReal(String tempOrderId, Long realOrderId);

    /**
     * 清理临时订单的所有数据
     * 
     * @param tempOrderId 临时订单ID
     * @return 是否清理成功
     */
    boolean clearTempData(String tempOrderId);

    // ========================================
    // 数据清理相关方法
    // ========================================

    /**
     * 清理过期的临时数据
     * 
     * @return 清理的数据条数
     */
    int cleanExpiredTempData();

    /**
     * 获取临时订单的过期时间（秒）
     * 
     * @param tempOrderId 临时订单ID
     * @return 剩余过期时间，-1表示不存在或已过期
     */
    long getTempDataExpireTime(String tempOrderId);

    /**
     * 刷新临时订单的过期时间
     * 
     * @param tempOrderId 临时订单ID
     * @return 是否刷新成功
     */
    boolean refreshTempDataExpireTime(String tempOrderId);

    // ========================================
    // 工具方法
    // ========================================

    /**
     * 检查是否为临时订单ID
     * 
     * @param orderId 订单ID
     * @return 是否为临时订单ID
     */
    boolean isTempOrderId(String orderId);

    /**
     * 生成新的临时订单ID
     * 
     * @return 临时订单ID
     */
    String generateTempOrderId();

    /**
     * 获取临时数据的统计信息
     * 
     * @param tempOrderId 临时订单ID
     * @return 统计信息Map，包含链接数量、图片数量等
     */
    java.util.Map<String, Object> getTempDataStatistics(String tempOrderId);
}

package com.cpmes.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cpmes.system.entity.PurchaseOrderImage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 采购订单图片管理 Service接口
 */
public interface IPurchaseOrderImageService extends IService<PurchaseOrderImage> {

    /**
     * 上传采购订单图片
     *
     * @param purchaseOrderId 采购订单ID
     * @param files 图片文件数组
     * @return 上传成功的图片信息列表
     */
    List<PurchaseOrderImage> uploadImages(Long purchaseOrderId, MultipartFile[] files);

    /**
     * 获取采购订单的图片列表
     *
     * @param purchaseOrderId 采购订单ID
     * @return 图片列表
     */
    List<PurchaseOrderImage> getImagesByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 获取采购订单的图片列表（包含上传人姓名）
     *
     * @param purchaseOrderId 采购订单ID
     * @return 图片列表
     */
    List<PurchaseOrderImage> getImagesWithUserNameByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 获取采购订单的图片列表（包含完整元数据和格式化文件大小）
     *
     * @param purchaseOrderId 采购订单ID
     * @return 包含完整元数据的图片列表
     */
    List<PurchaseOrderImage> getImagesWithMetadata(Long purchaseOrderId);

    /**
     * 删除图片
     *
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteImage(Long imageId);

    /**
     * 批量删除图片
     *
     * @param imageIds 图片ID列表
     * @return 是否删除成功
     */
    boolean batchDeleteImages(List<Long> imageIds);

    /**
     * 更新图片描述
     *
     * @param imageId 图片ID
     * @param description 图片描述
     * @return 是否更新成功
     */
    boolean updateImageDescription(Long imageId, String description);

    /**
     * 更新图片排序
     *
     * @param imageId 图片ID
     * @param sortOrder 排序序号
     * @return 是否更新成功
     */
    boolean updateImageSortOrder(Long imageId, Integer sortOrder);

    /**
     * 批量更新图片排序
     *
     * @param imageIds 图片ID列表（按新的排序顺序）
     * @return 是否更新成功
     */
    boolean batchUpdateImageSortOrder(List<Long> imageIds);

    /**
     * 根据采购订单ID删除所有图片
     *
     * @param purchaseOrderId 采购订单ID
     * @return 是否删除成功
     */
    boolean deleteImagesByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 格式化文件大小显示
     *
     * @param fileSize 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    String formatFileSize(Long fileSize);

    /**
     * 批量保存图片记录
     *
     * @param images 图片列表
     * @return 是否保存成功
     */
    boolean batchSave(List<PurchaseOrderImage> images);
}

package com.cpmes.system.service.impl;

import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.config.LogisticsConfig;
import com.cpmes.system.domain.validation.*;
import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.vo.LogisticsCompanyVO;
import com.cpmes.system.service.LogisticsQueryService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ConnectTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.cpmes.system.domain.validation.ErrorClassificationResult;
import javax.annotation.PostConstruct;

/**
 * 物流查询服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsQueryServiceImpl implements LogisticsQueryService {

    private final LogisticsConfig logisticsConfig;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    // 确保RedisTemplate依赖注入正确
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    // 在构造函数或@PostConstruct方法中验证配置
    @PostConstruct
    public void init() {
        if (redisTemplate != null) {
            log.info("物流查询服务Redis配置验证成功");
        } else {
            log.warn("RedisTemplate未配置，将仅使用Spring Cache注解功能");
        }
    }

    @PostConstruct
    public void validateCacheConfiguration() {
        try {
            log.info("=== 物流查询服务缓存配置验证 ===");
            log.info("使用缓存管理器: framework模块的PlusSpringCacheManager");
            log.info("缓存名称: logistics:tracking");
            log.info("RedisTemplate可用: {}", redisTemplate != null);
            log.info("=== 缓存配置验证完成 ===");
        } catch (Exception e) {
            log.error("缓存配置验证失败: {}", e.getMessage(), e);
        }
    }

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 验证识别结果与查询结果的一致性
     */
    private void validateCompanyConsistency(String trackingNumber, String recognizedCompany, String queryCompany) {
        if (StringUtils.isEmpty(recognizedCompany) || StringUtils.isEmpty(queryCompany)) {
            return;
        }

        if (!recognizedCompany.equals(queryCompany)) {
            log.warn("物流公司识别不一致: trackingNumber={}, recognized={}, queried={}",
                    trackingNumber, recognizedCompany, queryCompany);

            // 记录不一致的情况用于后续优化
            recordInconsistency(trackingNumber, recognizedCompany, queryCompany);
        } else {
            log.debug("物流公司识别一致: trackingNumber={}, company={}", trackingNumber, recognizedCompany);
        }
    }

    /**
     * 记录识别不一致的情况
     */
    private void recordInconsistency(String trackingNumber, String recognizedCompany, String queryCompany) {
        try {
            // 可以记录到数据库或日志文件中，用于后续分析和优化
            log.info("=== 物流公司识别不一致记录 ===");
            log.info("快递单号: {}", trackingNumber);
            log.info("识别结果: {}", recognizedCompany);
            log.info("查询结果: {}", queryCompany);
            log.info("单号长度: {}", trackingNumber.length());
            log.info("单号前缀: {}", trackingNumber.substring(0, Math.min(3, trackingNumber.length())));
            log.info("单号格式: {}", trackingNumber.matches("\\d+") ? "纯数字" : "包含字母");
            log.info("=== 记录结束 ===");

            // 这里可以添加数据库记录逻辑
            // inconsistencyRepository.save(new CompanyInconsistency(trackingNumber, recognizedCompany, queryCompany));

        } catch (Exception e) {
            log.error("记录识别不一致情况失败: {}", e.getMessage());
        }
    }

    /**
     * 查询物流信息（增加一致性验证）
     */
    @Override
    @Cacheable(value = "logistics:tracking", key = "#trackingNumber",
               unless = "#result == null || !#result.querySuccess || " +
                       "'QUERY_FAILED'.equals(#result.status) || " +
                       "'SERVICE_ERROR'.equals(#result.status) || " +
                       "'PARSE_ERROR'.equals(#result.status)")
    public LogisticsTrackingDto queryLogistics(String trackingNumber, String logisticsCompany) {
        if (StringUtils.isEmpty(trackingNumber)) {
            throw new ServiceException("快递单号不能为空");
        }

        trackingNumber = trackingNumber.trim().toUpperCase();

        // 如果未提供物流公司，使用默认值或从API响应中获取
        if (StringUtils.isEmpty(logisticsCompany)) {
            logisticsCompany = "未知物流公司";
            log.info("未指定物流公司，将从API响应中获取: trackingNumber={}", trackingNumber);
        } else {
            log.info("开始查询物流信息: trackingNumber={}, logisticsCompany={}", trackingNumber, logisticsCompany);
        }

        try {
            // 处理顺丰快递特殊情况（需要手机号后四位）
            String queryNumber = processTrackingNumber(trackingNumber, logisticsCompany);
            log.info("处理后的查询单号: originalNumber={}, queryNumber={}", trackingNumber, queryNumber);

            // 调用外部API
            log.info("准备调用阿里云物流查询API: trackingNumber={}", trackingNumber);
            JsonNode apiResponse = callLogisticsApi(queryNumber);
            log.info("阿里云API调用完成，开始解析响应: trackingNumber={}", trackingNumber);

            // 解析响应数据
            LogisticsTrackingDto result = parseApiResponse(apiResponse, trackingNumber, logisticsCompany);

            log.info("物流查询完成: trackingNumber={}, status={}, querySuccess={}",
                    trackingNumber, result.getStatus(), result.isQuerySuccess());

            return result;

        } catch (Exception e) {
            log.error("查询物流信息失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            return createFallbackResponse(trackingNumber, logisticsCompany, e.getMessage());
        }
    }

    /**
     * 绕过缓存的物流查询（用于调试和强制刷新）
     */
    @Override
    @CacheEvict(value = "logistics:tracking", key = "#trackingNumber")
    public LogisticsTrackingDto queryLogisticsWithoutCache(String trackingNumber, String logisticsCompany) {
        if (StringUtils.isEmpty(trackingNumber)) {
            throw new ServiceException("快递单号不能为空");
        }

        log.info("=== 开始物流查询流程（绕过缓存） ===");
        log.info("查询参数: trackingNumber={}, logisticsCompany={}", trackingNumber, logisticsCompany);

        try {
            // 处理顺丰快递特殊情况（需要手机号后四位）
            String queryNumber = processTrackingNumber(trackingNumber, logisticsCompany);
            log.info("处理后的查询单号: originalNumber={}, queryNumber={}", trackingNumber, queryNumber);

            // 调用外部API
            log.info("准备调用阿里云物流查询API: trackingNumber={}", trackingNumber);
            JsonNode apiResponse = callLogisticsApi(queryNumber);
            log.info("阿里云API调用完成，开始解析响应: trackingNumber={}", trackingNumber);

            // 解析响应数据
            LogisticsTrackingDto result = parseApiResponse(apiResponse, trackingNumber, logisticsCompany);

            log.info("=== 物流查询流程完成（绕过缓存） ===");
            log.info("查询结果汇总: trackingNumber={}, status={}, querySuccess={}, hasDetails={}, errorMessage={}",
                trackingNumber, result.getStatus(), result.isQuerySuccess(),
                result.getTrackingDetails() != null && !result.getTrackingDetails().isEmpty(),
                result.getErrorMessage());

            return result;

        } catch (Exception e) {
            log.error("=== 物流查询流程异常（绕过缓存） ===");
            log.error("查询异常详情: trackingNumber={}, logisticsCompany={}, errorType={}, error={}",
                trackingNumber, logisticsCompany, e.getClass().getSimpleName(), e.getMessage(), e);

            // 返回降级响应，避免完全失败
            LogisticsTrackingDto fallbackResult = createFallbackResponse(trackingNumber, logisticsCompany, e.getMessage());
            log.info("返回降级响应: trackingNumber={}, status={}, querySuccess={}",
                trackingNumber, fallbackResult.getStatus(), fallbackResult.isQuerySuccess());

            return fallbackResult;
        }
    }

    /**
     * 处理快递单号（特殊情况处理，基于参考代码）
     */
    private String processTrackingNumber(String trackingNumber, String logisticsCompany) {
        // 顺丰快递需要添加手机号后四位（参考代码中的特殊处理）
        if (trackingNumber.toUpperCase().startsWith("SF")) {
            log.warn("顺丰快递查询需要手机号后四位，当前查询可能不准确: {}", trackingNumber);

            // 检查是否已经包含手机号后四位（格式：SF1234567890:1234）
            if (!trackingNumber.contains(":")) {
                log.warn("顺丰快递单号缺少手机号后四位，建议格式：{}:手机号后四位", trackingNumber);
                // 可以在这里添加从数据库获取手机号的逻辑
                // 暂时返回原单号，让API自行处理
            }
        }

        // 其他快递公司的特殊处理可以在这里添加
        return trackingNumber;
    }

    /**
     * 调用物流查询API（增强日志记录版本）
     */
    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 2000))
    private JsonNode callLogisticsApi(String trackingNumber) throws Exception {
        // 强制控制台输出，确保能看到调用
        System.out.println("=== DEBUG: callLogisticsApi 方法被调用 ===");
        System.out.println("DEBUG: trackingNumber=" + trackingNumber);

        String url = logisticsConfig.getAliCloud().getUrl();
        String appCode = logisticsConfig.getAliCloud().getAppCode();

        System.out.println("DEBUG: API配置 - url=" + url + ", appCode=" + (appCode != null ? "已配置" : "未配置"));

        // 构建请求头（参考Python代码的实现）
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "APPCODE " + appCode);

        // 构建完整URL（参考Python代码：params={'no': order_id}）
        StringBuilder urlBuilder = new StringBuilder(url);
        urlBuilder.append("?no=").append(trackingNumber);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        log.info("调用物流查询API: url={}, trackingNumber={}", urlBuilder.toString(), trackingNumber);
        System.out.println("DEBUG: 准备发送HTTP请求到: " + urlBuilder.toString());

        try {
            // 记录请求开始时间
            long startTime = System.currentTimeMillis();

            // 发送请求（设置10秒超时，参考Python代码）
            ResponseEntity<String> response = restTemplate.exchange(
                urlBuilder.toString(),
                HttpMethod.GET,
                entity,
                String.class
            );

            // 记录请求结束时间
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;

            int httpStatusCode = response.getStatusCodeValue();
            String responseBody = response.getBody();
            int responseSize = responseBody != null ? responseBody.length() : 0;

            // ===== 强制控制台输出 =====
            System.out.println("=== DEBUG: HTTP请求完成 ===");
            System.out.println("DEBUG: HTTP状态码=" + httpStatusCode);
            System.out.println("DEBUG: 响应时间=" + responseTime + "ms");
            System.out.println("DEBUG: 响应大小=" + responseSize + " bytes");
            System.out.println("DEBUG: 响应体=" + responseBody);

            // ===== 详细日志记录开始 =====
            log.info("阿里云物流API原始响应: trackingNumber={}, httpStatus={}, responseSize={}, responseTime={}ms",
                trackingNumber, httpStatusCode, responseSize, responseTime);

            // 打印HTTP响应头信息
            HttpHeaders responseHeaders = response.getHeaders();
            log.info("响应头信息: trackingNumber={}, Content-Type={}, Content-Length={}",
                trackingNumber,
                responseHeaders.getContentType(),
                responseHeaders.getContentLength());

            if (httpStatusCode == 200) {
                // 成功响应，详细记录原始JSON数据
                if (responseBody != null && !responseBody.isEmpty()) {
                    try {
                        // 格式化JSON输出，提高可读性
                        JsonNode jsonNode = objectMapper.readTree(responseBody);
                        String formattedJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode);

                        System.out.println("=== DEBUG: 格式化JSON开始 ===");
                        System.out.println(formattedJson);
                        System.out.println("=== DEBUG: 格式化JSON结束 ===");

                        log.info("=== 阿里云物流API原始JSON响应开始 ===");
                        log.info("快递单号: {}", trackingNumber);
                        log.info("格式化JSON数据:\n{}", formattedJson);
                        log.info("=== 阿里云物流API原始JSON响应结束 ===");

                        // 同时记录压缩格式的JSON（便于日志搜索）
                        log.info("原始JSON数据: trackingNumber={}, json={}", trackingNumber, responseBody);

                        return jsonNode;

                    } catch (JsonProcessingException e) {
                        System.out.println("DEBUG: JSON解析失败 - " + e.getMessage());
                        log.error("JSON格式化失败，记录原始响应: trackingNumber={}, error={}, rawResponse={}",
                            trackingNumber, e.getMessage(), responseBody);

                        // 尝试直接解析
                        return objectMapper.readTree(responseBody);
                    }
                } else {
                    System.out.println("DEBUG: 响应体为空");
                    log.warn("API响应体为空: trackingNumber={}, httpStatus={}", trackingNumber, httpStatusCode);
                    throw new ServiceException("API响应体为空");
                }
            } else {
                System.out.println("DEBUG: HTTP状态码非200 - " + httpStatusCode);
                // 处理非200状态码
                return handleApiErrorResponse(response, httpStatusCode, trackingNumber);
            }

        } catch (Exception e) {
            System.out.println("DEBUG: API调用异常 - " + e.getClass().getSimpleName() + ": " + e.getMessage());
            log.error("物流查询API调用异常: trackingNumber={}, errorType={}, error={}",
                trackingNumber, e.getClass().getSimpleName(), e.getMessage(), e);
            throw new ServiceException("物流查询服务暂不可用: " + e.getMessage());
        }
    }

    /**
     * 处理API错误响应（增强日志记录版本）
     */
    private JsonNode handleApiErrorResponse(ResponseEntity<String> response, int httpStatusCode, String trackingNumber) throws Exception {

        log.warn("=== 处理API错误响应开始 ===");
        log.warn("快递单号: {}", trackingNumber);
        log.warn("HTTP状态码: {}", httpStatusCode);

        String responseBody = response.getBody();
        log.warn("错误响应体: {}", responseBody);

        // 记录响应头中的错误信息
        HttpHeaders headers = response.getHeaders();
        String errorMessage = headers.getFirst("X-Ca-Error-Message");
        if (errorMessage != null) {
            log.warn("X-Ca-Error-Message头: {}", errorMessage);
        }

        // 构建错误响应JSON
        Map<String, Object> errorResponse = new HashMap<>();

        if (httpStatusCode == 403) {
            // 处理403错误（参考Python代码）
            log.warn("处理403错误: trackingNumber={}, X-Ca-Error-Message={}", trackingNumber, errorMessage);

            if ("Quota Exhausted".equals(errorMessage)) {
                errorResponse.put("code", 403);
                errorResponse.put("massage", "套餐包次数用完");  // 注意：使用massage而不是message
                errorResponse.put("error_", "发生错误");
                log.warn("配额用完错误: trackingNumber={}", trackingNumber);
            } else if ("Api Market Subscription quota exhausted".equals(errorMessage)) {
                errorResponse.put("code", 403);
                errorResponse.put("massage", "套餐包次数用完，请续购套餐");
                errorResponse.put("error_", "发生错误");
                log.warn("API市场订阅配额用完: trackingNumber={}", trackingNumber);
            } else {
                errorResponse.put("code", 400);
                errorResponse.put("massage", responseBody);
                errorResponse.put("error_", "发生错误");
                log.warn("其他403错误: trackingNumber={}, responseBody={}", trackingNumber, responseBody);
            }
        } else {
            // 其他错误状态码
            errorResponse.put("code", 400);
            errorResponse.put("massage", responseBody != null ? responseBody : "未知错误");
            errorResponse.put("error_", "发生错误");
            log.warn("其他HTTP错误: trackingNumber={}, statusCode={}, responseBody={}",
                trackingNumber, httpStatusCode, responseBody);
        }

        log.warn("构建的错误响应: trackingNumber={}, errorResponse={}", trackingNumber, errorResponse);
        log.warn("=== 处理API错误响应结束 ===");

        // 转换为JsonNode返回
        String errorJson = objectMapper.writeValueAsString(errorResponse);
        JsonNode errorJsonNode = objectMapper.readTree(errorJson);

        // 记录最终的错误JSON
        log.warn("最终错误JSON: trackingNumber={}, errorJson={}", trackingNumber, errorJson);

        return errorJsonNode;
    }

    /**
     * 解析API响应数据（基于阿里云API标准格式）
     */
    private LogisticsTrackingDto parseApiResponse(JsonNode apiResponse, String trackingNumber, String logisticsCompany) {
        LogisticsTrackingDto dto = new LogisticsTrackingDto();
        dto.setTrackingNumber(trackingNumber);
        dto.setCompany(logisticsCompany);
        dto.setQuerySuccess(false); // 默认设置为失败，成功时再修改
        dto.setLastUpdateTime(new Date());
        dto.setTrackingDetails(new ArrayList<>());

        try {
            log.info("开始解析阿里云API响应: trackingNumber={}, 原始响应: {}", trackingNumber, apiResponse.toString());

            // 1. 检查阿里云API的status字段（字符串类型）
            String apiStatus = apiResponse.path("status").asText();
            log.info("阿里云API状态码: {}, 快递单号: {}", apiStatus, trackingNumber);

            // 2. 根据阿里云API文档的status值进行处理
            switch (apiStatus) {
                case "0":
                    // 正常查询，解析result数据
                    return parseSuccessResponse(dto, apiResponse, trackingNumber);

                case "201":
                    // 快递单号错误
                    return parseTrackingNumberError(dto, apiResponse, trackingNumber);

                case "203":
                    // 快递公司不存在
                    return parseCompanyNotExistError(dto, apiResponse, trackingNumber);

                case "204":
                    // 快递公司识别失败
                    return parseCompanyRecognitionError(dto, apiResponse, trackingNumber);

                case "205":
                    // 没有信息（正常情况，可能是新单号）
                    return parseNoInfoResponse(dto, apiResponse, trackingNumber);

                case "207":
                    // 该单号被限制
                    return parseRestrictedNumberError(dto, apiResponse, trackingNumber);

                default:
                    // 未知状态码
                    return parseUnknownStatusResponse(dto, apiResponse, apiStatus, trackingNumber);
            }

        } catch (Exception e) {
            log.error("解析阿里云API响应异常: trackingNumber={}, error={}, 原始响应: {}",
                trackingNumber, e.getMessage(), apiResponse.toString(), e);

            // 异常情况下返回解析失败状态
            dto.setQuerySuccess(false);
            dto.setStatus("PARSE_ERROR");
            dto.setStatusDescription("响应数据解析异常");
            dto.setErrorMessage("解析异常: " + e.getMessage());
            return dto;
        }
    }

    /**
     * 解析成功响应（status="0"）
     */
    private LogisticsTrackingDto parseSuccessResponse(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        log.info("解析成功响应: trackingNumber={}", trackingNumber);

        JsonNode result = apiResponse.path("result");
        if (result.isMissingNode() || result.isNull()) {
            log.error("成功响应中缺少result字段: trackingNumber={}", trackingNumber);
            dto.setStatus("DATA_ERROR");
            dto.setStatusDescription("响应数据格式错误");
            dto.setErrorMessage("API响应中缺少result字段");
            dto.setQuerySuccess(false);
            return dto;
        }

        // 设置查询成功
        dto.setQuerySuccess(true);

        // 解析物流状态（基于阿里云API的deliverystatus和issign字段）
        String deliveryStatus = result.path("deliverystatus").asText();
        String isSign = result.path("issign").asText();

        log.info("物流状态解析: trackingNumber={}, deliverystatus={}, issign={}",
            trackingNumber, deliveryStatus, isSign);

        // 根据阿里云API文档映射物流状态
        if ("1".equals(isSign)) {
            // 已签收
            dto.setStatus("SIGNED");
            dto.setStatusDescription("已签收");
        } else {
            // 根据deliverystatus判断具体状态
            switch (deliveryStatus) {
                case "0":
                    dto.setStatus("PICKED_UP");
                    dto.setStatusDescription("快递收件");
                    break;
                case "1":
                    dto.setStatus("IN_TRANSIT");
                    dto.setStatusDescription("运输中");
                    break;
                case "2":
                    dto.setStatus("OUT_FOR_DELIVERY");
                    dto.setStatusDescription("正在派件");
                    break;
                case "3":
                    dto.setStatus("SIGNED");
                    dto.setStatusDescription("已签收");
                    break;
                case "4":
                    dto.setStatus("DELIVERY_FAILED");
                    dto.setStatusDescription("派送失败");
                    break;
                case "5":
                    dto.setStatus("PROBLEM_PACKAGE");
                    dto.setStatusDescription("疑难件");
                    break;
                case "6":
                    dto.setStatus("RETURNED");
                    dto.setStatusDescription("退件签收");
                    break;
                default:
                    dto.setStatus("IN_TRANSIT");
                    dto.setStatusDescription("运输中");
                    break;
            }
        }

        // 解析更新时间
        String updateTimeStr = result.path("updateTime").asText();
        if (StringUtils.isNotEmpty(updateTimeStr)) {
            try {
                dto.setLastUpdateTime(dateFormat.parse(updateTimeStr));
            } catch (ParseException e) {
                log.warn("解析更新时间失败: trackingNumber={}, updateTime={}", trackingNumber, updateTimeStr);
                dto.setLastUpdateTime(new Date());
            }
        }

        // 解析快递员信息
        String courier = result.path("courier").asText();
        String courierPhone = result.path("courierPhone").asText();
        if (StringUtils.isNotEmpty(courierPhone)) {
            dto.setCourierPhone(courierPhone);
        }

        // 解析快递公司名称
        String expName = result.path("expName").asText();
        if (StringUtils.isNotEmpty(expName)) {
            dto.setCompany(expName);
        }

        // 解析物流轨迹（从result.list数组）
        JsonNode listNode = result.path("list");
        if (listNode.isArray() && listNode.size() > 0) {
            List<LogisticsTrackingDto.TrackingDetail> details = parseTrackingDetailsFromList(listNode, trackingNumber);
            dto.setTrackingDetails(details);
            log.info("成功解析物流轨迹: trackingNumber={}, 轨迹记录数={}", trackingNumber, details.size());
        } else {
            log.warn("成功响应中没有物流轨迹数据: trackingNumber={}", trackingNumber);
            dto.setTrackingDetails(new ArrayList<>());
        }

        // 成功时清空错误信息
        dto.setErrorMessage(null);

        log.info("成功响应解析完成: trackingNumber={}, status={}, trackingDetails={}",
            trackingNumber, dto.getStatus(), dto.getTrackingDetails().size());

        return dto;
    }

    /**
     * 解析物流轨迹详情（从阿里云API的list数组）
     */
    private List<LogisticsTrackingDto.TrackingDetail> parseTrackingDetailsFromList(JsonNode listNode, String trackingNumber) {
        List<LogisticsTrackingDto.TrackingDetail> details = new ArrayList<>();

        log.debug("开始解析物流轨迹: trackingNumber={}, list数组大小={}", trackingNumber, listNode.size());

        for (JsonNode item : listNode) {
            try {
                LogisticsTrackingDto.TrackingDetail detail = new LogisticsTrackingDto.TrackingDetail();

                // 解析时间（阿里云API格式：yyyy-MM-dd HH:mm:ss）
                String timeStr = item.path("time").asText();
                if (StringUtils.isNotEmpty(timeStr)) {
                    try {
                        detail.setTime(dateFormat.parse(timeStr));
                        detail.setTimeStr(timeStr);
                    } catch (ParseException e) {
                        log.warn("解析物流轨迹时间失败: trackingNumber={}, time={}", trackingNumber, timeStr);
                        detail.setTime(new Date());
                        detail.setTimeStr(timeStr);
                    }
                } else {
                    detail.setTime(new Date());
                    detail.setTimeStr("");
                }

                // 解析状态描述（阿里云API的status字段包含完整描述）
                String statusDesc = item.path("status").asText();
                if (StringUtils.isNotEmpty(statusDesc)) {
                    detail.setDescription(statusDesc);

                    // 尝试从描述中提取地点信息
                    String location = extractLocationFromDescription(statusDesc);
                    detail.setLocation(location);
                } else {
                    detail.setDescription("无状态信息");
                    detail.setLocation("");
                }

                // 设置操作员（如果描述中包含签收人信息）
                String operator = extractOperatorFromDescription(statusDesc);
                detail.setOperator(operator);

                // 设置状态类型（根据描述内容推断）
                String statusType = inferStatusTypeFromDescription(statusDesc);
                detail.setStatus(statusType);

                details.add(detail);

                log.debug("解析轨迹记录: trackingNumber={}, time={}, description={}",
                    trackingNumber, timeStr, statusDesc);

            } catch (Exception e) {
                log.warn("解析单条物流轨迹失败: trackingNumber={}, error={}, 数据: {}",
                    trackingNumber, e.getMessage(), item.toString());
            }
        }

        // 按时间倒序排列（最新的在前面）
        details.sort((a, b) -> {
            if (a.getTime() == null && b.getTime() == null) return 0;
            if (a.getTime() == null) return 1;
            if (b.getTime() == null) return -1;
            return b.getTime().compareTo(a.getTime());
        });

        log.debug("物流轨迹解析完成: trackingNumber={}, 总记录数={}", trackingNumber, details.size());
        return details;
    }

    /**
     * 从描述中提取地点信息
     */
    private String extractLocationFromDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return "";
        }

        // 匹配【地点】格式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("【([^】]+)】");
        java.util.regex.Matcher matcher = pattern.matcher(description);

        if (matcher.find()) {
            return matcher.group(1);
        }

        // 匹配其他常见地点格式
        if (description.contains("市") || description.contains("区") || description.contains("县")) {
            // 简单提取包含"市"、"区"、"县"的部分
            String[] parts = description.split("[，,。\\s]+");
            for (String part : parts) {
                if (part.contains("市") || part.contains("区") || part.contains("县")) {
                    return part.trim();
                }
            }
        }

        return "";
    }

    /**
     * 从描述中提取操作员信息
     */
    private String extractOperatorFromDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return "";
        }

        // 匹配签收人格式
        if (description.contains("签收人:") || description.contains("签收人：")) {
            String[] parts = description.split("签收人[:：]");
            if (parts.length > 1) {
                return parts[1].trim().split("[，,\\s]")[0];
            }
        }

        return "";
    }

    /**
     * 从描述推断状态类型
     */
    private String inferStatusTypeFromDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return "UNKNOWN";
        }

        String desc = description.toLowerCase();

        if (desc.contains("签收") || desc.contains("已收件")) {
            return "SIGNED";
        } else if (desc.contains("派件") || desc.contains("派送")) {
            return "OUT_FOR_DELIVERY";
        } else if (desc.contains("到达") || desc.contains("离开") || desc.contains("转运")) {
            return "IN_TRANSIT";
        } else if (desc.contains("收件") || desc.contains("揽件")) {
            return "PICKED_UP";
        } else {
            return "IN_TRANSIT";
        }
    }

    /**
     * 解析快递单号错误响应（status="201"）
     */
    private LogisticsTrackingDto parseTrackingNumberError(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        String message = apiResponse.path("msg").asText("快递单号错误");

        log.warn("快递单号错误: trackingNumber={}, message={}", trackingNumber, message);

        dto.setQuerySuccess(false);
        dto.setStatus("INVALID_TRACKING_NUMBER");
        dto.setStatusDescription("快递单号错误");
        dto.setErrorMessage(message);
        return dto;
    }

    /**
     * 解析快递公司不存在错误（status="203"）
     */
    private LogisticsTrackingDto parseCompanyNotExistError(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        String message = apiResponse.path("msg").asText("快递公司不存在");

        log.warn("快递公司不存在: trackingNumber={}, message={}", trackingNumber, message);

        dto.setQuerySuccess(false);
        dto.setStatus("COMPANY_NOT_EXIST");
        dto.setStatusDescription("快递公司不存在");
        dto.setErrorMessage(message);
        return dto;
    }

    /**
     * 解析快递公司识别失败错误（status="204"）
     */
    private LogisticsTrackingDto parseCompanyRecognitionError(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        String message = apiResponse.path("msg").asText("快递公司识别失败");

        log.warn("快递公司识别失败: trackingNumber={}, message={}", trackingNumber, message);

        dto.setQuerySuccess(false);
        dto.setStatus("COMPANY_RECOGNITION_FAILED");
        dto.setStatusDescription("快递公司识别失败");
        dto.setErrorMessage(message);
        return dto;
    }

    /**
     * 解析暂无信息响应（status="205"）
     */
    private LogisticsTrackingDto parseNoInfoResponse(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        String message = apiResponse.path("msg").asText("暂无物流信息");

        log.info("暂无物流信息: trackingNumber={}, message={}", trackingNumber, message);

        dto.setQuerySuccess(true); // 查询成功，只是暂无信息
        dto.setStatus("NO_INFO");
        dto.setStatusDescription("暂无物流轨迹信息");
        dto.setErrorMessage(null); // 不是错误，清空错误信息
        return dto;
    }

    /**
     * 解析单号被限制错误（status="207"）
     */
    private LogisticsTrackingDto parseRestrictedNumberError(LogisticsTrackingDto dto, JsonNode apiResponse, String trackingNumber) {
        String message = apiResponse.path("msg").asText("该单号被限制");

        log.warn("单号被限制: trackingNumber={}, message={}", trackingNumber, message);

        dto.setQuerySuccess(false);
        dto.setStatus("RESTRICTED_NUMBER");
        dto.setStatusDescription("该单号被限制查询");
        dto.setErrorMessage(message);
        return dto;
    }

    /**
     * 解析未知状态响应
     */
    private LogisticsTrackingDto parseUnknownStatusResponse(LogisticsTrackingDto dto, JsonNode apiResponse, String apiStatus, String trackingNumber) {
        String message = apiResponse.path("msg").asText("未知状态: " + apiStatus);

        log.warn("未知API状态码: trackingNumber={}, status={}, message={}", trackingNumber, apiStatus, message);

        dto.setQuerySuccess(false);
        dto.setStatus("UNKNOWN_API_STATUS");
        dto.setStatusDescription("API返回未知状态");
        dto.setErrorMessage("状态码 " + apiStatus + ": " + message);
        return dto;
    }

    /**
     * 创建降级响应（修复版本）
     */
    private LogisticsTrackingDto createFallbackResponse(String trackingNumber, String logisticsCompany, String errorMessage) {
        LogisticsTrackingDto dto = new LogisticsTrackingDto();
        dto.setTrackingNumber(trackingNumber);
        dto.setCompany(logisticsCompany != null ? logisticsCompany : "未知物流公司");

        // 确保逻辑一致性
        dto.setQuerySuccess(false);
        dto.setStatus("SERVICE_ERROR");
        dto.setStatusDescription("物流查询服务异常");
        dto.setErrorMessage(errorMessage);

        dto.setLastUpdateTime(new Date());
        dto.setTrackingDetails(new ArrayList<>());
        dto.setCourierPhone(null);
        dto.setEstimatedDeliveryTime(null);
        dto.setSenderInfo(null);
        dto.setReceiverInfo(null);

        log.info("创建降级响应: trackingNumber={}, status={}, querySuccess={}, errorMessage={}",
            trackingNumber, dto.getStatus(), dto.isQuerySuccess(), errorMessage);

        return dto;
    }

    /**
     * 清除指定快递单号的缓存
     */
    @Override
    @CacheEvict(value = "logistics:tracking", key = "#trackingNumber")
    public void clearCache(String trackingNumber) {
        if (StringUtils.isEmpty(trackingNumber)) {
            log.warn("快递单号为空，无法清除缓存");
            return;
        }

        try {
            String cacheKey = "logistics:tracking:" + trackingNumber.trim().toUpperCase();

            // 方式1：Spring Cache注解自动清除（主要方式）
            log.info("使用Spring Cache注解清除缓存: trackingNumber={}", trackingNumber);

            // 方式2：手动Redis操作（备用方式，确保彻底清除）
            if (redisTemplate != null) {
                Boolean deleted = redisTemplate.delete(cacheKey);
                log.info("手动清除Redis缓存: trackingNumber={}, deleted={}", trackingNumber, deleted);
            } else {
                log.debug("RedisTemplate未配置，仅使用Spring Cache注解清除缓存");
            }

            log.info("物流查询缓存清除完成: trackingNumber={}", trackingNumber);

        } catch (Exception e) {
            log.error("清除物流查询缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
            // 不抛出异常，避免影响业务流程，仅记录错误日志
        }
    }

    /**
     * 批量清除缓存（用于系统维护）
     */
    @CacheEvict(value = "logistics:tracking", allEntries = true)
    public void clearAllCache() {
        log.info("清除所有物流查询缓存");
    }

    /**
     * 清除指定用户的相关缓存（如果需要按用户隔离）
     */
    public void clearUserRelatedCache(Long userId) {
        // 根据业务需求实现
        // 通常物流缓存不需要按用户隔离
    }

    /**
     * 清除指定模式的缓存键
     */
    public void clearCacheByPattern(String pattern) {
        if (StringUtils.isEmpty(pattern)) {
            log.warn("缓存模式为空，无法清除缓存");
            return;
        }

        try {
            if (redisTemplate != null) {
                Set<String> keys = redisTemplate.keys(pattern);
                if (keys != null && !keys.isEmpty()) {
                    Long deletedCount = redisTemplate.delete(keys);
                    log.info("按模式清除缓存完成: pattern={}, deletedCount={}", pattern, deletedCount);
                } else {
                    log.info("未找到匹配的缓存键: pattern={}", pattern);
                }
            } else {
                log.warn("RedisTemplate未配置，无法按模式清除缓存");
            }

        } catch (Exception e) {
            log.error("按模式清除缓存失败: pattern={}, error={}", pattern, e.getMessage(), e);
        }
    }

    @Override
    public boolean isServiceAvailable() {
        try {
            // 使用一个测试单号检查服务可用性
            String testUrl = logisticsConfig.getAliCloud().getUrl();
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "APPCODE " + logisticsConfig.getAliCloud().getAppCode());

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(
                testUrl + "?no=test",
                HttpMethod.GET,
                entity,
                String.class
            );

            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.warn("物流查询服务不可用: {}", e.getMessage());
            return false;
        }
    }

    // 改进：统一错误响应格式
    private LogisticsTrackingDto createErrorResponse(String trackingNumber,
                                                   String status,
                                                   String description,
                                                   String errorMessage) {
        LogisticsTrackingDto dto = new LogisticsTrackingDto();
        dto.setTrackingNumber(trackingNumber);
        dto.setStatus(status);
        dto.setStatusDescription(description);
        dto.setQuerySuccess(false);  // 确保逻辑一致性
        dto.setErrorMessage(errorMessage);
        dto.setLastUpdateTime(new Date());
        dto.setTrackingDetails(new ArrayList<>());
        return dto;
    }

    /**
     * 验证API响应JSON结构
     */
    public JsonValidationResult validateJsonStructure(JsonNode apiResponse) {
        List<String> missingFields = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 检查必需字段
        if (!apiResponse.has("status")) {
            missingFields.add("status");
        }

        // 检查错误标识字段
        if (apiResponse.has("error_")) {
            log.warn("API响应包含错误标识: error_=true");

            // 检查错误消息字段（注意API使用massage而非message）
            if (!apiResponse.has("massage") && !apiResponse.has("message")) {
                warnings.add("错误响应缺少massage/message字段");
            }
        }

        // 检查业务数据字段
        String status = apiResponse.path("status").asText();
        if ("200".equals(status)) {
            // 成功状态应该有result字段
            if (!apiResponse.has("result")) {
                missingFields.add("result");
            } else {
                JsonNode result = apiResponse.path("result");
                if (!result.has("list")) {
                    missingFields.add("result.list");
                }
            }
        }

        // 记录验证结果
        if (!missingFields.isEmpty()) {
            log.error("JSON结构验证失败，缺少字段: {}", missingFields);
            return JsonValidationResult.invalid(missingFields, warnings);
        }

        if (!warnings.isEmpty()) {
            log.warn("JSON结构验证警告: {}", warnings);
        }

        return JsonValidationResult.valid(warnings);
    }

    /**
     * 区分API调用成功但业务逻辑失败的情况
     */
    public BusinessValidationResult validateBusinessLogic(JsonNode apiResponse) {
        // HTTP 200 + error_ 字段 = API调用成功但业务失败
        if (apiResponse.has("error_")) {
            String errorMsg = apiResponse.path("massage").asText();
            if (errorMsg.isEmpty()) {
                errorMsg = apiResponse.path("message").asText("业务处理异常");
            }

            log.warn("业务逻辑失败: error_=true, massage={}", errorMsg);
            return BusinessValidationResult.businessFailed(errorMsg);
        }

        // 检查业务状态码
        String status = apiResponse.path("status").asText();
        switch (status) {
            case "200":
                log.info("业务处理成功: status=200");
                return BusinessValidationResult.success("查询成功");

            case "205":
                log.info("查询成功但暂无数据: status=205");
                return BusinessValidationResult.noData("暂无物流轨迹信息");

            case "400":
                String message = apiResponse.path("message").asText("查询参数错误");
                log.warn("业务参数错误: status=400, message={}", message);
                return BusinessValidationResult.paramError(message);

            default:
                log.warn("未知业务状态码: status={}", status);
                return BusinessValidationResult.unknownStatus(status);
        }
    }

    /**
     * 验证物流轨迹数据完整性
     */
    public DataValidationResult validateTrackingData(JsonNode apiResponse) {
        if (!"200".equals(apiResponse.path("status").asText())) {
            return DataValidationResult.skip("非成功状态，跳过数据验证");
        }

        JsonNode result = apiResponse.path("result");
        JsonNode listNode = result.path("list");

        if (!listNode.isArray()) {
            log.error("result.list不是数组格式: {}", listNode.getNodeType());
            return DataValidationResult.invalid("轨迹数据格式错误");
        }

        int totalRecords = listNode.size();
        int validRecords = 0;
        List<String> dataIssues = new ArrayList<>();

        for (int i = 0; i < totalRecords; i++) {
            JsonNode item = listNode.get(i);
            DataRecordValidation recordValidation = validateSingleRecord(item, i);

            if (recordValidation.isValid()) {
                validRecords++;
            } else {
                dataIssues.addAll(recordValidation.getIssues());
            }
        }

        log.info("轨迹数据验证完成: 总记录数={}, 有效记录数={}, 问题记录数={}",
                 totalRecords, validRecords, totalRecords - validRecords);

        if (validRecords == 0 && totalRecords > 0) {
            return DataValidationResult.invalid("所有轨迹记录都无效: " + dataIssues);
        }

        return DataValidationResult.valid(totalRecords, validRecords, dataIssues);
    }

    /**
     * 验证单条轨迹记录
     */
    private DataRecordValidation validateSingleRecord(JsonNode item, int index) {
        List<String> issues = new ArrayList<>();

        // 检查必需字段
        if (!item.has("time") || item.path("time").asText().isEmpty()) {
            issues.add(String.format("记录[%d]缺少time字段", index));
        }

        if (!item.has("context") || item.path("context").asText().isEmpty()) {
            issues.add(String.format("记录[%d]缺少context字段", index));
        }

        // 验证时间格式
        String timeStr = item.path("time").asText();
        if (!timeStr.isEmpty()) {
            try {
                // 尝试解析时间格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                sdf.parse(timeStr);
            } catch (ParseException e) {
                issues.add(String.format("记录[%d]时间格式错误: %s", index, timeStr));
            }
        }

        return new DataRecordValidation(issues.isEmpty(), issues);
    }

    /**
     * 检查关键字段是否存在
     */
    public FieldValidationResult validateKeyFields(JsonNode apiResponse) {
        Map<String, FieldStatus> fieldStatus = new HashMap<>();

        // 检查顶级字段
        fieldStatus.put("status", checkField(apiResponse, "status", true));
        fieldStatus.put("message", checkField(apiResponse, "message", false));
        fieldStatus.put("massage", checkField(apiResponse, "massage", false)); // API拼写错误
        fieldStatus.put("error_", checkField(apiResponse, "error_", false));

        // 如果是成功状态，检查result字段
        if ("200".equals(apiResponse.path("status").asText())) {
            fieldStatus.put("result", checkField(apiResponse, "result", true));

            if (apiResponse.has("result")) {
                JsonNode result = apiResponse.path("result");
                fieldStatus.put("result.list", checkField(result, "list", true));
                fieldStatus.put("result.deliverystatus", checkField(result, "deliverystatus", false));
                fieldStatus.put("result.issign", checkField(result, "issign", false));
            }
        }

        // 统计结果
        long missingRequired = fieldStatus.entrySet().stream()
            .filter(entry -> entry.getValue() == FieldStatus.MISSING_REQUIRED)
            .count();

        long missingOptional = fieldStatus.entrySet().stream()
            .filter(entry -> entry.getValue() == FieldStatus.MISSING_OPTIONAL)
            .count();

        log.info("字段验证结果: 缺少必需字段={}, 缺少可选字段={}", missingRequired, missingOptional);

        return new FieldValidationResult(fieldStatus, missingRequired == 0);
    }

    private FieldStatus checkField(JsonNode node, String fieldName, boolean required) {
        if (node.has(fieldName) && !node.path(fieldName).isNull()) {
            return FieldStatus.PRESENT;
        } else {
            return required ? FieldStatus.MISSING_REQUIRED : FieldStatus.MISSING_OPTIONAL;
        }
    }

    /**
     * 判断返回的物流信息是否为最新数据
     */
    public FreshnessValidationResult validateDataFreshness(JsonNode apiResponse, String trackingNumber) {
        if (!"200".equals(apiResponse.path("status").asText())) {
            return FreshnessValidationResult.skip("非成功状态，跳过时效性检查");
        }

        JsonNode listNode = apiResponse.path("result").path("list");
        if (!listNode.isArray() || listNode.size() == 0) {
            return FreshnessValidationResult.noData("无轨迹数据");
        }

        // 找到最新的轨迹记录
        Date latestTime = null;
        String latestDescription = null;

        for (JsonNode item : listNode) {
            String timeStr = item.path("time").asText();
            if (!timeStr.isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date recordTime = sdf.parse(timeStr);

                    if (latestTime == null || recordTime.after(latestTime)) {
                        latestTime = recordTime;
                        latestDescription = item.path("context").asText();
                    }
                } catch (ParseException e) {
                    log.warn("解析时间失败: trackingNumber={}, time={}", trackingNumber, timeStr);
                }
            }
        }

        if (latestTime == null) {
            return FreshnessValidationResult.invalid("无法解析轨迹时间");
        }

        // 检查数据时效性（超过24小时认为可能不是最新）
        long hoursSinceUpdate = (System.currentTimeMillis() - latestTime.getTime()) / (1000 * 60 * 60);

        log.info("数据时效性检查: trackingNumber={}, 最新更新={}小时前, 描述={}",
                 trackingNumber, hoursSinceUpdate, latestDescription);

        if (hoursSinceUpdate > 24) {
            return FreshnessValidationResult.stale(latestTime, latestDescription, hoursSinceUpdate);
        } else {
            return FreshnessValidationResult.fresh(latestTime, latestDescription, hoursSinceUpdate);
        }
    }

    /**
     * 识别API配额用完的情况
     */
    public QuotaValidationResult validateQuotaStatus(ResponseEntity<String> response, JsonNode apiResponse) {
        // 方法1: 检查HTTP头
        if (response.getStatusCodeValue() == 403) {
            String errorHeader = response.getHeaders().getFirst("X-Ca-Error-Message");
            if ("Quota Exhausted".equals(errorHeader) ||
                "Api Market Subscription quota exhausted".equals(errorHeader)) {

                log.error("API配额用完 - HTTP头检测: X-Ca-Error-Message={}", errorHeader);
                return QuotaValidationResult.exhausted("HTTP头显示配额用完");
            }
        }

        // 方法2: 检查响应体中的massage字段
        if (apiResponse.has("massage")) {
            String massage = apiResponse.path("massage").asText();
            if (massage.contains("套餐包次数用完") || massage.contains("quota exhausted")) {
                log.error("API配额用完 - 响应体检测: massage={}", massage);
                return QuotaValidationResult.exhausted(massage);
            }
        }

        // 方法3: 检查code字段
        if (apiResponse.has("code")) {
            int code = apiResponse.path("code").asInt();
            if (code == 403) {
                String massage = apiResponse.path("massage").asText("配额限制");
                log.error("API配额用完 - 状态码检测: code={}, massage={}", code, massage);
                return QuotaValidationResult.exhausted(massage);
            }
        }

        return QuotaValidationResult.available("配额正常");
    }

    /**
     * 处理暂无物流信息的情况（status: "205"）
     */
    public NoInfoValidationResult validateNoInfoStatus(JsonNode apiResponse, String trackingNumber) {
        String status = apiResponse.path("status").asText();

        if ("205".equals(status)) {
            String message = apiResponse.path("message").asText("暂无物流轨迹信息");

            log.info("暂无物流信息: trackingNumber={}, status=205, message={}",
                     trackingNumber, message);

            // 检查是否是新单号（可能还未入网）
            boolean isPossiblyNewOrder = isLikelyNewTrackingNumber(trackingNumber);

            return NoInfoValidationResult.noInfo(message, isPossiblyNewOrder);
        }

        return NoInfoValidationResult.hasInfo("有物流信息");
    }

    /**
     * 判断是否可能是新单号
     */
    private boolean isLikelyNewTrackingNumber(String trackingNumber) {
        // 简单的启发式判断：
        // 1. 单号格式正确
        // 2. 长度符合常见快递单号规范
        // 3. 可以添加更多业务逻辑判断

        if (trackingNumber == null || trackingNumber.length() < 10) {
            return false;
        }

        // 检查是否全是数字（大部分快递单号是纯数字）
        return trackingNumber.matches("\\d{10,20}");
    }

    /**
     * 区分网络错误、API错误和数据解析错误
     */
    public ErrorClassificationResult classifyError(Exception exception,
                                                 ResponseEntity<String> response,
                                                 String rawResponse) {

        // 网络错误
        if (exception instanceof ConnectTimeoutException ||
            exception instanceof SocketTimeoutException ||
            exception instanceof ConnectException) {

            log.error("网络连接错误: {}", exception.getMessage());
            return ErrorClassificationResult.networkError(exception.getMessage());
        }

        // HTTP错误
        if (exception instanceof HttpClientErrorException ||
            exception instanceof HttpServerErrorException) {

            HttpStatusCodeException httpEx = (HttpStatusCodeException) exception;
            log.error("HTTP错误: status={}, body={}",
                      httpEx.getStatusCode(), httpEx.getResponseBodyAsString());
            return ErrorClassificationResult.httpError(
                httpEx.getStatusCode().value(),
                httpEx.getResponseBodyAsString()
            );
        }

        // JSON解析错误
        if (exception instanceof JsonProcessingException ||
            exception instanceof JsonParseException) {

            log.error("JSON解析错误: rawResponse={}, error={}", rawResponse, exception.getMessage());
            return ErrorClassificationResult.parseError(rawResponse, exception.getMessage());
        }

        // API业务错误（HTTP成功但业务失败）
        if (response != null && response.getStatusCodeValue() == 200) {
            try {
                JsonNode jsonResponse = objectMapper.readTree(rawResponse);
                if (jsonResponse.has("error_") || jsonResponse.has("code")) {
                    log.error("API业务错误: response={}", rawResponse);
                    return ErrorClassificationResult.apiBusinessError(rawResponse);
                }
            } catch (Exception e) {
                // 忽略解析异常，归类为未知错误
            }
        }

        // 未知错误
        log.error("未知错误类型: {}", exception.getMessage(), exception);
        return ErrorClassificationResult.unknownError(exception.getMessage());
    }

    /**
     * 获取物流公司列表
     */
    @Override
    public List<LogisticsCompanyVO> getLogisticsCompanyList() {
        List<LogisticsCompanyVO> companies = new ArrayList<>();

        companies.add(new LogisticsCompanyVO("SF", "顺丰速运", "https://www.sf-express.com", "95338"));
        companies.add(new LogisticsCompanyVO("STO", "申通快递", "https://www.sto.cn", "95543"));
        companies.add(new LogisticsCompanyVO("YTO", "圆通速递", "https://www.yto.net.cn", "95554"));
        companies.add(new LogisticsCompanyVO("ZTO", "中通快递", "https://www.zto.com", "95311"));
        companies.add(new LogisticsCompanyVO("YD", "韵达速递", "https://www.yunda.com", "95546"));
        companies.add(new LogisticsCompanyVO("JD", "京东物流", "https://www.jdl.com", "950616"));
        companies.add(new LogisticsCompanyVO("EMS", "中国邮政", "https://www.ems.com.cn", "11183"));
        companies.add(new LogisticsCompanyVO("JTSD", "极兔速递", "https://www.jtexpress.com.cn", "400-820-8585"));
        companies.add(new LogisticsCompanyVO("DBL", "德邦快递", "https://www.deppon.com", "95353"));

        log.info("获取物流公司列表成功，共{}家公司", companies.size());
        return companies;
    }

    /**
     * 清除物流查询缓存
     */
//    @Override
//    public void clearCache(String trackingNumber) {
//        if (StringUtils.isEmpty(trackingNumber)) {
//            log.warn("快递单号为空，无法清除缓存");
//            return;
//        }
//
//        try {
//            String cacheKey = "logistics:tracking:" + trackingNumber.trim().toUpperCase();
//
//            if (redisTemplate != null) {
//                redisTemplate.delete(cacheKey);
//                log.info("物流查询缓存清除成功: trackingNumber={}", trackingNumber);
//            } else {
//                log.warn("Redis模板未初始化，无法清除缓存");
//            }
//        } catch (Exception e) {
//            log.error("清除物流查询缓存失败: trackingNumber={}, error={}", trackingNumber, e.getMessage(), e);
//            throw new ServiceException("清除缓存失败");
//        }
//    }

    /**
     * 检查缓存是否存在（增强版）
     */
    public boolean hasCacheKey(String trackingNumber) {
        if (StringUtils.isEmpty(trackingNumber)) {
            return false;
        }

        try {
            String cacheKey = "logistics:tracking:" + trackingNumber.trim().toUpperCase();

            if (redisTemplate != null) {
                Boolean exists = redisTemplate.hasKey(cacheKey);
                log.debug("检查缓存键存在性: trackingNumber={}, exists={}", trackingNumber, exists);
                return Boolean.TRUE.equals(exists);
            } else {
                log.debug("RedisTemplate未配置，无法检查缓存键");
                return false;
            }
        } catch (Exception e) {
            log.warn("检查缓存键失败: trackingNumber={}, error={}", trackingNumber, e.getMessage());
            return false;
        }
    }

    /**
     * 获取缓存剩余过期时间（增强版）
     */
    public long getCacheExpireTime(String trackingNumber) {
        if (StringUtils.isEmpty(trackingNumber)) {
            return -2;
        }

        try {
            String cacheKey = "logistics:tracking:" + trackingNumber.trim().toUpperCase();

            if (redisTemplate != null) {
                Long expireTime = redisTemplate.getExpire(cacheKey);
                log.debug("获取缓存过期时间: trackingNumber={}, expireTime={}秒", trackingNumber, expireTime);
                return expireTime != null ? expireTime : -2;
            } else {
                log.debug("RedisTemplate未配置，无法获取缓存过期时间");
                return -2;
            }
        } catch (Exception e) {
            log.warn("获取缓存过期时间失败: trackingNumber={}, error={}", trackingNumber, e.getMessage());
            return -2;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            if (redisTemplate != null) {
                Set<String> keys = redisTemplate.keys("logistics:tracking:*");
                stats.put("totalCacheKeys", keys != null ? keys.size() : 0);
                stats.put("cacheKeyPattern", "logistics:tracking:*");
                stats.put("redisTemplateAvailable", true);

                // 统计即将过期的缓存（过期时间小于5分钟）
                long soonExpireCount = 0;
                if (keys != null) {
                    for (String key : keys) {
                        Long expire = redisTemplate.getExpire(key);
                        if (expire != null && expire > 0 && expire < 300) { // 5分钟 = 300秒
                            soonExpireCount++;
                        }
                    }
                }
                stats.put("soonExpireCacheCount", soonExpireCount);

            } else {
                stats.put("totalCacheKeys", 0);
                stats.put("redisTemplateAvailable", false);
                stats.put("message", "RedisTemplate未配置");
            }

            stats.put("timestamp", new Date());

        } catch (Exception e) {
            log.error("获取缓存统计信息失败: error={}", e.getMessage(), e);
            stats.put("error", e.getMessage());
            stats.put("redisTemplateAvailable", false);
        }

        return stats;
    }
}

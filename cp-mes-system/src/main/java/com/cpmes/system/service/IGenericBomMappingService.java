package com.cpmes.system.service;

import com.cpmes.system.domain.bo.GenericBomMappingBo;
import com.cpmes.system.domain.vo.GenericBomMappingVo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 通用BOM映射Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IGenericBomMappingService {

    /**
     * 查询通用BOM映射
     */
    GenericBomMappingVo queryById(Long id);

    /**
     * 查询通用BOM映射列表
     */
    TableDataInfo<GenericBomMappingVo> queryPageList(GenericBomMappingBo bo, PageQuery pageQuery);

    /**
     * 查询通用BOM映射列表
     */
    List<GenericBomMappingVo> queryList(GenericBomMappingBo bo);

    /**
     * 新增通用BOM映射
     */
    Boolean insertByBo(GenericBomMappingBo bo);

    /**
     * 修改通用BOM映射
     */
    Boolean updateByBo(GenericBomMappingBo bo);

    /**
     * 校验并批量删除通用BOM映射信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 根据通用ID获取映射列表（委托给ProductMaterialsBomService）
     */
    List<GenericBomMappingVo> getGenericMappingList(String genericId);
    
    /**
     * 获取通用ID的默认映射（委托给ProductMaterialsBomService）
     */
    GenericBomMappingVo getDefaultGenericMapping(String genericId);
} 
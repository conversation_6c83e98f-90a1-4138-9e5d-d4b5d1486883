package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.domain.PrintInterfaceConfig;
import com.cpmes.system.domain.bo.PrintInterfaceConfigBo;
import com.cpmes.system.domain.vo.PrintInterfaceConfigVo;
import com.cpmes.system.mapper.PrintInterfaceConfigMapper;
import com.cpmes.system.service.IPrintInterfaceConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 打印接口配置Service业务层处理
 * 默认使用MySQL主库（系统配置管理）
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class PrintInterfaceConfigServiceImpl implements IPrintInterfaceConfigService {

    private final PrintInterfaceConfigMapper baseMapper;

    /**
     * 查询打印接口配置
     */
    @Override
    public PrintInterfaceConfigVo queryById(Long configId) {
        PrintInterfaceConfig entity = baseMapper.selectById(configId);
        if (entity == null) {
            return null;
        }
        return BeanUtil.toBean(entity, PrintInterfaceConfigVo.class);
    }

    /**
     * 查询打印接口配置列表
     */
    @Override
    public TableDataInfo<PrintInterfaceConfigVo> queryPageList(PrintInterfaceConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = buildQueryWrapper(bo);
        Page<PrintInterfaceConfig> page = baseMapper.selectPage(pageQuery.build(), lqw);
        
        // 手动转换为Vo对象
        List<PrintInterfaceConfigVo> voList = page.getRecords().stream()
            .map(entity -> BeanUtil.toBean(entity, PrintInterfaceConfigVo.class))
            .collect(Collectors.toList());
        
        Page<PrintInterfaceConfigVo> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(voList);
        
        return TableDataInfo.build(voPage);
    }

    /**
     * 查询打印接口配置列表
     */
    @Override
    public List<PrintInterfaceConfigVo> queryList(PrintInterfaceConfigBo bo) {
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = buildQueryWrapper(bo);
        List<PrintInterfaceConfig> entityList = baseMapper.selectList(lqw);
        return entityList.stream()
            .map(entity -> BeanUtil.toBean(entity, PrintInterfaceConfigVo.class))
            .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<PrintInterfaceConfig> buildQueryWrapper(PrintInterfaceConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getLabelType()), PrintInterfaceConfig::getLabelType, bo.getLabelType());
        lqw.like(StringUtils.isNotBlank(bo.getPrinterName()), PrintInterfaceConfig::getPrinterName, bo.getPrinterName());
        lqw.eq(StringUtils.isNotBlank(bo.getPrinterIp()), PrintInterfaceConfig::getPrinterIp, bo.getPrinterIp());
        lqw.eq(ObjectUtil.isNotNull(bo.getPrinterPort()), PrintInterfaceConfig::getPrinterPort, bo.getPrinterPort());
        lqw.eq(ObjectUtil.isNotNull(bo.getIsDefault()), PrintInterfaceConfig::getIsDefault, bo.getIsDefault());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PrintInterfaceConfig::getStatus, bo.getStatus());
        lqw.orderByAsc(PrintInterfaceConfig::getLabelType, PrintInterfaceConfig::getPriority);
        return lqw;
    }

    /**
     * 新增打印接口配置
     */
    @Override
    public Boolean insertByBo(PrintInterfaceConfigBo bo) {
        PrintInterfaceConfig add = BeanUtil.toBean(bo, PrintInterfaceConfig.class);
        validEntityBeforeSave(add);
        // 设置默认值
        if (add.getPrinterPort() == null) {
            add.setPrinterPort(8081);
        }
        if (StringUtils.isBlank(add.getApiEndpoint())) {
            add.setApiEndpoint("/api/data");
        }
        if (add.getIsDefault() == null) {
            add.setIsDefault(false);
        }
        if (add.getPriority() == null) {
            add.setPriority(1);
        }
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("1");
        }
        if (add.getTimeoutSeconds() == null) {
            add.setTimeoutSeconds(30);
        }
        if (add.getRetryCount() == null) {
            add.setRetryCount(3);
        }
        
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setConfigId(add.getConfigId());
        }
        return flag;
    }

    /**
     * 修改打印接口配置
     */
    @Override
    public Boolean updateByBo(PrintInterfaceConfigBo bo) {
        PrintInterfaceConfig update = BeanUtil.toBean(bo, PrintInterfaceConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrintInterfaceConfig entity) {
        // 校验同一标签类型+IP+端口的唯一性
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(PrintInterfaceConfig::getLabelType, entity.getLabelType());
        lqw.eq(PrintInterfaceConfig::getPrinterIp, entity.getPrinterIp());
        lqw.eq(PrintInterfaceConfig::getPrinterPort, entity.getPrinterPort());
        if (entity.getConfigId() != null) {
            lqw.ne(PrintInterfaceConfig::getConfigId, entity.getConfigId());
        }
        
        long count = baseMapper.selectCount(lqw);
        if (count > 0) {
            throw new RuntimeException("同一标签类型下的IP地址和端口组合已存在");
        }
        
        // 如果设置为默认，需要将同一标签类型的其他配置设为非默认
        if (Boolean.TRUE.equals(entity.getIsDefault())) {
            PrintInterfaceConfig updateDefault = new PrintInterfaceConfig();
            updateDefault.setIsDefault(false);
            
            LambdaQueryWrapper<PrintInterfaceConfig> defaultLqw = Wrappers.lambdaQuery();
            defaultLqw.eq(PrintInterfaceConfig::getLabelType, entity.getLabelType());
            defaultLqw.eq(PrintInterfaceConfig::getIsDefault, true);
            if (entity.getConfigId() != null) {
                defaultLqw.ne(PrintInterfaceConfig::getConfigId, entity.getConfigId());
            }
            
            baseMapper.update(updateDefault, defaultLqw);
        }
    }

    /**
     * 批量删除打印接口配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验是否为默认配置，如果是则不允许删除
            for (Long id : ids) {
                PrintInterfaceConfigVo config = queryById(id);
                if (config == null) {
                    throw new RuntimeException("配置不存在");
                }
                if (Boolean.TRUE.equals(config.getIsDefault())) {
                    throw new RuntimeException("不能删除默认打印机配置：" + config.getPrinterName());
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量更新状态
     */
    @Override
    public Boolean updateStatus(Collection<Long> ids, String status) {
        if (ObjectUtil.isEmpty(ids) || StringUtils.isBlank(status)) {
            return false;
        }
        
        List<PrintInterfaceConfig> updateList = ids.stream()
            .map(id -> {
                PrintInterfaceConfig config = new PrintInterfaceConfig();
                config.setConfigId(id);
                config.setStatus(status);
                return config;
            })
            .collect(Collectors.toList());
            
        return baseMapper.updateBatchById(updateList);
    }

    /**
     * 根据标签类型查询打印机配置
     */
    @Override
    public List<PrintInterfaceConfigVo> queryByLabelType(String labelType) {
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(labelType), PrintInterfaceConfig::getLabelType, labelType);
        lqw.eq(PrintInterfaceConfig::getStatus, "1"); // 只查询启用的配置
        lqw.orderByAsc(PrintInterfaceConfig::getPriority);
        lqw.orderByDesc(PrintInterfaceConfig::getIsDefault);
        List<PrintInterfaceConfig> entityList = baseMapper.selectList(lqw);
        return entityList.stream()
            .map(entity -> BeanUtil.toBean(entity, PrintInterfaceConfigVo.class))
            .collect(Collectors.toList());
    }

    /**
     * 获取最佳打印机配置
     */
    @Override
    public Map<String, Object> getBestPrinterConfig(String labelType) {
        return baseMapper.selectBestPrinterConfig(labelType);
    }

    /**
     * 测试打印机连接
     */
    @Override
    public Boolean testPrinterConnection(Long configId) {
        PrintInterfaceConfigVo config = queryById(configId);
        if (config == null) {
            throw new RuntimeException("配置不存在");
        }
        
        System.out.println("测试打印机连接，配置ID: " + configId + ", 标签类型: " + config.getLabelType());
        
        try {
            String testUrl = String.format("http://%s:%d%s", 
                config.getPrinterIp(), 
                config.getPrinterPort(), 
                config.getApiEndpoint());
            
            System.out.println("开始测试打印机连接: " + testUrl);
            
            // 发送测试心跳包到打印机服务
            return sendTestRequest(testUrl, config.getTimeoutSeconds(), config.getLabelType());
            
        } catch (Exception e) {
            System.err.println("连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送测试请求到打印机服务
     */
    private Boolean sendTestRequest(String apiUrl, Integer timeoutSeconds, String labelType) {
        try {
            // 根据标签类型创建不同的测试数据
            Map<String, Object> testData = createTestDataByLabelType(labelType);
            
            // 构建JSON字符串
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : testData.entrySet()) {
                if (!first) jsonBuilder.append(",");
                jsonBuilder.append("\"").append(entry.getKey()).append("\":");
                
                Object value = entry.getValue();
                if (value instanceof String) {
                    jsonBuilder.append("\"").append(value).append("\"");
                } else if (value instanceof List) {
                    // 处理数组类型（如items）
                    jsonBuilder.append("[");
                    List<?> list = (List<?>) value;
                    for (int i = 0; i < list.size(); i++) {
                        if (i > 0) jsonBuilder.append(",");
                        Object item = list.get(i);
                        if (item instanceof Map) {
                            // 处理对象类型
                            jsonBuilder.append("{");
                            Map<?, ?> map = (Map<?, ?>) item;
                            boolean firstProp = true;
                            for (Map.Entry<?, ?> mapEntry : map.entrySet()) {
                                if (!firstProp) jsonBuilder.append(",");
                                jsonBuilder.append("\"").append(mapEntry.getKey()).append("\":");
                                if (mapEntry.getValue() instanceof String) {
                                    jsonBuilder.append("\"").append(mapEntry.getValue()).append("\"");
                                } else {
                                    jsonBuilder.append(mapEntry.getValue());
                                }
                                firstProp = false;
                            }
                            jsonBuilder.append("}");
                        } else {
                            jsonBuilder.append("\"").append(item).append("\"");
                        }
                    }
                    jsonBuilder.append("]");
                } else {
                    jsonBuilder.append(value);
                }
                first = false;
            }
            jsonBuilder.append("}");
            
            String jsonData = jsonBuilder.toString();
            
            // 使用Java原生HTTP客户端进行连接测试
            java.net.URL url = new java.net.URL(apiUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            
            // 设置连接参数
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(timeoutSeconds != null ? timeoutSeconds * 1000 : 30000); // 连接超时
            connection.setReadTimeout(timeoutSeconds != null ? timeoutSeconds * 1000 : 30000);    // 读取超时
            connection.setDoOutput(true);
            connection.setDoInput(true);
            
            // 发送数据
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes("utf-8");
                os.write(input, 0, input.length);
            }
            
            // 获取响应
            int responseCode = connection.getResponseCode();
            
            // 读取响应内容
            String responseBody = "";
            try (java.io.BufferedReader br = new java.io.BufferedReader(
                    new java.io.InputStreamReader(
                        responseCode >= 200 && responseCode < 300 
                            ? connection.getInputStream() 
                            : connection.getErrorStream(), 
                        "utf-8"))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                responseBody = response.toString();
            }
            
            System.out.println("打印机响应代码: " + responseCode);
            System.out.println("打印机响应内容: " + responseBody);
            
            // 判断连接是否成功
            // 1. HTTP状态码为2xx表示成功
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("打印机连接测试成功");
                return true;
            }
            // 2. 如果是404或其他错误，但服务器有响应，说明网络是通的，只是接口不存在
            else if (responseCode == 404) {
                System.out.println("打印机服务可达，但API端点可能不正确");
                return true; // 认为连接成功，只是接口配置可能有问题
            }
            // 3. 其他HTTP错误
            else {
                System.err.println("打印机连接失败，HTTP状态码: " + responseCode);
                return false;
            }
            
        } catch (java.net.ConnectException e) {
            System.err.println("无法连接到打印机服务: " + e.getMessage());
            return false;
        } catch (java.net.SocketTimeoutException e) {
            System.err.println("连接打印机超时: " + e.getMessage());
            return false;
        } catch (java.net.UnknownHostException e) {
            System.err.println("无法解析打印机主机地址: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("连接测试异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 设置默认打印机
     */
    @Override
    public Boolean setDefaultPrinter(Long configId) {
        PrintInterfaceConfigVo config = queryById(configId);
        if (config == null) {
            throw new RuntimeException("配置不存在");
        }
        
        // 将同一标签类型的其他配置设为非默认
        PrintInterfaceConfig updateOthers = new PrintInterfaceConfig();
        updateOthers.setIsDefault(false);
        
        LambdaQueryWrapper<PrintInterfaceConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(PrintInterfaceConfig::getLabelType, config.getLabelType());
        lqw.ne(PrintInterfaceConfig::getConfigId, configId);
        baseMapper.update(updateOthers, lqw);
        
        // 设置当前配置为默认
        PrintInterfaceConfig updateCurrent = new PrintInterfaceConfig();
        updateCurrent.setConfigId(configId);
        updateCurrent.setIsDefault(true);
        
        return baseMapper.updateById(updateCurrent) > 0;
    }
    
    /**
     * 根据标签类型创建不同的测试数据
     * 参考二维码打印文档中的标准格式
     */
    private Map<String, Object> createTestDataByLabelType(String labelType) {
        System.out.println("创建测试数据，标签类型: [" + labelType + "]");
        System.out.println("标签类型长度: " + (labelType != null ? labelType.length() : "null"));
        System.out.println("标签类型字节数组: " + (labelType != null ? java.util.Arrays.toString(labelType.getBytes()) : "null"));
        
        Map<String, Object> testData = new HashMap<>();
        
        // 根据标签类型添加特定字段（参考二维码打印文档格式）
        switch (labelType) {
            case "purchase":
                // 采购入库标签测试数据
                testData.put("label_type", "purchase");
                testData.put("purchase_no", "TEST_PO_" + System.currentTimeMillis());
                testData.put("item_name", "测试物料");
                testData.put("batch_no", "BATCH_TEST_001");
                testData.put("board_type", "双板");
                System.out.println("✓ 匹配到采购入库case分支");
                break;
                
            case "smt_outbound":
                // 贴片出库标签测试数据
                testData.put("label_type", "smt_outbound");
                testData.put("outbound_no", "TEST_SMT_OUT_" + System.currentTimeMillis());
                testData.put("item_name", "测试贴片产品");
                testData.put("batch_no", "BATCH_SMT_TEST_001");
                testData.put("board_type", "双板");
                System.out.println("✓ 匹配到贴片出库case分支");
                break;
                
            case "welding_outbound":
                // 焊接出库标签测试数据
                testData.put("label_type", "welding_outbound");
                testData.put("outbound_no", "TEST_WELD_OUT_" + System.currentTimeMillis());
                testData.put("item_name", "测试焊接产品");
                testData.put("batch_no", "BATCH_WELD_TEST_001");
                testData.put("board_type", "双板");
                testData.put("style_name", "标准款");
                System.out.println("✓ 匹配到焊接出库case分支");
                break;
                
            case "warehouse_zone":
                // 仓库区域标签测试数据
                testData.put("label_type", "warehouse_zone");
                testData.put("zone_code", "TEST_ZONE_A01_001");
                System.out.println("✓ 匹配到仓库区域case分支");
                break;
                
            case "storage":
            case "quality":
            case "shipping":
                // 其他标签类型使用通用格式进行兼容测试
                testData.put("label_type", labelType);
                testData.put("test_mode", true);
                testData.put("timestamp", System.currentTimeMillis());
                testData.put("test_data", "连接测试数据");
                System.out.println("✓ 匹配到其他标签类型case分支: " + labelType);
                break;
                
            default:
                // 默认通用测试数据
                testData.put("label_type", labelType != null ? labelType : "generic");
                testData.put("test_mode", true);
                testData.put("timestamp", System.currentTimeMillis());
                testData.put("message", "connection_test");
                System.out.println("⚠ 进入默认case分支，标签类型: [" + labelType + "]");
                break;
        }
        
        System.out.println("最终测试数据: " + testData);
        return testData;
    }
} 
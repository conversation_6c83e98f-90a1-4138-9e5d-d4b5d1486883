package com.cpmes.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.helper.LoginHelper;
import com.cpmes.oss.core.OssClient;
import com.cpmes.oss.factory.OssFactory;
import com.cpmes.system.entity.PurchaseOrderImage;
import com.cpmes.system.mapper.PurchaseOrderImageMapper;
import com.cpmes.system.service.IPurchaseOrderImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 采购订单图片管理 Service实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderImageServiceImpl extends ServiceImpl<PurchaseOrderImageMapper, PurchaseOrderImage> 
        implements IPurchaseOrderImageService {

    private final PurchaseOrderImageMapper purchaseOrderImageMapper;

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseOrderImage> uploadImages(Long purchaseOrderId, MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new ServiceException("请选择要上传的图片");
        }
        
        if (files.length > 10) {
            throw new ServiceException("最多只能上传10张图片");
        }

        List<PurchaseOrderImage> imageList = new ArrayList<>();
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername(); // 获取用户名
        Date currentTime = new Date();

        // 获取当前最大排序序号
        Integer maxSortOrder = getMaxSortOrderByPurchaseOrderId(purchaseOrderId);

        try {
            OssClient ossClient = OssFactory.instance("minio");

            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];

                // 验证文件
                validateImageFile(file);

                // 生成存储路径（与PurchaseMinioUtils保持一致）
                String objectName = generateImageObjectName(purchaseOrderId, file.getOriginalFilename());

                // 上传到MinIO
                com.cpmes.oss.entity.UploadResult uploadResult = ossClient.upload(
                    file.getBytes(),
                    objectName,
                    file.getContentType()
                );

                // 创建图片记录
                PurchaseOrderImage image = new PurchaseOrderImage();
                image.setPurchaseOrderId(purchaseOrderId);
                image.setOriginalName(file.getOriginalFilename());
                image.setStoragePath(uploadResult.getUrl());  // 使用UploadResult的URL
                image.setFileSize(file.getSize());
                image.setContentType(file.getContentType());
                image.setUploadTime(currentTime);
                image.setUploadUserId(currentUserId);
                image.setUploadUserName(currentUserName);  // 存储用户名，避免跨数据库查询
                image.setSortOrder(maxSortOrder + i + 1);
                image.setStatus(PurchaseOrderImage.Status.NORMAL);
                image.setCreateTime(currentTime);
                image.setUpdateTime(currentTime);

                imageList.add(image);
                log.info("采购订单{}图片上传成功: {}", purchaseOrderId, uploadResult.getUrl());
            }
            
            // 批量插入数据库
            if (!imageList.isEmpty()) {
                purchaseOrderImageMapper.batchInsert(imageList);
            }
            
            log.info("采购订单{}图片上传成功，共上传{}张图片", purchaseOrderId, imageList.size());
            return imageList;
            
        } catch (Exception e) {
            log.error("采购订单{}图片上传失败", purchaseOrderId, e);
            throw new ServiceException("图片上传失败：" + e.getMessage());
        }
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源
    public List<PurchaseOrderImage> getImagesByPurchaseOrderId(Long purchaseOrderId) {
        List<PurchaseOrderImage> images = purchaseOrderImageMapper.selectByPurchaseOrderId(purchaseOrderId);
        
        // 格式化文件大小
        images.forEach(image -> {
            image.setFileSizeFormatted(formatFileSize(image.getFileSize()));
        });
        
        return images;
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源
    public List<PurchaseOrderImage> getImagesWithUserNameByPurchaseOrderId(Long purchaseOrderId) {
        List<PurchaseOrderImage> images = purchaseOrderImageMapper.selectByPurchaseOrderIdWithUserName(purchaseOrderId);

        // 格式化文件大小
        images.forEach(image -> {
            image.setFileSizeFormatted(formatFileSize(image.getFileSize()));
        });

        return images;
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源
    public List<PurchaseOrderImage> getImagesWithMetadata(Long purchaseOrderId) {
        List<PurchaseOrderImage> images = purchaseOrderImageMapper.selectByPurchaseOrderIdWithUserName(purchaseOrderId);

        // 格式化文件大小并设置完整元数据
        images.forEach(image -> {
            image.setFileSizeFormatted(formatFileSize(image.getFileSize()));
        });

        log.info("获取采购订单{}的图片元数据，共{}张图片", purchaseOrderId, images.size());
        return images;
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行UPDATE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean updateImageDescription(Long imageId, String description) {
        try {
            LambdaUpdateWrapper<PurchaseOrderImage> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PurchaseOrderImage::getId, imageId)
                        .set(PurchaseOrderImage::getImageDescription, description)
                        .set(PurchaseOrderImage::getUpdateTime, new Date());

            boolean result = this.update(updateWrapper);
            if (result) {
                log.info("图片{}描述更新成功: {}", imageId, description);
            }
            return result;
        } catch (Exception e) {
            log.error("更新图片{}描述失败", imageId, e);
            throw new ServiceException("更新图片描述失败：" + e.getMessage());
        }
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行DELETE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImage(Long imageId) {
        LambdaUpdateWrapper<PurchaseOrderImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PurchaseOrderImage::getId, imageId)
                    .set(PurchaseOrderImage::getStatus, PurchaseOrderImage.Status.DELETED)
                    .set(PurchaseOrderImage::getUpdateTime, new Date());

        return this.update(updateWrapper);
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行批量DELETE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteImages(List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return true;
        }
        
        LambdaUpdateWrapper<PurchaseOrderImage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(PurchaseOrderImage::getId, imageIds)
                    .set(PurchaseOrderImage::getStatus, PurchaseOrderImage.Status.DELETED)
                    .set(PurchaseOrderImage::getUpdateTime, new Date());
        
        return this.update(updateWrapper);
    }



    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行UPDATE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean updateImageSortOrder(Long imageId, Integer sortOrder) {
        return purchaseOrderImageMapper.updateSortOrder(imageId, sortOrder) > 0;
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行批量UPDATE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateImageSortOrder(List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return true;
        }

        for (int i = 0; i < imageIds.size(); i++) {
            purchaseOrderImageMapper.updateSortOrder(imageIds.get(i), i + 1);
        }

        return true;
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行DELETE操作
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteImagesByPurchaseOrderId(Long purchaseOrderId) {
        return purchaseOrderImageMapper.deleteByPurchaseOrderId(purchaseOrderId) >= 0;
    }

    @Override
    public String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }

        final String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(fileSize) / Math.log10(1024));

        if (digitGroups >= units.length) {
            digitGroups = units.length - 1;
        }

        DecimalFormat df = new DecimalFormat("#,##0.#");
        return df.format(fileSize / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    @Override
    @DS("slave") // 确保使用PostgreSQL数据源
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<PurchaseOrderImage> images) {
        if (images == null || images.isEmpty()) {
            return true;
        }

        try {
            // 使用MyBatis-Plus的批量保存方法
            boolean success = this.saveBatch(images);
            if (success) {
                log.info("批量保存图片成功，共{}张图片", images.size());
            } else {
                log.error("批量保存图片失败");
            }
            return success;
        } catch (Exception e) {
            log.error("批量保存图片异常", e);
            return false;
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new ServiceException("图片文件不能为空");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new ServiceException("只能上传图片文件");
        }
        
        // 检查文件大小（5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            throw new ServiceException("图片文件大小不能超过5MB");
        }
    }

    /**
     * 生成图片对象名称（与PurchaseMinioUtils保持一致）
     */
    private String generateImageObjectName(Long purchaseOrderId, String originalFilename) {
        // 生成时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());

        // 获取文件扩展名
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成唯一文件名
        String uniqueFileName = timestamp + "_" + java.util.UUID.randomUUID().toString().replace("-", "") + extension;

        // 构建完整的对象路径
        return "purchase/" + purchaseOrderId + "/images/" + uniqueFileName;
    }

    /**
     * 获取采购订单的最大排序序号
     */
    @Override
    @DS("slave") // 确保使用PostgreSQL数据源进行查询
    public Integer getMaxSortOrderByPurchaseOrderId(Long purchaseOrderId) {
        LambdaQueryWrapper<PurchaseOrderImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrderImage::getPurchaseOrderId, purchaseOrderId)
                   .eq(PurchaseOrderImage::getStatus, PurchaseOrderImage.Status.NORMAL)
                   .orderByDesc(PurchaseOrderImage::getSortOrder)
                   .last("LIMIT 1");
        
        PurchaseOrderImage lastImage = this.getOne(queryWrapper);
        return lastImage != null ? lastImage.getSortOrder() : 0;
    }
}

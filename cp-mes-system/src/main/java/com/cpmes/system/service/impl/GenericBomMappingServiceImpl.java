package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cpmes.system.domain.bo.GenericBomMappingBo;
import com.cpmes.system.domain.vo.GenericBomMappingVo;
import com.cpmes.system.domain.GenericBomMapping;
import com.cpmes.system.mapper.GenericBomMappingMapper;
import com.cpmes.system.service.IGenericBomMappingService;
import com.cpmes.system.service.IProductMaterialsBomService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 通用BOM映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@RequiredArgsConstructor
@Service
public class GenericBomMappingServiceImpl implements IGenericBomMappingService {

    private final GenericBomMappingMapper baseMapper;
    private final IProductMaterialsBomService productMaterialsBomService;

    /**
     * 查询通用BOM映射
     */
    @Override
    public GenericBomMappingVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询通用BOM映射列表
     */
    @Override
    public TableDataInfo<GenericBomMappingVo> queryPageList(GenericBomMappingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GenericBomMapping> lqw = buildQueryWrapper(bo);
        Page<GenericBomMappingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询通用BOM映射列表
     */
    @Override
    public List<GenericBomMappingVo> queryList(GenericBomMappingBo bo) {
        LambdaQueryWrapper<GenericBomMapping> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GenericBomMapping> buildQueryWrapper(GenericBomMappingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GenericBomMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getGenericId()), GenericBomMapping::getGenericId, bo.getGenericId());
        lqw.like(ObjectUtil.isNotNull(bo.getGenericName()), GenericBomMapping::getGenericName, bo.getGenericName());
        lqw.eq(ObjectUtil.isNotNull(bo.getMappedModel()), GenericBomMapping::getMappedModel, bo.getMappedModel());
        lqw.eq(ObjectUtil.isNotNull(bo.getMappedModule()), GenericBomMapping::getMappedModule, bo.getMappedModule());
        lqw.eq(ObjectUtil.isNotNull(bo.getMappedBoardType()), GenericBomMapping::getMappedBoardType, bo.getMappedBoardType());
        lqw.eq(ObjectUtil.isNotNull(bo.getIsDefault()), GenericBomMapping::getIsDefault, bo.getIsDefault());
        lqw.orderByAsc(GenericBomMapping::getSortOrder);
        lqw.orderByDesc(GenericBomMapping::getCreateTime);
        return lqw;
    }

    /**
     * 新增通用BOM映射
     */
    @Override
    public Boolean insertByBo(GenericBomMappingBo bo) {
        GenericBomMapping add = BeanUtil.toBean(bo, GenericBomMapping.class);
        
        // 设置默认值
        if (add.getSortOrder() == null) {
            add.setSortOrder(1);
        }
        if (add.getIsDefault() == null) {
            add.setIsDefault("0");
        }
        if (add.getDelFlag() == null) {
            add.setDelFlag("0");
        }
        
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改通用BOM映射
     */
    @Override
    public Boolean updateByBo(GenericBomMappingBo bo) {
        GenericBomMapping update = BeanUtil.toBean(bo, GenericBomMapping.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除通用BOM映射信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 这里可以添加删除前的业务校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
    
    /**
     * 根据通用ID获取映射列表（委托给ProductMaterialsBomService）
     */
    @Override
    public List<GenericBomMappingVo> getGenericMappingList(String genericId) {
        return productMaterialsBomService.getGenericMappingList(genericId);
    }
    
    /**
     * 获取通用ID的默认映射（委托给ProductMaterialsBomService）
     */
    @Override
    public GenericBomMappingVo getDefaultGenericMapping(String genericId) {
        return productMaterialsBomService.getDefaultGenericMapping(genericId);
    }
} 
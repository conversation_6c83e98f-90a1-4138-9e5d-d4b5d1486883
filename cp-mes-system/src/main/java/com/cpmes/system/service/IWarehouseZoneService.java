package com.cpmes.system.service;

import com.cpmes.system.domain.vo.WarehouseZoneVo;
import com.cpmes.system.domain.bo.WarehouseZoneBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仓库区域Service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IWarehouseZoneService {

    /**
     * 查询仓库区域
     *
     * @param zoneId 仓库区域主键
     * @return 仓库区域
     */
    WarehouseZoneVo queryById(Long zoneId);

    /**
     * 查询仓库区域列表
     *
     * @param bo 仓库区域
     * @return 仓库区域集合
     */
    TableDataInfo<WarehouseZoneVo> queryPageList(WarehouseZoneBo bo, PageQuery pageQuery);

    /**
     * 查询仓库区域列表
     *
     * @param bo 仓库区域
     * @return 仓库区域集合
     */
    List<WarehouseZoneVo> queryList(WarehouseZoneBo bo);

    /**
     * 新增仓库区域
     *
     * @param bo 仓库区域
     * @return 结果
     */
    Boolean insertByBo(WarehouseZoneBo bo);

    /**
     * 修改仓库区域
     *
     * @param bo 仓库区域
     * @return 结果
     */
    Boolean updateByBo(WarehouseZoneBo bo);

    /**
     * 校验并批量删除仓库区域信息
     *
     * @param ids 需要删除的仓库区域主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量启用/停用区域
     *
     * @param ids 区域ID集合
     * @param status 状态(0:停用 1:启用)
     * @return 结果
     */
    Boolean updateStatus(Collection<Long> ids, String status);

    /**
     * 根据仓库编码批量更新区域状态（级联停用）
     *
     * @param warehouseCode 仓库编码
     * @param status 状态(0:停用 1:启用)
     * @return 结果
     */
    Boolean updateStatusByWarehouseCode(String warehouseCode, String status);

    /**
     * 根据仓库类型查询区域列表
     *
     * @param warehouseType 仓库类型
     * @return 仓库区域集合
     */
    List<WarehouseZoneVo> queryByWarehouseType(String warehouseType);

    /**
     * 根据仓库ID查询区域列表
     *
     * @param warehouseId 仓库ID
     * @return 仓库区域集合
     */
    List<WarehouseZoneVo> queryByWarehouseId(Long warehouseId);

    /**
     * 根据仓库编码查询区域列表
     *
     * @param warehouseCode 仓库编码
     * @return 仓库区域集合
     */
    List<WarehouseZoneVo> queryByWarehouseCode(String warehouseCode);

    /**
     * 根据区域编码查询区域信息
     *
     * @param zoneCode 区域编码
     * @return 仓库区域信息
     */
    WarehouseZoneVo queryByZoneCode(String zoneCode);

    /**
     * 校验区域编码是否唯一
     *
     * @param zoneCode 区域编码
     * @param zoneId 区域ID
     * @return 结果
     */
    Boolean checkZoneCodeUnique(String zoneCode, Long zoneId);

    /**
     * 校验区域名称、仓库、类型组合是否唯一
     *
     * @param zoneName 区域名称
     * @param warehouseId 仓库ID
     * @param zoneType 区域类型
     * @param zoneId 区域ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    Boolean checkZoneInfoUnique(String zoneName, Long warehouseId, String zoneType, Long zoneId);

    /**
     * 校验区域名称、仓库编码、类型组合是否唯一
     *
     * @param zoneName 区域名称
     * @param warehouseCode 仓库编码
     * @param zoneType 区域类型
     * @param zoneId 区域ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    Boolean checkZoneInfoUniqueByCode(String zoneName, String warehouseCode, String zoneType, Long zoneId);

    /**
     * 生成区域编码
     *
     * @param warehouseId 仓库ID
     * @param warehouseCode 仓库编码
     * @param zoneType 区域类型
     * @return 生成的区域编码
     */
    String generateZoneCode(Long warehouseId, String warehouseCode, String zoneType);

    /**
     * 获取区域统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getZoneStatistics();

    /**
     * 获取所有启用状态的区域编码列表
     * @return 区域编码列表
     */
    List<String> getAllZoneCodes();

    /**
     * 获取所有启用状态的区域编码和名称列表
     * @return 区域编码和名称列表
     */
    List<com.cpmes.system.entity.vo.PurchaseZoneDataVo.ZoneInfo> getAllZoneCodesWithNames();

    /**
     * 生成区域二维码
     *
     * @param zoneId 区域ID
     * @return 结果
     */
    Boolean generateQrCode(Long zoneId);

    /**
     * 批量生成区域二维码
     *
     * @param zoneIds 区域ID集合
     * @return 结果
     */
    Boolean batchGenerateQrCode(Collection<Long> zoneIds);

    /**
     * 获取区域二维码信息
     *
     * @param zoneId 区域ID
     * @return 二维码信息
     */
    Map<String, Object> getQrCodeInfo(Long zoneId);

    /**
     * 打印区域二维码
     *
     * @param printData 打印数据
     * @param clientIp 客户端IP
     * @return 打印是否成功
     */
    Boolean printQrCode(Map<String, Object> printData, String clientIp);
}

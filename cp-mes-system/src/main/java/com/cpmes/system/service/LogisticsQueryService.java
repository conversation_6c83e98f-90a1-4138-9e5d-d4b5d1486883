package com.cpmes.system.service;

import com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto;
import com.cpmes.system.entity.vo.LogisticsCompanyVO;

import java.util.List;

/**
 * 物流查询服务接口
 */
public interface LogisticsQueryService {

    /**
     * 查询物流信息
     *
     * @param trackingNumber 快递单号
     * @param logisticsCompany 物流公司（可选，用于优化查询）
     * @return 物流追踪信息
     */
    LogisticsTrackingDto queryLogistics(String trackingNumber, String logisticsCompany);

    /**
     * 绕过缓存查询物流信息（用于调试和强制刷新）
     *
     * @param trackingNumber 快递单号
     * @param logisticsCompany 物流公司（可选，用于优化查询）
     * @return 物流追踪信息
     */
    LogisticsTrackingDto queryLogisticsWithoutCache(String trackingNumber, String logisticsCompany);

    /**
     * 清除缓存
     *
     * @param trackingNumber 快递单号
     */
    void clearCache(String trackingNumber);

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isServiceAvailable();

    /**
     * 获取物流公司列表
     *
     * @return 物流公司列表
     */
    List<LogisticsCompanyVO> getLogisticsCompanyList();
}

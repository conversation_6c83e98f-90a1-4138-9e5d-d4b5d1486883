package com.cpmes.system.service;

import com.cpmes.system.domain.FunctionalStyle;
import com.cpmes.system.domain.vo.FunctionalStyleVo;
import com.cpmes.system.domain.bo.FunctionalStyleBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 功能系列款式Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IFunctionalStyleService {

    /**
     * 查询功能系列款式
     */
    FunctionalStyleVo queryById(Integer styleId);

    /**
     * 查询功能系列款式列表
     */
    TableDataInfo<FunctionalStyleVo> queryPageList(FunctionalStyleBo bo, PageQuery pageQuery);

    /**
     * 查询功能系列款式列表
     */
    List<FunctionalStyleVo> queryList(FunctionalStyleBo bo);

    /**
     * 新增功能系列款式
     */
    Boolean insertByBo(FunctionalStyleBo bo);

    /**
     * 修改功能系列款式
     */
    Boolean updateByBo(FunctionalStyleBo bo);

    /**
     * 校验并批量删除功能系列款式信息
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);
} 
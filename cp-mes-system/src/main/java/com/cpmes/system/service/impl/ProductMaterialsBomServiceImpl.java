package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.common.utils.spring.SpringUtils;
import com.cpmes.system.domain.ProductMaterialsBom;
import com.cpmes.system.domain.bo.ProductMaterialsBomBo;
import com.cpmes.system.domain.bo.ProductMaterialsBomImportBo;
import com.cpmes.system.domain.vo.GenericBomMappingVo;
import com.cpmes.system.domain.vo.ImportProcessResult;
import com.cpmes.system.domain.vo.ProductMaterialsBomVo;
import com.cpmes.system.domain.GenericBomMapping;
import com.cpmes.system.entity.RawMaterialWarehouse;
import com.cpmes.system.mapper.GenericBomMappingMapper;
import com.cpmes.system.mapper.ProductMaterialsBomMapper;
import com.cpmes.system.service.IProductMaterialsBomService;
import com.cpmes.system.serviceJenasi.RawMaterialWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品所需原材料明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductMaterialsBomServiceImpl implements IProductMaterialsBomService {

    private final ProductMaterialsBomMapper baseMapper;
    private final GenericBomMappingMapper genericBomMappingMapper;
    private final RawMaterialWarehouseService rawMaterialWarehouseService;

    /**
     * 查询产品所需原材料明细
     */
    @Override
    public ProductMaterialsBomVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询产品所需原材料明细列表（关联产品表获取款式信息）
     */
    @Override
    public TableDataInfo<ProductMaterialsBomVo> queryPageList(ProductMaterialsBomBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = buildQueryWrapper(bo);
        Page<ProductMaterialsBomVo> result = baseMapper.selectVoPageWithStyle(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询产品所需原材料明细列表（关联产品表获取款式信息）
     */
    @Override
    public List<ProductMaterialsBomVo> queryList(ProductMaterialsBomBo bo) {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoListWithStyle(lqw);
    }

    private LambdaQueryWrapper<ProductMaterialsBom> buildQueryWrapper(ProductMaterialsBomBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getModel()), ProductMaterialsBom::getModel, bo.getModel());
        lqw.like(StringUtils.isNotBlank(bo.getModule()), ProductMaterialsBom::getModule, bo.getModule());
        lqw.like(StringUtils.isNotBlank(bo.getBoardType()), ProductMaterialsBom::getBoardType, bo.getBoardType());
        lqw.like(StringUtils.isNotBlank(bo.getMaterial()), ProductMaterialsBom::getMaterial, bo.getMaterial());
        lqw.eq(StringUtils.isNotBlank(bo.getVersion()), ProductMaterialsBom::getVersion, bo.getVersion());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdatedBy()), ProductMaterialsBom::getUpdatedBy, bo.getUpdatedBy());
        lqw.orderByDesc(ProductMaterialsBom::getId);
        return lqw;
    }

    /**
     * 新增产品所需原材料明细
     */
    @Override
    public Boolean insertByBo(ProductMaterialsBomBo bo) {
        ProductMaterialsBom add = BeanUtil.toBean(bo, ProductMaterialsBom.class);

        // 设置默认删除标志
        if (add.getDelFlag() == null) {
            add.setDelFlag("0");
        }

        // 手动设置自定义字段
        fillCustomFields(add, true);

        // 确保预留字段3不为null（避免MyBatis-Plus的NOT_NULL策略跳过该字段）
        if (add.getReservedField3() == null) {
            add.setReservedField3("");
        }

        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改产品所需原材料明细
     */
    @Override
    public Boolean updateByBo(ProductMaterialsBomBo bo) {
        ProductMaterialsBom update = BeanUtil.toBean(bo, ProductMaterialsBom.class);

        // 手动设置自定义字段
        fillCustomFields(update, false);

        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 填充自定义字段
     */
    private void fillCustomFields(ProductMaterialsBom entity, boolean isInsert) {
        try {
            String currentTime = java.time.LocalDateTime.now().toString();
            String username = com.cpmes.common.helper.LoginHelper.getLoginUser().getUsername();

            if (isInsert) {
                // 新增时设置
                if (StringUtils.isBlank(entity.getUpdatedBy())) {
                    entity.setUpdatedBy(username);
                }
                if (StringUtils.isBlank(entity.getUpdatedAt())) {
                    entity.setUpdatedAt(currentTime);
                }
            } else {
                // 更新时总是设置
                entity.setUpdatedBy(username);
                entity.setUpdatedAt(currentTime);
            }
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
            entity.setUpdatedBy("系统用户");
            entity.setUpdatedAt(java.time.LocalDateTime.now().toString());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductMaterialsBom entity) {
        // 检查唯一性约束：同一版本下的型号、模块、板类型、原料组合必须唯一
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<ProductMaterialsBom>()
            .eq(ProductMaterialsBom::getModel, entity.getModel())
            .eq(ProductMaterialsBom::getModule, entity.getModule())
            .eq(ProductMaterialsBom::getBoardType, entity.getBoardType())
            .eq(ProductMaterialsBom::getMaterial, entity.getMaterial())
            .eq(ProductMaterialsBom::getVersion, entity.getVersion())
            .ne(ObjectUtil.isNotNull(entity.getId()), ProductMaterialsBom::getId, entity.getId()));
        if (exist) {
            throw new ServiceException("该版本下的BOM配置已存在，请检查型号、模块、板类型、原料组合是否重复");
        }
    }

    /**
     * 批量删除产品所需原材料明细
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 可以在这里添加删除前的业务校验
            // 例如：检查BOM是否被工单引用等
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取所有版本列表
     */
    @Override
    public List<String> getAllVersions() {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.select(ProductMaterialsBom::getVersion);
        lqw.groupBy(ProductMaterialsBom::getVersion);
        lqw.orderByDesc(ProductMaterialsBom::getVersion);
        List<ProductMaterialsBom> list = baseMapper.selectList(lqw);
        return list.stream()
            .map(ProductMaterialsBom::getVersion)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 根据型号、模块、板类型获取版本列表
     */
    @Override
    public List<String> getVersionsByGroup(String model, String module, String boardType) {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.select(ProductMaterialsBom::getVersion);
        lqw.eq(StringUtils.isNotBlank(model), ProductMaterialsBom::getModel, model);
        lqw.eq(StringUtils.isNotBlank(module), ProductMaterialsBom::getModule, module);
        lqw.eq(StringUtils.isNotBlank(boardType), ProductMaterialsBom::getBoardType, boardType);
        lqw.groupBy(ProductMaterialsBom::getVersion);
        lqw.orderByDesc(ProductMaterialsBom::getVersion);
        List<ProductMaterialsBom> list = baseMapper.selectList(lqw);
        return list.stream()
            .map(ProductMaterialsBom::getVersion)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 批量导入BOM数据
     */
    @Override
    public String importBomData(String filePath, String updatedBy) {
        // 这里可以集成ExcelToMySQL.java的逻辑
        // 或者调用外部的导入服务
        try {
            // 调用现有的ExcelToMySQL类进行导入
            // 可以将ExcelToMySQL改造为服务类，然后在这里调用
            return "BOM数据导入成功";
        } catch (Exception e) {
            throw new ServiceException("BOM数据导入失败：" + e.getMessage());
        }
    }

    /**
     * 批量处理导入的BOM数据（事务管理）
     * 这个方法在Service层中执行，确保事务管理正确生效
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportProcessResult processBomImportData(List<ProductMaterialsBomImportBo> importDataList,
                                                  boolean isUpdateSupport,
                                                  String versionStrategy,
                                                  String targetVersion) {
        ImportProcessResult result = new ImportProcessResult();

        if (importDataList == null || importDataList.isEmpty()) {
            result.appendFailureMsg("没有读取到任何导入数据");
            return result;
        }

        log.info("开始处理BOM导入数据，共{}条记录", importDataList.size());

        try {
            // 获取当前用户信息
            String currentUserName = getCurrentUserName();

            // 数据预处理和分组
            Map<String, List<MaterialDetail>> bomGroups = groupAndValidateImportData(importDataList, result);

            if (bomGroups.isEmpty()) {
                if (result.getFailureNum() == 0) {
                    result.appendFailureMsg("没有找到有效的BOM组数据，请检查Excel格式");
                }
                return result;
            }

            log.info("共找到{}个BOM组，开始处理...", bomGroups.size());

            // 按组处理BOM数据
            for (Map.Entry<String, List<MaterialDetail>> entry : bomGroups.entrySet()) {
                try {
                    processBomGroup(entry.getKey(), entry.getValue(), versionStrategy, targetVersion, currentUserName, result);
                } catch (Exception e) {
                    log.error("处理BOM组{}时发生错误", entry.getKey(), e);
                    String[] groupParts = entry.getKey().split("\\|\\|");
                    String model = groupParts.length > 0 ? groupParts[0] : "未知";
                    String module = groupParts.length > 1 ? groupParts[1] : "未知";
                    String boardType = groupParts.length > 2 ? groupParts[2] : "未知";

                    result.addFailure(entry.getValue().size());
                    result.appendFailureMsg(String.format("BOM组 [%s, %s, %s] 导入失败：%s",
                        model, module, boardType, e.getMessage()));
                }
            }

            log.info("BOM数据处理完成！成功: {}, 失败: {}, 新版本: {}, 跳过: {}",
                result.getSuccessNum(), result.getFailureNum(), result.getNewVersionsCreated(), result.getSkippedIdenticalGroups());

        } catch (Exception e) {
            log.error("BOM数据处理过程中发生异常", e);
            result.appendFailureMsg("处理过程中发生异常：" + e.getMessage());
            throw new ServiceException("BOM数据处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName() {
        try {
            return com.cpmes.common.helper.LoginHelper.getUsername();
        } catch (Exception e) {
            log.warn("获取当前用户失败，使用默认用户：{}", e.getMessage());
            return "系统导入";
        }
    }

    /**
     * 数据预处理和分组
     */
    private Map<String, List<MaterialDetail>> groupAndValidateImportData(List<ProductMaterialsBomImportBo> importDataList, ImportProcessResult result) {
        Map<String, List<MaterialDetail>> materialGroups = new HashMap<>();
        int processedCount = 0;
        int skippedCount = 0;

        for (ProductMaterialsBomImportBo importBo : importDataList) {
            try {
                // 基本字段验证
                if (StringUtils.isBlank(importBo.getModel())) {
                    skippedCount++;
                    log.debug("跳过空型号数据行");
                    continue;
                }

                String model = importBo.getModel().trim();
                String module = StringUtils.isNotBlank(importBo.getModule()) ? importBo.getModule().trim() : "标准款";
                String boardType = StringUtils.isNotBlank(importBo.getBoardType()) ? importBo.getBoardType().trim() : "单层板";
                String material = StringUtils.isNotBlank(importBo.getMaterial()) ? importBo.getMaterial().trim() : "";
                String materialCode = StringUtils.isNotBlank(importBo.getMaterialCode()) ? importBo.getMaterialCode().trim() : "";
                String productNumber = StringUtils.isNotBlank(importBo.getProductNumber()) ? importBo.getProductNumber().trim() : "";
                String unit = StringUtils.isNotBlank(importBo.getUnit()) ? importBo.getUnit().trim() : "";

                // 跳过引用记录和通用板块记录（这些需要单独处理或暂不支持）
                if (material.isEmpty()) {
                    skippedCount++;
                    log.debug("跳过空材料记录：model={}", model);
                    continue;
                }

                // 验证必需字段
                if (unit.isEmpty()) {
                    skippedCount++;
                    result.appendFailureMsg(String.format("型号[%s]的材料[%s]缺少单位信息", model, material));
                    continue;
                }

                // 处理物料编码逻辑（增强异常处理，包含单位和板型信息）
                if (StringUtils.isNotBlank(materialCode)) {
                    try {
                        log.debug("开始处理物料编码: {} -> {}, 单位: {}, 板型: {}", materialCode, material, unit, boardType);
                        // 使用包含单位和板型信息的新方法
                        boolean processResult = rawMaterialWarehouseService.processRawMaterialByCodeWithUnitAndBoardType(materialCode, material, unit, boardType);
                        if (!processResult) {
                            log.warn("处理物料编码[{}]返回失败", materialCode);
                            result.appendFailureMsg(String.format("处理物料编码[%s]失败", materialCode));
                        } else {
                            log.debug("物料编码[{}]处理成功，单位: {}, 板型: {}", materialCode, unit, boardType);
                        }
                    } catch (Exception e) {
                        log.error("处理物料编码[{}]时发生错误: {}", materialCode, e.getMessage(), e);
                        result.appendFailureMsg(String.format("处理物料编码[%s]失败：%s", materialCode, e.getMessage()));

                        // 检查是否是严重错误，如果是则抛出异常中断处理
                        if (e instanceof org.springframework.dao.DataIntegrityViolationException ||
                            e instanceof java.sql.SQLException ||
                            e.getMessage().contains("Connection") ||
                            e.getMessage().contains("timeout")) {
                            log.error("检测到严重数据库错误，中断处理: {}", e.getMessage());
                            throw new RuntimeException("数据库操作失败，中断导入：" + e.getMessage(), e);
                        }
                        // 其他错误继续处理，不中断
                    }
                }

                // 数量处理
                int quantity = parseQuantitySafe(importBo.getQuantity());
                if (quantity <= 0) {
                    skippedCount++;
                    result.appendFailureMsg(String.format("型号[%s]的材料[%s]数量无效：%s", model, material, importBo.getQuantity()));
                    continue;
                }

                // 创建分组
                String groupKey = model + "||" + module + "||" + boardType;
                materialGroups.computeIfAbsent(groupKey, k -> new ArrayList<>())
                    .add(new MaterialDetail(material, quantity, unit, materialCode, productNumber));

                processedCount++;
                log.debug("处理数据行: 型号={}, 模块={}, 板型={}, 原料={}, 数量={}, 单位={}, 物料编码={}, 成品编码={}",
                    model, module, boardType, material, quantity, unit, materialCode, productNumber);

            } catch (Exception e) {
                log.error("数据处理失败: {}", e.getMessage(), e);
                result.incrementFailure();
                result.appendFailureMsg("数据行处理失败：" + e.getMessage());
                skippedCount++;
            }
        }

        log.info("数据分组完成：总数据{}条，有效数据{}条，跳过{}条，分组{}个",
            importDataList.size(), processedCount, skippedCount, materialGroups.size());

        return materialGroups;
    }

    /**
     * 处理单个BOM组
     */
    private void processBomGroup(String groupKey, List<MaterialDetail> materials, String versionStrategy,
                               String targetVersion, String currentUserName, ImportProcessResult result) {

        String[] groupParts = groupKey.split("\\|\\|");
        String model = groupParts[0];
        String module = groupParts[1];
        String boardType = groupParts[2];

        log.info("处理BOM组: 型号={}, 模块={}, 板型={}, 材料数量={}", model, module, boardType, materials.size());

        // 对材料列表排序，确保比较的一致性
        Collections.sort(materials);

        // 获取数据库中已存在的版本
        Map<String, List<MaterialDetail>> dbVersionMaterials = fetchExistingVersionsFromDb(model, module, boardType);

        // 根据版本策略处理
        if ("custom".equals(versionStrategy) && StringUtils.isNotBlank(targetVersion)) {
            handleCustomVersionImport(model, module, boardType, materials, targetVersion, currentUserName, dbVersionMaterials, result);
        } else {
            handleAutoVersionImport(model, module, boardType, materials, currentUserName, dbVersionMaterials, result);
        }
    }

    /**
     * 处理自动版本导入
     */
    private void handleAutoVersionImport(String model, String module, String boardType, List<MaterialDetail> materials,
                                       String currentUserName, Map<String, List<MaterialDetail>> dbVersionMaterials, ImportProcessResult result) {

        // 检查是否存在相同配置
        for (Map.Entry<String, List<MaterialDetail>> dbEntry : dbVersionMaterials.entrySet()) {
            if (areMaterialListsIdentical(materials, dbEntry.getValue())) {
                log.info("BOM组 [{}, {}, {}] 与现有版本 '{}' 配置相同，跳过导入", model, module, boardType, dbEntry.getKey());
                result.incrementSkipped();
                result.addSuccess(materials.size());
                result.appendSuccessMsg(String.format("BOM组 [%s, %s, %s] 与现有版本 '%s' 相同，跳过导入",
                    model, module, boardType, dbEntry.getKey()));
                return;
            }
        }

        // 生成新版本号
        Version latestVersion = getLatestVersion(dbVersionMaterials.keySet());
        Version newVersion = Version.autoIncrement(latestVersion);

        log.info("为BOM组 [{}, {}, {}] 创建新版本: {} (前一版本: {})", model, module, boardType, newVersion.toString(),
            latestVersion != null ? latestVersion.toString() : "无");

        // 插入新版本的BOM数据
        insertNewBomVersion(model, module, boardType, materials, newVersion.toString(), currentUserName);
        result.incrementNewVersions();
        result.addSuccess(materials.size());
        result.appendSuccessMsg(String.format("BOM组 [%s, %s, %s] 导入成功，版本: %s，共 %d 种原材料",
            model, module, boardType, newVersion.toString(), materials.size()));
    }

    /**
     * 处理指定版本导入
     */
    private void handleCustomVersionImport(String model, String module, String boardType, List<MaterialDetail> materials,
                                         String targetVersion, String currentUserName, Map<String, List<MaterialDetail>> dbVersionMaterials, ImportProcessResult result) {

        boolean targetVersionExists = dbVersionMaterials.containsKey(targetVersion);

        if (targetVersionExists) {
            List<MaterialDetail> existingMaterials = dbVersionMaterials.get(targetVersion);

            // 检查是否完全相同
            if (areMaterialListsIdentical(materials, existingMaterials)) {
                log.info("BOM组 [{}, {}, {}] 与目标版本 '{}' 配置完全相同，跳过导入", model, module, boardType, targetVersion);
                result.incrementSkipped();
                result.addSuccess(materials.size());
                result.appendSuccessMsg(String.format("BOM组 [%s, %s, %s] 与目标版本 '%s' 配置相同，跳过导入",
                    model, module, boardType, targetVersion));
                return;
            }

            // 删除现有版本数据
            deleteVersionData(model, module, boardType, targetVersion);
            log.info("已删除BOM组 [{}, {}, {}] 版本 '{}' 的现有数据", model, module, boardType, targetVersion);
        }

        // 插入新数据
        insertNewBomVersion(model, module, boardType, materials, targetVersion, currentUserName);

        if (targetVersionExists) {
            result.addSuccess(materials.size());
            result.appendSuccessMsg(String.format("BOM组 [%s, %s, %s] 已覆盖版本: %s，共 %d 种原材料",
                model, module, boardType, targetVersion, materials.size()));
        } else {
            result.incrementNewVersions();
            result.addSuccess(materials.size());
            result.appendSuccessMsg(String.format("BOM组 [%s, %s, %s] 已创建新版本: %s，共 %d 种原材料",
                model, module, boardType, targetVersion, materials.size()));
        }
    }

    /**
     * 安全解析数量
     */
    private int parseQuantitySafe(String quantityStr) {
        if (StringUtils.isBlank(quantityStr)) {
            return 0;
        }
        try {
            return (int) Double.parseDouble(quantityStr.trim());
        } catch (NumberFormatException e) {
            log.warn("数量格式错误: '{}', 默认为0", quantityStr);
            return 0;
        }
    }

    /**
     * 从数据库获取现有版本信息
     */
    private Map<String, List<MaterialDetail>> fetchExistingVersionsFromDb(String model, String module, String boardType) {
        Map<String, List<MaterialDetail>> versions = new HashMap<>();

        try {
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getModel, model);
            lqw.eq(ProductMaterialsBom::getModule, module);
            lqw.eq(ProductMaterialsBom::getBoardType, boardType);
            lqw.orderBy(true, true, ProductMaterialsBom::getVersion);
            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            List<ProductMaterialsBomVo> existingBomVos = baseMapper.selectVoList(lqw);

            // 按版本分组
            for (ProductMaterialsBomVo vo : existingBomVos) {
                String version = vo.getVersion();
                String material = vo.getMaterial();
                Integer quantity = vo.getQuantity();
                String unit = vo.getUnit();
                String materialCode = vo.getReservedField3(); // 从预留字段3获取物料编码
                String productNumber = vo.getProductNumber(); // 获取成品编码

                // 确保数据完整性
                if (StringUtils.isBlank(version) || StringUtils.isBlank(material) ||
                    quantity == null || StringUtils.isBlank(unit)) {
                    continue;
                }

                versions.computeIfAbsent(version, k -> new ArrayList<>())
                    .add(new MaterialDetail(material, quantity, unit, materialCode, productNumber));
            }

            // 对每个版本的材料列表排序
            for (List<MaterialDetail> materialList : versions.values()) {
                Collections.sort(materialList);
            }

            log.debug("查询到现有版本: model={}, module={}, boardType={}, 版本数={}",
                model, module, boardType, versions.size());

        } catch (Exception e) {
            log.error("查询现有版本失败: model={}, module={}, boardType={}", model, module, boardType, e);
        }

        return versions;
    }

    /**
     * 比较两个材料列表是否相同
     */
    private boolean areMaterialListsIdentical(List<MaterialDetail> list1, List<MaterialDetail> list2) {
        if (list1.size() != list2.size()) {
            return false;
        }

        // 确保两个列表都已排序
        Collections.sort(list1);
        Collections.sort(list2);

        for (int i = 0; i < list1.size(); i++) {
            if (!list1.get(i).equals(list2.get(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取最新版本
     */
    private Version getLatestVersion(Set<String> versionStrings) {
        if (versionStrings == null || versionStrings.isEmpty()) {
            return null;
        }

        Version latestVersion = null;
        for (String versionStr : versionStrings) {
            try {
                Version version = new Version(versionStr);
                if (latestVersion == null || version.compareTo(latestVersion) > 0) {
                    latestVersion = version;
                }
            } catch (Exception e) {
                log.warn("无效的版本号格式: {}", versionStr);
            }
        }
        return latestVersion;
    }

    /**
     * 插入新BOM版本数据
     */
    private void insertNewBomVersion(String model, String module, String boardType,
                                   List<MaterialDetail> materials, String version, String updatedBy) {
        try {
            for (MaterialDetail material : materials) {
                ProductMaterialsBom entity = new ProductMaterialsBom();
                entity.setModel(model);
                entity.setModule(module);
                entity.setBoardType(boardType);
                entity.setMaterial(material.getMaterial());
                entity.setQuantity(material.getQuantity());
                entity.setUnit(material.getUnit());
                entity.setVersion(version);
                entity.setUpdatedBy(updatedBy);
                entity.setUpdatedAt(java.time.LocalDateTime.now().toString());
                entity.setDelFlag("0");

                // 保存物料编码到预留字段3
                if (StringUtils.isNotBlank(material.getMaterialCode())) {
                    entity.setReservedField3(material.getMaterialCode());
                }

                // 保存成品编码到productNumber字段
                if (StringUtils.isNotBlank(material.getProductNumber())) {
                    entity.setProductNumber(material.getProductNumber());
                }

                baseMapper.insert(entity);

                // 注释：移除成品编码同步功能
                // 原料表同步逻辑已移除，仅保留物料编码和物料名称的同步功能
                // 成品编码不再同步到原料仓库表中
            }

            log.info("成功插入BOM版本: model={}, module={}, boardType={}, version={}, 材料数量={}",
                model, module, boardType, version, materials.size());

        } catch (Exception e) {
            log.error("插入BOM版本失败: model={}, module={}, boardType={}, version={}",
                model, module, boardType, version, e);
            throw new ServiceException("插入BOM版本失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定版本的BOM数据
     */
    private void deleteVersionData(String model, String module, String boardType, String version) {
        try {
            LambdaQueryWrapper<ProductMaterialsBom> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.eq(ProductMaterialsBom::getModel, model);
            deleteWrapper.eq(ProductMaterialsBom::getModule, module);
            deleteWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
            deleteWrapper.eq(ProductMaterialsBom::getVersion, version);

            int deletedCount = baseMapper.delete(deleteWrapper);
            log.debug("删除BOM数据：model={}, module={}, boardType={}, version={}, 删除数量={}",
                model, module, boardType, version, deletedCount);

        } catch (Exception e) {
            log.error("删除版本数据失败: model={}, module={}, boardType={}, version={}",
                model, module, boardType, version, e);
            throw new ServiceException("删除版本数据失败: " + e.getMessage());
        }
    }

    /**
     * 材料详情类
     */
    private static class MaterialDetail implements Comparable<MaterialDetail> {
        private final String material;
        private final int quantity;
        private final String unit;
        private final String materialCode;
        private final String productNumber;  // 新增：成品编码字段

        public MaterialDetail(String material, int quantity, String unit) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = null;
            this.productNumber = null;  // 默认为null
        }

        public MaterialDetail(String material, int quantity, String unit, String materialCode) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = materialCode;
            this.productNumber = null;  // 默认为null
        }

        // 5参数构造函数，完整支持物料编码和成品编码
        public MaterialDetail(String material, int quantity, String unit, String materialCode, String productNumber) {
            this.material = material;
            this.quantity = quantity;
            this.unit = unit;
            this.materialCode = materialCode;
            this.productNumber = productNumber;
        }

        public String getMaterial() { return material; }
        public int getQuantity() { return quantity; }
        public String getUnit() { return unit; }
        public String getMaterialCode() { return materialCode; }
        public String getProductNumber() { return productNumber; }  // 新增：获取成品编码

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MaterialDetail that = (MaterialDetail) o;
            return quantity == that.quantity &&
                   Objects.equals(material, that.material) &&
                   Objects.equals(unit, that.unit) &&
                   Objects.equals(materialCode, that.materialCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(material, quantity, unit, materialCode);
        }

        @Override
        public int compareTo(MaterialDetail o) {
            int materialCompare = this.material.compareTo(o.material);
            if (materialCompare != 0) return materialCompare;
            int quantityCompare = Integer.compare(this.quantity, o.quantity);
            if (quantityCompare != 0) return quantityCompare;
            return this.unit.compareTo(o.unit);
        }

        @Override
        public String toString() {
            return String.format("MaterialDetail{material='%s', quantity=%d, unit='%s', materialCode='%s', productNumber='%s'}",
                material, quantity, unit, materialCode, productNumber);
        }
    }

    /**
     * 版本管理类
     */
    private static class Version implements Comparable<Version> {
        private final int major;
        private final int minor;

        public Version(String versionStr) {
            if (StringUtils.isBlank(versionStr)) {
                throw new IllegalArgumentException("版本号不能为空");
            }

            String[] parts = versionStr.trim().split("\\.");
            if (parts.length != 2) {
                throw new IllegalArgumentException("版本号格式应为 major.minor，如: 1.0");
            }

            try {
                this.major = Integer.parseInt(parts[0]);
                this.minor = Integer.parseInt(parts[1]);

                if (major < 0 || minor < 0) {
                    throw new IllegalArgumentException("版本号不能为负数");
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("版本号必须为数字格式，如: 1.0");
            }
        }

        public Version(int major, int minor) {
            if (major < 0 || minor < 0) {
                throw new IllegalArgumentException("版本号不能为负数");
            }
            this.major = major;
            this.minor = minor;
        }

        public static Version autoIncrement(Version latestVersion) {
            if (latestVersion == null) {
                return new Version(1, 0);
            }

            // 简单递增次版本号
            int newMinor = latestVersion.minor + 1;
            if (newMinor >= 100) {
                // 次版本号达到100时，主版本号递增，次版本号重置为0
                return new Version(latestVersion.major + 1, 0);
            } else {
                return new Version(latestVersion.major, newMinor);
            }
        }

        @Override
        public String toString() {
            return major + "." + minor;
        }

        @Override
        public int compareTo(Version other) {
            int majorCompare = Integer.compare(this.major, other.major);
            return majorCompare != 0 ? majorCompare : Integer.compare(this.minor, other.minor);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Version version = (Version) o;
            return major == version.major && minor == version.minor;
        }

        @Override
        public int hashCode() {
            return Objects.hash(major, minor);
        }
    }

    /**
     * 获取所有型号列表
     */
    @Override
    public List<String> getAllModels() {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.select(ProductMaterialsBom::getModel);
        lqw.groupBy(ProductMaterialsBom::getModel);
        lqw.orderBy(true, true, ProductMaterialsBom::getModel);
        List<ProductMaterialsBom> list = baseMapper.selectList(lqw);
        return list.stream()
            .map(ProductMaterialsBom::getModel)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 根据型号获取BOM列表（优化版本 - 添加缓存和限制）
     */
    @Override
    public List<ProductMaterialsBomVo> getBomByModel(String model) {
        return getBomByModel(model, false);
    }

    /**
     * 根据型号获取BOM列表（支持显示已删除记录）
     */
    @Override
    public List<ProductMaterialsBomVo> getBomByModel(String model, Boolean showDeleted) {
        if (StringUtils.isBlank(model)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getModel, model);

            // 如果不显示已删除记录，则只查询未删除的记录
            // MyBatis-Plus的@TableLogic会自动处理逻辑删除，但我们需要手动控制
            if (showDeleted != null && showDeleted) {
                // 显示所有记录（包括已删除的）
                lqw.in(ProductMaterialsBom::getDelFlag, "0", "2");
            } else {
                // 只显示未删除的记录（@TableLogic默认行为）
                lqw.eq(ProductMaterialsBom::getDelFlag, "0");
            }

            lqw.orderBy(true, true, ProductMaterialsBom::getModule);
            lqw.orderBy(true, true, ProductMaterialsBom::getBoardType);
            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            // 添加查询限制，避免数据量过大
            lqw.last("LIMIT 1000");

            List<ProductMaterialsBomVo> result = baseMapper.selectVoList(lqw);

            // 记录日志便于监控
            if (result.size() > 500) {
                log.warn("型号 {} 的BOM数据量较大: {} 条记录", model, result.size());
            }

            return result;

        } catch (Exception e) {
            log.error("查询型号 {} 的BOM数据失败: {}", model, e.getMessage(), e);
            // 返回空列表而不是抛出异常，提高系统稳定性
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有功能模块列表
     */
    @Override
    public List<String> getAllModules() {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.select(ProductMaterialsBom::getModule);
        lqw.isNotNull(ProductMaterialsBom::getModule);
        lqw.ne(ProductMaterialsBom::getModule, "");
        lqw.groupBy(ProductMaterialsBom::getModule);
        lqw.orderBy(true, true, ProductMaterialsBom::getModule);
        List<ProductMaterialsBom> list = baseMapper.selectList(lqw);
        return list.stream()
            .map(ProductMaterialsBom::getModule)
            .filter(Objects::nonNull)
            .filter(module -> !module.trim().isEmpty())
            .collect(Collectors.toList());
    }

    /**
     * 获取所有上下板类型列表
     */
    @Override
    public List<String> getAllBoardTypes() {
        LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
        lqw.select(ProductMaterialsBom::getBoardType);
        lqw.isNotNull(ProductMaterialsBom::getBoardType);
        lqw.ne(ProductMaterialsBom::getBoardType, "");
        lqw.groupBy(ProductMaterialsBom::getBoardType);
        lqw.orderBy(true, true, ProductMaterialsBom::getBoardType);
        List<ProductMaterialsBom> list = baseMapper.selectList(lqw);
        return list.stream()
            .map(ProductMaterialsBom::getBoardType)
            .filter(Objects::nonNull)
            .filter(boardType -> !boardType.trim().isEmpty())
            .collect(Collectors.toList());
    }

    // ============= 通用板块功能相关方法实现 =============

    /*
     * 注意：BOM引用功能已被废弃，以下方法已注释掉
     */

    /*
    /**
     * 获取指定型号可以引用的通用板块列表（动态查找）
     * @deprecated BOM引用功能已废弃
     */
    /*
    @Override
    public List<ProductMaterialsBomVo> getAvailableGenericBomList(String currentModel, String module, String boardType) {
        try {
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();

            // 排除当前型号
            if (StringUtils.isNotBlank(currentModel)) {
                lqw.ne(ProductMaterialsBom::getModel, currentModel);
            }

            // 模块和板类型条件（可选）
            if (StringUtils.isNotBlank(module)) {
                lqw.eq(ProductMaterialsBom::getModule, module);
            }
            if (StringUtils.isNotBlank(boardType)) {
                lqw.eq(ProductMaterialsBom::getBoardType, boardType);
            }

            // 只查询有物料清单的记录（非通用板块）
            lqw.isNotNull(ProductMaterialsBom::getMaterial);
            lqw.ne(ProductMaterialsBom::getMaterial, "");

            // 排除逻辑删除的记录
            lqw.eq(ProductMaterialsBom::getDelFlag, "0");

            lqw.orderBy(true, true, ProductMaterialsBom::getModel);
            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            List<ProductMaterialsBomVo> result = baseMapper.selectVoList(lqw);

            log.info("找到可引用的BOM清单数量: {}", result.size());

            return result;

        } catch (Exception e) {
            log.error("获取可引用BOM列表失败: module={}, boardType={}, error={}", module, boardType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    */

    /*
    /**
     * 获取指定条件的材料清单（用于动态展示引用内容）
     * @deprecated BOM引用功能已废弃
     */
    /*
    @Override
    public List<ProductMaterialsBomVo> getMaterialListForReference(String referencedModel, String module, String boardType) {
        if (StringUtils.isBlank(referencedModel)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getModel, referencedModel);

            // 模块和板类型条件（可选）
            if (StringUtils.isNotBlank(module)) {
                lqw.eq(ProductMaterialsBom::getModule, module);
            }
            if (StringUtils.isNotBlank(boardType)) {
                lqw.eq(ProductMaterialsBom::getBoardType, boardType);
            }

            // 只查询有物料清单的记录
            lqw.isNotNull(ProductMaterialsBom::getMaterial);
            lqw.ne(ProductMaterialsBom::getMaterial, "");
            lqw.eq(ProductMaterialsBom::getDelFlag, "0");

            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            List<ProductMaterialsBomVo> result = baseMapper.selectVoList(lqw);

            log.info("获取引用材料清单: 型号={}, 模块={}, 板类型={}, 记录数={}",
                    referencedModel, module, boardType, result.size());

            return result;

        } catch (Exception e) {
            log.error("获取引用材料清单失败: 型号={}, 模块={}, 板类型={}, error={}",
                    referencedModel, module, boardType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    */

    /**
     * 创建通用板块记录（material为null的记录）
     */
    @Override
    public Boolean createGenericBomRecord(String model, String module, String boardType, String updatedBy) {
        if (StringUtils.isBlank(model)) {
            throw new ServiceException("创建通用板块记录时，型号不能为空");
        }

        try {
            // 检查是否已存在相同的通用板块记录
            LambdaQueryWrapper<ProductMaterialsBom> checkLqw = Wrappers.lambdaQuery();
            checkLqw.eq(ProductMaterialsBom::getModel, model);

            // 模块和板类型条件（如果提供）
            if (StringUtils.isNotBlank(module)) {
                checkLqw.eq(ProductMaterialsBom::getModule, module);
            } else {
                checkLqw.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getModule).or().eq(ProductMaterialsBom::getModule, ""));
            }

            if (StringUtils.isNotBlank(boardType)) {
                checkLqw.eq(ProductMaterialsBom::getBoardType, boardType);
            } else {
                checkLqw.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getBoardType).or().eq(ProductMaterialsBom::getBoardType, ""));
            }

            // 查找通用板块记录（material为null）
            checkLqw.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getMaterial).or().eq(ProductMaterialsBom::getMaterial, ""));
            checkLqw.eq(ProductMaterialsBom::getDelFlag, "0");

            ProductMaterialsBom existingRecord = baseMapper.selectOne(checkLqw);

            if (existingRecord != null) {
                log.info("通用板块记录已存在，跳过创建: model={}, module={}, boardType={}",
                        model, module, boardType);
                return true; // 已存在记录，算作成功
            }

            // 创建通用板块记录
            ProductMaterialsBom genericRecord = new ProductMaterialsBom();
            genericRecord.setModel(model);
            genericRecord.setModule(StringUtils.isNotBlank(module) ? module : "默认模块");
            genericRecord.setBoardType(StringUtils.isNotBlank(boardType) ? boardType : "通用板类型");
            genericRecord.setMaterial(null); // material为null表示这是通用板块
            genericRecord.setQuantity(0); // 通用板块的数量为0
            genericRecord.setUnit("通用");
            genericRecord.setVersion("1.0"); // 默认版本
            genericRecord.setUpdatedBy(updatedBy);
            genericRecord.setDelFlag("0");
            genericRecord.setRemark(String.format("通用板块：%s，可被其他型号动态引用", model));

            fillCustomFields(genericRecord, true);

            boolean result = baseMapper.insert(genericRecord) > 0;

            if (result) {
                log.info("成功创建通用板块记录: model={}, module={}, boardType={}",
                        model, module, boardType);
            }

            return result;

        } catch (Exception e) {
            log.error("创建通用板块记录失败: model={}, module={}, boardType={}, error={}",
                    model, module, boardType, e.getMessage(), e);
            throw new ServiceException("创建通用板块记录失败：" + e.getMessage());
        }
    }



    // ============= 通用映射功能相关方法实现 =============

    /**
     * 根据通用ID获取映射列表
     */
    @Override
    public List<GenericBomMappingVo> getGenericMappingList(String genericId) {
        if (StringUtils.isBlank(genericId)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<GenericBomMapping> lqw = Wrappers.lambdaQuery();
            lqw.eq(GenericBomMapping::getGenericId, genericId);
            lqw.eq(GenericBomMapping::getDelFlag, "0");
            lqw.orderByAsc(GenericBomMapping::getSortOrder);
            lqw.orderByDesc(GenericBomMapping::getCreateTime);

            List<GenericBomMapping> mappings = genericBomMappingMapper.selectList(lqw);
            return BeanUtil.copyToList(mappings, GenericBomMappingVo.class);

        } catch (Exception e) {
            log.error("获取通用映射列表失败: genericId={}, error={}", genericId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取通用ID的默认映射
     */
    @Override
    public GenericBomMappingVo getDefaultGenericMapping(String genericId) {
        if (StringUtils.isBlank(genericId)) {
            return null;
        }

        try {
            // 先查找标记为默认的映射
            LambdaQueryWrapper<GenericBomMapping> defaultLqw = Wrappers.lambdaQuery();
            defaultLqw.eq(GenericBomMapping::getGenericId, genericId);
            defaultLqw.eq(GenericBomMapping::getIsDefault, "1");
            defaultLqw.eq(GenericBomMapping::getDelFlag, "0");
            defaultLqw.orderByAsc(GenericBomMapping::getSortOrder);

            GenericBomMapping defaultMapping = genericBomMappingMapper.selectOne(defaultLqw);

            if (defaultMapping != null) {
                return BeanUtil.toBean(defaultMapping, GenericBomMappingVo.class);
            }

            // 如果没有默认映射，返回第一个映射
            LambdaQueryWrapper<GenericBomMapping> firstLqw = Wrappers.lambdaQuery();
            firstLqw.eq(GenericBomMapping::getGenericId, genericId);
            firstLqw.eq(GenericBomMapping::getDelFlag, "0");
            firstLqw.orderByAsc(GenericBomMapping::getSortOrder);
            firstLqw.last("LIMIT 1");

            GenericBomMapping firstMapping = genericBomMappingMapper.selectOne(firstLqw);
            return BeanUtil.toBean(firstMapping, GenericBomMappingVo.class);

        } catch (Exception e) {
            log.error("获取默认通用映射失败: genericId={}, error={}", genericId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据映射信息获取具体的材料清单
     */
    @Override
    public List<ProductMaterialsBomVo> getMaterialListByMapping(String mappedModel, String mappedModule, String mappedBoardType) {
        if (StringUtils.isAnyBlank(mappedModel, mappedModule, mappedBoardType)) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getModel, mappedModel);
            lqw.eq(ProductMaterialsBom::getModule, mappedModule);
            lqw.eq(ProductMaterialsBom::getBoardType, mappedBoardType);

            // 只查询有具体物料的记录（排除通用板块记录）
            lqw.isNotNull(ProductMaterialsBom::getMaterial);
            lqw.ne(ProductMaterialsBom::getMaterial, "");

            lqw.eq(ProductMaterialsBom::getDelFlag, "0");
            lqw.orderBy(true, true, ProductMaterialsBom::getMaterial);

            List<ProductMaterialsBomVo> result = baseMapper.selectVoList(lqw);

            log.info("根据映射获取材料清单: 型号={}, 模块={}, 板类型={}, 记录数={}",
                    mappedModel, mappedModule, mappedBoardType, result.size());

            return result;

        } catch (Exception e) {
            log.error("根据映射获取材料清单失败: 型号={}, 模块={}, 板类型={}, error={}",
                    mappedModel, mappedModule, mappedBoardType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建通用板块记录（使用预留字段）
     */
    @Override
    public Boolean createGenericBomWithMapping(String model, String module, String boardType,
                                             String genericId, String genericName, String updatedBy) {
        try {
            // 创建通用BOM记录
            boolean bomCreated = createGenericBomRecord(model, module, boardType, updatedBy);
            if (!bomCreated) {
                throw new ServiceException("创建通用BOM记录失败");
            }

            // 创建映射关系记录
            GenericBomMapping mapping = new GenericBomMapping();
            mapping.setGenericId(genericId);
            mapping.setMappedModel(model);
            mapping.setMappedModule(module);
            mapping.setMappedBoardType(boardType);
                         mapping.setGenericName(genericName);
            mapping.setIsDefault("1"); // 设为默认映射
            mapping.setCreateBy(updatedBy);
            mapping.setUpdateBy(updatedBy);

            int insertResult = genericBomMappingMapper.insert(mapping);
            if (insertResult <= 0) {
                throw new ServiceException("创建映射关系失败");
            }

            log.info("成功创建通用BOM及映射: 通用ID={}, 映射={}|{}|{}",
                genericId, model, module, boardType);
            return true;

        } catch (Exception e) {
            log.error("创建通用BOM及映射失败", e);
            throw new ServiceException("创建通用BOM及映射失败：" + e.getMessage());
        }
    }

    /**
     * 根据型号和样式名称获取BOM清单
     */
    @Override
    public List<ProductMaterialsBomVo> getBomByModelAndStyle(String model, String styleName) {
        // 1. 查询 BOM 数据（MySQL）
        LambdaQueryWrapper<ProductMaterialsBom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductMaterialsBom::getModel, model);
        wrapper.eq(ProductMaterialsBom::getModule, styleName);

        List<ProductMaterialsBom> bomList = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(bomList)) {
            return Collections.emptyList();
        }

        // 2. 提取 reserved_field3（对应库存的 fields3）
        List<String> reservedField3List = bomList.stream()
            // 获取每个 BOM 的 reservedField3
            .map(ProductMaterialsBom::getReservedField3)
            // 过滤空字符串和 null（Apache 工具）
            .filter(StringUtils::isNotBlank)
            // 去除前后空格
            .map(String::trim)
            // 去重，避免多次查询相同库存
            .distinct()
            // 转为 List
            .collect(Collectors.toList());

        // 3. 查询库存数据（PostgreSQL）
        List<RawMaterialWarehouse> stockList = CollectionUtils.isEmpty(reservedField3List)
            // 如果没有任何原料编码，返回空库存列表
            ? new ArrayList<>()
            : rawMaterialWarehouseService.list(
            // 否则根据 fields3 批量查询库存数据
            new LambdaQueryWrapper<RawMaterialWarehouse>()
                .in(RawMaterialWarehouse::getFields3, reservedField3List)
        );

        // 4. 将库存列表转为 Map：key 为 fields3，value 为对应 RawMaterialWarehouse 对象
        // 用于后续快速匹配 BOM 与库存信息
        Map<String, RawMaterialWarehouse> stockMap = stockList.stream()
            .collect(Collectors.toMap(
                // key：原料编码
                RawMaterialWarehouse::getFields3,
                // value：RawMaterialWarehouse 实体
                Function.identity(),
                // 如果有重复 key，取第一个（避免异常）
                (a, b) -> a
            ));

        //  5. 遍历 BOM 列表，将其转为 VO 对象并合并库存信息
        List<ProductMaterialsBomVo> voList = bomList.stream().map(bom -> {
            ProductMaterialsBomVo vo = new ProductMaterialsBomVo();
            // 将 BOM 的属性复制到 VO 中（忽略 null 安全的字段）
            BeanUtils.copyProperties(bom, vo);
            // 尝试从库存映射中获取与当前 BOM 匹配的库存信息
            RawMaterialWarehouse stock = stockMap.get(bom.getReservedField3());
            if (stock != null) {
                vo.setCurrentStock(stock.getCurrentStock());
                vo.setMinStockQuantity(stock.getMinStockQuantity()); // 如果 VO 中定义了
            } else {
                vo.setCurrentStock(0); // 或 null，视需求而定
            }
            return vo;
        }).collect(Collectors.toList());

        return voList;
    }

    /**
     * 批量修改型号和产品编码（支持按款式分别设置编码）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateModel(List<Long> ids, String targetModel, String productNumber, String updatedBy) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("更新记录ID列表不能为空");
        }
        if (StringUtils.isBlank(targetModel)) {
            throw new ServiceException("目标型号不能为空");
        }

        log.info("开始批量修改型号: 记录数={}, 目标型号={}, 产品编码={}, 操作人={}",
            ids.size(), targetModel, productNumber, updatedBy);

        try {
            // 构建更新条件
            LambdaUpdateWrapper<ProductMaterialsBom> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(ProductMaterialsBom::getId, ids)
                        .set(ProductMaterialsBom::getModel, targetModel)
                        .set(ProductMaterialsBom::getUpdatedBy, updatedBy)
                        .set(ProductMaterialsBom::getUpdatedAt, new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            // 如果提供了产品编码，则同时更新
            if (StringUtils.isNotBlank(productNumber)) {
                updateWrapper.set(ProductMaterialsBom::getProductNumber, productNumber);
                log.info("同时更新产品编码: {}", productNumber);
            }

            // 执行批量更新
            int updateCount = baseMapper.update(null, updateWrapper);

            if (updateCount > 0) {
                log.info("批量修改型号成功: 实际更新记录数={}", updateCount);
                return true;
            } else {
                log.warn("批量修改型号失败: 没有记录被更新");
                return false;
            }
        } catch (Exception e) {
            log.error("批量修改型号时发生异常: {}", e.getMessage(), e);
            throw new ServiceException("批量修改型号失败：" + e.getMessage());
        }
    }

    /**
     * 批量修改型号和产品编码（支持按款式分别设置编码）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateModelWithStyle(List<Long> ids, String targetModel,
                                           java.util.Map<String, String> styleCodeMapping, String updatedBy) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("更新记录ID列表不能为空");
        }
        if (StringUtils.isBlank(targetModel)) {
            throw new ServiceException("目标型号不能为空");
        }

        log.info("开始按款式批量修改型号: 记录数={}, 目标型号={}, 款式编码映射={}, 操作人={}",
            ids.size(), targetModel, styleCodeMapping, updatedBy);

        try {
            // 先查询出所有要更新的记录，获取它们的款式信息
            List<ProductMaterialsBomVo> bomRecords = baseMapper.selectVoListWithStyle(
                Wrappers.<ProductMaterialsBom>lambdaQuery().in(ProductMaterialsBom::getId, ids)
            );

            if (bomRecords.isEmpty()) {
                log.warn("没有找到要更新的BOM记录");
                return false;
            }

            int totalUpdateCount = 0;
            String currentTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            // 按款式分组处理
            java.util.Map<String, List<ProductMaterialsBomVo>> styleGroups = bomRecords.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    record -> record.getStyleName() != null ? record.getStyleName() : "默认款式"
                ));

            for (java.util.Map.Entry<String, List<ProductMaterialsBomVo>> entry : styleGroups.entrySet()) {
                String styleName = entry.getKey();
                List<ProductMaterialsBomVo> styleRecords = entry.getValue();

                // 获取该款式对应的产品编码
                String productNumber = styleCodeMapping != null ? styleCodeMapping.get(styleName) : null;

                // 提取该款式下所有记录的ID
                List<Long> styleIds = styleRecords.stream()
                    .map(ProductMaterialsBomVo::getId)
                    .collect(java.util.stream.Collectors.toList());

                // 构建更新条件
                LambdaUpdateWrapper<ProductMaterialsBom> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(ProductMaterialsBom::getId, styleIds)
                            .set(ProductMaterialsBom::getModel, targetModel)
                            .set(ProductMaterialsBom::getUpdatedBy, updatedBy)
                            .set(ProductMaterialsBom::getUpdatedAt, currentTime);

                // 如果该款式有对应的产品编码，则更新
                if (StringUtils.isNotBlank(productNumber)) {
                    updateWrapper.set(ProductMaterialsBom::getProductNumber, productNumber);
                    log.info("款式[{}]更新产品编码: {}", styleName, productNumber);
                } else {
                    log.info("款式[{}]未提供产品编码，仅更新型号", styleName);
                }

                // 执行更新
                int updateCount = baseMapper.update(null, updateWrapper);
                totalUpdateCount += updateCount;

                log.info("款式[{}]更新完成: 记录数={}", styleName, updateCount);
            }

            if (totalUpdateCount > 0) {
                log.info("按款式批量修改型号成功: 总更新记录数={}", totalUpdateCount);
                return true;
            } else {
                log.warn("按款式批量修改型号失败: 没有记录被更新");
                return false;
            }

        } catch (Exception e) {
            log.error("按款式批量修改型号时发生异常: {}", e.getMessage(), e);
            throw new ServiceException("按款式批量修改型号失败: " + e.getMessage());
        }
    }

    // ============= 通用设置功能相关方法 =============

    /**
     * 检查原料是否可以设置为通用（按原料名称）
     * @deprecated 使用 checkGenericEligibilityByGroup 替代
     */
    @Override
    @Deprecated
    public Map<String, Object> checkGenericEligibility(String materialName) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询该原料在BOM表中的所有记录
            LambdaQueryWrapper<ProductMaterialsBom> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductMaterialsBom::getMaterial, materialName);
            lqw.eq(ProductMaterialsBom::getDelFlag, "0");
            List<ProductMaterialsBom> bomRecords = baseMapper.selectList(lqw);

            if (bomRecords.isEmpty()) {
                result.put("eligible", false);
                result.put("reason", "该原料在BOM清单中不存在");
                return result;
            }

            // 按板型分组统计
            Map<String, List<ProductMaterialsBom>> boardTypeGroups = bomRecords.stream()
                .collect(Collectors.groupingBy(
                    record -> StringUtils.isNotBlank(record.getBoardType()) ? record.getBoardType() : "未知板型"
                ));

            // 检查是否有任意板型只存在一条记录
            List<String> eligibleBoardTypes = new ArrayList<>();
            boolean hasEligibleBoardType = false;

            for (Map.Entry<String, List<ProductMaterialsBom>> entry : boardTypeGroups.entrySet()) {
                String boardType = entry.getKey();
                List<ProductMaterialsBom> records = entry.getValue();

                // 跳过已经是"通用"的板型
                if ("通用".equals(boardType)) {
                    continue;
                }

                // 如果该板型只有一条记录，则符合通用设置条件
                if (records.size() == 1) {
                    eligibleBoardTypes.add(boardType);
                    hasEligibleBoardType = true;
                }
            }

            result.put("eligible", hasEligibleBoardType);
            result.put("eligibleBoardTypes", eligibleBoardTypes);

            if (hasEligibleBoardType) {
                result.put("reason", String.format("原料 '%s' 在板型 %s 中只有一条记录，可以设置为通用",
                    materialName, String.join("、", eligibleBoardTypes)));
            } else {
                result.put("reason", "该原料在所有板型中都有多条记录，无法设置为通用");
            }

        } catch (Exception e) {
            log.error("检查原料通用设置资格时发生错误: {}", e.getMessage(), e);
            result.put("eligible", false);
            result.put("reason", "检查过程中发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查BOM记录是否可以设置为通用（按分组）
     */
    @Override
    public Map<String, Object> checkGenericEligibilityByGroup(String model, String module, String boardType) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 基本参数校验
            if (StringUtils.isBlank(model) || StringUtils.isBlank(boardType)) {
                result.put("eligible", false);
                result.put("reason", "产品型号和板型不能为空");
                return result;
            }

            // 检查是否已经设置为通用（通过reserved_field_1字段）
            LambdaQueryWrapper<ProductMaterialsBom> genericCheckWrapper = Wrappers.lambdaQuery();
            genericCheckWrapper.eq(ProductMaterialsBom::getModel, model);
            if (StringUtils.isNotBlank(module)) {
                genericCheckWrapper.eq(ProductMaterialsBom::getModule, module);
            } else {
                genericCheckWrapper.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getModule).or().eq(ProductMaterialsBom::getModule, ""));
            }
            genericCheckWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
            genericCheckWrapper.eq(ProductMaterialsBom::getReservedField1, "GENERIC");
            genericCheckWrapper.eq(ProductMaterialsBom::getDelFlag, "0");

            Long genericCount = baseMapper.selectCount(genericCheckWrapper);
            if (genericCount > 0) {
                result.put("eligible", false);
                result.put("reason", "该记录已经设置为通用类型");
                return result;
            }

            // 使用COUNT查询优化性能，避免查询所有记录
            LambdaQueryWrapper<ProductMaterialsBom> countWrapper = Wrappers.lambdaQuery();
            countWrapper.eq(ProductMaterialsBom::getModel, model);
            if (StringUtils.isNotBlank(module)) {
                countWrapper.eq(ProductMaterialsBom::getModule, module);
            } else {
                countWrapper.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getModule).or().eq(ProductMaterialsBom::getModule, ""));
            }
            countWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
            countWrapper.eq(ProductMaterialsBom::getDelFlag, "0");
            countWrapper.isNotNull(ProductMaterialsBom::getMaterial);
            countWrapper.ne(ProductMaterialsBom::getMaterial, "");

            Long recordCount = baseMapper.selectCount(countWrapper);

            // 检查记录数量是否为1
            boolean eligible = recordCount == 1;
            result.put("eligible", eligible);

            if (eligible) {
                // 只有在符合条件时才查询具体记录获取材料名称
                LambdaQueryWrapper<ProductMaterialsBom> detailWrapper = Wrappers.lambdaQuery();
                detailWrapper.eq(ProductMaterialsBom::getModel, model);
                if (StringUtils.isNotBlank(module)) {
                    detailWrapper.eq(ProductMaterialsBom::getModule, module);
                } else {
                    detailWrapper.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getModule).or().eq(ProductMaterialsBom::getModule, ""));
                }
                detailWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
                detailWrapper.eq(ProductMaterialsBom::getDelFlag, "0");
                detailWrapper.isNotNull(ProductMaterialsBom::getMaterial);
                detailWrapper.ne(ProductMaterialsBom::getMaterial, "");
                detailWrapper.last("LIMIT 1"); // 限制只查询一条记录

                ProductMaterialsBom record = baseMapper.selectOne(detailWrapper);
                if (record != null) {
                    result.put("reason", String.format("产品 '%s' 款式 '%s' 板型 '%s' 下只有一条原料记录 '%s'，可以设置为通用",
                        model, StringUtils.isNotBlank(module) ? module : "默认", boardType, record.getMaterial()));
                    result.put("materialName", record.getMaterial());
                } else {
                    result.put("reason", "数据异常：计数为1但未找到记录");
                }
            } else if (recordCount == 0) {
                result.put("reason", String.format("产品 '%s' 款式 '%s' 板型 '%s' 下没有原料记录",
                    model, StringUtils.isNotBlank(module) ? module : "默认", boardType));
            } else {
                result.put("reason", String.format("产品 '%s' 款式 '%s' 板型 '%s' 下有 %d 条原料记录，无法设置为通用",
                    model, StringUtils.isNotBlank(module) ? module : "默认", boardType, recordCount));
            }

        } catch (Exception e) {
            log.error("检查BOM记录通用设置资格时发生错误: {}", e.getMessage(), e);
            result.put("eligible", false);
            result.put("reason", "检查过程中发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 设置原料为通用类型（按原料名称）
     * @deprecated 使用 setMaterialAsGenericByGroup 替代
     */
    @Override
    @Deprecated
    public String setMaterialAsGeneric(String materialName, String updatedBy) {
        try {
            // 先检查是否符合设置条件
            Map<String, Object> eligibilityCheck = checkGenericEligibility(materialName);
            Boolean eligible = (Boolean) eligibilityCheck.get("eligible");

            if (!eligible) {
                String reason = (String) eligibilityCheck.get("reason");
                return "设置失败：" + reason;
            }

            // 获取符合条件的板型列表
            @SuppressWarnings("unchecked")
            List<String> eligibleBoardTypes = (List<String>) eligibilityCheck.get("eligibleBoardTypes");

            int updatedCount = 0;

            // 更新BOM表中符合条件的记录
            for (String boardType : eligibleBoardTypes) {
                LambdaUpdateWrapper<ProductMaterialsBom> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(ProductMaterialsBom::getMaterial, materialName);
                updateWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
                updateWrapper.eq(ProductMaterialsBom::getDelFlag, "0");
                updateWrapper.set(ProductMaterialsBom::getBoardType, "通用");
                updateWrapper.set(ProductMaterialsBom::getUpdatedBy, updatedBy);
                updateWrapper.set(ProductMaterialsBom::getUpdatedAt, new Date());

                int count = baseMapper.update(null, updateWrapper);
                updatedCount += count;
            }

            return String.format("设置成功：已将原料 '%s' 在板型 %s 中的 %d 条记录设置为通用",
                materialName, String.join("、", eligibleBoardTypes), updatedCount);

        } catch (Exception e) {
            log.error("设置原料为通用类型时发生错误: {}", e.getMessage(), e);
            return "设置失败：" + e.getMessage();
        }
    }

    /**
     * 设置BOM记录为通用类型（按分组）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String setMaterialAsGenericByGroup(String model, String module, String boardType, String updatedBy) {
        try {
            // 先检查是否符合设置条件
            Map<String, Object> eligibilityCheck = checkGenericEligibilityByGroup(model, module, boardType);
            Boolean eligible = (Boolean) eligibilityCheck.get("eligible");

            if (!eligible) {
                String reason = (String) eligibilityCheck.get("reason");
                return "设置失败：" + reason;
            }

            // 更新BOM表中指定产品型号、款式和板型的记录，设置通用标识
            // 注意：保持原有的board_type不变，只设置reserved_field_1为"GENERIC"
            LambdaUpdateWrapper<ProductMaterialsBom> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(ProductMaterialsBom::getModel, model);
            if (StringUtils.isNotBlank(module)) {
                updateWrapper.eq(ProductMaterialsBom::getModule, module);
            } else {
                updateWrapper.and(wrapper -> wrapper.isNull(ProductMaterialsBom::getModule).or().eq(ProductMaterialsBom::getModule, ""));
            }
            updateWrapper.eq(ProductMaterialsBom::getBoardType, boardType);
            updateWrapper.eq(ProductMaterialsBom::getDelFlag, "0");
            // 关键修改：设置reserved_field_1为"GENERIC"，保持board_type不变
            updateWrapper.set(ProductMaterialsBom::getReservedField1, "GENERIC");
            updateWrapper.set(ProductMaterialsBom::getUpdatedBy, updatedBy);
            updateWrapper.set(ProductMaterialsBom::getUpdatedAt, new Date());

            int updatedCount = baseMapper.update(null, updateWrapper);

            if (updatedCount > 0) {
                String materialName = (String) eligibilityCheck.get("materialName");
                return String.format("设置成功：已将产品 '%s' 款式 '%s' 板型 '%s' 下的原料 '%s' 设置为通用（保持板型 '%s' 不变）",
                    model, StringUtils.isNotBlank(module) ? module : "默认", boardType, materialName, boardType);
            } else {
                return "设置失败：没有找到符合条件的记录";
            }

        } catch (Exception e) {
            log.error("设置BOM记录为通用类型时发生错误: {}", e.getMessage(), e);
            throw new ServiceException("设置失败：" + e.getMessage());
        }
    }

}

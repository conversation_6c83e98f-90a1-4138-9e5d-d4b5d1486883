package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.cpmes.common.core.domain.BaseEntity;
import com.cpmes.common.core.domain.entity.SysUser;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cpmes.system.domain.bo.SeriesProductsBo;
import com.cpmes.system.domain.vo.SeriesProductsVo;
import com.cpmes.system.domain.SeriesProducts;
import com.cpmes.system.mapper.SeriesProductsMapper;
import com.cpmes.system.service.ISeriesProductsService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系列产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class SeriesProductsServiceImpl implements ISeriesProductsService {

    private final SeriesProductsMapper baseMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 查询系列产品
     */
    @Override
    @DS("slave")
    public SeriesProductsVo queryById(Integer id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询系列产品列表
     */
    @Override
    @DS("slave")
    public TableDataInfo<SeriesProductsVo> queryPageList(SeriesProductsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SeriesProducts> lqw = buildQueryWrapper(bo);
        Page<SeriesProductsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }
    
    /**
     * 查询用户信息（使用主库）
     */
    @DS("master")
    private List<SysUser> getUsersByUsernames(List<String> usernames) {
        if (ObjectUtil.isEmpty(usernames)) {
            return new ArrayList<>();
        }
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, usernames));
    }

    /**
     * 查询系列产品列表
     */
    @Override
    @DS("slave")
    public List<SeriesProductsVo> queryList(SeriesProductsBo bo) {
        LambdaQueryWrapper<SeriesProducts> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SeriesProducts> buildQueryWrapper(SeriesProductsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SeriesProducts> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSeriesName()), SeriesProducts::getSeriesName, bo.getSeriesName());
        lqw.eq(StringUtils.isNotBlank(bo.getField1()), SeriesProducts::getField1, bo.getField1());
        lqw.eq(StringUtils.isNotBlank(bo.getField2()), SeriesProducts::getField2, bo.getField2());
        lqw.eq(StringUtils.isNotBlank(bo.getField3()), SeriesProducts::getField3, bo.getField3());
        lqw.between(bo.getParams().get("startTime") != null && bo.getParams().get("endTime") != null, SeriesProducts::getCreateTime, bo.getParams().get("startTime"), bo.getParams().get("endTime"));
        return lqw;
    }

    /**
     * 新增系列产品
     */
    @Override
    @DS("slave")
    public Boolean insertByBo(SeriesProductsBo bo) {
        SeriesProducts add = BeanUtil.toBean(bo, SeriesProducts.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改系列产品
     */
    @Override
    @DS("slave")
    public Boolean updateByBo(SeriesProductsBo bo) {
        SeriesProducts update = BeanUtil.toBean(bo, SeriesProducts.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SeriesProducts entity) {
        // 校验系列名称唯一性
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SeriesProducts>().eq(SeriesProducts::getSeriesName, entity.getSeriesName())
            .ne(ObjectUtil.isNotNull(entity.getId()), SeriesProducts::getId, entity.getId()));
        if (exist) {
            throw new ServiceException("系列名称已存在");
        }
    }

    /**
     * 批量删除系列产品
     */
    @Override
    @DS("slave")
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
} 
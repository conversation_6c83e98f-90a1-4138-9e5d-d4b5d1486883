package com.cpmes.system.service;

import com.cpmes.common.utils.poi.ExcelUtil;
import com.cpmes.system.domain.Sheet;
import com.cpmes.system.domain.vo.SheetVo;
import com.cpmes.system.domain.bo.SheetBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单Service接口
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
public interface ISheetService {

    /**
     * 查询工单
     */
    SheetVo queryById(Long sheetId);

    /**
     * 查询工单(移动端)
     */
    SheetVo queryByIdMobile(Long sheetId);

    /**
     * 查询工单
     */
    SheetVo queryBySheetNumber(String SheetNumber);

    /**
     * 查询工单列表
     */
    TableDataInfo<SheetVo> queryPageList(SheetBo bo, PageQuery pageQuery);

    /**
     * 查询工单列表(移动端模糊搜索)
     */
    List<SheetVo> searchList(SheetBo bo);

    /**
     * 查询工单列表
     */
    List<SheetVo> queryList(SheetBo bo);

    /**
     * 新增工单
     */
    Boolean insertByBo(SheetBo bo);

    /**
     * 修改工单
     */
    Boolean updateByBo(SheetBo bo);

    /**
     * 校验并批量删除工单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 导出工单流转卡
     */
    void exportSheetCard(Long sheetId, HttpServletResponse response);
}

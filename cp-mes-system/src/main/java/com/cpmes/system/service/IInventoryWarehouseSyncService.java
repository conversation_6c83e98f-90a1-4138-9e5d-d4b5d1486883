package com.cpmes.system.service;

import com.cpmes.system.domain.InventoryDetail;
import com.cpmes.system.domain.Material;

import java.util.List;

/**
 * 库存仓储同步服务接口
 * 
 * 提供库存管理与仓储管理系统之间的数据同步功能
 * 支持直接主键关联的同步策略
 * 
 * <AUTHOR>
 * @date 2024-12-22
 */
public interface IInventoryWarehouseSyncService {

    /**
     * 同步库存明细操作到仓储管理表
     * 
     * @param inventoryDetail 库存明细信息
     * @param operation 操作类型：INSERT、UPDATE、DELETE、MOVE_IN、MOVE_OUT、ADJUST_INCREASE、ADJUST_DECREASE
     * @return 同步结果
     */
    Boolean syncInventoryDetailOperation(InventoryDetail inventoryDetail, String operation);

    /**
     * 同步物料操作到仓储管理表
     * 
     * @param material 物料信息
     * @param operation 操作类型：INSERT、UPDATE、DELETE
     * @return 同步结果
     */
    Boolean syncMaterialOperation(Material material, String operation);

    /**
     * 批量同步库存明细操作
     * 
     * @param inventoryDetails 库存明细列表
     * @param operation 操作类型
     * @return 同步结果
     */
    Boolean batchSyncInventoryDetailOperation(List<InventoryDetail> inventoryDetails, String operation);

    /**
     * 验证物料类型兼容性
     * 
     * @param materialType 物料类型
     * @param materialName 物料名称
     * @return 是否兼容
     */
    Boolean validateMaterialTypeCompatibility(String materialType, String materialName);

    /**
     * 获取仓储系统中的库存数量
     * 
     * @param materialType 物料类型
     * @param materialName 物料名称
     * @return 库存数量
     */
    Integer getWarehouseStockQuantity(String materialType, String materialName);

    /**
     * 同步库存调整操作
     * 
     * @param materialType 物料类型
     * @param materialName 物料名称
     * @param adjustQuantity 调整数量（正数为增加，负数为减少）
     * @param adjustReason 调整原因
     * @param operator 操作员
     * @return 同步结果
     */
    Boolean syncStockAdjustment(String materialType, String materialName, Integer adjustQuantity, 
                               String adjustReason, String operator);

    /**
     * 同步出入库操作
     * 
     * @param materialType 物料类型
     * @param materialName 物料名称
     * @param quantity 数量
     * @param operationType 操作类型：INBOUND（入库）、OUTBOUND（出库）
     * @param operator 操作员
     * @param remark 备注
     * @return 同步结果
     */
    Boolean syncInOutboundOperation(String materialType, String materialName, Integer quantity,
                                   String operationType, String operator, String remark);

    /**
     * 检查仓储系统连接状态
     * 
     * @return 连接状态
     */
    Boolean checkWarehouseSystemConnection();

    /**
     * 获取同步失败计数
     * 
     * @return 失败次数
     */
    Long getSyncFailureCount();

    /**
     * 重试失败的同步操作
     * 
     * @return 重试结果
     */
    Boolean retrySyncFailures();
}

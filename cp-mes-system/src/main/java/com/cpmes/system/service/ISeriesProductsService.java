package com.cpmes.system.service;

import com.cpmes.system.domain.SeriesProducts;
import com.cpmes.system.domain.vo.SeriesProductsVo;
import com.cpmes.system.domain.bo.SeriesProductsBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 系列产品Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ISeriesProductsService {

    /**
     * 查询系列产品
     */
    SeriesProductsVo queryById(Integer id);

    /**
     * 查询系列产品列表
     */
    TableDataInfo<SeriesProductsVo> queryPageList(SeriesProductsBo bo, PageQuery pageQuery);

    /**
     * 查询系列产品列表
     */
    List<SeriesProductsVo> queryList(SeriesProductsBo bo);

    /**
     * 新增系列产品
     */
    Boolean insertByBo(SeriesProductsBo bo);

    /**
     * 修改系列产品
     */
    Boolean updateByBo(SeriesProductsBo bo);

    /**
     * 校验并批量删除系列产品信息
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);
} 
package com.cpmes.system.service;

import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.system.domain.bo.BinOperationLogBo;
import com.cpmes.system.domain.vo.BinOperationLogVo;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 货位操作记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public interface IBinOperationLogService {

    /**
     * 查询货位操作记录
     */
    BinOperationLogVo queryById(Long logId);

    /**
     * 查询货位操作记录列表
     */
    TableDataInfo<BinOperationLogVo> queryPageList(BinOperationLogBo bo, PageQuery pageQuery);

    /**
     * 查询货位操作记录列表
     */
    List<BinOperationLogVo> queryList(BinOperationLogBo bo);

    /**
     * 新增货位操作记录
     */
    Boolean insertByBo(BinOperationLogBo bo);

    /**
     * 修改货位操作记录
     */
    Boolean updateByBo(BinOperationLogBo bo);

    /**
     * 校验并批量删除货位操作记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据区域编码查询操作记录
     *
     * @param zoneCode 区域编码
     * @return 操作记录列表
     */
    List<BinOperationLogVo> queryByZoneCode(String zoneCode);

    /**
     * 根据操作类型查询操作记录
     *
     * @param operationType 操作类型
     * @return 操作记录列表
     */
    List<BinOperationLogVo> queryByOperationType(String operationType);

    /**
     * 根据时间范围查询操作记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作记录列表
     */
    List<BinOperationLogVo> queryByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 记录库存操作日志
     *
     * @param zoneCode 区域编码
     * @param operationType 操作类型（inbound-入库，outbound-出库，transfer-移库，adjust-调整，check-盘点）
     * @param materialId 物料ID
     * @param materialName 物料名称
     * @param quantityBefore 操作前数量
     * @param quantityChange 数量变化（正数为增加，负数为减少）
     * @param quantityAfter 操作后数量
     * @param batchNo 批次号
     * @param sourceDocument 来源单据号
     * @param operator 操作人员
     * @param operationReason 操作原因
     * @param remark 备注
     * @return 记录结果
     */
    Boolean recordOperationLog(String zoneCode, String operationType, String materialId, String materialName,
                              Integer quantityBefore, Integer quantityChange, Integer quantityAfter,
                              String batchNo, String sourceDocument, String operator,
                              String operationReason, String remark);

    /**
     * 批量验证操作记录
     *
     * @param logIds 日志ID列表
     * @return 验证结果
     */
    Boolean batchVerifyOperationLog(List<Long> logIds);
} 
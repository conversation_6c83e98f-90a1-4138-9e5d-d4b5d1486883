package com.cpmes.system.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 临时文件存储服务接口
 * 用于处理临时订单的文件存储和访问
 * 
 * <AUTHOR>
 */
public interface ITempFileStorageService {

    /**
     * 保存临时文件
     * 
     * @param tempOrderId 临时订单ID
     * @param file 文件
     * @return 文件访问URL
     */
    String saveTempFile(String tempOrderId, MultipartFile file);

    /**
     * 批量保存临时文件
     * 
     * @param tempOrderId 临时订单ID
     * @param files 文件列表
     * @return 文件访问URL列表
     */
    List<String> saveTempFiles(String tempOrderId, MultipartFile[] files);

    /**
     * 获取临时文件输入流
     * 
     * @param tempOrderId 临时订单ID
     * @param fileName 文件名
     * @return 文件输入流
     */
    InputStream getTempFileStream(String tempOrderId, String fileName);

    /**
     * 获取临时文件访问URL
     * 
     * @param tempOrderId 临时订单ID
     * @param fileName 文件名
     * @return 访问URL
     */
    String getTempFileUrl(String tempOrderId, String fileName);

    /**
     * 获取临时订单的所有文件URL
     * 
     * @param tempOrderId 临时订单ID
     * @return 文件URL列表
     */
    List<String> getTempFileUrls(String tempOrderId);

    /**
     * 删除临时文件
     * 
     * @param tempOrderId 临时订单ID
     * @param fileName 文件名
     * @return 是否删除成功
     */
    boolean deleteTempFile(String tempOrderId, String fileName);

    /**
     * 删除临时订单的所有文件
     * 
     * @param tempOrderId 临时订单ID
     * @return 是否删除成功
     */
    boolean deleteTempFiles(String tempOrderId);

    /**
     * 清理过期的临时文件
     * 
     * @return 清理的文件数量
     */
    int cleanExpiredTempFiles();

    /**
     * 检查临时文件是否存在
     * 
     * @param tempOrderId 临时订单ID
     * @param fileName 文件名
     * @return 是否存在
     */
    boolean tempFileExists(String tempOrderId, String fileName);

    /**
     * 获取临时文件的元数据
     * 
     * @param tempOrderId 临时订单ID
     * @param fileName 文件名
     * @return 文件元数据
     */
    TempFileMetadata getTempFileMetadata(String tempOrderId, String fileName);

    /**
     * 临时文件元数据
     */
    class TempFileMetadata {
        private String fileName;
        private String originalName;
        private long fileSize;
        private String contentType;
        private long createTime;
        private String accessUrl;

        // 构造函数
        public TempFileMetadata() {}

        public TempFileMetadata(String fileName, String originalName, long fileSize, 
                               String contentType, long createTime, String accessUrl) {
            this.fileName = fileName;
            this.originalName = originalName;
            this.fileSize = fileSize;
            this.contentType = contentType;
            this.createTime = createTime;
            this.accessUrl = accessUrl;
        }

        // Getter和Setter方法
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public String getOriginalName() { return originalName; }
        public void setOriginalName(String originalName) { this.originalName = originalName; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }

        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public String getAccessUrl() { return accessUrl; }
        public void setAccessUrl(String accessUrl) { this.accessUrl = accessUrl; }
    }
}

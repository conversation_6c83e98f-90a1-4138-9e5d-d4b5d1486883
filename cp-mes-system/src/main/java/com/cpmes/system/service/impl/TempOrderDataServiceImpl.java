package com.cpmes.system.service.impl;

import com.cpmes.common.utils.redis.RedisUtils;
import org.apache.commons.io.IOUtils;
import com.cpmes.system.entity.dto.purchaseOrder.PurchaseLinkDto;
import com.cpmes.system.entity.PurchaseOrderImage;
import com.cpmes.system.service.IPurchaseOrderImageService;
import com.cpmes.system.service.ITempOrderDataService;
import com.cpmes.system.serviceJenasi.PurchaseOrderService;
import com.cpmes.system.service.ITempFileStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 临时订单数据管理服务实现类
 * 使用Redis作为临时数据存储，支持过期自动清理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TempOrderDataServiceImpl implements ITempOrderDataService {

    private final PurchaseOrderService purchaseOrderService;
    private final ITempFileStorageService tempFileStorageService;
    private final IPurchaseOrderImageService purchaseOrderImageService;

    /**
     * 临时数据过期时间（分钟），默认30分钟
     */
    @Value("${temp-order.expire-minutes:30}")
    private int expireMinutes;

    /**
     * Redis key前缀
     */
    private static final String TEMP_ORDER_PREFIX = "temp:order:";
    private static final String TEMP_LINKS_SUFFIX = ":links";
    private static final String TEMP_IMAGES_SUFFIX = ":images";
    private static final String TEMP_EXPIRE_SUFFIX = ":expire";

    // ========================================
    // 链接管理相关方法
    // ========================================

    @Override
    public boolean saveTempLinks(String tempOrderId, List<PurchaseLinkDto> links) {
        if (!isTempOrderId(tempOrderId)) {
            log.warn("无效的临时订单ID: {}", tempOrderId);
            return false;
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_LINKS_SUFFIX;
            RedisUtils.setCacheObject(key, links, Duration.ofMinutes(expireMinutes));

            // 更新过期时间标记
            refreshTempDataExpireTime(tempOrderId);

            log.info("临时订单{}保存链接数据成功，共{}个链接", tempOrderId, links.size());
            return true;
        } catch (Exception e) {
            log.error("临时订单{}保存链接数据失败", tempOrderId, e);
            return false;
        }
    }

    @Override
    public List<PurchaseLinkDto> getTempLinks(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return new ArrayList<>();
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_LINKS_SUFFIX;
            List<PurchaseLinkDto> links = RedisUtils.getCacheObject(key);
            return links != null ? links : new ArrayList<>();
        } catch (Exception e) {
            log.error("临时订单{}获取链接数据失败", tempOrderId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean deleteTempLinks(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return false;
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_LINKS_SUFFIX;
            RedisUtils.deleteObject(key);
            log.info("临时订单{}删除链接数据成功", tempOrderId);
            return true;
        } catch (Exception e) {
            log.error("临时订单{}删除链接数据失败", tempOrderId, e);
            return false;
        }
    }

    // ========================================
    // 图片管理相关方法
    // ========================================

    @Override
    public boolean saveTempImages(String tempOrderId, List<PurchaseOrderImage> images) {
        if (!isTempOrderId(tempOrderId)) {
            log.warn("无效的临时订单ID: {}", tempOrderId);
            return false;
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_IMAGES_SUFFIX;

            // 增强日志：记录每张图片的备注信息
            log.info("临时订单{}保存图片数据，共{}张图片", tempOrderId, images.size());
            int imagesWithDescription = 0;
            for (int i = 0; i < images.size(); i++) {
                PurchaseOrderImage image = images.get(i);
                boolean hasDescription = image.getImageDescription() != null && !image.getImageDescription().trim().isEmpty();
                if (hasDescription) {
                    imagesWithDescription++;
                }
                log.info("  图片{}: 文件名={}, 备注='{}', 有备注={}",
                    i + 1, image.getOriginalName(), image.getImageDescription(), hasDescription);
            }
            log.info("临时订单{}图片备注统计: 总数={}, 有备注={}, 无备注={}",
                tempOrderId, images.size(), imagesWithDescription, images.size() - imagesWithDescription);

            RedisUtils.setCacheObject(key, images, Duration.ofMinutes(expireMinutes));

            // 更新过期时间标记
            refreshTempDataExpireTime(tempOrderId);

            log.info("临时订单{}图片数据保存到Redis成功", tempOrderId);
            return true;
        } catch (Exception e) {
            log.error("临时订单{}保存图片数据失败", tempOrderId, e);
            return false;
        }
    }

    @Override
    public List<PurchaseOrderImage> getTempImages(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return new ArrayList<>();
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_IMAGES_SUFFIX;
            List<PurchaseOrderImage> images = RedisUtils.getCacheObject(key);

            if (images != null && !images.isEmpty()) {
                log.info("临时订单{}获取图片数据成功，共{}张图片", tempOrderId, images.size());
                // 增强日志：记录每张图片的备注信息
                int imagesWithDescription = 0;
                for (int i = 0; i < images.size(); i++) {
                    PurchaseOrderImage image = images.get(i);
                    boolean hasDescription = image.getImageDescription() != null && !image.getImageDescription().trim().isEmpty();
                    if (hasDescription) {
                        imagesWithDescription++;
                    }
                    log.info("  图片{}: 文件名={}, 备注='{}', 有备注={}",
                        i + 1, image.getOriginalName(), image.getImageDescription(), hasDescription);
                }
                log.info("临时订单{}图片备注获取统计: 总数={}, 有备注={}, 无备注={}",
                    tempOrderId, images.size(), imagesWithDescription, images.size() - imagesWithDescription);
            } else {
                log.info("临时订单{}无图片数据或数据为空", tempOrderId);
            }

            return images != null ? images : new ArrayList<>();
        } catch (Exception e) {
            log.error("临时订单{}获取图片数据失败", tempOrderId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getTempImageUrls(String tempOrderId) {
        List<PurchaseOrderImage> images = getTempImages(tempOrderId);
        return images.stream()
                .map(PurchaseOrderImage::getStoragePath)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public boolean addTempImage(String tempOrderId, PurchaseOrderImage image) {
        if (!isTempOrderId(tempOrderId)) {
            log.warn("无效的临时订单ID: {}", tempOrderId);
            return false;
        }

        try {
            List<PurchaseOrderImage> images = getTempImages(tempOrderId);
            images.add(image);
            return saveTempImages(tempOrderId, images);
        } catch (Exception e) {
            log.error("临时订单{}添加图片失败", tempOrderId, e);
            return false;
        }
    }

    @Override
    public boolean deleteTempImages(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return false;
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_IMAGES_SUFFIX;
            RedisUtils.deleteObject(key);
            log.info("临时订单{}删除图片数据成功", tempOrderId);
            return true;
        } catch (Exception e) {
            log.error("临时订单{}删除图片数据失败", tempOrderId, e);
            return false;
        }
    }

    @Override
    public boolean deleteTempImage(String tempOrderId, String imagePath) {
        if (!isTempOrderId(tempOrderId)) {
            return false;
        }

        try {
            List<PurchaseOrderImage> images = getTempImages(tempOrderId);
            images.removeIf(image -> Objects.equals(image.getStoragePath(), imagePath));
            return saveTempImages(tempOrderId, images);
        } catch (Exception e) {
            log.error("临时订单{}删除特定图片失败: {}", tempOrderId, imagePath, e);
            return false;
        }
    }

    // ========================================
    // 数据迁移相关方法
    // ========================================

    @Override
    public boolean migrateTempDataToReal(String tempOrderId, Long realOrderId) {
        // 调用增强版本，不传递用户名参数（保持向后兼容）
        return migrateTempDataToReal(tempOrderId, realOrderId, null);
    }

    @Override
    public boolean migrateTempDataToReal(String tempOrderId, Long realOrderId, String optionalUsername) {
        if (!isTempOrderId(tempOrderId) || realOrderId == null) {
            log.warn("数据迁移参数无效: tempOrderId={}, realOrderId={}", tempOrderId, realOrderId);
            return false;
        }

        try {
            log.info("开始执行临时数据迁移: tempOrderId={}, realOrderId={}, 可选用户名: '{}'",
                tempOrderId, realOrderId, optionalUsername);
            boolean success = true;

            // 获取临时数据统计
            Map<String, Object> statistics = getTempDataStatistics(tempOrderId);
            log.info("临时订单{}数据统计: {}", tempOrderId, statistics);

            // 迁移链接数据
            List<PurchaseLinkDto> links = getTempLinks(tempOrderId);
            if (!links.isEmpty()) {
                log.info("开始迁移临时订单{}的{}个链接到正式订单{}", tempOrderId, links.size(), realOrderId);
                boolean linkSuccess = purchaseOrderService.savePurchaseLinks(realOrderId, links);
                if (linkSuccess) {
                    log.info("临时订单{}链接数据迁移成功", tempOrderId);
                } else {
                    log.error("临时订单{}链接数据迁移到正式订单{}失败", tempOrderId, realOrderId);
                    success = false;
                }
            } else {
                log.info("临时订单{}无链接数据需要迁移", tempOrderId);
            }

            // 迁移图片数据 - 增强版本，支持可选用户名参数
            List<PurchaseOrderImage> images = getTempImages(tempOrderId);
            if (!images.isEmpty()) {
                log.info("开始迁移临时订单{}的{}张图片到正式订单{}", tempOrderId, images.size(), realOrderId);

                // 使用增强版本的图片迁移方法，支持可选用户名参数
                boolean imageSuccess = migrateImagesWithFileTransfer(tempOrderId, realOrderId, images, optionalUsername);
                if (imageSuccess) {
                    log.info("临时订单{}图片数据迁移成功", tempOrderId);
                } else {
                    log.error("临时订单{}图片数据迁移失败", tempOrderId);
                    success = false;
                }
            } else {
                log.info("临时订单{}无图片数据需要迁移", tempOrderId);
            }

            // 清理临时数据
            if (success) {
                boolean cleanSuccess = clearTempData(tempOrderId);
                if (cleanSuccess) {
                    log.info("临时订单{}数据迁移到正式订单{}成功，临时数据已清理", tempOrderId, realOrderId);
                } else {
                    log.warn("临时订单{}数据迁移成功，但临时数据清理失败", tempOrderId);
                }
            } else {
                log.error("临时订单{}数据迁移到正式订单{}失败，保留临时数据用于调试", tempOrderId, realOrderId);
            }

            return success;
        } catch (Exception e) {
            log.error("临时订单{}数据迁移到正式订单{}异常", tempOrderId, realOrderId, e);
            return false;
        }
    }

    @Override
    public boolean clearTempData(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return false;
        }

        try {
            boolean success = true;

            // 清理Redis中的数据
            success &= deleteTempLinks(tempOrderId);
            success &= deleteTempImages(tempOrderId);

            // 清理文件系统中的临时文件
            success &= tempFileStorageService.deleteTempFiles(tempOrderId);

            // 删除过期时间标记
            String expireKey = TEMP_ORDER_PREFIX + tempOrderId + TEMP_EXPIRE_SUFFIX;
            RedisUtils.deleteObject(expireKey);

            log.info("临时订单{}数据清理{}", tempOrderId, success ? "成功" : "失败");
            return success;
        } catch (Exception e) {
            log.error("临时订单{}数据清理失败", tempOrderId, e);
            return false;
        }
    }

    // ========================================
    // 数据清理相关方法
    // ========================================

    @Override
    public int cleanExpiredTempData() {
        // 这里可以实现定时清理逻辑
        // Redis的过期机制会自动清理过期数据，所以这里主要是统计和日志
        log.info("执行临时数据清理任务");
        return 0;
    }

    @Override
    public long getTempDataExpireTime(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return -1;
        }

        try {
            String key = TEMP_ORDER_PREFIX + tempOrderId + TEMP_LINKS_SUFFIX;
            return RedisUtils.getTimeToLive(key);
        } catch (Exception e) {
            log.error("获取临时订单{}过期时间失败", tempOrderId, e);
            return -1;
        }
    }

    @Override
    public boolean refreshTempDataExpireTime(String tempOrderId) {
        if (!isTempOrderId(tempOrderId)) {
            return false;
        }

        try {
            String expireKey = TEMP_ORDER_PREFIX + tempOrderId + TEMP_EXPIRE_SUFFIX;
            RedisUtils.setCacheObject(expireKey, System.currentTimeMillis(), Duration.ofMinutes(expireMinutes));
            return true;
        } catch (Exception e) {
            log.error("刷新临时订单{}过期时间失败", tempOrderId, e);
            return false;
        }
    }

    // ========================================
    // 工具方法
    // ========================================

    @Override
    public boolean isTempOrderId(String orderId) {
        return orderId != null && orderId.startsWith("temp_");
    }

    @Override
    public String generateTempOrderId() {
        return "temp_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    @Override
    public Map<String, Object> getTempDataStatistics(String tempOrderId) {
        Map<String, Object> statistics = new HashMap<>();

        if (!isTempOrderId(tempOrderId)) {
            statistics.put("valid", false);
            return statistics;
        }

        try {
            List<PurchaseLinkDto> links = getTempLinks(tempOrderId);
            List<PurchaseOrderImage> images = getTempImages(tempOrderId);
            long expireTime = getTempDataExpireTime(tempOrderId);

            // 统计图片备注信息
            long imagesWithDescription = images.stream()
                .filter(img -> img.getImageDescription() != null && !img.getImageDescription().trim().isEmpty())
                .count();

            statistics.put("valid", true);
            statistics.put("tempOrderId", tempOrderId);
            statistics.put("linkCount", links.size());
            statistics.put("imageCount", images.size());
            statistics.put("imagesWithDescription", imagesWithDescription);
            statistics.put("expireTime", expireTime);
            statistics.put("hasData", !links.isEmpty() || !images.isEmpty());

            log.debug("临时订单{}数据统计: 链接{}个, 图片{}个(含备注{}个)",
                tempOrderId, links.size(), images.size(), imagesWithDescription);

        } catch (Exception e) {
            log.error("获取临时订单{}统计信息失败", tempOrderId, e);
            statistics.put("valid", false);
            statistics.put("error", e.getMessage());
        }

        return statistics;
    }

    /**
     * 迁移图片并进行文件传输（增强版本）
     * 从临时文件存储复制到MinIO永久存储
     */
    private boolean migrateImagesWithFileTransfer(String tempOrderId, Long realOrderId, List<PurchaseOrderImage> tempImages) {
        log.info("开始迁移图片文件: tempOrderId={}, realOrderId={}, imageCount={}", tempOrderId, realOrderId, tempImages.size());

        com.cpmes.oss.core.OssClient ossClient = null;
        try {
            // 获取MinIO客户端
            try {
                ossClient = com.cpmes.oss.factory.OssFactory.instance("minio");
                log.info("MinIO客户端获取成功");
            } catch (Exception e) {
                log.error("获取MinIO客户端失败", e);
                // 尝试获取默认客户端
                try {
                    ossClient = com.cpmes.oss.factory.OssFactory.instance();
                    log.info("使用默认OSS客户端");
                } catch (Exception e2) {
                    log.error("获取默认OSS客户端也失败", e2);
                    return false;
                }
            }

            List<com.cpmes.system.entity.PurchaseOrderImage> newImages = new ArrayList<>();
            Date currentTime = new Date();

            // 获取当前最大排序序号
            Integer maxSortOrder = purchaseOrderImageService.getMaxSortOrderByPurchaseOrderId(realOrderId);
            if (maxSortOrder == null) {
                maxSortOrder = 0;
            }
            log.info("当前最大排序序号: {}", maxSortOrder);

            for (int i = 0; i < tempImages.size(); i++) {
                PurchaseOrderImage tempImage = tempImages.get(i);
                java.io.InputStream fileStream = null;

                try {
                    log.info("开始迁移第{}张图片: {}", i + 1, tempImage.getOriginalName());

                    // 从临时存储获取文件流
                    String fileName = extractFileNameFromPath(tempImage.getStoragePath());
                    log.info("提取的文件名: {} -> {}", tempImage.getStoragePath(), fileName);

                    // 增强调试信息：显示原始文件名和URL编码状态
                    boolean isUrlEncoded = fileName.contains("%");
                    log.info("文件名分析: 原始={}, URL编码={}, 包含中文={}",
                        fileName, isUrlEncoded, containsChinese(fileName));

                    fileStream = tempFileStorageService.getTempFileStream(tempOrderId, fileName);

                    if (fileStream == null) {
                        log.error("无法获取临时文件流: tempOrderId={}, fileName={}, originalPath={}, isUrlEncoded={}",
                            tempOrderId, fileName, tempImage.getStoragePath(), isUrlEncoded);

                        // 尝试列出临时目录中的实际文件，便于调试
                        try {
                            java.nio.file.Path tempDir = java.nio.file.Paths.get("test", tempOrderId);
                            if (java.nio.file.Files.exists(tempDir)) {
                                log.info("临时目录{}中的实际文件:", tempDir);
                                java.nio.file.Files.list(tempDir)
                                    .forEach(path -> log.info("  - {}", path.getFileName()));
                            } else {
                                log.warn("临时目录不存在: {}", tempDir);
                            }
                        } catch (Exception e) {
                            log.warn("列出临时目录文件失败", e);
                        }

                        continue;
                    }

                    // 生成新的MinIO对象名称
                    String objectName = generateImageObjectName(realOrderId, tempImage.getOriginalName());
                    log.info("生成MinIO对象名称: {}", objectName);

                    // 读取文件数据
                    byte[] fileData = org.apache.commons.io.IOUtils.toByteArray(fileStream);
                    log.info("读取文件数据成功，大小: {} bytes", fileData.length);

                    // 上传到MinIO
                    com.cpmes.oss.entity.UploadResult uploadResult = ossClient.upload(
                        fileData,
                        objectName,
                        tempImage.getContentType()
                    );

                    if (uploadResult == null || uploadResult.getUrl() == null) {
                        log.error("MinIO上传失败，返回结果为空: objectName={}", objectName);
                        continue;
                    }

                    log.info("MinIO上传成功: {} -> {}", objectName, uploadResult.getUrl());

                    // 创建新的图片记录
                    PurchaseOrderImage newImage = new PurchaseOrderImage();
                    newImage.setPurchaseOrderId(realOrderId);
                    newImage.setOriginalName(tempImage.getOriginalName());
                    newImage.setStoragePath(uploadResult.getUrl());  // 使用MinIO的新URL
                    newImage.setFileSize(tempImage.getFileSize());
                    newImage.setContentType(tempImage.getContentType());

                    // 重点：迁移图片备注信息
                    String tempDescription = tempImage.getImageDescription();
                    newImage.setImageDescription(tempDescription);
                    log.info("图片备注迁移: 临时备注='{}' -> 正式备注='{}'",
                        tempDescription, newImage.getImageDescription());

                    newImage.setUploadTime(currentTime);

                    // 增强移动端兼容性：安全获取用户信息
                    try {
                        Long userId = com.cpmes.common.helper.LoginHelper.getUserId();
                        String userName = com.cpmes.common.helper.LoginHelper.getUsername();

                        newImage.setUploadUserId(userId != null ? userId : 0L); // 默认用户ID为0
                        newImage.setUploadUserName(userName != null ? userName : "system_user"); // 默认用户名

                        log.info("图片迁移用户信息: userId={}, userName='{}'",
                            newImage.getUploadUserId(), newImage.getUploadUserName());
                    } catch (Exception e) {
                        log.warn("获取用户信息失败，使用默认值: {}", e.getMessage());
                        newImage.setUploadUserId(0L);
                        newImage.setUploadUserName("system_user");
                    }

                    newImage.setSortOrder(maxSortOrder + i + 1);
                    newImage.setStatus(PurchaseOrderImage.Status.NORMAL);
                    newImage.setCreateTime(currentTime);
                    newImage.setUpdateTime(currentTime);

                    newImages.add(newImage);

                    log.info("临时图片迁移成功: {} -> {}, 备注: '{}'",
                        tempImage.getStoragePath(), uploadResult.getUrl(), tempDescription);

                } catch (Exception e) {
                    log.error("迁移临时图片失败: tempOrderId={}, image={}", tempOrderId, tempImage.getOriginalName(), e);
                    // 继续处理其他图片，不立即返回失败
                } finally {
                    // 确保文件流被关闭
                    if (fileStream != null) {
                        try {
                            fileStream.close();
                        } catch (Exception e) {
                            log.warn("关闭文件流失败", e);
                        }
                    }
                }
            }

            // 批量保存新图片记录
            if (!newImages.isEmpty()) {
                log.info("开始批量保存{}张图片到数据库", newImages.size());
                boolean saveSuccess = purchaseOrderImageService.batchSave(newImages);
                if (saveSuccess) {
                    log.info("图片迁移完全成功: tempOrderId={}, realOrderId={}, successCount={}/{}",
                        tempOrderId, realOrderId, newImages.size(), tempImages.size());
                    return true;
                } else {
                    log.error("图片数据库保存失败: tempOrderId={}, realOrderId={}, preparedCount={}",
                        tempOrderId, realOrderId, newImages.size());
                    return false;
                }
            } else {
                log.error("没有成功迁移任何图片: tempOrderId={}, realOrderId={}, totalCount={}",
                    tempOrderId, realOrderId, tempImages.size());
                return false;
            }

        } catch (Exception e) {
            log.error("图片迁移过程中发生异常", e);
            return false;
        }
    }

    /**
     * 迁移图片并进行文件传输（增强版本，支持可选用户名参数）
     * 从临时文件存储复制到MinIO永久存储
     */
    private boolean migrateImagesWithFileTransfer(String tempOrderId, Long realOrderId,
                                                List<PurchaseOrderImage> tempImages, String optionalUsername) {
        // 调用原版本方法，但在用户信息获取时使用可选用户名参数
        return migrateImagesWithFileTransferEnhanced(tempOrderId, realOrderId, tempImages, optionalUsername);
    }

    /**
     * 迁移图片并进行文件传输的核心实现（支持智能用户信息获取）
     */
    private boolean migrateImagesWithFileTransferEnhanced(String tempOrderId, Long realOrderId,
                                                        List<PurchaseOrderImage> tempImages, String optionalUsername) {
        log.info("开始迁移图片文件: tempOrderId={}, realOrderId={}, imageCount={}, optionalUsername='{}'",
            tempOrderId, realOrderId, tempImages.size(), optionalUsername);

        com.cpmes.oss.core.OssClient ossClient = null;
        try {
            // 获取MinIO客户端
            try {
                ossClient = com.cpmes.oss.factory.OssFactory.instance("minio");
                log.info("MinIO客户端获取成功");
            } catch (Exception e) {
                log.error("获取MinIO客户端失败", e);
                // 尝试获取默认客户端
                try {
                    ossClient = com.cpmes.oss.factory.OssFactory.instance();
                    log.info("使用默认OSS客户端");
                } catch (Exception e2) {
                    log.error("获取默认OSS客户端也失败", e2);
                    return false;
                }
            }

            List<com.cpmes.system.entity.PurchaseOrderImage> newImages = new ArrayList<>();
            Date currentTime = new Date();

            // 获取当前最大排序序号
            Integer maxSortOrder = purchaseOrderImageService.getMaxSortOrderByPurchaseOrderId(realOrderId);
            if (maxSortOrder == null) {
                maxSortOrder = 0;
            }
            log.info("当前最大排序序号: {}", maxSortOrder);

            // 智能获取用户信息
            UserInfo userInfo = getSmartUserInfo(optionalUsername);
            log.info("智能用户信息获取结果: userId={}, userName='{}'", userInfo.userId, userInfo.userName);

            for (int i = 0; i < tempImages.size(); i++) {
                PurchaseOrderImage tempImage = tempImages.get(i);
                java.io.InputStream fileStream = null;

                try {
                    log.info("开始迁移第{}张图片: {}", i + 1, tempImage.getOriginalName());

                    // 从临时存储获取文件流（修复：使用正确的文件访问方式）
                    String fileName = extractFileNameFromPath(tempImage.getStoragePath());
                    log.info("提取的文件名: {} -> {}", tempImage.getStoragePath(), fileName);

                    // 增强调试信息：显示原始文件名和URL编码状态
                    boolean isUrlEncoded = fileName.contains("%");
                    log.info("文件名分析: 原始={}, URL编码={}, 包含中文={}",
                        fileName, isUrlEncoded, containsChinese(fileName));

                    // 修复：使用tempFileStorageService获取文件流，而不是直接访问HTTP URL
                    fileStream = tempFileStorageService.getTempFileStream(tempOrderId, fileName);

                    if (fileStream == null) {
                        log.error("无法获取临时文件流: tempOrderId={}, fileName={}, originalPath={}, isUrlEncoded={}",
                            tempOrderId, fileName, tempImage.getStoragePath(), isUrlEncoded);

                        // 尝试列出临时目录中的实际文件，便于调试
                        try {
                            java.nio.file.Path tempDir = java.nio.file.Paths.get("test", tempOrderId);
                            if (java.nio.file.Files.exists(tempDir)) {
                                log.info("临时目录{}中的实际文件:", tempDir);
                                java.nio.file.Files.list(tempDir)
                                    .forEach(path -> log.info("  - {}", path.getFileName()));
                            } else {
                                log.warn("临时目录不存在: {}", tempDir);
                            }
                        } catch (Exception e) {
                            log.warn("列出临时目录文件失败", e);
                        }

                        continue;
                    }

                    // 生成新的MinIO对象名称
                    String objectName = generateImageObjectName(realOrderId, tempImage.getOriginalName());
                    log.info("生成MinIO对象名称: {}", objectName);

                    // 读取文件数据
                    byte[] fileData = org.apache.commons.io.IOUtils.toByteArray(fileStream);
                    log.info("读取文件数据成功，大小: {} bytes", fileData.length);

                    // 上传到MinIO
                    com.cpmes.oss.entity.UploadResult uploadResult = ossClient.upload(
                        fileData,
                        objectName,
                        tempImage.getContentType()
                    );

                    if (uploadResult == null || uploadResult.getUrl() == null) {
                        log.error("MinIO上传失败，返回结果为空: objectName={}", objectName);
                        continue;
                    }

                    log.info("MinIO上传成功: {} -> {}", objectName, uploadResult.getUrl());

                        // 创建新的图片记录
                        PurchaseOrderImage newImage = new PurchaseOrderImage();
                        newImage.setPurchaseOrderId(realOrderId);
                        newImage.setOriginalName(tempImage.getOriginalName());
                        newImage.setStoragePath(uploadResult.getUrl());
                        newImage.setFileSize(tempImage.getFileSize());
                        newImage.setContentType(tempImage.getContentType());

                        // 重点：迁移图片备注信息
                        String tempDescription = tempImage.getImageDescription();
                        newImage.setImageDescription(tempDescription);
                        log.info("图片备注迁移: 临时备注='{}' -> 正式备注='{}'",
                            tempDescription, newImage.getImageDescription());

                        newImage.setUploadTime(currentTime);

                        // 使用智能获取的用户信息
                        newImage.setUploadUserId(userInfo.userId);
                        newImage.setUploadUserName(userInfo.userName);

                        newImage.setSortOrder(maxSortOrder + i + 1);
                        newImage.setStatus(PurchaseOrderImage.Status.NORMAL);
                        newImage.setCreateTime(currentTime);
                        newImage.setUpdateTime(currentTime);

                        newImages.add(newImage);

                        log.info("临时图片迁移成功: {} -> {}, 备注: '{}', 用户: {}({})",
                            tempImage.getStoragePath(), uploadResult.getUrl(), tempDescription,
                            userInfo.userName, userInfo.userId);

                } catch (Exception e) {
                    log.error("迁移临时图片失败: tempOrderId={}, image={}", tempOrderId, tempImage.getOriginalName(), e);
                    // 继续处理其他图片，不立即返回失败
                } finally {
                    // 确保文件流被关闭
                    if (fileStream != null) {
                        try {
                            fileStream.close();
                        } catch (Exception e) {
                            log.warn("关闭文件流失败", e);
                        }
                    }
                }
            }

            // 批量保存新图片记录
            if (!newImages.isEmpty()) {
                log.info("开始批量保存{}张图片到数据库", newImages.size());
                boolean saveSuccess = purchaseOrderImageService.batchSave(newImages);
                if (saveSuccess) {
                    log.info("图片迁移完全成功: tempOrderId={}, realOrderId={}, successCount={}/{}",
                        tempOrderId, realOrderId, newImages.size(), tempImages.size());
                    return true;
                } else {
                    log.error("图片数据库保存失败: tempOrderId={}, realOrderId={}, preparedCount={}",
                        tempOrderId, realOrderId, newImages.size());
                    return false;
                }
            } else {
                log.error("没有成功迁移任何图片: tempOrderId={}, realOrderId={}, totalCount={}",
                    tempOrderId, realOrderId, tempImages.size());
                return false;
            }

        } catch (Exception e) {
            log.error("图片迁移过程中发生异常", e);
            return false;
        }
    }

    /**
     * 从路径中提取文件名（增强版本，支持URL解码）
     */
    private String extractFileNameFromPath(String path) {
        if (path == null || path.isEmpty()) {
            log.warn("路径为空，无法提取文件名");
            return "";
        }

        try {
            // 处理URL路径或文件路径
            String fileName;
            if (path.contains("/")) {
                fileName = path.substring(path.lastIndexOf("/") + 1);
            } else if (path.contains("\\")) {
                fileName = path.substring(path.lastIndexOf("\\") + 1);
            } else {
                fileName = path;
            }

            // 检查是否为URL编码格式，如果是则进行解码
            String decodedFileName = fileName;
            if (fileName.contains("%")) {
                try {
                    decodedFileName = java.net.URLDecoder.decode(fileName, "UTF-8");
                    log.info("URL解码文件名: {} -> {}", fileName, decodedFileName);
                } catch (java.io.UnsupportedEncodingException e) {
                    log.warn("URL解码失败，使用原始文件名: {}", fileName, e);
                    decodedFileName = fileName;
                }
            }

            log.debug("从路径{}提取文件名: {} (解码后: {})", path, fileName, decodedFileName);
            return decodedFileName;

        } catch (Exception e) {
            log.error("提取文件名失败: path={}", path, e);
            return "";
        }
    }

    /**
     * 生成图片对象名称（与PurchaseOrderImageServiceImpl保持一致）
     */
    private String generateImageObjectName(Long purchaseOrderId, String originalFilename) {
        // 生成时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());

        // 获取文件扩展名
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成唯一文件名
        String uniqueFileName = timestamp + "_" + java.util.UUID.randomUUID().toString().replace("-", "") + extension;

        // 构建完整的对象路径
        return "purchase/" + purchaseOrderId + "/images/" + uniqueFileName;
    }

    // ========================================
    // 私有辅助方法
    // ========================================

    /**
     * 根据文件名推断Content-Type
     */
    private String inferContentTypeFromFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "image/jpeg"; // 默认类型
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowerFileName.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerFileName.endsWith(".svg")) {
            return "image/svg+xml";
        } else {
            // 默认返回JPEG类型
            return "image/jpeg";
        }
    }

    /**
     * 检查字符串是否包含中文字符
     */
    private boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        for (char c : str.toCharArray()) {
            if (Character.UnicodeScript.of(c) == Character.UnicodeScript.HAN) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从URL中提取文件名
     * 支持多种URL格式的文件名提取
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "";
        }

        try {
            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];

            // 获取最后一个斜杠后的内容
            String fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1);

            // URL解码处理中文字符
            try {
                fileName = java.net.URLDecoder.decode(fileName, "UTF-8");
            } catch (Exception e) {
                // 解码失败时使用原始文件名
                log.debug("URL解码失败，使用原始文件名: {}", fileName);
            }

            return fileName;
        } catch (Exception e) {
            log.warn("从URL提取文件名失败: url={}, error={}", url, e.getMessage());
            return "";
        }
    }

    /**
     * 更新临时图片备注（增强版本，支持多种路径格式匹配）
     */
    public boolean updateTempImageDescription(String tempOrderId, String imagePath, String description) {
        if (!isTempOrderId(tempOrderId)) {
            log.warn("无效的临时订单ID: {}", tempOrderId);
            return false;
        }

        try {
            // 获取当前的临时图片列表
            List<PurchaseOrderImage> images = getTempImages(tempOrderId);
            if (images.isEmpty()) {
                log.warn("临时订单{}无图片数据", tempOrderId);
                return false;
            }

            log.info("开始查找图片进行备注更新: tempOrderId={}, imagePath={}, 图片总数={}",
                tempOrderId, imagePath, images.size());

            // 增强的路径匹配逻辑：支持完整URL和文件名两种格式
            boolean found = false;
            PurchaseOrderImage targetImage = null;

            for (int i = 0; i < images.size(); i++) {
                PurchaseOrderImage image = images.get(i);
                String storagePath = image.getStoragePath();

                log.info("  图片{}: 存储路径={}, 原始文件名={}",
                    i + 1, storagePath, image.getOriginalName());

                // 方式1：完整路径匹配（网页端格式）
                if (imagePath.equals(storagePath)) {
                    log.info("  -> 完整路径匹配成功");
                    targetImage = image;
                    found = true;
                    break;
                }

                // 方式2：文件名匹配（移动端格式）
                String fileName = extractFileNameFromUrl(storagePath);
                if (imagePath.equals(fileName)) {
                    log.info("  -> 文件名匹配成功: {} == {}", imagePath, fileName);
                    targetImage = image;
                    found = true;
                    break;
                }

                // 方式3：原始文件名匹配（备用方案）
                if (imagePath.equals(image.getOriginalName())) {
                    log.info("  -> 原始文件名匹配成功: {} == {}", imagePath, image.getOriginalName());
                    targetImage = image;
                    found = true;
                    break;
                }

                log.info("  -> 未匹配");
            }

            if (!found || targetImage == null) {
                log.warn("临时订单{}中未找到路径为{}的图片，已尝试完整路径、文件名、原始文件名三种匹配方式",
                    tempOrderId, imagePath);

                // 输出调试信息
                log.warn("调试信息 - 当前图片列表:");
                for (int i = 0; i < images.size(); i++) {
                    PurchaseOrderImage img = images.get(i);
                    log.warn("  图片{}: storagePath={}, originalName={}, fileName={}",
                        i + 1, img.getStoragePath(), img.getOriginalName(),
                        extractFileNameFromUrl(img.getStoragePath()));
                }
                return false;
            }

            // 更新找到的图片备注
            String oldDescription = targetImage.getImageDescription();
            targetImage.setImageDescription(description);
            log.info("更新临时图片备注成功: 文件={}, 旧备注='{}' -> 新备注='{}'",
                targetImage.getOriginalName(), oldDescription, description);

            // 保存更新后的图片列表到Redis
            boolean saveSuccess = saveTempImages(tempOrderId, images);
            if (saveSuccess) {
                log.info("临时订单{}图片备注更新并保存成功", tempOrderId);
                return true;
            } else {
                log.error("临时订单{}图片备注更新后保存失败", tempOrderId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新临时图片备注异常: tempOrderId={}, imagePath={}", tempOrderId, imagePath, e);
            return false;
        }
    }

    /**
     * 用户信息封装类
     */
    private static class UserInfo {
        public Long userId;
        public String userName;

        public UserInfo(Long userId, String userName) {
            this.userId = userId;
            this.userName = userName;
        }
    }

    /**
     * 智能获取用户信息
     * 优先级：传递的参数 > LoginHelper获取 > 默认值
     */
    private UserInfo getSmartUserInfo(String optionalUsername) {
        Long userId = null;
        String userName = null;

        try {
            // 第一优先级：尝试从LoginHelper获取用户ID
            userId = com.cpmes.common.helper.LoginHelper.getUserId();

            // 第二优先级：用户名获取策略
            if (optionalUsername != null && !optionalUsername.trim().isEmpty()) {
                // 如果传递了用户名参数，优先使用传递的参数
                userName = optionalUsername.trim();
                log.info("使用传递的用户名参数: '{}'", userName);
            } else {
                // 否则尝试从LoginHelper获取
                try {
                    userName = com.cpmes.common.helper.LoginHelper.getUsername();
                    log.info("从LoginHelper获取用户名: '{}'", userName);
                } catch (Exception e) {
                    log.warn("从LoginHelper获取用户名失败: {}", e.getMessage());
                    userName = null;
                }
            }

        } catch (Exception e) {
            log.warn("获取用户ID失败: {}", e.getMessage());
            userId = null;
        }

        // 应用默认值策略
        if (userId == null) {
            userId = 0L;
            log.info("使用默认用户ID: {}", userId);
        }

        if (userName == null || userName.trim().isEmpty()) {
            if (userId != null && userId > 0) {
                userName = "mobile_user_" + userId;
            } else {
                userName = "system_user";
            }
            log.info("使用默认用户名: '{}'", userName);
        }

        return new UserInfo(userId, userName);
    }
}

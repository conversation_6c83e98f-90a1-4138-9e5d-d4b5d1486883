package com.cpmes.system.service;

import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.system.domain.bo.PrintInterfaceConfigBo;
import com.cpmes.system.domain.vo.PrintInterfaceConfigVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 打印接口配置Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IPrintInterfaceConfigService {

    /**
     * 查询打印接口配置
     */
    PrintInterfaceConfigVo queryById(Long configId);

    /**
     * 查询打印接口配置列表
     */
    TableDataInfo<PrintInterfaceConfigVo> queryPageList(PrintInterfaceConfigBo bo, PageQuery pageQuery);

    /**
     * 查询打印接口配置列表
     */
    List<PrintInterfaceConfigVo> queryList(PrintInterfaceConfigBo bo);

    /**
     * 新增打印接口配置
     */
    Boolean insertByBo(PrintInterfaceConfigBo bo);

    /**
     * 修改打印接口配置
     */
    Boolean updateByBo(PrintInterfaceConfigBo bo);

    /**
     * 校验并批量删除打印接口配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量更新状态
     */
    Boolean updateStatus(Collection<Long> ids, String status);

    /**
     * 根据标签类型查询打印机配置
     */
    List<PrintInterfaceConfigVo> queryByLabelType(String labelType);

    /**
     * 获取最佳打印机配置
     */
    Map<String, Object> getBestPrinterConfig(String labelType);

    /**
     * 测试打印机连接
     */
    Boolean testPrinterConnection(Long configId);

    /**
     * 设置默认打印机
     */
    Boolean setDefaultPrinter(Long configId);
} 
package com.cpmes.system.service;

import com.cpmes.system.domain.vo.WarehouseInfoVo;
import com.cpmes.system.domain.bo.WarehouseInfoBo;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 仓库信息Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IWarehouseInfoService {

    /**
     * 查询仓库信息
     *
     * @param warehouseId 仓库信息主键
     * @return 仓库信息
     */
    WarehouseInfoVo queryById(Long warehouseId);

    /**
     * 查询仓库信息列表
     *
     * @param bo 仓库信息
     * @return 仓库信息集合
     */
    TableDataInfo<WarehouseInfoVo> queryPageList(WarehouseInfoBo bo, PageQuery pageQuery);

    /**
     * 查询仓库信息列表
     *
     * @param bo 仓库信息
     * @return 仓库信息集合
     */
    List<WarehouseInfoVo> queryList(WarehouseInfoBo bo);

    /**
     * 新增仓库信息
     *
     * @param bo 仓库信息
     * @return 结果
     */
    Boolean insertByBo(WarehouseInfoBo bo);

    /**
     * 修改仓库信息
     *
     * @param bo 仓库信息
     * @return 结果
     */
    Boolean updateByBo(WarehouseInfoBo bo);

    /**
     * 校验并批量删除仓库信息信息
     *
     * @param ids 需要删除的仓库信息主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量启用/停用仓库
     *
     * @param ids 仓库ID集合
     * @param status 状态(0:停用 1:启用)
     * @return 结果
     */
    Boolean updateStatus(Collection<Long> ids, String status);

    /**
     * 根据仓库类型查询仓库列表
     *
     * @param warehouseType 仓库类型
     * @return 仓库信息集合
     */
    List<WarehouseInfoVo> queryByType(String warehouseType);

    /**
     * 校验仓库编码是否唯一
     *
     * @param warehouseCode 仓库编码
     * @param warehouseId 仓库ID
     * @return 结果
     */
    Boolean checkWarehouseCodeUnique(String warehouseCode, Long warehouseId);

    /**
     * 校验仓库名称、类型、位置组合是否唯一
     *
     * @param warehouseName 仓库名称
     * @param warehouseType 仓库类型
     * @param warehouseAddress 仓库地址
     * @param warehouseId 仓库ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    Boolean checkWarehouseInfoUnique(String warehouseName, String warehouseType, String warehouseAddress, Long warehouseId);

    /**
     * 生成仓库编码
     *
     * @param warehouseType 仓库类型
     * @return 生成的仓库编码
     */
    String generateWarehouseCode(String warehouseType);

    /**
     * 根据仓库编码查询仓库信息
     *
     * @param warehouseCode 仓库编码
     * @return 仓库信息
     */
    WarehouseInfoVo queryByWarehouseCode(String warehouseCode);
}

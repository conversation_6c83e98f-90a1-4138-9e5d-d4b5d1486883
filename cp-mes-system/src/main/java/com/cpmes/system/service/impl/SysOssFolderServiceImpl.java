package com.cpmes.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.utils.BeanCopyUtils;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.oss.core.OssClient;
import com.cpmes.oss.enumd.FileStorageStrategy;
import com.cpmes.oss.factory.OssFactory;
import com.cpmes.system.domain.SysOss;
import com.cpmes.system.domain.SysOssFolder;
import com.cpmes.system.domain.bo.SysOssFolderBo;
import com.cpmes.system.domain.vo.SysOssFolderVo;
import com.cpmes.system.mapper.SysOssFolderMapper;
import com.cpmes.system.mapper.SysOssMapper;
import com.cpmes.system.service.ISysOssFolderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 文件夹管理 服务层实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysOssFolderServiceImpl implements ISysOssFolderService {

    private final SysOssFolderMapper baseMapper;
    private final SysOssMapper ossMapper;

    @Override
    public TableDataInfo<SysOssFolderVo> queryPageList(SysOssFolderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssFolder> lqw = buildQueryWrapper(bo);
        Page<SysOssFolderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<SysOssFolderVo> queryPrivateFolderTree(Long userId) {
        return baseMapper.selectFolderTreeByUser(userId, FileStorageStrategy.PRIVATE.getCode());
    }

    @Override
    public List<SysOssFolderVo> querySharedFolderTree(Long userId) {
        // 对于共享文件夹，显示所有用户创建的文件夹
        LambdaQueryWrapper<SysOssFolder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOssFolder::getStorageStrategy, FileStorageStrategy.SHARED.getCode())
           .orderByAsc(SysOssFolder::getLevel, SysOssFolder::getOrderNum, SysOssFolder::getCreateTime);
        
        List<SysOssFolderVo> allSharedFolders = baseMapper.selectVoList(lqw);
        
        // 为每个文件夹添加文件数量统计（所有用户的文件总数）
        for (SysOssFolderVo folder : allSharedFolders) {
            LambdaQueryWrapper<SysOss> ossLqw = Wrappers.lambdaQuery();
            ossLqw.eq(SysOss::getFolderId, folder.getFolderId())
               .eq(SysOss::getStorageStrategy, FileStorageStrategy.SHARED.getCode());
            Long fileCount = ossMapper.selectCount(ossLqw);
            folder.setFileCount(fileCount);
        }
        
        return allSharedFolders;
    }

    @Override
    public SysOssFolderVo queryById(Long folderId) {
        SysOssFolderVo vo = baseMapper.selectVoById(folderId);
        if (ObjectUtil.isNotNull(vo)) {
            // 查询文件数量
            Long fileCount = baseMapper.countFilesByFolder(folderId, vo.getUserId());
            vo.setFileCount(fileCount);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SysOssFolderBo bo) {
        SysOssFolder folder = BeanCopyUtils.copy(bo, SysOssFolder.class);
        
        // 构建文件夹路径
        String folderPath = buildFolderPath(bo.getParentId(), bo.getFolderName());
        folder.setFolderPath(folderPath);
        
        // 计算层级
        Integer level = calculateLevel(bo.getParentId());
        folder.setLevel(level);
        
        return baseMapper.insert(folder) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFolder(SysOssFolderBo bo, Long userId) {
        // 设置用户ID
        bo.setUserId(userId);
        
        // 检查同级文件夹名称是否重复
        if (checkFolderNameExists(bo.getFolderName(), bo.getParentId(), userId, bo.getStorageStrategy())) {
            throw new ServiceException("同级目录下已存在相同名称的文件夹!");
        }
        
        // 创建数据库记录
        Boolean result = insertByBo(bo);
        
        if (result) {
            try {
                // 在对象存储中创建虚拟文件夹
                OssClient ossClient = OssFactory.instance();
                String folderPath = buildFolderPath(bo.getParentId(), bo.getFolderName());
                FileStorageStrategy strategy = FileStorageStrategy.getByCode(bo.getStorageStrategy());
                ossClient.createFolder(userId, folderPath, strategy);
            } catch (Exception e) {
                throw new ServiceException("创建文件夹失败: " + e.getMessage());
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SysOssFolderBo bo) {
        // 注意：这里不需要特殊保护，因为前端已经过滤了根级节点
        // 如果需要额外的权限控制，可以在这里添加用户权限检查
        
        SysOssFolder folder = BeanCopyUtils.copy(bo, SysOssFolder.class);
        return baseMapper.updateById(folder) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有子文件夹
            for (Long folderId : ids) {
                // 获取文件夹信息
                SysOssFolderVo folder = baseMapper.selectVoById(folderId);
                if (folder == null) {
                    continue;
                }
                
                // 注意：这里不需要特殊保护，因为前端已经过滤了根级节点  
                // 如果需要额外的权限控制，可以在这里添加用户权限检查
                
                LambdaQueryWrapper<SysOssFolder> lqw = Wrappers.lambdaQuery();
                lqw.eq(SysOssFolder::getParentId, folderId);
                Long childCount = baseMapper.selectCount(lqw);
                if (childCount > 0) {
                    throw new ServiceException("存在子文件夹，请先删除子文件夹!");
                }
                
                // 检查是否有文件
                LambdaQueryWrapper<SysOss> ossLqw = Wrappers.lambdaQuery();
                ossLqw.eq(SysOss::getFolderId, folderId);
                Long fileCount = ossMapper.selectCount(ossLqw);
                if (fileCount > 0) {
                    throw new ServiceException("文件夹中存在文件，请先删除文件!");
                }
            }
        }
        
        // 先获取要删除的文件夹信息
        List<SysOssFolderVo> foldersToDelete = ids.stream()
            .map(id -> baseMapper.selectVoById(id))
            .filter(ObjectUtil::isNotNull)
            .collect(Collectors.toList());
        
        // 尝试删除对象存储中的文件夹
        List<String> failedDeletions = new ArrayList<>();
        for (SysOssFolderVo folder : foldersToDelete) {
            try {
                OssClient ossClient = OssFactory.instance();
                FileStorageStrategy strategy = FileStorageStrategy.getByCode(folder.getStorageStrategy());
                ossClient.deleteFolder(folder.getUserId(), folder.getFolderPath(), strategy);
            } catch (Exception e) {
                failedDeletions.add(folder.getFolderName() + ": " + e.getMessage());
            }
        }
        
        // 如果对象存储删除失败，抛出异常
        if (!failedDeletions.isEmpty()) {
            throw new ServiceException("删除对象存储文件夹失败: " + String.join("; ", failedDeletions));
        }
        
        // 只有对象存储删除成功后才删除数据库记录
        Boolean result = baseMapper.deleteBatchIds(ids) > 0;
        
        return result;
    }

    @Override
    public SysOssFolderVo queryByPath(String folderPath, Long userId, String storageStrategy) {
        LambdaQueryWrapper<SysOssFolder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOssFolder::getFolderPath, folderPath)
           .eq(SysOssFolder::getUserId, userId)
           .eq(SysOssFolder::getStorageStrategy, storageStrategy);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SysOssFolder> buildQueryWrapper(SysOssFolderBo bo) {
        LambdaQueryWrapper<SysOssFolder> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFolderName()), SysOssFolder::getFolderName, bo.getFolderName());
        lqw.eq(ObjectUtil.isNotNull(bo.getParentId()), SysOssFolder::getParentId, bo.getParentId());
        lqw.eq(ObjectUtil.isNotNull(bo.getUserId()), SysOssFolder::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getStorageStrategy()), SysOssFolder::getStorageStrategy, bo.getStorageStrategy());
        lqw.orderByAsc(SysOssFolder::getLevel, SysOssFolder::getOrderNum);
        return lqw;
    }

    /**
     * 构建文件夹路径
     */
    private String buildFolderPath(Long parentId, String folderName) {
        if (parentId == null || parentId == 0) {
            return folderName;
        }
        
        SysOssFolderVo parentFolder = baseMapper.selectVoById(parentId);
        if (ObjectUtil.isNull(parentFolder)) {
            throw new ServiceException("父文件夹不存在!");
        }
        
        return parentFolder.getFolderPath() + "/" + folderName;
    }

    /**
     * 计算文件夹层级
     */
    private Integer calculateLevel(Long parentId) {
        if (parentId == null || parentId == 0) {
            return 1;
        }
        
        SysOssFolderVo parentFolder = baseMapper.selectVoById(parentId);
        if (ObjectUtil.isNull(parentFolder)) {
            throw new ServiceException("父文件夹不存在!");
        }
        
        return parentFolder.getLevel() + 1;
    }

    /**
     * 检查文件夹名称是否已存在
     */
    private Boolean checkFolderNameExists(String folderName, Long parentId, Long userId, String storageStrategy) {
        LambdaQueryWrapper<SysOssFolder> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOssFolder::getFolderName, folderName)
           .eq(SysOssFolder::getParentId, parentId)
           .eq(SysOssFolder::getUserId, userId)
           .eq(SysOssFolder::getStorageStrategy, storageStrategy);
        return baseMapper.selectCount(lqw) > 0;
    }
} 
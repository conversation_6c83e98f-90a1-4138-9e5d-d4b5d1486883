package com.cpmes.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.common.helper.LoginHelper;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.domain.InventoryDetail;
import com.cpmes.system.domain.bo.InventoryDetailBo;
import com.cpmes.system.domain.vo.InventoryDetailVo;
import com.cpmes.system.domain.vo.WarehouseZoneVo;
import com.cpmes.system.domain.dto.StorageLocationValidationDto;
import com.cpmes.system.domain.vo.StorageLocationValidationVo;
import com.cpmes.system.mapper.InventoryDetailMapper;
import com.cpmes.system.service.IBinOperationLogService;
import com.cpmes.system.service.IInventoryDetailService;
import com.cpmes.system.service.IInventoryWarehouseSyncService;
import com.cpmes.system.service.IWarehouseZoneService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 库存明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@RequiredArgsConstructor
@Service
public class InventoryDetailServiceImpl implements IInventoryDetailService {

    private final InventoryDetailMapper baseMapper;
    private final IWarehouseZoneService warehouseZoneService;
    private final IBinOperationLogService binOperationLogService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger log = LoggerFactory.getLogger(InventoryDetailServiceImpl.class);

    @Resource
    private IInventoryWarehouseSyncService inventoryWarehouseSyncService;

    /**
     * 安全获取当前用户名
     * @return 用户名，如果获取失败则返回"system"
     */
    private String getSafeUsername() {
        try {
            String username = LoginHelper.getUsername();
            if (StringUtils.isNotBlank(username)) {
                return username;
            }
        } catch (Exception e) {
            log.warn("获取当前登录用户失败，使用系统用户: {}", e.getMessage());
        }
        return "system";
    }

    /**
     * 查询库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public InventoryDetailVo queryById(Long detailId) {
        return baseMapper.selectInventoryDetailById(detailId);
    }

    /**
     * 查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public TableDataInfo<InventoryDetailVo> queryPageList(InventoryDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryDetail> lqw = buildQueryWrapper(bo);
        Page<InventoryDetail> result = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO对象并关联区域和仓库信息
        List<InventoryDetailVo> voList = new ArrayList<>();
        for (InventoryDetail detail : result.getRecords()) {
            InventoryDetailVo vo = baseMapper.selectInventoryDetailById(detail.getDetailId());
            if (vo != null) {
                voList.add(vo);
            }
        }

        TableDataInfo<InventoryDetailVo> tableData = new TableDataInfo<>(voList, result.getTotal());
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<InventoryDetailVo> queryList(InventoryDetailBo bo) {
        InventoryDetail queryEntity = BeanUtil.toBean(bo, InventoryDetail.class);
        return baseMapper.selectInventoryDetailList(queryEntity);
    }

    /**
     * 根据区域编码查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public TableDataInfo<InventoryDetailVo> queryPageListByZoneCode(String zoneCode, PageQuery pageQuery) {
        // 使用自定义SQL查询
        List<InventoryDetailVo> list = baseMapper.selectInventoryDetailByZoneCode(zoneCode);

        // 手动分页处理
        int total = list.size();
        int pageNum = pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<InventoryDetailVo> pageList = new ArrayList<>();
        if (startIndex < total) {
            pageList = list.subList(startIndex, endIndex);
        }

        TableDataInfo<InventoryDetailVo> tableData = new TableDataInfo<>(pageList, (long) total);
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 根据区域ID查询库存明细列表（保留兼容性）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public TableDataInfo<InventoryDetailVo> queryPageListByZone(Long zoneId, PageQuery pageQuery) {
        // 使用自定义SQL查询 - 注意：这里需要转换为zoneCode
        // TODO: 需要修改接口参数为zoneCode
        List<InventoryDetailVo> list = baseMapper.selectInventoryDetailByZoneCode(String.valueOf(zoneId));

        // 手动分页
        int total = list.size();
        int startIndex = (pageQuery.getPageNum() - 1) * pageQuery.getPageSize();
        int endIndex = Math.min(startIndex + pageQuery.getPageSize(), total);

        List<InventoryDetailVo> pageList = startIndex < total ?
            list.subList(startIndex, endIndex) : new ArrayList<>();

        TableDataInfo<InventoryDetailVo> tableData = new TableDataInfo<>(pageList, (long) total);
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 新增库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(InventoryDetailBo bo) {
        InventoryDetail add = BeanUtil.toBean(bo, InventoryDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDetailId(add.getDetailId());

            // 记录新增库存的操作日志
            try {
                binOperationLogService.recordOperationLog(
                    add.getZoneCode(),           // 区域编码
                    "inbound",                   // 操作类型：入库
                    add.getMaterialId(),         // 物料ID
                    add.getMaterialName(),       // 物料名称
                    0,                           // 操作前数量（新增时为0）
                    add.getCurrentStock() != null ? add.getCurrentStock() : 0,  // 数量变化
                    add.getCurrentStock() != null ? add.getCurrentStock() : 0,  // 操作后数量
                    add.getBatchNo(),            // 批次号
                    "INVENTORY_ADD",             // 来源单据号
                    getSafeUsername(),           // 操作人员
                    "新增库存明细",               // 操作原因
                    String.format("新增库存明细 - 物料类型: %s, 计量单位: %s, 备注: %s",
                            add.getMaterialType() != null ? add.getMaterialType() : "未指定",
                            add.getUnit() != null ? add.getUnit() : "未指定",
                            add.getRemark() != null ? add.getRemark() : "无")  // 备注
                );
                log.info("新增库存操作日志记录成功 - 明细ID: {}, 物料: {}, 数量: {}",
                        add.getDetailId(), add.getMaterialName(), add.getCurrentStock());
            } catch (Exception e) {
                log.warn("记录新增库存操作日志失败: {}", e.getMessage());
                // 不阻断主业务流程，操作日志失败不影响主要业务
            }

            // 同步到仓储管理系统
            try {
                inventoryWarehouseSyncService.syncInventoryDetailOperation(add, "INSERT");
                log.info("库存明细新增同步成功 - 物料ID: {}, 物料名称: {}", add.getMaterialId(), add.getMaterialName());
            } catch (Exception e) {
                log.error("库存明细新增同步失败 - 物料ID: {}, 物料名称: {}", add.getMaterialId(), add.getMaterialName(), e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存明细新增同步失败: " + e.getMessage(), e);
            }
        }
        return flag;
    }

    /**
     * 修改库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(InventoryDetailBo bo) {
        InventoryDetail update = BeanUtil.toBean(bo, InventoryDetail.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;

        if (flag) {
            // 同步到仓储管理系统
            try {
                inventoryWarehouseSyncService.syncInventoryDetailOperation(update, "UPDATE");
                log.info("库存明细修改同步成功 - 物料ID: {}, 物料名称: {}", update.getMaterialId(), update.getMaterialName());
            } catch (Exception e) {
                log.error("库存明细修改同步失败 - 物料ID: {}, 物料名称: {}", update.getMaterialId(), update.getMaterialName(), e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存明细修改同步失败: " + e.getMessage(), e);
            }
        }

        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryDetail entity) {
        // 校验区域状态
        if (StringUtils.isNotBlank(entity.getZoneCode())) {
            WarehouseZoneVo zone = warehouseZoneService.queryByZoneCode(entity.getZoneCode());
            if (zone == null) {
                throw new ServiceException("所选区域不存在");
            }
            if ("0".equals(zone.getStatus())) {
                throw new ServiceException("所选区域已停用，无法进行库存操作");
            }
            if ("0".equals(zone.getWarehouseStatus())) {
                throw new ServiceException("所选区域的仓库已停用，无法进行库存操作");
            }
        }

        // 校验物料名称+物料类型+批次号+存储区域的组合唯一性
        if (StringUtils.isNotBlank(entity.getMaterialName()) &&
            StringUtils.isNotBlank(entity.getMaterialType()) &&
            StringUtils.isNotBlank(entity.getZoneCode())) {

            boolean isUnique = checkInventoryDetailUniqueByCode(
                entity.getMaterialName(),
                entity.getMaterialType(),
                entity.getBatchNo(),
                entity.getZoneCode(),
                entity.getDetailId()
            );

            if (!isUnique) {
                throw new ServiceException(String.format(
                    "库存明细重复：物料名称[%s]+物料类型[%s]+批次号[%s]+存储区域的组合已存在",
                    entity.getMaterialName(),
                    entity.getMaterialType(),
                    entity.getBatchNo() != null ? entity.getBatchNo() : "无"
                ));
            }
        }

        // 物料ID校验（使用编码）
        if (StringUtils.isNotBlank(entity.getMaterialId()) && StringUtils.isNotBlank(entity.getZoneCode())) {
            InventoryDetailVo existingDetail = baseMapper.selectByMaterialAndZoneCode(
                entity.getMaterialId(),
                entity.getZoneCode(),
                entity.getBatchNo()
            );

            if (existingDetail != null) {
                // 如果是更新操作，排除自身
                if (entity.getDetailId() == null || !existingDetail.getDetailId().equals(entity.getDetailId())) {
                    throw new ServiceException(String.format(
                        "库存明细重复：物料ID[%s]在区域[%s]批次[%s]的组合已存在",
                        entity.getMaterialId(),
                        entity.getZoneCode(),
                        entity.getBatchNo() != null ? entity.getBatchNo() : "无"
                    ));
                }
            }
        }
    }

    /**
     * 批量删除库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }

        // 在删除前获取要删除的记录，用于同步
        List<InventoryDetail> toDeleteDetails = new ArrayList<>();
        for (Long id : ids) {
            InventoryDetail detail = baseMapper.selectById(id);
            if (detail != null) {
                toDeleteDetails.add(detail);
            }
        }

        boolean flag = baseMapper.deleteBatchIds(ids) > 0;

        if (flag && !toDeleteDetails.isEmpty()) {
            // 同步删除操作到仓储管理系统
            try {
                for (InventoryDetail detail : toDeleteDetails) {
                    inventoryWarehouseSyncService.syncInventoryDetailOperation(detail, "DELETE");
                }
                log.info("库存明细批量删除同步成功 - 删除数量: {}", toDeleteDetails.size());
            } catch (Exception e) {
                log.error("库存明细批量删除同步失败 - 删除数量: {}", toDeleteDetails.size(), e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存明细批量删除同步失败: " + e.getMessage(), e);
            }
        }

        return flag;
    }

    /**
     * 获取库存统计信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Map<String, Object> getInventoryStatistics(InventoryDetailBo bo) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(bo.getMaterialType())) {
            params.put("materialType", bo.getMaterialType());
        }
        if (StringUtils.isNotBlank(bo.getZoneCode())) {
            params.put("zoneCode", bo.getZoneCode());
        }
        if (StringUtils.isNotBlank(bo.getWarehouseType())) {
            params.put("warehouseType", bo.getWarehouseType());
        }

        return baseMapper.selectInventoryStatistics(params);
    }

    /**
     * 获取低库存预警列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<InventoryDetailVo> getLowStockAlert() {
        return baseMapper.selectLowStockAlert();
    }

    /**
     * 库存调整
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustInventoryStock(Long detailId, Integer adjustQuantity, String adjustType, String adjustReason, String remark) {
        // 默认为调整操作
        return adjustInventoryStock(detailId, adjustQuantity, adjustType, adjustReason, remark, "adjust", "INVENTORY_ADJUST");
    }

    /**
     * 库存调整（支持指定操作类型和来源单据号）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustInventoryStock(Long detailId, Integer adjustQuantity, String adjustType, String adjustReason, String remark, String operationType, String sourceDocument) {
        // 根据调整类型确定调整数量的正负
        Integer finalAdjustQuantity = "decrease".equals(adjustType) ? -adjustQuantity : adjustQuantity;

        // 获取调整前的库存明细信息
        InventoryDetail beforeDetail = baseMapper.selectById(detailId);
        if (beforeDetail == null) {
            throw new RuntimeException("库存明细不存在，无法进行调整");
        }

        // 获取当前登录用户，如果获取失败则使用系统用户
        String operator = getSafeUsername();

        int result = baseMapper.adjustInventoryStock(detailId, finalAdjustQuantity, getSafeUsername());

        if (result > 0) {
            // 获取调整后的库存明细信息
            InventoryDetail afterDetail = baseMapper.selectById(detailId);

            // 记录操作日志
            try {
                binOperationLogService.recordOperationLog(
                    beforeDetail.getZoneCode(),
                    operationType,
                    beforeDetail.getMaterialId(),
                    beforeDetail.getMaterialName(),
                    beforeDetail.getCurrentStock(),
                    finalAdjustQuantity,
                    afterDetail.getCurrentStock(),
                    beforeDetail.getBatchNo(),
                    sourceDocument,
                    operator,
                    adjustReason,
                    remark
                );
            } catch (Exception e) {
                log.warn("记录库存调整操作日志失败: {}", e.getMessage());
                // 不阻断主业务流程
            }

            // 同步调整操作到仓储管理系统
            try {
                String operation = finalAdjustQuantity > 0 ? "ADJUST_INCREASE" : "ADJUST_DECREASE";

                // 修复：直接传递调整后的完整库存明细信息，而不是累加数量
                inventoryWarehouseSyncService.syncInventoryDetailOperation(afterDetail, operation);
                log.info("库存调整同步成功 - 物料ID: {}, 调整数量: {}", afterDetail.getMaterialId(), finalAdjustQuantity);
            } catch (Exception e) {
                log.error("库存调整同步失败 - 物料ID: {}, 调整数量: {}", afterDetail.getMaterialId(), finalAdjustQuantity, e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存调整同步失败: " + e.getMessage(), e);
            }
        }

        return result > 0;
    }

    /**
     * 库存移库操作（使用区域编码）
     */
    @Override
    @DS("master") // 使用PostgreSQL主数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean moveInventoryStockByCode(Long sourceDetailId, String targetZoneCode, Integer moveQuantity, String moveReason) {
        // 获取当前登录用户，如果获取失败则使用系统用户
        String operator = getSafeUsername();

        // 1. 获取源库存的完整信息，为同步做准备
        InventoryDetailVo sourceDetailVo = baseMapper.selectInventoryDetailById(sourceDetailId);
        if (sourceDetailVo == null) {
            throw new ServiceException("源库存信息不存在");
        }

        // 2. 校验源区域和目标区域状态
        WarehouseZoneVo sourceZone = warehouseZoneService.queryByZoneCode(sourceDetailVo.getZoneCode());
        if (sourceZone == null) {
            throw new ServiceException("源区域不存在");
        }
        if ("0".equals(sourceZone.getStatus())) {
            throw new ServiceException("源区域已停用，无法进行移库操作");
        }
        if ("0".equals(sourceZone.getWarehouseStatus())) {
            throw new ServiceException("源区域的仓库已停用，无法进行移库操作");
        }

        WarehouseZoneVo targetZone = warehouseZoneService.queryByZoneCode(targetZoneCode);
        if (targetZone == null) {
            throw new ServiceException("目标区域不存在");
        }
        if ("0".equals(targetZone.getStatus())) {
            throw new ServiceException("目标区域已停用，无法进行移库操作");
        }
        if ("0".equals(targetZone.getWarehouseStatus())) {
            throw new ServiceException("目标区域的仓库已停用，无法进行移库操作");
        }

        // 3. 扣减源库存
        int decreaseResult = baseMapper.decreaseSourceStock(sourceDetailId, null, moveQuantity, operator);
        if (decreaseResult == 0) {
            throw new ServiceException("扣减源库存失败，请检查库存数量是否充足");
        }

        // 4. 查询目标区域是否已存在相同物料的库存
        InventoryDetailVo targetDetailVo = baseMapper.selectByMaterialAndZoneCode(
            sourceDetailVo.getMaterialId(),
            targetZoneCode,
            sourceDetailVo.getBatchNo()
        );

        InventoryDetail targetDetail = null;
        if (targetDetailVo != null) {
            // 5a. 如果目标区域已存在，则更新库存
            baseMapper.increaseTargetStock(targetDetailVo.getDetailId(), sourceDetailId, moveQuantity, operator);
            targetDetail = BeanUtil.copyProperties(targetDetailVo, InventoryDetail.class);
            targetDetail.setCurrentStock(targetDetail.getCurrentStock() + moveQuantity);
        } else {
            // 5b. 如果目标区域不存在，则创建新的库存明细
            InventoryDetail newDetail = BeanUtil.copyProperties(sourceDetailVo, InventoryDetail.class);
            newDetail.setDetailId(null); // 清除ID，让数据库自动生成
            newDetail.setZoneCode(targetZoneCode);
            newDetail.setCurrentStock(moveQuantity);
            newDetail.setInboundQuantity(moveQuantity); // 本次移库作为入库
            newDetail.setOutboundQuantity(0);
            newDetail.setStockQuantity(0); // 结存数量默认为0
            newDetail.setCreateBy(operator);
            newDetail.setUpdateBy(operator);
            newDetail.setRemark("从库存明细ID:" + sourceDetailId + "移入(数量:" + moveQuantity + "); " + moveReason);

            baseMapper.insertNewTargetStock(newDetail);
            targetDetail = newDetail;
        }

        // 记录操作日志 - 源区域出库
        try {
            binOperationLogService.recordOperationLog(
                sourceDetailVo.getZoneCode(),
                "outbound",
                sourceDetailVo.getMaterialId(),
                sourceDetailVo.getMaterialName(),
                sourceDetailVo.getCurrentStock(),
                -moveQuantity,
                sourceDetailVo.getCurrentStock() - moveQuantity,
                sourceDetailVo.getBatchNo(),
                "INVENTORY_TRANSFER_OUT",
                operator,
                "移库到区域: " + targetZoneCode + ", 原因: " + moveReason,
                "移库操作"
            );

            // 记录操作日志 - 目标区域入库
            binOperationLogService.recordOperationLog(
                targetZoneCode,
                "inbound",
                sourceDetailVo.getMaterialId(),
                sourceDetailVo.getMaterialName(),
                targetDetailVo != null ? targetDetailVo.getCurrentStock() : 0,
                moveQuantity,
                (targetDetailVo != null ? targetDetailVo.getCurrentStock() : 0) + moveQuantity,
                sourceDetailVo.getBatchNo(),
                "INVENTORY_TRANSFER_IN",
                operator,
                "移库来源区域: " + sourceDetailVo.getZoneCode() + ", 原因: " + moveReason,
                "移库操作"
            );
        } catch (Exception e) {
            log.warn("记录库存移库操作日志失败: {}", e.getMessage());
            // 不阻断主业务流程
        }

        // 移库操作不再同步到仓储管理系统
        log.info("库存移库操作完成，不同步到仓储管理系统 - 物料ID: {}, 移库数量: {}", sourceDetailVo.getMaterialId(), moveQuantity);

        return true;
    }

    /**
     * 库存移库操作（保留兼容性）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean moveInventoryStock(Long sourceDetailId, Long targetZoneId, Integer moveQuantity, String moveReason) {
        // 获取当前登录用户，如果获取失败则使用系统用户
        String operator = getSafeUsername();

        // 1. 获取源库存的完整信息，为同步做准备
        InventoryDetailVo sourceDetailVo = baseMapper.selectInventoryDetailById(sourceDetailId);
        if (sourceDetailVo == null) {
            throw new ServiceException("源库存信息不存在");
        }

        // 2. 扣减源库存
        int decreaseResult = baseMapper.decreaseSourceStock(sourceDetailId, targetZoneId, moveQuantity, operator);
        if (decreaseResult == 0) {
            throw new ServiceException("扣减源库存失败，请检查库存数量是否充足");
        }

        // 3. 查询目标区域是否已存在相同物料的库存
        // TODO: 需要将targetZoneId转换为zoneCode
        InventoryDetailVo targetDetailVo = baseMapper.selectByMaterialAndZoneCode(
            sourceDetailVo.getMaterialId(),
            String.valueOf(targetZoneId), // 临时转换，需要修改接口
            sourceDetailVo.getBatchNo()
        );

        InventoryDetail targetDetail = null;
        if (targetDetailVo != null) {
            // 4a. 如果目标区域已存在，则更新库存
            baseMapper.increaseTargetStock(targetDetailVo.getDetailId(), sourceDetailId, moveQuantity, operator);
            targetDetail = BeanUtil.copyProperties(targetDetailVo, InventoryDetail.class);
            targetDetail.setCurrentStock(targetDetail.getCurrentStock() + moveQuantity);
        } else {
            // 4b. 如果目标区域不存在，则创建新的库存明细
            InventoryDetail newDetail = BeanUtil.copyProperties(sourceDetailVo, InventoryDetail.class);
            newDetail.setDetailId(null); // 清除ID，让数据库自动生成
            newDetail.setZoneCode(String.valueOf(targetZoneId)); // 临时转换，需要修改接口
            newDetail.setCurrentStock(moveQuantity);
            newDetail.setInboundQuantity(moveQuantity); // 本次移库作为入库
            newDetail.setOutboundQuantity(0);
            newDetail.setStockQuantity(0); // 结存数量默认为0
            newDetail.setCreateBy(operator);
            newDetail.setUpdateBy(operator);
            newDetail.setRemark("从库存明细ID:" + sourceDetailId + "移入(数量:" + moveQuantity + "); " + moveReason);

            baseMapper.insertNewTargetStock(newDetail);
            targetDetail = newDetail;
        }

        // 移库操作不再同步到仓储管理系统
        log.info("库存移库操作完成，不同步到仓储管理系统 - 物料ID: {}, 移库数量: {}", sourceDetailVo.getMaterialId(), moveQuantity);

        return true;
    }

    /**
     * 批量更新库存状态
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateStatus(List<Long> detailIds, String status) {
        // 获取当前登录用户，如果获取失败则使用系统用户
        String operator = getSafeUsername();
        int result = baseMapper.batchUpdateStatus(detailIds, status, operator);
        return result > 0;
    }

    /**
     * 获取库存追溯信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<Map<String, Object>> getInventoryTrace(Long detailId) {
        return baseMapper.selectInventoryTrace(detailId);
    }

    /**
     * 根据物料和区域查询库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public InventoryDetailVo queryByMaterialAndZone(String materialId, Long zoneId, String batchNo) {
        // TODO: 需要修改接口参数为zoneCode
        return baseMapper.selectByMaterialAndZoneCode(materialId, String.valueOf(zoneId), batchNo);
    }

    /**
     * 校验库存明细唯一性（使用编码）
     */
    public boolean checkInventoryDetailUniqueByCode(String materialName, String materialType, String batchNo, String zoneCode, Long detailId) {
        InventoryDetailVo existingDetail = baseMapper.selectByMaterialNameTypeAndZoneCode(materialName, materialType, batchNo, zoneCode);

        if (existingDetail == null) {
            return true; // 没有重复，唯一
        }

        // 如果是更新操作，排除自身
        if (detailId != null && existingDetail.getDetailId().equals(detailId)) {
            return true; // 是自身记录，唯一
        }

        return false; // 存在重复
    }

    /**
     * 校验物料ID唯一性
     * 注意：此方法保留用于自动生成模式的全局唯一性校验
     * 手动输入模式应使用 checkMaterialIdUniqueInContext 方法
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean checkMaterialIdUnique(String materialId) {
        if (StringUtils.isBlank(materialId)) {
            return false;
        }

        try {
            // 查询是否存在相同物料ID的记录
            LambdaQueryWrapper<InventoryDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InventoryDetail::getMaterialId, materialId);
            queryWrapper.eq(InventoryDetail::getDelFlag, "0"); // 只查询未删除的记录

            long count = baseMapper.selectCount(queryWrapper);
            return count == 0; // 返回true表示唯一（不存在重复），false表示重复
        } catch (Exception e) {
            log.error("校验物料ID唯一性失败 - materialId: {}", materialId, e);
            return false; // 发生异常时返回false，表示校验失败
        }
    }

    /**
     * 校验物料ID在特定上下文中的唯一性
     * 基于物料ID + 区域编码 + 批次号的组合唯一性校验
     *
     * @param materialId 物料ID
     * @param zoneCode 区域编码
     * @param batchNo 批次号（可选）
     * @param detailId 当前记录ID（更新时排除自身）
     * @return true表示唯一，false表示重复
     */
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean checkMaterialIdUniqueInContext(String materialId, String zoneCode, String batchNo, Long detailId) {
        if (StringUtils.isBlank(materialId) || StringUtils.isBlank(zoneCode)) {
            return false;
        }

        try {
            log.info("校验物料ID上下文唯一性 - 物料ID: {}, 区域: {}, 批次: {}, 排除记录ID: {}",
                    materialId, zoneCode, batchNo, detailId);

            // 查询是否存在相同 物料ID + 区域编码 + 批次号 的记录
            LambdaQueryWrapper<InventoryDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InventoryDetail::getMaterialId, materialId);
            queryWrapper.eq(InventoryDetail::getZoneCode, zoneCode);

            // 批次号校验：如果提供了批次号，则精确匹配；如果没有提供，则匹配空值
            if (StringUtils.isNotBlank(batchNo)) {
                queryWrapper.eq(InventoryDetail::getBatchNo, batchNo);
            } else {
                queryWrapper.and(wrapper -> wrapper.isNull(InventoryDetail::getBatchNo)
                        .or().eq(InventoryDetail::getBatchNo, ""));
            }

            queryWrapper.eq(InventoryDetail::getDelFlag, "0"); // 只查询未删除的记录

            // 如果是更新操作，排除自身记录
            if (detailId != null) {
                queryWrapper.ne(InventoryDetail::getDetailId, detailId);
            }

            long count = baseMapper.selectCount(queryWrapper);
            boolean isUnique = count == 0;

            log.info("物料ID上下文唯一性校验结果 - 物料ID: {}, 是否唯一: {}, 找到记录数: {}",
                    materialId, isUnique, count);

            return isUnique; // 返回true表示唯一（不存在重复），false表示重复
        } catch (Exception e) {
            log.error("校验物料ID上下文唯一性失败 - materialId: {}, zoneCode: {}, batchNo: {}",
                    materialId, zoneCode, batchNo, e);
            return false; // 发生异常时返回false，表示校验失败
        }
    }

    /**
     * 生成下一个可用的物料ID
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public String generateNextMaterialId(String materialType) {
        log.info("开始生成物料ID - 物料类型: {}", materialType);

        if (StringUtils.isBlank(materialType)) {
            log.error("物料类型参数为空");
            throw new IllegalArgumentException("物料类型不能为空");
        }

        try {
            // 根据物料类型确定前缀和序列名称
            String prefix;
            String sequenceName;

            switch (materialType) {
                case "原料":
                    prefix = "RM";
                    sequenceName = "storage.material_id_seq_rm";
                    break;
                case "零部件":
                    prefix = "CP";
                    sequenceName = "storage.material_id_seq_cp";
                    break;
                case "成品":
                    prefix = "PD";
                    sequenceName = "storage.material_id_seq_pd";
                    break;
                case "一级半成品":
                    prefix = "SF1";
                    sequenceName = "storage.material_id_seq_sf1";
                    break;
                case "二级半成品":
                    prefix = "SF2";
                    sequenceName = "storage.material_id_seq_sf2";
                    break;
                default:
                    log.error("不支持的物料类型: {}", materialType);
                    throw new IllegalArgumentException("不支持的物料类型: " + materialType);
            }

            log.info("物料类型映射 - 类型: {}, 前缀: {}, 序列: {}", materialType, prefix, sequenceName);

            // 使用数据库序列生成下一个序号，确保并发安全
            Long nextSequence = getNextSequenceValue(sequenceName);
            log.info("序列值获取成功 - 序列: {}, 值: {}", sequenceName, nextSequence);

            // 根据物料类型格式化ID
            String materialId;
            if ("一级半成品".equals(materialType) || "二级半成品".equals(materialType)) {
                // SF1/SF2 + 7位数字 (如: SF1000001, SF2000001)
                materialId = String.format("%s%06d", prefix, nextSequence);
            } else {
                // RM/CP/PD + 6位数字 (如: RM000001, CP000001, PD000001)
                materialId = String.format("%s%06d", prefix, nextSequence);
            }

            log.info("生成物料ID成功 - 物料类型: {}, 生成ID: {}", materialType, materialId);
            return materialId;

        } catch (IllegalArgumentException e) {
            // 参数错误，直接抛出
            throw e;
        } catch (Exception e) {
            log.error("生成物料ID失败 - 物料类型: {}, 错误: {}", materialType, e.getMessage(), e);
            throw new RuntimeException("生成物料ID失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取数据库序列的下一个值
     */
    private Long getNextSequenceValue(String sequenceName) {
        try {
            log.info("开始获取序列值 - 序列名: {}", sequenceName);

            // 首先检查序列是否存在
            String checkSql = "SELECT 1 FROM information_schema.sequences WHERE sequence_schema = 'storage' AND sequence_name = '" +
                              sequenceName.substring(sequenceName.lastIndexOf('.') + 1) + "'";
            log.info("检查序列是否存在 - SQL: {}", checkSql);

            // 使用原生SQL查询序列的下一个值
            String sql = "SELECT nextval('" + sequenceName + "')";
            log.info("获取序列值 - SQL: {}", sql);

            Long sequenceValue = baseMapper.selectNextSequenceValue(sql);

            if (sequenceValue == null) {
                log.error("序列值为null - 序列名: {}", sequenceName);
                throw new RuntimeException("序列值为null，可能序列不存在: " + sequenceName);
            }

            log.info("序列值获取成功 - 序列名: {}, 值: {}", sequenceName, sequenceValue);
            return sequenceValue;

        } catch (Exception e) {
            log.error("获取序列值失败 - 序列名: {}, 错误类型: {}, 错误消息: {}",
                     sequenceName, e.getClass().getSimpleName(), e.getMessage(), e);

            // 检查是否是序列不存在的错误
            if (e.getMessage() != null && e.getMessage().contains("does not exist")) {
                throw new RuntimeException("数据库序列不存在: " + sequenceName + "，请先执行序列初始化脚本", e);
            }

            throw new RuntimeException("获取序列值失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验库存明细唯一性（兼容旧接口）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public boolean checkInventoryDetailUnique(String materialName, String materialType, String batchNo, Long zoneId, Long detailId) {
        // TODO: 需要将zoneId转换为zoneCode
        InventoryDetailVo existingDetail = baseMapper.selectByMaterialNameTypeAndZoneCode(materialName, materialType, batchNo, String.valueOf(zoneId));

        if (existingDetail == null) {
            return true; // 没有重复，唯一
        }

        // 如果是更新操作，排除自身
        if (detailId != null && existingDetail.getDetailId().equals(detailId)) {
            return true; // 是自身记录，唯一
        }

        return false; // 存在重复
    }

    private LambdaQueryWrapper<InventoryDetail> buildQueryWrapper(InventoryDetailBo bo) {
        Map<String, Object> params = new HashMap<>();
        LambdaQueryWrapper<InventoryDetail> lqw = Wrappers.lambdaQuery();

        lqw.eq(StringUtils.isNotBlank(bo.getZoneCode()), InventoryDetail::getZoneCode, bo.getZoneCode());
        lqw.eq(bo.getMaterialId() != null, InventoryDetail::getMaterialId, bo.getMaterialId());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialType()), InventoryDetail::getMaterialType, bo.getMaterialType());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialName()), InventoryDetail::getMaterialName, bo.getMaterialName());
        lqw.like(StringUtils.isNotBlank(bo.getBatchNo()), InventoryDetail::getBatchNo, bo.getBatchNo());
        lqw.like(StringUtils.isNotBlank(bo.getSourceType()), InventoryDetail::getSourceType, bo.getSourceType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InventoryDetail::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getNeedPurchase()), InventoryDetail::getNeedPurchase, bo.getNeedPurchase());
        lqw.between(bo.getStartDate() != null && bo.getEndDate() != null,
            InventoryDetail::getEntryDate, bo.getStartDate(), bo.getEndDate());

        // 新增字段查询条件
        lqw.like(StringUtils.isNotBlank(bo.getProductNumber()), InventoryDetail::getProductNumber, bo.getProductNumber());
        lqw.eq(bo.getSeriesId() != null, InventoryDetail::getSeriesId, bo.getSeriesId());
        lqw.eq(bo.getSupplierId() != null, InventoryDetail::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), InventoryDetail::getSupplierName, bo.getSupplierName());
        lqw.like(StringUtils.isNotBlank(bo.getBoardType()), InventoryDetail::getBoardType, bo.getBoardType());
        lqw.eq(bo.getStyleId() != null, InventoryDetail::getStyleId, bo.getStyleId());

        // 添加排序逻辑
        if (StringUtils.isNotBlank(bo.getSortField()) && StringUtils.isNotBlank(bo.getSortOrder())) {
            boolean isAsc = "asc".equalsIgnoreCase(bo.getSortOrder());
            switch (bo.getSortField()) {
                case "currentStock":
                    lqw.orderBy(true, isAsc, InventoryDetail::getCurrentStock);
                    break;
                case "inboundQuantity":
                    lqw.orderBy(true, isAsc, InventoryDetail::getInboundQuantity);
                    break;
                case "outboundQuantity":
                    lqw.orderBy(true, isAsc, InventoryDetail::getOutboundQuantity);
                    break;
                case "stockQuantity":
                    lqw.orderBy(true, isAsc, InventoryDetail::getStockQuantity);
                    break;
                case "minStockQuantity":
                    lqw.orderBy(true, isAsc, InventoryDetail::getMinStockQuantity);
                    break;
                case "entryDate":
                    lqw.orderBy(true, isAsc, InventoryDetail::getEntryDate);
                    break;
                case "updateTime":
                    lqw.orderBy(true, isAsc, InventoryDetail::getUpdateTime);
                    break;
                default:
                    // 默认按更新时间降序排序
                    lqw.orderByDesc(InventoryDetail::getUpdateTime);
                    break;
            }
        } else {
            // 默认按更新时间降序排序
            lqw.orderByDesc(InventoryDetail::getUpdateTime);
        }

        return lqw;
    }

    /**
     * 根据物料和区域编码查询库存明细
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public InventoryDetailVo queryByMaterialAndZoneCode(String materialId, String zoneCode, String batchNo) {
        return baseMapper.selectByMaterialAndZoneCode(materialId, zoneCode, batchNo);
    }

    /**
     * 根据产品编码查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<InventoryDetailVo> queryByProductNumber(String productNumber) {
        return baseMapper.selectInventoryDetailByProductNumber(productNumber);
    }

    /**
     * 根据系列ID查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<InventoryDetailVo> queryBySeriesId(Integer seriesId) {
        return baseMapper.selectInventoryDetailBySeriesId(seriesId);
    }

    /**
     * 根据供应商ID查询库存明细列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<InventoryDetailVo> queryBySupplierId(Integer supplierId) {
        return baseMapper.selectInventoryDetailBySupplierId(supplierId);
    }

    /**
     * 检查数据同步状态
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Map<String, Object> checkSyncStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 统计各类型物料数量
            Map<String, Object> materialStats = new HashMap<>();
            materialStats.put("零部件", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "零部件")
                .eq(InventoryDetail::getCreateBy, "system")));
            materialStats.put("原料", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "原料")
                .eq(InventoryDetail::getCreateBy, "system")));
            materialStats.put("成品", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "成品")
                .eq(InventoryDetail::getCreateBy, "system")));
            materialStats.put("一级半成品", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "一级半成品")
                .eq(InventoryDetail::getCreateBy, "system")));
            materialStats.put("二级半成品", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "二级半成品")
                .eq(InventoryDetail::getCreateBy, "system")));

            // 检查新增字段数据完整性
            Map<String, Object> fieldStats = new HashMap<>();
            fieldStats.put("缺失产品编码数量", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .isNull(InventoryDetail::getProductNumber)
                .eq(InventoryDetail::getCreateBy, "system")));
            fieldStats.put("成品缺失系列ID数量", baseMapper.selectCount(Wrappers.<InventoryDetail>lambdaQuery()
                .eq(InventoryDetail::getMaterialType, "成品")
                .isNull(InventoryDetail::getSeriesId)
                .eq(InventoryDetail::getCreateBy, "system")));

            result.put("materialStats", materialStats);
            result.put("fieldStats", fieldStats);
            result.put("syncTime", LocalDateTime.now());
            result.put("status", "success");

        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", e.getMessage());
        }

        return result;
    }

    /**
     * 判断物料是否适合存放在指定区域（使用区域编码）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean validateMaterialZoneCompatibilityByCode(String materialId, String materialType, String zoneCode, String batchNo) {
        try {
            // 1. 检查区域是否存在且启用
            WarehouseZoneVo zone = warehouseZoneService.queryByZoneCode(zoneCode);
            if (zone == null || !"1".equals(zone.getStatus())) {
                return false; // 区域不存在或未启用
            }

            // 2. 检查物料类型与仓库类型的兼容性
            String warehouseType = zone.getWarehouseType();
            if (!isCompatibleMaterialType(materialType, warehouseType)) {
                return false; // 物料类型与仓库类型不兼容
            }

            // 3. 检查区域容量限制
            if (zone.getZoneCapacity() != null && zone.getZoneCapacity() > 0) {
                Integer currentUsage = zone.getCurrentUsage() != null ? zone.getCurrentUsage() : 0;
                if (currentUsage >= zone.getZoneCapacity()) {
                    return false; // 区域已满
                }
            }

            // 4. 检查是否已存在相同物料的库存（批次兼容性）
            InventoryDetailVo existingDetail = baseMapper.selectByMaterialAndZoneCode(materialId, zoneCode, batchNo);
            if (existingDetail != null) {
                // 如果已存在相同物料和批次，则可以合并存放
                return true;
            }

            // 5. 其他业务规则检查（可根据需要扩展）
            return true;

        } catch (Exception e) {
            // 发生异常时，为安全起见返回false
            return false;
        }
    }

    /**
     * 判断物料是否适合存放在指定区域（保留兼容性）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean validateMaterialZoneCompatibility(String materialId, String materialType, Long zoneId, String batchNo) {
        // TODO: 需要将zoneId转换为zoneCode
        return validateMaterialZoneCompatibilityByCode(materialId, materialType, String.valueOf(zoneId), batchNo);
    }

    /**
     * 检查物料类型与仓库类型的兼容性
     */
    private boolean isCompatibleMaterialType(String materialType, String warehouseType) {
        if (StringUtils.isBlank(materialType) || StringUtils.isBlank(warehouseType)) {
            return false;
        }

        // 根据业务规则定义兼容性
        switch (warehouseType) {
            case "1": // 原料仓库
            case "raw_material":
                return "raw_material".equals(materialType) || "原料".equals(materialType);
            case "2": // 零部件仓库
            case "component":
                return "component".equals(materialType) || "零部件".equals(materialType);
            case "3": // 半成品仓库
            case "semi_finished":
                return "semi_finished".equals(materialType) || "半成品".equals(materialType);
            case "4": // 成品仓库
            case "product":
                return "product".equals(materialType) || "成品".equals(materialType);
            default:
                // 未知仓库类型，允许存放
                return true;
        }
    }

    /**
     * 库存调整（扩展方法，支持文档规范中的所有参数）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> adjustInventoryStockExtended(Long detailId, Integer adjustQuantity, String adjustType,
                                                           String adjustReason, String remark, String operationType,
                                                           String sourceDocument, String zoneCode, String itemCode,
                                                           String itemName, String unitOfMeasure, String lotNumber) {
        try {
            log.info("执行扩展库存调整 - detailId: {}, quantity: {}, type: {}, operation: {}",
                    detailId, adjustQuantity, adjustType, operationType);

            InventoryDetail targetDetail = null;
            InventoryDetail beforeDetail = null;
            boolean isNewDetail = false;

            // 如果detailId为null，通过zoneCode和itemCode查找或创建库存明细
            if (detailId == null) {
                if (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode)) {
                    throw new ServiceException("detailId为空时，zoneCode和itemCode不能为空");
                }

                // 智能物料匹配逻辑：优先使用物料ID，备用物料名称+类型组合
                // 注意：这里materialType需要从其他地方获取，暂时使用默认值
                String materialType = "原料"; // 默认物料类型，实际应该从参数传入
                InventoryDetail existingDetail = findExistingInventoryDetail(itemCode, itemName, materialType, zoneCode, lotNumber);

                if (existingDetail != null) {
                    // 找到现有库存明细
                    detailId = existingDetail.getDetailId();
                    beforeDetail = baseMapper.selectById(detailId);
                    log.info("找到现有库存明细 - detailId: {}, materialId: {}, zoneCode: {}, batchNo: {}",
                            detailId, existingDetail.getMaterialId(), zoneCode, existingDetail.getBatchNo());
                } else if ("inbound".equals(operationType)) {
                    // 入库操作且不存在库存明细，创建新的库存明细
                    InventoryDetail newDetail = new InventoryDetail();
                    newDetail.setZoneCode(zoneCode);
                    newDetail.setMaterialId(itemCode);
                    newDetail.setMaterialName(StringUtils.isNotBlank(itemName) ? itemName : itemCode);
                    newDetail.setCurrentStock(adjustQuantity); // 当前库存 = 入库数量（因为结存和出库都是0）
                    newDetail.setInboundQuantity(adjustQuantity); // 入库数量
                    newDetail.setOutboundQuantity(0); // 出库数量初始为0
                    newDetail.setStockQuantity(0); // 结存数量初始为0（表示期初库存）
                    newDetail.setMinStockQuantity(0);
                    newDetail.setUnit(StringUtils.isNotBlank(unitOfMeasure) ? unitOfMeasure : "个");
                    newDetail.setBatchNo(lotNumber);
                    newDetail.setSourceType("manual");
                    newDetail.setSourceNo(sourceDocument);
                    newDetail.setStatus("1");
                    newDetail.setDelFlag("0");
                    newDetail.setCreateBy(getSafeUsername());
                    newDetail.setUpdateBy(getSafeUsername());
                    newDetail.setRemark(remark);

                    int insertResult = baseMapper.insert(newDetail);
                    if (insertResult > 0) {
                        detailId = newDetail.getDetailId();
                        beforeDetail = newDetail;
                        isNewDetail = true;
                        log.info("创建新的库存明细 - ID: {}, 物料: {}, 区域: {}, 入库数量: {}",
                                detailId, itemCode, zoneCode, adjustQuantity);
                    } else {
                        throw new ServiceException("创建库存明细失败");
                    }
                } else {
                    // 出库操作但不存在库存明细
                    throw new ServiceException("出库操作失败：指定区域和物料的库存明细不存在");
                }
            } else {
                // 使用提供的detailId获取库存明细信息
                beforeDetail = baseMapper.selectById(detailId);
                if (beforeDetail == null) {
                    throw new ServiceException("库存明细不存在，ID：" + detailId);
                }
            }

            // 使用扩展参数或从现有记录获取信息
            String finalZoneCode = StringUtils.isNotBlank(zoneCode) ? zoneCode : beforeDetail.getZoneCode();
            String finalItemCode = StringUtils.isNotBlank(itemCode) ? itemCode : beforeDetail.getMaterialId();
            String finalItemName = StringUtils.isNotBlank(itemName) ? itemName : beforeDetail.getMaterialName();
            String finalUnitOfMeasure = StringUtils.isNotBlank(unitOfMeasure) ? unitOfMeasure : beforeDetail.getUnit();
            String finalLotNumber = StringUtils.isNotBlank(lotNumber) ? lotNumber : beforeDetail.getBatchNo();
            String finalSourceDocument = StringUtils.isNotBlank(sourceDocument) ? sourceDocument :
                    ("inbound".equals(operationType) ? "INVENTORY_INBOUND" : "INVENTORY_OUTBOUND");

            // 如果不是新建的库存明细，执行原有的库存调整逻辑
            if (!isNewDetail) {
                boolean adjustResult = adjustInventoryStock(detailId, adjustQuantity, adjustType, adjustReason, remark, operationType, finalSourceDocument);

                if (!adjustResult) {
                    throw new ServiceException("库存调整失败");
                }
            }
            // 新建的库存明细已经在创建时设置了正确的数量，无需额外调整

            // 获取调整后的库存信息
            InventoryDetail afterDetail = baseMapper.selectById(detailId);

            // 同步到仓储管理系统
            try {
                String operation = "increase".equals(adjustType) ? "ADJUST_INCREASE" : "ADJUST_DECREASE";

                // 修复：直接传递调整后的完整库存明细信息，而不是创建新对象
                inventoryWarehouseSyncService.syncInventoryDetailOperation(afterDetail, operation);
                log.info("扩展库存调整同步成功 - 物料ID: {}, 调整数量: {}, 操作: {}",
                        afterDetail.getMaterialId(), adjustQuantity, operation);
            } catch (Exception e) {
                log.error("扩展库存调整同步失败 - 物料ID: {}, 调整数量: {}",
                        afterDetail.getMaterialId(), adjustQuantity, e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存调整同步失败: " + e.getMessage(), e);
            }

            // 记录操作日志（只有新建库存明细时才需要记录，否则已在adjustInventoryStock方法中记录）
            Long operationId = null;
            if (isNewDetail) {
                try {
                    boolean logResult = binOperationLogService.recordOperationLog(
                        finalZoneCode,
                        operationType,
                        finalItemCode,
                        finalItemName,
                        0, // 新建库存明细前库存为0
                        "increase".equals(adjustType) ? adjustQuantity : -adjustQuantity,
                        afterDetail.getCurrentStock(),
                        finalLotNumber,
                        finalSourceDocument,
                        getSafeUsername(),
                        adjustReason,
                        String.format("新建库存明细 - 计量单位: %s, 批次号: %s, 备注: %s",
                                finalUnitOfMeasure, finalLotNumber, remark)
                    );

                    if (logResult) {
                        operationId = System.currentTimeMillis(); // 临时处理，实际应该从插入结果获取
                    }
                } catch (Exception e) {
                    log.warn("记录新建库存明细操作日志失败: {}", e.getMessage());
                    // 不阻断主业务流程
                }
            } else {
                // 非新建库存明细，操作日志已在adjustInventoryStock方法中记录
                operationId = System.currentTimeMillis(); // 临时处理，实际可以从日志服务获取最新的操作ID
            }

            // 构造返回结果，符合文档规范格式
            Map<String, Object> result = new HashMap<>();
            result.put("operationId", operationId);
            result.put("zoneCode", finalZoneCode);
            result.put("finalStock", afterDetail.getCurrentStock());
            result.put("itemCode", finalItemCode);
            result.put("itemName", finalItemName);
            result.put("unitOfMeasure", finalUnitOfMeasure);
            result.put("lotNumber", finalLotNumber);
            result.put("sourceDocument", finalSourceDocument);
            result.put("quantityBefore", isNewDetail ? 0 : beforeDetail.getCurrentStock());
            result.put("quantityAfter", afterDetail.getCurrentStock());
            result.put("quantityChange", "increase".equals(adjustType) ? adjustQuantity : -adjustQuantity);
            result.put("detailId", detailId); // 返回实际使用的detailId
            result.put("isNewDetail", isNewDetail); // 标识是否为新建库存明细

            log.info("扩展库存调整完成 - 操作ID: {}, 最终库存: {}, detailId: {}", operationId, afterDetail.getCurrentStock(), detailId);
            return result;

        } catch (Exception e) {
            log.error("扩展库存调整失败: {}", e.getMessage(), e);
            throw new ServiceException("库存调整失败: " + e.getMessage());
        }
    }

    /**
     * 根据物料ID查询物料在各个区域的分布情况
     * 用于"查看分布"功能
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public TableDataInfo<InventoryDetailVo> queryMaterialZoneDistribution(String materialId, String materialType, PageQuery pageQuery) {
        log.info("开始查询物料区域分布 - 物料ID: {}, 物料类型: {}", materialId, materialType);

        // 构建查询条件
        InventoryDetailBo bo = new InventoryDetailBo();
        bo.setMaterialId(materialId); // 使用物料ID进行精确匹配
        if (StringUtils.isNotBlank(materialType)) {
            bo.setMaterialType(materialType);
        }

        // 构建查询Wrapper
        LambdaQueryWrapper<InventoryDetail> lqw = buildQueryWrapper(bo);

        // 执行分页查询
        Page<InventoryDetail> result = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO对象并关联区域和仓库信息
        List<InventoryDetailVo> voList = new ArrayList<>();
        for (InventoryDetail detail : result.getRecords()) {
            InventoryDetailVo vo = baseMapper.selectInventoryDetailById(detail.getDetailId());
            if (vo != null) {
                voList.add(vo);
            }
        }

        log.info("物料区域分布查询完成 - 物料ID: {}, 找到: {} 条记录", materialId, voList.size());

        TableDataInfo<InventoryDetailVo> tableData = new TableDataInfo<>(voList, result.getTotal());
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 校验物料存放位置是否合规
     */
    @Override
    public StorageLocationValidationVo validateStorageLocation(StorageLocationValidationDto dto) {
        log.info("开始校验物料存放位置 - 物料ID: {}, 仓库类型: {}, 区域编码: {}",
                dto.getMaterialId(), dto.getWarehouseType(), dto.getZoneCode());

        try {
            // 1. 获取物料类型（如果未提供则从数据库查询）
            String materialType = dto.getMaterialType();
            String materialName = null;

            if (StringUtils.isBlank(materialType)) {
                // 从库存明细表中查询物料类型
                InventoryDetailVo existingDetail = queryByMaterialId(dto.getMaterialId());
                if (existingDetail != null) {
                    materialType = existingDetail.getMaterialType();
                    materialName = existingDetail.getMaterialName();
                } else {
                    return StorageLocationValidationVo.failure(
                        "未找到物料信息，请确认物料ID是否正确",
                        "MATERIAL_NOT_FOUND"
                    );
                }
            }

            // 2. 获取区域信息（如果提供了区域编码）
            String warehouseType = dto.getWarehouseType();
            String zoneName = null;

            if (StringUtils.isNotBlank(dto.getZoneCode())) {
                WarehouseZoneVo zoneInfo = warehouseZoneService.queryByZoneCode(dto.getZoneCode());
                if (zoneInfo == null) {
                    return StorageLocationValidationVo.failure(
                        "未找到指定的区域信息",
                        "ZONE_NOT_FOUND"
                    );
                }

                // 如果未提供仓库类型，从区域信息中获取
                if (StringUtils.isBlank(warehouseType)) {
                    warehouseType = zoneInfo.getWarehouseType();
                }

                zoneName = zoneInfo.getZoneName();

                // 检查区域状态
                if (!"1".equals(zoneInfo.getStatus())) {
                    return StorageLocationValidationVo.failure(
                        "选择的区域已停用，无法存放物料",
                        "ZONE_DISABLED"
                    );
                }

                // 检查仓库状态
                if (!"1".equals(zoneInfo.getWarehouseStatus())) {
                    return StorageLocationValidationVo.failure(
                        "选择区域所属的仓库已停用，无法存放物料",
                        "WAREHOUSE_DISABLED"
                    );
                }
            }

            // 3. 执行物料类型与仓库类型的匹配校验
            StorageLocationValidationVo validationResult = validateMaterialWarehouseTypeMatch(
                materialType, warehouseType, dto.getValidationMode()
            );

            // 4. 设置返回信息
            validationResult.setMaterialType(materialType);
            validationResult.setMaterialName(materialName);
            validationResult.setWarehouseType(warehouseType);
            validationResult.setWarehouseTypeName(getWarehouseTypeName(warehouseType));
            validationResult.setZoneCode(dto.getZoneCode());
            validationResult.setZoneName(zoneName);

            log.info("物料存放位置校验完成 - 物料ID: {}, 校验结果: {}, 消息: {}",
                    dto.getMaterialId(), validationResult.getValid(), validationResult.getMessage());

            return validationResult;

        } catch (Exception e) {
            log.error("校验物料存放位置时发生异常 - 物料ID: {}", dto.getMaterialId(), e);
            return StorageLocationValidationVo.failure(
                "校验过程中发生系统错误，请稍后重试",
                "SYSTEM_ERROR"
            );
        }
    }

    /**
     * 校验物料类型与仓库类型的匹配关系
     */
    private StorageLocationValidationVo validateMaterialWarehouseTypeMatch(String materialType, String warehouseType, String validationMode) {
        if (StringUtils.isBlank(materialType) || StringUtils.isBlank(warehouseType)) {
            return StorageLocationValidationVo.failure(
                "物料类型或仓库类型信息不完整",
                "INCOMPLETE_INFO"
            );
        }

        // 定义物料类型与仓库类型的匹配关系
        Map<String, String> materialToWarehouseMapping = new HashMap<>();
        materialToWarehouseMapping.put("成品", "3");      // 成品 -> 成品仓库
        materialToWarehouseMapping.put("原料", "1");      // 原料 -> 原料仓库
        materialToWarehouseMapping.put("半成品", "2");    // 半成品 -> 半成品仓库
        materialToWarehouseMapping.put("零部件", "4");    // 零部件 -> 零件仓库
        materialToWarehouseMapping.put("零件", "4");      // 零件 -> 零件仓库（兼容性）
        materialToWarehouseMapping.put("一级半成品", "2"); // 一级半成品 -> 半成品仓库
        materialToWarehouseMapping.put("二级半成品", "2"); // 二级半成品 -> 半成品仓库

        String expectedWarehouseType = materialToWarehouseMapping.get(materialType);

        if (expectedWarehouseType == null) {
            return StorageLocationValidationVo.failure(
                String.format("未知的物料类型：%s", materialType),
                "UNKNOWN_MATERIAL_TYPE"
            );
        }

        // 严格模式校验
        if ("strict".equals(validationMode)) {
            if (!expectedWarehouseType.equals(warehouseType)) {
                String suggestedLocation = String.format("%s应存放到%s",
                    materialType, getWarehouseTypeName(expectedWarehouseType));
                return StorageLocationValidationVo.failure(
                    String.format("%s类型的物料不能存放到%s，%s",
                        materialType, getWarehouseTypeName(warehouseType), suggestedLocation),
                    "TYPE_MISMATCH",
                    suggestedLocation
                );
            }
        } else if ("loose".equals(validationMode)) {
            // 宽松模式：允许一些兼容性存放
            if (!isCompatibleStorage(materialType, warehouseType)) {
                String suggestedLocation = String.format("%s建议存放到%s",
                    materialType, getWarehouseTypeName(expectedWarehouseType));
                return StorageLocationValidationVo.failure(
                    String.format("%s类型的物料不建议存放到%s，%s",
                        materialType, getWarehouseTypeName(warehouseType), suggestedLocation),
                    "TYPE_NOT_RECOMMENDED",
                    suggestedLocation
                );
            }
        }

        return StorageLocationValidationVo.success(
            String.format("%s类型的物料可以存放到%s", materialType, getWarehouseTypeName(warehouseType))
        );
    }

    /**
     * 检查是否为兼容性存放（宽松模式）
     */
    private boolean isCompatibleStorage(String materialType, String warehouseType) {
        // 定义兼容性规则
        Map<String, List<String>> compatibilityRules = new HashMap<>();
        compatibilityRules.put("半成品", Arrays.asList("2", "1")); // 半成品可以存放到半成品仓库或原料仓库
        compatibilityRules.put("一级半成品", Arrays.asList("2", "1"));
        compatibilityRules.put("二级半成品", Arrays.asList("2", "3")); // 二级半成品可以存放到半成品仓库或成品仓库
        compatibilityRules.put("原料", Arrays.asList("1"));
        compatibilityRules.put("成品", Arrays.asList("3"));
        compatibilityRules.put("零部件", Arrays.asList("4", "1")); // 零部件可以存放到零件仓库或原料仓库
        compatibilityRules.put("零件", Arrays.asList("4", "1")); // 零件可以存放到零件仓库或原料仓库（兼容性）

        List<String> compatibleTypes = compatibilityRules.get(materialType);
        return compatibleTypes != null && compatibleTypes.contains(warehouseType);
    }

    /**
     * 获取仓库类型名称
     */
    private String getWarehouseTypeName(String warehouseType) {
        Map<String, String> typeNames = new HashMap<>();
        typeNames.put("1", "原料仓库");
        typeNames.put("2", "半成品仓库");
        typeNames.put("3", "成品仓库");
        typeNames.put("4", "零件仓库");
        return typeNames.getOrDefault(warehouseType, "未知类型");
    }

    /**
     * 根据物料ID查询库存明细（用于获取物料信息）
     */
    private InventoryDetailVo queryByMaterialId(String materialId) {
        LambdaQueryWrapper<InventoryDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(InventoryDetail::getMaterialId, materialId);
        lqw.eq(InventoryDetail::getDelFlag, "0");
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 智能物料匹配逻辑：查找现有库存明细
     *
     * @param itemCode 物料编码
     * @param itemName 物料名称
     * @param materialType 物料类型
     * @param zoneCode 区域编码
     * @param batchNo 批次号
     * @return 匹配的库存明细，如果没有找到则返回null
     */
    private InventoryDetail findExistingInventoryDetail(String itemCode, String itemName,
                                                       String materialType, String zoneCode, String batchNo) {
        try {
            log.info("开始智能物料匹配 - itemCode: {}, itemName: {}, materialType: {}, zoneCode: {}, batchNo: {}",
                    itemCode, itemName, materialType, zoneCode, batchNo);

            // 第一优先级：物料ID + 区域编码 + 批次号匹配
            if (StringUtils.isNotBlank(itemCode)) {
                InventoryDetailVo detailVo = baseMapper.selectByMaterialAndZoneCode(itemCode, zoneCode, batchNo);
                if (detailVo != null) {
                    InventoryDetail detail = baseMapper.selectById(detailVo.getDetailId());
                    if (detail != null) {
                        log.info("通过物料ID匹配成功 - detailId: {}, materialId: {}", detail.getDetailId(), detail.getMaterialId());
                        return detail;
                    }
                }
            }

            // 第二优先级：物料名称 + 物料类型 + 区域编码 + 批次号匹配
            if (StringUtils.isNotBlank(itemName) && StringUtils.isNotBlank(materialType)) {
                InventoryDetailVo detailVo = baseMapper.selectByMaterialNameTypeAndZoneCode(itemName, materialType, batchNo, zoneCode);
                if (detailVo != null) {
                    InventoryDetail detail = baseMapper.selectById(detailVo.getDetailId());
                    if (detail != null) {
                        log.info("通过物料名称+类型匹配成功 - detailId: {}, materialName: {}, materialType: {}",
                                detail.getDetailId(), detail.getMaterialName(), detail.getMaterialType());
                        return detail;
                    }
                }
            }

            // 第三优先级：如果批次号为空，尝试匹配批次号也为空的记录
            if (StringUtils.isBlank(batchNo)) {
                // 先尝试物料ID匹配
                if (StringUtils.isNotBlank(itemCode)) {
                    InventoryDetailVo detailVo = baseMapper.selectByMaterialAndZoneCode(itemCode, zoneCode, null);
                    if (detailVo != null) {
                        InventoryDetail detail = baseMapper.selectById(detailVo.getDetailId());
                        if (detail != null && StringUtils.isBlank(detail.getBatchNo())) {
                            log.info("通过物料ID匹配成功（空批次号） - detailId: {}, materialId: {}", detail.getDetailId(), detail.getMaterialId());
                            return detail;
                        }
                    }
                }

                // 再尝试物料名称+类型匹配
                if (StringUtils.isNotBlank(itemName) && StringUtils.isNotBlank(materialType)) {
                    InventoryDetailVo detailVo = baseMapper.selectByMaterialNameTypeAndZoneCode(itemName, materialType, null, zoneCode);
                    if (detailVo != null) {
                        InventoryDetail detail = baseMapper.selectById(detailVo.getDetailId());
                        if (detail != null && StringUtils.isBlank(detail.getBatchNo())) {
                            log.info("通过物料名称+类型匹配成功（空批次号） - detailId: {}, materialName: {}, materialType: {}",
                                    detail.getDetailId(), detail.getMaterialName(), detail.getMaterialType());
                            return detail;
                        }
                    }
                }
            }

            log.info("未找到匹配的库存明细 - itemCode: {}, itemName: {}, materialType: {}, zoneCode: {}, batchNo: {}",
                    itemCode, itemName, materialType, zoneCode, batchNo);
            return null;

        } catch (Exception e) {
            log.error("智能物料匹配过程中发生异常 - itemCode: {}, itemName: {}, materialType: {}, zoneCode: {}, batchNo: {}",
                    itemCode, itemName, materialType, zoneCode, batchNo, e);
            return null;
        }
    }

    /**
     * 库存调整（完整扩展方法，支持采购入库的所有字段映射）
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> adjustInventoryStockExtendedFull(Long detailId, Integer adjustQuantity, String adjustType,
                                                               String adjustReason, String remark, String operationType,
                                                               String sourceDocument, String zoneCode, String itemCode,
                                                               String itemName, String unitOfMeasure, String lotNumber,
                                                               String boardType, String materialType, Integer supplierId, String supplierName,Integer styleId) {
        try {
            log.info("执行完整扩展库存调整 - detailId: {}, quantity: {}, type: {}, operation: {}, boardType: {}, materialType: {}, supplierId: {}, supplierName: {}",
                    detailId, adjustQuantity, adjustType, operationType, boardType, materialType, supplierId, supplierName);

            InventoryDetail targetDetail = null;
            InventoryDetail beforeDetail = null;
            boolean isNewDetail = false;
            Long operationId = null;

            if (detailId == null) {
                // 智能物料匹配逻辑：优先使用物料ID，备用物料名称+类型组合
                InventoryDetail existingDetail = findExistingInventoryDetail(itemCode, itemName, materialType, zoneCode, lotNumber);

                if (existingDetail != null) {
                    // 找到现有库存明细
                    detailId = existingDetail.getDetailId();
                    beforeDetail = baseMapper.selectById(detailId);
                    log.info("找到现有库存明细 - detailId: {}, materialId: {}, zoneCode: {}, batchNo: {}",
                            detailId, existingDetail.getMaterialId(), zoneCode, existingDetail.getBatchNo());
                } else if ("inbound".equals(operationType)) {
                    // 入库操作且不存在库存明细，创建新的库存明细
                    InventoryDetail newDetail = new InventoryDetail();
                    newDetail.setZoneCode(zoneCode);
                    newDetail.setMaterialId(itemCode);
                    newDetail.setMaterialName(StringUtils.isNotBlank(itemName) ? itemName : itemCode);
                    newDetail.setCurrentStock(adjustQuantity); // 当前库存 = 入库数量（因为结存和出库都是0）
                    newDetail.setInboundQuantity(adjustQuantity); // 入库数量
                    newDetail.setOutboundQuantity(0); // 出库数量初始为0
                    newDetail.setStockQuantity(0); // 结存数量初始为0（表示期初库存）
                    newDetail.setMinStockQuantity(0);
                    newDetail.setUnit(StringUtils.isNotBlank(unitOfMeasure) ? unitOfMeasure : "个");

                    // 设置新增的字段
                    newDetail.setBoardType(boardType);
                    newDetail.setMaterialType(StringUtils.isNotBlank(materialType) ? materialType : "原料");
                    newDetail.setSupplierId(supplierId);
                    newDetail.setSupplierName(supplierName);
                    //款式id不传值则为null
                    newDetail.setStyleId(styleId);

                    newDetail.setBatchNo(StringUtils.isNotBlank(lotNumber) ? lotNumber : itemCode + "_" + System.currentTimeMillis());
                    newDetail.setSourceType("purchase");
                    newDetail.setSourceNo(sourceDocument);
                    newDetail.setEntryDate(java.time.LocalDateTime.now());
                    newDetail.setStatus("1"); // 正常状态
                    newDetail.setDelFlag("0");
                    newDetail.setCreateBy(getSafeUsername());
                    newDetail.setUpdateBy(getSafeUsername());
                    newDetail.setRemark(remark);

                    // 保存新的库存明细
                    int insertResult = baseMapper.insert(newDetail);
                    if (insertResult <= 0) {
                        throw new RuntimeException("创建库存明细失败");
                    }

                    detailId = newDetail.getDetailId();
                    isNewDetail = true;
                    log.info("创建新库存明细成功 - detailId: {}, materialId: {}, zoneCode: {}, boardType: {}, supplierId: {}",
                            detailId, itemCode, zoneCode, boardType, supplierId);
                } else {
                    throw new RuntimeException("库存明细不存在，无法进行" + operationType + "操作");
                }
            } else {
                // 使用指定的库存明细ID
                beforeDetail = baseMapper.selectById(detailId);
                if (beforeDetail == null) {
                    throw new RuntimeException("库存明细不存在，无法进行调整");
                }
            }

            // 新建的库存明细已经在创建时设置了正确的数量，无需额外调整
            String operator = getSafeUsername();
            if (!isNewDetail) {
                // 对现有库存明细进行调整
                Integer finalAdjustQuantity = "decrease".equals(adjustType) ? -adjustQuantity : adjustQuantity;
                int result = baseMapper.adjustInventoryStock(detailId, finalAdjustQuantity, operator);
                if (result <= 0) {
                    throw new RuntimeException("库存调整失败");
                }
            }

            // 获取调整后的库存信息
            InventoryDetail afterDetail = baseMapper.selectById(detailId);

            // 记录库存操作日志
            try {
                boolean logResult = binOperationLogService.recordOperationLog(
                        zoneCode,                    // 区域编码
                        operationType,               // 操作类型
                        itemCode,                    // 物料ID
                        itemName,                    // 物料名称
                        isNewDetail ? 0 : beforeDetail.getCurrentStock(), // 操作前数量
                        "increase".equals(adjustType) ? adjustQuantity : -adjustQuantity, // 数量变化
                        afterDetail.getCurrentStock(), // 操作后数量
                        lotNumber,                   // 批次号
                        sourceDocument,              // 来源单据号
                        operator,                    // 操作人员
                        adjustReason,                // 操作原因
                        String.format("完整扩展库存调整 - 计量单位: %s, 批次号: %s, 板型: %s, 物料类型: %s, 供应商: %s, 备注: %s",
                                unitOfMeasure, lotNumber, boardType, materialType, supplierName, remark)
                );

                if (logResult) {
                    operationId = System.currentTimeMillis(); // 临时处理，实际应该从插入结果获取
                }
            } catch (Exception e) {
                log.warn("记录完整扩展库存调整操作日志失败: {}", e.getMessage());
            }

            // 同步到仓储管理系统
            try {
                String operation = "increase".equals(adjustType) ? "ADJUST_INCREASE" : "ADJUST_DECREASE";
                inventoryWarehouseSyncService.syncInventoryDetailOperation(afterDetail, operation);
                log.info("完整扩展库存调整同步成功 - 物料ID: {}, 调整数量: {}, 操作: {}, 供应商: {}",
                        afterDetail.getMaterialId(), adjustQuantity, operation, supplierName);
            } catch (Exception e) {
                log.error("完整扩展库存调整同步失败 - 物料ID: {}, 调整数量: {}",
                        afterDetail.getMaterialId(), adjustQuantity, e);
                // 根据配置决定是否回滚事务
                throw new RuntimeException("库存调整同步失败: " + e.getMessage(), e);
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("operationId", operationId);
            result.put("detailId", detailId);
            result.put("zoneCode", zoneCode);
            result.put("materialId", itemCode);
            result.put("materialName", itemName);
            result.put("materialType", materialType);
            result.put("quantity", adjustQuantity);
            result.put("currentStock", afterDetail.getCurrentStock());
            result.put("boardType", boardType);
            result.put("supplierId", supplierId);
            result.put("supplierName", supplierName);
            result.put("unitOfMeasure", unitOfMeasure);
            result.put("lotNumber", lotNumber);
            result.put("sourceDocument", sourceDocument);
            result.put("quantityBefore", isNewDetail ? 0 : beforeDetail.getCurrentStock());
            result.put("quantityAfter", afterDetail.getCurrentStock());
            result.put("quantityChange", "increase".equals(adjustType) ? adjustQuantity : -adjustQuantity);
            result.put("isNewDetail", isNewDetail);

            log.info("完整扩展库存调整完成 - detailId: {}, 当前库存: {}, 供应商: {}, 板型: {}",
                    detailId, afterDetail.getCurrentStock(), supplierName, boardType);

            return result;

        } catch (Exception e) {
            log.error("完整扩展库存调整失败 - itemCode: {}, zoneCode: {}, quantity: {}",
                    itemCode, zoneCode, adjustQuantity, e);
            throw new ServiceException("库存调整失败: " + e.getMessage());
        }
    }

}

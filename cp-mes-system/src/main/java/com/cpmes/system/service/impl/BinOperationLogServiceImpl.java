package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpmes.common.core.domain.PageQuery;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.domain.BinOperationLog;
import com.cpmes.system.domain.bo.BinOperationLogBo;
import com.cpmes.system.domain.vo.BinOperationLogVo;
import com.cpmes.system.mapper.BinOperationLogMapper;
import com.cpmes.system.service.IBinOperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 货位操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BinOperationLogServiceImpl implements IBinOperationLogService {

    private final BinOperationLogMapper baseMapper;

    /**
     * 查询货位操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public BinOperationLogVo queryById(Long logId) {
        return baseMapper.selectBinOperationLogById(logId);
    }

    /**
     * 查询货位操作记录列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public TableDataInfo<BinOperationLogVo> queryPageList(BinOperationLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BinOperationLog> lqw = buildQueryWrapper(bo);
        Page<BinOperationLog> result = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO对象并关联区域和仓库信息
        List<BinOperationLogVo> voList = baseMapper.selectBinOperationLogList(BeanUtil.toBean(bo, BinOperationLog.class));
        
        TableDataInfo<BinOperationLogVo> tableData = new TableDataInfo<>(voList, result.getTotal());
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 查询货位操作记录列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<BinOperationLogVo> queryList(BinOperationLogBo bo) {
        BinOperationLog queryEntity = BeanUtil.toBean(bo, BinOperationLog.class);
        return baseMapper.selectBinOperationLogList(queryEntity);
    }

    private LambdaQueryWrapper<BinOperationLog> buildQueryWrapper(BinOperationLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BinOperationLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getZoneCode()), BinOperationLog::getZoneCode, bo.getZoneCode());
        lqw.eq(StringUtils.isNotBlank(bo.getOperationType()), BinOperationLog::getOperationType, bo.getOperationType());
        lqw.eq(bo.getMaterialId() != null, BinOperationLog::getMaterialId, bo.getMaterialId());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialName()), BinOperationLog::getMaterialName, bo.getMaterialName());
        lqw.like(StringUtils.isNotBlank(bo.getOperator()), BinOperationLog::getOperator, bo.getOperator());
        lqw.eq(StringUtils.isNotBlank(bo.getVerificationStatus()), BinOperationLog::getVerificationStatus, bo.getVerificationStatus());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            BinOperationLog::getOperationTime, params.get("beginTime"), params.get("endTime"));
        lqw.orderByDesc(BinOperationLog::getOperationTime, BinOperationLog::getLogId);
        return lqw;
    }

    /**
     * 新增货位操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean insertByBo(BinOperationLogBo bo) {
        BinOperationLog add = BeanUtil.toBean(bo, BinOperationLog.class);
        validEntityBeforeSave(add);
        // 设置默认值
        if (add.getOperationTime() == null) {
            add.setOperationTime(LocalDateTime.now());
        }
        if (StringUtils.isBlank(add.getVerificationStatus())) {
            add.setVerificationStatus("0");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLogId(add.getLogId());
        }
        return flag;
    }

    /**
     * 修改货位操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean updateByBo(BinOperationLogBo bo) {
        BinOperationLog update = BeanUtil.toBean(bo, BinOperationLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BinOperationLog entity) {
        // 校验区域编码是否存在
        if (StringUtils.isBlank(entity.getZoneCode())) {
            throw new RuntimeException("区域编码不能为空");
        }
        
        // 校验操作类型
        if (StringUtils.isBlank(entity.getOperationType())) {
            throw new RuntimeException("操作类型不能为空");
        }
        
        // 校验数量变化
        if (entity.getQuantityChange() == null) {
            throw new RuntimeException("数量变化不能为空");
        }
        
        // 校验操作人员
        if (StringUtils.isBlank(entity.getOperator())) {
            throw new RuntimeException("操作人员不能为空");
        }
    }

    /**
     * 批量删除货位操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 此处可以添加删除前的业务校验逻辑
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据区域编码查询操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<BinOperationLogVo> queryByZoneCode(String zoneCode) {
        return baseMapper.selectByZoneCode(zoneCode);
    }

    /**
     * 根据操作类型查询操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<BinOperationLogVo> queryByOperationType(String operationType) {
        return baseMapper.selectByOperationType(operationType);
    }

    /**
     * 根据时间范围查询操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    public List<BinOperationLogVo> queryByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectByTimeRange(startTime, endTime);
    }

    /**
     * 记录库存操作日志
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean recordOperationLog(String zoneCode, String operationType, String materialId, String materialName,
                                     Integer quantityBefore, Integer quantityChange, Integer quantityAfter,
                                     String batchNo, String sourceDocument, String operator,
                                     String operationReason, String remark) {
        try {
            log.info("记录库存操作日志: 区域编码={}, 操作类型={}, 物料名称={}, 数量变化={}, 操作人={}",
                    zoneCode, operationType, materialName, quantityChange, operator);
            
            int result = baseMapper.insertOperationLog(zoneCode, operationType, materialId, materialName,
                    quantityBefore, quantityChange, quantityAfter, batchNo, sourceDocument,
                    operator, operationReason, remark);
            
            return result > 0;
        } catch (Exception e) {
            log.error("记录库存操作日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("记录库存操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 批量验证操作记录
     */
    @Override
    @DS("slave") // 使用PostgreSQL从数据库
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchVerifyOperationLog(List<Long> logIds) {
        try {
            // 更新验证状态为已验证
            LambdaQueryWrapper<BinOperationLog> updateWrapper = Wrappers.lambdaQuery();
            updateWrapper.in(BinOperationLog::getLogId, logIds);
            
            BinOperationLog updateEntity = new BinOperationLog();
            updateEntity.setVerificationStatus("1");
            
            return baseMapper.update(updateEntity, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("批量验证操作记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量验证操作记录失败: " + e.getMessage());
        }
    }
} 
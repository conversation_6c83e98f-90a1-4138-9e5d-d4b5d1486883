package com.cpmes.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cpmes.common.core.domain.BaseEntity;
import com.cpmes.common.core.domain.entity.SysUser;
import com.cpmes.common.core.page.TableDataInfo;
import com.cpmes.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cpmes.common.utils.StringUtils;
import com.cpmes.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cpmes.system.domain.bo.WarehouseInfoBo;
import com.cpmes.system.domain.vo.WarehouseInfoVo;
import com.cpmes.system.domain.WarehouseInfo;
import com.cpmes.system.mapper.WarehouseInfoMapper;
import com.cpmes.system.service.IWarehouseInfoService;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 仓库信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RequiredArgsConstructor
@Service
public class WarehouseInfoServiceImpl implements IWarehouseInfoService {

    private final WarehouseInfoMapper baseMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 查询仓库信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public WarehouseInfoVo queryById(Long warehouseId) {
        return baseMapper.selectVoById(warehouseId);
    }

    /**
     * 查询仓库信息列表
     */
    @Override
    public TableDataInfo<WarehouseInfoVo> queryPageList(WarehouseInfoBo bo, PageQuery pageQuery) {
        // 查询仓库信息 - 使用PostgreSQL
        LambdaQueryWrapper<WarehouseInfo> lqw = buildQueryWrapper(bo);
        Page<WarehouseInfoVo> result = queryWarehousePageFromSlave(pageQuery, lqw);

        // 查询用户信息 - 使用MySQL
        List<String> usernames = result.getRecords().stream().map(BaseEntity::getCreateBy).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(usernames)) {
            List<SysUser> allUserList = queryUsersFromMaster(usernames);
            for (WarehouseInfoVo vo : result.getRecords()) {
                SysUser user = allUserList.stream().filter(u -> u.getUserName().equals(vo.getCreateBy())).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(user)) {
                    vo.setCreateName(user.getNickName());
                }
            }
        }
        return TableDataInfo.build(result);
    }

    @DS("slave") // PostgreSQL数据源
    private Page<WarehouseInfoVo> queryWarehousePageFromSlave(PageQuery pageQuery, LambdaQueryWrapper<WarehouseInfo> lqw) {
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }

    @DS("master") // MySQL数据源
    private List<SysUser> queryUsersFromMaster(List<String> usernames) {
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserName, usernames));
    }

    /**
     * 查询仓库信息列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public List<WarehouseInfoVo> queryList(WarehouseInfoBo bo) {
        LambdaQueryWrapper<WarehouseInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WarehouseInfo> buildQueryWrapper(WarehouseInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseCode()), WarehouseInfo::getWarehouseCode, bo.getWarehouseCode());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseName()), WarehouseInfo::getWarehouseName, bo.getWarehouseName());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), WarehouseInfo::getWarehouseType, bo.getWarehouseType());
        lqw.like(StringUtils.isNotBlank(bo.getWarehouseAddress()), WarehouseInfo::getWarehouseAddress, bo.getWarehouseAddress());
        lqw.like(StringUtils.isNotBlank(bo.getManager()), WarehouseInfo::getManager, bo.getManager());
        lqw.like(StringUtils.isNotBlank(bo.getContactPhone()), WarehouseInfo::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WarehouseInfo::getStatus, bo.getStatus());
        lqw.orderByDesc(WarehouseInfo::getCreateTime);
        return lqw;
    }

    /**
     * 新增仓库信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean insertByBo(WarehouseInfoBo bo) {
        WarehouseInfo add = BeanUtil.toBean(bo, WarehouseInfo.class);
        validEntityBeforeSave(add);
        // 默认设置为启用状态
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("1");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWarehouseId(add.getWarehouseId());
        }
        return flag;
    }

    /**
     * 修改仓库信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean updateByBo(WarehouseInfoBo bo) {
        WarehouseInfo update = BeanUtil.toBean(bo, WarehouseInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WarehouseInfo entity) {
        // 校验仓库编码唯一性
        if (StringUtils.isNotBlank(entity.getWarehouseCode())) {
            Boolean isUnique = checkWarehouseCodeUnique(entity.getWarehouseCode(), entity.getWarehouseId());
            if (!isUnique) {
                throw new RuntimeException("仓库编码已存在");
            }
        }

        // 校验仓库名称、类型、位置组合唯一性
        if (StringUtils.isNotBlank(entity.getWarehouseName())
            && StringUtils.isNotBlank(entity.getWarehouseType())
            && StringUtils.isNotBlank(entity.getWarehouseAddress())) {
            Boolean isInfoUnique = checkWarehouseInfoUnique(
                entity.getWarehouseName(),
                entity.getWarehouseType(),
                entity.getWarehouseAddress(),
                entity.getWarehouseId()
            );
            if (!isInfoUnique) {
                throw new RuntimeException("相同名称、类型、位置的仓库已存在，请检查后重新提交");
            }
        }
    }

    /**
     * 批量删除仓库信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO: 可以添加业务校验，比如检查仓库是否还有库存等
            for (Long id : ids) {
                WarehouseInfoVo warehouse = queryById(id);
                if (warehouse == null) {
                    throw new RuntimeException("仓库不存在");
                }
                // 这里可以添加更多的业务校验逻辑
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量启用/停用仓库
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean updateStatus(Collection<Long> ids, String status) {
        if (ObjectUtil.isEmpty(ids) || StringUtils.isBlank(status)) {
            return false;
        }

        List<WarehouseInfo> updateList = ids.stream()
            .map(id -> {
                WarehouseInfo warehouse = new WarehouseInfo();
                warehouse.setWarehouseId(id);
                warehouse.setStatus(status);
                return warehouse;
            })
            .collect(Collectors.toList());

        return baseMapper.updateBatchById(updateList);
    }

    /**
     * 根据仓库类型查询仓库列表
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public List<WarehouseInfoVo> queryByType(String warehouseType) {
        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(warehouseType), WarehouseInfo::getWarehouseType, warehouseType);
        lqw.eq(WarehouseInfo::getStatus, "1"); // 只查询启用的仓库
        lqw.orderByAsc(WarehouseInfo::getWarehouseCode);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 校验仓库编码是否唯一
     *
     * @param warehouseCode 仓库编码
     * @param warehouseId 仓库ID
     * @return 结果 true-唯一，false-不唯一
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean checkWarehouseCodeUnique(String warehouseCode, Long warehouseId) {
        if (StringUtils.isBlank(warehouseCode)) {
            return false;
        }

        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseInfo::getWarehouseCode, warehouseCode);
        if (warehouseId != null) {
            lqw.ne(WarehouseInfo::getWarehouseId, warehouseId);
        }
        lqw.eq(WarehouseInfo::getDelFlag, "0");

        long count = baseMapper.selectCount(lqw);
        return count == 0;
    }

    /**
     * 校验仓库名称、类型、位置组合是否唯一
     *
     * @param warehouseName 仓库名称
     * @param warehouseType 仓库类型
     * @param warehouseAddress 仓库地址
     * @param warehouseId 仓库ID（编辑时传入，排除自身）
     * @return 结果 true-唯一，false-不唯一
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public Boolean checkWarehouseInfoUnique(String warehouseName, String warehouseType, String warehouseAddress, Long warehouseId) {
        if (StringUtils.isBlank(warehouseName) || StringUtils.isBlank(warehouseType) || StringUtils.isBlank(warehouseAddress)) {
            return false;
        }

        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseInfo::getWarehouseName, warehouseName);
        lqw.eq(WarehouseInfo::getWarehouseType, warehouseType);
        lqw.eq(WarehouseInfo::getWarehouseAddress, warehouseAddress);
        if (warehouseId != null) {
            lqw.ne(WarehouseInfo::getWarehouseId, warehouseId);
        }
        lqw.eq(WarehouseInfo::getDelFlag, "0");

        long count = baseMapper.selectCount(lqw);
        return count == 0;
    }

    /**
     * 生成仓库编码
     *
     * @param warehouseType 仓库类型
     * @return 生成的仓库编码
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public String generateWarehouseCode(String warehouseType) {
        String prefix = getWarehouseCodePrefix(warehouseType);
        String datePart = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 查询当天该类型仓库的最大序号
        String likePattern = prefix + datePart + "%";
        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(WarehouseInfo::getWarehouseCode, likePattern);
        lqw.eq(WarehouseInfo::getDelFlag, "0");
        lqw.orderByDesc(WarehouseInfo::getWarehouseCode);
        lqw.last("LIMIT 1");

        WarehouseInfo lastWarehouse = baseMapper.selectOne(lqw);

        int nextSequence = 1;
        if (lastWarehouse != null && StringUtils.isNotBlank(lastWarehouse.getWarehouseCode())) {
            String lastCode = lastWarehouse.getWarehouseCode();
            if (lastCode.startsWith(prefix + datePart) && lastCode.length() >= prefix.length() + datePart.length() + 3) {
                try {
                    String sequenceStr = lastCode.substring(prefix.length() + datePart.length());
                    int lastSequence = Integer.parseInt(sequenceStr);
                    nextSequence = lastSequence + 1;
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用默认序号1
                    nextSequence = 1;
                }
            }
        }

        String newCode = prefix + datePart + String.format("%03d", nextSequence);

        // 再次校验生成的编码是否唯一，如果不唯一则递增序号
        while (!checkWarehouseCodeUnique(newCode, null)) {
            nextSequence++;
            newCode = prefix + datePart + String.format("%03d", nextSequence);
        }

        return newCode;
    }

    /**
     * 根据仓库类型获取编码前缀
     *
     * @param warehouseType 仓库类型
     * @return 编码前缀
     */
    private String getWarehouseCodePrefix(String warehouseType) {
        if (StringUtils.isBlank(warehouseType)) {
            return "WH"; // 默认前缀
        }

        switch (warehouseType) {
            case "1":
            case "raw_material":
                return "RM"; // Raw Material - 原料仓库
            case "2":
            case "semi_finished":
                return "SF"; // Semi Finished - 半成品仓库
            case "3":
            case "product":
                return "PD"; // Product - 成品仓库
            case "4":
            case "component":
                return "CP"; // Component - 零件仓库
            default:
                return "WH"; // Warehouse - 通用仓库
        }
    }

    /**
     * 根据仓库类型获取类型编码
     *
     * @param warehouseType 仓库类型
     * @return 类型编码
     */
    private String getWarehouseTypeCode(String warehouseType) {
        if (StringUtils.isBlank(warehouseType)) {
            return "99"; // 默认类型
        }

        switch (warehouseType) {
            case "1":
            case "raw_material":
                return "01"; // 原料仓库
            case "2":
            case "semi_finished":
                return "02"; // 半成品仓库
            case "3":
            case "product":
                return "03"; // 成品仓库
            case "4":
            case "component":
                return "04"; // 零件仓库
            default:
                return "99"; // 通用仓库
        }
    }

    /**
     * 根据仓库编码查询仓库信息
     */
    @Override
    @DS("slave") // 使用PostgreSQL数据源
    public WarehouseInfoVo queryByWarehouseCode(String warehouseCode) {
        if (StringUtils.isBlank(warehouseCode)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(WarehouseInfo::getWarehouseCode, warehouseCode);
        lqw.eq(WarehouseInfo::getDelFlag, "0");
        return baseMapper.selectVoOne(lqw);
    }
}

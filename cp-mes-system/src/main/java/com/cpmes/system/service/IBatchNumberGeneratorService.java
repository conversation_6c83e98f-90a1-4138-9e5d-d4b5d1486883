package com.cpmes.system.service;

import java.time.LocalDate;

/**
 * 批次号生成服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface IBatchNumberGeneratorService {

    /**
     * 生成下一个批次号
     * 格式：YYYYMMDD-XXX（年月日-3位自增序号）
     * 示例：20241226-001, 20241226-002
     * 
     * @return 新生成的批次号
     * @throws com.cpmes.common.exception.ServiceException 当日批次号已达上限时抛出异常
     */
    String generateNextBatchNumber();

    /**
     * 从批次号解析日期信息
     * 
     * @param batchNumber 批次号，格式为YYYYMMDD-XXX
     * @return 解析出的日期，如果格式不正确返回null
     */
    LocalDate parseDateFromBatchNumber(String batchNumber);

    /**
     * 从批次号解析序号信息
     * 
     * @param batchNumber 批次号，格式为YYYYMMDD-XXX
     * @return 解析出的序号，如果格式不正确返回null
     */
    Integer parseSequenceFromBatchNumber(String batchNumber);

    /**
     * 验证批次号格式是否正确
     * 
     * @param batchNumber 待验证的批次号
     * @return true-格式正确，false-格式错误
     */
    boolean isValidBatchNumber(String batchNumber);

    /**
     * 获取指定日期的当前最大序号
     * 
     * @param date 指定日期
     * @return 当前最大序号，如果当天没有批次号则返回0
     */
    Integer getCurrentMaxSequence(LocalDate date);

    /**
     * 查询指定日期范围内的所有批次号
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 批次号列表
     */
    java.util.List<String> getBatchNumbersByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 重置指定日期的序号计数器（谨慎使用）
     *
     * @param date 指定日期
     * @param newMaxSequence 新的最大序号
     * @return 是否重置成功
     */
    boolean resetSequenceCounter(LocalDate date, Integer newMaxSequence);

    /**
     * 批量生成批次号（用于批量入库场景）
     *
     * @param count 需要生成的批次号数量
     * @return 批次号列表
     */
    java.util.List<String> generateBatchNumbers(int count);
}

package com.cpmes.system.service;

import com.cpmes.system.entity.vo.TrackingNumberVO;
import java.util.List;

/**
 * 快递单号管理服务接口
 *
 * <AUTHOR> System
 * @date 2024-01-01
 */
public interface TrackingNumberService {

    /**
     * 获取快递单号列表
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param keyword 搜索关键词
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbers(int pageNum, int pageSize, String keyword);

    /**
     * 保存快递单号
     *
     * @param trackingNumber 快递单号信息
     * @return 保存后的快递单号信息
     */
    TrackingNumberVO saveTrackingNumber(TrackingNumberVO trackingNumber);

    /**
     * 更新快递单号
     *
     * @param trackingNumber 快递单号信息
     * @return 更新后的快递单号信息
     */
    TrackingNumberVO updateTrackingNumber(TrackingNumberVO trackingNumber);

    /**
     * 删除快递单号
     *
     * @param trackingNumber 快递单号
     * @return 是否删除成功
     */
    boolean deleteTrackingNumber(String trackingNumber);

    /**
     * 根据ID删除快递单号
     *
     * @param id 快递单号ID
     * @return 是否删除成功
     */
    boolean deleteTrackingNumberById(Long id);

    /**
     * 批量删除快递单号
     *
     * @param trackingNumbers 快递单号列表
     * @return 删除的记录数
     */
    int batchDeleteTrackingNumbers(List<String> trackingNumbers);

    /**
     * 获取快递单号详情
     *
     * @param trackingNumber 快递单号
     * @return 快递单号详情
     */
    TrackingNumberVO getTrackingNumberDetail(String trackingNumber);

    /**
     * 根据采购订单号获取快递单号列表
     *
     * @param purchaseOrderNo 采购订单号
     * @return 快递单号列表
     */
    List<TrackingNumberVO> getTrackingNumbersByPurchaseOrder(String purchaseOrderNo);

    /**
     * 更新快递单号的查询结果
     *
     * @param trackingNumber 快递单号
     * @param queryResult 查询结果
     * @return 是否更新成功
     */
    boolean updateQueryResult(String trackingNumber, com.cpmes.system.entity.dto.purchaseOrder.LogisticsTrackingDto queryResult);

    /**
     * 获取需要自动查询的快递单号列表
     *
     * @return 需要自动查询的快递单号列表
     */
    List<TrackingNumberVO> getAutoQueryTrackingNumbers();
}
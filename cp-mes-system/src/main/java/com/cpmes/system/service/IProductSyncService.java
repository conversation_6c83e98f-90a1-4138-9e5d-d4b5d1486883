package com.cpmes.system.service;

import com.cpmes.common.core.domain.R;

/**
 * 产品同步到仓储管理系统Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IProductSyncService {

    /**
     * 将产品管理表中的所有产品数据同步到仓储管理系统的5个类型仓库表
     * 包括：product_warehouse、raw_material_warehouse、semi_finished_product、
     * semi_finished_product_two、component_warehouse
     *
     * @return 同步结果信息
     */
    R<String> syncAllProductsToWarehouses();

    /**
     * 同步指定产品到所有仓储表
     *
     * @param productId 产品ID
     * @return 同步结果信息
     */
    R<String> syncProductToWarehouses(Long productId);

    /**
     * 批量同步指定产品列表到所有仓储表
     *
     * @param productIds 产品ID列表
     * @return 同步结果信息
     */
    R<String> batchSyncProductsToWarehouses(Long[] productIds);

    /**
     * 测试款式映射功能
     *
     * @param styleName 款式名称
     * @return 映射结果信息
     */
    R<String> testStyleMapping(String styleName);

    /**
     * 分析同步状态
     *
     * @return 同步状态分析结果
     */
    R<String> analyzeSyncStatus();
}

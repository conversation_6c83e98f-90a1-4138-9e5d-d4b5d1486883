# MES系统扫码登录项目说明文档

## 项目概述

### 功能简介
MES系统扫码登录功能允许用户通过移动设备扫描网页端生成的二维码，快速完成网页端登录，无需手动输入用户名密码，提升用户体验和登录效率。

### 核心特性
- ✅ **无验证码登录**：扫码登录过程完全绕过传统验证码验证
- ✅ **自动用户映射**：移动端用户自动对应到相应的网页端账户
- ✅ **安全可靠**：采用UUID、时效控制、一次性验证等多重安全机制
- ✅ **简化流程**：2步完成登录（扫码→验证），操作简单直观
- ✅ **实时响应**：支持网页端实时获取登录状态

## 技术架构

### 系统架构图
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   网页端    │    │   后端服务   │    │   移动端    │
│ (Vue.js)    │    │ (Spring)    │    │ (测试页面)   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │ 1.请求生成二维码    │                   │
       ├─────────────────→│                   │
       │                   │                   │
       │ 2.返回二维码UUID   │                   │
       │←─────────────────┤                   │
       │                   │                   │
       │ 3.显示二维码      │                   │
       │                   │                   │
       │                   │ 4.扫码获取UUID     │
       │                   │←─────────────────│
       │                   │                   │
       │                   │ 5.提交登录验证      │
       │                   │←─────────────────│
       │                   │                   │
       │                   │ 6.验证用户密码      │
       │                   │ 7.生成登录token    │
       │                   │ 8.关联UUID-token  │
       │                   │                   │
       │ 9.轮询检查状态     │                   │
       ├─────────────────→│                   │
       │                   │                   │
       │ 10.返回登录token   │                   │
       │←─────────────────┤                   │
       │                   │                   │
       │ 11.完成登录       │                   │
```

### 核心组件

#### 1. 后端组件
```
cp-mes-admin/src/main/java/com/cpmes/web/controller/
├── SysQrCodeLoginController.java     # 二维码登录控制器
└── vo/QrCodeLoginVerifyVo.java       # 登录验证数据传输对象
```

#### 2. 前端组件
```
cp-mes-ui/src/api/
└── qrcode.js                         # 二维码登录API接口
```

#### 3. 移动端测试应用
```
cp-mes-ui/test-qrcode-app/
├── index.html                        # 移动端测试页面
├── config.js                         # 配置文件
└── docs/                             # 相关文档
```

## 核心功能模块

### 1. 二维码生成与管理
- **生成接口**：`GET /qrcode/generate`
- **功能**：生成包含UUID的二维码，存储到Redis
- **时效**：5分钟自动过期
- **格式**：JSON格式包含type、uuid、timestamp等信息

### 2. 扫码登录验证
- **验证接口**：`POST /qrcode/login-verify`
- **功能**：验证用户名密码，生成登录token并关联UUID
- **特点**：完全绕过验证码验证
- **安全**：支持BCrypt密码验证

### 3. 登录状态查询
- **查询接口**：`GET /qrcode/status/{uuid}`
- **功能**：网页端轮询检查扫码登录状态
- **响应**：返回登录状态和用户token信息

### 4. 传统Token授权（可选）
- **授权接口**：`POST /qrcode/verify`
- **功能**：使用移动端已有token进行授权
- **场景**：适用于已登录移动端应用的用户

## 接口详细说明

### 1. 生成二维码
```http
GET /qrcode/generate
Content-Type: application/json

Response:
{
    "code": 200,
    "msg": "success",
    "data": {
        "uuid": "qr-login-1234567890",
        "qrContent": "{\"type\":\"login\",\"uuid\":\"qr-login-1234567890\",\"timestamp\":1234567890}",
        "expireTime": 300
    }
}
```

### 2. 扫码登录验证
```http
POST /qrcode/login-verify
Content-Type: application/json

Request:
{
    "qrUuid": "qr-login-1234567890",
    "username": "testuser",
    "password": "password123",
    "deviceInfo": "Mozilla/5.0...",
    "userConfirmed": true
}

Response:
{
    "code": 200,
    "msg": "登录验证成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 7200,
        "userInfo": { ... }
    }
}
```

### 3. 查询登录状态
```http
GET /qrcode/status/qr-login-1234567890

Response:
{
    "code": 200,
    "msg": "success",
    "data": {
        "status": "confirmed",
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "userInfo": { ... }
    }
}
```

## 使用说明

### 网页端使用流程

#### 1. 集成二维码生成
```javascript
// 在登录页面添加二维码登录选项
import { generateQrCode, checkQrCodeStatus } from '@/api/qrcode'

export default {
  data() {
    return {
      qrCodeData: null,
      polling: null
    }
  },
  
  async mounted() {
    await this.generateQrCode()
    this.startPolling()
  },
  
  methods: {
    // 生成二维码
    async generateQrCode() {
      try {
        const response = await generateQrCode()
        this.qrCodeData = response.data
        // 显示二维码给用户扫描
        this.displayQrCode(this.qrCodeData.qrContent)
      } catch (error) {
        console.error('二维码生成失败:', error)
      }
    },
    
    // 轮询检查登录状态
    startPolling() {
      this.polling = setInterval(async () => {
        try {
          const response = await checkQrCodeStatus(this.qrCodeData.uuid)
          if (response.data.status === 'confirmed') {
            // 登录成功，保存token并跳转
            this.$store.dispatch('user/setToken', response.data.access_token)
            this.$router.push('/dashboard')
            clearInterval(this.polling)
          }
        } catch (error) {
          console.error('状态检查失败:', error)
        }
      }, 2000) // 每2秒检查一次
    }
  }
}
```

#### 2. 添加API接口
```javascript
// src/api/qrcode.js
import request from '@/utils/request'

// 生成二维码
export function generateQrCode() {
  return request({
    url: '/qrcode/generate',
    method: 'get'
  })
}

// 检查二维码状态
export function checkQrCodeStatus(uuid) {
  return request({
    url: `/qrcode/status/${uuid}`,
    method: 'get'
  })
}

// 扫码登录验证
export function loginAndVerifyQrCode(data) {
  return request({
    url: '/qrcode/login-verify',
    method: 'post',
    data
  })
}
```

### 移动端使用流程

#### 1. 打开测试页面
```
http://localhost:8080/test-qrcode-app/index.html
```

#### 2. 扫码登录操作
1. 在二维码内容输入框中粘贴扫描到的二维码内容
2. 点击"🚀 自动登录验证"按钮
3. 系统自动根据当前移动端用户进行网页端登录
4. 等待登录成功提示

#### 3. 用户切换
- 点击"切换用户"按钮可以在不同测试用户间切换
- 每个移动端用户都有对应的网页端账户映射

## 部署指南

### 环境要求
- Java 8+
- Spring Boot 2.x
- Redis 6.x+
- Vue.js 2.x/3.x
- MySQL 5.7+

### 后端部署

#### 1. 添加依赖
```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

#### 2. 配置Redis
```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 3000ms
    jedis:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5
```

#### 3. 部署控制器
将以下文件复制到对应目录：
- `SysQrCodeLoginController.java` → `src/main/java/com/cpmes/web/controller/`
- `QrCodeLoginVerifyVo.java` → `src/main/java/com/cpmes/web/controller/vo/`

### 前端部署

#### 1. 添加API接口
将 `qrcode.js` 复制到 `src/api/` 目录

#### 2. 集成到登录页面
在现有登录页面添加二维码登录功能

#### 3. 配置路由和权限
```javascript
// router/index.js
{
  path: '/qrcode-test',
  component: () => import('@/views/QrCodeTest.vue'),
  hidden: true
}
```

### 移动端测试页面部署

#### 1. 复制文件
将 `test-qrcode-app` 整个目录复制到静态资源目录

#### 2. 配置访问路径
```java
// 添加静态资源映射
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/test-qrcode-app/**")
                .addResourceLocations("classpath:/static/test-qrcode-app/");
    }
}
```

#### 3. 创建测试用户
```sql
-- 创建测试用户
INSERT INTO sys_user (user_name, nick_name, password, status, del_flag, create_time, remark) VALUES
('qrtest01', '扫码测试用户1', '$2a$10$encrypted_password_hash', '0', '0', NOW(), '扫码登录测试专用'),
('qrtest02', '扫码测试用户2', '$2a$10$encrypted_password_hash', '0', '0', NOW(), '扫码登录测试专用');

-- 分配基础角色
INSERT INTO sys_user_role (user_id, role_id) VALUES 
(获取的用户ID, 基础角色ID);
```

## 安全配置

### 1. 环境隔离
```javascript
// 生产环境禁用测试功能
if (process.env.NODE_ENV === 'production') {
  // 隐藏测试相关功能
  console.warn('扫码登录测试功能在生产环境已禁用')
}
```

### 2. 访问控制
```java
// 限制测试页面访问
@RequestMapping("/test-qrcode-app/**")
public class TestPageController {
    @GetMapping("/**")
    public String testPage(HttpServletRequest request) {
        // 生产环境返回404
        if ("production".equals(environment)) {
            throw new NotFoundException();
        }
        return "forward:/test-qrcode-app/index.html";
    }
}
```

### 3. 数据加密
```java
// 敏感数据加密存储
@Service
public class QrCodeSecurityService {
    
    @Autowired
    private AESUtil aesUtil;
    
    public void storeQrCodeData(String uuid, Object data) {
        String encryptedData = aesUtil.encrypt(JSON.toJSONString(data));
        redisTemplate.opsForValue().set(uuid, encryptedData, 5, TimeUnit.MINUTES);
    }
}
```

## 监控与日志

### 1. 性能监控
```java
@RestController
@Slf4j
public class SysQrCodeLoginController {
    
    @PostMapping("/login-verify")
    @Timed(name = "qrcode.login.verify", description = "扫码登录验证耗时")
    public R<Map<String, Object>> loginAndVerifyQrCode(@RequestBody QrCodeLoginVerifyVo loginVerifyVo) {
        long startTime = System.currentTimeMillis();
        try {
            // 业务逻辑
            return result;
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("扫码登录验证耗时: {}ms, UUID: {}", endTime - startTime, loginVerifyVo.getQrUuid());
        }
    }
}
```

### 2. 操作日志
```java
// 记录关键操作
log.info("扫码登录验证成功 - 用户: {}, UUID: {}, IP: {}", 
         user.getUserName(), loginVerifyVo.getQrUuid(), IpUtils.getIpAddr(request));
```

### 3. 异常监控
```java
@ControllerAdvice
public class QrCodeExceptionHandler {
    
    @ExceptionHandler(QrCodeExpiredException.class)
    public R<Void> handleQrCodeExpired(QrCodeExpiredException e) {
        log.warn("二维码已过期: {}", e.getMessage());
        return R.fail("二维码已过期，请刷新重试");
    }
}
```

## 常见问题与解决方案

### Q1: 二维码生成失败
**原因**：Redis连接异常或配置错误  
**解决**：
1. 检查Redis服务状态
2. 验证Redis连接配置
3. 查看Redis连接池配置

### Q2: 扫码登录验证失败
**原因**：用户不存在或密码错误  
**解决**：
1. 确认测试用户已创建
2. 检查密码是否正确
3. 验证用户状态是否正常

### Q3: 网页端无法获取登录状态
**原因**：轮询机制异常或网络问题  
**解决**：
1. 检查轮询间隔设置
2. 验证网络连接状态
3. 查看浏览器控制台错误

### Q4: 移动端测试页面无法访问
**原因**：静态资源配置问题  
**解决**：
1. 检查静态资源映射配置
2. 确认文件路径正确
3. 验证Web服务器配置

## 性能优化

### 1. Redis优化
```yaml
# Redis配置优化
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5
    timeout: 3000ms
```

### 2. 前端优化
```javascript
// 防抖处理
const debouncedCheck = debounce(checkQrCodeStatus, 2000)

// 智能轮询
let pollInterval = 2000
const adaptivePolling = () => {
  // 根据网络状况调整轮询间隔
  if (networkSpeed === 'slow') {
    pollInterval = 5000
  }
}
```

### 3. 缓存优化
```java
// 合理设置缓存过期时间
@Cacheable(value = "qrcode", key = "#uuid", expire = 300)
public QrCodeData getQrCodeData(String uuid) {
    return qrCodeService.findByUuid(uuid);
}
```
 /**
 * v-hasPermi 操作权限处理
 * Copyright (c) 2019 ruoyi
 */

import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const all_permission = "*:*:*";
    const permissions = store.getters && store.getters.permissions

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      // 增加权限数组的安全检查
      if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
        el.parentNode && el.parentNode.removeChild(el)
        return
      }

      // 首先检查是否有超级权限
      if (permissions.includes(all_permission)) {
        return
      }

      // 检查具体权限
      const hasPermissions = permissionFlag.some(flag => permissions.includes(flag))

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}

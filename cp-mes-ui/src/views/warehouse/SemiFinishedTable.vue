<template>
  <div>
    <el-form inline m-t-10>
      <el-form-item label="名称">
        <el-input v-model="searchQuery" placeholder="请输入半成品名称" clearable />
      </el-form-item>
      <!-- <el-form-item label="物料类型">
        <el-select v-model="materialType" placeholder="请选择物料类型" clearable @change="handleSearch"
          class="type-select" popper-class="type-select-dropdown">
          <el-option label="全部" value="" />
          <el-option label="半成品" value="半成品" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <i class="el-icon-search"></i>
          查询
        </el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 筛选条件展示区域 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <span class="filter-label">已选筛选条件:</span>
      <el-tag v-if="searchQuery" closable @close="clearNameFilter" class="filter-tag">
        半成品名称: {{ searchQuery }}
      </el-tag>
      <!-- <el-tag v-if="materialType" closable @close="clearTypeFilter" class="filter-tag" type="success">
        物料类型: {{ materialType }}
      </el-tag> -->
      <el-button size="small" type="info" text @click="resetSearch">清除全部</el-button>
    </div>

    <el-table
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      :row-class-name="tableRowClassName"
      border
      highlight-current-row
      :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
      :cell-style="{padding:'12px 0'}"
      empty-text="暂无数据"
      v-loading="loading"
      fit>
      <el-table-column type="selection" width="55" />
      <el-table-column prop="fields3" label="物料ID" width="120" show-overflow-tooltip sortable align="center">
        <template slot-scope="scope">
          <span style="color: #409eff;">
            <i class="el-icon-files" style="margin-right: 4px;"></i>
            {{ scope.row.fields3 || '--' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="semiProductName" label="半成品名称" width="200" show-overflow-tooltip sortable align="center" />
      <el-table-column prop="materialType" label="物料类型" width="150" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          <el-tag size="small" type="warning">
            {{ scope.row.materialType || '半成品' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="currentStock" label="当前库存" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span :class="scope.row.currentStock <= (scope.row.minStockQuantity || 0) ? 'warning-status' : 'normal-status'">
            <i class="el-icon-box" style="margin-right: 4px;"></i>
            {{ scope.row.currentStock || 0 }}
            <span style="color: #909399; font-size: 12px; margin-left: 4px;">
              {{ scope.row.unit || '个' }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="inboundQuantity" label="入库数据" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #67c23a;">
            <i class="el-icon-bottom" style="margin-right: 4px;"></i>
            {{ scope.row.inboundQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="outboundQuantity" label="出库数据" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #e6a23c;">
            <i class="el-icon-top" style="margin-right: 4px;"></i>
            {{ scope.row.outboundQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="stockQuantity" label="结存数据" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #409eff; font-weight: 500;">
            <i class="el-icon-document" style="margin-right: 4px;"></i>
            {{ scope.row.stockQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <!-- 工序流程和流程状态字段已隐藏 -->
      <el-table-column prop="fields2" label="安全库存" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #f56c6c;">
            <i class="el-icon-warning" style="margin-right: 4px;"></i>
            {{ scope.row.fields2 || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="boardTypeName" label="板型" min-width="120" align="center">
        <template slot-scope="scope">
          <el-tag :type="getBoardTypeTagType(scope.row.boardTypeName)" size="small">
            <i class="el-icon-s-grid" style="margin-right: 4px;"></i>
            {{ scope.row.boardTypeName || '--' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="unit" label="单位" min-width="80" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" type="info">
            <i class="el-icon-price-tag" style="margin-right: 4px;"></i>
            {{ scope.row.unit || '个' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="styleName" label="款式" min-width="120" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStyleTagType(scope.row.styleName)" size="small">
            <i class="el-icon-s-operation" style="margin-right: 4px;"></i>
            {{ scope.row.styleName || '--' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="productNumber" label="产品编号" min-width="120" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.productNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="存放区域" min-width="120" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleViewZoneDistribution(scope.row)"
            :disabled="!scope.row.fields3"
            class="zone-link-btn"
          >
            <i class="el-icon-location" style="margin-right: 4px;"></i>
            查看分布
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="updatedTime" label="更新时间" min-width="160" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #909399;">
            <i class="el-icon-time" style="margin-right: 4px;"></i>
            {{ scope.row.updatedTime || '--' }}
          </span>
        </template>
      </el-table-column>
      <!-- 操作列已隐藏 - 仅保留查询功能 -->
      <!--
      <el-table-column label="操作" width="350" fixed="right" align="center">
        <template slot="header">
          <div style="display: flex; gap: 6px; justify-content: center; align-items: center; flex-wrap: wrap;">
            <el-button size="mini" type="danger" @click="handleDelList" :disabled="selectRows.length == 0" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-delete" style="margin-right: 2px;"></i>
              删除已选
            </el-button>
            <el-button size="mini" type="primary" @click="dialogAddVisible = true" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-plus" style="margin-right: 2px;"></i>
              新增
            </el-button>
          </div>
        </template>
        <template slot-scope="scope">
          <div style="display: flex; gap: 4px; justify-content: center; flex-wrap: wrap;">
            <el-button size="mini" type="success" @click="handleInbound(scope.row)" style="padding: 4px 6px; font-size: 10px;">
              <i class="el-icon-bottom" style="margin-right: 2px;"></i>
              入库
            </el-button>
            <el-button size="mini" type="warning" @click="handleOutbound(scope.row)" style="padding: 4px 6px; font-size: 10px;">
              <i class="el-icon-top" style="margin-right: 2px;"></i>
              出库
            </el-button>
            <el-button size="mini" type="primary" @click="handleUpdate(scope.row)" style="padding: 4px 6px; font-size: 10px;">
              <i class="el-icon-edit" style="margin-right: 2px;"></i>
              修改
            </el-button>
            <el-button size="mini" type="danger" @click="handleDel(scope.row)" style="padding: 4px 6px; font-size: 10px;">
              <i class="el-icon-delete" style="margin-right: 2px;"></i>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
      -->
    </el-table>

    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />

    <!-- 添加对话框 -->
    <semi-finished-dialog-add v-model="dialogAddVisible" v-if="dialogAddVisible" @ok="fetchData"></semi-finished-dialog-add>

    <!-- 修改对话框 -->
    <semi-finished-dialog-update v-model="dialogUpdateVisible" v-if="dialogUpdateVisible" :row="operateRow"
      @ok="fetchData"></semi-finished-dialog-update>

    <!-- 入库出库对话框 -->
    <semi-finished-in-out-dialog v-model="inOutDialogVisible" v-if="inOutDialogVisible" :operation-type="inOutOperationType" :semi-finished-data="inOutSemiFinishedData" @ok="fetchData"></semi-finished-in-out-dialog>

    <!-- 物料区域分布弹窗 -->
    <material-zone-dialog
      :visible.sync="zoneDialogVisible"
      :material-info="selectedMaterial"
      material-type="一级半成品"
    />
  </div>
</template>

<script>
import {
  pageSemiFinishedProduct,
  addSemiFinishedProduct,
  updateSemiFinishedProduct,
  delSemiFinishedProduct,
  delBatchSemiFinishedProduct,
  outboundSemiFinishedProduct,
  inboundSemiFinishedProduct
} from '@/api/jenasi/semiFinishedProduct';
import SemiFinishedDialogAdd from './SemiFinishedDialogAdd.vue';
import SemiFinishedDialogUpdate from './SemiFinishedDialogUpdate.vue';
import SemiFinishedInOutDialog from './SemiFinishedInOutDialog.vue';
import MaterialZoneDialog from './components/MaterialZoneDialog.vue';

export default {
  name: 'SemiFinishedTable',
  components: {
    SemiFinishedDialogAdd,
    SemiFinishedDialogUpdate,
    SemiFinishedInOutDialog,
    MaterialZoneDialog
  },
  data() {
    return {
      searchQuery: '',
      materialType: '',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectRows: [],
      operateRow: null,
      dialogAddVisible: false,
      dialogUpdateVisible: false,
      inOutDialogVisible: false,
      inOutOperationType: 'inbound',
      inOutSemiFinishedData: {},
      loading: false,
      // 物料区域弹窗相关
      zoneDialogVisible: false,
      selectedMaterial: {},
      sortField: null,
      sortOrder: null
    };
  },
  computed: {
    hasActiveFilters() {
      return this.searchQuery;
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    // 清除单个筛选条件
    clearNameFilter() {
      this.searchQuery = '';
      this.handleSearch();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchQuery = '';
      this.currentPage = 1;
      this.sortField = null;
      this.sortOrder = null;
      this.fetchData();
    },

    async fetchData() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          semiProductName: this.searchQuery || undefined,
          sortField: this.sortField,
          sortOrder: this.sortOrder
        };

        const response = await pageSemiFinishedProduct(params);
        console.log('一级半成品数据响应:', response);

        // 处理响应数据
        if (response && response.data) {
          const data = response.data;
          this.tableData = data.records || [];
          this.total = data.total || 0;

          // 确保每条记录都有正确的ID
          this.tableData = this.tableData.map((record, index) => {
            const recordCopy = { ...record };

            // 确保semiProductId存在
            if (!recordCopy.semiProductId && recordCopy.id) {
              recordCopy.semiProductId = recordCopy.id;
            } else if (!recordCopy.id && recordCopy.semiProductId) {
              recordCopy.id = recordCopy.semiProductId;
            } else if (!recordCopy.semiProductId && !recordCopy.id) {
              recordCopy.semiProductId = index + 1;
              recordCopy.id = index + 1;
              recordCopy._invalid = true;
            }

            return recordCopy;
          });

          console.log('一级半成品数据加载成功，总数:', this.total);
        } else {
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取一级半成品数据失败:', error);
        this.$message.error('获取数据失败');
        this.tableData = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },

    handleUpdate(row) {
      const rowCopy = JSON.parse(JSON.stringify(row));

      if (!rowCopy.semiProductId) {
        if (rowCopy.id) {
          console.warn('行数据缺少semiProductId，使用id:', rowCopy.id);
          rowCopy.semiProductId = rowCopy.id;
        } else {
          this.$message.error('该记录缺少有效ID，无法修改');
          return;
        }
      }

      this.operateRow = rowCopy;

      this.$nextTick(() => {
        this.dialogUpdateVisible = true;
      });
    },

    async handleDel(row) {
      try {
        const currentRow = JSON.parse(JSON.stringify(row));
        const semiProductId = currentRow.semiProductId || currentRow.id;

        if (!semiProductId) {
          this.$message.error('无法获取半成品ID，删除失败');
          return;
        }

        await this.$confirm(
          `确认删除半成品 "${currentRow.semiProductName || '未命名半成品'}"？\nID: ${semiProductId}`,
          '删除确认',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        await delSemiFinishedProduct(semiProductId);
        this.$message.success('删除成功');
        await this.fetchData();
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除');
        } else {
          console.error('删除失败:', error);
          this.$message.error('删除失败: ' + (error.message || '未知错误'));
        }
      }
    },

    handleSelectionChange(val) {
      this.selectRows = val;
    },

    async handleDelList() {
      if (this.selectRows.length === 0) {
        this.$message.warning('请先选择要删除的数据');
        return;
      }

      try {
        const selectedRows = this.selectRows.map(row =>
          JSON.parse(JSON.stringify(row))
        );

        const idsMap = selectedRows.map((row, index) => {
          const semiProductId = row.semiProductId || row.id;
          return { index, semiProductId, name: row.semiProductName };
        });

        const validEntries = idsMap.filter(entry => entry.semiProductId);

        if (validEntries.length < idsMap.length) {
          const missingIds = idsMap.filter(entry => !entry.semiProductId).map(entry => entry.index);
          this.$message.error(`部分记录(行 ${missingIds.join(', ')})缺少有效ID，无法删除`);
          return;
        }

        const idsToDelete = validEntries.map(entry => entry.semiProductId);
        const selectedNames = validEntries.map(entry => entry.name || '未命名半成品').join('", "');

        await this.$confirm(
          `确认删除以下半成品?\n半成品名称: "${selectedNames}"\nID列表: [${idsToDelete.join(', ')}]`,
          '批量删除确认',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        await delBatchSemiFinishedProduct(idsToDelete);
        this.$message.success('批量删除成功');
        await this.fetchData();
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消批量删除');
        } else {
          console.error('批量删除失败:', error);
          this.$message.error('批量删除失败: ' + (error.message || '未知错误'));
        }
      }
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.fetchData();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    },

    handleSortChange({ column, prop, order }) {
      this.sortField = prop;
      this.sortOrder = order === 'ascending' ? 'asc' : (order === 'descending' ? 'desc' : null);
      this.fetchData();
    },

    tableRowClassName({ row }) {
      if (row.currentStock <= (row.minStockQuantity || 0)) {
        return 'warning-row';
      }
      return '';
    },

    getStatusTagType(status) {
      switch (status) {
        case '待处理':
          return 'info';
        case '处理中':
          return 'warning';
        case '已完成':
          return 'success';
        default:
          return '';
      }
    },

    handleInbound(row) {
      try {
        const currentRow = JSON.parse(JSON.stringify(row));
        const semiProductId = currentRow.semiProductId || currentRow.id;

        if (!semiProductId) {
          this.$message.error('无法获取半成品ID，入库失败');
          return;
        }

        this.inOutSemiFinishedData = currentRow;
        this.inOutOperationType = 'inbound';
        this.inOutDialogVisible = true;
      } catch (error) {
        console.error('打开入库对话框失败:', error);
        this.$message.error('打开入库对话框失败');
      }
    },

    handleOutbound(row) {
      try {
        const currentRow = JSON.parse(JSON.stringify(row));
        const semiProductId = currentRow.semiProductId || currentRow.id;

        if (!semiProductId) {
          this.$message.error('无法获取半成品ID，出库失败');
          return;
        }

        this.inOutSemiFinishedData = currentRow;
        this.inOutOperationType = 'outbound';
        this.inOutDialogVisible = true;
      } catch (error) {
        console.error('打开出库对话框失败:', error);
        this.$message.error('打开出库对话框失败');
      }
    },

    // 查看物料区域分布
    handleViewZoneDistribution(row) {
      if (!row.fields3 && !row.semiProductId) {
        this.$message.warning('物料ID不能为空');
        return;
      }

      console.log('查看区域分布 - 初级半成品数据:', row);

      this.selectedMaterial = {
        fields3: row.fields3 || row.semiProductId,
        materialName: row.semiProductName,
        materialType: '一级半成品' // 使用中文类型，与库存明细数据保持一致
      };
      this.zoneDialogVisible = true;
    },

         getBoardTypeTagType(boardTypeName) {
       if (boardTypeName === '上板') {
         return 'success';
       } else if (boardTypeName === '下板') {
         return 'info';
       } else if (boardTypeName === '单板') {
         return 'warning';
       } else {
         return '';
       }
     },

     getStyleTagType(styleName) {
       if (styleName === '标准款' || styleName === '统一款式') {
         return 'success';
       } else if (styleName === '蓝牙款') {
         return 'primary';
       } else if (styleName === '无时款' || styleName === '无时款') {
         return 'info';
       } else if (styleName === '按键款') {
         return 'warning';
       } else if (styleName && styleName.startsWith('款式1')) {
         return 'success';
       } else if (styleName && styleName.startsWith('款式2')) {
         return 'info';
       } else if (styleName && styleName.startsWith('款式3')) {
         return 'warning';
       } else if (styleName && styleName.startsWith('款式')) {
         return 'danger';
       } else {
         return '';
       }
     }
  }
};
</script>

<style scoped>
/* 添加下拉样式 */
:deep(.type-select),
:deep(.status-select) {
  width: 120px;
}

:deep(.type-select-dropdown),
:deep(.status-select-dropdown) {
  min-width: 120px;
}

/* 状态样式 */
.warning-status {
  color: #f56c6c;
  font-weight: bold;
}

.normal-status {
  color: #67c23a;
  font-weight: bold;
}

/* 优化筛选条件标签样式 */
.active-filters {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: var(--base-menu-background, #f5f7fa);
  color: var(--base-text-color, #303133);
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.filter-label {
  margin-right: 12px;
  font-weight: 500;
  color: var(--base-text-color-secondary, #606266);
}

.filter-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  color: #303133 !important;
}

/* 深色主题 - 表格行样式 */
.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  color: var(--theme-color) !important;
}

/* 区域链接按钮样式 */
.zone-link-btn {
  color: var(--current-color);
  font-size: 12px;
  padding: 2px 6px;
  transition: all 0.3s ease;

  &:hover {
    color: var(--color-2);
    background-color: rgba(var(--current-color-rgb), 0.1);
  }

  &:disabled {
    color: var(--base-color-3);
    cursor: not-allowed;
  }

  .el-icon-location {
    color: inherit;
  }
}

/* 表格优化样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        color: #303133;
        font-weight: 600;
        font-size: 14px;
        border: none;
        padding: 16px 12px;

        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8faff !important;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        &.el-table__row--striped {
          background-color: #fafbfc;
        }

        td {
          border: none;
          padding: 16px 12px;
          font-size: 13px;

          .cell {
            display: flex;
            align-items: center;
            justify-content: center;

            &.el-tooltip {
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }

  /* 预警行高亮 */
  .warning-row {
    background: linear-gradient(90deg, #fef0f0 0%, #fde2e2 100%) !important;

    &:hover {
      background: linear-gradient(90deg, #fde2e2 0%, #fecaca 100%) !important;
    }
  }

  /* 边框样式 */
  &.el-table--border {
    border: 1px solid #e4e7ed;

    &::after {
      display: none;
    }

    &::before {
      display: none;
    }
  }

  /* 空数据样式 */
  .el-table__empty-block {
    padding: 60px 0;

    .el-table__empty-text {
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;

  &.el-tag--success {
    background: linear-gradient(135deg, #67c23a, #85ce61);
    color: white;
  }

  &.el-tag--danger {
    background: linear-gradient(135deg, #f56c6c, #f78989);
    color: white;
  }

  &.el-tag--warning {
    background: linear-gradient(135deg, #e6a23c, #ebb563);
    color: white;
  }

  &.el-tag--info {
    background: linear-gradient(135deg, #909399, #a6a9ad);
    color: white;
  }
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.el-button--primary {
    background: linear-gradient(135deg, #409eff, #66b1ff);
    border: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
    }
  }

  &.el-button--danger {
    background: linear-gradient(135deg, #f56c6c, #f78989);
    border: none;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(245, 108, 108, 0.4);
    }
  }

  &.el-button--small,
  &.el-button--mini {
    padding: 8px 15px;
    font-size: 12px;
    border-radius: 4px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: center;

  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;

    &.active {
      background: linear-gradient(135deg, #409eff, #66b1ff);
      color: white;
    }
  }

  .el-pagination__sizes .el-select .el-input .el-input__inner {
    border-radius: 4px;
  }

  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
}
</style>

<template>
  <el-dialog 
    :title="dialogTitle" 
    width="400px" 
    destroy-on-close 
    :visible.sync="dialogVisible"
    @close="handleClose"
    :close-on-click-modal="false"
    class="in-out-dialog">
    <el-form :model="form" ref="formRef" :label-width="80" status-icon>
      <el-form-item label="零件ID" prop="componentId">
        <el-input v-model="form.componentId" placeholder="零件ID" disabled size="small" />
      </el-form-item>
      
      <el-form-item label="零件名称" prop="componentName">
        <el-input v-model="form.componentName" placeholder="零件名称" disabled size="small" />
      </el-form-item>
      
      <el-form-item label="数量" prop="quantity" required>
        <el-input-number 
          v-model="form.quantity" 
          :min="1" 
          :max="operationType === 'outbound' ? currentStock : 999999"
          placeholder="请输入数量" 
          size="small"
          style="width: 100%;" />
      </el-form-item>
      
      <el-form-item label="操作者" prop="userName" required>
        <el-input v-model="form.userName" placeholder="请输入操作者姓名" clearable size="small" />
      </el-form-item>
      
      <el-form-item v-if="operationType === 'outbound'" label="当前库存">
        <el-tag type="info" size="small">
          <i class="el-icon-box" style="margin-right: 4px;"></i>
          {{ currentStock }}
        </el-tag>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取消</el-button>
      <el-button type="primary" @click="handleOk" size="small">
        <i :class="operationType === 'inbound' ? 'el-icon-bottom' : 'el-icon-top'" style="margin-right: 4px;"></i>
        {{ operationType === 'inbound' ? '确认入库' : '确认出库' }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { inboundComponentWarehouse, outboundComponentWarehouse } from '@/api/jenasi/componentWarehouse';

export default {
  name: 'ComponentInOutDialog',
  props: {
    value: Boolean,
    operationType: {
      type: String,
      default: 'inbound' // 'inbound' 或 'outbound'
    },
    componentData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        componentId: '',
        componentName: '',
        quantity: 1,
        userName: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    dialogTitle() {
      return this.operationType === 'inbound' ? '零件入库' : '零件出库';
    },
    currentStock() {
      return this.componentData.currentStock || 0;
    }
  },
  watch: {
    componentData: {
      handler(newData) {
        if (newData) {
          this.form.componentId = newData.componentId || newData.id || '';
          this.form.componentName = newData.componentName || '';
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.form = {
        componentId: '',
        componentName: '',
        quantity: 1,
        userName: ''
      };
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          const params = {
            componentId: this.form.componentId,
            quantity: this.form.quantity,
            userName: this.form.userName
          };
          
          if (this.operationType === 'inbound') {
            await inboundComponentWarehouse(params);
            this.$message.success('入库成功');
          } else {
            // 检查出库数量是否超过库存
            if (this.form.quantity > this.currentStock) {
              this.$message.error(`出库数量不能超过当前库存(${this.currentStock})`);
              return;
            }
            await outboundComponentWarehouse(params);
            this.$message.success('出库成功');
          }
          
          this.$emit('ok');
          this.handleClose();
        }
      } catch (error) {
        console.error(`${this.operationType === 'inbound' ? '入库' : '出库'}失败:`, error);
        const errorMessage = (error.response && error.response.data && error.response.data.message) || error.message || '未知错误';
        this.$message.error(`${this.operationType === 'inbound' ? '入库' : '出库'}失败: ${errorMessage}`);
      }
    }
  }
};
</script>

<style scoped>
.in-out-dialog :deep(.el-dialog) {
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.in-out-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
}

.in-out-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.in-out-dialog :deep(.el-dialog__headerbtn) {
  top: 15px;
  right: 15px;
}

.in-out-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.in-out-dialog :deep(.el-dialog__body) {
  padding: 20px;
  background: var(--base-background, #ffffff);
}

.in-out-dialog :deep(.el-form-item) {
  margin-bottom: 18px;
}

.in-out-dialog :deep(.el-form-item__label) {
  color: var(--base-text-color, #303133);
  font-weight: 500;
}

.in-out-dialog :deep(.el-input__inner) {
  border-radius: 4px;
  border: 1px solid var(--base-border-color, #dcdfe6);
  background: var(--base-background, #ffffff);
  color: var(--base-text-color, #303133);
}

.in-out-dialog :deep(.el-input.is-disabled .el-input__inner) {
  background-color: var(--base-menu-background, #f5f7fa);
  color: var(--base-text-color-secondary, #909399);
}

.in-out-dialog :deep(.el-input-number) {
  width: 100%;
}

.in-out-dialog :deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.dialog-footer {
  text-align: right;
  padding: 15px 20px;
  background: var(--base-menu-background, #f5f7fa);
  border-radius: 0 0 8px 8px;
  border-top: 1px solid var(--base-border-color, #ebeef5);
}

.dialog-footer .el-button {
  margin-left: 10px;
  border-radius: 4px;
  font-weight: 500;
  padding: 8px 16px;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border: none;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.dialog-footer .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
}

/* 主题适配 */
:deep(.theme-dark) {
  .in-out-dialog .el-dialog__body {
    background: var(--base-background);
  }
  
  .dialog-footer {
    background: var(--base-menu-background);
    border-top-color: var(--base-border-color);
  }
  
  .el-input__inner {
    background: var(--base-background);
    border-color: var(--base-border-color);
    color: var(--base-text-color);
  }
  
  .el-input.is-disabled .el-input__inner {
    background-color: var(--base-menu-background);
    color: var(--base-text-color-secondary);
  }
}
</style> 
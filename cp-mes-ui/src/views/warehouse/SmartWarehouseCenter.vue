<template>
    <div class="smart-warehouse-center">

    <!-- 导航模式切换和面包屑导航 -->
    <div class="navigation-header">
      <!-- 导航模式切换 -->
      <div class="navigation-mode-switch">
        <el-tooltip content="切换导航模式" placement="top">
          <el-switch
            v-model="isHierarchicalMode"
            active-text="层级导航"
            inactive-text="Tab导航"
            active-color="#409EFF"
            inactive-color="#909399"
            @change="handleNavigationModeChange"
          />
        </el-tooltip>
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb-section" v-if="showBreadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item @click.native="navigateToLevel('root')" class="breadcrumb-clickable">
            <i class="el-icon-office-building"></i>
            仓储中心
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-if="selectedWarehouseName"
            @click.native="navigateToLevel('warehouse')"
            :class="{ 'breadcrumb-clickable': isHierarchicalMode && currentLevel !== 'warehouse' }"
          >
            <i class="el-icon-setting"></i>
            {{ selectedWarehouseName }}
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-if="selectedZoneName"
            @click.native="navigateToLevel('zone')"
            :class="{ 'breadcrumb-clickable': isHierarchicalMode && currentLevel !== 'zone' }"
          >
            <i class="el-icon-place"></i>
            {{ selectedZoneName }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <!-- Tab导航模式 - 功能模块切换 -->
    <div class="tab-navigation" v-if="!isHierarchicalMode">
      <el-button-group>
        <el-button
          :type="activeTab === 'warehouse' ? 'primary' : ''"
          @click="navigateToWarehouse"
        >
          <i class="el-icon-setting"></i>
          仓库管理
        </el-button>
        <el-button
          :type="activeTab === 'zone' ? 'primary' : ''"
          @click="navigateToZone"
        >
          <i class="el-icon-place"></i>
          区域管理
        </el-button>

        <el-button
          :type="activeTab === 'inventory' ? 'primary' : ''"
          @click="navigateToInventory"
        >
          <i class="el-icon-document-checked"></i>
          库存明细
        </el-button>
      </el-button-group>
    </div>

    <!-- 层级导航模式 - 返回按钮 -->
    <div class="hierarchical-navigation" v-if="isHierarchicalMode && currentLevel !== 'warehouse'">
      <el-button
        type="text"
        icon="el-icon-arrow-left"
        @click="navigateBack"
        class="back-button"
      >
        返回{{ getBackButtonText() }}
      </el-button>
    </div>

    <!-- 功能模块内容 -->
    <div class="content-area">
      <!-- Tab导航模式 -->
      <div v-if="!isHierarchicalMode" class="tab-content">
        <!-- 仓库管理 -->
        <div v-show="activeTab === 'warehouse'">
          <WarehouseManagement ref="warehouseManagementRef" @manage-zones="handleManageZones" />
        </div>

        <!-- 区域管理 -->
        <div v-show="activeTab === 'zone'">
          <WarehouseZoneManagement
            ref="zoneManagementRef"
            :warehouseId="selectedWarehouseId"
            :warehouseName="selectedWarehouseName"
            :warehouseCode="selectedWarehouseCode"
            :warehouseType="selectedWarehouseType"
            :warehouseTypeName="selectedWarehouseTypeName"
            @go-to-detail="handleGoToDetail"
          />
        </div>

        <!-- 库存明细 -->
        <div v-show="activeTab === 'inventory'">
          <InventoryDetail
            ref="inventoryDetailRef"
            :zoneId="selectedZoneId"
            :zoneName="selectedZoneName"
            :warehouseId="selectedWarehouseId"
            :warehouseName="selectedWarehouseName"
          />
        </div>
      </div>

      <!-- 层级导航模式 -->
      <div v-else class="hierarchical-content">
        <!-- 仓库管理层级 -->
        <div v-if="currentLevel === 'warehouse'">
          <WarehouseManagement
            ref="warehouseManagementHierarchicalRef"
            @manage-zones="handleHierarchicalWarehouseClick"
            :hierarchical-mode="true"
          />
        </div>

        <!-- 区域管理层级 -->
        <div v-else-if="currentLevel === 'zone'">
          <WarehouseZoneManagement
            ref="zoneManagementHierarchicalRef"
            :warehouseId="selectedWarehouseId"
            :warehouseName="selectedWarehouseName"
            :warehouseCode="selectedWarehouseCode"
            :warehouseType="selectedWarehouseType"
            :warehouseTypeName="selectedWarehouseTypeName"
            @go-to-detail="handleHierarchicalZoneClick"
            :hierarchical-mode="true"
          />
        </div>

        <!-- 库存明细层级 -->
        <div v-else-if="currentLevel === 'inventory'">
          <InventoryDetail
            ref="inventoryDetailHierarchicalRef"
            :zoneId="selectedZoneCode"
            :zoneName="selectedZoneName"
            :warehouseId="selectedWarehouseId"
            :warehouseName="selectedWarehouseName"
            :hierarchical-mode="true"
          />
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import WarehouseManagement from './WarehouseManagement.vue';
import WarehouseZoneManagement from './components/WarehouseZoneManagement.vue';

import InventoryDetail from './InventoryDetail.vue';

export default {
  name: 'SmartWarehouseCenter',
  components: {
    WarehouseManagement,
    WarehouseZoneManagement,
    InventoryDetail
  },
  data() {
    return {
      // Tab导航模式相关
      activeTab: 'warehouse',

      // 层级导航模式相关
      isHierarchicalMode: false, // 是否为层级导航模式
      currentLevel: 'warehouse', // 当前层级：warehouse, zone, inventory

      // 选中的数据
      selectedZoneId: null,
      selectedZoneName: '',
      selectedZoneCode: '', // 添加区域编码
      selectedWarehouseId: null,
      selectedWarehouseName: '',
      selectedWarehouseCode: '',
      selectedWarehouseType: '',
      selectedWarehouseTypeName: '',

      // 导航历史栈（用于层级导航的返回功能）
      navigationHistory: []
    };
  },
  computed: {
    // 是否显示面包屑导航
    showBreadcrumb() {
      if (this.isHierarchicalMode) {
        // 层级导航模式下，总是显示面包屑
        return true;
      } else {
        // Tab导航模式下，有选中数据时显示
        return this.selectedWarehouseName || this.selectedZoneName;
      }
    }
  },
  mounted() {
    this.initNavigationMode();
    this.initPageData();
  },
  watch: {
    // 监听路由变化，重新初始化页面数据
    '$route'(to, from) {
      this.initPageData();
    }
  },
  methods: {
    // 初始化导航模式
    initNavigationMode() {
      // 从localStorage读取用户选择的导航模式
      const savedMode = localStorage.getItem('warehouse-navigation-mode');
      if (savedMode !== null) {
        this.isHierarchicalMode = savedMode === 'hierarchical';
      }

      // 根据路由参数确定当前层级
      this.determineCurrentLevel();
    },

    // 根据路由参数确定当前层级
    determineCurrentLevel() {
      const { tab, warehouseId, zoneId } = this.$route.query;

      if (this.isHierarchicalMode) {
        if (zoneId) {
          this.currentLevel = 'inventory';
        } else if (warehouseId) {
          this.currentLevel = 'zone';
        } else {
          this.currentLevel = 'warehouse';
        }
      } else {
        // Tab导航模式下，根据tab参数设置activeTab
        if (tab && ['warehouse', 'zone', 'inventory'].includes(tab)) {
          this.activeTab = tab;
        }
      }
    },

    // 处理导航模式切换
    handleNavigationModeChange(isHierarchical) {
      console.log('导航模式切换:', isHierarchical ? '层级导航' : 'Tab导航');

      // 保存用户选择到localStorage
      localStorage.setItem('warehouse-navigation-mode', isHierarchical ? 'hierarchical' : 'tab');

      if (isHierarchical) {
        // 切换到层级导航模式
        this.switchToHierarchicalMode();
      } else {
        // 切换到Tab导航模式
        this.switchToTabMode();
      }
    },

    // 切换到层级导航模式
    switchToHierarchicalMode() {
      // 根据当前选中的数据确定层级
      if (this.selectedZoneId && this.selectedZoneName) {
        this.currentLevel = 'inventory';
      } else if (this.selectedWarehouseId && this.selectedWarehouseName) {
        this.currentLevel = 'zone';
      } else {
        this.currentLevel = 'warehouse';
      }

      // 更新路由参数
      this.updateRouteForHierarchicalMode();
    },

    // 切换到Tab导航模式
    switchToTabMode() {
      // 根据当前层级设置activeTab
      if (this.currentLevel === 'inventory') {
        this.activeTab = 'inventory';
      } else if (this.currentLevel === 'zone') {
        this.activeTab = 'zone';
      } else {
        this.activeTab = 'warehouse';
      }

      // 更新路由参数
      this.updateRouteForTabMode();
    },

    // 更新路由参数（层级导航模式）
    updateRouteForHierarchicalMode() {
      const query = { ...this.$route.query };
      delete query.tab; // 移除tab参数

      // 根据当前层级设置参数
      if (this.currentLevel === 'inventory' && this.selectedZoneId) {
        query.zoneId = this.selectedZoneId;
        query.zoneCode = this.selectedZoneCode;
        query.zoneName = this.selectedZoneName;
        query.warehouseId = this.selectedWarehouseId;
        query.warehouseName = this.selectedWarehouseName;
      } else if (this.currentLevel === 'zone' && this.selectedWarehouseId) {
        query.warehouseId = this.selectedWarehouseId;
        query.warehouseName = this.selectedWarehouseName;
        query.warehouseCode = this.selectedWarehouseCode;
        // 移除区域相关参数
        delete query.zoneId;
        delete query.zoneCode;
        delete query.zoneName;
      } else {
        // 仓库层级，移除所有选中参数
        delete query.warehouseId;
        delete query.warehouseName;
        delete query.warehouseCode;
        delete query.zoneId;
        delete query.zoneCode;
        delete query.zoneName;
      }

      this.$router.replace({ query }).catch(() => {});
    },

    // 更新路由参数（Tab导航模式）
    updateRouteForTabMode() {
      const query = { ...this.$route.query };
      query.tab = this.activeTab;

      this.$router.replace({ query }).catch(() => {});
    },

    // 初始化页面数据
    initPageData() {
      // 检查路由参数，确定默认激活的标签页
      const { tab, zoneId, zoneCode, zoneName, warehouseId, warehouseName, warehouseCode } = this.$route.query;
      console.log('智慧仓储中心 - 初始化数据', { tab, zoneId, zoneCode, zoneName, warehouseId, warehouseName, warehouseCode });

      if (tab && ['warehouse', 'zone', 'inventory'].includes(tab)) {
        this.activeTab = tab;
      }

      // 重置区域信息
      this.selectedZoneId = null;
      this.selectedZoneName = '';
      this.selectedZoneCode = ''; // 修复：添加zoneCode重置

      // 重置仓库信息
      this.selectedWarehouseId = null;
      this.selectedWarehouseName = '';
      this.selectedWarehouseCode = '';
      this.selectedWarehouseType = '';
      this.selectedWarehouseTypeName = '';

      // 如果从仓库管理跳转过来，设置选中的仓库信息
      if (warehouseId && warehouseName) {
        this.selectedWarehouseId = warehouseId;
        this.selectedWarehouseName = warehouseName;
        this.selectedWarehouseCode = warehouseCode || '';
        console.log('设置仓库信息', {
          selectedWarehouseId: this.selectedWarehouseId,
          selectedWarehouseName: this.selectedWarehouseName,
          selectedWarehouseCode: this.selectedWarehouseCode
        });
      }

      // 如果从区域管理跳转过来，设置选中的区域信息
      if (zoneId && zoneName) {
        this.selectedZoneId = zoneId;
        this.selectedZoneName = zoneName;
        this.selectedZoneCode = zoneCode || ''; // 修复：添加zoneCode设置
        console.log('设置区域信息', {
          selectedZoneId: this.selectedZoneId,
          selectedZoneName: this.selectedZoneName,
          selectedZoneCode: this.selectedZoneCode
        });
      }

      this.fetchOverviewStats();
    },

    // 层级导航 - 面包屑点击导航
    navigateToLevel(level) {
      if (!this.isHierarchicalMode) return;

      console.log('面包屑导航到层级:', level);

      switch (level) {
        case 'root':
          this.navigateToWarehouseLevel();
          break;
        case 'warehouse':
          if (this.currentLevel !== 'warehouse') {
            this.navigateToZoneLevel();
          }
          break;
        case 'zone':
          if (this.currentLevel === 'inventory') {
            // 从库存明细返回到区域管理，保持当前区域选中状态
            this.currentLevel = 'zone';
            this.updateRouteForHierarchicalMode();
          }
          break;
      }
    },

    // 导航到仓库层级
    navigateToWarehouseLevel() {
      this.currentLevel = 'warehouse';
      this.clearSelectedData();
      this.updateRouteForHierarchicalMode();
    },

    // 导航到区域层级
    navigateToZoneLevel() {
      if (this.selectedWarehouseId) {
        this.currentLevel = 'zone';
        // 清除区域选中数据，但保留仓库数据
        this.selectedZoneId = null;
        this.selectedZoneName = '';
        this.selectedZoneCode = ''; // 修复：添加zoneCode清除
        this.updateRouteForHierarchicalMode();
      }
    },

    // 导航到库存明细层级
    navigateToInventoryLevel() {
      if (this.selectedZoneId) {
        this.currentLevel = 'inventory';
        this.updateRouteForHierarchicalMode();
      }
    },

    // 返回上一级
    navigateBack() {
      console.log('返回上一级，当前层级:', this.currentLevel);

      switch (this.currentLevel) {
        case 'zone':
          this.navigateToWarehouseLevel();
          break;
        case 'inventory':
          this.navigateToZoneLevel();
          break;
      }
    },

    // 获取返回按钮文本
    getBackButtonText() {
      switch (this.currentLevel) {
        case 'zone':
          return '仓库列表';
        case 'inventory':
          return '区域列表';
        default:
          return '';
      }
    },

    // 清除选中数据
    clearSelectedData() {
      this.selectedWarehouseId = null;
      this.selectedWarehouseName = '';
      this.selectedWarehouseCode = '';
      this.selectedWarehouseType = '';
      this.selectedWarehouseTypeName = '';
      this.selectedZoneId = null;
      this.selectedZoneName = '';
      this.selectedZoneCode = ''; // 修复：添加zoneCode清除
    },

    // 层级导航 - 处理仓库点击
    handleHierarchicalWarehouseClick(warehouseInfo) {
      console.log('层级导航 - 仓库点击:', warehouseInfo);

      // 设置选中的仓库信息
      this.selectedWarehouseId = warehouseInfo.warehouseId;
      this.selectedWarehouseName = warehouseInfo.warehouseName;
      this.selectedWarehouseCode = warehouseInfo.warehouseCode;
      this.selectedWarehouseType = warehouseInfo.warehouseType;
      this.selectedWarehouseTypeName = warehouseInfo.warehouseTypeName;

      // 导航到区域层级
      this.currentLevel = 'zone';
      this.updateRouteForHierarchicalMode();
    },

    // 层级导航 - 处理区域点击
    handleHierarchicalZoneClick(zoneInfo) {
      console.log('层级导航 - 区域点击:', zoneInfo);

      // 设置选中的区域信息
      this.selectedZoneId = zoneInfo.zoneId;
      this.selectedZoneName = zoneInfo.zoneName;
      this.selectedZoneCode = zoneInfo.zoneCode; // 修复：添加zoneCode
      this.selectedWarehouseId = zoneInfo.warehouseId;

      // 导航到库存明细层级
      this.currentLevel = 'inventory';
      this.updateRouteForHierarchicalMode();
    },

    // 获取概览统计数据
    fetchOverviewStats() {
      // 模拟获取统计数据
      this.overviewStats = {
        totalWarehouses: 8,
        totalZones: 24,
        totalItems: 1256
      };
    },

    // 导航到仓库管理
    navigateToWarehouse() {
      this.activeTab = 'warehouse';
      // 更新路由查询参数，但不刷新页面
      if (this.$route.query.tab !== 'warehouse') {
        this.$router.replace({
          path: this.$route.path,
          query: { ...this.$route.query, tab: 'warehouse' }
        });
      }
    },

    // 导航到区域管理
    navigateToZone() {
      this.activeTab = 'zone';
      // 更新路由查询参数，但不刷新页面
      if (this.$route.query.tab !== 'zone') {
        this.$router.replace({
          path: this.$route.path,
          query: { ...this.$route.query, tab: 'zone' }
        });
      }
    },



    // 导航到库存明细
    navigateToInventory() {
      this.activeTab = 'inventory';
      // 更新路由查询参数，但不刷新页面
      if (this.$route.query.tab !== 'inventory') {
        this.$router.replace({
          path: this.$route.path,
          query: { ...this.$route.query, tab: 'inventory' }
        });
      }
    },

    // 处理仓库管理的区域管理事件
    handleManageZones(warehouseInfo) {
      console.log('切换到区域管理', warehouseInfo);
      // 设置选中的仓库信息
      this.selectedWarehouseId = warehouseInfo.warehouseId;
      this.selectedWarehouseName = warehouseInfo.warehouseName;
      this.selectedWarehouseCode = warehouseInfo.warehouseCode;
      this.selectedWarehouseType = warehouseInfo.warehouseType;
      this.selectedWarehouseTypeName = warehouseInfo.warehouseTypeName;

      // 切换到区域管理页面
      this.activeTab = 'zone';

      // 更新路由查询参数
      this.$router.replace({
        path: this.$route.path,
        query: {
          ...this.$route.query,
          tab: 'zone',
          warehouseId: warehouseInfo.warehouseId,
          warehouseName: warehouseInfo.warehouseName,
          warehouseCode: warehouseInfo.warehouseCode
        }
      });
    },

    // 处理区域管理的库存明细跳转事件
    handleGoToDetail(zoneInfo) {
      console.log('切换到库存明细', zoneInfo);
      // 设置选中的区域信息
      this.selectedZoneId = zoneInfo.zoneId;
      this.selectedZoneName = zoneInfo.zoneName;
      this.selectedWarehouseId = zoneInfo.warehouseId;
      // 可以在这里根据 warehouseId 查找 warehouseName

      // 切换到库存明细页面
      this.activeTab = 'inventory';

      // 更新路由查询参数 - 修复：传递正确的参数
      this.$router.replace({
        path: this.$route.path,
        query: {
          ...this.$route.query,
          tab: 'inventory',
          zoneId: zoneInfo.zoneId,
          zoneCode: zoneInfo.zoneCode,  // 添加zoneCode参数
          zoneName: zoneInfo.zoneName,
          warehouseId: zoneInfo.warehouseId,
          warehouseName: zoneInfo.warehouseName || this.selectedWarehouseName
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.smart-warehouse-center {
  padding: 20px;
}

/* 导航头部区域 */
.navigation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.navigation-mode-switch {
  display: flex;
  align-items: center;

  .el-switch {
    margin-left: 10px;
  }
}

.breadcrumb-section {
  flex: 1;
  margin-left: 20px;

  .breadcrumb-clickable {
    cursor: pointer;
    color: #409eff;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }
}

/* 层级导航返回按钮 */
.hierarchical-navigation {
  margin-bottom: 16px;

  .back-button {
    font-size: 14px;
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }

    i {
      margin-right: 5px;
    }
  }
}

.tab-navigation {
  margin-bottom: 20px;
}

.tab-content {
  margin-top: 20px;
}

.tab-navigation {
  margin-bottom: 20px;
  text-align: center;

  .el-button-group {
    .el-button {
      border-radius: 8px;
      font-weight: 600;
      padding: 12px 20px;
      border: 1px solid var(--border-color-1);
      background: var(--base-main-bg);
      color: var(--theme-color);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(var(--current-color-rgb), 0.1);
        border-color: var(--current-color);
        color: var(--current-color);
        transform: translateY(-2px);
      }

      &.el-button--primary {
        background: var(--current-color);
        border-color: var(--current-color);
        color: white;

        &:hover {
          background: var(--color-2);
          border-color: var(--color-2);
          transform: translateY(-2px);
        }
      }

      i {
        margin-right: 8px;
      }
    }
  }
}

.tab-content {
  // 内容区域无额外样式，直接展示子组件
}







// 样式适配
:deep(.el-card) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);

  .el-card__header {
    background: var(--base-color-9);
    border-bottom-color: var(--border-color-1);
    color: var(--theme-color);
    font-weight: 600;
  }

  .el-card__body {
    background: var(--base-main-bg);
  }
}

:deep(.el-button-group) {
  .el-button {
    border-color: var(--border-color-1);
    background: var(--base-main-bg);
    color: var(--theme-color);

    &:hover {
      color: var(--current-color);
      border-color: var(--current-color);
    }

    &.el-button--primary {
      background: var(--current-color);
      border-color: var(--current-color);
      color: white;
    }
  }
}

:deep(.el-tag) {
  border-color: transparent;

  &.el-tag--light {
    background: rgba(var(--current-color-rgb), 0.1);
    color: var(--current-color);
  }
 }
</style>

<template>
  <el-dialog 
    title="添加零件" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="零部件名称" prop="componentName" required>
        <el-input v-model="form.componentName" placeholder="请输入零部件名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType">
        <el-input v-model="form.materialType" placeholder="请输入物料类型" clearable />
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addComponentWarehouse } from '@/api/jenasi/componentWarehouse';

export default {
  name: 'ComponentDialogAdd',
  props: {
    value: Boolean // Vue 2使用value而不是modelValue
  },
  data() {
    return {
      form: {
        componentName: '',
        materialType: '零部件',
        currentStock: 0
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          await addComponentWarehouse(this.form);
          this.$message.success('新增成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('新增失败:', error);
        this.$message.error('新增失败，请重试');
      }
    }
  }
};
</script>
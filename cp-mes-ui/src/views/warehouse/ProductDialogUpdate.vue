<template>
  <el-dialog 
    title="修改成品" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="成品名称" prop="productName" required>
        <el-input v-model="form.productName" placeholder="请输入成品名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType">
        <el-input v-model="form.materialType" placeholder="请输入物料类型" clearable />
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>

      <el-form-item label="功能类型" prop="styleId">
        <el-select v-model="form.styleId" placeholder="请选择功能类型" clearable>
          <el-option label="蓝牙款" :value="1" />
          <el-option label="无时款" :value="2" />
          <el-option label="按键款" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="系列类型" prop="seriesId">
        <el-select v-model="form.seriesId" placeholder="请选择系列类型" clearable>
          <el-option label="一体机系列" :value="1" />
          <el-option label="电脑编程系列" :value="2" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="存放区域" prop="regionId">
        <el-input v-model="form.regionId" placeholder="请输入存放区域" clearable />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateProductWarehouse } from '@/api/jenasi/productWarehouse';

export default {
  name: 'ProductDialogUpdate',
  props: {
    value: Boolean,
    row: Object
  },
  data() {
    return {
      form: {
        productId: '',
        productName: '',
        materialType: '',
        currentStock: 0,
        styleId: null,
        seriesId: null,
        stockQuantity: 0,
        regionId: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    row: {
      handler(newRow) {
        if (newRow) {
          this.form = { ...newRow };
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          await updateProductWarehouse(this.form);
          this.$message.success('修改成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('修改失败:', error);
        this.$message.error('修改失败，请重试');
      }
    }
  }
};
</script>
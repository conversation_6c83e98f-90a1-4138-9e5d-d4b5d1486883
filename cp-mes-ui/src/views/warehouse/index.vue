<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-box"></i>
        仓储管理系统
      </h2>
      <p class="page-description">统一管理各类物料的库存信息和出入库操作</p>
    </div>

    <!-- 导航选项卡 -->
    <el-card class="nav-card" shadow="never">
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <!-- 库存总览 - 已隐藏 -->
        <!-- 
        <el-tab-pane label="库存总览" name="materialSummary">
          <template slot="label">
            <i class="el-icon-coin"></i>
           库存总览
          </template>
        </el-tab-pane>
        -->
        <el-tab-pane label="原料储备" name="rawMaterial">
          <template slot="label">
            <i class="el-icon-setting"></i>
           原料仓
          </template>
        </el-tab-pane>
        <el-tab-pane label="零件仓库" name="component">
          <template slot="label">
            <i class="el-icon-box"></i>
           零件仓
          </template>
        </el-tab-pane>
        <el-tab-pane label="初级半成品仓库" name="semiFinished">
          <template slot="label">
            <i class="el-icon-goods"></i>
            贴片仓
          </template>
        </el-tab-pane>
        <el-tab-pane label="二级半成品仓库" name="semiFinishedTwo">
          <template slot="label">
            <i class="el-icon-goods"></i> 
            线边仓
          </template>
        </el-tab-pane>
        <el-tab-pane label="成品仓库" name="product">
          <template slot="label">
            <i class="el-icon-data-analysis"></i>
            成品仓
          </template>
        </el-tab-pane>
        <!-- <el-tab-pane label="仓库管理" name="warehouseManagement">
          <template slot="label">
            <i class="el-icon-setting"></i>
            仓库管理
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="区域管理" name="zoneManagement">
          <template slot="label">
            <i class="el-icon-place"></i>
            区域管理
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="库存明细" name="inventoryDetail">
          <template slot="label">
            <i class="el-icon-document-checked"></i>
            库存明细
          </template>
        </el-tab-pane> -->
      </el-tabs>
    </el-card>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 库存总览 - 已隐藏 -->
      <!-- 
      <div v-show="activeTab === 'materialSummary'" class="tab-content">
        <MaterialSummaryTable ref="materialSummaryTableRef" />
      </div>
      -->

      <!-- 原料储备 -->
      <div v-show="activeTab === 'rawMaterial'" class="tab-content">
        <RawMaterialTable ref="rawMaterialTableRef" />
      </div>

      <!-- 零件仓库 -->
      <div v-show="activeTab === 'component'" class="tab-content">
        <ComponentTable ref="componentTableRef" />
      </div>

      <!-- 初级半成品仓库 -->
      <div v-show="activeTab === 'semiFinished'" class="tab-content">
        <SemiFinishedTable ref="semiFinishedTableRef" />
      </div>

      <!-- 二级半成品仓库 -->
      <div v-show="activeTab === 'semiFinishedTwo'" class="tab-content">
        <SemiFinishedTwoTable ref="semiFinishedTwoTableRef" />
      </div>

      <!-- 成品仓库 -->
      <div v-show="activeTab === 'product'" class="tab-content">
        <ProductTable ref="productTableRef" />
      </div>

      <!-- 仓库管理 -->
      <div v-show="activeTab === 'warehouseManagement'" class="tab-content">
        <WarehouseManagement ref="warehouseManagementRef" />
      </div>

      <!-- 区域管理 -->
      <div v-show="activeTab === 'zoneManagement'" class="tab-content">
        <WarehouseZoneManagement ref="zoneManagementRef" />
      </div>

      <!-- 库存明细 -->
      <div v-show="activeTab === 'inventoryDetail'" class="tab-content">
        <InventoryDetail ref="inventoryDetailRef" />
      </div>
    </div>
  </div>
</template>

<script>
import RawMaterialTable from './RawMaterialTable.vue';
import ComponentTable from './ComponentTable.vue';
import ProductTable from './ProductTable.vue';
import SemiFinishedTable from './SemiFinishedTable.vue';
// 库存总览组件 - 已隐藏
// import MaterialSummaryTable from './MaterialSummaryTable.vue';
import SemiFinishedTwoTable from './SemiFinishedTwoTable.vue';
// 注释掉不存在的组件，暂时使用半成品表格替代
// import EnhancedSemiFinishedTwoTable from './EnhancedSemiFinishedTwoTable.vue';
import WarehouseManagement from './WarehouseManagement.vue';
import WarehouseZoneManagement from './components/WarehouseZoneManagement.vue';
import InventoryDetail from './InventoryDetail.vue';

export default {
  name: 'WarehouseIndex',
  components: {
    RawMaterialTable,
    ComponentTable,
    ProductTable,
    SemiFinishedTable,
    // 库存总览组件 - 已隐藏
    // MaterialSummaryTable,
    SemiFinishedTwoTable,
    WarehouseManagement,
    WarehouseZoneManagement,
    InventoryDetail
  },
  data() {
    return {
      // 默认激活原料储备页面，因为库存总览已隐藏
      activeTab: 'rawMaterial'
    };
  },
  methods: {
    handleTabClick(tab) {
      this.activeTab = tab.name;
      
      // 更新路由参数
      const query = { ...this.$route.query, tab: tab.name };
      this.$router.replace({ query }).catch(() => {});
      
      // 刷新当前tab的数据
      this.refreshCurrentTabData();
    },
    
    refreshCurrentTabData() {
      this.$nextTick(() => {
        const currentTableRef = this.getCurrentTableRef();
        if (currentTableRef && currentTableRef.fetchData) {
          currentTableRef.fetchData();
        }
      });
    },
    
    getCurrentTableRef() {
      const refMap = {
        // 库存总览 - 已隐藏
        // materialSummary: this.$refs.materialSummaryTableRef,
        rawMaterial: this.$refs.rawMaterialTableRef,
        component: this.$refs.componentTableRef,
        semiFinished: this.$refs.semiFinishedTableRef,
        semiFinishedTwo: this.$refs.semiFinishedTwoTableRef,
        product: this.$refs.productTableRef,
        warehouseManagement: this.$refs.warehouseManagementRef,
        zoneManagement: this.$refs.zoneManagementRef,
        inventoryDetail: this.$refs.inventoryDetailRef
      };
      return refMap[this.activeTab];
    },
    
    getTabLabel() {
      const labels = {
        // 库存总览 - 已隐藏
        // materialSummary: '库存总览',
        rawMaterial: '原料储备',
        component: '零件仓库',
        semiFinished: '初级半成品仓库',
        semiFinishedTwo: '二级半成品仓库',
        product: '成品仓库',
        warehouseManagement: '仓库管理',
        zoneManagement: '区域管理',
        inventoryDetail: '库存明细'
      };
      return labels[this.activeTab] || '';
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: var(--base-body-background);
  min-height: calc(100vh - 84px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color-1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }

  &:hover::before {
    left: 100%;
  }

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--current-color);
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    
    i {
      margin-right: 10px;
      animation: bounce 2s infinite;
    }
  }

  .page-description {
    color: var(--base-color-3);
    margin: 0;
    font-size: 16px;
    position: relative;
    z-index: 1;
  }
}

.nav-card {
  margin-bottom: 20px;
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  
  :deep(.el-card__body) {
    padding: 20px;
    background: var(--base-main-bg);
  }
  
  :deep(.el-tabs__header) {
    margin: 0;
    background: var(--base-color-9);
    border-radius: 8px 8px 0 0;
    position: relative;
  }
  
  :deep(.el-tabs__nav) {
    display: flex;
    width: 100%;
  }
  
  :deep(.el-tabs__item) {
    flex: 1;
    text-align: center;
    color: var(--base-color-1);
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
    border-radius: 6px;
    margin: 4px;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--current-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &.is-active {
      color: var(--theme-color);
      font-weight: 600;
      background: var(--current-color);
      box-shadow: 0 2px 8px var(--tag-shadow-color-2);
      transform: translateY(-2px);
      
      &::before {
        opacity: 0.1;
      }
    }
    
    &:hover {
      color: var(--current-color);
      background: var(--base-main-bg);
      transform: translateY(-1px);
      
      &::before {
        opacity: 0.05;
      }
    }
  }
  
  :deep(.el-tabs__active-bar) {
    background: var(--current-color);
    height: 3px;
    border-radius: 2px;
  }
  
  :deep(.el-tabs__content) {
    padding: 0;
    margin: 0;
    background: var(--base-main-bg);
  }
}

.content-wrapper {
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  border: 1px solid var(--border-color-1);
  min-height: 600px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--current-color);
    opacity: 0.8;
  }
}

.tab-content {
  padding: 20px;
  min-height: 600px;
  background: var(--base-main-bg);
  transition: all 0.3s ease;
  
  // 确保表格组件样式继承主题
  :deep(.el-form) {
    .el-form-item__label {
      color: var(--theme-color);
      font-weight: 500;
    }
    
    .el-input__inner {
      background: var(--base-main-bg);
      border-color: var(--border-color-1);
      color: var(--theme-color);
      border-radius: 6px;
      transition: all 0.3s ease;
      
      &:focus {
        border-color: var(--current-color);
        box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
      }
      
      &:hover {
        border-color: var(--current-color);
      }
    }
    
    .el-select .el-input__inner {
      background: var(--base-main-bg);
    }
    
    .el-button {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &.el-button--primary {
        background: var(--current-color);
        border-color: var(--current-color);
        color: var(--theme-color);
        box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
        
        &:hover {
          background: var(--color-2);
          border-color: var(--color-2);
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(54, 113, 232, 0.4);
        }
      }
    }
  }
  
  :deep(.el-table) {
    background: var(--base-main-bg);
    border: 1px solid var(--border-color-1);
    border-radius: 8px;
    overflow: hidden;
    
    th {
      background: var(--base-color-9);
      color: var(--theme-color);
      border-bottom: 1px solid var(--border-color-1);
      font-weight: 600;
    }
    
    td {
      border-bottom: 1px solid var(--border-color-1);
      color: var(--theme-color);
      background: var(--base-main-bg);
      transition: all 0.3s ease;
    }
    
    tr:hover td {
      background: var(--table-row-hover-bg);
      transform: scale(1.005);
    }
    
    .el-button--text {
      color: var(--current-color);
      
      &:hover {
        color: var(--color-2);
      }
    }
  }
  
  :deep(.el-tag) {
    border: none;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.el-tag--success {
      background: linear-gradient(135deg, #67c23a, #85ce61);
      color: white;
      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
    }
    
    &.el-tag--warning {
      background: linear-gradient(135deg, #e6a23c, #ebb563);
      color: white;
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
    }
    
    &.el-tag--danger {
      background: linear-gradient(135deg, #f56c6c, #f78989);
      color: white;
      box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
    }
    
    &.el-tag--info {
      background: linear-gradient(135deg, #909399, #a6a9ad);
      color: white;
      box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
    }
    
    &.el-tag--primary {
      background: var(--current-color);
      color: white;
      box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
    }
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  :deep(.el-pagination) {
    margin-top: 20px;
    text-align: center;
    
    .el-pager li {
      background: var(--base-main-bg);
      color: var(--theme-color);
      border: 1px solid var(--border-color-1);
      border-radius: 6px;
      margin: 0 2px;
      transition: all 0.3s ease;
      
      &.active {
        background: var(--current-color);
        border-color: var(--current-color);
        color: white;
        box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
      }
      
      &:hover {
        color: var(--current-color);
        border-color: var(--current-color);
        transform: translateY(-1px);
      }
    }
    
    .btn-prev, .btn-next {
      background: var(--base-main-bg);
      color: var(--theme-color);
      border: 1px solid var(--border-color-1);
      border-radius: 6px;
      transition: all 0.3s ease;
      
      &:hover {
        color: var(--current-color);
        border-color: var(--current-color);
        transform: translateY(-1px);
      }
    }
    
    .el-pagination__editor .el-input__inner {
      background: var(--base-main-bg);
      border-color: var(--border-color-1);
      color: var(--theme-color);
      border-radius: 6px;
    }
  }
}

/* 星空主题特殊适配 */
.theme-starry-sky .app-container {
  background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 25%, #1e3a8a 50%, #0b0d1a 75%, #1a1f3c 100%);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.9), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(30, 58, 138, 0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.7), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(30, 58, 138, 0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.8), transparent),
      radial-gradient(1px 1px at 200px 90px, rgba(30, 58, 138, 0.5), transparent);
    background-repeat: repeat;
    background-size: 250px 150px;
    pointer-events: none;
    animation: starAnimation 25s linear infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(ellipse at top, rgba(30, 58, 138, 0.1) 0%, transparent 70%),
      radial-gradient(ellipse at bottom, rgba(26, 31, 60, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.theme-starry-sky {
  .page-header {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 32px rgba(30, 58, 138, 0.2);
    
    &::before {
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.3), transparent);
    }
    
    .page-title {
      color: var(--current-color);
      text-shadow: 
        0 0 10px rgba(30, 58, 138, 0.8),
        0 0 20px rgba(30, 58, 138, 0.5),
        0 0 30px rgba(30, 58, 138, 0.3);
      
      i {
        animation: bounce 2s infinite, glow 3s ease-in-out infinite alternate;
      }
    }
    
    .page-description {
      color: var(--base-color-2);
      text-shadow: 0 0 10px rgba(30, 58, 138, 0.3);
    }
  }
  
  .nav-card {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 16px rgba(30, 58, 138, 0.2);
    
    :deep(.el-card__body) {
      background: transparent;
    }
    
    :deep(.el-tabs__header) {
      background: rgba(var(--base-color-9), 0.5);
    }
    
    :deep(.el-tabs__item) {
      color: var(--base-color-2);
      
      &.is-active {
        color: var(--theme-color);
        background: var(--current-color);
        text-shadow: 
          0 0 8px rgba(30, 58, 138, 0.8),
          0 0 16px rgba(30, 58, 138, 0.5);
        box-shadow: 
          0 4px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }
      
      &:hover {
        color: var(--current-color);
        background: rgba(var(--current-color), 0.1);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.6);
      }
    }
    
    :deep(.el-tabs__active-bar) {
      background: var(--current-color);
      box-shadow: 0 0 16px rgba(30, 58, 138, 0.8);
    }
    
    :deep(.el-tabs__content) {
      background: transparent;
    }
  }
  
  .content-wrapper {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 16px rgba(30, 58, 138, 0.2);
    
    &::before {
      background: var(--current-color);
      box-shadow: 0 0 16px rgba(30, 58, 138, 0.8);
    }
  }
  
  .tab-content {
    background: transparent;
    
    :deep(.el-form) {
      .el-form-item__label {
        color: var(--theme-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.3);
      }
      
      .el-input__inner {
        background: var(--input-bg);
        border-color: var(--input-border);
        color: var(--input-color);
        backdrop-filter: blur(10px);
        
        &:focus {
          border-color: var(--current-color);
          box-shadow: 
            0 0 16px rgba(30, 58, 138, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        &::placeholder {
          color: var(--base-color-3);
        }
      }
      
      .el-button {
        &.el-button--primary {
          background: var(--current-color);
          border: none;
          box-shadow: 
            0 4px 16px rgba(30, 58, 138, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
          
          &:hover {
            background: var(--color-2);
            box-shadow: 
              0 6px 20px rgba(30, 58, 138, 0.6),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
          }
        }
      }
    }
    
    :deep(.el-table) {
      background: var(--table-row-bg);
      border-color: var(--table-border-color);
      backdrop-filter: blur(10px);
      
      th {
        background: var(--table-header-bg);
        color: var(--table-header-color);
        border-bottom-color: var(--table-border-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.3);
      }
      
      td {
        border-bottom-color: var(--table-border-color);
        color: var(--theme-color);
        background: transparent;
      }
      
      tr:hover td {
        background: var(--table-row-hover-bg);
        box-shadow: 0 0 16px rgba(30, 58, 138, 0.2);
      }
      
      .el-button--text {
        color: var(--current-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.5);
        
        &:hover {
          color: var(--color-2);
          text-shadow: 0 0 12px rgba(30, 58, 138, 0.8);
        }
      }
    }
    
    :deep(.el-pagination) {
      .el-pager li {
        background: var(--input-bg);
        color: var(--input-color);
        border-color: var(--input-border);
        backdrop-filter: blur(10px);
        
        &.active {
          background: var(--current-color);
          border-color: var(--current-color);
          color: white;
          box-shadow: 
            0 0 16px rgba(30, 58, 138, 0.8),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        &:hover {
          color: var(--current-color);
          border-color: var(--current-color);
          box-shadow: 0 0 12px rgba(30, 58, 138, 0.5);
        }
      }
      
      .btn-prev, .btn-next {
        background: var(--input-bg);
        color: var(--input-color);
        border-color: var(--input-border);
        backdrop-filter: blur(10px);
        
        &:hover {
          color: var(--current-color);
          border-color: var(--current-color);
          box-shadow: 0 0 12px rgba(30, 58, 138, 0.5);
        }
      }
    }
  }
}

/* 动画效果 */
@keyframes starAnimation {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-6px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes glow {
  0% {
    text-shadow: 
      0 0 5px rgba(30, 58, 138, 0.5),
      0 0 10px rgba(30, 58, 138, 0.3);
  }
  100% {
    text-shadow: 
      0 0 10px rgba(30, 58, 138, 0.8),
      0 0 20px rgba(30, 58, 138, 0.5),
      0 0 30px rgba(30, 58, 138, 0.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 15px;
    margin-bottom: 15px;
    
    .page-title {
      font-size: 22px;
      
      i {
        margin-right: 8px;
      }
    }
    
    .page-description {
      font-size: 14px;
    }
  }
  
  .nav-card {
    margin-bottom: 15px;
    
    :deep(.el-card__body) {
      padding: 15px;
    }
    
    :deep(.el-tabs__item) {
      font-size: 12px;
      padding: 0 8px;
    }
  }
  
  .tab-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 5px;
  }
  
  .page-header {
    padding: 10px;
    
    .page-title {
      font-size: 18px;
      flex-direction: column;
      
      i {
        margin-right: 0;
        margin-bottom: 5px;
      }
    }
  }
  
  .nav-card {
    :deep(.el-tabs__item) {
      font-size: 10px;
      padding: 0 4px;
    }
  }
  
  .tab-content {
    padding: 10px;
  }
}
</style>
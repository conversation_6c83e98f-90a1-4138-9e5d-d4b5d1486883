<template>
  <div class="app-container inventory-detail">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-box"></i>
        库存明细管理
      </h1>
      <p class="page-description">精细化库存查询与管理，支持批次追溯、库存调整、移库操作</p>
    </div> -->

    <!-- 统计卡片 -->
    <!-- <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-goods"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.totalItems || 0 }}</div>
                <div class="stat-label">总库存项目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-collection"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.totalQuantity || 0 }}</div>
                <div class="stat-label">总库存数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-money"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">¥{{ (statistics.totalValue || 0).toLocaleString() }}</div>
                <div class="stat-label">总库存价值</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.lowStockItems || 0 }}</div>
                <div class="stat-label">低库存预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div> -->

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card>
        <div slot="header">
          <span>搜索筛选</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="toggleSearchCollapse">
            <i :class="searchCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </el-button>
        </div>
        <el-collapse-transition>
          <div v-show="!searchCollapsed">
            <el-form :model="searchForm" :inline="true" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="物料名称">
                    <el-input
                      v-model="searchForm.materialName"
                      placeholder="请输入物料名称"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="物料类型">
                    <el-select
                      v-model="searchForm.materialType"
                      placeholder="请选择物料类型"
                      clearable
                    >
                      <el-option
                        v-for="option in materialTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
                  <el-form-item label="仓库类型">
                    <el-select
                      v-model="searchForm.warehouseType"
                      placeholder="请选择仓库类型"
                      clearable
                      @change="handleWarehouseTypeChange"
                    >
                      <el-option
                        v-for="item in warehouseTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="6">
                  <el-form-item label="区域">
                    <el-select
                      v-model="searchForm.zoneCode"
                      placeholder="请选择区域"
                      clearable
                      @change="handleZoneChange"
                    >
                      <el-option
                        v-for="zone in zoneOptions"
                        :key="zone.zoneCode"
                        :label="zone.zoneName"
                        :value="zone.zoneCode"
                        :disabled="zone.status === '0' || zone.warehouseStatus === '0'"
                        :class="getZoneOptionClass(zone)"
                      >
                        <span :style="getZoneTextStyle(zone)">
                          {{ zone.zoneName }}
                          <span v-if="zone.status === '0'" class="disabled-text">(区域已停用)</span>
                          <span v-else-if="zone.warehouseStatus === '0'" class="disabled-text">(所属仓库已停用)</span>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="批次号">
                    <el-input
                      v-model="searchForm.batchNo"
                      placeholder="请输入批次号"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="库存状态">
                    <el-select
                      v-model="searchForm.status"
                      placeholder="请选择库存状态"
                      clearable
                    >
                      <el-option
                        v-for="option in stockStatusOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="采购状态">
                    <el-select
                      v-model="searchForm.needPurchase"
                      placeholder="请选择采购状态"
                      clearable
                    >
                      <el-option label="是" value="true" />
                      <el-option label="否" value="false" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="产品编码">
                    <el-input
                      v-model="searchForm.productNumber"
                      placeholder="请输入产品编码"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="系列">
                    <el-select
                      v-model="searchForm.seriesId"
                      placeholder="请选择系列"
                      clearable
                      :loading="seriesOptionsLoading"
                    >
                      <el-option
                        v-for="series in seriesOptions"
                        :key="series.id"
                        :label="series.seriesName"
                        :value="series.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="供应商">
                    <el-select
                      v-model="searchForm.supplierId"
                      placeholder="请选择供应商"
                      clearable
                      filterable
                      :loading="supplierOptionsLoading"
                    >
                      <el-option
                        v-for="supplier in supplierOptions"
                        :key="supplier.id"
                        :label="supplier.supplierName"
                        :value="supplier.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="款式">
                    <el-select
                      v-model="searchForm.styleId"
                      placeholder="请选择款式"
                      clearable
                      :loading="styleOptionsLoading"
                    >
                      <el-option
                        v-for="style in styleOptions"
                        :key="style.styleId"
                        :label="style.styleName"
                        :value="style.styleId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="板类型">
                    <el-select
                      v-model="searchForm.boardType"
                      placeholder="请选择板类型"
                      clearable
                    >
                      <el-option label="上板" value="上板" />
                      <el-option label="下板" value="下板" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleSearch">
                      搜索
                    </el-button>
                    <el-button icon="el-icon-refresh" @click="handleReset">
                      重置
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-collapse-transition>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-section">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
        新增库存
      </el-button>
      <el-button
        type="success"
        icon="el-icon-bottom"
        :disabled="selectedRows.length === 0"
        @click="handleBatchInbound"
      >
        批量入库
      </el-button>
      <el-button
        type="warning"
        icon="el-icon-top"
        :disabled="selectedRows.length === 0"
        @click="handleBatchOutbound"
      >
        批量出库
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>

      <el-button type="success" icon="el-icon-download" @click="handleExport">
        导出数据
      </el-button>
      <el-button type="info" icon="el-icon-refresh" @click="handleRefresh">
        刷新数据
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-warning"
        @click="handleLowStockAlert"
      >
        低库存预警
      </el-button>
      <el-button type="success" plain icon="el-icon-setting" @click="handleColumnConfig">
        字段配置
      </el-button>
    </div>

    <!-- 库存明细表格 -->
    <div class="table-section">
      <el-card>
        <el-table
          ref="table"
          v-loading="loading"
          :data="tableData"
          stripe
          border
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          max-height="600"
          style="overflow: visible;"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- 动态渲染表格列 -->
          <el-table-column
            v-for="column in visibleColumns"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            :width="column.width"
            :min-width="column.minWidth"
            :fixed="column.fixed"
            :sortable="column.prop === 'currentStock' || column.prop === 'inboundQuantity' || column.prop === 'outboundQuantity' || column.prop === 'stockQuantity' || column.prop === 'minStockQuantity' || column.prop === 'entryDate' || column.prop === 'updateTime' ? 'custom' : false"
            align="center"
          >
            <template slot-scope="scope">
              <!-- 物料类型字段 -->
              <el-tag v-if="column.prop === 'materialType'" :type="getMaterialTypeTag(scope.row.materialType)">
                {{ scope.row.materialType }}
              </el-tag>

              <!-- 存储区域字段 -->
              <div v-else-if="column.prop === 'zoneName'" class="location-info">
                <div class="zone-name">
                  {{ scope.row.zoneName }}
                  <el-tag
                    v-if="getZoneStatusInfo(scope.row.zoneCode).isZoneDisabled"
                    type="danger"
                    size="mini"
                    style="margin-left: 5px;"
                  >
                    区域停用
                  </el-tag>
                  <el-tag
                    v-else-if="getZoneStatusInfo(scope.row.zoneCode).isWarehouseDisabled"
                    type="warning"
                    size="mini"
                    style="margin-left: 5px;"
                  >
                    仓库停用
                  </el-tag>
                </div>
                <div
                  v-if="getZoneStatusInfo(scope.row.zoneCode).isZoneDisabled"
                  class="zone-status-tip"
                  style="color: #f56c6c; font-size: 12px; margin-top: 2px;"
                >
                  <i class="el-icon-warning"></i>
                  该区域已停用，无法进行新的库存操作
                </div>
                <div
                  v-else-if="getZoneStatusInfo(scope.row.zoneCode).isWarehouseDisabled"
                  class="zone-status-tip"
                  style="color: #e6a23c; font-size: 12px; margin-top: 2px;"
                >
                  <i class="el-icon-warning"></i>
                  所属仓库已停用，建议尽快处理
                </div>
              </div>

              <!-- 当前库存字段 -->
              <span v-else-if="column.prop === 'currentStock'" :class="getStockClass(scope.row)">
                {{ scope.row.currentStock }}
              </span>

              <!-- 最低库存字段 -->
              <span v-else-if="column.prop === 'minStockQuantity'" :class="scope.row.currentStock <= scope.row.minStockQuantity ? 'text-danger' : ''">
                {{ scope.row.minStockQuantity }}
              </span>

              <!-- 需要采购字段 -->
              <el-tag v-else-if="column.prop === 'needPurchase'" :type="scope.row.needPurchase === 'true' ? 'danger' : 'success'">
                {{ scope.row.needPurchase === 'true' ? '是' : '否' }}
              </el-tag>

              <!-- 板类型、款式、系列、供应商字段 - 显示空值处理 -->
              <span v-else-if="column.prop === 'boardType' || column.prop === 'styleName' || column.prop === 'seriesName' || column.prop === 'supplierName'">
                <span v-if="scope.row[column.prop]">{{ scope.row[column.prop] }}</span>
                <span v-else class="text-muted">-</span>
              </span>

              <!-- 入库时间字段 -->
              <span v-else-if="column.prop === 'entryDate'">
                {{ formatDate(scope.row.entryDate) }}
              </span>

              <!-- 状态字段 -->
              <el-tag v-else-if="column.prop === 'status'" :type="scope.row.status === '1' ? 'success' : 'danger'">
                {{ scope.row.status === '1' ? '正常' : '异常' }}
              </el-tag>

              <!-- 来源类型字段 -->
              <span v-else-if="column.prop === 'sourceType'">
                {{ sourceTypeMap[scope.row.sourceType] || scope.row.sourceType || '-' }}
              </span>

              <!-- 生产日期和到期日期字段 -->
              <span v-else-if="column.prop === 'productionDate' || column.prop === 'expiryDate'">
                {{ formatDate(scope.row[column.prop]) }}
              </span>

              <!-- 最后盘点时间字段 -->
              <span v-else-if="column.prop === 'lastCheckDate'">
                {{ formatDate(scope.row.lastCheckDate) }}
              </span>

              <!-- 默认字段显示 -->
              <span v-else>
                <!-- 数值类型字段：显示0而不是- -->
                <template v-if="isNumericField(column.prop)">
                  {{ scope.row[column.prop] || 0 }}
                </template>
                <!-- 其他字段：空值显示- -->
                <template v-else>
                  {{ scope.row[column.prop] || '-' }}
                </template>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right" align="center">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <!-- 主要操作按钮 -->
                <el-button type="text" size="small" class="view-btn" @click="handleView(scope.row)">
                  <i class="el-icon-view"></i>
                  查看
                </el-button>
                <el-button type="text" size="small" class="edit-btn" @click="handleEdit(scope.row)">
                  <i class="el-icon-edit"></i>
                  编辑
                </el-button>

                <!-- 更多操作下拉菜单 -->
                <el-dropdown size="small" @command="handleCommand($event, scope.row)">
                  <el-button type="text" size="small" class="more-btn">
                    更多
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="inbound">
                      <i class="el-icon-bottom"></i>
                      入库
                    </el-dropdown-item>
                    <el-dropdown-item command="outbound">
                      <i class="el-icon-top"></i>
                      出库
                    </el-dropdown-item>
                    <el-dropdown-item command="trace" :divided="true">
                      <i class="el-icon-time"></i>
                      追溯
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <i class="el-icon-delete"></i>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 库存调整弹窗 -->
    <el-dialog
      title="库存调整"
      :visible.sync="adjustDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :z-index="3000"
      append-to-body
    >
      <el-form
        ref="adjustForm"
        :model="adjustFormData"
        :rules="adjustFormRules"
        label-width="120px"
      >
        <el-form-item label="物料名称">
          <el-input v-model="adjustFormData.materialName" disabled />
        </el-form-item>
        <el-form-item label="当前库存">
          <el-input v-model="adjustFormData.currentStock" disabled />
        </el-form-item>
        <el-form-item label="调整类型" prop="adjustType">
          <el-radio-group v-model="adjustFormData.adjustType">
            <el-radio label="increase">增加</el-radio>
            <el-radio label="decrease">减少</el-radio>
            <el-radio label="set">设置</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量" prop="adjustQuantity">
          <el-input-number
            v-model="adjustFormData.adjustQuantity"
            :min="adjustFormData.adjustType === 'decrease' ? -adjustFormData.currentQuantity : 0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="adjustReason">
          <el-select
            v-model="adjustFormData.adjustReason"
            placeholder="请选择调整原因"
            style="width: 100%"
          >
            <el-option label="盘点调整" value="inventory" />
            <el-option label="损耗调整" value="loss" />
            <el-option label="报废调整" value="scrap" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="adjustFormData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入调整备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adjustDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAdjustSubmit" :loading="adjustLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 移库弹窗已移除 -->

         <!-- 批量移库弹窗已移除 -->

    <!-- 查看详情对话框 -->
    <el-dialog
      title="库存明细详情"
      :visible.sync="viewDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      class="detail-dialog"
      :z-index="3000"
      append-to-body
    >
      <div class="detail-content">
        <!-- 基础信息 -->
        <div class="section-title material-section-title">
          <i class="el-icon-info"></i>
          <span>基础信息</span>
        </div>
        <el-row :gutter="20" class="material-info">
          <el-col :span="8">
            <div class="detail-item"><label>明细ID</label><span>{{ viewData.detailId }}</span></div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item"><label>物料ID</label><span>{{ viewData.materialId }}</span></div>
          </el-col>
          <el-col :span="8">
             <div class="detail-item"><label>批次号</label><span>{{ viewData.batchNo || '-' }}</span></div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="material-info">
          <el-col :span="16">
            <div class="detail-item"><label>物料名称</label><span>{{ viewData.materialName }}</span></div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>物料类型</label>
              <el-tag :type="getMaterialTypeTag(viewData.materialType)" size="small">{{ viewData.materialType }}</el-tag>
            </div>
          </el-col>
        </el-row>

        <!-- 存储信息 -->
        <div class="section-title warehouse-section-title">
          <i class="el-icon-place"></i>
          <span>存储信息</span>
        </div>
        <el-row :gutter="20" class="warehouse-info">
          <el-col :span="12">
            <div class="detail-item"><label>仓库名称</label><span>{{ viewData.warehouseName }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item"><label>存储区域</label><span>{{ viewData.zoneName }}</span></div>
          </el-col>
        </el-row>

        <!-- 库存信息 -->
        <div class="section-title stock-section-title">
          <i class="el-icon-goods"></i>
          <span>库存信息</span>
        </div>
        <el-row :gutter="20" class="stock-info">
          <el-col :span="8">
            <div class="detail-item"><label>当前库存</label><span class="stock-value">{{ viewData.currentStock }} {{ viewData.unit }}</span></div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item"><label>最低库存</label><span>{{ viewData.minStockQuantity }} {{ viewData.unit }}</span></div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>需要采购</label>
              <el-tag :type="viewData.needPurchase === 'true' ? 'warning' : 'success'" size="small">
                {{ viewData.needPurchase === 'true' ? '是' : '否' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="stock-info">
            <el-col :span="8">
              <div class="detail-item"><label>入库数量</label><span>{{ viewData.inboundQuantity  }} {{ viewData.unit }} </span></div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item"><label>出库数量</label><span>{{ viewData.outboundQuantity }} {{ viewData.unit }}</span></div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item"><label>结存数量</label><span>{{ viewData.stockQuantity }} {{ viewData.unit }}</span></div>
            </el-col>
        </el-row>

        <!-- 时间信息 -->
        <div class="section-title time-section-title">
          <i class="el-icon-time"></i>
          <span>时间信息</span>
        </div>
        <el-row :gutter="20" class="time-info">
          <el-col :span="12">
            <div class="detail-item"><label>入库时间</label><span>{{ formatDate(viewData.entryDate) || '-' }}</span></div>
          </el-col>
        </el-row>

        <!-- 来源信息 -->
        <div class="section-title source-section-title">
          <i class="el-icon-document"></i>
          <span>来源信息</span>
        </div>
        <el-row :gutter="20" class="source-info">
            <el-col :span="12">
              <div class="detail-item"><label>来源类型</label><span>{{ getSourceTypeText(viewData.sourceType) }}</span></div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item"><label>来源单号</label><span>{{ viewData.sourceNo || '-' }}</span></div>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="source-info">
            <el-col :span="24">
              <div class="detail-item"><label>备注</label><span>{{ viewData.remark || '-' }}</span></div>
            </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false" size="medium">
          <i class="el-icon-close"></i>
          关闭
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增/编辑库存明细弹窗 -->
    <el-dialog
      :title="formDialogTitle"
      :visible.sync="formDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :z-index="3000"
      :modal-append-to-body="false"
      :lock-scroll="false"
      @opened="handleDialogOpened"
      @open="handleDialogOpen"
    >
      <el-form
        ref="detailForm"
        :model="detailFormData"
        :rules="detailFormRules"
        label-width="120px"
        class="centered-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="warehouseType">
              <template slot="label">
                仓库类型
                <el-tooltip v-if="isEditMode" content="编辑模式下仓库类型根据区域信息自动填充" placement="top">
                  <i class="el-icon-info" style="color: #409eff; margin-left: 4px;"></i>
                </el-tooltip>
              </template>
              <el-select
                v-model="detailFormData.warehouseType"
                placeholder="请选择仓库类型"
                style="width: 100%"
                :disabled="isEditMode"
                @change="handleWarehouseTypeChange"
              >
                <el-option label="原料仓库" value="1" />
                <el-option label="半成品仓库" value="2" />
                <el-option label="成品仓库" value="3" />
                <el-option label="零件仓库" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域" prop="zoneCode">
              <el-select
                v-model="detailFormData.zoneCode"
                placeholder="请选择区域"
                style="width: 100%"
                @change="handleZoneChange"
              >
                <el-option
                  v-for="zone in zoneOptions"
                  :key="zone.zoneCode"
                  :label="zone.zoneName"
                  :value="zone.zoneCode"
                  :disabled="zone.status === '0' || zone.warehouseStatus === '0'"
                  :class="getZoneOptionClass(zone)"
                >
                  <span :style="getZoneTextStyle(zone)">
                    {{ zone.zoneName }}
                    <span v-if="zone.status === '0'" class="disabled-text">(区域已停用)</span>
                    <span v-else-if="zone.warehouseStatus === '0'" class="disabled-text">(所属仓库已停用)</span>
                    <span v-else-if="!isEditMode && detailFormData.warehouseType && zone.warehouseType !== detailFormData.warehouseType" class="mismatch-text">
                      ({{ getWarehouseTypeLabel(zone.warehouseType) }})
                    </span>
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料类型" prop="materialType">
              <el-select
                v-model="detailFormData.materialType"
                placeholder="请选择物料类型"
                style="width: 100%"
                @change="handleMaterialTypeChange"
              >
                <el-option
                  v-for="type in materialTypeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料ID" prop="materialId">
              <el-input
                v-model="detailFormData.materialId"
                style="width: 100%"
                placeholder="不填写将自动生成"
                @input="handleMaterialIdInput"
              />
              <div class="material-id-hint">
                <span class="mode-hint">
                  <i class="el-icon-info"></i>
                  不填写将自动生成，填写则使用自定义ID
                </span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="detailFormData.materialName"
                placeholder="请输入物料名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号">
              <el-input
                v-model="detailFormData.batchNo"
                placeholder="请输入批次号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input
                v-model="detailFormData.unit"
                placeholder="请输入计量单位，如：件、个、公斤"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 新增弹窗显示创建人字段 -->
            <el-form-item v-if="!detailFormData.detailId" label="创建人">
              <el-input
                v-model="detailFormData.createBy"
                placeholder="创建人"
                disabled
                style="width: 100%"
              />
            </el-form-item>
            <!-- 编辑弹窗显示更新人字段 -->
            <el-form-item v-else label="更新人">
              <el-input
                v-model="detailFormData.updateBy"
                placeholder="更新人"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="入库数量" prop="inboundQuantity">
              <el-input-number
                v-model="detailFormData.inboundQuantity"
                :min="0"
                style="width: 100%"
                placeholder="请输入入库数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库数量" prop="outboundQuantity">
              <el-input-number
                v-model="detailFormData.outboundQuantity"
                :min="0"
                style="width: 100%"
                placeholder="请输入出库数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低库存">
              <el-input-number
                v-model="detailFormData.minStockQuantity"
                :min="0"
                style="width: 100%"
                placeholder="请输入最低库存"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库时间" prop="entryDate">
              <el-date-picker
                v-model="detailFormData.entryDate"
                type="datetime"
                placeholder="选择入库时间"
                style="width: 100%"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="来源类型">
              <el-select
                v-model="detailFormData.sourceType"
                placeholder="请选择来源类型"
                style="width: 100%"
              >
                <el-option
                  v-for="type in sourceTypeOptions"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="来源单号">
              <el-input
                v-model="detailFormData.sourceNo"
                placeholder="请输入来源单号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 新增字段行 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品编码">
              <el-input
                v-model="detailFormData.productNumber"
                placeholder="请输入产品编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="板类型">
              <el-select
                v-model="detailFormData.boardType"
                placeholder="请选择板类型"
                style="width: 100%"
                clearable
              >
                <el-option label="上板" value="上板" />
                <el-option label="下板" value="下板" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="款式">
              <el-select
                v-model="detailFormData.styleId"
                placeholder="请选择款式"
                style="width: 100%"
                clearable
                :loading="styleOptionsLoading"
              >
                <el-option
                  v-for="style in styleOptions"
                  :key="style.styleId"
                  :label="style.styleName"
                  :value="style.styleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系列">
              <el-select
                v-model="detailFormData.seriesId"
                placeholder="请选择系列"
                style="width: 100%"
                clearable
                :loading="seriesOptionsLoading"
              >
                <el-option
                  v-for="series in seriesOptions"
                  :key="series.id"
                  :label="series.seriesName"
                  :value="series.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商">
              <el-select
                v-model="detailFormData.supplierId"
                placeholder="请选择供应商"
                style="width: 100%"
                clearable
                filterable
                :loading="supplierOptionsLoading"
                @change="handleSupplierChange"
              >
                <el-option
                  v-for="supplier in supplierOptions"
                  :key="supplier.id"
                  :label="supplier.supplierName"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input
            v-model="detailFormData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="handleFormCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleFormSubmit" :loading="formLoading" :disabled="formLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 低库存预警详细列表对话框 -->
    <el-dialog
      title="低库存预警详情"
      :visible.sync="lowStockAlertVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="alert-summary">
        <el-alert
          :title="`发现 ${lowStockAlertData.length} 项低库存预警，请及时处理`"
          type="warning"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        />
      </div>

      <el-table
        :data="lowStockAlertData"
        stripe
        border
        height="400"
        style="width: 100%"
      >
        <el-table-column prop="materialName" label="物料名称" width="150" />
        <el-table-column prop="materialType" label="物料类型" width="120" />
        <el-table-column prop="batchNo" label="批次号" width="120" />
        <el-table-column prop="zoneName" label="区域" width="120" />
        <el-table-column prop="currentStock" label="当前库存" width="100" align="right">
          <template slot-scope="scope">
            <span class="low-stock">{{ scope.row.currentStock }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="minStockQuantity" label="最低库存" width="100" align="right" />
        <el-table-column label="缺口数量" width="100" align="right">
          <template slot-scope="scope">
            <span class="low-stock">
              {{ (scope.row.minStockQuantity || 0) - (scope.row.currentStock || 0) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="entryDate" label="入库时间" width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.entryDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="needPurchase" label="需要采购" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.needPurchase === 'true' ? 'danger' : 'success'" size="small">
              {{ scope.row.needPurchase === 'true' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleView(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="lowStockAlertVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleExportLowStock">导出预警清单</el-button>
      </div>
    </el-dialog>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="columnConfigVisible" width="600px" append-to-body class="column-config-dialog">
      <div style="max-height: 400px; overflow-y: auto;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>可选字段</h4>
            <el-checkbox-group v-model="selectedColumns" @change="handleColumnChange">
              <div v-for="column in allColumns" :key="column.prop" style="margin-bottom: 8px;">
                <el-checkbox :label="column.prop" :disabled="column.required">
                  {{ column.label }}
                  <span v-if="column.required" style="color: var(--current-color, #f56c6c);">(必需)</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-col>
          <el-col :span="12">
            <h4>字段预览</h4>
            <div class="preview-container">
              <div v-for="column in previewColumns" :key="column.prop" class="preview-item">
                <span>{{ column.label }}</span>
                <span style="color: var(--base-color-2, #909399); font-size: 12px;">{{ column.width || 'auto' }}</span>
              </div>
              <div v-if="previewColumns.length === 0" class="preview-empty">
                请选择要显示的字段
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetColumnConfig">重置默认</el-button>
        <el-button type="primary" @click="saveColumnConfig">确 定</el-button>
        <el-button @click="columnConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInventoryDetail,
  getInventoryDetail,
  addInventoryDetail,
  updateInventoryDetail,
  delInventoryDetail,
  exportInventoryDetail,
  adjustInventoryStock,
  getInventoryStatistics,
  getLowStockAlert,
  getInventoryDetailByZoneCode,
  batchUpdateStatus,
  getInventoryTrace,
  validateMaterialZoneCompatibility,
  checkInventoryDetailUniqueByCode,
  checkMaterialIdUnique,
  checkMaterialIdUniqueInContext,
  generateNextMaterialId,
  getMaterialZoneDistribution
} from '@/api/inventory/inventoryDetail'
import {
  inboundInventory,
  outboundInventory,
  batchInboundInventory,
  batchOutboundInventory
} from '@/api/inventory/operation'
import { listZone } from '@/api/inventory/zone'
import { validateStorageLocation } from '@/api/inventory/warehouse'
import { listSeriesProducts } from '@/api/basicData/seriesProducts'
import { listFunctionalStyle } from '@/api/basicData/functionalStyle'
import { getSupplierList } from '@/api/jenasi/supplier'

// 注意：数据同步功能已移至后端实现
// 前端调用库存明细接口后，后端会自动同步到对应的仓储管理表

export default {
  name: 'InventoryDetail',
  components: {
    // ... existing code ...
  },
  props: {
    warehouseId: {
      type: [String, Number],
      default: null
    },
    warehouseName: {
      type: String,
      default: ''
    },
    zoneId: {
      type: [String, Number],
      default: null
    },
    zoneName: {
      type: String,
      default: ''
    },
    // 是否为层级导航模式
    hierarchicalMode: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    zoneId: {
      handler(newVal) {
        // 当 zoneId prop 变化时，更新查询参数并获取列表
        if (this.searchForm) {
          this.searchForm.zoneCode = newVal || '';
        }
        this.fetchData();
      },
      immediate: true
    },
    // 监听路由变化，重新初始化数据
    '$route'(to, from) {
      // 只有当路由查询参数发生变化时才重新初始化
      if (JSON.stringify(to.query) !== JSON.stringify(from.query)) {
        this.initPageData();
      }
    }
  },
  data() {
    return {
      loading: false,
      searchCollapsed: false,
      // 当前登录用户信息
      currentUser: {
        userId: null,
        userName: ''
      },
      searchForm: {
        materialName: '',
        materialType: '',
        warehouseType: '',
        zoneCode: '',
        batchNo: '',
        status: '',
        needPurchase: '',
        productNumber: '',
        seriesId: '',
        supplierId: '', // 改为供应商ID
        supplierName: '', // 保留供应商名称用于其他地方
        boardType: '',
        styleId: ''
      },
      // 唯一性校验防抖定时器
      uniquenessValidationTimer: null,
      // 最后一次唯一性校验参数
      lastUniquenessParams: null,
      warehouseTypeOptions: [
        { label: '原料仓库', value: '1' },
        { label: '半成品仓库', value: '2' },
        { label: '成品仓库', value: '3' },
        { label: '零件仓库', value: '4' }
      ],
      materialTypeOptions: [
        { label: '原料', value: '原料' },
        { label: '零部件', value: '零部件' },
        { label: '成品', value: '成品' },
        { label: '一级半成品', value: '一级半成品' },
        { label: '二级半成品', value: '二级半成品' }
      ],
      // 物料ID前缀配置 - 已取消校验逻辑，保留用于备用生成方法
      materialIdPrefixConfig: {
        '原料': 'RM',           // Raw Material - RM000001格式
        '零部件': 'CP',         // Component Part - CP000001格式
        '成品': 'PD',           // Product - PD000001格式
        '一级半成品': 'SF1',     // Semi-finished Product Level 1 - SF1000001格式
        '二级半成品': 'SF2'      // Semi-finished Product Level 2 - SF2000001格式
      },
      stockStatusOptions: [
        { label: '正常', value: '1' },
        { label: '异常', value: '0' },
        { label: '过期', value: '2' },
        { label: '待处理', value: '3' }
      ],
      sourceTypeOptions: [
        { label: '采购', value: 'purchase' },
        { label: '生产', value: 'production' },
        { label: '退货', value: 'return' },
        { label: '调拨', value: 'transfer' },
        { label: '初始入库', value: 'warehouse' }
      ],
      sourceTypeMap: {
        'purchase': '采购',
        'production': '生产',
        'return': '退货',
        'transfer': '调拨',
        'warehouse': '初始入库'
      },
      zoneOptions: [],
      seriesOptions: [], // 系列选项
      styleOptions: [], // 款式选项
      supplierOptions: [], // 供应商选项
      seriesOptionsLoading: false, // 系列选项加载状态
      styleOptionsLoading: false, // 款式选项加载状态
      supplierOptionsLoading: false, // 供应商选项加载状态

      tableData: [],
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      // 排序参数
      sortInfo: {
        sortField: '',
        sortOrder: ''
      },
      statistics: {
        totalItems: 0,
        totalStock: 0,
        totalValue: 0,
        lowStockItems: 0
      },
      adjustDialogVisible: false,
      adjustLoading: false,
      adjustFormData: {
        detailId: '',
        materialName: '',
        currentStock: 0,
        adjustType: 'increase',
        adjustQuantity: 0,
        adjustReason: '',
        remark: ''
      },
      adjustFormRules: {
        adjustType: [
          { required: true, message: '请选择调整类型', trigger: 'change' }
        ],
        adjustQuantity: [
          { required: true, message: '请输入调整数量', trigger: 'blur' }
        ],
        adjustReason: [
          { required: true, message: '请选择调整原因', trigger: 'change' }
        ]
      },
      moveDialogVisible: false,
      moveLoading: false,
      moveFormData: {
        detailId: '',
        materialName: '',
        currentLocation: '',
        availableStock: 0,
        targetZoneId: '',
        moveQuantity: 0,
        moveReason: '',
        currentZoneId: null
      },
      moveFormRules: {
        targetZoneId: [
          { required: true, message: '请选择目标区域', trigger: 'change' }
        ],

        moveQuantity: [
          { required: true, message: '请输入移库数量', trigger: 'blur' }
        ],
        moveReason: [
          { required: true, message: '请输入移库原因', trigger: 'blur' }
        ]
      },

      viewDialogVisible: false,
      viewData: {},
      formDialogVisible: false,
      formLoading: false,
      formDialogTitle: '',
      detailFormData: {
        detailId: null,
        zoneCode: '',
        materialId: '',
        materialName: '',
        materialType: '',
        batchNo: '',
        unit: '',
        currentStock: 0,
        inboundQuantity: 0,
        outboundQuantity: 0,
        stockQuantity: 0, // 新增时，结存数量为0
        minStockQuantity: 0,
        entryDate: '',
        sourceType: '',
        sourceNo: '',
        remark: '',
        createBy: '', // 创建人
        updateBy: '',  // 更新人
        // 新增字段
        boardType: '', // 板类型
        styleId: '', // 款式ID
        seriesId: '', // 系列ID
        supplierId: '', // 供应商ID
        supplierName: '', // 供应商名称
        productNumber: '' // 产品编码
      },
      detailFormRules: {
        warehouseType: [
          { required: true, message: '请选择仓库类型', trigger: 'change' }
        ],
        zoneCode: [
          { required: true, message: '请选择区域', trigger: 'change' },
          { validator: this.validateZoneStatusAndUniqueness, trigger: 'change' }
        ],
        materialId: [
          // 已取消物料ID格式校验
        ],
        materialName: [
          { required: true, message: '请输入物料名称', trigger: 'blur' },
          { validator: this.validateUniqueness, trigger: 'blur' }
        ],
        materialType: [
          { required: true, message: '请选择物料类型', trigger: 'change' },
          { validator: this.validateUniqueness, trigger: 'change' }
        ],
        batchNo: [
          { validator: this.validateUniqueness, trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        inboundQuantity: [
          { required: true, message: '请输入入库数量', trigger: 'blur' }
        ],
        outboundQuantity: [
          { required: true, message: '请输入出库数量', trigger: 'blur' }
        ],
        minStockQuantity: [
          { required: true, message: '请输入最低库存', trigger: 'blur' }
        ],
        entryDate: [
          { required: true, message: '请选择入库时间', trigger: 'change' }
        ],
        sourceType: [
          { required: true, message: '请选择来源类型', trigger: 'change' }
        ],
        sourceNo: [
          { required: true, message: '请输入来源单号', trigger: 'blur' }
        ],
        // 新增字段验证规则
        productNumber: [
          { required: false, message: '请输入产品编码', trigger: 'blur' },
          { min: 2, max: 50, message: '产品编码长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        boardType: [
          { required: false, message: '请选择板类型', trigger: 'change' }
        ],
        styleId: [
          { required: false, message: '请选择款式', trigger: 'change' }
        ],
        seriesId: [
          { required: false, message: '请选择系列', trigger: 'change' }
        ],
        supplierId: [
          { required: false, message: '请选择供应商', trigger: 'change' }
        ]
      },
      // 低库存预警对话框
      lowStockAlertVisible: false,
      lowStockAlertData: [],

      // 物料ID前缀配置 - 基于数据库现有格式
      materialIdPrefixConfig: {
        '原料': 'RM',           // Raw Material - RM000001格式
        '零部件': 'CP',         // Component Part - CP000001格式
        '成品': 'PD',           // Product - PD000001格式
        '一级半成品': 'SF1',     // Semi-finished Product Level 1 - SF1000001格式
        '二级半成品': 'SF2'      // Semi-finished Product Level 2 - SF2000001格式
      },

      // ============= 字段配置相关数据 =============
      // 字段配置对话框显示状态
      columnConfigVisible: false,
      // 当前选中的字段列表
      selectedColumns: [
        'productNumber',
        'materialType',
        'batchNo',
        'currentStock',
        'inboundQuantity',
        'outboundQuantity',
        'stockQuantity',
        'minStockQuantity',
        'needPurchase',
        'unit',
        'boardType',
        'styleName',
        'seriesName',
        'supplierName',
        'entryDate',
        'status',
        'updateTime'
      ],
      // 所有可选字段定义
      allColumns: [
        // 必需字段（不可取消）
        { prop: 'materialId', label: '物料ID', required: true, width: 100, fixed: 'left' },
        { prop: 'materialName', label: '物料名称', required: true, minWidth: 150, fixed: 'left' },
        { prop: 'zoneName', label: '存储区域', required: true, width: 200 },

        // 可选字段
        // { prop: 'productNumber', label: '产品编码', required: false, width: 120 },
        { prop: 'materialType', label: '物料类型', required: false, width: 100 },
        { prop: 'batchNo', label: '批次号', required: false, width: 120 },
        { prop: 'currentStock', label: '当前库存', required: false, width: 100 },
        { prop: 'inboundQuantity', label: '入库数量', required: false, width: 100 },
        { prop: 'outboundQuantity', label: '出库数量', required: false, width: 100 },
        { prop: 'stockQuantity', label: '结存数量', required: false, width: 100 },
        { prop: 'minStockQuantity', label: '最低库存', required: false, width: 100 },
        { prop: 'needPurchase', label: '需要采购', required: false, width: 100 },
        { prop: 'unit', label: '单位', required: false, width: 80 },
        { prop: 'boardType', label: '板类型', required: false, width: 100 },
        { prop: 'styleName', label: '款式', required: false, width: 120 },
        { prop: 'seriesName', label: '系列', required: false, width: 120 },
        { prop: 'supplierName', label: '供应商', required: false, width: 120 },
        // { prop: 'productionDate', label: '生产日期', required: false, width: 120 },
        // { prop: 'expiryDate', label: '到期日期', required: false, width: 120 },
        { prop: 'sourceType', label: '来源类型', required: false, width: 100 },
        { prop: 'sourceNo', label: '来源单号', required: false, width: 120 },
        // { prop: 'qrCode', label: '二维码', required: false, width: 120 },
        // { prop: 'entryDate', label: '入库时间', required: false, width: 120 },
        // { prop: 'lastCheckDate', label: '最后盘点时间', required: false, width: 160 },
        { prop: 'status', label: '状态', required: false, width: 80 },
        { prop: 'createBy', label: '创建人', required: false, width: 100 },
        { prop: 'createTime', label: '创建时间', required: false, width: 160 },
        { prop: 'updateBy', label: '更新人', required: false, width: 100 },
        { prop: 'updateTime', label: '更新时间', required: false, width: 160 },
        { prop: 'remark', label: '备注', required: false, minWidth: 150 }
      ],
      lowStockAlertData: []
    };
  },
  computed: {
    // 过滤后的可用区域选项（排除当前区域）
    availableZoneOptions() {
      if (!this.moveFormData.currentZoneId) {
        return this.zoneOptions;
      }
      return this.zoneOptions.filter(zone => zone.zoneId !== this.moveFormData.currentZoneId);
    },

    // 判断是否为编辑模式
    isEditMode() {
      return !!(this.detailFormData && this.detailFormData.detailId);
    },

    // ============= 字段配置相关计算属性 =============

    // 可见的列配置
    visibleColumns() {
      return this.allColumns.filter(column =>
        column.required || this.selectedColumns.includes(column.prop)
      );
    },

    // 预览的列配置
    previewColumns() {
      return this.visibleColumns;
    }
  },
  mounted() {
    this.getCurrentUserInfo();
    this.initColumnConfig(); // 初始化字段配置
    this.initPageData();
    this.fetchSeriesOptions(); // 获取系列选项
    this.fetchStyleOptions(); // 获取款式选项
    this.fetchSupplierOptions(); // 获取供应商选项

    // 添加全局点击监听器来确保按钮响应（临时解决方案）
    this.setupGlobalClickHandler();
  },

  beforeDestroy() {
    // 清理全局监听器
    if (this.globalClickHandler) {
      document.removeEventListener('click', this.globalClickHandler);
    }

    // 清理唯一性校验防抖定时器
    if (this.uniquenessValidationTimer) {
      clearTimeout(this.uniquenessValidationTimer);
      this.uniquenessValidationTimer = null;
    }
  },
  methods: {
    // 获取当前登录用户信息
    // 设置全局点击处理器（解决双击问题的临时方案）
    setupGlobalClickHandler() {
      this.globalClickHandler = (event) => {
        // 检查是否点击的是弹窗内的按钮
        const target = event.target;
        const isDialogButton = target.closest('.dialog-footer .el-button');

        if (isDialogButton && this.formDialogVisible) {
          console.log('全局点击处理器检测到弹窗按钮点击:', target);

          // 检查按钮文本来确定应该调用哪个方法
          const buttonText = target.textContent || '';

          if (buttonText.includes('确定') || buttonText.includes('提交')) {
            console.log('全局处理器触发表单提交');
            event.stopPropagation();
            event.preventDefault();
            this.handleFormSubmit();
          } else if (buttonText.includes('取消')) {
            console.log('全局处理器触发表单取消');
            event.stopPropagation();
            event.preventDefault();
            this.handleFormCancel();
          }
        }
      };

      // 添加捕获阶段的监听器，确保能够拦截到所有点击
      document.addEventListener('click', this.globalClickHandler, true);
    },

    getCurrentUserInfo() {
      try {
        // 从store中获取当前登录用户信息
        const userId = this.$store.getters.userId;
        const userName = this.$store.getters.name;

        if (userId && userName) {
          this.currentUser = {
            userId: userId,
            userName: userName
          };
          console.log('当前登录用户:', this.currentUser);
        } else {
          console.warn('无法获取当前用户信息');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    },

    // 根据区域编码获取仓库类型
    getWarehouseTypeByZoneCode(zoneCode) {
      if (!zoneCode || !this.zoneOptions || this.zoneOptions.length === 0) {
        return null;
      }

      const zone = this.zoneOptions.find(z => z.zoneCode === zoneCode);
      if (zone && zone.warehouseType) {
        console.log('根据区域编码获取仓库类型:', {
          zoneCode: zoneCode,
          warehouseType: zone.warehouseType,
          zoneName: zone.zoneName
        });
        return zone.warehouseType;
      }

      console.warn('未找到区域对应的仓库类型:', zoneCode);
      return null;
    },

    // 获取仓库类型标签
    getWarehouseTypeLabel(warehouseType) {
      const typeMap = {
        '1': '原料仓库',
        '2': '半成品仓库',
        '3': '成品仓库',
        '4': '零件仓库'
      };
      return typeMap[warehouseType] || '未知类型';
    },

    /**
     * 物料与区域兼容性提示 - 轻量级前端检查
     *
     * 目的：给用户即时的友好提示，避免填完表单后才发现错误
     * 原则：
     * - 只做基本的类型匹配提示，不替代后端校验
     * - 不阻止用户操作，只是温馨提示
     * - 基于已知的简单规则，不调用后端接口
     */
    checkMaterialZoneCompatibilityHint(materialType, warehouseType, zoneName) {
      // 基本的物料类型与仓库类型匹配规则
      const compatibilityRules = {
        '原料': ['1'], // 原料 → 原料仓库
        '零部件': ['4'], // 零部件 → 零件仓库
        '成品': ['3'], // 成品 → 成品仓库
        '一级半成品': ['2'], // 一级半成品 → 半成品仓库
        '二级半成品': ['2']  // 二级半成品 → 半成品仓库
      };

      const compatibleTypes = compatibilityRules[materialType];

      if (compatibleTypes && !compatibleTypes.includes(warehouseType)) {
        // 不匹配时给出友好提示
        const warehouseTypeName = this.getWarehouseTypeLabel(warehouseType);
        const suggestedTypeName = this.getWarehouseTypeLabel(compatibleTypes[0]);

        this.$message.warning({
          message: `💡 温馨提示：${materialType}通常存放在${suggestedTypeName}，当前选择的"${zoneName}"属于${warehouseTypeName}`,
          duration: 5000,
          showClose: true
        });
      }
    },

    /**
     * 校验物料存放位置 - 简化版本
     *
     * 架构原则：
     * - 前端只负责调用后端接口并处理结果
     * - 所有业务校验逻辑在后端实现
     * - 避免前后端重复实现相同逻辑
     * - 降低维护成本，提高代码可维护性
     *
     * 调用时机：
     * - 只在表单提交前调用一次（而不是实时校验）
     * - 避免了重复校验问题
     * - 职责更加清晰
     */
    async validateMaterialStorageLocation(materialId, materialType, warehouseType, zoneCode) {
      if (!materialId) {
        return { valid: true, message: '物料ID为空，跳过校验' };
      }

      try {
        const validationData = {
          materialId,
          materialType,
          warehouseType,
          zoneCode,
          validationMode: 'strict'
        };

        console.log('调用后端校验物料存放位置:', validationData);
        const response = await validateStorageLocation(validationData);

        // 成功响应：直接返回后端的校验结果
        if (response && response.code === 200) {
          console.log('校验通过:', response.data);
          return response.data;
        }

        // 校验失败：返回后端的错误信息
        if (response && response.code === 500 && response.data) {
          console.log('校验失败:', response.data);
          return {
            valid: false,
            message: response.data.message || response.msg || '校验失败',
            errorCode: response.data.errorCode || 'VALIDATION_FAILED',
            suggestedLocation: response.data.suggestedLocation
          };
        }

        // 其他响应错误
        return {
          valid: false,
          message: response?.msg || '校验接口调用失败'
        };

      } catch (error) {
        console.error('校验接口调用异常:', error);

        // 简化错误处理：检查是否是业务校验错误
        if (error.message && error.message.includes('不能存放到')) {
          return {
            valid: false,
            message: error.message
          };
        }

        // 其他异常
        return {
          valid: false,
          message: '校验服务暂时不可用，请稍后重试'
        };
      }
    },

    // 初始化页面数据
    initPageData() {
      // 检查路由参数 - 修复：正确处理zoneCode和zoneId参数
      const { zoneId, zoneCode, zoneName, warehouseId, warehouseName } = this.$route.query;

      // 优先使用zoneCode，如果没有则使用zoneId（向后兼容）
      if (zoneCode) {
        this.searchForm.zoneCode = zoneCode;
      } else if (zoneId) {
        this.searchForm.zoneCode = zoneId;
      }

      // 如果有传递区域名称，可以在页面标题中显示
      if (zoneName) {
        console.log('当前查看区域:', zoneName);
      }

      // 如果有传递仓库信息，可以用于面包屑导航
      if (warehouseId && warehouseName) {
        console.log('当前仓库:', warehouseName);
      }

      this.fetchData();
      this.fetchStatistics();
      this.fetchZoneOptions();
    },

    // 获取库存明细数据
    async fetchData() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          materialName: this.searchForm.materialName,
          materialType: this.searchForm.materialType,
          zoneCode: this.searchForm.zoneCode,
          batchNo: this.searchForm.batchNo,
          status: this.searchForm.status,
          needPurchase: this.searchForm.needPurchase,
          productNumber: this.searchForm.productNumber,
          seriesId: this.searchForm.seriesId,
          supplierId: this.searchForm.supplierId, // 使用供应商ID
          supplierName: this.searchForm.supplierName,
          boardType: this.searchForm.boardType,
          styleId: this.searchForm.styleId,
          sortField: this.sortInfo.sortField,
          sortOrder: this.sortInfo.sortOrder
        };

        const response = await listInventoryDetail(params);
        if (response && response.code === 200) {
          this.tableData = response.rows || response.data || [];
          this.pagination.total = response.total || (response.data ? response.data.length : 0);
        } else {
          this.$message.error(response.message || '获取库存明细数据失败');
          this.tableData = [];
          this.pagination.total = 0;
        }
      } catch (error) {
        console.error('获取库存明细数据失败:', error);
        this.$message.error('获取库存明细数据失败');
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 获取统计数据
    async fetchStatistics() {
      try {
        const response = await getInventoryStatistics(this.searchForm);
        if (response && response.code === 200) {
          this.statistics = response.data || {
            totalItems: 0,
            totalStock: 0,
            totalValue: 0,
            lowStockItems: 0
          };
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
        this.statistics = {
          totalItems: 0,
          totalStock: 0,
          totalValue: 0,
          lowStockItems: 0
        };
      }
    },

    // 获取区域选项
    async fetchZoneOptions(params = {}) {
      try {
        console.log('获取区域选项，参数:', params);
        const response = await listZone(params);

        // 若依框架的响应结构通常是 { code, msg, rows, total }
        if (response && response.code === 200) {
          let zones = response.rows || [];

          // 如果指定了仓库类型，过滤区域
          if (params.warehouseType) {
            zones = zones.filter(zone => zone.warehouseType === params.warehouseType);
            console.log('根据仓库类型过滤后的区域:', {
              warehouseType: params.warehouseType,
              filteredCount: zones.length,
              zones: zones
            });
          }

          this.zoneOptions = zones;
          console.log('设置区域选项:', this.zoneOptions);
        } else {
          // 兼容可能存在的 data 包装
          const data = response.data || {};
          let zones = data.rows || data.records || [];

          // 如果指定了仓库类型，过滤区域
          if (params.warehouseType) {
            zones = zones.filter(zone => zone.warehouseType === params.warehouseType);
          }

          this.zoneOptions = zones;
          console.warn('获取区域选项响应异常或结构不符，尝试从data中解析:', {
            response: response,
            filteredZones: zones
          });
        }
      } catch (error) {
        console.error('获取区域选项失败:', error);
        this.zoneOptions = [];
      }
    },

    // 获取系列选项
    async fetchSeriesOptions() {
      try {
        this.seriesOptionsLoading = true;
        console.log('开始获取系列选项...');

        const response = await listSeriesProducts({
          pageNum: 1,
          pageSize: 100,
          delFlag: '0' // 只获取未删除的系列
        });

        console.log('系列选项API响应:', response);

        if (response && response.rows) {
          this.seriesOptions = response.rows.map(item => ({
            id: item.id,
            seriesName: item.seriesName
          }));

          console.log('系列选项处理完成:', this.seriesOptions);

          // 如果没有获取到任何系列，提供默认选项
          if (this.seriesOptions.length === 0) {
            console.warn('未获取到任何系列数据，使用默认选项');
            this.seriesOptions = [
              { id: 1, seriesName: '标准系列' }
            ];
          }
        } else {
          throw new Error('接口返回数据格式异常');
        }
      } catch (error) {
        console.error('获取系列选项失败:', error);
        this.$message.warning('获取系列选项失败，使用默认选项');
        // 接口调用失败时，提供默认选项
        this.seriesOptions = [
          { id: 1, seriesName: '标准系列' },
          { id: 2, seriesName: '高端系列' },
          { id: 3, seriesName: '经济系列' },
          { id: 4, seriesName: '定制系列' }
        ];
      } finally {
        this.seriesOptionsLoading = false;
      }
    },

    // 获取款式选项
    async fetchStyleOptions() {
      try {
        this.styleOptionsLoading = true;
        console.log('开始获取款式选项...');

        const response = await listFunctionalStyle({
          pageNum: 1,
          pageSize: 100,
          delFlag: '0' // 只获取未删除的款式
        });

        console.log('款式选项API响应:', response);

        if (response && response.rows) {
          this.styleOptions = response.rows.map(item => ({
            styleId: item.styleId,
            styleName: item.styleName
          }));

          console.log('款式选项处理完成:', this.styleOptions);

          // 如果没有获取到任何款式，提供默认选项
          if (this.styleOptions.length === 0) {
            console.warn('未获取到任何款式数据，使用默认选项');
            this.styleOptions = [
              { styleId: 1, styleName: '标准款' }
            ];
          }
        } else {
          throw new Error('接口返回数据格式异常');
        }
      } catch (error) {
        console.error('获取款式选项失败:', error);
        this.$message.warning('获取款式选项失败，使用默认选项');
        // 接口调用失败时，提供默认选项
        this.styleOptions = [
          { styleId: 1, styleName: '蓝牙款' },
          { styleId: 2, styleName: '无线款' },
          { styleId: 3, styleName: '按键款' },
          { styleId: 9, styleName: '标准款' }
        ];
      } finally {
        this.styleOptionsLoading = false;
      }
    },

    // 获取供应商选项
    async fetchSupplierOptions() {
      try {
        this.supplierOptionsLoading = true;
        console.log('开始获取供应商选项...');

        const response = await getSupplierList({
          pageNum: 1,
          pageSize: 100,
          supplierName: '' // 获取所有供应商
        });

        console.log('供应商选项API响应:', response);

        if (response && response.data && response.data.records) {
          this.supplierOptions = response.data.records.map(item => ({
            id: item.id,
            supplierName: item.supplierName
          }));

          console.log('供应商选项处理完成:', this.supplierOptions);

          // 如果没有获取到任何供应商，提供默认选项
          if (this.supplierOptions.length === 0) {
            console.warn('未获取到任何供应商数据，使用默认选项');
            this.supplierOptions = [
              { id: 1, supplierName: '默认供应商' }
            ];
          }
        } else {
          throw new Error('接口返回数据格式异常');
        }
      } catch (error) {
        console.error('获取供应商选项失败:', error);
        this.$message.warning('获取供应商选项失败，使用默认选项');
        // 接口调用失败时，提供默认选项
        this.supplierOptions = [
          { id: 1, supplierName: '供应商A' },
          { id: 2, supplierName: '供应商B' },
          { id: 3, supplierName: '供应商C' },
          { id: 4, supplierName: '供应商D' }
        ];
      } finally {
        this.supplierOptionsLoading = false;
      }
    },

    // 处理供应商选择变化
    handleSupplierChange(supplierId) {
      if (supplierId) {
        const supplier = this.supplierOptions.find(s => s.id === supplierId);
        if (supplier) {
          this.detailFormData.supplierName = supplier.supplierName;
        }
      } else {
        this.detailFormData.supplierName = '';
      }
    },

    // 切换搜索区域折叠状态
    toggleSearchCollapse() {
      this.searchCollapsed = !this.searchCollapsed;
    },

    // 仓库类型变化
    handleWarehouseTypeChange() {
      this.searchForm.zoneCode = '';
    },

    // 区域变化
    handleZoneChange() {
      // 区域变化后重新搜索数据
      this.handleSearch();
    },

    // 目标区域变化
    handleTargetZoneChange() {
      // 目标区域变化后可以进行移库操作
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        materialName: '',
        materialType: '',
        warehouseType: '',
        zoneCode: '',
        batchNo: '',
        status: '',
        needPurchase: '',
        productNumber: '',
        seriesId: '',
        supplierId: '', // 改为供应商ID
        supplierName: '',
        boardType: '',
        styleId: ''
      };
      this.fetchZoneOptions(); // 重新加载区域选项
      this.handleSearch();
    },

    // 刷新数据
    handleRefresh() {
      this.fetchData();
      this.fetchStatistics();
      this.$message.success('数据刷新成功');
    },

    // 导出
    handleExport() {
      this.download('system/inventoryDetail/export', {
        ...this.searchForm
      }, `库存明细_${new Date().getTime()}.xlsx`);
    },

    // 低库存预警
    async handleLowStockAlert() {
      try {
        const response = await getLowStockAlert({});
        if (response && response.code === 200) {
          const alertData = response.data || [];
          if (alertData.length > 0) {
            this.lowStockAlertData = alertData;
            this.lowStockAlertVisible = true;
          } else {
            this.$message.success('当前库存正常，无预警项目');
          }
        }
      } catch (error) {
        console.error('获取低库存预警失败:', error);
        this.$message.error('获取预警信息失败');
      }
    },

    // 查看详情
    async handleView(row) {
      try {
        const response = await getInventoryDetail(row.detailId);
        if (response && response.code === 200) {
          this.viewDialogVisible = true;
          this.viewData = response.data;
        } else {
          this.$message.error('获取库存明细详情失败');
        }
      } catch (error) {
        console.error('获取库存明细详情失败:', error);
        this.$message.error('获取库存明细详情失败');
      }
    },

    // 库存调整
    handleAdjust(row) {
      this.adjustFormData = {
        detailId: row.detailId,
        materialName: row.materialName,
        currentStock: row.currentStock,
        adjustType: 'increase',
        adjustQuantity: 0,
        adjustReason: '',
        remark: ''
      };
      this.adjustDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.adjustForm) {
          this.$refs.adjustForm.clearValidate();
        }
      });
    },

    // 提交调整
    async handleAdjustSubmit() {
      this.$refs.adjustForm.validate(async (valid) => {
        if (!valid) {
          return;
        }

        try {
          this.adjustLoading = true;
          const response = await adjustInventoryDetail(this.adjustFormData);
          if (response && response.code === 200) {
            this.$message.success('库存调整成功');
            this.adjustDialogVisible = false;
            this.fetchData();
            this.fetchStatistics();
          } else {
            this.$message.error(response.message || '库存调整失败');
          }
        } catch (error) {
          console.error('库存调整失败:', error);
          this.$message.error('库存调整失败');
        } finally {
          this.adjustLoading = false;
        }
      });
    },

    // 移库
    handleMove(row) {
      // 检查当前区域状态
      const currentZone = this.zoneOptions.find(z => z.zoneCode === row.zoneCode);
      if (currentZone) {
        if (currentZone.status === '0') {
          this.$message.error('当前区域已停用，无法进行移库操作');
          return;
        }
        if (currentZone.warehouseStatus === '0') {
          this.$message.error('当前区域的仓库已停用，无法进行移库操作');
          return;
        }
      }

      // 确保获取最新的区域列表
      this.fetchZoneOptions();

      this.moveFormData = {
        detailId: row.detailId,
        materialName: row.materialName,
        currentLocation: `${row.zoneName}`,
        availableStock: row.currentStock || 0,
        targetZoneId: '',
        moveQuantity: 1, // 默认值为1
        moveReason: '',
        currentZoneId: row.zoneId
      };

      this.moveDialogVisible = true;

      this.$nextTick(() => {
        if (this.$refs.moveForm) {
          this.$refs.moveForm.clearValidate();
        }
      });
    },

    // 提交移库
    async handleMoveSubmit() {
      this.$refs.moveForm.validate(async (valid) => {
        if (!valid) {
          return;
        }

        try {
          this.moveLoading = true;
          const response = await moveInventoryStock(this.moveFormData);
          if (response && response.code === 200) {
            this.$message.success('移库成功');
            this.moveDialogVisible = false;
            this.fetchData();
            this.fetchStatistics();
          } else {
            this.$message.error(response.message || '移库失败');
          }
        } catch (error) {
          console.error('移库失败:', error);
          this.$message.error('移库失败');
        } finally {
          this.moveLoading = false;
        }
      });
    },



    // 根据区域ID获取区域名称
    getZoneNameById(zoneId) {
      const zone = this.zoneOptions.find(z => z.zoneId === zoneId);
      return zone ? zone.zoneName : '未知区域';
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'inbound':
          this.handleInbound(row);
          break;
        case 'outbound':
          this.handleOutbound(row);
          break;
        case 'trace':
          this.handleTrace(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
        default:
          console.warn('未知的操作命令:', command);
      }
    },

    // 追溯
    async handleTrace(row) {
      try {
        const response = await getInventoryTrace(row.detailId);
        if (response && response.code === 200) {
          const traceData = response.data || [];
          if (traceData.length > 0) {
            this.showTraceDialog(row, traceData);
          } else {
            this.$message.info('暂无追溯信息');
          }
        } else {
          this.$message.error('获取追溯信息失败');
        }
      } catch (error) {
        console.error('获取追溯信息失败:', error);
        this.$message.error('获取追溯信息失败');
      }
    },

    // 显示追溯信息弹窗
    showTraceDialog(row, traceData) {
      let content = `<div style="text-align: left;">`;
      content += `<h4>物料：${row.materialName}</h4>`;
      content += `<h4>批次号：${row.batchNo || '无'}</h4>`;
      content += `<h4>追溯记录：</h4>`;
      content += `<ul>`;

      traceData.forEach(trace => {
        content += `<li>`;
        content += `<strong>${trace.operation_time}</strong><br/>`;
        content += `操作人：${trace.operator}<br/>`;
        content += `操作：${trace.operation_desc}<br/>`;
        if (trace.remark) {
          content += `备注：${trace.remark}<br/>`;
        }
        content += `</li><hr/>`;
      });

      content += `</ul></div>`;

      this.$alert(content, '库存追溯信息', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true
      });
    },

    // 表格选择
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchData();
    },

    // 获取物料类型标签样式
    getMaterialTypeTag(type) {
      const tagMap = {
        '原料': 'primary',
        '一级半成品': 'warning',
        '二级半成品': 'warning',
        '成品': 'success',
        '零部件': 'info'
      };
      return tagMap[type] || 'info';
    },

    // 获取库存样式
    getStockClass(row) {
      const currentStock = row.currentStock || 0;
      const minStock = row.minStockQuantity || 0;

      if (currentStock <= minStock) return 'low-stock';
      if (currentStock <= minStock * 2) return 'medium-stock';
      return 'high-stock';
    },

    // 获取区域选项样式类
    getZoneOptionClass(zone) {
      if (zone.status === '0') {
        return 'disabled-zone-option';
      } else if (zone.warehouseStatus === '0') {
        return 'disabled-warehouse-option';
      }
      return '';
    },

    // 获取区域文本样式
    getZoneTextStyle(zone) {
      if (zone.status === '0') {
        return {
          color: '#f56c6c',
          textDecoration: 'line-through'
        };
      } else if (zone.warehouseStatus === '0') {
        return {
          color: '#e6a23c',
          fontStyle: 'italic'
        };
      }
      return {};
    },

    // 验证区域状态
    validateZoneStatus(zoneCode) {
      const zone = this.zoneOptions.find(z => z.zoneCode === zoneCode);
      if (!zone) {
        return '所选区域不存在';
      }
      if (zone.status === '0') {
        return '所选区域已停用，无法进行库存操作';
      }
      if (zone.warehouseStatus === '0') {
        return '所选区域的仓库已停用，无法进行库存操作';
      }
      return '';
    },

    // 验证区域状态和唯一性的合并方法
    // 注意：此方法现在只做区域状态验证，不执行异步唯一性校验，避免干扰表单提交
    validateZoneStatusAndUniqueness(rule, value, callback) {
      // 先验证区域状态
      if (value) {
        const statusError = this.validateZoneStatus(value);
        if (statusError) {
          callback(new Error(statusError));
          return;
        }
      }

      // 在表单验证阶段跳过异步唯一性校验，将在提交时单独验证
      console.log('表单验证阶段：区域状态验证通过，跳过异步唯一性校验');
      callback();
    },

    // 获取区域状态信息
    getZoneStatusInfo(zoneCode) {
      const zone = this.zoneOptions.find(z => z.zoneCode === zoneCode);

      if (!zone) {
        return {
          isZoneDisabled: false,
          isWarehouseDisabled: false,
          zone: null
        };
      }

      return {
        isZoneDisabled: zone.status === '0',
        isWarehouseDisabled: zone.warehouseStatus === '0',
        zone: zone
      };
    },





    // 表格汇总 - 已禁用
    // getSummaries(param) {
    //   const { columns, data } = param;
    //   const sums = [];
    //   columns.forEach((column, index) => {
    //     if (index === 0) {
    //       sums[index] = '合计';
    //       return;
    //     }
    //     if (column.property === 'currentStock') {
    //       const values = data.map(item => Number(item.currentStock) || 0);
    //       sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //     } else if (column.property === 'inboundQuantity') {
    //       const values = data.map(item => Number(item.inboundQuantity) || 0);
    //       sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //     } else if (column.property === 'outboundQuantity') {
    //       const values = data.map(item => Number(item.outboundQuantity) || 0);
    //       sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //     } else if (column.property === 'stockQuantity') {
    //       const values = data.map(item => Number(item.stockQuantity) || 0);
    //       sums[index] = values.reduce((prev, curr) => prev + curr, 0);
    //     } else if (column.property === 'totalValue') {
    //       const values = data.map(item => (item.currentStock || 0) * (item.unitPrice || 0));
    //       sums[index] = '¥' + values.reduce((prev, curr) => prev + curr, 0).toFixed(2);
    //     } else {
    //       sums[index] = '';
    //     }
    //   });
    //   return sums;
    // },

    // 新增库存明细
    handleAdd() {
      console.log('=== 开始新增库存明细 ===');
      this.formDialogTitle = '新增库存明细';

      // 初始化表单数据，物料ID为空表示自动生成
      this.detailFormData = {
        detailId: null,
        warehouseType: '',  // 新增：仓库类型字段
        zoneCode: '',
        materialId: '', // 初始为空，避免验证冲突
        materialName: '',
        materialType: '',
        batchNo: '',
        unit: '',
        currentStock: 0,
        inboundQuantity: 0,
        outboundQuantity: 0,
        stockQuantity: 0, // 新增时，结存数量为0
        minStockQuantity: 0,
        entryDate: '',
        sourceType: '',
        sourceNo: '',
        remark: '',
        createBy: this.$store.getters.name || '', // 自动设置创建人为当前登录用户
        updateBy: '',
        // 新增字段
        boardType: '',
        styleId: '',
        seriesId: '',
        supplierId: '',
        supplierName: '',
        productNumber: ''
      };

      // 重新获取区域选项
      this.fetchZoneOptions();

      console.log('表单数据初始化完成');

      // 显示对话框（初始化逻辑已移至handleDialogOpened方法）
      this.formDialogVisible = true;
    },

    // 处理弹窗开始打开（预处理）
    handleDialogOpen() {
      console.log('弹窗开始打开，进行预处理...');

      // 强制移除可能影响交互的全局状态
      document.body.style.pointerEvents = 'auto';

      // 确保没有残留的事件阻止器
      setTimeout(() => {
        const dialogEl = document.querySelector('.el-dialog');
        if (dialogEl) {
          dialogEl.style.pointerEvents = 'auto';

          // 强制重新绑定事件
          const buttons = dialogEl.querySelectorAll('.el-button');
          buttons.forEach(btn => {
            btn.style.pointerEvents = 'auto';
            btn.removeAttribute('disabled');
          });
        }
      }, 50);
    },

    // 处理弹窗完全打开后的初始化
    handleDialogOpened() {
      console.log('弹窗已完全打开，开始最终初始化...');

      // 使用setTimeout确保DOM完全就绪
      setTimeout(() => {
        if (this.$refs.detailForm) {
          this.$refs.detailForm.clearValidate();
          console.log('弹窗打开后 - 表单验证已清空');

          // 确保整个表单区域都可交互
          const formEl = this.$refs.detailForm.$el;
          if (formEl) {
            formEl.style.pointerEvents = 'auto';
            formEl.removeAttribute('inert');

            // 确保所有子元素都可交互
            const allElements = formEl.querySelectorAll('*');
            allElements.forEach(el => {
              el.style.pointerEvents = 'auto';
            });
          }

          // 强制重新绑定弹窗按钮事件
          const dialogFooter = document.querySelector('.dialog-footer');
          if (dialogFooter) {
            const buttons = dialogFooter.querySelectorAll('.el-button');
            buttons.forEach((btn, index) => {
              btn.style.pointerEvents = 'auto';
              btn.removeAttribute('disabled');

              // 移除旧的事件监听器并重新绑定
              const newBtn = btn.cloneNode(true);
              btn.parentNode.replaceChild(newBtn, btn);

              // 重新绑定事件
              if (index === 0) { // 取消按钮
                newBtn.addEventListener('click', (e) => {
                  e.stopPropagation();
                  this.handleFormCancel();
                });
              } else if (index === 1) { // 确定按钮
                newBtn.addEventListener('click', (e) => {
                  e.stopPropagation();
                  this.handleFormSubmit();
                });
              }
            });
          }
        }
      }, 100);
    },

    // 处理表单取消
    handleFormCancel() {
      console.log('=== 表单取消事件触发 ===');
      console.log('取消按钮被点击，时间戳:', new Date().toISOString());

      this.formDialogVisible = false;

      // 清理表单状态
      this.$nextTick(() => {
        if (this.$refs.detailForm) {
          this.$refs.detailForm.clearValidate();
        }
      });

      console.log('=== 表单取消处理完成 ===');
    },

    // 处理仓库类型变化
    async handleWarehouseTypeChange(value) {
      console.log('仓库类型变化:', value);

      // 只在新增模式下且用户主动选择仓库类型时才清空区域
      // 编辑模式下不清空区域，因为仓库类型是自动填充的
      if (!this.isEditMode) {
        // 清空物料类型（但不清空区域，允许用户保持已选择的区域）
        this.detailFormData.materialType = '';

        // 简化：只做基本的UI提示，不进行后端校验
        // 完整的业务校验在表单提交时由后端统一处理
        if (this.detailFormData.zoneCode && value) {
          const selectedZone = this.zoneOptions.find(z => z.zoneCode === this.detailFormData.zoneCode);
          if (selectedZone && selectedZone.warehouseType !== value) {
            this.$message.warning(`当前选择的区域"${selectedZone.zoneName}"属于${this.getWarehouseTypeLabel(selectedZone.warehouseType)}，与所选仓库类型不匹配`);
          }
        }
      }

      // 根据仓库类型设置可选的物料类型
      if (value) {
        this.setMaterialTypesByWarehouseType(value);
      }
    },

    // 处理区域变化
    async handleZoneChange(value) {
      console.log('区域变化:', value);

      if (value) {
        // 根据选中的区域获取仓库类型
        const zone = this.zoneOptions.find(z => z.zoneCode === value);
        if (zone) {
          const previousWarehouseType = this.detailFormData.warehouseType;

          // 修复：无论新增模式还是编辑模式，都需要根据区域自动设置仓库类型以保持数据一致性
          this.detailFormData.warehouseType = zone.warehouseType;

          // 智能提示：如果用户已输入物料ID，检查是否与区域匹配
          if (this.detailFormData.materialId && this.detailFormData.materialType) {
            this.checkMaterialZoneCompatibilityHint(
              this.detailFormData.materialType,
              zone.warehouseType,
              zone.zoneName
            );
          }

          // 根据模式和变化情况给出不同的提示
          if (this.isEditMode) {
            // 编辑模式下的提示
            if (previousWarehouseType && previousWarehouseType !== zone.warehouseType) {
              // this.$message.info(`编辑模式：已根据区域变更自动更新仓库类型为：${this.getWarehouseTypeLabel(zone.warehouseType)}`);
              console.log('编辑模式 - 仓库类型已同步更新:', {
                previousType: previousWarehouseType,
                newType: zone.warehouseType,
                previousTypeName: this.getWarehouseTypeLabel(previousWarehouseType),
                newTypeName: this.getWarehouseTypeLabel(zone.warehouseType)
              });
            } else if (!previousWarehouseType) {
              this.$message.success(`编辑模式：已根据区域自动填充仓库类型：${this.getWarehouseTypeLabel(zone.warehouseType)}`);
            }
          } else {
            // 新增模式下的提示
            if (previousWarehouseType && previousWarehouseType !== zone.warehouseType) {
              this.$message.info(`已根据选择的区域自动调整仓库类型为：${this.getWarehouseTypeLabel(zone.warehouseType)}`);
            } else if (!previousWarehouseType) {
              this.$message.success(`已根据选择的区域自动填充仓库类型：${this.getWarehouseTypeLabel(zone.warehouseType)}`);
            }
          }

          // 根据仓库类型设置可选的物料类型
          this.setMaterialTypesByWarehouseType(zone.warehouseType);

          console.log('区域选择完成:', {
            mode: this.isEditMode ? '编辑模式' : '新增模式',
            zoneCode: value,
            zoneName: zone.zoneName,
            warehouseType: zone.warehouseType,
            warehouseTypeName: this.getWarehouseTypeLabel(zone.warehouseType),
            previousWarehouseType: previousWarehouseType,
            warehouseTypeUpdated: previousWarehouseType !== zone.warehouseType
          });
        }
      } else {
        // 清空时的处理
        if (!this.isEditMode) {
          // 新增模式下清空仓库类型和物料类型
          this.detailFormData.warehouseType = '';
          this.detailFormData.materialType = '';
        } else {
          // 编辑模式下清空区域时，也需要清空仓库类型以保持一致性
          this.detailFormData.warehouseType = '';
          this.detailFormData.materialType = '';
          this.$message.info('编辑模式：已清空区域和仓库类型');
        }
      }
    },

    // 根据仓库类型设置可选的物料类型
    setMaterialTypesByWarehouseType(warehouseType) {
      // 清空当前物料类型
      this.detailFormData.materialType = '';

      // 根据仓库类型设置可选的物料类型
      switch (warehouseType) {
        case '1': // 原料仓库
          this.materialTypeOptions = [
            { label: '原料', value: '原料' }
          ];
          break;
        case '2': // 半成品仓库
          this.materialTypeOptions = [
            { label: '一级半成品', value: '一级半成品' },
            { label: '二级半成品', value: '二级半成品' }
          ];
          break;
        case '3': // 成品仓库
          this.materialTypeOptions = [
            { label: '成品', value: '成品' }
          ];
          break;
        case '4': // 零件仓库
          this.materialTypeOptions = [
            { label: '零部件', value: '零部件' }
          ];
          break;
        default:
          // 如果没有仓库类型，显示所有物料类型
          this.materialTypeOptions = [
            { label: '原料', value: '原料' },
            { label: '零部件', value: '零部件' },
            { label: '成品', value: '成品' },
            { label: '一级半成品', value: '一级半成品' },
            { label: '二级半成品', value: '二级半成品' }
          ];
      }
    },

    // 根据物料类型生成物料ID
    async generateMaterialId(materialType) {
      if (!materialType) {
        console.warn('物料类型为空，无法生成物料ID');
        return '';
      }

      console.log('🚀 调用后端API生成物料ID，物料类型:', materialType);
      console.log('🚀 API URL: /system/inventoryDetail/generateNextMaterialId');
      console.log('🚀 API参数: { materialType:', materialType, '}');

      try {
        // 调用后端API获取下一个可用的物料ID
        const response = await generateNextMaterialId(materialType);
        console.log('📡 后端API完整响应:', JSON.stringify(response, null, 2));

        if (response && response.code === 200 && response.data) {
          console.log('✅ 后端API成功返回物料ID:', response.data);
          return response.data;
        } else {
          // 如果API调用失败，使用备用方案
          console.error('❌ 后端API调用失败，响应码:', response?.code);
          console.error('❌ 错误信息:', response?.msg || response?.message);
          console.error('❌ 完整响应:', response);

          this.$message.error(`API调用失败: ${response?.msg || response?.message || '未知错误'}`);

          const fallbackId = this.generateFallbackMaterialId(materialType);
          return fallbackId;
        }
      } catch (error) {
        console.error('💥 调用后端API时发生异常:', error);
        console.error('💥 异常类型:', error.name);
        console.error('💥 异常消息:', error.message);
        console.error('💥 异常堆栈:', error.stack);

        // 检查是否是网络错误
        if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          this.$message.error('网络连接失败，请检查网络连接');
        } else if (error.response) {
          // 服务器返回了错误响应
          console.error('💥 服务器错误响应:', error.response);
          this.$message.error(`服务器错误: ${error.response.status} - ${error.response.statusText}`);
        } else {
          this.$message.error(`API调用异常: ${error.message}`);
        }

        // 发生错误时使用备用方案
        const fallbackId = this.generateFallbackMaterialId(materialType);
        return fallbackId;
      }
    },

    // 备用物料ID生成方法（仅在API完全失败时使用）
    generateFallbackMaterialId(materialType) {
      console.warn('⚠️ 使用备用方案生成物料ID，物料类型:', materialType);

      const prefix = this.materialIdPrefixConfig[materialType] || 'MAT';

      // 使用更安全的随机数生成，避免与现有数据冲突
      const randomBase = Math.floor(Math.random() * 900000) + 100000; // 6位随机数

      // 根据物料类型确定格式，保持与后端一致
      let fallbackId;
      if (materialType === '一级半成品' || materialType === '二级半成品') {
        // SF1/SF2 + 7位数字
        fallbackId = `${prefix}${randomBase.toString().padStart(7, '9')}`;
      } else {
        // RM/CP/PD + 6位数字
        fallbackId = `${prefix}${randomBase.toString().padStart(6, '9')}`;
      }

      console.warn('⚠️ 备用方案生成的物料ID:', fallbackId);
      this.$message.warning(`API调用失败，使用备用ID: ${fallbackId}`);

      return fallbackId;
    },

    // 处理物料ID输入
    handleMaterialIdInput(value) {
      console.log('物料ID输入变化:', value);

      // 如果在编辑模式下，不允许修改
      if (this.detailFormData.detailId) {
        this.$message.warning('编辑模式下不能修改物料ID');
        return;
      }
    },

    // 获取物料ID格式说明 - 已取消校验逻辑，不再需要
    // getMaterialIdFormat() {
    //   const materialType = this.detailFormData.materialType;
    //   if (!materialType) return '';

    //   const prefix = this.materialIdPrefixConfig[materialType] || 'MAT';
    //   if (materialType === '一级半成品' || materialType === '二级半成品') {
    //     return `${prefix}XXXXXX，如 ${prefix}000001`;
    //   } else {
    //     return `${prefix}XXXXX，如 ${prefix}00001`;
    //   }
    // },

    // 验证自定义物料ID - 已取消校验逻辑
    // async validateCustomMaterialId() {
    //   const materialId = this.detailFormData.materialId;
    //   if (!materialId) {
    //     this.$message.warning('请先输入物料ID');
    //     return;
    //   }

    //   try {
    //     // 触发表单验证
    //     this.$refs.detailForm.validateField('materialId', (errorMessage) => {
    //       if (errorMessage) {
    //         this.$message.error('物料ID验证失败：' + errorMessage);
    //       } else {
    //         this.$message.success('物料ID验证通过');
    //       }
    //     });
    //   } catch (error) {
    //     console.error('验证物料ID失败:', error);
    //     this.$message.error('验证失败，请重试');
    //   }
    // },

    // 处理物料类型变化
    handleMaterialTypeChange(materialType) {
      if (materialType && !this.detailFormData.detailId) {
        // 清空物料ID，让用户重新输入或在提交时自动生成
        this.detailFormData.materialId = '';
        console.log('物料类型已选择:', materialType, '- 物料ID已清空');

        // 智能提示：如果用户已选择区域，检查是否与物料类型匹配
        if (this.detailFormData.zoneCode && this.detailFormData.warehouseType) {
          const zone = this.zoneOptions.find(z => z.zoneCode === this.detailFormData.zoneCode);
          if (zone) {
            this.checkMaterialZoneCompatibilityHint(
              materialType,
              zone.warehouseType,
              zone.zoneName
            );
          }
        }
      }
    },

    // 验证物料ID唯一性 - 已取消校验逻辑
    // async validateMaterialIdUniqueness(materialId) {
    //   if (!materialId) {
    //     console.log('物料ID为空，跳过唯一性验证');
    //     return true;
    //   }

    //   console.log('验证物料ID唯一性 - 物料ID:', materialId, '模式:', this.materialIdMode);

    //   try {
    //     let response;

    //     if (this.materialIdMode === 'auto') {
    //       // 自动生成模式：使用全局唯一性校验
    //       console.log('自动生成模式 - 调用全局唯一性校验API');
    //       response = await checkMaterialIdUnique(materialId);
    //     } else {
    //       // 手动输入模式：使用上下文唯一性校验（基于区域和批次）
    //       console.log('手动输入模式 - 调用上下文唯一性校验API');
    //       const zoneCode = this.detailFormData.zoneCode;
    //       const batchNo = this.detailFormData.batchNo;
    //       const detailId = this.detailFormData.detailId;

    //       if (!zoneCode) {
    //         console.warn('手动输入模式下缺少区域编码，跳过唯一性验证');
    //         return true; // 缺少必要参数时跳过验证
    //       }

    //       response = await checkMaterialIdUniqueInContext(materialId, zoneCode, batchNo, detailId);
    //     }

    //     console.log('物料ID唯一性验证API响应:', response);
    //     const isUnique = response && response.code === 200 && response.data === true;
    //     console.log('物料ID唯一性验证结果:', isUnique);

    //     return isUnique;
    //   } catch (error) {
    //     console.warn('物料ID唯一性校验API调用失败:', error);
    //     return true; // 校验失败时不阻止提交，由后端最终校验
    //   }
    // },

    // 重新生成物料ID
    async handleRegenerateMaterialId() {
      if (!this.detailFormData.materialType) {
        this.$message.warning('请先选择物料类型');
        return;
      }

      if (this.detailFormData.detailId) {
        this.$message.warning('编辑模式下不能重新生成物料ID');
        return;
      }

      try {
        console.log('重新生成物料ID，物料类型:', this.detailFormData.materialType);
        const newMaterialId = await this.generateMaterialId(this.detailFormData.materialType);
        console.log('重新生成的物料ID:', newMaterialId);

        this.detailFormData.materialId = newMaterialId;
        console.log('设置到表单的新物料ID:', this.detailFormData.materialId);

        this.$message.success(`物料ID已重新生成: ${newMaterialId}`);
      } catch (error) {
        console.error('重新生成物料ID失败:', error);
        this.$message.error('重新生成物料ID失败，请重试');
      }
    },

    // 编辑库存明细
    async handleEdit(row) {
      try {
        // 确保区域选项已加载，以便正确获取仓库类型
        if (!this.zoneOptions || this.zoneOptions.length === 0) {
          console.log('区域选项未加载，先加载区域选项...');
          await this.fetchZoneOptions();
        }

        const response = await getInventoryDetail(row.detailId);
        if (response && response.code === 200) {
          this.formDialogTitle = '编辑库存明细';

          // 根据区域编码自动获取仓库类型
          const autoWarehouseType = this.getWarehouseTypeByZoneCode(response.data.zoneCode);

          this.detailFormData = {
            detailId: response.data.detailId,
            warehouseType: autoWarehouseType || response.data.warehouseType || '', // 自动填充仓库类型
            zoneCode: response.data.zoneCode,
            materialId: response.data.materialId || '',
            materialName: response.data.materialName,
            materialType: response.data.materialType,
            batchNo: response.data.batchNo || '',
            unit: response.data.unit || '',
            inboundQuantity: response.data.inboundQuantity || 0,
            outboundQuantity: response.data.outboundQuantity || 0,
            stockQuantity: response.data.stockQuantity || 0,
            minStockQuantity: response.data.minStockQuantity || 0,
            entryDate: response.data.entryDate || '',
            sourceType: response.data.sourceType || '',
            sourceNo: response.data.sourceNo || '',
            remark: response.data.remark || '',
            createBy: response.data.createBy || '', // 保留原创建人
            updateBy: this.$store.getters.name || '', // 自动设置更新人为当前登录用户
            // 新增字段
            boardType: response.data.boardType || '',
            styleId: response.data.styleId || '',
            seriesId: response.data.seriesId || '',
            supplierId: response.data.supplierId || '',
            supplierName: response.data.supplierName || '',
            productNumber: response.data.productNumber || ''
          };

          // 如果自动填充了仓库类型，显示提示信息
          if (autoWarehouseType) {
            console.log('编辑模式：已自动填充仓库类型', {
              zoneCode: response.data.zoneCode,
              warehouseType: autoWarehouseType,
              warehouseTypeName: this.getWarehouseTypeLabel(autoWarehouseType)
            });
            this.$message.success(`已自动填充仓库类型：${this.getWarehouseTypeLabel(autoWarehouseType)}`);
          } else {
            console.warn('未能自动填充仓库类型，使用原有数据');
          }

          // 显示对话框（初始化逻辑已移至handleDialogOpened方法）
          this.formDialogVisible = true;
        } else {
          this.$message.error('获取库存明细详情失败');
        }
      } catch (error) {
        console.error('获取库存明细详情失败:', error);
        this.$message.error('获取库存明细详情失败');
      }
    },

    // 删除库存明细
    handleDelete(row) {
      this.$confirm(`确定要删除库存明细"${row.materialName}"吗？删除后不可恢复！`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 后端会自动同步删除对应的仓储管理表记录
          const response = await delInventoryDetail(row.detailId);
          if (response && response.code === 200) {
            this.$message.success('删除成功');
            this.fetchData();
            this.fetchStatistics();
          } else {
            this.$message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }

      this.$confirm(`确定要删除选中的${this.selectedRows.length}条库存明细吗？删除后不可恢复！`, '确认批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const detailIds = this.selectedRows.map(row => row.detailId).join(',');
          // 后端会自动同步删除对应的仓储管理表记录
          const response = await delInventoryDetail(detailIds);
          if (response && response.code === 200) {
            this.$message.success('批量删除成功');
            this.fetchData();
            this.fetchStatistics();
          } else {
            this.$message.error(response.message || '批量删除失败');
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          this.$message.error('批量删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 处理新增或更新库存明细
    async handleAddOrUpdateInventoryDetail() {
      const dataToSend = { ...this.detailFormData };

      // 根据公式计算当前库存
      const stockQuantity = Number(dataToSend.stockQuantity) || 0;
      const inboundQuantity = Number(dataToSend.inboundQuantity) || 0;
      const outboundQuantity = Number(dataToSend.outboundQuantity) || 0;
      dataToSend.currentStock = stockQuantity + inboundQuantity - outboundQuantity;

      if (dataToSend.currentStock < 0) {
        this.$message.error('计算出的当前库存为负数，请检查入库、出库数量。');
        return Promise.reject(new Error('库存计算错误'));
      }

      if (dataToSend.detailId) {
        // 更新现有记录 - 后端会自动同步到对应的仓储管理表
        return updateInventoryDetail(dataToSend);
      } else {
        // 新增记录 - 后端会自动同步到对应的仓储管理表
        return addInventoryDetail(dataToSend);
      }
    },



    // 处理区域变化
    handleFormZoneChange(zoneCode) {
      // 检查选择的区域状态
      if (zoneCode) {
        const zone = this.zoneOptions.find(z => z.zoneCode === zoneCode);
        if (zone) {
          if (zone.status === '0') {
            this.$message.warning('警告：当前选择的区域已停用');
            this.$nextTick(() => {
              this.$refs.detailForm.validateField('zoneCode');
            });
          } else if (zone.warehouseStatus === '0') {
            this.$message.warning('警告：当前选择区域的仓库已停用');
            this.$nextTick(() => {
              this.$refs.detailForm.validateField('zoneCode');
            });
          }
        }
      }
    },

    // 获取来源类型中文文本
    getSourceTypeText(sourceType) {
      return this.sourceTypeMap[sourceType] || sourceType || '-';
    },

    // 物料ID格式验证 - 已取消校验逻辑
    // async validateMaterialIdFormat(rule, value, callback) {
    //   console.log('=== 开始物料ID验证 ===');
    //   console.log('验证的物料ID:', value);
    //   console.log('当前表单物料ID:', this.detailFormData.materialId);

    //   // 在新增模式下
    //   if (!this.detailFormData.detailId) {
    //     // 如果物料ID为空，表示使用自动生成模式，跳过验证
    //     if (!value) {
    //       console.log('物料ID为空，将使用自动生成模式');
    //       callback();
    //       return;
    //     }
    //   } else {
    //     // 编辑模式下，物料ID不能为空
    //     if (!value) {
    //       console.log('编辑模式下物料ID不能为空');
    //       callback(new Error('物料ID不能为空'));
    //       return;
    //     }
    //   }

    //   // 如果有值，进行格式验证
    //   if (value) {
    //     // 验证物料ID格式是否符合前缀规则
    //     const materialType = this.detailFormData.materialType;
    //     console.log('当前物料类型:', materialType);

    //     if (materialType) {
    //       const expectedPrefix = this.materialIdPrefixConfig[materialType];
    //       console.log('期望前缀:', expectedPrefix);

    //       if (expectedPrefix && !value.startsWith(expectedPrefix)) {
    //         console.warn(`❌ 物料ID格式不正确: 期望前缀=${expectedPrefix}, 实际值=${value}`);
    //         callback(new Error(`物料ID应以"${expectedPrefix}"开头`));
    //         return;
    //       }

    //       // 验证数字部分的长度
    //       const numberPart = value.substring(expectedPrefix.length);
    //       const expectedLength = (materialType === '一级半成品' || materialType === '二级半成品') ? 6 : 6;

    //       if (!/^\d+$/.test(numberPart)) {
    //         console.warn(`❌ 物料ID格式不正确: 数字部分包含非数字字符, 实际值=${value}`);
    //         callback(new Error(`物料ID格式不正确，${expectedPrefix}后应为${expectedLength}位数字`));
    //         return;
    //       }

    //       if (numberPart.length !== expectedLength) {
    //         console.warn(`❌ 物料ID格式不正确: 数字部分长度=${numberPart.length}, 期望长度=${expectedLength}`);
    //         callback(new Error(`物料ID格式不正确，${expectedPrefix}后应为${expectedLength}位数字`));
    //         return;
    //       }

    //       console.log('✅ 物料ID格式验证通过');
    //     }

    //     // // 验证物料ID唯一性（仅在新增时）
    //     // if (!this.detailFormData.detailId) {
    //     //   try {
    //     //     console.log('开始验证物料ID唯一性:', value);
    //     //     const isUnique = await this.validateMaterialIdUniqueness(value);
    //     //     console.log('物料ID唯一性验证结果:', isUnique);

    //     //     if (!isUnique) {
    //     //       console.warn(`❌ 物料ID[${value}]已存在`);
    //     //       if (this.materialIdMode === 'auto') {
    //     //         callback(new Error(`物料ID[${value}]已存在，请重新生成`));
    //     //       } else {
    //     //         callback(new Error(`物料ID[${value}]已存在，请输入其他ID`));
    //     //       }
    //     //     } else {
    //     //       console.log('✅ 物料ID唯一性验证通过');
    //     //       callback();
    //     //     }
    //     //   } catch (error) {
    //     //     console.warn('物料ID唯一性校验失败:', error);
    //     //     callback(); // 校验失败时不阻止提交
    //     //   }
    //     // } else {
    //     //   console.log('编辑模式，跳过唯一性验证');
    //     //   callback();
    //     // }
    //   }
    //   console.log('=== 物料ID验证完成 ===');
    // },

    // 唯一性校验（新校验：物料名称+物料类型+批次号+存储区域组合）
    // 注意：此方法现在只做基础验证，不执行异步操作，避免干扰表单提交
    async validateUniqueness(rule, value, callback) {
      // 检查必要字段是否都有值
      const { materialName, materialType, batchNo, zoneCode } = this.detailFormData;

      if (!materialName || !materialType || !zoneCode) {
        // 如果必要字段没有值，跳过校验
        callback();
        return;
      }

      // 在表单验证阶段，只做基础的格式检查，不执行异步唯一性校验
      // 异步唯一性校验将在提交时单独进行，避免干扰表单验证流程
      console.log('表单验证阶段：跳过异步唯一性校验，将在提交时单独验证');
      callback();
    },

    // 带防抖的唯一性校验
    validateUniquenessWithDebounce(materialName, materialType, batchNo, zoneCode, callback) {
      // 生成当前校验参数的唯一标识
      const currentParams = `${materialName}|${materialType}|${batchNo || ''}|${zoneCode}`;

      // 如果参数与上次相同，直接返回成功（避免重复校验）
      if (this.lastUniquenessParams === currentParams) {
        console.log('检测到相同参数的唯一性校验，跳过重复调用:', currentParams);
        callback();
        return;
      }

      // 清除之前的定时器
      if (this.uniquenessValidationTimer) {
        clearTimeout(this.uniquenessValidationTimer);
      }

      // 设置新的防抖定时器
      this.uniquenessValidationTimer = setTimeout(async () => {
        try {
          console.log('执行唯一性校验:', { materialName, materialType, batchNo, zoneCode });

          const isUnique = await this.checkInventoryDetailUniqueness(materialName, materialType, batchNo, zoneCode);

          // 记录本次校验参数
          this.lastUniquenessParams = currentParams;

          if (!isUnique) {
            callback(new Error(`物料名称[${materialName}]+物料类型[${materialType}]+批次号[${batchNo || '无'}]+存储区域的组合已存在`));
          } else {
            callback();
          }
        } catch (error) {
          // 查询失败时不阻止提交，由后端最终校验
          console.warn('库存明细唯一性校验查询失败:', error);
          callback();
        } finally {
          this.uniquenessValidationTimer = null;
        }
      }, 300); // 300ms防抖延迟
    },

    // 检查库存明细唯一性的API调用
    async checkInventoryDetailUniqueness(materialName, materialType, batchNo, zoneCode) {
      try {
        const params = {
          materialName,
          materialType,
          batchNo: batchNo || '',
          zoneCode,
          detailId: this.detailFormData.detailId || null
        };

        const response = await checkInventoryDetailUniqueByCode(params);
        if (response && response.code === 200) {
          return response.data; // true表示唯一，false表示重复
        }
        return true; // 默认认为唯一，由后端最终校验
      } catch (error) {
        console.error('校验库存明细唯一性失败:', error);
        return true; // 默认认为唯一，由后端最终校验
      }
    },

    // 检查唯一性的API调用（保留兼容性）
    async checkUniqueness(materialId, batchNo, zoneId) {
      // 调用后端API检查唯一性
      return await getInventoryDetailByMaterialAndZone(materialId, zoneId, batchNo);
    },

    // 处理新增或更新库存明细
    async handleFormSubmit() {
      console.log('=== 表单提交事件触发 ===');
      console.log('提交按钮被点击，时间戳:', new Date().toISOString());
      console.log('formLoading状态:', this.formLoading);
      console.log('表单引用可用:', !!this.$refs.detailForm);

      // 如果正在提交中，防止重复提交
      if (this.formLoading) {
        console.log('表单正在提交中，忽略重复点击');
        return;
      }

      try {
        this.formLoading = true;

        // 清除之前的唯一性校验定时器，避免异步校验干扰
        if (this.uniquenessValidationTimer) {
          clearTimeout(this.uniquenessValidationTimer);
          this.uniquenessValidationTimer = null;
          console.log('清除之前的唯一性校验定时器');
        }

        // 进行基础表单验证（不包含异步校验）
        const basicValidation = await this.validateBasicForm();
        if (!basicValidation.valid) {
          this.$message.error(basicValidation.message);
          this.formLoading = false;
          return;
        }

        // 确保在提交前生成物料ID（如果需要）
        const materialIdGenerated = await this.ensureMaterialIdGenerated();
        if (!materialIdGenerated) {
          this.formLoading = false;
          return;
        }

        // 在提交前进行物料存放位置校验（只在新增/编辑时调用一次）
        if (this.detailFormData.materialId && this.detailFormData.materialType &&
            this.detailFormData.warehouseType && this.detailFormData.zoneCode) {

          const validationResult = await this.validateMaterialStorageLocation(
            this.detailFormData.materialId,
            this.detailFormData.materialType,
            this.detailFormData.warehouseType,
            this.detailFormData.zoneCode
          );

          if (!validationResult.valid) {
            this.$message.error(validationResult.message);
            if (validationResult.suggestedLocation) {
              this.$message.warning(`建议：${validationResult.suggestedLocation}`);
            }
            this.formLoading = false;
            return;
          }
        }

        // 执行唯一性校验（在提交时进行，避免与表单验证冲突）
        const uniquenessValidation = await this.validateUniquenessBeforeSubmit();
        if (!uniquenessValidation.valid) {
          this.$message.error(uniquenessValidation.message);
          this.formLoading = false;
          return;
        }

        const response = await this.handleAddOrUpdateInventoryDetail();
        if (response && response.code === 200) {
          this.$message.success('库存明细保存成功');
          this.formDialogVisible = false;
          this.fetchData();
          this.fetchStatistics();
        } else {
          this.$message.error(response.message || '保存库存明细失败');
        }
      } catch (error) {
        console.error('保存库存明细失败:', error);
        this.$message.error('保存库存明细失败');
      } finally {
        this.formLoading = false;
      }
    },

    // 处理新增或更新库存明细（新增提交前物料ID生成逻辑）
    async ensureMaterialIdGenerated() {
      // 如果是新增模式
      if (!this.detailFormData.detailId) {
        // 如果物料ID为空且有物料类型，则自动生成物料ID
        if (!this.detailFormData.materialId && this.detailFormData.materialType) {
          console.log('物料ID为空，准备自动生成，物料类型:', this.detailFormData.materialType);
          try {
            const generatedId = await this.generateMaterialId(this.detailFormData.materialType);
            this.detailFormData.materialId = generatedId;
            console.log('自动生成的物料ID:', generatedId);
            return true;
          } catch (error) {
            console.error('自动生成物料ID失败:', error);
            this.$message.error('生成物料ID失败，请重试');
            return false;
          }
        }
        // 如果已输入物料ID，使用手动输入的值
        else if (this.detailFormData.materialId) {
          console.log('使用手动输入的物料ID:', this.detailFormData.materialId);
          return true;
        }
      }
      return true;
    },

    // 基础表单验证（不包含异步校验）
    async validateBasicForm() {
      return new Promise((resolve) => {
        // 创建临时的表单验证规则，排除异步校验
        const tempRules = { ...this.detailFormRules };

        // 移除可能导致异步校验的规则
        if (tempRules.materialName) {
          tempRules.materialName = tempRules.materialName.filter(rule =>
            !rule.validator || rule.validator.name !== 'validateUniqueness'
          );
        }
        if (tempRules.materialType) {
          tempRules.materialType = tempRules.materialType.filter(rule =>
            !rule.validator || rule.validator.name !== 'validateUniqueness'
          );
        }
        if (tempRules.batchNo) {
          tempRules.batchNo = tempRules.batchNo.filter(rule =>
            !rule.validator || rule.validator.name !== 'validateUniqueness'
          );
        }
        if (tempRules.zoneCode) {
          tempRules.zoneCode = tempRules.zoneCode.filter(rule =>
            !rule.validator || rule.validator.name !== 'validateZoneStatusAndUniqueness'
          );
          // 保留区域选择的基础验证
          if (!tempRules.zoneCode.some(rule => rule.required)) {
            tempRules.zoneCode.push({ required: true, message: '请选择区域', trigger: 'change' });
          }
        }

        // 临时替换表单规则并进行验证
        const originalRules = this.$refs.detailForm.rules;
        this.$refs.detailForm.rules = tempRules;

        this.$refs.detailForm.validate((valid, invalidFields) => {
          // 恢复原始规则
          this.$refs.detailForm.rules = originalRules;

          if (valid) {
            resolve({ valid: true });
          } else {
            const firstError = Object.values(invalidFields)[0][0];
            resolve({
              valid: false,
              message: firstError.message || '表单验证失败，请检查输入'
            });
          }
        });
      });
    },

    // 在提交前进行唯一性校验（避免与表单验证冲突）
    async validateUniquenessBeforeSubmit() {
      try {
        const { materialName, materialType, batchNo, zoneCode } = this.detailFormData;

        // 检查必要字段是否都有值
        if (!materialName || !materialType || !zoneCode) {
          return { valid: true, message: '基础字段验证通过' };
        }

        console.log('执行提交前唯一性校验:', { materialName, materialType, batchNo, zoneCode });

        const isUnique = await this.checkInventoryDetailUniqueness(materialName, materialType, batchNo, zoneCode);

        if (!isUnique) {
          return {
            valid: false,
            message: `物料名称[${materialName}]+物料类型[${materialType}]+批次号[${batchNo || '无'}]+存储区域的组合已存在`
          };
        }

        return { valid: true, message: '唯一性校验通过' };
      } catch (error) {
        console.warn('提交前唯一性校验失败:', error);
        // 校验失败时不阻止提交，由后端最终校验
        return { valid: true, message: '唯一性校验异常，由后端校验' };
      }
    },

    // 处理表格排序变化
    handleSortChange({ column, prop, order }) {
      console.log('排序变化:', { column, prop, order });

      if (order === null) {
        // 取消排序
        this.sortInfo.sortField = '';
        this.sortInfo.sortOrder = '';
      } else {
        this.sortInfo.sortField = prop;
        this.sortInfo.sortOrder = order === 'ascending' ? 'asc' : 'desc';
      }

      // 重新获取数据
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    // 格式化日期为年月日
    formatDate(dateStr) {
      if (!dateStr) return '';

      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.warn('日期格式化失败:', dateStr, error);
        return '';
      }
    },

    // 导出低库存预警清单
    async handleExportLowStock() {
      try {
        if (this.lowStockAlertData.length === 0) {
          this.$message.warning('没有低库存预警数据可以导出');
          return;
        }

        // 准备导出数据
        const exportData = this.lowStockAlertData.map(item => ({
          materialName: item.materialName || '',
          materialType: item.materialType || '',
          batchNo: item.batchNo || '',
          zoneName: item.zoneName || '',
          currentStock: item.currentStock || 0,
          minStockQuantity: item.minStockQuantity || 0,
          shortage: (item.minStockQuantity || 0) - (item.currentStock || 0),
          unit: item.unit || '',
          entryDate: this.formatDate(item.entryDate),
          needPurchase: item.needPurchase === 'true' ? '是' : '否'
        }));

        // 使用已有的导出接口，传入筛选后的数据
        const response = await exportInventoryDetail({
          exportType: 'lowStockAlert',
          data: exportData
        });

        this.$message.success('低库存预警清单导出成功');
      } catch (error) {
        console.error('导出低库存预警清单失败:', error);
        this.$message.error('导出失败');
      }
    },

    // 单个项目入库
    handleInbound(row) {
      this.$prompt('请输入入库数量', '库存入库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d*$/,
        inputErrorMessage: '请输入有效的入库数量',
        inputPlaceholder: '请输入入库数量',
        inputType: 'number'
      }).then(({ value }) => {
        this.$prompt('请输入入库原因', '库存入库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (val) => {
            return val && val.trim().length > 0;
          },
          inputErrorMessage: '请输入入库原因'
        }).then(({ value: reason }) => {
          this.performInbound(row.detailId, parseInt(value), reason);
        });
      }).catch(() => {
        this.$message.info('已取消入库操作');
      });
    },

    // 单个项目出库
    handleOutbound(row) {
      this.$prompt('请输入出库数量', '库存出库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d*$/,
        inputErrorMessage: '请输入有效的出库数量',
        inputPlaceholder: `当前库存：${row.currentStock}`,
        inputType: 'number'
      }).then(({ value }) => {
        if (parseInt(value) > row.currentStock) {
          this.$message.error('出库数量不能大于当前库存');
          return;
        }
        this.$prompt('请输入出库原因', '库存出库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (val) => {
            return val && val.trim().length > 0;
          },
          inputErrorMessage: '请输入出库原因'
        }).then(({ value: reason }) => {
          this.performOutbound(row.detailId, parseInt(value), reason);
        });
      }).catch(() => {
        this.$message.info('已取消出库操作');
      });
    },

    // 批量入库
    handleBatchInbound() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要入库的库存项目');
        return;
      }

      this.$prompt('请输入入库数量（将应用到所有选中项目）', '批量入库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d*$/,
        inputErrorMessage: '请输入有效的入库数量',
        inputPlaceholder: '请输入入库数量',
        inputType: 'number'
      }).then(({ value }) => {
        this.$prompt('请输入入库原因', '批量入库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (val) => {
            return val && val.trim().length > 0;
          },
          inputErrorMessage: '请输入入库原因'
        }).then(({ value: reason }) => {
          this.performBatchInbound(parseInt(value), reason);
        });
      }).catch(() => {
        this.$message.info('已取消批量入库操作');
      });
    },

    // 批量出库
    handleBatchOutbound() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要出库的库存项目');
        return;
      }

      // 检查是否有库存不足的项目
      const insufficientStockItems = this.selectedRows.filter(row => row.currentStock <= 0);
      if (insufficientStockItems.length > 0) {
        this.$message.error(`以下项目库存不足，无法出库：${insufficientStockItems.map(item => item.materialName).join('、')}`);
        return;
      }

      this.$prompt('请输入出库数量（将应用到所有选中项目）', '批量出库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d*$/,
        inputErrorMessage: '请输入有效的出库数量',
        inputPlaceholder: '请输入出库数量',
        inputType: 'number'
      }).then(({ value }) => {
        // 检查是否有项目出库数量超过库存
        const quantity = parseInt(value);
        const overStockItems = this.selectedRows.filter(row => quantity > row.currentStock);
        if (overStockItems.length > 0) {
          this.$message.error(`以下项目出库数量超过当前库存：${overStockItems.map(item => `${item.materialName}(库存:${item.currentStock})`).join('、')}`);
          return;
        }

        this.$prompt('请输入出库原因', '批量出库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (val) => {
            return val && val.trim().length > 0;
          },
          inputErrorMessage: '请输入出库原因'
        }).then(({ value: reason }) => {
          this.performBatchOutbound(quantity, reason);
        });
      }).catch(() => {
        this.$message.info('已取消批量出库操作');
      });
    },

    // 执行入库操作
    async performInbound(detailId, quantity, reason) {
      try {
        const response = await inboundInventory({
          detailId,
          quantity,
          reason,
          remark: '前端库存入库操作'
        });

        if (response.code === 200) {
          this.$message.success(`入库成功：数量 ${quantity}`);
          this.fetchData(); // 刷新数据
        } else {
          this.$message.error(response.msg || '入库失败');
        }
      } catch (error) {
        console.error('入库操作失败:', error);
        this.$message.error('入库操作失败：' + (error.message || '网络错误'));
      }
    },

    // 执行出库操作
    async performOutbound(detailId, quantity, reason) {
      try {
        const response = await outboundInventory({
          detailId,
          quantity,
          reason,
          remark: '前端库存出库操作'
        });

        if (response.code === 200) {
          this.$message.success(`出库成功：数量 ${quantity}`);
          this.fetchData(); // 刷新数据
        } else {
          this.$message.error(response.msg || '出库失败');
        }
      } catch (error) {
        console.error('出库操作失败:', error);
        this.$message.error('出库操作失败：' + (error.message || '网络错误'));
      }
    },

    // 执行批量入库操作
    async performBatchInbound(quantity, reason) {
      try {
        const detailIds = this.selectedRows.map(row => row.detailId);
        const response = await batchInboundInventory({
          detailIds,
          quantity,
          reason,
          remark: '前端批量库存入库操作'
        });

        if (response.code === 200) {
          this.$message.success(`批量入库成功：${this.selectedRows.length} 个项目，每个入库数量 ${quantity}`);
          this.fetchData(); // 刷新数据
          this.selectedRows = []; // 清空选择
        } else {
          this.$message.error(response.msg || '批量入库失败');
        }
      } catch (error) {
        console.error('批量入库操作失败:', error);
        this.$message.error('批量入库操作失败：' + (error.message || '网络错误'));
      }
    },

    // 执行批量出库操作
    async performBatchOutbound(quantity, reason) {
      try {
        const detailIds = this.selectedRows.map(row => row.detailId);
        const response = await batchOutboundInventory({
          detailIds,
          quantity,
          reason,
          remark: '前端批量库存出库操作'
        });

        if (response.code === 200) {
          this.$message.success(`批量出库成功：${this.selectedRows.length} 个项目，每个出库数量 ${quantity}`);
          this.fetchData(); // 刷新数据
          this.selectedRows = []; // 清空选择
        } else {
          this.$message.error(response.msg || '批量出库失败');
        }
      } catch (error) {
        console.error('批量出库操作失败:', error);
        this.$message.error('批量出库操作失败：' + (error.message || '网络错误'));
      }
    },

    // ============= 字段配置相关方法 =============

    /** 初始化字段配置 */
    initColumnConfig() {
      const savedConfig = localStorage.getItem('inventory_detail_column_config');
      if (savedConfig) {
        try {
          this.selectedColumns = JSON.parse(savedConfig);
        } catch (e) {
          console.error('解析字段配置失败:', e);
          // 使用默认配置
          this.selectedColumns = [
            'productNumber',
            'materialType',
            'batchNo',
            'currentStock',
            'inboundQuantity',
            'outboundQuantity',
            'stockQuantity',
            'minStockQuantity',
            'needPurchase',
            'unit',
            'boardType',
            'styleName',
            'seriesName',
            'supplierName',
            'entryDate',
            'status',
            'updateTime'
          ];
        }
      } else {
        // 默认显示的字段
        this.selectedColumns = [
          'productNumber',
          'materialType',
          'batchNo',
          'currentStock',
          'inboundQuantity',
          'outboundQuantity',
          'stockQuantity',
          'minStockQuantity',
          'needPurchase',
          'unit',
          'boardType',
          'styleName',
          'seriesName',
          'supplierName',
          'entryDate',
          'status',
          'updateTime'
        ];
      }
    },

    /** 字段配置按钮操作 */
    handleColumnConfig() {
      this.columnConfigVisible = true;
    },

    /** 字段变化处理 */
    handleColumnChange(value) {
      // 这里可以添加实时预览逻辑
      console.log('字段配置变化:', value);
    },

    /** 重置为默认配置 */
    resetColumnConfig() {
      this.selectedColumns = [
        'productNumber',
        'materialType',
        'batchNo',
        'currentStock',
        'inboundQuantity',
        'outboundQuantity',
        'stockQuantity',
        'minStockQuantity',
        'needPurchase',
        'unit',
        'boardType',
        'styleName',
        'seriesName',
        'supplierName',
        'entryDate',
        'status',
        'updateTime'
      ];
      // 清除本地存储的配置
      localStorage.removeItem('inventory_detail_column_config');
    },

    /** 保存字段配置 */
    saveColumnConfig() {
      // 保存到本地存储
      localStorage.setItem('inventory_detail_column_config', JSON.stringify(this.selectedColumns));
      this.columnConfigVisible = false;
      this.$modal.msgSuccess("字段配置已保存");
    },

    /** 判断字段是否为数值类型 */
    isNumericField(fieldProp) {
      // 定义数值类型字段列表
      const numericFields = [
        'currentStock',
        'inboundQuantity',
        'outboundQuantity',
        'stockQuantity',
        'minStockQuantity',
        'styleId',
        'seriesId',
        'supplierId'
      ];
      return numericFields.includes(fieldProp);
    }
  }
};
</script>

<style lang="scss" scoped>
/* 只读字段样式 */
:deep(.readonly-input) {
  .el-input__inner {
    background-color: var(--base-menu-background, #f5f7fa) !important;
    color: var(--base-color-3, #909399) !important;
    cursor: not-allowed;
    border-color: var(--border-color-1, #dcdfe6) !important;
  }
}

/* 表单字段居中样式 */
:deep(.centered-form) {
  .el-form-item__content {
    text-align: center;

    .el-input,
    .el-select,
    .el-input-number,
    .el-date-editor,
    .el-textarea {
      text-align: center;

      .el-input__inner,
      .el-textarea__inner {
        text-align: center;
      }

      .el-input-number__decrease,
      .el-input-number__increase {
        line-height: 1;
      }
    }

    .el-select .el-input__inner {
      text-align: center;
    }
  }
}

/* 表格字段居中样式 */
:deep(.el-table) {
  .el-table__body-wrapper {
    .el-table__body {
      td {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }

  .el-table__header-wrapper {
    .el-table__header {
      th {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }
}

/* 物料ID模式提示样式 */
.material-id-hint {
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;

  .mode-hint {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 4px;

    i {
      margin-right: 6px;
      font-size: 14px;
    }

    &.auto {
      background-color: var(--base-menu-background, #e7f4ff);
      color: var(--current-color, #1890ff);
      border: 1px solid var(--current-color, #1890ff);
    }

    &.manual {
      background-color: var(--base-warning-bg, #fff7e6);
      color: var(--base-warning-color, #faad14);
      border: 1px solid var(--base-warning-color, #faad14);
    }

    .format-hint {
      margin-left: 4px;
      font-style: italic;
      opacity: 0.8;
    }
  }
}

/* 深色主题适配 */
.theme-dark {
  :deep(.readonly-input) {
    .el-input__inner {
      background-color: var(--base-item-bg, #2d3748) !important;
      color: var(--base-color-3, #a0aec0) !important;
      border-color: var(--border-color-1, #4a5568) !important;
    }
  }

  .material-id-hint {
    .mode-hint {
      &.auto {
        background-color: var(--base-item-bg, #1f2937);
        color: var(--current-color, #60a5fa);
        border-color: var(--current-color, #60a5fa);
      }

      &.manual {
        background-color: var(--base-item-bg, #374151);
        color: var(--base-warning-color, #fbbf24);
        border-color: var(--base-warning-color, #fbbf24);
      }
    }
  }
}
.app-container {
  padding: 20px;
  background: var(--base-body-background);
  min-height: calc(100vh - 84px);
  transition: all 0.3s ease;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color-1);

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--current-color);
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      margin-right: 10px;
      animation: bounce 2s infinite;
    }
  }

  .page-description {
    color: var(--base-color-3);
    margin: 0;
    font-size: 16px;
  }
}

.stats-section {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;

  .stat-card {
    border: 1px solid var(--border-color-1);
    transition: all 0.3s ease;
    background: var(--base-main-bg);
    position: relative;
    z-index: 2;

    &:hover {
      box-shadow: 0 4px 16px var(--tag-shadow-color-1);
      transform: translateY(-2px);
      z-index: 3;
    }

    .stat-item {
      display: flex;
      align-items: center;
      padding: 20px;
      position: relative;

      .stat-icon {
        font-size: 32px;
        color: var(--current-color);
        margin-right: 15px;

        i {
          display: block;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--current-color);
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--base-color-3);
        }
      }
    }
  }
}

.search-section {
  margin-bottom: 20px;
}

.operation-section {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;
  }
}

.table-section {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;

  .location-info {
    .zone-name {
      font-weight: 500;
      color: var(--theme-color);
    }

    .bin-code {
      font-size: 12px;
      color: var(--base-color-3);
      margin-top: 2px;
    }
  }

  .low-stock {
    color: #f56c6c;
    font-weight: 600;
  }

  .medium-stock {
    color: #e6a23c;
  }

  .high-stock {
    color: #67c23a;
  }

  .available-qty {
    color: var(--current-color);
    font-weight: 500;
  }

  .total-value {
    color: var(--current-color);
    font-weight: 500;
  }

  // 操作列按钮容器样式
  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;

    .el-button {
      margin: 0 !important;
      padding: 4px 8px !important;
      font-size: 12px !important;
      min-width: auto !important;
    }

    .el-dropdown {
      margin-left: 4px;
    }
  }

  .expired {
    color: #f56c6c;
    font-weight: 600;
  }

  .expiring-soon {
    color: #e6a23c;
    font-weight: 600;
  }

  // 表格合计行样式 - 已禁用
  // :deep(.el-table) {
  //   .el-table__footer-wrapper {
  //     position: relative !important;
  //     z-index: 10 !important;
  //     background: var(--base-main-bg) !important;
  //
  //     .el-table__footer {
  //       background: var(--base-main-bg) !important;
  //
  //       td.el-table__cell {
  //         background: var(--base-color-9) !important;
  //         color: var(--theme-color) !important;
  //         font-weight: 600 !important;
  //         border-top: 2px solid var(--current-color) !important;
  //         border-bottom: 1px solid var(--border-color-1) !important;
  //       }
  //     }
  //   }
  // }
}

.pagination-section {
  text-align: center;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// 表单样式适配
:deep(.el-form) {
  .el-form-item__label {
    color: var(--theme-color);
    font-weight: 500;
  }

  .el-input__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);

    &:focus {
      border-color: var(--current-color);
    }
  }

  .el-select .el-input__inner {
    background: var(--base-main-bg);
  }

  .el-textarea__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);

    &:focus {
      border-color: var(--current-color);
    }
  }

  .el-radio__label {
    color: var(--theme-color);
  }
}

// 表格样式适配
:deep(.el-table) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);

  th {
    background: var(--base-color-9);
    color: var(--theme-color);
    border-bottom-color: var(--border-color-1);
  }

  td {
    border-bottom-color: var(--border-color-1);
    color: var(--theme-color);
    background: var(--base-main-bg);
  }

  tr:hover td {
    background: var(--table-row-hover-bg);
  }

  .el-button--text {
    color: var(--current-color) !important;
    background: transparent !important;
    border: none !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;

    &:hover {
      color: var(--base-menu-color-active) !important;
      background-color: var(--current-color) !important;
      border-radius: 4px !important;
    }

    &:focus {
      color: var(--current-color) !important;
      background: transparent !important;
    }

    // 为不同类型的操作按钮设置不同颜色
    &.view-btn {
      color: var(--color-2) !important;

      &:hover {
        background-color: var(--color-2) !important;
        color: #fff !important;
      }
    }

    &.edit-btn {
      color: #e6a23c !important;

      &:hover {
        background-color: #e6a23c !important;
        color: #fff !important;
      }
    }

    &.move-btn {
      color: #909399 !important;

      &:hover {
        background-color: #909399 !important;
        color: #fff !important;
      }
    }

    &.trace-btn {
      color: #67c23a !important;

      &:hover {
        background-color: #67c23a !important;
        color: #fff !important;
      }
    }

    &.inbound-btn {
      color: #67c23a !important;

      &:hover {
        background-color: #67c23a !important;
        color: #fff !important;
      }
    }

    &.outbound-btn {
      color: #e6a23c !important;

      &:hover {
        background-color: #e6a23c !important;
        color: #fff !important;
      }
    }

    &.delete-btn {
      color: #f56c6c !important;

      &:hover {
        background-color: #f56c6c !important;
        color: #fff !important;
      }
    }
  }

  .el-table__footer {
    background: var(--base-color-9);
    color: var(--theme-color);
  }
}

// 卡片样式适配
:deep(.el-card) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);

  .el-card__header {
    background: var(--base-color-9);
    border-bottom-color: var(--border-color-1);
  }

  .el-card__body {
    background: var(--base-main-bg);
  }
}

// 弹窗样式适配
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);

  .el-dialog__title {
    color: var(--theme-color);
  }

  // 查看详情对话框样式
  .detail-content {
    padding: 20px;
    background: var(--base-main-bg);
    border-radius: 8px;

    .el-row {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .detail-item {
      padding: 12px 16px;
      background-color: var(--base-main-bg) !important;
      border-radius: 6px;
      border: 1px solid var(--border-color-1);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--current-color);
        box-shadow: 0 2px 8px var(--tag-shadow-color-1);
      }

      label {
        display: inline-block;
        font-weight: 600;
        color: var(--theme-color);
        min-width: 100px;
        margin-right: 12px;
        font-size: 14px;

        &::after {
          content: '';
          margin-left: 4px;
        }
      }

      span {
        color: var(--theme-color);
        font-size: 14px;
        font-weight: 500;

        &.stock-value {
          font-weight: 700;
          font-size: 16px;
          color: var(--current-color);
          background: linear-gradient(135deg, var(--current-color), #67c23a);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .el-tag {
        font-weight: 600;
        border-radius: 12px;
        padding: 4px 12px;
        font-size: 12px;
      }
    }

    // 分组标题
    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      margin-top: 25px;
      padding-bottom: 8px;
      border-bottom: 2px solid;

      &:first-child {
        margin-top: 0;
      }

      i {
        font-size: 18px;
        margin-right: 8px;
      }

      span {
        font-size: 16px;
        font-weight: 700;
        color: var(--theme-color);
      }

      &.material-section-title {
        border-color: var(--color-2);
        i { color: var(--color-2); }
      }

      &.warehouse-section-title {
        border-color: #f56c6c;
        i { color: #f56c6c; }
      }

      &.stock-section-title {
        border-color: #67c23a;
        i { color: #67c23a; }
      }

      &.time-section-title {
        border-color: #e6a23c;
        i { color: #e6a23c; }
      }

      &.source-section-title {
        border-color: var(--base-color-6);
        i { color: var(--base-color-6); }
      }
    }

    // 特殊样式区域
    .material-info {
      .detail-item {
        border-left: 4px solid var(--color-2);
        background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.05) 0%, var(--base-main-bg) 100%) !important;
      }
    }

    .warehouse-info {
      .detail-item {
        border-left: 4px solid #f56c6c;
        background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, var(--base-main-bg) 100%) !important;
      }
    }

    .stock-info {
      .detail-item {
        border-left: 4px solid #67c23a;
        background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, var(--base-main-bg) 100%) !important;
      }
    }

    .time-info {
      .detail-item {
        border-left: 4px solid #e6a23c;
        background: linear-gradient(135deg, rgba(230, 162, 60, 0.05) 0%, var(--base-main-bg) 100%) !important;
      }
    }

    .source-info {
      .detail-item {
        border-left: 4px solid var(--base-color-6);
        background: linear-gradient(135deg, rgba(204, 204, 204, 0.05) 0%, var(--base-main-bg) 100%) !important;
      }
    }
  }
}

// 对话框整体美化
:deep(.detail-dialog) {
  .el-dialog {
    background: var(--base-main-bg);
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, var(--current-color) 0%, #67c23a 100%);
    padding: 20px 30px;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: #fff;
      font-size: 18px;
      font-weight: 700;
    }

    .el-dialog__close {
      color: #fff;
      font-size: 20px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    background: var(--base-main-bg);
  }

  .el-dialog__footer {
    background: var(--base-color-9);
    border-radius: 0 0 8px 8px;
    padding: 15px 30px;
    text-align: center;

    .el-button {
      padding: 8px 20px;
      border-radius: 20px;
      font-weight: 600;
    }
  }
}

// 全局分页器样式适配
.el-pagination {
  .el-pagination__jump .el-input__inner {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-color: var(--border-color-1) !important;
  }
}

// 修复库存页面激活菜单悬浮bug
.inventory-detail .sidebar-container .el-menu .is-active:hover > .el-submenu__title,
.inventory-detail .sidebar-container .el-menu .el-submenu .is-active:hover {
  color: var(--base-menu-color-active) !important;
}

// 查看详情对话框美化样式
.detail-dialog .el-dialog {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

// 操作按钮样式
.el-table .el-button--text {
  padding: 4px 8px !important;
  margin: 0 2px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;

  &.view-btn {
    color: #409EFF !important;
    background: rgba(64, 158, 255, 0.1) !important;
    border-color: rgba(64, 158, 255, 0.2) !important;

    &:hover {
      background: #409EFF !important;
      color: #fff !important;
      border-color: #409EFF !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
    }
  }

  &.edit-btn {
    color: #E6A23C !important;
    background: rgba(230, 162, 60, 0.1) !important;
    border-color: rgba(230, 162, 60, 0.2) !important;

    &:hover {
      background: #E6A23C !important;
      color: #fff !important;
      border-color: #E6A23C !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3) !important;
    }
  }

  &.more-btn {
    color: var(--base-color-3) !important;
    background: rgba(144, 147, 153, 0.1) !important;
    border-color: rgba(144, 147, 153, 0.2) !important;

    &:hover {
      background: var(--base-menu-background-active) !important;
      color: var(--base-menu-color-active) !important;
      border-color: var(--current-color) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3) !important;
    }
  }

  &.inbound-btn {
    color: #67C23A !important;
    background: rgba(103, 194, 58, 0.1) !important;
    border-color: rgba(103, 194, 58, 0.2) !important;

    &:hover {
      background: #67C23A !important;
      color: #fff !important;
      border-color: #67C23A !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;
    }
  }

  &.outbound-btn {
    color: #E6A23C !important;
    background: rgba(230, 162, 60, 0.1) !important;
    border-color: rgba(230, 162, 60, 0.2) !important;

    &:hover {
      background: #E6A23C !important;
      color: #fff !important;
      border-color: #E6A23C !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3) !important;
    }
  }

  &.delete-btn {
    color: #F56C6C !important;
    background: rgba(245, 108, 108, 0.1) !important;
    border-color: rgba(245, 108, 108, 0.2) !important;

    &:hover {
      background: #F56C6C !important;
      color: #fff !important;
      border-color: #F56C6C !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;
    }
  }

  &.trace-btn {
    color: #67C23A !important;
    background: rgba(103, 194, 58, 0.1) !important;
    border-color: rgba(103, 194, 58, 0.2) !important;

    &:hover {
      background: #67C23A !important;
      color: #fff !important;
      border-color: #67C23A !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;
    }
  }

  i {
    margin-right: 4px !important;
    font-size: 12px !important;
  }
}

.detail-dialog {
  .detail-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    margin-bottom: 10px;
    background-color: var(--base-color-9) !important;
    border-radius: 6px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
    min-height: 40px;

    label {
      width: 90px;
      font-weight: 500;
      color: var(--sub-theme-color) !important;
      margin-right: 10px;
      flex-shrink: 0;
    }

    span, .el-tag {
      flex: 1;
      font-weight: 500;
      color: var(--theme-color) !important;
      word-break: break-all;
    }
  }

  .stock-value {
    font-size: 16px;
    font-weight: bold !important;
    color: var(--current-color) !important;
  }

  .material-info .detail-item { border-left-color: var(--color-2) !important; }
  .warehouse-info .detail-item { border-left-color: #f56c6c !important; }
  .stock-info .detail-item { border-left-color: #67c23a !important; }
  .time-info .detail-item { border-left-color: #e6a23c !important; }
  .source-info .detail-item { border-left-color: var(--base-color-6) !important; }

}

.form-help-text {
  font-size: 12px;
  color: var(--text-color-3);
  margin-top: 4px;
  line-height: 1.4;
}

.theme-light .inventory-detail .sidebar-container .el-menu-item.is-active:hover,
.theme-light .inventory-detail .sidebar-container .el-submenu.is-active > .el-submenu__title:hover {
    color: var(--base-menu-color-active) !important;
}

/* 移除全局样式中与 scoped 样式冲突的部分 */
/* .detail-dialog .el-dialog { ... } */
/*
  之前在这里可能存在一些全局的 .detail-dialog 样式，
  由于已经将它们移入 scoped style 并优化，
  这里的全局样式可以被安全地清理，以避免冲突和重复。
*/
.inventory-detail-page {
  /* ... 其他页面样式 ... */
}

</style>

<!-- 全局样式覆盖，确保主题适配正常工作 -->
<style lang="scss">
/* 详情弹窗美化样式 */
.detail-dialog .el-dialog {
  background-color: var(--base-main-bg) !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
}

.detail-dialog .el-dialog__body {
  background-color: var(--base-main-bg) !important;
}

.detail-dialog .el-dialog__header {
  background: linear-gradient(135deg, var(--current-color) 0%, #67c23a 100%) !important;
}

.detail-dialog .el-dialog__footer {
  background-color: var(--base-color-9) !important;
}

/* 弹窗和表单内元素主题适配 */
.el-dialog {
  background-color: var(--base-main-bg) !important;

  .el-form-item__label {
    color: var(--theme-color) !important;
  }

  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner,
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner,
  .el-input-number__decrease,
  .el-input-number__increase {
    background-color: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-color: var(--border-color-1) !important;
  }

  .el-input.is-disabled .el-input__inner {
    background-color: var(--base-color-9) !important;
    border-color: var(--border-color-1) !important;
    color: var(--base-color-6) !important;
  }

  .el-radio__label,
  .el-checkbox__label {
    color: var(--theme-color) !important;
  }
}

/* 统计区域确保不被覆盖 */
.inventory-detail .stats-section {
  position: relative !important;
  z-index: 10 !important;

  .stat-card {
    position: relative !important;
    z-index: 11 !important;
    background-color: var(--base-main-bg) !important;
  }
}

/* 表格合计行样式 - 已禁用 */
// .el-table__footer-wrapper {
//   position: relative !important;
//   z-index: 999 !important;
//   background-color: var(--base-main-bg) !important;
//
//   .el-table__footer {
//     background-color: var(--base-color-9) !important;
//
//     td.el-table__cell {
//       background-color: var(--base-color-9) !important;
//       color: var(--theme-color) !important;
//       font-weight: 600 !important;
//       border-top: 2px solid var(--current-color) !important;
//       position: relative !important;
//       z-index: 1000 !important;
//     }
//   }
// }

/* 表格和表单组件适配 */
.inventory-detail {
  .el-table {
    background-color: var(--base-main-bg) !important;

    th.el-table__cell {
      background-color: var(--base-color-9) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    td.el-table__cell {
      background-color: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    tr.el-table__row:hover > td.el-table__cell {
      background-color: var(--table-row-hover-bg) !important;
    }
  }

  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner {
    background-color: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-color: var(--border-color-1) !important;
  }

  .el-form-item__label {
    color: var(--theme-color) !important;
  }

  .el-button {
    &:not(.el-button--primary):not(.el-button--success):not(.el-button--danger):not(.el-button--warning):not(.el-button--info) {
      background-color: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;
    }
  }
}

/* 下拉菜单适配 */
.el-dropdown-menu {
  background: var(--base-main-bg) !important;
  border-color: var(--border-color-1) !important;

  .el-dropdown-menu__item {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;

    &:hover {
      background: var(--table-row-hover-bg) !important;
    }

    &.danger-item {
      color: #f56c6c !important;

      &:hover {
        background: rgba(245, 108, 108, 0.1) !important;
      }
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }
}

.el-select-dropdown {
  background-color: var(--base-main-bg) !important;
  border-color: var(--border-color-1) !important;

  .el-select-dropdown__item {
    color: var(--theme-color) !important;

    &.hover, &:hover {
      background-color: var(--table-row-hover-bg) !important;
    }

    &.selected {
      color: var(--current-color) !important;
      font-weight: bold;
    }

    /* 停用状态样式 */
    &.disabled-zone-option {
      background-color: #fef0f0 !important;
      border-left: 3px solid #f56c6c !important;
      opacity: 0.7;

      .disabled-text {
        color: #f56c6c !important;
        font-size: 12px;
        margin-left: 8px;
      }
    }

    &.disabled-warehouse-option {
      background-color: #fdf6ec !important;
      border-left: 3px solid #e6a23c !important;
      opacity: 0.8;

      .disabled-text {
        color: #e6a23c !important;
        font-size: 12px;
        margin-left: 8px;
      }
    }

    /* 暗色主题下的停用状态样式 */
    .theme-dark & {
      &.disabled-zone-option {
        background-color: rgba(245, 108, 108, 0.1) !important;
      }

      &.disabled-warehouse-option {
        background-color: rgba(230, 162, 60, 0.1) !important;
      }
    }
  }
}

/* 日期选择器适配 */
.el-picker-panel {
  background-color: var(--base-main-bg) !important;
  border-color: var(--border-color-1) !important;

  .el-date-table th,
  .el-date-picker__header-label,
  .el-date-table td span {
    color: var(--theme-color) !important;
  }

  .el-date-table td.available:hover {
    color: var(--current-color) !important;
  }

  .el-date-table td.current:not(.disabled) span {
    background-color: var(--current-color) !important;
    color: #fff !important;
  }

  .el-picker-panel__icon-btn {
    color: var(--theme-color) !important;
  }
}

// 界面其他部分保持原有样式
/* ... 其他原有全局样式 ... */

/* 表格居中对齐样式 */
.table-section {
  ::v-deep .el-table {
    th {
      text-align: center;
    }

    td {
      text-align: center;
    }

    .cell {
      text-align: center;
    }
  }
}

/* 区域选择样式 */
.mismatch-text {
  color: #e6a23c;
  font-size: 12px;
  margin-left: 4px;
}

.disabled-text {
  color: #c0c4cc;
  font-size: 12px;
  margin-left: 4px;
}

/* 弹窗表单优化样式 */
.el-form-item {
  .el-tooltip {
    .el-icon-info {
      cursor: help;
    }
  }
}

/* 新增字段样式 */
.text-muted {
  color: #909399;
  font-style: italic;
}

/* 产品编码列样式 */
.el-table .product-number-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* 系列和供应商列样式 */
.el-table .series-cell,
.el-table .supplier-cell {
  color: #606266;
}

/* 空值提示样式 */
.empty-value {
  color: #c0c4cc;
  font-style: italic;
}

/* 字段配置对话框样式 */
.column-config-dialog {
  .el-checkbox-group .el-checkbox {
    width: 100%;
    margin-right: 0;
  }

  .el-checkbox-group .el-checkbox + .el-checkbox {
    margin-left: 0;
  }

  .preview-container {
    border: 1px solid var(--border-color-1, #e4e7ed);
    border-radius: 4px;
    padding: 12px;
    background-color: var(--base-main-bg, #f5f7fa);
    max-height: 300px;
    overflow-y: auto;
  }

  .preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid var(--border-color-2, #ebeef5);

    &:last-child {
      border-bottom: none;
    }

    span:first-child {
      font-weight: 500;
      color: var(--theme-color, #303133);
    }
  }

  .preview-empty {
    text-align: center;
    color: var(--base-color-2, #909399);
    font-style: italic;
    padding: 20px 0;
  }

  h4 {
    margin: 0 0 12px 0;
    color: var(--theme-color, #303133);
    font-size: 14px;
    font-weight: 600;
  }
}
</style>


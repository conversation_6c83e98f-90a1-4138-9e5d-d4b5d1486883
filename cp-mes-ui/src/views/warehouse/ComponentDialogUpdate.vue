<template>
  <el-dialog 
    title="修改零件" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="零部件名称" prop="componentName" required>
        <el-input v-model="form.componentName" placeholder="请输入零部件名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType">
        <el-input v-model="form.materialType" placeholder="请输入物料类型" clearable />
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>
      
      <el-form-item label="保底库存" prop="minStockQuantity">
        <el-input-number v-model="form.minStockQuantity" :min="0" placeholder="请输入保底库存" />
      </el-form-item>
      
      <el-form-item label="存放区域" prop="regionId">
        <el-input v-model="form.regionId" placeholder="请输入存放区域" clearable />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateComponentWarehouse } from '@/api/jenasi/componentWarehouse';

export default {
  name: 'ComponentDialogUpdate',
  props: {
    value: Boolean,
    row: Object
  },
  data() {
    return {
      form: {
        componentId: '',
        componentName: '',
        materialType: '',
        currentStock: 0,
        minStockQuantity: 0,
        regionId: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    row: {
      handler(newRow) {
        if (newRow) {
          this.form = { ...newRow };
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          await updateComponentWarehouse(this.form);
          this.$message.success('修改成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('修改失败:', error);
        this.$message.error('修改失败，请重试');
      }
    }
  }
};
</script>
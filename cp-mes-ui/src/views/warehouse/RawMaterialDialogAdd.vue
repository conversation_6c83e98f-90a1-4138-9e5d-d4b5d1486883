<template>
  <el-dialog 
    title="添加原料" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="原料名称" prop="materialName" required>
        <el-input v-model="form.materialName" placeholder="请输入原料名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType" required>
        <el-select v-model="form.materialType" placeholder="请选择物料类型" clearable>
          <el-option label="原料" value="原料" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>
      
      <el-form-item label="保底库存" prop="minStockQuantity">
        <el-input-number v-model="form.minStockQuantity" :min="0" placeholder="请输入保底库存" />
      </el-form-item>
      
      <el-form-item label="存放区域" prop="regionId">
        <el-input v-model="form.regionId" placeholder="请输入存放区域" clearable />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addRawMaterialWarehouse } from '@/api/jenasi/rawMaterialWarehouse';

export default {
  name: 'RawMaterialDialogAdd',
  props: {
    value: Boolean
  },
  data() {
    return {
      form: {
        materialName: '',
        materialType: '原料',
        currentStock: 0,
        minStockQuantity: 0,
        regionId: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          const submissionData = {
            materialName: this.form.materialName,
            materialType: this.form.materialType || '原料',
            currentStock: this.form.currentStock || 0,
            minStockQuantity: this.form.minStockQuantity || 0,
            regionId: this.form.regionId || ''
          };
          
          console.log('提交新增原料数据:', submissionData);
          
          try {
            await addRawMaterialWarehouse(submissionData);
            this.$message.success('新增成功');
            this.$emit('ok');
            this.$emit('input', false);
          } catch (error) {
            // 处理主键冲突错误
            if (error.response && 
                (error.response.status === 500 || error.response.status === 400) && 
                error.response.data && error.response.data.message && error.response.data.message.includes('duplicate key')) {
              this.$message.error('添加失败：记录ID冲突，请联系管理员');
            } else if (error.response && error.response.data && error.response.data.message && error.response.data.message.includes('Unrecognized field')) {
              this.$message.error('添加失败：提交数据格式不符合要求');
              console.error('字段格式错误:', error.response.data.message);
            } else {
              throw error;
            }
          }
        }
      } catch (error) {
        console.error('新增失败:', error);
        const errorMessage = (error.response && error.response.data && error.response.data.message) || error.message || '未知错误';
        this.$message.error('新增失败: ' + errorMessage);
      }
    }
  }
};
</script>
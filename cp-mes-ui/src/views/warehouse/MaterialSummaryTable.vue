<template>
  <div>
    <el-form inline m-t-10>
      <el-form-item label="名称">
        <el-input v-model="searchQuery" placeholder="请输入物料名称" clearable />
      </el-form-item>
      <el-form-item label="物料类型">
        <el-select v-model="materialType" placeholder="请选择物料类型" clearable @change="handleSearch" 
          class="type-select" popper-class="type-select-dropdown">
          <el-option label="全部" value="" />
          <el-option label="原料" value="原料" />
          <el-option label="零部件" value="零部件" />
          <el-option label="初级半成品" value="初级半成品" />
          <el-option label="二级半成品" value="二级半成品" />
          <el-option label="成品" value="成品" />
        </el-select>
      </el-form-item>
      <el-form-item label="采购预警">
        <el-select v-model="warningStatus" placeholder="请选择" clearable @change="handleSearch" 
          class="status-select" popper-class="status-select-dropdown">
          <el-option label="全部" value="" />
          <el-option label="预警" value="true" />
          <el-option label="正常" value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <i class="el-icon-search"></i>
          查询
        </el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 筛选条件展示区域 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <span class="filter-label">已选筛选条件:</span>
      <el-tag v-if="searchQuery" closable @close="clearNameFilter" class="filter-tag">
        物料名称: {{ searchQuery }}
      </el-tag>
      <el-tag v-if="materialType" closable @close="clearTypeFilter" class="filter-tag" type="success">
        物料类型: {{ materialType }}
      </el-tag>
      <el-tag v-if="warningStatus" closable @close="clearWarningStatusFilter" class="filter-tag" type="warning">
        采购预警: {{ warningStatus === 'true' ? '预警' : '正常' }}
      </el-tag>
      <el-button size="small" type="info" text @click="resetSearch">清除全部</el-button>
    </div>

    <el-table 
      :data="tableData" 
      style="width: 100%" 
      @selection-change="handleSelectionChange" 
      :row-class-name="tableRowClassName"
              border
      highlight-current-row
      :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
      :cell-style="{padding:'12px 0'}"
      empty-text="暂无数据"
      v-loading="loading"
      fit>
      <el-table-column type="selection" width="55" />
      <el-table-column prop="materialId" label="物料ID" width="120" show-overflow-tooltip sortable align="center" />
      <el-table-column prop="materialName" label="物料名称" width="250" show-overflow-tooltip sortable align="center" />
      <el-table-column prop="materialType" label="物料类型" width="200" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          <el-tag size="small" :type="getTypeTagType(scope.row.materialType)">
            {{ scope.row.materialType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="currentStock" label="当前库存" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span :class="scope.row.currentStock <= (scope.row.minStockQuantity || 0) ? 'warning-status' : 'normal-status'">
            <i class="el-icon-box" style="margin-right: 4px;"></i>
            {{ scope.row.currentStock || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="inboundQuantity" label="入库数据" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #67c23a;">
            <i class="el-icon-bottom" style="margin-right: 4px;"></i>
            {{ scope.row.inboundQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="outboundQuantity" label="出库数据" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #e6a23c;">
            <i class="el-icon-top" style="margin-right: 4px;"></i>
            {{ scope.row.outboundQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="stockQuantity" label="库存台账" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #409eff; font-weight: 500;">
            <i class="el-icon-document" style="margin-right: 4px;"></i>
            {{ scope.row.stockQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="minStockQuantity" label="预警存量" min-width="120" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #f56c6c;">
            <i class="el-icon-warning" style="margin-right: 4px;"></i>
            {{ scope.row.minStockQuantity || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="needPurchase" label="采购状态" min-width="120" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.needPurchase ? 'danger' : 'success'" size="small" effect="dark">
            <i :class="scope.row.needPurchase ? 'el-icon-warning' : 'el-icon-circle-check'" style="margin-right: 4px;"></i>
            {{ scope.row.needPurchase ? '预警' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="regionId" label="存放区域" min-width="120" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          <span style="color: #606266;">
            <i class="el-icon-location" style="margin-right: 4px;"></i>
            {{ scope.row.regionId || '未设置' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="updatedTime" label="更新时间" min-width="160" align="center" sortable>
        <template slot-scope="scope">
          <span style="color: #909399;">
            <i class="el-icon-time" style="margin-right: 4px;"></i>
            {{ scope.row.updatedTime || '--' }}
          </span>
        </template>
      </el-table-column>
      <!-- 操作列已隐藏 - 仅保留查询功能 -->
      <!-- 
      <el-table-column label="操作" width="280" fixed="right" align="center">
        <template slot="header">
          <div style="display: flex; gap: 6px; justify-content: center; align-items: center;">
            <el-button size="mini" type="danger" @click="handleDelList" :disabled="selectRows.length == 0" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-delete" style="margin-right: 2px;"></i>
              删除已选
            </el-button>
            <el-button size="mini" type="primary" @click="dialogAddVisible = true" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-plus" style="margin-right: 2px;"></i>
              新增
            </el-button>
          </div>
        </template>
        <template slot-scope="scope">
          <div style="display: flex; gap: 6px; justify-content: center;">
            <el-button size="mini" type="primary" @click="handleUpdate(scope.row)" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-edit" style="margin-right: 2px;"></i>
              修改
            </el-button>
            <el-button size="mini" type="danger" @click="handleDel(scope.row)" style="padding: 5px 8px; font-size: 11px;">
              <i class="el-icon-delete" style="margin-right: 2px;"></i>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
      -->
    </el-table>

    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
      
    <!-- 添加对话框 -->
    <dialog-add v-model="dialogAddVisible" v-if="dialogAddVisible" @ok="fetchData"></dialog-add>
    
    <!-- 修改对话框 -->
    <dialog-update v-model="dialogUpdateVisible" v-if="dialogUpdateVisible" :row="operateRow"
      @ok="fetchData"></dialog-update>
  </div>
</template>

<script>
import { 
  pageMaterialSummary,
  addMaterialSummary,
  updateMaterialSummary,
  delMaterialSummary,
  delBatchMaterialSummary
} from '@/api/jenasi/materialSummary';
import dialogAdd from './dialogAdd.vue';
import dialogUpdate from './dialogUpdate.vue';

export default {
  name: 'MaterialSummaryTable',
  components: {
    dialogAdd,
    dialogUpdate
  },
  data() {
    return {
      searchQuery: '',
      materialType: '',
      warningStatus: '',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectRows: [],
      operateRow: null,
      dialogAddVisible: false,
      dialogUpdateVisible: false,
      loading: false
    };
  },
  computed: {
    hasActiveFilters() {
      return this.searchQuery || this.materialType || this.warningStatus;
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    // 清除单个筛选条件
    clearNameFilter() {
      this.searchQuery = '';
      this.handleSearch();
    },

    clearTypeFilter() {
      this.materialType = '';
      this.handleSearch();
    },

    clearWarningStatusFilter() {
      this.warningStatus = '';
      this.handleSearch();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchQuery = '';
      this.materialType = '';
      this.warningStatus = '';
      this.currentPage = 1;
      this.fetchData();
    },

    async fetchData() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          materialName: this.searchQuery || undefined,
          materialType: this.materialType || undefined,
          needPurchase: this.warningStatus || undefined
        };
        
        const response = await pageMaterialSummary(params);
        console.log('库存总览数据响应:', response);
        
        // 处理响应数据
        if (response && response.data) {
          const data = response.data;
          this.tableData = data.result || [];
          this.total = data.totalNumber || 0;
          
          // 确保每条记录都有正确的ID和格式化数据
          this.tableData = this.tableData.map((record, index) => {
            const recordCopy = { ...record };
            
            // 确保materialId存在
            if (!recordCopy.id && recordCopy.materialId) {
              recordCopy.id = recordCopy.materialId;
            } else if (!recordCopy.materialId && recordCopy.id) {
              recordCopy.materialId = recordCopy.id;
            } else if (!recordCopy.materialId && !recordCopy.id) {
              recordCopy.id = index + 1;
              recordCopy.materialId = index + 1;
              recordCopy._invalid = true;
            }
            
            // 确保数字字段的默认值
            recordCopy.currentStock = recordCopy.currentStock || 0;
            recordCopy.inboundQuantity = recordCopy.inboundQuantity || 0;
            recordCopy.outboundQuantity = recordCopy.outboundQuantity || 0;
            recordCopy.stockQuantity = recordCopy.stockQuantity || 0;
            recordCopy.minStockQuantity = recordCopy.minStockQuantity || 0;
            
            // 处理needPurchase字段
            if (recordCopy.needPurchase === "false" || recordCopy.needPurchase === false) {
              recordCopy.needPurchase = false;
            } else if (recordCopy.needPurchase === "true" || recordCopy.needPurchase === true) {
              recordCopy.needPurchase = true;
            } else {
              // 根据库存数量自动判断是否需要采购预警
              recordCopy.needPurchase = recordCopy.currentStock <= recordCopy.minStockQuantity;
            }
            
            // 确保物料类型字段存在
            if (!recordCopy.materialType) {
              recordCopy.materialType = '未分类';
            }
            
            // 确保物料名称字段存在
            if (!recordCopy.materialName) {
              recordCopy.materialName = '未命名物料';
            }
            
            return recordCopy;
          });
          
          console.log('库存总览数据加载成功，总数:', this.total, '数据:', this.tableData);
          
          // 验证数据格式
          if (this.tableData.length > 0) {
            console.log('第一条记录:', this.tableData[0]);
            console.log('数据字段检查:', {
              hasId: !!this.tableData[0].id,
              hasMaterialId: !!this.tableData[0].materialId,
              hasMaterialName: !!this.tableData[0].materialName,
              hasCurrentStock: this.tableData[0].currentStock !== undefined
            });
          }
        } else {
          console.warn('库存总览API返回数据格式异常:', response);
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取库存总览数据失败:', error);
        
        // 更详细的错误信息
        let errorMessage = '获取数据失败';
        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = '接口不存在，请联系管理员';
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误';
          } else {
            errorMessage = `请求失败: ${error.response.status}`;
          }
        } else if (error.request) {
          errorMessage = '网络连接失败，请检查网络';
        }
        
        this.$message.error(errorMessage);
        this.tableData = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },

    handleUpdate(row) {
      const rowCopy = JSON.parse(JSON.stringify(row));
      
      if (!rowCopy.materialId) {
        if (rowCopy.id) {
          console.warn('行数据缺少materialId，使用id:', rowCopy.id);
          rowCopy.materialId = rowCopy.id;
        } else {
          this.$message.error('该记录缺少有效ID，无法修改');
          return;
        }
      }
      
      this.operateRow = rowCopy;
      
      this.$nextTick(() => {
        this.dialogUpdateVisible = true;
      });
    },

    async handleDel(row) {
      try {
        const currentRow = JSON.parse(JSON.stringify(row));
        const materialId = currentRow.id || currentRow.materialId;
        
        if (!materialId) {
          this.$message.error('无法获取材料ID，删除失败');
          return;
        }
        
        await this.$confirm(
          `确认删除材料 "${currentRow.materialName || '未命名材料'}"？\nID: ${materialId}`, 
          '删除确认', 
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await delMaterialSummary(materialId);
        this.$message.success('删除成功');
        await this.fetchData();
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除');
        } else {
          console.error('删除失败:', error);
          this.$message.error('删除失败: ' + (error.message || '未知错误'));
        }
      }
    },

    handleSelectionChange(val) {
      this.selectRows = val;
    },

    async handleDelList() {
      if (this.selectRows.length === 0) {
        this.$message.warning('请先选择要删除的数据');
        return;
      }
      
      try {
        const selectedRows = this.selectRows.map(row => 
          JSON.parse(JSON.stringify(row))
        );
        
        const idsMap = selectedRows.map((row, index) => {
          const materialId = row.id || row.materialId;
          return { index, materialId, name: row.materialName };
        });
        
        const validEntries = idsMap.filter(entry => entry.materialId);
        
        if (validEntries.length < idsMap.length) {
          const missingIds = idsMap.filter(entry => !entry.materialId).map(entry => entry.index);
          this.$message.error(`部分记录(行 ${missingIds.join(', ')})缺少有效ID，无法删除`);
          return;
        }
        
        const idsToDelete = validEntries.map(entry => entry.materialId);
        const selectedNames = validEntries.map(entry => entry.name || '未命名材料').join('", "');
        
        await this.$confirm(
          `确认删除以下材料?\n材料名称: "${selectedNames}"\nID列表: [${idsToDelete.join(', ')}]`, 
          '批量删除确认', 
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await delBatchMaterialSummary(idsToDelete);
        this.$message.success('批量删除成功');
        await this.fetchData();
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消批量删除');
        } else {
          console.error('批量删除失败:', error);
          this.$message.error('批量删除失败: ' + (error.message || '未知错误'));
        }
      }
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.fetchData();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    },

    tableRowClassName({ row }) {
      if (row.currentStock <= (row.minStockQuantity || 0)) {
        return 'warning-row';
      }
      return '';
    },

    getTypeTagType(type) {
      switch (type) {
        case '原料':
          return 'success';
        case '零部件':
          return 'info';
        case '初级半成品':
          return 'warning';
        case '二级半成品':
          return 'warning';
        case '半成品':
          return 'warning';
        case '成品':
          return 'danger';
        default:
          return '';
      }
    }
  }
};
</script>

<style scoped>
/* 状态样式 */
.warning-status {
  color: #f56c6c;
  font-weight: bold;
}

.normal-status {
  color: #67c23a;
  font-weight: bold;
}

/* 添加下拉样式 */
:deep(.type-select),
:deep(.status-select) {
  width: 120px;
}

:deep(.type-select-dropdown),
:deep(.status-select-dropdown) {
  min-width: 120px;
}

/* 优化筛选条件标签样式 */
.active-filters {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #2b2b2b;
  color: #e0e0e0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.filter-label {
  margin-right: 12px;
  font-weight: 500;
  color: #c0c4cc;
}

.filter-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  color: #303133 !important;
}

/* 深色主题 - 表格行样式 */
.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  color: var(--theme-color) !important;
}

/* 表格优化样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        color: #303133;
        font-weight: 600;
        font-size: 14px;
        border: none;
        padding: 16px 12px;
        
        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  
  .el-table__body-wrapper {
    .el-table__body {
      tr {
        transition: all 0.3s ease;
        
        &:hover {
          background-color: #f8faff !important;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.el-table__row--striped {
          background-color: #fafbfc;
        }
        
        td {
          border: none;
          padding: 16px 12px;
          font-size: 13px;
          
          .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            
            &.el-tooltip {
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
  
  /* 预警行高亮 */
  .warning-row {
    background: linear-gradient(90deg, #fef0f0 0%, #fde2e2 100%) !important;
    
    &:hover {
      background: linear-gradient(90deg, #fde2e2 0%, #fecaca 100%) !important;
    }
  }
  
  /* 边框样式 */
  &.el-table--border {
    border: 1px solid #e4e7ed;
    
    &::after {
      display: none;
    }
    
    &::before {
      display: none;
    }
  }
  
  /* 空数据样式 */
  .el-table__empty-block {
    padding: 60px 0;
    
    .el-table__empty-text {
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 数字列居中对齐 */
:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  
  &.el-tag--success {
    background: linear-gradient(135deg, #67c23a, #85ce61);
    color: white;
  }
  
  &.el-tag--danger {
    background: linear-gradient(135deg, #f56c6c, #f78989);
    color: white;
  }
  
  &.el-tag--warning {
    background: linear-gradient(135deg, #e6a23c, #ebb563);
    color: white;
  }
  
  &.el-tag--info {
    background: linear-gradient(135deg, #909399, #a6a9ad);
    color: white;
  }
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.el-button--primary {
    background: linear-gradient(135deg, #409eff, #66b1ff);
    border: none;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);
    }
  }
  
  &.el-button--danger {
    background: linear-gradient(135deg, #f56c6c, #f78989);
    border: none;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(245, 108, 108, 0.4);
    }
  }
  
  &.el-button--small,
  &.el-button--mini {
    padding: 8px 15px;
    font-size: 12px;
    border-radius: 4px;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* 分页样式优化 */
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: center;
  
  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
    
    &.active {
      background: linear-gradient(135deg, #409eff, #66b1ff);
      color: white;
    }
  }
  
  .el-pagination__sizes .el-select .el-input .el-input__inner {
    border-radius: 4px;
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
}
</style>
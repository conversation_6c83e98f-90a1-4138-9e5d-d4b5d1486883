<template>
  <el-dialog 
    title="修改库存信息" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="物料名称" prop="materialName" required>
        <el-input v-model="form.materialName" placeholder="请输入物料名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType" required>
        <el-select v-model="form.materialType" placeholder="请选择物料类型" clearable>
          <el-option label="原料" value="原料" />
          <el-option label="零部件" value="零部件" />
          <el-option label="初级半成品" value="初级半成品" />
          <el-option label="二级半成品" value="二级半成品" />
          <el-option label="半成品" value="半成品" />
          <el-option label="成品" value="成品" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>
      
      <el-form-item label="入库数量" prop="inboundQuantity">
        <el-input-number v-model="form.inboundQuantity" :min="0" placeholder="请输入入库数量" />
      </el-form-item>
      
      <el-form-item label="出库数量" prop="outboundQuantity">
        <el-input-number v-model="form.outboundQuantity" :min="0" placeholder="请输入出库数量" />
      </el-form-item>
      
      <el-form-item label="库存台账" prop="stockQuantity">
        <el-input-number v-model="form.stockQuantity" :min="0" placeholder="请输入库存台账" />
      </el-form-item>
      
      <el-form-item label="预警存量" prop="minStockQuantity">
        <el-input-number v-model="form.minStockQuantity" :min="0" placeholder="请输入预警存量" />
      </el-form-item>
      
      <el-form-item label="需要采购" prop="needPurchase">
        <el-switch v-model="form.needPurchase" />
      </el-form-item>
      
      <el-form-item label="存放区域" prop="regionId">
        <el-input v-model="form.regionId" placeholder="请输入存放区域" clearable />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateMaterialSummary } from '@/api/jenasi/materialSummary';

export default {
  name: 'DialogUpdate',
  props: {
    value: Boolean,
    row: Object
  },
  data() {
    return {
      form: {
        materialId: '',
        materialName: '',
        materialType: '',
        currentStock: 0,
        inboundQuantity: 0,
        outboundQuantity: 0,
        stockQuantity: 0,
        minStockQuantity: 0,
        needPurchase: false,
        regionId: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    row: {
      handler(newRow) {
        if (newRow) {
          this.form = { ...newRow };
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          await updateMaterialSummary(this.form);
          this.$message.success('修改成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('修改失败:', error);
        this.$message.error('修改失败，请重试');
      }
    }
  }
};
</script> 
<template>
  <el-dialog 
    title="添加成品" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="成品名称" prop="productName" required>
        <el-input v-model="form.productName" placeholder="请输入成品名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType">
        <el-input v-model="form.materialType" placeholder="请输入物料类型" clearable />
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>

      <el-form-item label="功能类型" prop="styleId">
        <el-select v-model="form.styleId" placeholder="请选择功能类型" clearable>
          <el-option label="蓝牙款" :value="1" />
          <el-option label="无时款" :value="2" />
          <el-option label="按键款" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="系列类型" prop="seriesId">
        <el-select v-model="form.seriesId" placeholder="请选择系列类型" clearable>
          <el-option label="一体机系列" :value="1" />
          <el-option label="电脑编程系列" :value="2" />
        </el-select>
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addProductWarehouse } from '@/api/jenasi/productWarehouse';

export default {
  name: 'ProductDialogAdd',
  props: {
    value: Boolean
  },
  data() {
    return {
      form: {
        productName: '',
        materialType: '成品',
        currentStock: 0,
        styleId: null,
        seriesId: null,
        stockQuantity: 0,
        regionId: null
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          const submissionData = { 
            productName: this.form.productName,
            materialType: this.form.materialType || '成品',
            currentStock: this.form.currentStock || 0,
            styleId: this.form.styleId || null,
            seriesId: this.form.seriesId || null,
            stockQuantity: this.form.stockQuantity || 0,
            regionId: this.form.regionId
          };

          console.log('提交数据:', submissionData);
          await addProductWarehouse(submissionData);
          this.$message.success('新增成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('新增失败:', error);
        // 移除可选链操作符，使用传统方式访问嵌套属性
        const errorMessage = (error.response && error.response.data && error.response.data.message) || error.message || '未知错误';
        this.$message.error('新增失败，请重试: ' + errorMessage);
      }
    }
  }
};
</script>
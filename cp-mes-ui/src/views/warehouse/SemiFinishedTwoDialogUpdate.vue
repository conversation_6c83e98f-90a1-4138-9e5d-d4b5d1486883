<template>
  <el-dialog 
    title="修改二级半成品" 
    width="30%" 
    destroy-on-close 
    draggable
    :visible.sync="dialogVisible"
    @close="handleClose">
    <el-form :model="form" ref="formRef" :label-width="150" status-icon>
      <el-form-item label="二级半成品名称" prop="semiProductTwoName" required>
        <el-input v-model="form.semiProductTwoName" placeholder="请输入二级半成品名称" clearable />
      </el-form-item>
      
      <el-form-item label="物料类型" prop="materialType" required>
        <el-select v-model="form.materialType" placeholder="请选择物料类型" clearable>
          <el-option label="二级半成品" value="二级半成品" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="当前库存" prop="currentStock" required>
        <el-input-number v-model="form.currentStock" :min="0" placeholder="请输入库存数" />
      </el-form-item>
      
      <el-form-item label="当前步骤" prop="currentStep">
        <el-input v-model="form.currentStep" placeholder="请输入当前步骤" clearable />
      </el-form-item>
      
      <el-form-item label="处理状态" prop="processStatus">
        <el-select v-model="form.processStatus" placeholder="请选择处理状态" clearable>
          <el-option label="待处理" value="待处理" />
          <el-option label="处理中" value="处理中" />
          <el-option label="已完成" value="已完成" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="存放区域" prop="regionId">
        <el-input v-model="form.regionId" placeholder="请输入存放区域" clearable />
      </el-form-item>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateSemiFinishedProductTwo } from '@/api/jenasi/semiFinishedProductTwo';

export default {
  name: 'SemiFinishedTwoDialogUpdate',
  props: {
    value: Boolean,
    row: Object
  },
  data() {
    return {
      form: {
        semiProductTwoId: '',
        semiProductTwoName: '',
        materialType: '二级半成品',
        currentStock: 0,
        currentStep: '',
        processStatus: '',
        regionId: ''
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {
    row: {
      handler(newRow) {
        if (newRow) {
          this.form = { ...newRow };
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false);
    },

    async handleOk() {
      if (!this.$refs.formRef) return;
      
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          await updateSemiFinishedProductTwo(this.form);
          this.$message.success('修改成功');
          this.$emit('ok');
          this.$emit('input', false);
        }
      } catch (error) {
        console.error('修改失败:', error);
        this.$message.error('修改失败，请重试');
      }
    }
  }
};
</script> 
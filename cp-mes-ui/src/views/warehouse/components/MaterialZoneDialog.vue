<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="material-zone-dialog"
    custom-class="material-zone-dialog"
    :modal-append-to-body="false"
    top="10vh"
  >
    <!-- 物料基本信息 - 参考图片设计的简洁布局 -->
    <div class="material-info-section">
      <div class="material-info-title">
        <i class="el-icon-box"></i>
        物料基本信息
      </div>
      <div class="material-info-content">
        <div class="info-group">
          <div class="info-item">
            <span class="label">物料名称：</span>
            <span class="value primary">{{ materialInfo.materialName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">类型：</span>
            <el-tag :type="getMaterialTypeTag(materialInfo.materialType)" size="small">
              {{ getMaterialTypeLabel(materialInfo.materialType) }}
            </el-tag>
            <span class="material-id">{{ materialInfo.materialId || '' }}</span>
          </div>
        </div>
        <div class="info-group">
          <div class="info-item">
            <span class="label">总库存：</span>
            <span class="value highlight">{{ totalStock }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 区域分布表格 - 参考图片设计 -->
    <div class="zone-distribution-section">
      <div class="section-title">
        <i class="el-icon-location-outline"></i>
        区域分布详情
      </div>
      
      <div class="table-wrapper">
        <el-table
          v-loading="loading"
          :data="zoneList"
          stripe
          border
          style="width: 100%"
          max-height="260"
          class="zone-table"
          :header-cell-style="{background:'#fafbfc', color:'#303133', fontWeight: '500', fontSize: '14px', textAlign: 'center'}"
          :row-class-name="getRowClassName"
        >
          <!-- 区域ID字段已注释隐藏 -->
          <!-- <el-table-column prop="zoneId" label="区域ID" width="100" align="center">
            <template slot-scope="scope">
              <span class="zone-id">{{ scope.row.zoneId }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="zoneName" label="区域名称" min-width="140" align="center">
            <template slot-scope="scope">
              <div class="zone-name-cell">
                <i class="el-icon-location-outline"></i>
                <span>{{ scope.row.zoneName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="warehouseName" label="所属仓库" min-width="140" align="center" />
          <el-table-column prop="batchNo" label="批次号" width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.batchNo" class="batch-no">{{ scope.row.batchNo }}</span>
              <span v-else class="empty-text">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="currentStock" label="库存数量" width="100" align="center">
            <template slot-scope="scope">
              <span :class="getStockClass(scope.row)" class="stock-value">
                {{ scope.row.currentStock || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template slot-scope="scope">
              <el-tag 
                :type="getStatusTagType(scope.row.status)" 
                size="mini"
              >
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastUpdateTime" label="最近更新" width="140" align="center">
            <template slot-scope="scope">
              <span class="update-time">
                {{ formatDateTime(scope.row.lastUpdateTime) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="footer-summary">
        <span class="summary-text">共 {{ zoneList.length }} 个存储区域，总库存：{{ totalStock }}</span>
      </div>
      <div class="footer-actions">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" v-if="zoneList.length > 0">
          导出
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { listInventoryDetail } from '@/api/inventory/inventoryDetail'
import request from '@/utils/request'

export default {
  name: 'MaterialZoneDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    materialInfo: {
      type: Object,
      default: () => ({})
    },
    materialType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      zoneList: [],
      showPriceInfo: false // 控制是否显示价格相关信息
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    dialogTitle() {
      return `物料区域分布 - ${this.materialInfo.materialName || '未知物料'}`
    },
    totalStock() {
      return this.zoneList.reduce((sum, item) => sum + (item.currentStock || 0), 0)
    },
    totalValue() {
      return this.zoneList.reduce((sum, item) => sum + (item.totalValue || 0), 0)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.materialInfo.fields3) {
        this.fetchZoneData()
      }
    }
  },
  methods: {
    async fetchZoneData() {
      if (!this.materialInfo.fields3) {
        this.$message.warning('物料ID不能为空')
        return
      }

      try {
        this.loading = true
        console.log('查询参数:', {
          materialId: this.materialInfo.fields3,
          materialType: this.materialType || this.materialInfo.materialType
        })

        // 使用物料ID进行精确匹配查询，调用专门的区域分布接口
        const params = {
          materialId: this.materialInfo.fields3,
          materialType: this.materialType || this.materialInfo.materialType,
          pageNum: 1,
          pageSize: 999 // 获取所有数据
        }

        console.log('调用物料区域分布接口: /system/inventoryDetail/materialZoneDistribution')
        console.log('请求参数:', params)
        
        // 调用新的物料区域分布接口
        const response = await request({
          url: '/system/inventoryDetail/materialZoneDistribution',
          method: 'get',
          params: params
        })
        
        console.log('物料区域分布查询响应:', response)
        console.log('接口返回状态码:', response?.code)
        console.log('接口返回数据:', response?.data)

                  if (response && response.code === 200) {
            // 根据实际返回的数据结构进行适配
            const data = response.data || response
            console.log('接口返回的完整数据结构:', data)
            
            let inventoryList = data.records || data.rows || data.list || data || []
            console.log('原始库存明细列表:', inventoryList)
            console.log('原始数据数量:', inventoryList.length)
            
            if (inventoryList.length === 0) {
              console.log('接口返回空数据，检查是否有其他字段包含数据')
              console.log('data对象的所有属性:', Object.keys(data))
            }
            
            // 由于后端已通过materialId精确匹配，无需再次筛选
            console.log('后端已精确匹配的库存明细数据:', inventoryList)

                      this.zoneList = inventoryList.map(item => ({
              zoneId: item.zoneId,
                              zoneName: item.zoneName || (item.zoneId ? `区域${item.zoneId}` : '未指定区域'),
                warehouseName: item.warehouseName || '未指定仓库',
              batchNo: item.batchNo,
              currentStock: item.currentStock || 0,
              unitPrice: item.unitPrice,
              totalValue: (item.currentStock || 0) * (item.unitPrice || 0),
              status: item.status || 'normal',
              lastUpdateTime: item.updateTime || item.lastUpdateTime || item.createdTime,
              // 添加原始数据用于调试
              _originalData: item
            }))
            
            console.log('最终处理后的区域列表:', this.zoneList)

          // 如果有价格信息，显示价格列
          this.showPriceInfo = this.zoneList.some(item => item.unitPrice > 0)

          if (this.zoneList.length === 0) {
            this.$message.info(`未找到物料ID"${this.materialInfo.fields3}"的区域分布信息`)
          } else {
            console.log('成功获取区域分布数据，共', this.zoneList.length, '条记录')
          }
        } else {
          this.zoneList = []
          console.log('接口返回数据为空或格式异常:', response)
          this.$message.info('暂无该物料的区域分布信息')
        }
      } catch (error) {
        console.error('获取物料区域分布失败:', error)
        this.$message.error('获取物料区域分布失败: ' + (error.message || '未知错误'))
        this.zoneList = []
      } finally {
        this.loading = false
      }
    },

    getMaterialTypeTag(type) {
      const tagMap = {
        'raw_material': 'primary',
        'semi_finished': 'warning', 
        'semi_finished_two': 'warning',
        'product': 'success',
        'component': 'info'
      }
      return tagMap[type] || 'info'
    },

         getMaterialTypeLabel(type) {
       const labelMap = {
         'raw_material': '原料',
         'semi_finished': '一级半成品',
         'semi_finished_two': '二级半成品', 
         'product': '成品',
         'component': '零部件',
         '零部件': '零部件', // 添加中文匹配
         '原料': '原料',
         '一级半成品': '一级半成品',
         '二级半成品': '二级半成品',
         '成品': '成品'
       }
       return labelMap[type] || type || '未知'
     },

    getStockClass(row) {
      const stock = row.currentStock || 0
      if (stock <= 0) return 'zero-stock'
      if (stock <= (row.minStockQuantity || 0)) return 'low-stock'
      return 'normal-stock'
    },

    getStatusTagType(status) {
      const typeMap = {
        'normal': 'success',
        'warning': 'warning', 
        'danger': 'danger',
        'locked': 'info'
      }
      return typeMap[status] || 'success'
    },

    getStatusLabel(status) {
      const labelMap = {
        'normal': '正常',
        'warning': '预警',
        'danger': '异常', 
        'locked': '锁定'
      }
      return labelMap[status] || '正常'
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
    },

    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (column.property === 'currentStock') {
          sums[index] = data.reduce((sum, item) => sum + (item.currentStock || 0), 0)
        } else if (column.property === 'totalValue' && this.showPriceInfo) {
          sums[index] = '¥' + data.reduce((sum, item) => sum + (item.totalValue || 0), 0).toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },

    handleExport() {
      if (this.zoneList.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 使用CSV格式导出，确保兼容性
        this.exportToCSV()
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请检查浏览器是否支持文件下载')
      }
    },

    // 备用CSV导出方案
    exportToCSV() {
      const csvData = []
      
      // 添加物料基本信息
      csvData.push(['物料基本信息'])
      csvData.push(['物料名称', this.materialInfo.materialName || '-'])
      csvData.push(['物料类型', this.getMaterialTypeLabel(this.materialInfo.materialType)])
      csvData.push(['物料ID', this.materialInfo.materialId || '-'])
      csvData.push(['总库存', this.totalStock])
      csvData.push([]) // 空行
      
      // 添加区域分布数据标题
      csvData.push(['区域分布详情'])
      csvData.push(['序号', '区域名称', '所属仓库', '批次号', '库存数量', '状态', '最后更新'])
      
      // 添加区域分布数据
      this.zoneList.forEach((zone, index) => {
        csvData.push([
          index + 1,
          zone.zoneName,
          zone.warehouseName,
          zone.batchNo || '-',
          zone.currentStock || 0,
          this.getStatusLabel(zone.status),
          this.formatDateTime(zone.lastUpdateTime)
        ])
      })
      
      // 转换为CSV格式
      const csvContent = csvData.map(row => 
        row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
      ).join('\n')
      
      // 添加BOM头，确保中文正确显示
      const BOM = '\uFEFF'
      const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
      
      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      
      // 生成友好的文件名
      const now = new Date()
      const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`
      const timeStr = `${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`
      const fileName = `${this.materialInfo.materialName || '物料'}_区域分布_${dateStr}_${timeStr}.csv`
      
      link.setAttribute('href', url)
      link.setAttribute('download', fileName)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      this.$message.success(`导出成功：${fileName}`)
    },

    handleClose() {
      this.dialogVisible = false
      this.zoneList = []
      this.showPriceInfo = false
    },

    // 获取表格行样式类名
    getRowClassName({ row, rowIndex }) {
      const stock = row.currentStock || 0
      if (stock <= 0) return 'zero-stock-row'
      if (stock <= 10) return 'low-stock-row'
      if (rowIndex % 2 === 0) return 'even-row'
      return 'odd-row'
    }
  }
}
</script>

<style lang="scss" scoped>
// 高级毛玻璃卡片视图设计 - 全局隐藏滚动条
:deep(.material-zone-dialog) {
  /* 全局滚动条隐藏 */
  * {
    &::-webkit-scrollbar {
      width: 0px !important;
      height: 0px !important;
      background: transparent !important;
      display: none !important;
    }
    
    &::-webkit-scrollbar-thumb {
      display: none !important;
    }
    
    &::-webkit-scrollbar-track {
      display: none !important;
    }
    
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }
  .el-dialog {
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.8) 50%, 
      rgba(248, 250, 255, 0.85) 100%
    );
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-radius: 20px;
    border: 2px solid;
    border-image: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.6),
      rgba(var(--theme-color-rgb), 0.3),
      rgba(255, 255, 255, 0.4)
    ) 1;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(var(--theme-color-rgb), 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    overflow: hidden;
    position: relative;
    animation: dialogAppear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, 
        var(--theme-color),
        var(--current-color),
        var(--color-2),
        var(--theme-color)
      );
      background-size: 300% 100%;
      animation: shimmer 3s ease-in-out infinite;
    }
  }
  
  .el-dialog__header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 255, 0.9));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--theme-color);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 16px 20px;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: var(--theme-color);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .el-dialog__close {
      color: var(--base-color-2);
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      
      &:hover {
        color: var(--current-color);
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.1);
      }
    }
  }
  
  .el-dialog__body {
    padding: 16px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .el-dialog__footer {
    background: rgba(248, 250, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px 16px;
  }
}

.material-info-section {
  margin-bottom: 16px;
  padding: 16px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 255, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(var(--theme-color-rgb), 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out 0.1s both;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(255, 255, 255, 0.2), 
      transparent
    );
    transition: left 0.5s ease;
  }
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 
      0 12px 35px rgba(0, 0, 0, 0.15),
      0 4px 20px rgba(var(--theme-color-rgb), 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    border-color: rgba(var(--theme-color-rgb), 0.3);
    
    &::before {
      left: 100%;
    }
  }
  
  .material-info-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-color);
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--border-color-1);
    
    i {
      margin-right: 6px;
      font-size: 14px;
    }
  }
  
  .material-info-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
    
    .info-group {
      display: flex;
      gap: 20px;
      align-items: center;
      
      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .label {
          color: var(--base-color-2);
          font-size: 13px;
          font-weight: 500;
          white-space: nowrap;
        }
        
        .value {
          color: var(--theme-color);
          font-weight: 600;
          font-size: 13px;
          
          &.primary {
            color: var(--current-color);
            font-size: 14px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
          
          &.material-id {
            color: var(--base-color-3);
            font-size: 12px;
            margin-left: 6px;
            padding: 2px 6px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 4px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
          }
          
          &.highlight {
            color: var(--current-color);
            font-size: 16px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            padding: 4px 12px;
            background: linear-gradient(135deg, 
              rgba(var(--current-color-rgb), 0.15),
              rgba(var(--color-2-rgb), 0.1)
            );
            border-radius: 8px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(var(--current-color-rgb), 0.3);
            animation: float 2s ease-in-out infinite;
            position: relative;
            
            &::after {
              content: '✨';
              position: absolute;
              top: -5px;
              right: -5px;
              font-size: 10px;
              animation: sparkle 2s ease-in-out infinite;
            }
          }
        }
      }
    }
  }
}

.zone-distribution-section {
  .section-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-color);
    margin-bottom: 8px;
    
    i {
      margin-right: 6px;
      font-size: 14px;
    }
  }
  
  .table-wrapper {
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.95) 0%,
      rgba(250, 252, 255, 0.9) 100%
    );
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 
      0 6px 25px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(var(--theme-color-rgb), 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    animation: cardSlideIn 0.6s ease-out 0.3s both;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, 
        rgba(var(--theme-color-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--current-color-rgb), 0.02) 100%
      );
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      transform: translateY(-2px) scale(1.01);
      box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 4px 15px rgba(var(--theme-color-rgb), 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);
      border-color: rgba(var(--theme-color-rgb), 0.3);
      
      &::after {
        opacity: 1;
      }
    }
  }
  
  .zone-table {
    background: transparent;
    
    /* 彻底隐藏所有滚动条 */
    :deep(.el-table__body-wrapper),
    :deep(.el-table__header-wrapper),
    :deep(.el-scrollbar__wrap),
    :deep(.el-scrollbar) {
      &::-webkit-scrollbar {
        width: 0px !important;
        height: 0px !important;
        background: transparent !important;
        display: none !important;
      }
      
      &::-webkit-scrollbar-thumb {
        background: transparent !important;
        display: none !important;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent !important;
        display: none !important;
      }
      
      /* 兼容火狐浏览器 */
      scrollbar-width: none !important;
      -ms-overflow-style: none !important;
      overflow: -moz-scrollbars-none !important;
    }
    
    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th {
          background: rgba(248, 250, 255, 0.8) !important;
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
          color: #303133 !important;
          font-weight: 500;
          font-size: 14px;
          text-align: center;
          border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
    
    :deep(.el-table__body-wrapper) {
      .el-table__body {
        tr {
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          background: rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, 
              rgba(var(--theme-color-rgb), 0.1),
              rgba(var(--current-color-rgb), 0.1)
            );
            transition: width 0.3s ease;
            z-index: 1;
          }
          
          &:hover {
            background: rgba(255, 255, 255, 0.7) !important;
            transform: translateX(5px) scale(1.02);
            box-shadow: 
              0 4px 20px rgba(0, 0, 0, 0.1),
              0 2px 8px rgba(var(--theme-color-rgb), 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            
            &::before {
              width: 4px;
            }
            
            td {
              position: relative;
              z-index: 2;
            }
          }
          
          &:nth-child(even) {
            background: rgba(248, 250, 255, 0.4);
          }
          
          &.zero-stock-row {
            background: rgba(245, 108, 108, 0.15);
            animation: pulse 2s ease-in-out infinite;
            
            &::before {
              background: linear-gradient(90deg, 
                rgba(245, 108, 108, 0.3),
                rgba(245, 108, 108, 0.1)
              );
            }
          }
          
          &.low-stock-row {
            background: rgba(230, 162, 60, 0.15);
            animation: glow 3s ease-in-out infinite;
            
            &::before {
              background: linear-gradient(90deg, 
                rgba(230, 162, 60, 0.3),
                rgba(230, 162, 60, 0.1)
              );
            }
          }
          
          td {
            text-align: center;
            font-size: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px;
            backdrop-filter: inherit;
            -webkit-backdrop-filter: inherit;
            transition: all 0.3s ease;
            position: relative;
            
            &:hover {
              background: rgba(var(--theme-color-rgb), 0.05);
              border-radius: 6px;
              transform: scale(1.05);
            }
          }
        }
      }
    }
  }
  
  /* 区域ID样式已注释（字段已隐藏）
  .zone-id {
    color: var(--theme-color);
    font-weight: 600;
    font-size: 13px;
  }
  */
  
  .zone-name-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      color: var(--current-color);
      margin-right: 6px;
      font-size: 14px;
    }
    
    span {
      color: var(--theme-color);
      font-weight: 500;
    }
  }
  
  .batch-no {
    color: var(--theme-color);
    font-weight: 500;
    font-size: 13px;
  }
  
  .empty-text {
    color: var(--base-color-3);
    font-size: 12px;
  }
  
  .stock-value {
    font-weight: 600;
    font-size: 14px;
    
    &.zero-stock {
      color: #f56c6c;
    }
    
    &.low-stock {
      color: #e6a23c;
    }
    
    &.normal-stock {
      color: var(--current-color);
    }
  }
  
  .update-time {
    color: var(--base-color-3);
    font-size: 12px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .footer-summary {
    color: var(--base-color-3);
    font-size: 13px;
    
    .summary-text {
      color: var(--theme-color);
      font-weight: 500;
    }
  }
  
  .footer-actions {
    display: flex;
    gap: 12px;
    
    .el-button {
      padding: 12px 24px;
      border-radius: 12px;
      font-weight: 600;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.4);
      color: var(--theme-color);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, 
          transparent, 
          rgba(255, 255, 255, 0.3), 
          transparent
        );
        transition: left 0.5s ease;
      }
      
      &:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 
          0 8px 25px rgba(0, 0, 0, 0.15),
          0 4px 12px rgba(var(--theme-color-rgb), 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 1);
        border-color: rgba(var(--theme-color-rgb), 0.4);
        
        &::before {
          left: 100%;
        }
      }
      
      &.el-button--primary {
        background: linear-gradient(135deg, 
          var(--theme-color) 0%, 
          var(--current-color) 50%,
          var(--color-2) 100%
        );
        background-size: 200% 200%;
        color: white;
        border: none;
        box-shadow: 
          0 4px 15px rgba(var(--theme-color-rgb), 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        animation: gradientShift 3s ease infinite;
        
        &::before {
          background: linear-gradient(90deg, 
            transparent, 
            rgba(255, 255, 255, 0.2), 
            transparent
          );
        }
        
        &:hover {
          background: linear-gradient(135deg, 
            var(--current-color) 0%, 
            var(--color-2) 50%,
            var(--theme-color) 100%
          );
          transform: translateY(-4px) scale(1.08);
          box-shadow: 
            0 12px 35px rgba(var(--theme-color-rgb), 0.4),
            0 6px 20px rgba(var(--current-color-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

/* 毛玻璃主题适配 */
:deep(.el-tag) {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  color: var(--theme-color) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &.el-tag--success {
    background: rgba(103, 194, 58, 0.2) !important;
    color: #67c23a !important;
  }
  
  &.el-tag--warning {
    background: rgba(230, 162, 60, 0.2) !important;
    color: #e6a23c !important;
  }
  
  &.el-tag--danger {
    background: rgba(245, 108, 108, 0.2) !important;
    color: #f56c6c !important;
  }
  
  &.el-tag--info {
    background: rgba(144, 147, 153, 0.2) !important;
    color: #909399 !important;
  }
  
  &.el-tag--primary {
    background: rgba(var(--theme-color-rgb), 0.2) !important;
    color: var(--theme-color) !important;
  }
}

:deep(.el-table) {
  background: transparent !important;
  border: none !important;
  
  th, td {
    background: transparent !important;
    border: none !important;
  }
  
  &:before {
    display: none;
  }
  
  &:after {
    display: none;
  }
}

/* 毛玻璃背景动画 */
.material-zone-dialog {
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
      rgba(74, 144, 226, 0.05), 
      rgba(80, 227, 194, 0.05),
      rgba(255, 107, 107, 0.05)
    );
    animation: gradient 15s ease infinite;
    pointer-events: none;
    z-index: -1;
  }
}

/* 动画关键帧 */
@keyframes dialogAppear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
    filter: blur(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -300% 0;
  }
  100% {
    background-position: 300% 0;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--theme-color-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--theme-color-rgb), 0.6);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .material-info-section {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 10px;
    
    .footer-summary {
      width: 100%;
      justify-content: center;
    }
    
    .footer-actions {
      width: 100%;
      justify-content: center;
    }
  }
}
</style> 
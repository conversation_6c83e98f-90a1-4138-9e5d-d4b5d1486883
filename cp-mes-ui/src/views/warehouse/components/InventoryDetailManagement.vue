<template>
  <div class="inventory-detail-management">
    <!-- 统计卡片 -->
    <!-- <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalItems || 0 }}</div>
              <div class="stat-label">总库存项目</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalQuantity || 0 }}</div>
              <div class="stat-label">总库存数量</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalValue || 0 }}</div>
              <div class="stat-label">总库存价值</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.lowStockItems || 0 }}</div>
              <div class="stat-label">低库存预警</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div> -->

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="物料名称">
          <el-input
            v-model="searchForm.materialName"
            placeholder="请输入物料名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="物料类型">
          <el-select
            v-model="searchForm.materialType"
            placeholder="请选择物料类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in materialTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select
            v-model="searchForm.zoneCode"
            placeholder="请选择区域"
            clearable
            style="width: 150px"
            @change="handleZoneChange"
          >
            <el-option
              v-for="zone in zoneOptions"
              :key="zone.value"
              :label="zone.label"
              :value="zone.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="批次号">
          <el-input
            v-model="searchForm.batchNo"
            placeholder="请输入批次号"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="库存状态" prop="status">
            <el-select v-model="searchForm.status" placeholder="请选择库存状态" clearable style="width: 150px">
                <el-option label="正常" value="1"></el-option>
                <el-option label="异常" value="0"></el-option>
                <el-option label="过期" value="2"></el-option>
                <el-option label="待处理" value="3"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="handleExport">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 库存明细表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        show-summary
        :summary-method="getSummaries"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="materialId" label="物料ID" width="100" />
        <el-table-column prop="materialName" label="物料名称" min-width="150" />
        <el-table-column prop="materialType" label="物料类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getMaterialTypeTag(scope.row.materialType)">
              {{ getMaterialTypeLabel(scope.row.materialType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="batchNo" label="批次号" width="120" />
        <el-table-column label="存储区域" width="150">
          <template slot-scope="scope">
            <div class="zone-name">{{ scope.row.zoneName }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="currentStock" label="当前库存" width="100" align="right">
          <template slot-scope="scope">
            <span :class="getStockClass(scope.row)">
              {{ scope.row.currentStock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="inboundQuantity" label="入库数量" width="100" align="right" />
        <el-table-column prop="outboundQuantity" label="出库数量" width="100" align="right" />
        <el-table-column prop="stockQuantity" label="结存数量" width="100" align="right" />
        <el-table-column prop="minStockQuantity" label="最低库存" width="100" align="right">
          <template slot-scope="scope">
            <span :class="scope.row.currentStock <= scope.row.minStockQuantity ? 'text-danger' : ''">
              {{ scope.row.minStockQuantity }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="needPurchase" label="需要采购" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.needPurchase === 'true' ? 'danger' : 'success'">
              {{ scope.row.needPurchase === 'true' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="unitPrice" label="单价" width="100" align="right">
          <template slot-scope="scope">
            ¥{{ (scope.row.unitPrice || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalValue" label="总价值" width="120" align="right">
          <template slot-scope="scope">
            ¥{{ ((scope.row.currentStock || 0) * (scope.row.unitPrice || 0)).toFixed(2) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="productionDate" label="生产日期" width="120" />
        <!-- <el-table-column prop="expiryDate" label="到期日期" width="120">
          <template slot-scope="scope">
            <span :class="getExpiryClass(scope.row.expiryDate)">
              {{ scope.row.expiryDate }}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="160" />
        <el-table-column label="操作" width="180" fixed="right" v-if="showActions">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="view-btn" @click="handleView(scope.row)">
              <i class="el-icon-view"></i>
              查看
            </el-button>
            <el-button type="text" size="small" class="edit-btn" @click="handleAdjust(scope.row)">
              <i class="el-icon-edit-outline"></i>
              调整
            </el-button>
            <el-button type="text" size="small" class="move-btn" @click="handleMove(scope.row)">
              <i class="el-icon-position"></i>
              移库
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 库存调整弹窗 -->
    <el-dialog
      title="库存调整"
      :visible.sync="adjustDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :z-index="4000"
    >
      <el-form
        ref="adjustForm"
        :model="adjustFormData"
        :rules="adjustFormRules"
        label-width="120px"
      >
        <el-form-item label="物料名称">
          <el-input v-model="adjustFormData.materialName" disabled />
        </el-form-item>
        <el-form-item label="当前库存">
          <el-input v-model="adjustFormData.currentQuantity" disabled />
        </el-form-item>
        <el-form-item label="调整类型" prop="adjustType">
          <el-radio-group v-model="adjustFormData.adjustType">
            <el-radio label="increase">增加</el-radio>
            <el-radio label="decrease">减少</el-radio>
            <el-radio label="set">设置</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量" prop="adjustQuantity">
          <el-input-number
            v-model="adjustFormData.adjustQuantity"
            :min="adjustFormData.adjustType === 'decrease' ? -adjustFormData.currentQuantity : 0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="adjustReason">
          <el-select
            v-model="adjustFormData.adjustReason"
            placeholder="请选择调整原因"
            style="width: 100%"
          >
            <el-option label="盘点调整" value="inventory" />
            <el-option label="损耗调整" value="loss" />
            <el-option label="报废调整" value="scrap" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="adjustFormData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入调整备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adjustDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAdjustSubmit" :loading="adjustLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 移库弹窗 -->
    <el-dialog
      title="库存移库"
      :visible.sync="moveDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <el-form
        ref="moveForm"
        :model="moveFormData"
        :rules="moveFormRules"
        label-width="120px"
      >
        <el-form-item label="物料名称">
          <el-input v-model="moveFormData.materialName" disabled />
        </el-form-item>
        <el-form-item label="当前位置">
          <el-input v-model="moveFormData.currentLocation" disabled />
        </el-form-item>
        <el-form-item label="可移数量">
          <el-input v-model="moveFormData.availableStock" disabled />
        </el-form-item>
        <el-form-item label="目标区域" prop="targetZoneId">
          <el-select
            v-model="moveFormData.targetZoneId"
            placeholder="请选择目标区域"
            style="width: 100%"
            @change="handleTargetZoneChange"
          >
            <el-option
              v-for="zone in availableZoneOptions"
              :key="zone.zoneId"
              :label="zone.zoneName"
              :value="zone.zoneId"
            />
          </el-select>
          <!-- 物料区域兼容性提示 -->
          <div v-if="moveFormData.targetZoneId && moveFormData.isCompatible !== null" class="compatibility-tip">
            <el-alert
              v-if="moveFormData.isCompatible"
              title="物料与目标区域兼容"
              type="success"
              :closable="false"
              show-icon
              size="small"
            />
            <el-alert
              v-else
              title="物料与目标区域不兼容，请检查区域存储规则或选择其他区域"
              type="warning"
              :closable="false"
              show-icon
              size="small"
            />
          </div>
        </el-form-item>
        <el-form-item label="目标货位" prop="targetBinId">
          <el-select
            v-model="moveFormData.targetBinId"
            placeholder="请选择目标货位"
            style="width: 100%"
          >
            <el-option
              v-for="bin in targetBinOptions"
              :key="bin.binId"
              :label="bin.binCode"
              :value="bin.binId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="移库数量" prop="moveQuantity">
          <el-input-number
            v-model="moveFormData.moveQuantity"
            :min="1"
            :max="moveFormData.availableStock"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="移库原因" prop="moveReason">
          <el-input
            v-model="moveFormData.moveReason"
            type="textarea"
            :rows="3"
            placeholder="请输入移库原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="moveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleMoveSubmit" :loading="moveLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 父组件(区域管理)的库存明细弹窗 -->
    <el-dialog
      :title="`库存明细 - ${selectedZone.zoneName}`"
      :visible.sync="inventoryDialogVisible"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
    >
      <inventory-detail-management v-if="inventoryDialogVisible" :zone-id="selectedZone.zoneId" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goToInventoryDetailPage">前往库存明细页面</el-button>
        <el-button @click="inventoryDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listInventoryDetail, 
  getInventoryDetailByZoneCode,
  getInventoryStatistics, 
  adjustInventoryDetail, 
  moveInventoryDetail,
  validateMaterialZoneCompatibility,
  moveInventoryStock
} from '@/api/inventory/inventoryDetail'
import { listZone } from '@/api/inventory/zone'

export default {
  name: 'InventoryDetailManagement',
  props: {
    zoneId: {
      type: [String, Number],
      default: null
    },
    zoneCode: {
      type: String,
      default: ''
    },
    zoneName: {
      type: String,
      default: ''
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        materialName: '',
        materialType: '',
        zoneCode: '', // 改为使用zoneCode
        batchNo: '',
        status: ''
      },
      materialTypeOptions: [
        { label: '原料', value: 'raw_material' },
        { label: '半成品', value: 'semi_finished' },
        { label: '成品', value: 'product' },
        { label: '零件', value: 'component' }
      ],
      zoneOptions: [],
      tableData: [],
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      statistics: {
        totalItems: 0,
        totalStock: 0,
        totalValue: 0,
        lowStockItems: 0
      },
      adjustDialogVisible: false,
      adjustLoading: false,
      adjustFormData: {
        detailId: '',
        materialName: '',
        currentStock: 0,
        adjustType: 'increase',
        adjustQuantity: 0,
        adjustReason: '',
        remark: ''
      },
      adjustFormRules: {
        adjustType: [
          { required: true, message: '请选择调整类型', trigger: 'change' }
        ],
        adjustQuantity: [
          { required: true, message: '请输入调整数量', trigger: 'blur' }
        ],
        adjustReason: [
          { required: true, message: '请选择调整原因', trigger: 'change' }
        ]
      },
      moveDialogVisible: false,
      moveLoading: false,
      moveFormData: {
        detailId: '',
        materialId: '',
        materialType: '',
        materialName: '',
        currentLocation: '',
        availableStock: 0,
        targetZoneId: '',
        moveQuantity: 0,
        moveReason: '',
        currentZoneId: '',
        isCompatible: null
      },
      moveFormRules: {
        targetZoneId: [
          { required: true, message: '请选择目标区域', trigger: 'change' }
        ],
        moveQuantity: [
          { required: true, message: '请输入移库数量', trigger: 'blur' }
        ],
        moveReason: [
          { required: true, message: '请输入移库原因', trigger: 'blur' }
        ]
      },
      inventoryDialogVisible: false,
      selectedZone: {}
    };
  },
  computed: {
    // 过滤后的可用区域选项（排除当前区域）
    availableZoneOptions() {
      if (!this.moveFormData.currentZoneId) {
        return this.zoneOptions;
      }
      return this.zoneOptions.filter(zone => zone.zoneId !== this.moveFormData.currentZoneId);
    }
  },
  watch: {
    zoneId: {
      handler(newVal) {
        if (newVal) {
          this.searchForm.zoneCode = newVal; // 兼容旧属性，但使用zoneCode查询
          this.fetchData(); // 重新获取数据
        }
      },
      immediate: true // 立即执行一次，替代 mounted 中的逻辑
    },
    zoneCode: {
      handler(newVal) {
        if (newVal) {
          this.searchForm.zoneCode = newVal;
          this.fetchData(); // 重新获取数据
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.fetchData();
    this.fetchStatistics();
    this.fetchZoneOptions();
  },
  methods: {
    // 获取库存明细数据
    async fetchData() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          materialName: this.searchForm.materialName,
          materialType: this.searchForm.materialType,
          zoneCode: this.searchForm.zoneCode, // 使用zoneCode
          batchNo: this.searchForm.batchNo,
          status: this.searchForm.status
        };
        
        const response = await listInventoryDetail(params);
        if (response && response.code === 200) {
          this.tableData = response.rows || [];
          this.pagination.total = response.total || 0;
        } else {
          this.$message.error(response.message || '获取库存明细数据失败');
          this.tableData = [];
          this.pagination.total = 0;
        }
      } catch (error) {
        console.error('获取库存明细数据失败:', error);
        this.$message.error('获取库存明细数据失败');
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        this.loading = false;
      }
    },
    
    // 获取统计数据
    async fetchStatistics() {
      try {
        const response = await getInventoryStatistics(this.searchForm);
        if (response && response.code === 200) {
          this.statistics = response.data || {
            totalItems: 0,
            totalStock: 0,
            totalValue: 0,
            lowStockItems: 0
          };
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
        this.statistics = {
          totalItems: 0,
          totalStock: 0,
          totalValue: 0,
          lowStockItems: 0
        };
      }
    },
    
    // 获取区域选项
    async fetchZoneOptions() {
      try {
        const response = await listZone({});
        if (response && response.code === 200) {
          this.zoneOptions = (response.rows || []).map(zone => ({
            value: zone.zoneCode, // 使用zoneCode
            label: zone.zoneName || `区域编码-${zone.zoneCode}`, // 确保有可显示的名称
            zoneCode: zone.zoneCode // 保留zoneCode
          }));
          console.log('InventoryDetailManagement - 获取到的区域选项:', this.zoneOptions);
        } else {
          console.warn('InventoryDetailManagement - 获取区域选项响应异常:', response);
          this.zoneOptions = [];
        }
      } catch (error) {
        console.error('InventoryDetailManagement - 获取区域选项失败:', error);
        this.zoneOptions = [];
      }
    },
    
    // 区域变化
    handleZoneChange() {
      this.searchForm.binId = '';
      // 根据区域获取货位列表
      this.fetchBinOptions(this.searchForm.zoneCode);
    },
    
    // 获取货位选项
    fetchBinOptions(zoneId) {
      if (!zoneId) {
        this.binOptions = [];
        return;
      }
      // 模拟货位数据
      this.binOptions = [
        { binId: 1, binCode: 'A01-001' },
        { binId: 2, binCode: 'A01-002' },
        { binId: 3, binCode: 'A01-003' }
      ];
    },
    
    // 目标区域变化
    async handleTargetZoneChange() {
      this.moveFormData.targetBinId = '';
      this.fetchTargetBinOptions(this.moveFormData.targetZoneId);
      
      // 验证物料是否适合存放在目标区域
      if (this.moveFormData.targetZoneId && this.moveFormData.materialId && this.moveFormData.materialType) {
        await this.validateZoneCompatibility();
      }
    },
    
    // 获取目标货位选项
    fetchTargetBinOptions(zoneId) {
      if (!zoneId) {
        this.targetBinOptions = [];
        return;
      }
      // 模拟目标货位数据
      this.targetBinOptions = [
        { binId: 4, binCode: 'B01-001' },
        { binId: 5, binCode: 'B01-002' },
        { binId: 6, binCode: 'B01-003' }
      ];
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.fetchData();
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        materialName: '',
        materialType: '',
        zoneCode: this.zoneCode || this.zoneId || '', // 优先使用zoneCode，兼容zoneId
        batchNo: '',
        status: ''
      };
      this.fetchZoneOptions();
      this.handleSearch();
    },
    
    // 导出
    handleExport() {
      this.$message.success('导出成功');
    },
    
    // 查看详情
    handleView(row) {
      console.log('查看库存明细:', row);
    },
    
    // 库存调整
    handleAdjust(row) {
      this.adjustFormData = {
        detailId: row.detailId,
        materialName: row.materialName,
        currentStock: row.currentStock,
        adjustType: 'increase',
        adjustQuantity: 0,
        adjustReason: '',
        remark: ''
      };
      this.adjustDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.adjustForm) {
          this.$refs.adjustForm.clearValidate();
        }
      });
    },
    
    // 提交调整
    async handleAdjustSubmit() {
      this.$refs.adjustForm.validate(async (valid) => {
        if (!valid) {
          return;
        }
        
        try {
          this.adjustLoading = true;
          const response = await adjustInventoryDetail(this.adjustFormData);
          if (response && response.code === 200) {
            this.$message.success('库存调整成功');
            this.adjustDialogVisible = false;
            this.fetchData();
            this.fetchStatistics();
          } else {
            this.$message.error(response.message || '库存调整失败');
          }
        } catch (error) {
          console.error('库存调整失败:', error);
          this.$message.error('库存调整失败');
        } finally {
          this.adjustLoading = false;
        }
      });
    },
    
    // 移库
    handleMove(row) {
      this.moveFormData = {
        detailId: row.detailId,
        materialId: row.materialId,
        materialType: row.materialType,
        materialName: row.materialName,
        currentLocation: `${row.zoneName}`,
        availableStock: row.currentStock || 0,
        targetZoneId: '',
        moveQuantity: 1,
        moveReason: '',
        currentZoneId: row.zoneId,
        isCompatible: null
      };
      
      this.moveDialogVisible = true;
      
      // 在打开对话框时重新获取区域选项
      this.fetchZoneOptions();
      
      this.$nextTick(() => {
        if (this.$refs.moveForm) {
          this.$refs.moveForm.clearValidate();
        }
      });
    },
    
    // 验证物料区域兼容性
    async validateZoneCompatibility() {
      try {
        const params = {
          materialId: this.moveFormData.materialId,
          materialType: this.moveFormData.materialType,
          zoneId: this.moveFormData.targetZoneId,
          batchNo: this.moveFormData.batchNo
        };
        
        const response = await validateMaterialZoneCompatibility(params);
        if (response.code === 200) {
          this.moveFormData.isCompatible = response.data;
          
          if (!response.data) {
            this.$message.warning('物料与目标区域不兼容，请重新选择区域或查看区域存储规则');
          } else {
            this.$message.success('物料与目标区域兼容');
          }
        }
      } catch (error) {
        console.error('验证物料区域兼容性失败:', error);
        this.$message.error('验证失败，请稍后重试');
        this.moveFormData.isCompatible = null;
      }
    },

    // 移库提交
    async handleMoveSubmit() {
      this.$refs.moveForm.validate(async (valid) => {
        if (valid) {
          // 移库前再次验证兼容性
          if (this.moveFormData.targetZoneId && this.moveFormData.isCompatible === null) {
            await this.validateZoneCompatibility();
          }
          
          // 如果不兼容，询问用户是否强制移库
          if (this.moveFormData.isCompatible === false) {
            try {
              await this.$confirm('物料与目标区域不兼容，是否强制移库？', '兼容性警告', {
                confirmButtonText: '强制移库',
                cancelButtonText: '取消',
                type: 'warning'
              });
            } catch {
              return; // 用户取消
            }
          }
          
          this.moveLoading = true;
          try {
            const params = {
              detailId: this.moveFormData.detailId,
              targetZoneId: this.moveFormData.targetZoneId,
              moveQuantity: this.moveFormData.moveQuantity,
              moveReason: this.moveFormData.moveReason
            };
            
            await moveInventoryStock(params);
            this.$message.success('移库成功');
            this.moveDialogVisible = false;
            this.fetchData(); // 刷新数据
          } catch (error) {
            console.error('移库失败:', error);
            this.$message.error('移库失败：' + error.message);
          } finally {
            this.moveLoading = false;
          }
        }
      });
    },
    
    // 表格选择
    handleSelectionChange(val) {
      this.selectedRows = val;
    },
    
    // 分页
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },
    
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchData();
    },
    
    // 获取物料类型标签样式
    getMaterialTypeTag(type) {
      const tagMap = {
        'raw_material': 'primary',
        'semi_finished': 'warning',
        'semi_finished_two': 'warning',
        'product': 'success',
        'component': 'info'
      };
      return tagMap[type] || 'info';
    },
    
    // 获取物料类型标签文本
    getMaterialTypeLabel(type) {
      const typeMap = {
        'raw_material': '原料',
        'semi_finished': '一级半成品',
        'semi_finished_two': '二级半成品',
        'product': '成品',
        'component': '零部件'
      };
      return typeMap[type] || type || '未知';
    },
    
    // 获取库存样式
    getStockClass(row) {
      const currentStock = row.currentStock || 0;
      const minStock = row.minStockQuantity || 0;
      
      if (currentStock <= minStock) return 'low-stock';
      if (currentStock <= minStock * 2) return 'medium-stock';
      return 'high-stock';
    },
    
    // 获取到期日期样式
    getExpiryClass(expiryDate) {
      if (!expiryDate) return '';
      const today = new Date();
      const expiry = new Date(expiryDate);
      const diffDays = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return 'expired';
      if (diffDays <= 30) return 'expiring-soon';
      return '';
    },
    
    // 表格汇总
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property === 'currentStock') {
          const values = data.map(item => Number(item.currentStock) || 0);
          sums[index] = values.reduce((prev, curr) => prev + curr, 0);
        } else if (column.property === 'inboundQuantity') {
          const values = data.map(item => Number(item.inboundQuantity) || 0);
          sums[index] = values.reduce((prev, curr) => prev + curr, 0);
        } else if (column.property === 'outboundQuantity') {
          const values = data.map(item => Number(item.outboundQuantity) || 0);
          sums[index] = values.reduce((prev, curr) => prev + curr, 0);
        } else if (column.property === 'stockQuantity') {
          const values = data.map(item => Number(item.stockQuantity) || 0);
          sums[index] = values.reduce((prev, curr) => prev + curr, 0);
        } else if (column.property === 'totalValue') {
          const values = data.map(item => (item.currentStock || 0) * (item.unitPrice || 0));
          sums[index] = '¥' + values.reduce((prev, curr) => prev + curr, 0).toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    
    // 前往库存明细详情页面
    goToInventoryDetailPage() {
        this.$router.push({
            path: '/warehouse/smart-center',
            query: {
                tab: 'inventory',
                zoneId: this.zoneId
            }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
/* 表格字段居中样式 */
.inventory-detail-management :deep(.el-table) {
  .el-table__body-wrapper {
    .el-table__body {
      td {
        text-align: center !important;
        
        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }
  
  .el-table__header-wrapper {
    .el-table__header {
      th {
        text-align: center !important;
        
        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }
}
.inventory-detail-management {
  padding: 20px;
  background: var(--base-main-bg);
  
  .stats-section {
    margin-bottom: 20px;
    
    .stat-card {
      border: 1px solid var(--border-color-1);
      
      .stat-item {
        text-align: center;
        padding: 15px;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--current-color);
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--base-color-3);
        }
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
  }
  
  .table-section {
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
    
    .location-info {
      .bin-code {
        font-size: 12px;
        color: var(--base-color-3);
        margin-top: 2px;
      }
    }
    
    .low-stock {
      color: #f56c6c;
      font-weight: 600;
    }
    
    .medium-stock {
      color: #e6a23c;
    }
    
    .high-stock {
      color: #67c23a;
    }
    
    .expired {
      color: #f56c6c;
      font-weight: 600;
    }
    
    .expiring-soon {
      color: #e6a23c;
      font-weight: 600;
    }
  }
  
  .pagination-section {
    margin-top: 20px;
    text-align: center;
  }
}

// 表单样式适配
:deep(.el-form) {
  .el-form-item__label {
    color: var(--theme-color);
    font-weight: 500;
  }
  
  .el-input__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);
    
    &:focus {
      border-color: var(--current-color);
    }
  }
  
  .el-select .el-input__inner {
    background: var(--base-main-bg);
  }
  
  .el-textarea__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);
    
    &:focus {
      border-color: var(--current-color);
    }
  }
  
  .el-radio__label {
    color: var(--theme-color);
  }
}

// 表格样式适配
:deep(.el-table) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  
  th {
    background: var(--base-color-9);
    color: var(--theme-color);
    border-bottom-color: var(--border-color-1);
  }
  
  td {
    border-bottom-color: var(--border-color-1);
    color: var(--theme-color);
    background: var(--base-main-bg);
  }
  
  tr:hover td {
    background: var(--table-row-hover-bg);
  }
  
  .el-button--text {
    color: var(--current-color) !important;
    background: transparent !important;
    border: none !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    
    &:hover {
      color: var(--base-menu-color-active) !important;
      background-color: var(--current-color) !important;
      border-radius: 4px !important;
    }
    
    &:focus {
      color: var(--current-color) !important;
      background: transparent !important;
    }
    
    // 为不同类型的操作按钮设置不同颜色
    &.view-btn {
      color: var(--color-2) !important;
      
      &:hover {
        background-color: var(--color-2) !important;
        color: #fff !important;
      }
    }
    
    &.edit-btn {
      color: #e6a23c !important;
      
      &:hover {
        background-color: #e6a23c !important;
        color: #fff !important;
      }
    }
    
    &.delete-btn {
      color: #f56c6c !important;
      
      &:hover {
        background-color: #f56c6c !important;
        color: #fff !important;
      }
    }
    
    &.move-btn {
      color: #909399 !important;
      
      &:hover {
        background-color: #909399 !important;
        color: #fff !important;
      }
    }
  }
  
  .el-table__footer {
    background: var(--base-color-9);
    color: var(--theme-color);
  }
}

// 卡片样式适配
:deep(.el-card) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  
  .el-card__body {
    background: var(--base-main-bg);
  }
}

// 弹窗样式适配
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  
  .el-dialog__title {
    color: var(--theme-color);
  }
}

/* 物料区域兼容性提示样式 */
.compatibility-tip {
  margin-top: 8px;
}

.compatibility-tip .el-alert {
  margin-bottom: 8px;
}

.compatibility-tip .el-alert--success {
  background-color: #f0f9ff;
  border-color: #e6f7ff;
}

.compatibility-tip .el-alert--warning {
  background-color: #fff7e6;
  border-color: #ffd591;
}
</style> 
<template>
  <div class="warehouse-zone-management">
    <!-- 仓库信息提示（如果是从仓库管理进入） -->
    <!-- <el-alert
      v-if="selectedWarehouseFromParent && warehouseName"
      :title="`从仓库管理进入 - 当前查看：${warehouseName}${warehouseTypeName}`"
      type="info"
      :closable="false"
      class="mb8"
      show-icon
    /> -->

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input
            v-model="searchForm.zoneName"
            placeholder="请输入区域名称"
            prefix-icon="el-icon-search"
            clearable
            @clear="handleSearch"
            @keyup.enter.native="handleSearch"
          />
        </el-col>
        <!-- 仓库筛选（只显示仓库名称） -->
        <el-col :span="5">
          <el-select
            v-model="searchForm.warehouseCode"
            placeholder="全部仓库"
            clearable
            filterable
            @clear="handleSearch"
            @change="handleSearch"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.warehouseCode"
              :label="warehouse.warehouseName"
              :value="warehouse.warehouseCode"
            />
          </el-select>
        </el-col>
        <!-- 仓库类型筛选 -->
        <el-col :span="5">
          <el-select
            v-model="searchForm.warehouseType"
            placeholder="全部类型"
            clearable
            @clear="handleSearch"
            @change="handleSearch"
          >
            <el-option
              v-for="option in warehouseTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            @clear="handleSearch"
            @change="handleSearch"
          >
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-section">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
        新增区域
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
      <el-button
        type="warning"
        icon="el-icon-printer"
        :disabled="selectedRows.length === 0"
        @click="handleBatchPrintQrCode"
      >
        批量打印二维码
      </el-button>
      <el-button type="success" icon="el-icon-download" @click="handleExport">
        导出数据
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        stripe
        border
        :row-class-name="getRowClassName"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="zoneCode" label="区域编码" width="130" align="center" />
        <el-table-column prop="zoneName" label="区域名称" min-width="150" align="center">
          <template slot-scope="scope">
            <span
              :class="{ 'zone-name-clickable': hierarchicalMode }"
              @click="handleZoneNameClick(scope.row)"
            >
              {{ scope.row.zoneName }}
            </span>
          </template>
        </el-table-column>
        <!-- 所属仓库列（始终显示） -->
        <el-table-column label="所属仓库" width="180" align="center">
          <template slot-scope="scope">
            <div class="warehouse-info">
              <div class="warehouse-name">
                {{ scope.row.warehouseName }}
                <el-tag
                  v-if="scope.row.warehouseStatus === '0'"
                  type="warning"
                  size="mini"
                  style="margin-left: 5px;"
                >
                  仓库停用
                </el-tag>
              </div>
              <div
                v-if="scope.row.warehouseStatus === '0'"
                class="warehouse-status-tip"
                style="color: #e6a23c; font-size: 12px; margin-top: 2px;"
              >
                <i class="el-icon-warning"></i>
                所属仓库已停用
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseType" label="仓库类型" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getWarehouseTypeTag(scope.row.warehouseType)">
              {{ getWarehouseTypeLabel(scope.row.warehouseType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="zoneType" label="区域类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag size="small" :type="getZoneTypeTag(scope.row.zoneType)">
              {{ getZoneTypeLabel(scope.row.zoneType) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 容量使用率列（已注释）
        <el-table-column label="容量使用率" width="120">
          <template slot-scope="scope">
            <el-progress
              :percentage="getUsageRate(scope.row)"
              :color="getUsageColor(scope.row)"
              :show-text="false"
            />
            <span class="usage-text">
              {{ scope.row.currentUsage || 0 }}/{{ scope.row.zoneCapacity || 0 }}
            </span>
          </template>
        </el-table-column>
        -->

        <!-- 新增二维码相关列 -->
        <el-table-column label="二维码状态" width="120" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.zoneQrCode ? 'success' : 'warning'"
              size="small"
            >
              {{ scope.row.zoneQrCode ? '已生成' : '未生成' }}
            </el-tag>
            <br>
            <span v-if="scope.row.qrCodePrintTime" class="qr-time">
              {{ formatQrCodeTime(scope.row.qrCodePrintTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="二维码版本" width="100" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="info">
              v{{ scope.row.qrCodeVersion || 1 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120" align="center">
          <template slot-scope="scope">
            <div class="status-info">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                {{ scope.row.status === '1' ? '启用' : '停用' }}
              </el-tag>
              <div
                v-if="scope.row.status === '0'"
                class="zone-status-tip"
                style="color: #f56c6c; font-size: 12px; margin-top: 2px;"
              >
                <i class="el-icon-warning"></i>
                区域已停用
              </div>
              <div
                v-else-if="scope.row.warehouseStatus === '0'"
                class="zone-status-tip"
                style="color: #e6a23c; font-size: 12px; margin-top: 2px;"
              >
                <i class="el-icon-warning"></i>
                仓库已停用
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template slot-scope="scope">
            <!-- 主要操作按钮 -->
            <el-button type="text" size="small" class="view-btn" @click="handleView(scope.row)">
              <i class="el-icon-view"></i>
              查看
            </el-button>
            <el-button type="text" size="small" class="edit-btn" @click="handleEdit(scope.row)">
              <i class="el-icon-edit"></i>
              修改
            </el-button>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown size="small" @command="handleCommand($event, scope.row)">
              <el-button type="text" size="small" class="more-btn">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="manageInventory">
                  <i class="el-icon-document-checked"></i>
                  库存管理
                </el-dropdown-item>
                <el-dropdown-item command="printQrCode">
                  <i class="el-icon-printer"></i>
                  打印二维码
                </el-dropdown-item>
                <el-dropdown-item
                  :command="scope.row.status === '1' ? 'disable' : 'enable'"
                  :divided="true"
                >
                  <i :class="scope.row.status === '1' ? 'el-icon-close' : 'el-icon-check'"></i>
                  {{ scope.row.status === '1' ? '停用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item command="delete" class="danger-item">
                  <i class="el-icon-delete"></i>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 查看详情弹窗 -->
    <el-dialog
      title="区域详情"
      :visible.sync="viewDialogVisible"
      width="800px"
    >
      <div class="zone-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="区域编码">
            {{ viewData.zoneCode }}
          </el-descriptions-item>
          <el-descriptions-item label="区域名称">
            {{ viewData.zoneName }}
          </el-descriptions-item>
          <el-descriptions-item label="所属仓库">
            {{ viewData.warehouseName }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库类型">
            <el-tag :type="getWarehouseTypeTag(viewData.warehouseType)">
              {{ getWarehouseTypeLabel(viewData.warehouseType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="区域类型">
            <el-tag :type="getZoneTypeTag(viewData.zoneType)">
              {{ getZoneTypeLabel(viewData.zoneType) }}
            </el-tag>
          </el-descriptions-item>
          <!-- 容量相关信息（已注释）
          <el-descriptions-item label="区域容量">
            {{ viewData.zoneCapacity }}
          </el-descriptions-item>
          <el-descriptions-item label="当前使用量">
            {{ viewData.currentUsage }}
          </el-descriptions-item>
          <el-descriptions-item label="使用率">
            <el-progress
              :percentage="getUsageRate(viewData)"
              :color="getUsageColor(viewData)"
            />
          </el-descriptions-item>
          -->

          <!-- 新增二维码相关信息 -->
          <el-descriptions-item label="二维码内容">
            <el-input
              v-if="viewData.zoneQrCode"
              :value="viewData.zoneQrCode"
              readonly
              size="small"
              style="width: 200px;"
            />
            <el-tag v-else type="warning" size="small">未生成</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="二维码版本">
            <el-tag type="info" size="small">
              v{{ viewData.qrCodeVersion || 1 }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="打印时间">
            {{ viewData.qrCodePrintTime ? formatDateTime(viewData.qrCodePrintTime) : '未打印' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewData.status === '1' ? 'success' : 'danger'">
              {{ viewData.status === '1' ? '启用' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ viewData.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ viewData.zoneDescription }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 新增/修改区域弹窗 -->
    <el-dialog
      :title="editFormData.zoneId ? '修改区域' : '新增区域'"
      :visible.sync="editDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editForm"
        :model="editFormData"
        :rules="editFormRules"
        label-width="120px"
        class="centered-form"
      >
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="区域编码" prop="zoneCode">
              <el-input
                v-model="editFormData.zoneCode"
                :placeholder="editFormData.zoneId ? '请输入区域编码' : '区域编码将自动生成'"
                :readonly="!editFormData.zoneId"
                :class="!editFormData.zoneId ? 'readonly-input' : ''"
                style="min-width: 150px;"
              >
                <!-- <template v-if="!editFormData.zoneId" slot="append">
                  <el-button
                    @click="handleGenerateZoneCode"
                    :disabled="!editFormData.warehouseId || !editFormData.zoneType"
                    icon="el-icon-refresh"
                    title="重新生成编码"
                    size="small"
                  >
                    {{ editFormData.zoneCode ? '重新生成' : '生成' }}
                  </el-button>
                </template> -->
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="zoneName">
              <el-input
                v-model="editFormData.zoneName"
                placeholder="请输入区域名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属仓库" prop="warehouseCode">
              <el-select
                v-model="editFormData.warehouseCode"
                placeholder="请选择所属仓库"
                filterable
                style="width: 100%"
                @change="handleWarehouseChange"
              >
                <el-option
                  v-for="warehouse in warehouseOptions"
                  :key="warehouse.warehouseCode"
                  :label="`${warehouse.warehouseName}（${warehouse.warehouseTypeName}）`"
                  :value="warehouse.warehouseCode"
                  :disabled="warehouse.status === '0'"
                  :class="getWarehouseOptionClass(warehouse)"
                >
                  <span :style="getWarehouseTextStyle(warehouse)">
                    {{ warehouse.warehouseName }}（{{ warehouse.warehouseTypeName }}）
                    <span v-if="warehouse.status === '0'" class="disabled-text">(仓库已停用)</span>
                  </span>
                </el-option>
              </el-select>
              <!-- 仓库状态提示信息 -->
              <div
                v-if="getSelectedWarehouseStatus() === '0'"
                class="warehouse-warning-tip"
                style="color: #e6a23c; font-size: 12px; margin-top: 5px;"
              >
                <i class="el-icon-warning"></i>
                警告：所选仓库已停用，无法进行区域操作
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域类型" prop="zoneType">
              <el-select
                v-model="editFormData.zoneType"
                placeholder="请选择区域类型"
                style="width: 100%"
                @change="handleZoneTypeChange"
              >
                <el-option label="储存区" value="storage" />
                <el-option label="拣货区" value="picking" />
                <el-option label="暂存区" value="staging" />
                <el-option label="检验区" value="inspection" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <!-- 容量字段（已注释）
          <el-col :span="12">
            <el-form-item label="区域容量" prop="zoneCapacity">
              <el-input-number
                v-model="editFormData.zoneCapacity"
                :min="1"
                style="width: 100%"
                placeholder="请输入区域容量"
              />
            </el-form-item>
          </el-col>
          -->

          <!-- 新增二维码相关字段 -->
          <el-col :span="12">
            <el-form-item label="二维码内容" prop="zoneQrCode">
              <el-input
                v-model="editFormData.zoneQrCode"
                placeholder="请输入二维码内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="editFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="启用" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="区域描述" prop="zoneDescription">
          <el-input
            v-model="editFormData.zoneDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入区域描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editFormData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit" :loading="editLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 库存明细弹窗 -->
    <el-dialog
      :title="`库存明细 - ${selectedZone.zoneName}`"
      :visible.sync="inventoryDialogVisible"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
    >
      <inventory-detail-management
        v-if="inventoryDialogVisible"
        :zone-code="selectedZone.zoneCode"
        :zone-name="selectedZone.zoneName"
        :show-actions="false"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="inventoryDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="goToInventoryDetailPage">前往库存明细页面</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZone,
  getZone,
  addZone,
  updateZone,
  delZone,
  delBatchZone,
  changeZoneStatus,
  generateZoneQrCode,
  batchGenerateZoneQrCode,
  getZoneTypeOptions,
  exportZone,
  printZoneQrCode,
  generateZoneCode,
  checkZoneInfoUnique,
  checkZoneInfoUniqueByCode,
  getZonesByWarehouseCode
} from '@/api/inventory/zone';
import { listWarehouse } from '@/api/inventory/warehouse';
import InventoryDetailManagement from './InventoryDetailManagement.vue';

export default {
  name: 'WarehouseZoneManagement',
  components: { InventoryDetailManagement },
  props: {
    warehouseId: {
      type: [String, Number],
      required: false,
      default: null
    },
    warehouseName: {
      type: String,
      default: ''
    },
    warehouseType: {
      type: String,
      default: ''
    },
    warehouseTypeName: {
      type: String,
      default: ''
    },
    // 新增仓库编码属性
    warehouseCode: {
      type: String,
      default: ''
    },
    // 是否为层级导航模式
    hierarchicalMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      selectedWarehouseFromParent: false, // 是否从仓库管理进入
      searchForm: {
        zoneName: '',
        warehouseCode: '', // 改为使用仓库编码
        warehouseType: '',
        status: ''
      },
      warehouseTypeOptions: [
        { label: '原料仓库', value: '1' },
        { label: '半成品仓库', value: '2' },
        { label: '成品仓库', value: '3' },
        { label: '零件仓库', value: '4' }
      ],
      warehouseOptions: [], // 仓库选项列表
      tableData: [],
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      viewDialogVisible: false,
      viewData: {},
      editDialogVisible: false,
      editLoading: false,
      editFormData: {
        zoneId: '',
        zoneCode: '',
        zoneName: '',
        warehouseCode: '', // 改为使用仓库编码
        zoneType: '',
        // zoneCapacity: null, // 已注释容量字段
        zoneQrCode: '', // 新增二维码字段
        qrCodeVersion: 1, // 二维码版本
        status: '1',
        zoneDescription: '',
        remark: ''
      },
      editFormRules: {
        zoneCode: [
          { required: true, message: '区域编码不能为空', trigger: 'change' }
        ],
        zoneName: [
          { required: true, message: '请输入区域名称', trigger: 'blur' }
        ],

        warehouseCode: [
          { required: true, message: '请选择所属仓库', trigger: 'change' },
          { validator: this.validateWarehouseStatus, trigger: 'change' }
        ],
        zoneType: [
          { required: true, message: '请选择区域类型', trigger: 'change' }
        ],
        // zoneCapacity: [
        //   { required: true, message: '请输入区域容量', trigger: 'blur' }
        // ], // 已注释容量验证规则
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      inventoryDialogVisible: false,
      selectedZone: {}
    };
  },
  mounted() {
    console.log('区域管理组件mounted，接收到的props:', {
      warehouseId: this.warehouseId,
      warehouseName: this.warehouseName,
      warehouseCode: this.warehouseCode,
      warehouseType: this.warehouseType
    });

    // 先获取仓库列表，然后设置筛选条件
    this.fetchWarehouseOptions().then(() => {
      console.log('仓库选项加载完成:', this.warehouseOptions.length);

      // 优先使用warehouseCode进行筛选
      if (this.warehouseCode) {
        console.log('使用warehouseCode进行筛选:', this.warehouseCode);
        this.searchForm.warehouseCode = this.warehouseCode;
        this.selectedWarehouseFromParent = true;
      } else if (this.warehouseId) {
        // 兼容旧版本，通过warehouseId查找对应的warehouseCode
        console.log('通过warehouseId查找warehouseCode:', this.warehouseId);
        const warehouse = this.warehouseOptions.find(w => String(w.warehouseId) === String(this.warehouseId));
        if (warehouse) {
          console.log('找到对应仓库:', warehouse);
          this.searchForm.warehouseCode = warehouse.warehouseCode;
          this.selectedWarehouseFromParent = true;
        } else {
          console.warn('未找到对应的仓库:', this.warehouseId);
        }
      }

      console.log('最终筛选条件:', this.searchForm);

      // 获取区域数据
      this.fetchData();
    }).catch(error => {
      console.error('获取仓库选项失败:', error);
      // 即使获取仓库选项失败，也要尝试获取数据
      this.fetchData();
    });
  },
  watch: {
    // 监听仓库编码变化，更新筛选条件
    warehouseCode: {
      handler(newVal, oldVal) {
        console.log('warehouseCode 变化:', { newVal, oldVal });
        if (newVal !== oldVal) {
          if (newVal) {
            this.searchForm.warehouseCode = newVal;
            this.selectedWarehouseFromParent = true;
            console.log('设置仓库筛选条件:', newVal);
            // 确保仓库选项已加载后再获取数据
            if (this.warehouseOptions.length > 0) {
              this.fetchData();
            } else {
              // 如果仓库选项还没加载，先加载仓库选项再获取数据
              this.fetchWarehouseOptions().then(() => {
                this.fetchData();
              });
            }
          } else {
            // 如果warehouseCode为空，重置筛选条件
            this.searchForm.warehouseCode = '';
            this.selectedWarehouseFromParent = false;
            this.fetchData();
          }
        }
      },
      immediate: true
    },
    // 兼容监听仓库ID变化
    warehouseId: {
      handler(newVal, oldVal) {
        console.log('warehouseId 变化:', { newVal, oldVal });
        if (newVal !== oldVal && newVal && this.warehouseOptions.length > 0) {
          const warehouse = this.warehouseOptions.find(w => String(w.warehouseId) === String(newVal));
          if (warehouse) {
            this.searchForm.warehouseCode = warehouse.warehouseCode;
            this.selectedWarehouseFromParent = true;
            console.log('通过warehouseId设置仓库筛选条件:', warehouse.warehouseCode);
            this.fetchData();
          }
        }
      },
      immediate: true
    }
  },
      methods: {
    // 获取仓库选项列表
    fetchWarehouseOptions() {
      return listWarehouse().then(response => {
        if (response.code === 200) {
          this.warehouseOptions = (response.rows || []).map(warehouse => ({
            warehouseId: String(warehouse.warehouseId), // 确保ID为字符串
            warehouseCode: warehouse.warehouseCode,     // 添加仓库编码
            warehouseName: warehouse.warehouseName,
            warehouseType: warehouse.warehouseType,
            warehouseTypeName: this.getWarehouseTypeLabel(warehouse.warehouseType)
          }));
        }
        return response;
      }).catch(error => {
        console.error('获取仓库列表失败:', error);
        throw error;
      });
    },

    // 获取数据
    fetchData() {
      this.loading = true;
      const params = {
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        zoneName: this.searchForm.zoneName,
        warehouseCode: this.searchForm.warehouseCode, // 改为使用仓库编码
        warehouseType: this.searchForm.warehouseType, // 仓库类型筛选
        status: this.searchForm.status
      };

      console.log('区域管理 - 发送请求参数:', params);
      console.log('当前仓库筛选状态:', {
        selectedWarehouseFromParent: this.selectedWarehouseFromParent,
        searchFormWarehouseCode: this.searchForm.warehouseCode,
        propsWarehouseCode: this.warehouseCode
      });

      listZone(params).then(response => {
        console.log('区域管理 - 接收到响应:', response);
        if (response.code === 200) {
          const responseData = response.rows || [];
          console.log('原始区域数据条数:', responseData.length);

          this.tableData = responseData.map(row => {
            // 根据warehouseCode查找对应的仓库名称
            const warehouse = this.warehouseOptions.find(w => w.warehouseCode === row.warehouseCode);
            return {
              ...row,
              warehouseName: warehouse ? warehouse.warehouseName : '未找到仓库'
            };
          });

          console.log('处理后的区域数据条数:', this.tableData.length);
          if (this.searchForm.warehouseCode) {
            console.log('筛选特定仓库的区域:', this.searchForm.warehouseCode);
            const filteredData = this.tableData.filter(item => item.warehouseCode === this.searchForm.warehouseCode);
            console.log('筛选后的区域数据条数:', filteredData.length);
          }

          this.pagination.total = response.total || 0;
        } else {
          console.error('获取区域数据失败:', response.msg);
          this.$message.error(response.msg || '获取数据失败');
        }
      }).catch(error => {
        console.error('获取区域数据失败:', error);
        this.$message.error('获取数据失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        zoneName: '',
        warehouseCode: '',
        warehouseType: '',
        status: ''
      };
      this.handleSearch();
    },

    // 新增区域
    handleAdd() {
      const defaultWarehouseCode = this.searchForm.warehouseCode || '';

      this.editFormData = {
        zoneId: '',
        zoneCode: '',
        zoneName: '',
        warehouseCode: defaultWarehouseCode, // 默认使用当前筛选的仓库编码，或为空
        warehouseType: '', // 初始化为空，会在下面根据仓库编码设置
        zoneType: '',
        // zoneCapacity: null, // 已注释容量字段
        zoneQrCode: '', // 新增二维码字段
        qrCodeVersion: 1, // 二维码版本
        status: '1',
        zoneDescription: '',
        remark: ''
      };

      // 如果有默认仓库，自动设置仓库类型
      if (defaultWarehouseCode) {
        const warehouse = this.warehouseOptions.find(w => w.warehouseCode === defaultWarehouseCode);
        if (warehouse) {
          this.editFormData.warehouseType = warehouse.warehouseType;
        }
      }

      this.editDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate();
        }
      });
    },

    // 查看详情
    handleView(row) {
      // 根据warehouseCode查找对应的仓库名称
      const warehouse = this.warehouseOptions.find(w => w.warehouseCode === row.warehouseCode);
      this.viewData = {
        ...row,
        warehouseName: warehouse ? warehouse.warehouseName : '未找到仓库'
      };
      this.viewDialogVisible = true;
    },

    // 修改区域
    handleEdit(row) {
      this.editFormData = {
        ...row,
        warehouseCode: row.warehouseCode // 使用仓库编码
      };
      this.editDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate();
        }
        // 检查仓库状态并显示警告
        if (row.warehouseStatus === '0') {
          this.$message.warning('警告：当前区域所属的仓库已停用，修改操作可能受限');
        }
      });
    },

    // 提交修改
    handleEditSubmit() {
      this.$refs.editForm.validate(valid => {
        if (!valid) {
          return;
        }

        // 额外检查仓库状态（防止用户绕过前端验证）
        const warehouse = this.warehouseOptions.find(w => w.warehouseCode === this.editFormData.warehouseCode);
        if (warehouse && warehouse.status === '0') {
          this.$message.error('所选仓库已停用，无法进行区域操作');
          return;
        }

        this.editLoading = true;
        const isEdit = !!this.editFormData.zoneId;
        const apiCall = isEdit ? updateZone(this.editFormData) : addZone(this.editFormData);

        apiCall.then(response => {
          if (response.code === 200) {
            this.$message.success(isEdit ? '修改成功' : '新增成功');
            this.editDialogVisible = false;
            this.fetchData();
          } else {
            this.$message.error(response.msg || '操作失败');
          }
        }).catch(error => {
          console.error('保存区域失败:', error);
          this.$message.error('操作失败');
        }).finally(() => {
          this.editLoading = false;
        });
      });
    },

    // 处理区域名称点击（层级导航模式）
    handleZoneNameClick(row) {
      if (this.hierarchicalMode) {
        console.log('层级导航模式 - 区域名称点击:', row);
        // 根据warehouseCode查找对应的仓库信息
        const warehouse = this.warehouseOptions.find(w => w.warehouseCode === row.warehouseCode);

        // 构造完整的区域信息，包含仓库ID
        const zoneInfo = {
          ...row,
          warehouseId: warehouse ? warehouse.warehouseId : null,
          warehouseName: warehouse ? warehouse.warehouseName : row.warehouseName
        };

        console.log('跳转到库存明细，传递的区域信息:', zoneInfo);
        this.$emit('go-to-detail', zoneInfo);
      }
    },

    // 库存管理 - 打开弹窗显示区域内的物料库存
    handleManageInventory(row) {
      // 修复：无论在哪种导航模式下，操作按钮都应该显示库存明细弹窗
      // 这样保持了按钮行为的一致性
      this.selectedZone = row;
      this.inventoryDialogVisible = true;
    },

    // 删除区域
    handleDelete(row) {
      this.$confirm('确定删除该区域吗？删除后关联的库存数据也将被删除！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delZone(row.zoneId).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.fetchData();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          console.error('删除区域失败:', error);
          this.$message.error('删除失败');
        });
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }
      this.$confirm(`确定删除选中的${this.selectedRows.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const zoneIds = this.selectedRows.map(row => row.zoneId);
        delBatchZone(zoneIds.join(',')).then(response => {
          if (response.code === 200) {
            this.$message.success('批量删除成功');
            this.selectedRows = [];
            this.fetchData();
          } else {
            this.$message.error(response.msg || '批量删除失败');
          }
        }).catch(error => {
          console.error('批量删除区域失败:', error);
          this.$message.error('批量删除失败');
        });
      });
    },

    // 切换状态
    handleToggleStatus(row) {
      const status = row.status === '1' ? '0' : '1';
      const statusText = status === '1' ? '启用' : '停用';
      this.$confirm(`确定${statusText}该区域吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeZoneStatus([row.zoneId], status).then(response => {
          if (response.code === 200) {
            row.status = status;
            this.$message.success(`${statusText}成功`);
          } else {
            this.$message.error(response.msg || `${statusText}失败`);
          }
        }).catch(error => {
          console.error('更改区域状态失败:', error);
          this.$message.error(`${statusText}失败`);
        });
      });
    },

    // 导出
    handleExport() {
      this.$message.info('正在准备导出数据...');

      const params = {
        zoneName: this.searchForm.zoneName,
        warehouseCode: this.searchForm.warehouseCode,
        warehouseType: this.searchForm.warehouseType,
        status: this.searchForm.status
      };

      // 添加加载状态
      const loading = this.$loading({
        lock: true,
        text: '数据导出中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      exportZone(params).then(response => {
        loading.close();

        // 检查响应类型
        if (response instanceof Blob) {
          // Blob类型响应，直接下载
          this.downloadFile(response, 'warehouse_zones.xlsx');
          this.$message.success('导出成功');
        } else if (response && response.data) {
          // 如果返回的是base64或其他格式
          const blob = new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });
          this.downloadFile(blob, 'warehouse_zones.xlsx');
          this.$message.success('导出成功');
        } else {
          this.$message.error('导出数据为空');
        }
      }).catch(error => {
        loading.close();
        console.error('导出区域数据失败:', error);

        if (error.response && error.response.status === 404) {
          this.$message.error('导出接口不存在，请联系管理员');
        } else if (error.response && error.response.status === 500) {
          this.$message.error('服务器错误，导出失败');
        } else {
          this.$message.error('导出失败，请稍后重试');
        }
      });
    },

    // 文件下载方法
    downloadFile(blob, filename) {
      try {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;

        // 添加到DOM并触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('文件下载失败:', error);
        this.$message.error('文件下载失败');
      }
    },

    // 表格选择
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    // 分页
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchData();
    },

    // 获取仓库类型标签样式
    getWarehouseTypeTag(type) {
      const tagMap = {
        // 数字类型（数据库中的值）
        '1': 'primary',
        '2': 'warning',
        '3': 'info',
        '4': 'success',
        // 字符串类型（兼容原有）
        'raw_material': 'primary',
        'semi_finished': 'warning',
        'product': 'info',
        'component': 'success'
      };
      return tagMap[type] || 'info';
    },

    // 获取仓库类型标签文本
    getWarehouseTypeLabel(type) {
      const typeMap = {
        // 数字类型（数据库中的值）
        '1': '原料仓库',
        '2': '半成品仓库',
        '3': '成品仓库',
        '4': '零件仓库',
        // 字符串类型（兼容原有）
        'raw_material': '原料仓库',
        'semi_finished': '半成品仓库',
        'product': '成品仓库',
        'component': '零件仓库'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取区域类型标签样式
    getZoneTypeTag(type) {
      const tagMap = {
        'storage': 'primary',
        'picking': 'success',
        'staging': 'warning',
        'inspection': 'info'
      };
      return tagMap[type] || 'info';
    },

    // 获取区域类型标签文本
    getZoneTypeLabel(type) {
      const typeMap = {
        'storage': '储存区',
        'picking': '拣货区',
        'staging': '暂存区',
        'inspection': '检验区'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取使用率
    getUsageRate(row) {
      if (!row.zoneCapacity || row.zoneCapacity === 0) return 0;
      return Math.round((row.currentUsage || 0) / row.zoneCapacity * 100);
    },

    // 获取使用率颜色（已注释，保留作为备用）
    // getUsageColor(row) {
    //   const rate = this.getUsageRate(row);
    //   if (rate < 60) return '#67c23a';
    //   if (rate < 80) return '#e6a23c';
    //   return '#f56c6c';
    // },

    // 新增二维码相关方法
    formatQrCodeTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    },

    formatDateTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    },

    // 新增打印二维码方法
    handlePrintQrCode(row) {
      // 检查是否已经存在二维码
      if (row.zoneQrCode) {
        // 构建带悬停效果的二维码内容
        const qrPreview = row.zoneQrCode.length > 40 ?
          row.zoneQrCode.substring(0, 40) + '...' :
          row.zoneQrCode;

        const copyBtnId = `copy-btn-${row.zoneId}-${Date.now()}`;

        const qrCodeDisplay = `
          <div class="qr-content-container">
            <span class="qr-content-preview" title="${row.zoneQrCode}">${qrPreview}</span>
            <button id="${copyBtnId}" class="qr-copy-btn" title="复制二维码内容">
              📋
            </button>
          </div>`;

        const printTime = row.qrCodePrintTime ?
          this.formatQrCodeTime(row.qrCodePrintTime) :
          '未知';

        const message = `
<div class="qr-info-container">
  <div class="qr-info-item">📦 区域：${row.zoneName} (${row.zoneCode})</div>
  <div class="qr-info-item">🏷️  状态：已生成二维码</div>
  <div class="qr-info-item">📋 内容：${qrCodeDisplay}</div>
  <div class="qr-info-item">📌 版本：<span class="version-tag">v${row.qrCodeVersion || 1}</span></div>
  <div class="qr-info-item">⏰ 时间：${printTime}</div>
  <div class="qr-action-tip">请选择操作：</div>
</div>`;

        this.$confirm(message, '🖨️ 二维码打印', {
          confirmButtonText: '🔄 重新生成',
          cancelButtonText: '📄 直接打印',
          distinguishCancelAndClose: true,
          type: 'warning',
          customClass: 'qr-code-confirm-dialog',
          dangerouslyUseHTMLString: true
        }).then(() => {
          // 确认：重新生成并打印
          this.executeQrCodePrint(row);
        }).catch(action => {
          if (action === 'cancel') {
            // 取消：直接打印现有二维码
            this.callPrintService(row);
          }
          // close：不执行任何操作
        });

        // 延迟绑定复制按钮事件
        this.$nextTick(() => {
          const copyBtn = document.getElementById(copyBtnId);
          if (copyBtn) {
            copyBtn.addEventListener('click', () => {
              this.copyToClipboard(row.zoneQrCode, copyBtn);
            });
          }
        });

      } else {
        const message = `
<div class="qr-info-container">
  <div class="qr-info-item">📦 区域：${row.zoneName} (${row.zoneCode})</div>
  <div class="qr-info-item">🏷️  状态：未生成二维码</div>
  <div class="qr-action-tip">将为此区域生成新的二维码并打印</div>
</div>`;

        this.$confirm(message, '🖨️ 生成二维码', {
          confirmButtonText: '✅ 生成并打印',
          cancelButtonText: '❌ 取消',
          type: 'info',
          customClass: 'qr-code-confirm-dialog',
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.executeQrCodePrint(row);
        });
      }
    },

    // 复制到剪切板
    copyToClipboard(text, button) {
      // 使用现代API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          this.showCopySuccess(button);
          this.$message.success('二维码内容已复制到剪切板');
        }).catch(() => {
          this.fallbackCopyTextToClipboard(text, button);
        });
      } else {
        this.fallbackCopyTextToClipboard(text, button);
      }
    },

    // 备用复制方法
    fallbackCopyTextToClipboard(text, button) {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.left = '-999999px';
      textarea.style.top = '-999999px';
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();

      try {
        document.execCommand('copy');
        this.showCopySuccess(button);
        this.$message.success('二维码内容已复制到剪切板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      } finally {
        document.body.removeChild(textarea);
      }
    },

    // 显示复制成功状态
    showCopySuccess(button) {
      const originalText = button.innerHTML;
      const originalBackground = button.style.background;

      button.innerHTML = '✅';
      button.style.background = '#67c23a';
      button.style.transform = 'scale(1.1)';

      setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = originalBackground;
        button.style.transform = '';
      }, 1500);
    },

    // 执行二维码生成和打印
    executeQrCodePrint(row) {
      this.$message.info('正在生成二维码...');

      // 1. 先生成二维码
      generateZoneQrCode(row.zoneId).then(response => {
        if (response.code === 200) {
          this.$message.success('二维码生成成功');

          // 2. 获取最新的区域信息（包含新生成的二维码）
          getZone(row.zoneId).then(zoneResponse => {
            if (zoneResponse.code === 200) {
              const updatedRow = { ...row, ...zoneResponse.data };

              // 3. 调用打印服务
              this.callPrintService(updatedRow);
            } else {
              // 如果获取失败，使用原数据打印
              this.callPrintService(row);
            }

            // 4. 刷新数据显示最新状态
            this.fetchData();
          }).catch(() => {
            // 获取失败时使用原数据打印
            this.callPrintService(row);
            this.fetchData();
          });

        } else {
          this.$message.error(response.msg || '二维码生成失败');
        }
      }).catch(error => {
        console.error('生成二维码失败:', error);
        this.$message.error('二维码生成失败');
      });
    },

    // 调用打印服务
    callPrintService(row) {
      // 构建打印数据 - 根据新的二维码打印文档设计，只需要传递区域编码
      const printData = {
        zoneCode: row.zoneCode
      };

      // 调用打印API (这里需要根据实际打印服务接口调整)
      this.sendToPrinter(printData);
    },

    // 发送到打印机 (基于文档的打印机驱动接口实现)
    sendToPrinter(printData) {
      // 获取客户端IP并构建打印请求
      this.$message.info('正在发送打印任务...');

      // 调用后端打印接口
      printZoneQrCode(printData).then(response => {
        if (response.code === 200) {
          this.$message.success('打印任务已发送到打印机');
        } else {
          this.$message.error(response.msg || '打印任务发送失败');
        }
      }).catch(error => {
        console.error('打印请求失败:', error);
        this.$message.error('打印任务发送失败，请检查打印机连接');
      });
    },

    // 批量打印二维码
    handleBatchPrintQrCode() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要打印二维码的区域');
        return;
      }

      // 分析选择的区域情况
      const noQrCodeZones = this.selectedRows.filter(row => !row.zoneQrCode);
      const hasQrCodeZones = this.selectedRows.filter(row => row.zoneQrCode);

      let message = `
<div class="batch-qr-info-container">
  <div class="batch-qr-info-item">📋 已选择 ${this.selectedRows.length} 个区域</div>`;

      if (noQrCodeZones.length > 0) {
        message += `
  <div class="batch-qr-info-item">🆕 需要生成二维码：${noQrCodeZones.length} 个</div>
  <div class="batch-qr-zone-list">
    ${noQrCodeZones.map(zone => `<span class="zone-tag">${zone.zoneName}(${zone.zoneCode})</span>`).join(' ')}
  </div>`;
      }

      if (hasQrCodeZones.length > 0) {
        message += `
  <div class="batch-qr-info-item">✅ 已有二维码：${hasQrCodeZones.length} 个</div>
  <div class="batch-qr-zone-list">
    ${hasQrCodeZones.map(zone => `<span class="zone-tag">${zone.zoneName}(${zone.zoneCode})</span>`).join(' ')}
  </div>`;
      }

      message += `
  <div class="batch-qr-action-tip">
    ${noQrCodeZones.length > 0 ? '将为未生成二维码的区域自动生成，' : ''}所有区域的二维码都将发送到打印机
  </div>
</div>`;

      this.$confirm(message, '🖨️ 批量打印二维码', {
        confirmButtonText: '✅ 开始批量打印',
        cancelButtonText: '❌ 取消',
        type: 'info',
        customClass: 'batch-qr-code-confirm-dialog',
        dangerouslyUseHTMLString: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = '正在处理...';
            this.executeBatchQrCodePrint().then(() => {
              done();
            }).catch(() => {
              instance.confirmButtonLoading = false;
              instance.confirmButtonText = '✅ 开始批量打印';
            });
          } else {
            done();
          }
        }
      });
    },

    // 执行批量二维码打印
    async executeBatchQrCodePrint() {
      const totalCount = this.selectedRows.length;
      let successCount = 0;
      let failCount = 0;

      this.$message.info(`开始批量处理 ${totalCount} 个区域的二维码...`);

      // 第一步：批量生成二维码（为没有二维码的区域）
      const noQrCodeZones = this.selectedRows.filter(row => !row.zoneQrCode);
      if (noQrCodeZones.length > 0) {
        try {
          this.$message.info(`正在为 ${noQrCodeZones.length} 个区域生成二维码...`);
          const zoneIds = noQrCodeZones.map(zone => zone.zoneId);
          const response = await batchGenerateZoneQrCode(zoneIds);

          if (response.code === 200) {
            this.$message.success(`成功生成 ${noQrCodeZones.length} 个区域的二维码`);
            // 刷新数据以获取最新的二维码信息
            await this.fetchData();
          } else {
            this.$message.error(`批量生成二维码失败：${response.msg}`);
            throw new Error('批量生成二维码失败');
          }
        } catch (error) {
          console.error('批量生成二维码失败:', error);
          this.$message.error('批量生成二维码失败，停止后续操作');
          return;
        }
      }

      // 第二步：批量打印二维码
      this.$message.info(`开始批量打印 ${totalCount} 个区域的二维码...`);

      // 更新选中的行数据（包含新生成的二维码）
      const updatedSelectedRows = this.tableData.filter(row =>
        this.selectedRows.some(selectedRow => selectedRow.zoneId === row.zoneId)
      );

      // 逐个发送打印任务
      for (let i = 0; i < updatedSelectedRows.length; i++) {
        const row = updatedSelectedRows[i];
        try {
          const printData = { zoneCode: row.zoneCode };
          const response = await printZoneQrCode(printData);

          if (response.code === 200) {
            successCount++;
            this.$message.success(`${row.zoneName}(${row.zoneCode}) 打印任务发送成功 (${i + 1}/${totalCount})`);
          } else {
            failCount++;
            this.$message.error(`${row.zoneName}(${row.zoneCode}) 打印失败：${response.msg}`);
          }
        } catch (error) {
          failCount++;
          console.error(`打印区域 ${row.zoneCode} 失败:`, error);
          this.$message.error(`${row.zoneName}(${row.zoneCode}) 打印任务发送失败`);
        }

        // 添加短暂延迟，避免打印机负载过重
        if (i < updatedSelectedRows.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 显示最终结果
      const resultMessage = `批量打印完成！\n成功：${successCount} 个\n失败：${failCount} 个`;

      if (failCount === 0) {
        this.$message.success(resultMessage);
      } else if (successCount === 0) {
        this.$message.error(resultMessage);
      } else {
        this.$message.warning(resultMessage);
      }

      // 清空选择
      this.selectedRows = [];
      this.$refs.table?.clearSelection();
    },

    // 处理仓库变化
    async handleWarehouseChange(value) {
      // 检查选择的仓库状态
      if (value) {
        const warehouse = this.warehouseOptions.find(w => w.warehouseCode === value);
        if (warehouse) {
          if (warehouse.status === '0') {
            this.$message.warning('警告：当前选择的仓库已停用');
            this.$nextTick(() => {
              this.$refs.editForm.validateField('warehouseCode');
            });
          }

          // 根据仓库编码获取仓库类型，自动填充仓库类型字段
          this.editFormData.warehouseType = warehouse.warehouseType;
          console.log('自动设置仓库类型:', warehouse.warehouseType);

          // 如果有选择区域类型且不是编辑状态，自动生成编码
          if (this.editFormData.zoneType && !this.editFormData.zoneId) {
            await this.handleGenerateZoneCode();
          }
        } else {
          console.warn('未找到对应的仓库信息:', value);
        }
      }
    },

    // 区域类型变化时自动生成编码
    async handleZoneTypeChange() {
      if (this.editFormData.zoneType && this.editFormData.warehouseCode && !this.editFormData.zoneId) {
        await this.handleGenerateZoneCode();
      }
    },

    // 生成区域编码
    async handleGenerateZoneCode() {
      if (!this.editFormData.warehouseCode) {
        this.$message.warning('请先选择所属仓库');
        return;
      }
      if (!this.editFormData.zoneType) {
        this.$message.warning('请先选择区域类型');
        return;
      }

      // 获取仓库信息
      const warehouse = this.warehouseOptions.find(w => w.warehouseCode === this.editFormData.warehouseCode);
      if (!warehouse) {
        this.$message.warning('无法获取仓库信息');
        return;
      }

      try {
        const response = await generateZoneCode(warehouse.warehouseId, warehouse.warehouseCode, this.editFormData.zoneType);
        if (response.code === 200) {
          this.editFormData.zoneCode = response.data;
          this.$message.success('区域编码生成成功');
        } else {
          this.$message.error(response.msg || '生成区域编码失败');
        }
      } catch (error) {
        console.error('生成区域编码失败:', error);
        this.$message.error('生成区域编码失败');
      }
    },
    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'manageInventory':
          this.handleManageInventory(row);
          break;
        case 'printQrCode':
          this.handlePrintQrCode(row);
          break;
        case 'enable':
        case 'disable':
          this.handleToggleStatus(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },

    // 跳转到完整的库存明细页面
    goToInventoryDetailPage() {
      this.inventoryDialogVisible = false; // 关闭当前弹窗

      // 根据warehouseCode查找对应的仓库信息
      const warehouse = this.warehouseOptions.find(w => w.warehouseCode === this.selectedZone.warehouseCode);

      // 构造完整的区域信息，包含仓库ID
      const zoneInfo = {
        ...this.selectedZone,
        warehouseId: warehouse ? warehouse.warehouseId : null,
        warehouseName: warehouse ? warehouse.warehouseName : this.selectedZone.warehouseName
      };

      console.log('跳转到库存明细，传递的区域信息:', zoneInfo);
      this.$emit('go-to-detail', zoneInfo);
    },

    // 获取仓库选项样式类
    getWarehouseOptionClass(warehouse) {
      if (warehouse.status === '0') {
        return 'disabled-warehouse-option';
      }
      return '';
    },

    // 获取仓库文本样式
    getWarehouseTextStyle(warehouse) {
      if (warehouse.status === '0') {
        return {
          color: '#e6a23c',
          textDecoration: 'line-through'
        };
      }
      return {};
    },

    // 获取表格行的CSS类名
    getRowClassName({ row, rowIndex }) {
      if (row.status === '0') {
        return 'disabled-zone-row';
      } else if (row.warehouseStatus === '0') {
        return 'disabled-warehouse-row';
      }
      return '';
    },

    // 验证仓库状态
    validateWarehouseStatus(rule, value, callback) {
      if (value) {
        const warehouse = this.warehouseOptions.find(w => w.warehouseCode === value);
        if (!warehouse) {
          callback(new Error('所选仓库不存在'));
          return;
        }
        if (warehouse.status === '0') {
          callback(new Error('所选仓库已停用，无法进行区域操作'));
          return;
        }
      }
      callback();
    },

    // 获取选中仓库的状态
    getSelectedWarehouseStatus() {
      if (!this.editFormData.warehouseCode) {
        return '';
      }
      const warehouse = this.warehouseOptions.find(w => w.warehouseCode === this.editFormData.warehouseCode);
      return warehouse ? warehouse.status : '';
    }
  }
};
</script>

<style lang="scss" scoped>
/* 只读字段样式 */
:deep(.readonly-input) {
  .el-input__inner {
    background-color: var(--base-menu-background, #f5f7fa) !important;
    color: var(--base-color-3, #909399) !important;
    cursor: not-allowed;
    border-color: var(--border-color-1, #dcdfe6) !important;
  }
}

/* 表单字段居中样式 */
:deep(.centered-form) {
  .el-form-item__content {
    text-align: center;

    .el-input,
    .el-select,
    .el-input-number,
    .el-date-editor,
    .el-textarea {
      text-align: center;

      .el-input__inner,
      .el-textarea__inner {
        text-align: center;
      }

      .el-input-number__decrease,
      .el-input-number__increase {
        line-height: 1;
      }
    }

    .el-select .el-input__inner {
      text-align: center;
    }
  }
}

/* 表格字段居中样式 */
:deep(.el-table) {
  .el-table__body-wrapper {
    .el-table__body {
      td {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }

  .el-table__header-wrapper {
    .el-table__header {
      th {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }
}

/* 深色主题适配 */
.theme-dark {
  :deep(.readonly-input) {
    .el-input__inner {
      background-color: var(--base-item-bg, #2d3748) !important;
      color: var(--base-color-3, #a0aec0) !important;
      border-color: var(--border-color-1, #4a5568) !important;
    }
  }
}
.warehouse-zone-management {
  .mb8 {
    margin-bottom: 8px;
  }
  padding: 20px;
  background: var(--base-main-bg);

  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
  }

  .operation-section {
    margin-bottom: 20px;

    .el-button {
      margin-right: 10px;
    }
  }

  .table-section {
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);

    .usage-text {
      font-size: 12px;
      color: var(--theme-color);
      margin-left: 8px;
    }

    .qr-time {
      font-size: 11px;
      color: var(--text-color-3);
      display: block;
      margin-top: 2px;
    }
  }

  .pagination-section {
    margin-top: 20px;
    text-align: center;
  }

  .zone-detail {
    .el-descriptions {
      background: var(--base-main-bg);
    }
  }

  .text-danger {
    color: #f56c6c;
  }

  .text-success {
    color: #67c23a;
  }
}

// 表单样式适配
:deep(.el-form) {
  .el-form-item__label {
    color: var(--theme-color);
    font-weight: 500;
  }

  .el-input__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);

    &:focus {
      border-color: var(--current-color);
    }
  }

  .el-select .el-input__inner {
    background: var(--base-main-bg);
  }

  .el-textarea__inner {
    background: var(--base-main-bg);
    border-color: var(--border-color-1);
    color: var(--theme-color);

    &:focus {
      border-color: var(--current-color);
    }
  }
}

// 表格样式适配
:deep(.el-table) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);

  th {
    background: var(--base-color-9);
    color: var(--theme-color);
    border-bottom-color: var(--border-color-1);
  }

  td {
    border-bottom-color: var(--border-color-1);
    color: var(--theme-color);
    background: var(--base-main-bg);
  }

  tr:hover td {
    background: var(--table-row-hover-bg);
  }

  .el-button--text {
    color: var(--current-color);

    &:hover {
      color: var(--color-2);
    }
  }
}

// 弹窗样式适配
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);

  .el-dialog__title {
    color: var(--theme-color);
  }
}

// 进度条样式适配
:deep(.el-progress) {
  .el-progress__text {
    color: var(--theme-color);
    }
}
</style>

<!-- 全局样式，确保二维码弹窗功能正常 -->
<style lang="scss">
// 确保弹窗在所有主题下都能正常显示和使用
.el-message-box__wrapper.qr-code-confirm-dialog {
  .el-message-box {
    min-width: 480px;
    max-width: 600px;
  }
}

// 批量打印二维码弹窗样式
.el-message-box__wrapper.batch-qr-code-confirm-dialog {
  .el-message-box {
    min-width: 600px;
    max-width: 800px;
  }

  .batch-qr-info-container {
    text-align: left;

    .batch-qr-info-item {
      margin: 8px 0;
      padding: 8px 12px;
      background: #f7f9fc;
      border-radius: 4px;
      font-size: 14px;

      &:first-child {
        font-weight: bold;
        color: #409eff;
      }
    }

    .batch-qr-zone-list {
      margin: 8px 0;
      padding: 8px;
      background: #f0f2f5;
      border-radius: 4px;
      max-height: 120px;
      overflow-y: auto;

      .zone-tag {
        display: inline-block;
        margin: 2px 4px;
        padding: 2px 8px;
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        border-radius: 3px;
        font-size: 12px;
        color: #0050b3;
      }
    }

    .batch-qr-action-tip {
      margin-top: 12px;
      padding: 12px;
      background: linear-gradient(135deg, #e6f7ff, #f6ffed);
      border-left: 4px solid #52c41a;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
      color: #389e0d;
    }
  }
}

// 深色主题全局适配
.theme-dark {
  .qr-code-confirm-dialog,
  .batch-qr-code-confirm-dialog {
    .el-message-box {
      background-color: var(--dialog-background) !important;

      .el-message-box__header,
      .el-message-box__content,
      .el-message-box__btns {
        background-color: var(--dialog-background) !important;
      }

      .qr-info-item,
      .batch-qr-info-item {
        background-color: var(--base-menu-background-active) !important;
        color: var(--theme-color) !important;
      }

      .qr-content-preview {
        background-color: var(--primary-color) !important;
      }

      .qr-action-tip,
      .batch-qr-action-tip {
        background: linear-gradient(135deg, var(--base-menu-background-active), var(--table-row-hover-bg)) !important;
        border-left-color: var(--current-color) !important;
        color: var(--theme-color) !important;
      }

      .batch-qr-zone-list {
        background-color: var(--base-menu-background) !important;

        .zone-tag {
          background-color: var(--base-color-9) !important;
          border-color: var(--border-color-1) !important;
          color: var(--current-color) !important;
        }
      }

      .el-button:not(.el-button--primary) {
        background-color: var(--primary-color) !important;
        border-color: var(--input-border-color) !important;
      }
    }
  }
}

// 确保复制功能正常工作
.qr-copy-btn {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// 二维码内容可选择复制
.qr-content-preview {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
</style>

<!-- 全局样式覆盖，确保主题适配正常工作 -->
<style lang="scss">
// 针对当前组件强制应用主题样式
.warehouse-zone-management {
  // 表单样式适配
  .el-form {
    .el-form-item__label {
      color: var(--theme-color) !important;
      font-weight: 500;
    }

    .el-input__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;

      &:focus {
        border-color: var(--current-color) !important;
      }

      &::placeholder {
        color: var(--text-color-3) !important;
      }
    }

    .el-select {
      .el-input__inner {
        background: var(--base-main-bg) !important;
        color: var(--theme-color) !important;
      }

      .el-input__suffix {
        color: var(--theme-color) !important;
      }
    }

    .el-textarea__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;

      &:focus {
        border-color: var(--current-color) !important;
      }

      &::placeholder {
        color: var(--text-color-3) !important;
      }
    }

    .el-form-item__error {
      color: #f56c6c !important;
    }
  }

  // 表格样式适配
  .el-table {
    background: var(--base-main-bg) !important;
    border-color: var(--border-color-1) !important;
    color: var(--theme-color) !important;

    .el-table__header {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
    }

    .el-table__row {
      background: var(--base-main-bg) !important;

      &:hover {
        background: var(--table-row-hover-bg) !important;
      }
    }

    th {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    td {
      border-bottom-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;
      background: var(--base-main-bg) !important;
    }

    tr:hover td {
      background: var(--table-row-hover-bg) !important;
    }

    .el-button--text {
      color: var(--current-color) !important;

      &:hover {
        color: var(--color-2) !important;
      }
    }

    .el-table__empty-text {
      color: var(--theme-color) !important;
    }

    .el-checkbox__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;

      &:hover {
        border-color: var(--current-color) !important;
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background: var(--current-color) !important;
      border-color: var(--current-color) !important;
    }
  }

  // 弹窗样式适配
  .el-dialog {
    background: var(--base-main-bg) !important;
    border: 1px solid var(--border-color-1) !important;

    .el-dialog__header {
      background: var(--base-main-bg) !important;
      border-bottom: 1px solid var(--border-color-1) !important;
    }

    .el-dialog__title {
      color: var(--theme-color) !important;
    }

    .el-dialog__body {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
    }

    .el-dialog__footer {
      background: var(--base-main-bg) !important;
      border-top: 1px solid var(--border-color-1) !important;

      .el-button {
        background-color: var(--base-main-bg) !important;
        border-color: var(--border-color-1) !important;
        color: var(--theme-color) !important;

        &.el-button--primary {
          background: var(--current-color) !important;
          border-color: var(--current-color) !important;
          color: #fff !important;

          &:hover {
            background: var(--color-2) !important;
            border-color: var(--color-2) !important;
          }
        }

        &:hover {
          background: var(--table-row-hover-bg) !important;
        }
      }
    }

    .el-dialog__close {
      color: var(--theme-color) !important;

      &:hover {
        color: var(--current-color) !important;
      }
    }
  }

  // 描述列表样式适配
  .el-descriptions {
    .el-descriptions__header {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    .el-descriptions__body {
      background: var(--base-main-bg) !important;

      .el-descriptions__cell {
        border-color: var(--border-color-1) !important;
        color: var(--theme-color) !important;
      }

      .el-descriptions__label {
        color: var(--theme-color) !important;
        font-weight: 500;
      }
    }
  }

  // 下拉菜单样式适配
  .el-dropdown-menu {
    background: var(--base-main-bg) !important;
    border-color: var(--border-color-1) !important;

    .el-dropdown-menu__item {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;

      &:hover {
        background: var(--table-row-hover-bg) !important;
      }
    }
  }
}

// 二维码确认弹窗样式 - 完整主题适配版本
.qr-code-confirm-dialog {
  .el-message-box {
    background-color: var(--base-main-bg) !important;
    border: 1px solid var(--border-color-1) !important;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

    // 深色主题适配
    .theme-dark & {
      background-color: var(--dialog-background) !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

          .el-message-box__header {
        background-color: var(--base-main-bg) !important;
        padding: 20px 24px 10px;
        border-bottom: 1px solid var(--border-color-1);

        // 深色主题适配
        .theme-dark & {
          background-color: var(--dialog-background) !important;
        }

      .el-message-box__title {
        color: var(--theme-color) !important;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;

        &::before {
          content: "🖨️";
          margin-right: 8px;
          font-size: 20px;
        }
      }

      .el-message-box__headerbtn {
        .el-message-box__close {
          color: var(--theme-color) !important;
          font-size: 16px;

          &:hover {
            color: var(--current-color) !important;
            transform: scale(1.1);
          }
        }
      }
    }

          .el-message-box__content {
        background-color: var(--base-main-bg) !important;
        padding: 16px 24px;
        color: var(--theme-color) !important;

        // 深色主题适配
        .theme-dark & {
          background-color: var(--dialog-background) !important;
        }

      .el-message-box__message {
        color: var(--theme-color) !important;
        line-height: 1.6;

        .qr-info-container {
          padding: 12px 0;

                     .qr-info-item {
             display: flex;
             align-items: center;
             margin-bottom: 12px;
             padding: 12px 16px;
             background-color: var(--base-color-9);
             border-radius: 8px;
             border: 1px solid var(--border-color-1);
             font-size: 14px;
             line-height: 1.5;
             transition: all 0.2s ease;

             // 深色主题适配
             .theme-dark & {
               background-color: var(--base-menu-background-active);
             }

             &:hover {
               background-color: var(--table-row-hover-bg);
               border-color: var(--current-color);
             }

             &:last-child {
               margin-bottom: 0;
             }

            .qr-content-container {
              display: flex;
              align-items: center;
              flex: 1;
              margin-left: 10px;

                             .qr-content-preview {
                 flex: 1;
                 padding: 8px 12px;
                 background-color: var(--base-main-bg);
                 border: 1px solid var(--border-color-1);
                 border-radius: 6px;
                 font-family: 'Courier New', Consolas, monospace;
                 font-size: 12px;
                 color: var(--theme-color);
                 word-break: break-all;
                 cursor: help;
                 transition: all 0.2s ease;
                 max-height: 60px;
                 overflow-y: auto;

                 // 深色主题适配
                 .theme-dark & {
                   background-color: var(--primary-color);
                 }

                 &:hover {
                   border-color: var(--current-color);
                   background-color: var(--table-row-hover-bg);
                   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                 }
               }

              .qr-copy-btn {
                margin-left: 12px;
                padding: 8px 12px;
                background: var(--current-color);
                color: #fff;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 4px;

                &:hover {
                  background: var(--color-2);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                &:active {
                  transform: translateY(0);
                }
              }
            }

            .version-tag {
              background: var(--current-color);
              color: #fff;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              font-weight: 500;
              margin-left: 8px;
              letter-spacing: 0.5px;
            }
          }

                     .qr-action-tip {
             margin-top: 20px;
             padding: 16px;
             background: linear-gradient(135deg, var(--base-color-9), var(--table-row-hover-bg));
             border: 2px dashed var(--current-color);
             border-radius: 8px;
             text-align: center;
             font-weight: 500;
             color: var(--current-color);
             position: relative;

             // 深色主题适配
             .theme-dark & {
               background: linear-gradient(135deg, var(--base-menu-background-active), var(--table-row-hover-bg));
             }

             &::before {
               content: "💡";
               display: block;
               font-size: 24px;
               margin-bottom: 8px;
             }
           }
        }
      }
    }

          .el-message-box__btns {
        background-color: var(--base-main-bg) !important;
        padding: 16px 24px 20px;
        border-top: 1px solid var(--border-color-1);
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        // 深色主题适配
        .theme-dark & {
          background-color: var(--dialog-background) !important;
        }

              .el-button {
          background-color: var(--base-main-bg) !important;
          border-color: var(--border-color-1) !important;
          color: var(--theme-color) !important;
          padding: 10px 20px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          // 深色主题适配
          .theme-dark & {
            background-color: var(--primary-color) !important;
            border-color: var(--input-border-color) !important;
          }

        &.el-button--primary {
          background: var(--current-color) !important;
          border-color: var(--current-color) !important;
          color: #fff !important;

          &:hover {
            background: var(--color-2) !important;
            border-color: var(--color-2) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }

                  &.el-button--default {
            &:hover {
              background-color: var(--table-row-hover-bg) !important;
              border-color: var(--current-color) !important;
              color: var(--current-color) !important;
              transform: translateY(-1px);
            }
          }

        &:active {
          transform: translateY(0) !important;
        }
      }
    }
  }
}

// 操作列按钮样式适配
:deep(.el-table) {
  .el-button--text {
    color: var(--current-color) !important;
    background: transparent !important;
    border: none !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;

    &:hover {
      color: var(--base-menu-color-active) !important;
      background-color: var(--current-color) !important;
      border-radius: 4px !important;
    }

    &:focus {
      color: var(--current-color) !important;
      background: transparent !important;
    }

    // 为不同类型的操作按钮设置不同颜色
    &.view-btn {
      color: var(--color-2) !important;

      &:hover {
        background-color: var(--color-2) !important;
        color: #fff !important;
      }
    }

    &.edit-btn {
      color: #e6a23c !important;

      &:hover {
        background-color: #e6a23c !important;
        color: #fff !important;
      }
    }

    &.delete-btn {
      color: #f56c6c !important;

      &:hover {
        background-color: #f56c6c !important;
        color: #fff !important;
      }
    }

    &.more-btn {
      color: var(--base-color-3) !important;

      &:hover {
        background-color: var(--base-menu-background-active) !important;
        color: var(--base-menu-color-active) !important;
      }
    }
  }
}

/* 停用状态提示样式 */
.warehouse-info, .status-info {
  .warehouse-status-tip, .zone-status-tip {
    display: flex;
    align-items: center;

    i {
      margin-right: 4px;
    }
  }
}

/* 表单中的仓库警告提示样式 */
.warehouse-warning-tip {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  border-radius: 4px;
  animation: fadeIn 0.3s ease-in-out;

  i {
    margin-right: 6px;
    font-size: 14px;
  }

  /* 暗色主题下的样式 */
  .theme-dark & {
    background-color: rgba(230, 162, 60, 0.1);
    border-color: rgba(230, 162, 60, 0.3);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表格行停用状态样式 */
:deep(.el-table) {
  .disabled-zone-row {
    background-color: rgba(245, 108, 108, 0.05) !important;

    &:hover > td {
      background-color: rgba(245, 108, 108, 0.1) !important;
    }

    td {
      border-bottom-color: rgba(245, 108, 108, 0.2) !important;
    }
  }

  .disabled-warehouse-row {
    background-color: rgba(230, 162, 60, 0.05) !important;

    &:hover > td {
      background-color: rgba(230, 162, 60, 0.1) !important;
    }

    td {
      border-bottom-color: rgba(230, 162, 60, 0.2) !important;
    }
  }

  /* 暗色主题下的表格行样式 */
  .theme-dark & {
    .disabled-zone-row {
      background-color: rgba(245, 108, 108, 0.08) !important;

      &:hover > td {
        background-color: rgba(245, 108, 108, 0.15) !important;
      }
    }

    .disabled-warehouse-row {
      background-color: rgba(230, 162, 60, 0.08) !important;

      &:hover > td {
        background-color: rgba(230, 162, 60, 0.15) !important;
      }
    }
  }
}

/* 层级导航模式样式 */
.zone-name-clickable {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
}

/* 表格居中对齐样式 */
.table-section {
  ::v-deep .el-table {
    th {
      text-align: center;
    }

    td {
      text-align: center;
    }

    .cell {
      text-align: center;
    }
  }
}
</style>

<!-- 全局样式，用于下拉框选项的停用状态显示 -->
<style lang="scss">
/* 仓库选择下拉框停用状态样式 */
.el-select-dropdown .el-option {
  &.disabled-warehouse-option {
    background-color: #fdf6ec !important;
    border-left: 3px solid #e6a23c !important;
    opacity: 0.8;

    .disabled-text {
      color: #e6a23c !important;
      font-size: 12px;
      margin-left: 8px;
    }
  }

  /* 暗色主题下的停用状态样式 */
  .theme-dark & {
    &.disabled-warehouse-option {
      background-color: rgba(230, 162, 60, 0.1) !important;
    }
  }
}
</style>

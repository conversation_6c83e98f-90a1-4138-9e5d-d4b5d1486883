<template>
  <div class="warehouse-management">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.warehouseName"
            placeholder="请输入仓库名称"
            prefix-icon="el-icon-search"
            clearable
            @clear="handleSearch"
            @keyup.enter.native="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.warehouseType"
            placeholder="请选择仓库类型"
            clearable
            @clear="handleSearch"
            @change="handleSearch"
          >
            <el-option
              v-for="item in warehouseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            @clear="handleSearch"
            @change="handleSearch"
          >
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-section">
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >
        新增仓库
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
      <el-button type="success" icon="el-icon-download" @click="handleExport">
        导出数据
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="warehouseCode" label="仓库编码" width="130" align="center" />
        <el-table-column prop="warehouseName" label="仓库名称" min-width="120" align="center">
          <template slot-scope="scope">
            <span
              :class="{ 'warehouse-name-clickable': hierarchicalMode }"
              @click="handleWarehouseNameClick(scope.row)"
            >
              {{ scope.row.warehouseName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseType" label="仓库类型" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getWarehouseTypeTag(scope.row.warehouseType)">
              {{ getWarehouseTypeLabel(scope.row.warehouseType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseAddress" label="仓库位置" min-width="150" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
              {{ scope.row.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <!-- 主要操作按钮 -->
              <el-button type="text" size="small" class="view-btn" @click="handleView(scope.row)">
                <i class="el-icon-view"></i>
                查看
              </el-button>
              <el-button type="text" size="small" class="edit-btn" @click="handleEdit(scope.row)">
                <i class="el-icon-edit"></i>
                修改
              </el-button>

              <!-- 更多操作下拉菜单 -->
              <el-dropdown size="small" @command="handleCommand($event, scope.row)">
                <el-button type="text" size="small" class="more-btn">
                  更多
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="manageZones">
                    <i class="el-icon-place"></i>
                    区域管理
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="scope.row.status === '1' ? 'disable' : 'enable'"
                    :divided="true"
                  >
                    <i :class="scope.row.status === '1' ? 'el-icon-close' : 'el-icon-check'"></i>
                    {{ scope.row.status === '1' ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" class="danger-item">
                    <i class="el-icon-delete"></i>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 查看详情弹窗 -->
    <el-dialog
      title="仓库详情"
      :visible.sync="viewDialogVisible"
      width="600px"
    >
      <div class="warehouse-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="仓库编码">
            {{ viewData.warehouseCode }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库名称">
            {{ viewData.warehouseName }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库类型">
            <el-tag :type="getWarehouseTypeTag(viewData.warehouseType)">
              {{ getWarehouseTypeLabel(viewData.warehouseType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewData.status === '1' ? 'success' : 'danger'">
              {{ viewData.status === '1' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="仓库位置">
            {{ viewData.warehouseAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ viewData.manager }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ viewData.contactPhone }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ viewData.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ viewData.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 区域概览弹窗 -->
    <el-dialog
      :title="`${zoneOverviewData.warehouseName} - 区域概览`"
      :visible.sync="zoneOverviewVisible"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="zone-overview">
        <!-- 统计卡片 -->
        <el-row :gutter="20" class="mb16">
          <el-col :span="6">
            <el-card class="stat-card total">
              <div class="stat-content">
                <div class="stat-value">{{ zoneStats.totalZones }}</div>
                <div class="stat-label">总区域数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card active">
              <div class="stat-content">
                <div class="stat-value">{{ zoneStats.activeZones }}</div>
                <div class="stat-label">启用区域</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card inactive">
              <div class="stat-content">
                <div class="stat-value">{{ zoneStats.inactiveZones }}</div>
                <div class="stat-label">停用区域</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card qr">
              <div class="stat-content">
                <div class="stat-value">{{ zoneStats.qrCodeZones }}</div>
                <div class="stat-label">已生成二维码</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 区域列表 -->
        <el-table :data="zoneOverviewData.zones" v-loading="zoneOverviewLoading" max-height="300">
          <el-table-column prop="zoneCode" label="区域编码" width="120" />
          <el-table-column prop="zoneName" label="区域名称" min-width="150" />
          <el-table-column prop="zoneType" label="区域类型" width="100">
            <template slot-scope="scope">
              <el-tag size="small" :type="getZoneTypeTag(scope.row.zoneType)">
                {{ getZoneTypeLabel(scope.row.zoneType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="二维码状态" width="100">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.zoneQrCode ? 'success' : 'warning'"
                size="small"
              >
                {{ scope.row.zoneQrCode ? '已生成' : '未生成' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'" size="small">
                {{ scope.row.status === '1' ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="150" />
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="zoneOverviewVisible = false">关闭</el-button>
        <el-button type="primary" @click="goToZoneManagement">进入详细管理</el-button>
      </div>
    </el-dialog>

    <!-- 新增仓库弹窗 -->
    <el-dialog
      title="新增仓库"
      :visible.sync="addDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addForm"
        :model="addFormData"
        :rules="addFormRules"
        label-width="100px"
        class="centered-form"
      >
        <el-row :gutter="14">
          <el-col :span="12">
            <el-form-item label="仓库编码" prop="warehouseCode">
              <el-input
                v-model="addFormData.warehouseCode"
                placeholder="仓库编码将自动生成"
                readonly
                class="readonly-input"
                style="min-width: 130px;"
              >
                <!-- <template slot="append">
                  <el-button
                    @click="handleGenerateWarehouseCode"
                    :disabled="!addFormData.warehouseType"
                    icon="el-icon-refresh"
                    title="重新生成编码"
                    size="small"
                  >
                    {{ addFormData.warehouseCode ? '重新生成' : '生成' }}
                  </el-button>
                </template> -->
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input
                v-model="addFormData.warehouseName"
                placeholder="请输入仓库名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="仓库类型" prop="warehouseType">
              <el-select
                v-model="addFormData.warehouseType"
                placeholder="请选择仓库类型"
                style="width: 100%"
                @change="handleWarehouseTypeChange"
              >
                <el-option
                  v-for="item in warehouseTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="addFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="启用" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="仓库地址" prop="warehouseAddress">
          <el-input
            v-model="addFormData.warehouseAddress"
            placeholder="请输入仓库地址"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input
                v-model="addFormData.manager"
                placeholder="请输入负责人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input
                v-model="addFormData.contactPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="addFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入仓库描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="addFormData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit" :loading="addLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 修改仓库弹窗 -->
    <el-dialog
      title="修改仓库"
      :visible.sync="editDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editForm"
        :model="editFormData"
        :rules="editFormRules"
        label-width="100px"
        class="centered-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="仓库编码" prop="warehouseCode">
              <el-input
                v-model="editFormData.warehouseCode"
                placeholder="请输入仓库编码"
                disabled
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input
                v-model="editFormData.warehouseName"
                placeholder="请输入仓库名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="仓库类型" prop="warehouseType">
              <el-select
                v-model="editFormData.warehouseType"
                placeholder="请选择仓库类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in warehouseTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="editFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="启用" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="仓库地址" prop="warehouseAddress">
          <el-input
            v-model="editFormData.warehouseAddress"
            placeholder="请输入仓库地址"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input
                v-model="editFormData.manager"
                placeholder="请输入负责人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input
                v-model="editFormData.contactPhone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入仓库描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editFormData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEditSubmit" :loading="editLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWarehouse,
  getWarehouse,
  addWarehouse,
  updateWarehouse,
  delWarehouse,
  delBatchWarehouse,
  changeWarehouseStatus,
  exportWarehouse,
  getWarehouseTypeOptions,
  checkWarehouseCodeUnique,
  generateWarehouseCode
} from '@/api/inventory/warehouse';
import { listZone } from '@/api/inventory/zone';

export default {
  name: 'WarehouseManagement',
  props: {
    // 是否为层级导航模式
    hierarchicalMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        warehouseName: '',
        warehouseType: '',
        status: ''
      },
      warehouseTypeOptions: [
        { label: '原料仓库', value: '1' },
        { label: '半成品仓库', value: '2' },
        { label: '成品仓库', value: '3' },
        { label: '零件仓库', value: '4' }
      ],
      tableData: [],
      selectedRows: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      viewDialogVisible: false,
      viewData: {},
      // 新增仓库相关
      addDialogVisible: false,
      addLoading: false,
      addFormData: {
        warehouseCode: '',
        warehouseName: '',
        warehouseType: '',
        warehouseAddress: '',
        manager: '',
        contactPhone: '',
        status: '1',
        description: '',
        remark: ''
      },
      addFormRules: {
        warehouseCode: [
          { required: true, message: '仓库编码不能为空', trigger: 'change' }
        ],
        warehouseName: [
          { required: true, message: '请输入仓库名称', trigger: 'blur' },
          { min: 2, max: 100, message: '仓库名称长度在2到100个字符', trigger: 'blur' }
        ],
        warehouseType: [
          { required: true, message: '请选择仓库类型', trigger: 'change' }
        ],
        warehouseAddress: [
          { required: true, message: '请输入仓库地址', trigger: 'blur' }
        ],
        contactPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      editDialogVisible: false,
      editLoading: false,
      // 区域概览相关
      zoneOverviewVisible: false,
      zoneOverviewLoading: false,
      zoneOverviewData: {
        warehouseId: '',
        warehouseName: '',
        zones: []
      },
      zoneStats: {
        totalZones: 0,
        activeZones: 0,
        inactiveZones: 0,
        qrCodeZones: 0
      },
      editFormData: {
        warehouseId: '',
        warehouseCode: '',
        warehouseName: '',
        warehouseType: '',
        warehouseAddress: '',
        manager: '',
        contactPhone: '',
        status: '1',
        description: '',
        remark: ''
      },
      editFormRules: {
        warehouseName: [
          { required: true, message: '请输入仓库名称', trigger: 'blur' },
          { min: 2, max: 100, message: '仓库名称长度在2到100个字符', trigger: 'blur' }
        ],
        warehouseType: [
          { required: true, message: '请选择仓库类型', trigger: 'change' }
        ],
        warehouseAddress: [
          { required: true, message: '请输入仓库地址', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        contactPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    // 获取数据
    fetchData() {
      this.loading = true;
      const params = {
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        warehouseName: this.searchForm.warehouseName,
        warehouseType: this.searchForm.warehouseType,
        status: this.searchForm.status
      };

      listWarehouse(params).then(response => {
        this.tableData = response.rows || [];
        this.pagination.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取仓库列表失败:', error);
        this.$message.error('获取仓库列表失败');
        this.loading = false;
      });
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        warehouseName: '',
        warehouseType: '',
        status: ''
      };
      this.handleSearch();
    },

    // 查看详情
    handleView(row) {
      this.loading = true;
      getWarehouse(row.warehouseId).then(response => {
        this.viewData = response.data || {};
        this.viewDialogVisible = true;
        this.loading = false;
      }).catch(error => {
        console.error('获取仓库详情失败:', error);
        this.$message.error('获取仓库详情失败');
        this.loading = false;
      });
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'manageZones':
          this.handleManageZones(row);
          break;
        case 'enable':
        case 'disable':
          this.handleToggleStatus(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
        default:
          console.warn('未知的操作命令:', command);
      }
    },

    // 处理仓库名称点击（层级导航模式）
    handleWarehouseNameClick(row) {
      if (this.hierarchicalMode) {
        console.log('层级导航模式 - 仓库名称点击:', row);
        // 直接触发区域管理事件，不显示弹窗
        const warehouse = {
          warehouseId: row.warehouseId,
          warehouseName: row.warehouseName,
          warehouseCode: row.warehouseCode,
          warehouseType: row.warehouseType,
          warehouseTypeName: this.getWarehouseTypeLabel(row.warehouseType)
        };
        this.$emit('manage-zones', warehouse);
      }
    },

    // 区域管理
    handleManageZones(row) {
      // 修复：无论在哪种导航模式下，操作按钮都应该显示区域概览弹窗
      // 这样保持了按钮行为的一致性
      this.showZoneOverview(row);
    },

    // 显示区域概览
    showZoneOverview(warehouse) {
      this.zoneOverviewData.warehouseId = warehouse.warehouseId;
      this.zoneOverviewData.warehouseName = warehouse.warehouseName;
      this.zoneOverviewData.warehouseCode = warehouse.warehouseCode;
      this.zoneOverviewData.warehouseType = warehouse.warehouseType;
      this.zoneOverviewData.warehouseTypeName = this.getWarehouseTypeLabel(warehouse.warehouseType);
      this.zoneOverviewVisible = true;
      this.fetchZoneOverview(warehouse.warehouseId);
    },

    // 获取区域概览数据
    fetchZoneOverview(warehouseId) {
      this.zoneOverviewLoading = true;

      // 构建查询参数，确保只查询当前仓库的区域
      const params = {
        warehouseCode: this.zoneOverviewData.warehouseCode,  // 使用仓库编码进行筛选
        pageSize: 999  // 设置较大的页面大小以获取所有数据
      };

      console.log('获取区域概览，查询参数:', params);

      listZone(params).then(response => {
        console.log('区域概览数据响应:', response);
        if (response.code === 200) {
          const zones = response.rows || [];
          console.log('获取到的区域数据:', {
            warehouseCode: this.zoneOverviewData.warehouseCode,
            totalZones: zones.length,
            zones: zones
          });

          this.zoneOverviewData.zones = zones;

          // 计算统计数据
          this.zoneStats = {
            totalZones: zones.length,
            activeZones: zones.filter(zone => zone.status === '1').length,
            inactiveZones: zones.filter(zone => zone.status === '0').length,
            qrCodeZones: zones.filter(zone => zone.zoneQrCode).length
          };

          console.log('区域统计数据:', this.zoneStats);
        } else {
          console.error('获取区域数据失败:', response.msg);
          this.$message.error(response.msg || '获取区域数据失败');
        }
      }).catch(error => {
        console.error('获取区域概览失败:', error);
        this.$message.error('获取区域概览失败');
      }).finally(() => {
        this.zoneOverviewLoading = false;
      });
    },

    // 进入详细管理
    goToZoneManagement() {
      const warehouse = {
        warehouseId: this.zoneOverviewData.warehouseId,
        warehouseName: this.zoneOverviewData.warehouseName,
        warehouseCode: this.zoneOverviewData.warehouseCode,
        warehouseType: this.zoneOverviewData.warehouseType,
        warehouseTypeName: this.zoneOverviewData.warehouseTypeName
      };

      // 关闭弹窗
      this.zoneOverviewVisible = false;

      // 向父组件发送事件，传递仓库信息
      this.$emit('manage-zones', warehouse);
    },

    // 新增仓库
    handleAdd() {
      this.addFormData = {
        warehouseCode: '',
        warehouseName: '',
        warehouseType: '',
        warehouseAddress: '',
        manager: '',
        contactPhone: '',
        status: '1',
        description: '',
        remark: ''
      };
      this.addDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.addForm) {
          this.$refs.addForm.clearValidate();
        }
      });
    },

    // 仓库类型变化时自动生成编码
    async handleWarehouseTypeChange() {
      if (this.addFormData.warehouseType) {
        await this.handleGenerateWarehouseCode();
      }
    },

    // 生成仓库编码
    async handleGenerateWarehouseCode() {
      if (!this.addFormData.warehouseType) {
        this.$message.warning('请先选择仓库类型');
        return;
      }

      try {
        const response = await generateWarehouseCode(this.addFormData.warehouseType);
        if (response.code === 200) {
          this.addFormData.warehouseCode = response.data;
          this.$message.success('仓库编码生成成功');
        } else {
          this.$message.error(response.msg || '生成仓库编码失败');
        }
      } catch (error) {
        console.error('生成仓库编码失败:', error);
        this.$message.error('生成仓库编码失败');
      }
    },

    // 检查仓库编码唯一性
    checkWarehouseCodeUnique() {
      if (!this.addFormData.warehouseCode || this.addFormData.warehouseCode.length < 2) {
        return;
      }
      checkWarehouseCodeUnique(this.addFormData.warehouseCode).then(response => {
        if (response.data === false) {
          this.$message.warning('仓库编码已存在，请重新生成');
          this.$refs.addForm.validateField('warehouseCode');
        }
      }).catch(error => {
        console.error('检查仓库编码失败:', error);
      });
    },

    // 提交新增
    handleAddSubmit() {
      this.$refs.addForm.validate(valid => {
        if (!valid) {
          return;
        }

        this.addLoading = true;
        addWarehouse(this.addFormData).then(() => {
          this.$message.success('新增成功');
          this.addDialogVisible = false;
          this.fetchData();
        }).catch(error => {
          console.error('新增仓库失败:', error);
          this.$message.error('新增失败');
        }).finally(() => {
          this.addLoading = false;
        });
      });
    },

    // 修改仓库
    handleEdit(row) {
      this.loading = true;
      getWarehouse(row.warehouseId).then(response => {
        const data = response.data || {};
        this.editFormData = {
          warehouseId: data.warehouseId,
          warehouseCode: data.warehouseCode,
          warehouseName: data.warehouseName,
          warehouseType: data.warehouseType,
          warehouseAddress: data.warehouseAddress,
          manager: data.manager,
          contactPhone: data.contactPhone,
          status: data.status,
          description: data.description,
          remark: data.remark
        };
        this.editDialogVisible = true;
        this.loading = false;
        // 清除表单验证
        this.$nextTick(() => {
          if (this.$refs.editForm) {
            this.$refs.editForm.clearValidate();
          }
        });
      }).catch(error => {
        console.error('获取仓库详情失败:', error);
        this.$message.error('获取仓库详情失败');
        this.loading = false;
      });
    },

    // 提交修改
    handleEditSubmit() {
      this.$refs.editForm.validate(valid => {
        if (!valid) {
          return;
        }

        this.editLoading = true;
        updateWarehouse(this.editFormData).then(() => {
          this.$message.success('修改成功');
          this.editDialogVisible = false;
          this.fetchData();
        }).catch(error => {
          console.error('修改仓库失败:', error);
          this.$message.error('修改失败');
        }).finally(() => {
          this.editLoading = false;
        });
      });
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定删除该仓库吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        delWarehouse(row.warehouseId).then(() => {
          this.$message.success('删除成功');
          this.fetchData();
        }).catch(error => {
          console.error('删除仓库失败:', error);
          this.$message.error('删除失败');
          this.loading = false;
        });
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }
      this.$confirm(`确定删除选中的${this.selectedRows.length}条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        const warehouseIds = this.selectedRows.map(row => row.warehouseId).join(',');
        delBatchWarehouse(warehouseIds).then(() => {
          this.$message.success('批量删除成功');
          this.selectedRows = [];
          this.fetchData();
        }).catch(error => {
          console.error('批量删除仓库失败:', error);
          this.$message.error('批量删除失败');
          this.loading = false;
        });
      });
    },

    // 切换状态
    handleToggleStatus(row) {
      const status = row.status === '1' ? '0' : '1';
      const statusText = status === '1' ? '启用' : '停用';

      // 如果是停用操作，需要特别提醒会级联停用区域
      let confirmMessage = `确定${statusText}该仓库吗？`;
      if (status === '0') {
        confirmMessage = `确定停用该仓库吗？\n\n⚠️ 警告：停用仓库将会同时停用该仓库下的所有区域，这可能影响相关的库存操作。`;
      }

      this.$confirm(confirmMessage, `${statusText}仓库`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: status === '0' ? 'warning' : 'info',
        customClass: 'warehouse-status-confirm',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.loading = true;
        changeWarehouseStatus([row.warehouseId], status).then((response) => {
          if (response.code === 200) {
            row.status = status;
            if (status === '0') {
              this.$message({
                type: 'success',
                message: `仓库停用成功，已同时停用该仓库下的所有区域`,
                duration: 3000
              });
            } else {
              this.$message.success(`${statusText}成功`);
            }
          } else {
            this.$message.error(response.msg || `${statusText}失败`);
          }
          this.loading = false;
        }).catch(error => {
          console.error('切换仓库状态失败:', error);
          this.$message.error(`${statusText}失败`);
          this.loading = false;
        });
      });
    },

    // 导出
    handleExport() {
      const params = {
        warehouseName: this.searchForm.warehouseName,
        warehouseType: this.searchForm.warehouseType,
        status: this.searchForm.status
      };

      this.download('system/warehouse/export', params, `warehouse_${new Date().getTime()}.xlsx`);
    },

    // 表格选择
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    // 分页
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.fetchData();
    },

    // 获取仓库类型标签样式
    getWarehouseTypeTag(type) {
      const tagMap = {
        '1': 'primary',
        '2': 'warning',
        '3': 'info',
        '4': 'success'
      };
      return tagMap[type] || 'info';
    },

    // 获取仓库类型标签文本
    getWarehouseTypeLabel(type) {
      const typeMap = {
        '1': '原料仓库',
        '2': '半成品仓库',
        '3': '成品仓库',
        '4': '零件仓库'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取区域类型标签样式
    getZoneTypeTag(type) {
      const tagMap = {
        'storage': 'primary',
        'picking': 'success',
        'staging': 'warning',
        'inspection': 'info'
      };
      return tagMap[type] || 'info';
    },

    // 获取区域类型标签文本
    getZoneTypeLabel(type) {
      const typeMap = {
        'storage': '储存区',
        'picking': '拣货区',
        'staging': '暂存区',
        'inspection': '检验区'
      };
      return typeMap[type] || '未知类型';
    }
  }
};
</script>

<style lang="scss" scoped>
/* 只读字段样式 */
:deep(.readonly-input) {
  .el-input__inner {
    background-color: var(--base-menu-background, #f5f7fa) !important;
    color: var(--base-color-3, #909399) !important;
    cursor: not-allowed;
    border-color: var(--border-color-1, #dcdfe6) !important;
  }
}

/* 表单字段居中样式 */
:deep(.centered-form) {
  .el-form-item__content {
    text-align: center;

    .el-input,
    .el-select,
    .el-input-number,
    .el-date-editor,
    .el-textarea {
      text-align: center;

      .el-input__inner,
      .el-textarea__inner {
        text-align: center;
      }

      .el-input-number__decrease,
      .el-input-number__increase {
        line-height: 1;
      }
    }

    .el-select .el-input__inner {
      text-align: center;
    }
  }
}

/* 表格字段居中样式 */
:deep(.el-table) {
  .el-table__body-wrapper {
    .el-table__body {
      td {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }

  .el-table__header-wrapper {
    .el-table__header {
      th {
        text-align: center !important;

        .cell {
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
        }
      }
    }
  }
}

/* 深色主题适配 */
.theme-dark {
  :deep(.readonly-input) {
    .el-input__inner {
      background-color: var(--base-item-bg, #2d3748) !important;
      color: var(--base-color-3, #a0aec0) !important;
      border-color: var(--border-color-1, #4a5568) !important;
    }
  }
}
.warehouse-management {
  padding: 20px;
  background: var(--base-main-bg);
  color: var(--theme-color);

  .mb16 {
    margin-bottom: 16px;
  }

  // 搜索区域样式
  .search-section {
    background: var(--base-item-bg);
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    border: 1px solid var(--border-color-1);
  }

  // 操作按钮区域样式
  .operation-section {
    background: var(--base-item-bg);
    padding: 15px 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    border: 1px solid var(--border-color-1);
  }

  // 表格区域样式
  .table-section {
    background: var(--base-item-bg);
    border-radius: 4px;
    border: 1px solid var(--border-color-1);
  }

  // 分页区域样式
  .pagination-section {
    background: var(--base-item-bg);
    padding: 20px;
    border-radius: 4px;
    margin-top: 16px;
    text-align: right;
    border: 1px solid var(--border-color-1);
  }

  // 仓库详情样式
  .warehouse-detail {
    color: var(--theme-color);
  }

  // 操作按钮样式 - 使用主题变量
  .view-btn {
    color: var(--base-color-7) !important;
    &:hover {
      color: var(--current-color) !important;
    }
  }

  .edit-btn {
    color: #e6a23c !important;
    &:hover {
      color: #ebb563 !important;
    }
  }

  .zone-btn {
    color: #909399 !important;
    &:hover {
      color: #a6a9ad !important;
    }
  }

  .enable-btn {
    color: #67c23a !important;
    &:hover {
      color: #85ce61 !important;
    }
  }

  .disable-btn {
    color: #f56c6c !important;
    &:hover {
      color: #f78989 !important;
    }
  }

  .delete-btn {
    color: #f56c6c !important;
    &:hover {
      color: #f78989 !important;
    }
  }

  // 操作列按钮容器样式
  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;

    .el-button {
      margin: 0 !important;
      padding: 4px 8px !important;
      font-size: 12px !important;
      min-width: auto !important;
    }

    .el-dropdown {
      margin-left: 4px;
    }
  }

  // 区域概览弹窗样式
  .zone-overview {
    .stat-card {
      cursor: default;
      transition: all 0.3s ease;
      background: var(--base-item-bg);
      border: 1px solid var(--border-color-1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px var(--tag-shadow-color-1);
      }

      .stat-content {
        text-align: center;
        padding: 10px 0;

        .stat-value {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 5px;
          color: var(--theme-color);
        }

        .stat-label {
          font-size: 14px;
          color: var(--theme-color);
          opacity: 0.8;
        }
      }

      &.total .stat-value {
        color: var(--current-color);
      }

      &.active .stat-value {
        color: #67c23a;
      }

      &.inactive .stat-value {
        color: #f56c6c;
      }

      &.qr .stat-value {
        color: #409eff;
      }
    }
  }

  .search-section {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
  }

  .operation-section {
    margin-bottom: 20px;

    .el-button {
      margin-right: 10px;
    }
  }

  .table-section {
    background: var(--base-main-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color-1);
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
  }

  .pagination-section {
    margin-top: 20px;
    text-align: center;
  }

  .warehouse-detail {
    .el-descriptions {
      background: var(--base-main-bg);
    }
  }

  .text-danger {
    color: #f56c6c;
  }

  .text-success {
    color: #67c23a;
  }
}
</style>

<!-- 全局样式覆盖，确保主题适配正常工作 -->
<style lang="scss">
// 针对当前组件强制应用主题样式
.warehouse-management {
  // 表单样式适配
  .el-form {
    .el-form-item__label {
      color: var(--theme-color) !important;
      font-weight: 500;
    }

    .el-input__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;

      &:focus {
        border-color: var(--current-color) !important;
      }

      &::placeholder {
        color: var(--text-color-3) !important;
      }
    }

    .el-select {
      .el-input__inner {
        background: var(--base-main-bg) !important;
        color: var(--theme-color) !important;
      }

      .el-input__suffix {
        color: var(--theme-color) !important;
      }
    }

    .el-textarea__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;

      &:focus {
        border-color: var(--current-color) !important;
      }

      &::placeholder {
        color: var(--text-color-3) !important;
      }
    }

    .el-form-item__error {
      color: #f56c6c !important;
    }
  }

  // 表格样式适配
  .el-table {
    background: var(--base-main-bg) !important;
    border-color: var(--border-color-1) !important;
    color: var(--theme-color) !important;

    .el-table__header {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
    }

    .el-table__row {
      background: var(--base-main-bg) !important;

      &:hover {
        background: var(--table-row-hover-bg) !important;
      }
    }

    th {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    td {
      border-bottom-color: var(--border-color-1) !important;
      color: var(--theme-color) !important;
      background: var(--base-main-bg) !important;
    }

    tr:hover td {
      background: var(--table-row-hover-bg) !important;
    }

    .el-button--text {
      color: var(--current-color) !important;
      background: transparent !important;
      border: none !important;
      padding: 4px 8px !important;
      font-size: 12px !important;
      font-weight: 500 !important;

      &:hover {
        color: var(--base-menu-color-active) !important;
        background-color: var(--current-color) !important;
        border-radius: 4px !important;
      }

      &:focus {
        color: var(--current-color) !important;
        background: transparent !important;
      }

      // 为不同类型的操作按钮设置不同颜色
      &.view-btn {
        color: var(--color-2) !important;

        &:hover {
          background-color: var(--color-2) !important;
          color: #fff !important;
        }
      }

      &.edit-btn {
        color: #e6a23c !important;

        &:hover {
          background-color: #e6a23c !important;
          color: #fff !important;
        }
      }

      &.zone-btn {
        color: #67c23a !important;

        &:hover {
          background-color: #67c23a !important;
          color: #fff !important;
        }
      }

      &.delete-btn {
        color: #f56c6c !important;

        &:hover {
          background-color: #f56c6c !important;
          color: #fff !important;
        }
      }

      &.disable-btn {
        color: #f56c6c !important;

        &:hover {
          background-color: #f56c6c !important;
          color: #fff !important;
        }
      }

      &.enable-btn {
        color: #67c23a !important;

        &:hover {
          background-color: #67c23a !important;
          color: #fff !important;
        }
      }

      &.more-btn {
        color: var(--base-color-3) !important;

        &:hover {
          background-color: var(--base-menu-background-active) !important;
          color: var(--base-menu-color-active) !important;
        }
      }
    }

    .el-table__empty-text {
      color: var(--theme-color) !important;
    }

    .el-checkbox__inner {
      background: var(--base-main-bg) !important;
      border-color: var(--border-color-1) !important;

      &:hover {
        border-color: var(--current-color) !important;
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background: var(--current-color) !important;
      border-color: var(--current-color) !important;
    }
  }

  // 弹窗样式适配
  .el-dialog {
    background: var(--base-main-bg) !important;
    border: 1px solid var(--border-color-1) !important;

    .el-dialog__header {
      background: var(--base-main-bg) !important;
      border-bottom: 1px solid var(--border-color-1) !important;
    }

    .el-dialog__title {
      color: var(--theme-color) !important;
    }

    .el-dialog__body {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
    }

    .el-dialog__footer {
      background: var(--base-main-bg) !important;
      border-top: 1px solid var(--border-color-1) !important;

      .el-button {
        background: var(--base-main-bg) !important;
        border-color: var(--border-color-1) !important;
        color: var(--theme-color) !important;

        &.el-button--primary {
          background: var(--current-color) !important;
          border-color: var(--current-color) !important;
          color: #fff !important;

          &:hover {
            background: var(--color-2) !important;
            border-color: var(--color-2) !important;
          }
        }

        &:hover {
          background: var(--table-row-hover-bg) !important;
        }
      }
    }

    .el-dialog__close {
      color: var(--theme-color) !important;

      &:hover {
        color: var(--current-color) !important;
      }
    }
  }

  // 描述列表样式适配
  .el-descriptions {
    .el-descriptions__header {
      background: var(--base-color-9) !important;
      color: var(--theme-color) !important;
      border-bottom-color: var(--border-color-1) !important;
    }

    .el-descriptions__body {
      background: var(--base-main-bg) !important;

      .el-descriptions__cell {
        border-color: var(--border-color-1) !important;
        color: var(--theme-color) !important;
      }

      .el-descriptions__label {
        color: var(--theme-color) !important;
        font-weight: 500;
      }
    }
  }
}

// 下拉菜单样式适配
.el-dropdown-menu {
  background: var(--base-main-bg) !important;
  border-color: var(--border-color-1) !important;

  .el-dropdown-menu__item {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;

    &:hover {
      background: var(--table-row-hover-bg) !important;
    }

    &.danger-item {
      color: #f56c6c !important;

      &:hover {
        background: rgba(245, 108, 108, 0.1) !important;
      }
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }
}

// 下拉选择器全局样式覆盖
.el-select-dropdown {
  background: var(--base-main-bg) !important;
  border-color: var(--border-color-1) !important;

  .el-select-dropdown__item {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;

    &:hover {
      background: var(--table-row-hover-bg) !important;
    }

    &.selected {
      background: var(--current-color) !important;
      color: #fff !important;
    }
  }
}

/* 层级导航模式样式 */
.warehouse-name-clickable {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
}

/* 表格居中对齐样式 */
.table-section {
  ::v-deep .el-table {
    th {
      text-align: center;
    }

    td {
      text-align: center;
    }

    .cell {
      text-align: center;
    }
  }
}
</style>

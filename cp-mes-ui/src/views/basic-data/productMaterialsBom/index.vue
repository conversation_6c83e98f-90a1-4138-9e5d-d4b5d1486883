<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="toolbar-container">
      <div class="toolbar-left">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">
          新增
        </el-button>
        <el-button type="info" icon="el-icon-upload2" size="small" @click="handleImport">
          导入
        </el-button>
        <el-button type="warning" icon="el-icon-download" size="small" @click="handleExport">
          导出
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" plain icon="el-icon-folder-opened" size="small" @click="handleVersionManage">
          版本管理
        </el-button>
      </div>
    </div>

    <!-- 全局版本过滤提示 -->
    <div v-if="isGlobalVersionFilterActive" class="global-version-alert">
      <el-alert
        :title="`当前正在显示版本 ${currentGlobalVersion} 的数据`"
        type="info"
        show-icon
        :closable="false">
        <el-button type="text" size="small" @click="clearGlobalVersionFilter">
          <i class="el-icon-close"></i>
          清除版本过滤
        </el-button>
      </el-alert>
    </div>

    <!-- 主内容区域 - 左右分栏布局 -->
    <div class="bom-layout">
      <!-- 左侧型号面板 -->
      <div class="model-panel" :class="{ collapsed: panelCollapsed }">
                <div class="panel-header">
          <div class="header-title">
            <i class="el-icon-menu"></i>
            <h3 v-show="!panelCollapsed">型号列表</h3>
            <el-button
              type="text"
              size="mini"
              @click="togglePanel"
              class="collapse-btn"
              :icon="panelCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'">
            </el-button>
          </div>
          <div class="header-search">
            <el-input
              v-model="modelSearchText"
              placeholder="搜索型号"
              size="mini"
              clearable
              prefix-icon="el-icon-search">
            </el-input>
            <el-button
              size="mini"
              type="info"
              @click="loadModelStats"
              :loading="statsLoading"
              style="margin-left: 8px;"
              title="预加载型号统计数据">
              <i class="el-icon-refresh" v-if="!statsLoading"></i>
              统计
            </el-button>
          </div>
        </div>

        <div class="model-list-container">
          <div class="model-list" v-if="filteredModelList.length > 0">
            <div
              class="model-item"
              :class="{
                active: selectedModel === model,
                [`model-style-${getModelStyle(model)}`]: true
              }"
              v-for="model in filteredModelList"
              :key="model"
              @click="selectModel(model)">
              <div class="model-icon" :class="`icon-style-${getModelStyle(model)}`">
                <i class="el-icon-files"></i>
              </div>
              <div class="model-content">
                <div class="model-name-wrapper">
                  <span class="model-name">{{ model }}</span>
                  <el-tag
                    size="mini"
                    :type="getStyleTagType(getModelStyle(model))"
                    class="style-tag">
                    {{ getStyleDisplayName(getModelStyle(model)) }}
                  </el-tag>
                </div>
                <div class="model-stats">
                                  <el-tag
                  size="mini"
                  :type="getBomCountByModel(model) === '...' ? 'info' : 'success'"
                  @click.stop="loadSingleModelStats(model)"
                  style="cursor: pointer;"
                  :title="getBomCountByModel(model) === '...' ? '点击加载统计数据' : `${getBomCountByModel(model)} 种原材料`"
                >
                  <i class="el-icon-loading" v-if="loadingModels.includes(model)"></i>
                  <span v-else>
                    {{ getBomCountByModel(model) === '...' ? '点击加载' : `${getBomCountByModel(model)} 种原材料` }}
                  </span>
                </el-tag>
                </div>
              </div>
              <div class="model-arrow">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>

          <div v-else class="empty-model">
            <div class="empty-icon">
              <i class="el-icon-box"></i>
            </div>
            <p class="empty-text">暂无型号数据</p>
            <el-button type="text" @click="handleAdd" class="empty-action">
              <i class="el-icon-plus"></i>
              添加第一个型号
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧BOM内容面板 -->
      <div class="bom-panel">
        <div class="panel-header">
          <div class="header-title">
            <i class="el-icon-document"></i>
            <h3>{{ selectedModel ? `${selectedModel} - BOM清单` : '请选择型号' }}</h3>
          </div>
          <div class="header-actions" v-if="selectedModel">
            <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
            <el-button size="mini" type="success" icon="el-icon-upload2" @click="handleImport">导入</el-button>
            <el-button size="mini" type="warning" icon="el-icon-download" @click="handleExportModel">导出</el-button>
            <el-button size="mini" type="info" icon="el-icon-setting" @click="handleColumnConfig">字段配置</el-button>
          </div>
          <div class="header-actions" v-else>
            <el-button
              size="mini"
              type="info"
              icon="el-icon-refresh"
              @click="loadModelStats"
              :loading="statsLoading">
              {{ statsLoading ? '加载中...' : '加载型号统计' }}
            </el-button>
          </div>
        </div>

        <!-- 筛选面板 -->
        <div class="filter-panel" v-if="selectedModel">
          <el-form :inline="true" size="small" class="filter-form">
            <el-form-item label="功能模块">
              <el-select
                v-model="modelViewFilter.module"
                placeholder="全部模块"
                clearable
                @change="filterModelBomList">
                <el-option label="全部" value="" />
                <el-option
                  v-for="module in getModelModules"
                  :key="module"
                  :label="module"
                  :value="module"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="上下板类型">
              <el-select
                v-model="modelViewFilter.boardType"
                placeholder="全部类型"
                clearable
                @change="filterModelBomList">
                <el-option label="全部" value="" />
                <el-option label="上板" value="上板" />
                <el-option label="下板" value="下板" />
              </el-select>
            </el-form-item>

            <el-form-item label="产品款式">
              <el-select
                v-model="modelViewFilter.styleName"
                placeholder="全部款式"
                clearable
                @change="filterModelBomList">
                <el-option label="全部款式" value="" />
                <el-option
                  v-for="style in getModelStyles"
                  :key="style"
                  :label="style || '默认款式'"
                  :value="style">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="原料">
              <el-input
                v-model="modelViewFilter.material"
                placeholder="请输入原料"
                clearable
                @input="filterModelBomList"
              />
            </el-form-item>

            <el-form-item label="版本号" v-if="!isGlobalVersionFilterActive">
              <el-select
                v-model="modelViewFilter.version"
                placeholder="全部版本"
                clearable
                @change="filterModelBomList">
                <el-option label="全部版本" value="" />
                <el-option
                  v-for="version in getModelVersions"
                  :key="version"
                  :label="`v${version}`"
                  :value="version">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" size="small" @click="resetModelViewFilter">
                <i class="el-icon-refresh"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="batch-operations" v-if="selectedModel && selectedBomRows.length > 0">
          <div class="batch-info">
            <i class="el-icon-info"></i>
            <span>已选择 <strong>{{ selectedBomRows.length }}</strong> 条记录</span>
          </div>
          <div class="batch-actions">
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleBatchUpdate">
              批量修改型号
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleBatchDelete">
              批量删除
            </el-button>
            <el-button
              size="mini"
              @click="clearSelection">
              取消选择
            </el-button>
          </div>
        </div>

        <!-- BOM数据表格 -->
        <div class="bom-content" v-if="selectedModel">
          <el-table
            ref="bomTable"
            :data="filteredBomList"
            border
            style="width: 100%"
            v-loading="bomLoading"
            :header-cell-style="{ background: 'var(--table-header-bg, var(--current-color))', color: 'var(--table-header-color, var(--theme-color))', height: '50px' }"
            :cell-style="{ height: '48px', padding: '12px 8px' }"
            :row-class-name="tableRowClassName"
            @selection-change="handleSelectionChange">

            <el-table-column type="selection" width="55" align="center" />

            <!-- <el-table-column v-if="isColumnVisible('styleName')" label="产品款式" prop="styleName" width="120" align="center">
              <template slot-scope="scope">
                <el-tag
                  size="small"
                  :type="getStyleTagType(scope.row.styleName)"
                  class="style-tag">
                  <i class="el-icon-star-on" style="margin-right: 4px;"></i>
                  {{ scope.row.styleName || '默认款式' }}
                </el-tag>
              </template>
            </el-table-column> -->

            <el-table-column v-if="isColumnVisible('productNumber')" label="产品编码" prop="productNumber" width="200" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.productNumber" class="product-number">
                  <i class="el-icon-postcard" style="margin-right: 4px; color: #409EFF;"></i>
                  {{ scope.row.productNumber }}
                </span>
                <span v-else class="no-product-number">
                  <i class="el-icon-warning" style="margin-right: 4px; color: #E6A23C;"></i>
                  未设置
                </span>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('module')" label="功能模块" prop="module" width="140" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-tag
                  size="small"
                  :type="getModuleColorType(scope.row.module)"
                  :effect="getModuleTagEffect(scope.row.module)"
                  class="module-tag-enhanced">
                  <i :class="getModuleIcon(scope.row.module)" class="module-icon"></i>
                  {{ scope.row.module }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('boardType')" label="上下板类型" prop="boardType" width="100" align="center">
              <template slot-scope="scope">
                <el-tag
                  size="small"
                  :type="scope.row.boardType === '上板' ? 'success' : 'warning'"
                  class="board-type-tag">
                  {{ scope.row.boardType }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="所需原料" prop="material" min-width="200" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <div class="material-cell">
                  <!-- 有具体原料的情况 -->
                  <div v-if="scope.row.material" class="material-info">
                    <i class="el-icon-goods material-icon"></i>
                    <span class="material-name">{{ scope.row.material }}</span>
                  </div>
                  <!-- 通用板块的情况 -->
                  <div v-else-if="isGenericBlock(scope.row)" class="generic-bom-info">
                    <div class="generic-header">
                      <i class="el-icon-s-grid generic-icon"></i>
                      <span class="generic-label">{{ getGenericBomDisplayText(scope.row) }}</span>
                    </div>

                    <!-- 映射操作按钮组 -->
                    <div class="mapping-actions">
                      <el-button
                        size="mini"
                        type="primary"
                        @click="showMappingDialog(scope.row)"
                        class="mapping-btn">
                        <i class="el-icon-menu"></i>
                        {{ selectedMappings[scope.row.id] ? '切换映射' : '选择映射' }}
                      </el-button>

                      <el-button
                        v-if="selectedMappings[scope.row.id]"
                        size="mini"
                        type="success"
                        @click="showMappingDetail(scope.row)"
                        class="detail-btn">
                        <i class="el-icon-view"></i>
                        查看清单
                      </el-button>

                      <el-button
                        v-if="selectedMappings[scope.row.id]"
                        size="mini"
                        type="warning"
                        @click="clearMapping(scope.row)"
                        class="clear-btn">
                        <i class="el-icon-delete"></i>
                        清除
                      </el-button>
                    </div>

                    <!-- 当前选择的映射显示 -->
                    <div v-if="selectedMappings[scope.row.id]" class="current-mapping">
                      <el-tag size="mini" type="success">
                        当前选择：{{ getMappingDisplayText(selectedMappings[scope.row.id]) }}
                      </el-tag>
                    </div>
                  </div>
                  <!-- 未指定原料的情况 -->
                  <div v-else class="empty-material">
                    <i class="el-icon-warning-outline empty-icon"></i>
                    <span class="empty-text">未指定原料</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('quantity')" label="数量" prop="quantity" width="80" align="center">
              <template slot-scope="scope">
                <el-tooltip :content="`数量：${scope.row.quantity}（单位：${scope.row.unit}）`" placement="top">
                  <div class="quantity-info">
                    <span class="quantity-number">{{ scope.row.quantity }}</span>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('unit')" label="单位" prop="unit" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="info" class="unit-tag">
                  {{ scope.row.unit }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('version')" label="版本号" prop="version" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="small" class="version-tag">
                  v{{ scope.row.version }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('updatedBy')" label="更新人" prop="updatedBy" width="120" align="center">
              <template slot-scope="scope">
                <div class="user-info">
                  <el-tooltip :content="`更新人：${getUpdatedByName(scope.row)}`" placement="top">
                    <div class="user-avatar">
                      {{ getUserInitial(getUpdatedByName(scope.row)) }}
                    </div>
                  </el-tooltip>
                  <span>{{ getUpdatedByName(scope.row) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('updatedAt')" label="更新时间" prop="updatedAt" width="180" align="center">
              <template slot-scope="scope">
                <div class="time-column">
                  <div class="time-icon">
                    <i class="el-icon-time"></i>
                  </div>
                  <div class="time-content">
                    <div class="time-date">
                      {{ formatDateTime(scope.row.updatedAt).date }}
                    </div>
                    <div class="time-time">
                      {{ formatDateTime(scope.row.updatedAt).time }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('createBy')" label="创建人" prop="createBy" width="120" align="center">
              <template slot-scope="scope">
                <div class="user-info">
                  <el-tooltip :content="`创建人：${getCreateByName(scope.row)}`" placement="top">
                    <div class="user-avatar">
                      {{ getUserInitial(getCreateByName(scope.row)) }}
                    </div>
                  </el-tooltip>
                  <span>{{ getCreateByName(scope.row) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('createTime')" label="创建时间" prop="createTime" width="180" align="center">
              <template slot-scope="scope">
                <div class="time-column">
                  <div class="time-icon">
                    <i class="el-icon-time"></i>
                  </div>
                  <div class="time-content">
                    <div class="time-date">
                      {{ formatDateTime(scope.row.createTime).date }}
                    </div>
                    <div class="time-time">
                      {{ formatDateTime(scope.row.createTime).time }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column v-if="isColumnVisible('remark')" label="备注" prop="remark" min-width="150" show-overflow-tooltip></el-table-column>

            <el-table-column label="操作" width="200" fixed="right" align="center">
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <!-- 常规操作按钮 -->
                  <el-tooltip content="编辑此原材料信息" placement="top" :open-delay="500">
                    <el-button
                      size="mini"
                      type="primary"
                      icon="el-icon-edit"
                      @click="handleUpdate(scope.row)"
                      class="action-btn edit-btn"
                      circle>
                    </el-button>
                  </el-tooltip>

                  <!-- 设置为通用按钮 -->
                  <el-tooltip
                    v-if="canSetAsGeneric(scope.row)"
                    content="设置为通用类型"
                    placement="top"
                    :open-delay="500">
                    <el-button
                      size="mini"
                      type="warning"
                      icon="el-icon-s-grid"
                      @click="handleSetAsGeneric(scope.row)"
                      class="action-btn generic-btn"
                      :loading="genericSettingLoading"
                      circle>
                    </el-button>
                  </el-tooltip>

                  <el-tooltip content="删除此记录" placement="top" :open-delay="500">
                    <el-button
                      size="mini"
                      type="danger"
                      icon="el-icon-delete"
                      @click="handleDelete(scope.row)"
                      class="action-btn delete-btn"
                      circle>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div class="empty-content" v-else>
          <div class="empty-illustration">
            <i class="el-icon-warning-outline"></i>
          </div>
          <div class="empty-text">
            <h4>请选择型号</h4>
            <p>从左侧选择一个型号来查看其BOM清单</p>
          </div>
          <div class="empty-actions">
            <el-button type="primary" @click="handleAdd">
              <i class="el-icon-plus"></i>
              创建新型号
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加或修改BOM清单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-select
                v-model="form.model"
                placeholder="请选择产品型号或输入新型号"
                filterable
                allow-create
                remote
                :remote-method="searchProducts"
                :loading="productSearchLoading"
                style="width: 100%;"
                @change="handleModelChange"
                @focus="loadDefaultProducts">
                <el-option-group label="现有产品型号">
                  <el-option
                    v-for="product in productOptions"
                    :key="product.productId"
                    :label="`${product.productName} (${product.productNumber})`"
                    :value="product.productName">
                    <span style="float: left">{{ product.productName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ product.productNumber }}</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <div class="model-select-hint">
                <i class="el-icon-info"></i>
                可选择现有产品型号，也可直接输入新型号（将自动创建新产品）
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="功能模块" prop="module">
              <el-select v-model="form.module" placeholder="请选择功能模块" clearable filterable allow-create style="width: 100%;">
                <el-option
                  v-for="module in moduleList"
                  :key="module"
                  :label="module"
                  :value="module"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="上下板类型" prop="boardType">
              <el-select v-model="form.boardType" placeholder="请选择上下板类型" clearable style="width: 100%;">
                <el-option label="上板" value="上板" />
                <el-option label="下板" value="下板" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所需原料" prop="material">
              <el-select
                v-model="form.material"
                placeholder="请选择原料或输入新原料"
                filterable
                allow-create
                remote
                :remote-method="searchMaterials"
                :loading="materialSearchLoading"
                style="width: 100%;"
                @change="handleMaterialChange"
                @focus="loadDefaultMaterials">
                <el-option-group label="现有原料">
                  <el-option
                    v-for="material in materialOptions"
                    :key="material.warehouseMaterialId"
                    :label="`${material.materialName} (库存:${material.currentStock || 0})`"
                    :value="material.materialName">
                    <span style="float: left">{{ material.materialName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">库存:{{ material.currentStock || 0 }}</span>
                  </el-option>
                </el-option-group>
              </el-select>
              <div class="model-select-hint">
                <i class="el-icon-info"></i>
                可选择现有原料，也可直接输入新原料（将自动添加到原料仓库）
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="所需数量" prop="quantity">
              <el-input-number v-model="form.quantity" placeholder="请输入所需数量" :min="1" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原料单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入原料单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-select
                v-model="form.version"
                placeholder="选择现有版本或输入新版本号"
                filterable
                allow-create
                clearable
                style="width: 100%;"
                @focus="loadVersionsForForm">
                <el-option-group label="现有版本">
                  <el-option
                    v-for="version in formVersionOptions"
                    :key="version"
                    :label="`v${version}`"
                    :value="version">
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新人" prop="updatedBy">
              <el-input v-model="form.updatedBy" placeholder="系统自动获取" :readonly="true">
                <i slot="suffix" class="el-icon-user" style="color: var(--current-color, #409eff); margin-right: 8px;"></i>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量修改型号对话框 -->
    <el-dialog title="批量修改型号" :visible.sync="batchUpdateVisible" width="600px" append-to-body>
      <div class="batch-update-content">
        <div class="selected-info">
          <el-alert
            :title="`即将修改 ${selectedBomRows.length} 条BOM记录的型号`"
            type="warning"
            show-icon
            :closable="false">
          </el-alert>

          <!-- 显示当前型号信息 -->
          <div v-if="selectedModel" class="current-model-info" style="margin-top: 10px;">
            <el-card shadow="never" style="border: 1px dashed var(--current-color, #409eff);">
              <div style="display: flex; align-items: center; font-size: 14px;">
                <i class="el-icon-info" style="color: var(--current-color, #409eff); margin-right: 8px;"></i>
                <span style="color: var(--base-color-2, #666);">当前选中型号：</span>
                <el-tag type="primary" size="small" style="margin-left: 8px;">{{ selectedModel }}</el-tag>
                <span style="color: var(--base-color-3, #999); margin-left: 8px; font-size: 12px;">
                  （已自动设置为目标型号）
                </span>
              </div>
            </el-card>
          </div>
        </div>

        <el-form ref="batchUpdateForm" :model="batchUpdateForm" label-width="120px" style="margin-top: 20px;">
          <el-form-item label="目标型号" prop="targetModel" :rules="[{ required: true, message: '请输入目标型号', trigger: 'blur' }]">
            <el-select
              v-model="batchUpdateForm.targetModel"
              placeholder="请选择产品型号或输入新型号"
              filterable
              allow-create
              remote
              :remote-method="searchProducts"
              :loading="productSearchLoading"
              style="width: 100%;"
              @change="handleBatchModelChange"
              @focus="loadDefaultProducts">
              <el-option-group label="现有产品型号">
                <el-option
                  v-for="product in productOptions"
                  :key="product.productId"
                  :label="`${product.productName} (${product.productNumber})`"
                  :value="product.productName">
                  <span style="float: left">{{ product.productName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ product.productNumber }}</span>
                </el-option>
              </el-option-group>
            </el-select>
            <div class="model-select-hint">
              <i class="el-icon-info"></i>
              可选择现有产品型号，也可直接输入新型号（将自动创建新产品）
            </div>
          </el-form-item>

          <!-- 产品编码显示字段 - 显示当前记录的产品编码 -->
          <el-form-item label="当前产品编码">
            <div class="product-numbers-display">
              <div v-if="currentProductNumbers.length > 0" class="product-numbers-list">
                <el-tag
                  v-for="(productNumber, index) in currentProductNumbers"
                  :key="index"
                  :type="index === 0 ? 'primary' : 'info'"
                  size="small"
                  class="product-number-tag">
                  {{ productNumber }}
                </el-tag>
              </div>
              <div v-else class="no-product-number">
                <i class="el-icon-warning" style="color: #E6A23C; margin-right: 5px;"></i>
                <span style="color: var(--base-color-3, #999);">选中记录中暂无产品编码</span>
              </div>
            </div>
            <div class="model-select-hint">
              <i class="el-icon-info"></i>
              显示当前选中BOM记录的产品编码信息
            </div>
          </el-form-item>

          <!-- 新的目标产品编码字段 -->
          <!-- <el-form-item label="目标产品编码">
            <el-input
              v-model="batchUpdateForm.productNumber"
              placeholder="选择目标型号后自动获取产品编码"
              readonly
              style="width: 100%;">
              <template slot="prepend">
                <i class="el-icon-link"></i>
              </template>
            </el-input>
            <div class="model-select-hint">
              <i class="el-icon-info"></i>
              {{ batchUpdateForm.productNumber ? '目标产品编码已获取' : '请选择目标型号以获取产品编码' }}
            </div>
          </el-form-item> -->
        </el-form>

        <div class="selected-records">
          <h4>将要修改的记录：</h4>
          <el-table :data="selectedBomRows" border size="mini" max-height="300">
            <el-table-column label="当前型号" prop="model" width="120"></el-table-column>
            <el-table-column label="功能模块" prop="module" width="100"></el-table-column>
            <el-table-column label="上下板类型" prop="boardType" width="100"></el-table-column>
            <el-table-column label="所需原料" prop="material" min-width="120"></el-table-column>
            <el-table-column label="数量" prop="quantity" width="60"></el-table-column>
            <el-table-column label="单位" prop="unit" width="60"></el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitBatchUpdate">确 定</el-button>
        <el-button @click="cancelBatchUpdate">取 消</el-button>
      </div>
    </el-dialog>

    <!-- BOM清单导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="500px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&targetVersion=' + upload.targetVersion + '&versionStrategy=' + upload.versionStrategy"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的BOM数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。<strong>支持多工作表、多数据块导入</strong>，会自动读取Excel文件中所有工作表的数据，并且支持单个工作表中存在多个"型号"列开头的数据块。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="downloadTemplate">下载模板</el-link>
        </div>
      </el-upload>

      <!-- 版本号配置区域 -->
      <el-divider content-position="left">版本配置</el-divider>
      <el-form :model="upload" label-width="120px" size="small">
        <el-form-item label="版本策略：">
          <el-radio-group v-model="upload.versionStrategy" @change="handleVersionStrategyChange">
            <el-radio label="auto">自动生成新版本</el-radio>
            <el-radio label="custom">指定版本号</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="目标版本：" v-if="upload.versionStrategy === 'custom'">
          <el-select v-model="upload.targetVersion" filterable allow-create placeholder="选择现有版本或输入新版本号" style="width: 100%">
            <el-option label="输入新版本号..." value="" disabled></el-option>
            <el-option-group label="现有版本（选择将覆盖）">
              <el-option
                v-for="version in existingVersions"
                :key="version"
                :label="version + ' (覆盖现有数据)'"
                :value="version">
              </el-option>
            </el-option-group>
          </el-select>
          <div style="margin-top: 5px; font-size: 12px; color: #909399;">
            选择现有版本将覆盖该版本的所有数据，输入新版本号将创建新版本
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 版本管理对话框 -->
    <el-dialog
      title="版本管理中心"
      :visible.sync="versionManageVisible"
      width="1000px"
      append-to-body
      class="version-manage-dialog"
      :before-close="handleVersionDialogClose">

      <!-- 对话框头部 -->
      <div class="version-dialog-header" slot="title">
        <div class="header-icon">
          <i class="el-icon-time"></i>
        </div>
        <div class="header-content">
          <h3>版本管理中心</h3>
          <p>查看和管理BOM清单的所有历史版本</p>
        </div>
      </div>

      <!-- 全局版本切换控制区域 -->
      <el-card class="version-control-panel" shadow="never">
        <div slot="header" class="version-control-header">
          <div class="control-title">
            <i class="el-icon-switch-button"></i>
            <span>全局版本控制</span>
          </div>
          <el-tag v-if="currentGlobalVersion" type="success" size="small">
            当前显示版本：v{{ currentGlobalVersion }}
          </el-tag>
        </div>

        <div class="version-control-content">
          <el-form :inline="true" size="small">
            <el-form-item label="切换版本：">
              <el-select
                v-model="currentGlobalVersion"
                placeholder="选择要显示的版本"
                clearable
                @change="handleGlobalVersionChange"
                style="width: 200px;">
                <el-option label="显示所有版本" value="" />
                <el-option
                  v-for="version in allVersions"
                  :key="version"
                  :label="`版本 ${version}`"
                  :value="version">
                  <span style="float: left">v{{ version }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ getVersionUsageCount(version) }} 个型号使用
                  </span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="applyGlobalVersionFilter"
                :disabled="!currentGlobalVersion"
                size="small">
                <i class="el-icon-check"></i>
                应用到页面
              </el-button>
            </el-form-item>

            <el-form-item>
              <el-button
                @click="clearGlobalVersionFilter"
                size="small">
                <i class="el-icon-refresh-left"></i>
                显示全部
              </el-button>
            </el-form-item>
          </el-form>

          <div class="version-control-info">
            <el-alert
              v-if="currentGlobalVersion"
              :title="`当前页面只显示版本 ${currentGlobalVersion} 的数据`"
              type="info"
              show-icon
              :closable="false"
              class="version-alert">
            </el-alert>
            <el-alert
              v-else
              title="当前显示所有版本的数据"
              type="success"
              show-icon
              :closable="false"
              class="version-alert">
            </el-alert>
          </div>
        </div>
      </el-card>

      <!-- 筛选面板 -->
      <div class="version-filter-panel">
        <div class="filter-header">
          <div class="filter-title">
            <i class="el-icon-search"></i>
            <span>筛选条件</span>
          </div>
          <div class="filter-stats" v-if="groupVersionList.length > 0">
            <el-tag type="success" size="small">
              <i class="el-icon-data-line"></i>
              {{ groupVersionList.length }} 个版本
            </el-tag>
          </div>
        </div>

        <div class="filter-content">
          <div class="filter-row">
            <el-form :inline="true" size="small" class="filter-form">
              <el-form-item label="型号">
                <el-input
                  placeholder="请输入型号"
                  v-model="versionFilter.model"
                  clearable
                  class="filter-input"
                  prefix-icon="el-icon-goods">
                </el-input>
              </el-form-item>
              <el-form-item label="功能模块">
                <el-select
                  v-model="versionFilter.module"
                  placeholder="请选择功能模块"
                  clearable
                  class="filter-select">
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="module in moduleList"
                    :key="module"
                    :label="module"
                    :value="module"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="上下板类型">
                <el-select
                  v-model="versionFilter.boardType"
                  placeholder="请选择上下板类型"
                  clearable
                  class="filter-select">
                  <el-option label="全部" value="" />
                  <el-option label="上板" value="上板" />
                  <el-option label="下板" value="下板" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>

          <div class="filter-actions">
            <el-button type="primary" @click="loadVersionsByGroup" :loading="versionLoading" icon="el-icon-search">
              <span>查询版本</span>
            </el-button>
            <el-button @click="resetVersionFilter" icon="el-icon-refresh">
              <span>重置条件</span>
            </el-button>
            <el-button type="info" @click="loadAllVersions" :loading="versionLoading" icon="el-icon-view">
              <span>显示全部</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 版本列表表格 -->
      <div class="version-table-container">
        <el-table
          :data="groupVersionList"
          v-loading="versionLoading"
          class="version-table"
          :header-cell-style="{
            background: 'var(--current-color)',
            color: 'var(--theme-color)',
            height: '50px',
            fontSize: '14px',
            fontWeight: '600'
          }"
          :cell-style="{ height: '60px', padding: '16px 12px' }"
          :row-class-name="versionTableRowClassName">

          <el-table-column prop="version" label="版本号" width="140" align="center">
            <template slot-scope="scope">
              <div class="version-column">
                <div class="version-icon">
                  <i class="el-icon-collection-tag"></i>
                </div>
                <el-tag
                  size="medium"
                  :type="scope.row.version === 'unknown' ? 'warning' : 'primary'"
                  class="version-tag-enhanced"
                  effect="dark">
                  v{{ scope.row.version }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="count" label="原材料数量" width="160" align="center">
            <template slot-scope="scope">
              <div class="count-column">
                <div class="count-visual">
                  <div class="count-circle" :class="getCountLevelClass(scope.row.count)">
                    <span class="count-number">{{ scope.row.count }}</span>
                  </div>
                </div>
                <div class="count-text">
                  <span>种原材料</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="updatedBy" label="更新人" width="140" align="center">
            <template slot-scope="scope">
              <div class="user-column">
                <div class="user-avatar-enhanced">
                  {{ getUserInitial(scope.row.updatedBy) }}
                </div>
                <div class="user-name">
                  {{ scope.row.updatedBy || '未知' }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="updatedAt" label="更新时间" align="center" min-width="200">
            <template slot-scope="scope">
              <div class="time-column">
                <div class="time-icon">
                  <i class="el-icon-time"></i>
                </div>
                <div class="time-content">
                  <div class="time-date">
                    {{ formatDateTime(scope.row.updatedAt).date }}
                  </div>
                  <div class="time-time">
                    {{ formatDateTime(scope.row.updatedAt).time }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="140" align="center" fixed="right">
            <template slot-scope="scope">
              <div class="version-actions">
                <el-tooltip content="切换到此版本" placement="top" :open-delay="500">
                  <el-button
                    size="mini"
                    type="success"
                    icon="el-icon-switch-button"
                    @click="switchToVersion(scope.row)"
                    class="action-btn-enhanced switch-btn">
                    切换
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="groupVersionList.length === 0 && !versionLoading" class="version-empty-state">
          <div class="empty-illustration">
            <i class="el-icon-document-copy"></i>
          </div>
          <div class="empty-content">
            <h4>暂无版本数据</h4>
            <p>当前筛选条件下没有找到任何版本记录</p>
            <el-button type="primary" @click="resetVersionFilter" size="small">
              <i class="el-icon-refresh"></i>
              重置筛选条件
            </el-button>
          </div>
        </div>
      </div>

      <!-- 对话框底部 -->
      <div slot="footer" class="version-dialog-footer">
        <div class="footer-stats">
          <div class="stats-item">
            <i class="el-icon-data-analysis"></i>
            <span>共找到 <strong>{{ groupVersionList.length }}</strong> 个版本</span>
          </div>
          <div class="stats-item" v-if="groupVersionList.length > 0">
            <i class="el-icon-document"></i>
            <span>总计 <strong>{{ getTotalBomCount() }}</strong> 种原材料</span>
          </div>
        </div>
        <div class="footer-actions">
          <el-button size="medium" @click="versionManageVisible = false">
            <i class="el-icon-close"></i>
            关闭
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="columnConfigVisible" width="600px" append-to-body class="column-config-dialog">
      <div style="max-height: 400px; overflow-y: auto;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>可选字段</h4>
            <el-checkbox-group v-model="selectedColumns" @change="handleColumnChange">
              <div v-for="column in allColumns" :key="column.prop" style="margin-bottom: 8px;">
                <el-checkbox :label="column.prop" :disabled="column.required">
                  {{ column.label }}
                  <span v-if="column.required" style="color: var(--current-color, #f56c6c);">(必需)</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-col>
          <el-col :span="12">
            <h4>字段预览</h4>
            <div class="preview-container">
              <div v-for="column in previewColumns" :key="column.prop" class="preview-item">
                <span>{{ column.label }}</span>
                <span style="color: var(--base-color-2, #909399); font-size: 12px;">{{ column.width || 'auto' }}</span>
              </div>
              <div v-if="previewColumns.length === 0" class="preview-empty">
                请选择要显示的字段
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetColumnConfig">重置默认</el-button>
        <el-button type="primary" @click="saveColumnConfig">确 定</el-button>
        <el-button @click="columnConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>



    <!-- ============= 通用映射相关弹窗 ============= -->

    <!-- 映射选择弹窗 -->
    <el-dialog
      title="通用映射选择"
      :visible.sync="mappingDialogVisible"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
      class="mapping-selection-dialog">

      <div class="mapping-dialog-header" v-if="currentGenericRow">
        <div class="current-block-info">
          <div class="block-icon">
            <i class="el-icon-s-grid"></i>
          </div>
          <div class="block-details">
            <h4>当前通用板块</h4>
            <p>{{ currentGenericRow.model }} · {{ currentGenericRow.module }} · {{ currentGenericRow.boardType }}</p>
            <small>通用ID: {{ currentGenericRow.reservedField2 || '未设置' }}</small>
          </div>
        </div>
      </div>

      <div class="mapping-content" v-loading="mappingLoading">
        <div v-if="currentMappingList.length === 0" class="empty-mapping">
          <div class="empty-icon">
            <i class="el-icon-document"></i>
          </div>
          <h4>暂无可用映射</h4>
          <p class="empty-hint">该通用ID暂未配置任何映射方案</p>
          <p class="empty-action">请联系管理员添加映射配置</p>
        </div>

        <div v-else class="mapping-list">
          <div class="mapping-list-header">
            <div class="header-title">
              <h4><i class="el-icon-s-operation"></i> 选择映射方案</h4>
              <p>共找到 {{ filteredMappingList.length }} / {{ currentMappingList.length }} 个映射</p>
            </div>
            <div class="header-actions">
              <el-input
                v-model="mappingSearchText"
                placeholder="搜索映射..."
                size="mini"
                prefix-icon="el-icon-search"
                clearable
                class="mapping-search">
              </el-input>
              <el-select
                v-model="mappingFilterType"
                placeholder="筛选类型"
                size="mini"
                clearable
                class="mapping-filter">
                <el-option label="全部" value=""></el-option>
                <el-option label="默认映射" value="default"></el-option>
                <el-option label="自定义映射" value="custom"></el-option>
              </el-select>
            </div>
          </div>

          <div class="mapping-cards">
            <div
              v-for="mapping in filteredMappingList"
              :key="mapping.id"
              class="mapping-card"
              :class="{ 'selected': selectedMapping && selectedMapping.id === mapping.id, 'default': mapping.isDefault === '1' }"
              @click="selectedMapping = mapping">

              <div class="card-header">
                <div class="card-radio">
                  <el-radio
                    v-model="selectedMapping"
                    :label="mapping"
                    @click.native.stop>
                  </el-radio>
                </div>
                <div class="card-badges">
                  <el-tag v-if="mapping.isDefault === '1'" type="success" size="mini" effect="plain">
                    <i class="el-icon-star-off"></i> 默认
                  </el-tag>
                  <el-tag type="info" size="mini" effect="plain">
                    排序: {{ mapping.sortOrder }}
                  </el-tag>
                </div>
              </div>

              <div class="card-content">
                <div class="mapping-name">
                  <i class="el-icon-monitor"></i>
                  {{ mapping.mappedModel }}
                </div>
                <div class="mapping-specs">
                  <div class="spec-item">
                    <span class="spec-label">模块:</span>
                    <span class="spec-value">{{ mapping.mappedModule }}</span>
                  </div>
                  <div class="spec-item">
                    <span class="spec-label">板类型:</span>
                    <span class="spec-value">{{ mapping.mappedBoardType }}</span>
                  </div>
                </div>
                <div v-if="mapping.remark" class="mapping-remark">
                  <i class="el-icon-info"></i>
                  {{ mapping.remark }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button icon="el-icon-close" @click="closeMappingDialog">
          取 消
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-check"
          @click="confirmMappingSelection"
          :disabled="!selectedMapping">
          确认选择
        </el-button>
      </div>
    </el-dialog>

    <!-- 映射材料详情弹窗 -->
    <el-dialog
      title="映射材料清单"
      :visible.sync="mappingDetailVisible"
      width="800px"
      append-to-body
      class="mapping-detail-dialog">

      <div class="mapping-detail-header" v-if="currentGenericRow && selectedMappings[currentGenericRow.id]">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="通用板块">
            {{ currentGenericRow.model }}-{{ currentGenericRow.module }}-{{ currentGenericRow.boardType }}
          </el-descriptions-item>
          <el-descriptions-item label="映射目标">
            {{ getMappingDisplayText(selectedMappings[currentGenericRow.id]) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="mapping-detail-content" v-loading="false">
        <div v-if="currentMappingMaterials.length === 0" class="empty-materials">
          <i class="el-icon-box"></i>
          <p>该映射暂无材料清单</p>
        </div>

        <el-table v-else :data="currentMappingMaterials" class="mapping-materials-table">
          <el-table-column prop="material" label="原料名称" min-width="150"></el-table-column>
          <el-table-column prop="quantity" label="数量" width="100" align="center"></el-table-column>
          <el-table-column prop="unit" label="单位" width="100" align="center"></el-table-column>
          <el-table-column prop="version" label="版本" width="100" align="center">
            <template slot-scope="scope">
              <el-tag size="mini">v{{ scope.row.version }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updatedBy" label="更新人" width="120" align="center"></el-table-column>
          <el-table-column prop="updatedAt" label="更新时间" width="160" align="center">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.updatedAt).date }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="mappingDetailVisible = false">关 闭</el-button>
        <el-button
          type="primary"
          @click="switchMapping(currentGenericRow)">
          <i class="el-icon-refresh"></i>
          切换映射
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProductMaterialsBom,
  getProductMaterialsBom,
  delProductMaterialsBom,
  addProductMaterialsBom,
  updateProductMaterialsBom,
  exportProductMaterialsBom,
  importTemplate,
  getAllVersions,
  getVersionsByGroup,
  getAllModels,
  getBomByModel,
  getAllModules,
  getAllBoardTypes,
  getMaterialListByMapping,
  getGenericMappingList,
  getDefaultGenericMapping,
  createGenericBom,
  batchUpdateModel,
  // 通用设置功能相关API
  checkGenericEligibility,
  checkGenericEligibilityByGroup,
  setAsGeneric,
  setAsGenericByGroup
} from "@/api/basicData/productMaterialsBom";
import { listProduct, addProduct, autoCreateProduct } from "@/api/basicData/product";
import { pageRawMaterialWarehouse, addRawMaterialWarehouse } from "@/api/jenasi/rawMaterialWarehouse";
import { getToken } from "@/utils/auth";

export default {
  name: "ProductMaterialsBom",
      data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 版本列表
      versionList: [],
      // 功能模块列表
      moduleList: [],

      // 产品相关
      productOptions: [],           // 产品选项列表
      productSearchLoading: false,  // 产品搜索加载状态
      productSearchTimer: null,     // 产品搜索防抖定时器
      isCustomProduct: false,       // 是否为自定义产品

      // 原料相关
      materialOptions: [],          // 原料选项列表
      materialSearchLoading: false, // 原料搜索加载状态
      materialSearchTimer: null,    // 原料搜索防抖定时器
      isCustomMaterial: false,      // 是否为自定义原料

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        model: [
          { required: true, message: "型号不能为空", trigger: "blur" },
        ],
        module: [
          { required: true, message: "功能模块不能为空", trigger: "blur" },
        ],
        boardType: [
          { required: true, message: "上下板类型不能为空", trigger: "blur" },
        ],
        material: [
          { required: true, message: "所需原料不能为空", trigger: "blur" },
        ],
        quantity: [
          { required: true, message: "所需数量不能为空", trigger: "blur" },
        ],
        unit: [
          { required: true, message: "原料单位不能为空", trigger: "blur" },
        ],
        version: [
          { required: true, message: "版本号不能为空", trigger: "blur" },
        ],
        updatedBy: [
          { required: true, message: "更新人不能为空", trigger: "blur" },
        ],
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 版本策略：auto=自动生成，custom=指定版本
        versionStrategy: 'auto',
        // 目标版本号
        targetVersion: '',
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/basicData/productMaterialsBom/importData"
      },
      // 现有版本列表
      existingVersions: [],

      // 版本管理
      versionManageVisible: false,
      versionLoading: false,
      versionFilter: {
        model: '',
        module: '',
        boardType: ''
      },
      groupVersionList: [],

      // 全局版本控制
      currentGlobalVersion: '',     // 当前全局选中的版本
      allVersions: [],              // 所有可用版本列表
      isGlobalVersionFilterActive: false, // 是否启用了全局版本过滤

      // 表单版本选项
      formVersionOptions: [],       // 表单中版本号字段的选项

      // 新增：型号视图相关数据
      showModelLayout: true,           // 默认显示型号视图
      modelList: [],                   // 型号列表
      selectedModel: '',               // 选中的型号
      selectedBomList: [],            // 选中型号的BOM列表
      modelSearchText: '',            // 型号搜索文本
      bomLoading: false,              // BOM数据加载状态

      // 型号视图筛选条件
      modelViewFilter: {
        module: '',
        boardType: '',
        material: '',
        version: '',
        styleName: ''
      },

      // 面板折叠状态
      panelCollapsed: false,

      // 型号BOM数量缓存
      modelStatsCache: {},
      // 统计加载状态
      statsLoading: false,

      // 加载状态控制
      loadingModels: [], // 正在加载统计数据的型号列表



      // 批量操作相关
      selectedBomRows: [],          // 选中的BOM行
      batchUpdateVisible: false,    // 批量修改弹窗显示状态
      batchUpdateForm: {            // 批量修改表单
        targetModel: '',
        productNumber: ''           // 新增产品编码字段
      },

      // 字段配置相关
      columnConfigVisible: false,
      selectedColumns: [
        'module',
        'boardType',
        'quantity',
        'unit',
        'version',
        'updatedBy'
      ],
      // 所有可选字段定义
      allColumns: [
        { prop: 'material', label: '所需原料', required: true, width: 'auto' },
        { prop: 'styleName', label: '产品款式', required: false, width: 120 },
        { prop: 'productNumber', label: '产品编码', required: false, width: 140 },
        { prop: 'module', label: '功能模块', required: false, width: 140 },
        { prop: 'boardType', label: '上下板类型', required: false, width: 100 },
        { prop: 'quantity', label: '数量', required: false, width: 80 },
        { prop: 'unit', label: '单位', required: false, width: 80 },
        { prop: 'version', label: '版本号', required: false, width: 100 },
        { prop: 'updatedBy', label: '更新人', required: false, width: 120 },
        { prop: 'updatedAt', label: '更新时间', required: false, width: 180 },
        { prop: 'createBy', label: '创建人', required: false, width: 120 },
        { prop: 'createTime', label: '创建时间', required: false, width: 180 },
        { prop: 'remark', label: '备注', required: false, minWidth: 150 }
      ],



      // ============= 通用映射功能相关数据 =============

      // 通用映射相关数据
      selectedMappings: {}, // 存储每行选择的映射 {rowId: mappingData}
      availableMappings: {}, // 存储可用映射列表 {genericId: [mappings]}
      mappingDialogVisible: false, // 映射选择弹窗
      currentGenericRow: null, // 当前选择映射的行
      currentMappingList: [], // 当前通用ID的映射列表
      selectedMapping: null, // 当前选择的映射
      expandedMaterialLists: {}, // 展开的材料清单 {rowId: [materials]}
      mappingDetailVisible: false, // 映射详情弹窗
      currentMappingMaterials: [], // 当前映射的材料清单
      mappingSearchText: '', // 映射搜索文本
      mappingFilterType: '', // 映射筛选类型
      mappingLoading: false, // 映射加载状态

      // ============= 通用设置功能相关数据 =============

      // 通用设置相关数据
      genericEligibilityCache: {}, // 通用设置资格缓存 {model-module-boardType: boolean}
      genericSettingLoading: false // 通用设置操作加载状态
    };
  },
  computed: {
    // 当前选中记录的产品编码列表
    currentProductNumbers() {
      if (!this.selectedBomRows || this.selectedBomRows.length === 0) {
        return [];
      }

      // 提取所有选中记录的产品编码，并去重
      const productNumbers = this.selectedBomRows
        .map(row => row.productNumber)
        .filter(num => num && num.trim() !== '')
        .filter((num, index, arr) => arr.indexOf(num) === index); // 去重

      return productNumbers;
    },

    // 过滤后的型号列表
    filteredModelList() {
      if (!this.modelSearchText) {
        return this.modelList;
      }
      return this.modelList.filter(model =>
        model.toLowerCase().includes(this.modelSearchText.toLowerCase())
      );
    },

    // 过滤后的BOM列表（型号视图）
    filteredBomList() {
      if (!this.selectedBomList || this.selectedBomList.length === 0) {
        return [];
      }

      return this.selectedBomList.filter(item => {
        // 功能模块筛选
        if (this.modelViewFilter.module && item.module !== this.modelViewFilter.module) {
          return false;
        }

        // 上下板类型筛选 - 兼容驼峰和下划线命名
        const itemBoardType = item.boardType || item.board_type;
        if (this.modelViewFilter.boardType && itemBoardType !== this.modelViewFilter.boardType) {
          return false;
        }

        // 产品款式筛选
        if (this.modelViewFilter.styleName) {
          const itemStyleName = item.styleName || '';
          if (itemStyleName !== this.modelViewFilter.styleName) {
            return false;
          }
        }

        // 原料筛选 - 支持通用板块（material为null）
        if (this.modelViewFilter.material) {
          // 如果material为null（通用板块），则不匹配搜索条件
          if (!item.material) {
            return false;
          }
          // 普通材料记录的模糊搜索
          if (!item.material.toLowerCase().includes(this.modelViewFilter.material.toLowerCase())) {
            return false;
          }
        }

        // 版本筛选
        if (this.modelViewFilter.version && item.version !== this.modelViewFilter.version) {
          return false;
        }

        return true;
      });
    },

    // 当前型号的功能模块列表
    getModelModules() {
      if (!this.selectedBomList || this.selectedBomList.length === 0) {
        return [];
      }
      const modules = [...new Set(this.selectedBomList.map(item => item.module).filter(Boolean))];
      return modules.sort();
    },

    // 当前型号的版本列表
    getModelVersions() {
      if (!this.selectedBomList || this.selectedBomList.length === 0) {
        return [];
      }
      const versions = [...new Set(this.selectedBomList.map(item => item.version).filter(Boolean))];
      return versions.sort((a, b) => {
        const [aMajor, aMinor = 0] = a.split('.').map(Number);
        const [bMajor, bMinor = 0] = b.split('.').map(Number);
        return bMajor - aMajor || bMinor - aMinor;
      });
    },

    // 当前型号的款式列表
    getModelStyles() {
      if (!this.selectedBomList || this.selectedBomList.length === 0) {
        return [];
      }
      const styles = [...new Set(this.selectedBomList.map(item => item.styleName).filter(style => style !== null && style !== undefined))];
      return styles.sort();
    },

    // 可见的列配置
    visibleColumns() {
      return this.allColumns.filter(column =>
        column.required || this.selectedColumns.includes(column.prop)
      );
    },
    // 预览的列配置
    previewColumns() {
      return this.visibleColumns;
    },

    // 过滤后的映射列表
    filteredMappingList() {
      if (!this.currentMappingList || this.currentMappingList.length === 0) {
        return [];
      }

      let filtered = [...this.currentMappingList];

      // 搜索过滤
      if (this.mappingSearchText && this.mappingSearchText.trim()) {
        const searchText = this.mappingSearchText.trim().toLowerCase();
        filtered = filtered.filter(mapping =>
          mapping.mappedModel.toLowerCase().includes(searchText) ||
          mapping.mappedModule.toLowerCase().includes(searchText) ||
          mapping.mappedBoardType.toLowerCase().includes(searchText) ||
          (mapping.remark && mapping.remark.toLowerCase().includes(searchText))
        );
      }

      // 类型筛选
      if (this.mappingFilterType) {
        if (this.mappingFilterType === 'default') {
          filtered = filtered.filter(mapping => mapping.isDefault === '1');
        } else if (this.mappingFilterType === 'custom') {
          filtered = filtered.filter(mapping => mapping.isDefault !== '1');
        }
      }

      // 按默认和排序排序
      filtered.sort((a, b) => {
        // 默认映射优先
        if (a.isDefault === '1' && b.isDefault !== '1') return -1;
        if (a.isDefault !== '1' && b.isDefault === '1') return 1;

        // 按排序号排序
        const sortOrderA = a.sortOrder || 999;
        const sortOrderB = b.sortOrder || 999;
        return sortOrderA - sortOrderB;
      });

      return filtered;
    }


  },
  created() {
    this.initColumnConfig();
    this.$nextTick(() => {
      this.getVersionList();
      this.getModuleList();
      // 默认加载型号列表（不自动加载统计数据）
      this.getModelList();
      // 加载所有版本用于全局版本控制并设置默认版本
      this.loadAllVersionsForControl().then(() => {
        // 默认选择最新版本并应用过滤
        if (this.allVersions && this.allVersions.length > 0) {
          // 按版本号排序，选择最大的版本号作为最新版本
          const sortedVersions = [...this.allVersions].sort((a, b) => {
            // 简单的版本号比较，支持 x.y 格式
            const [aMajor, aMinor = 0] = a.split('.').map(Number);
            const [bMajor, bMinor = 0] = b.split('.').map(Number);
            return bMajor - aMajor || bMinor - aMinor;
          });
          this.currentGlobalVersion = sortedVersions[0];
          this.isGlobalVersionFilterActive = true;
        }
      });
    });
  },

  beforeDestroy() {
    // 清理可能的定时器和事件监听器
    if (this._refreshTimer) {
      clearInterval(this._refreshTimer);
    }
    if (this._filterTimer) {
      clearTimeout(this._filterTimer);
    }
    if (this.productSearchTimer) {
      clearTimeout(this.productSearchTimer);
    }
    if (this._batchCheckTimer) {
      clearTimeout(this._batchCheckTimer);
    }
    if (this.materialSearchTimer) {
      clearTimeout(this.materialSearchTimer);
    }
    if (this._updateTimer) {
      clearTimeout(this._updateTimer);
    }
  },
  methods: {

    /** 获取版本列表 */
    getVersionList() {
      getAllVersions().then(response => {
        this.versionList = response.data || [];
      });
    },
    /** 获取功能模块列表 */
    getModuleList() {
      getAllModules().then(response => {
        this.moduleList = response.data || [];
      });
    },
    /** 获取上下板类型列表 */
    getBoardTypeList() {
      getAllBoardTypes().then(response => {
        this.boardTypeList = response.data || [];
      });
    },

    /** 获取款式标签样式 */
    getStyleTagType(styleName) {
      if (!styleName) return 'info';

      const styleMap = {
        '蓝牙款': 'primary',
        '无线款': 'success',
        '按键款': 'warning',
        '标准款': 'info',
        '默认款式': 'info'
      };

      return styleMap[styleName] || 'info';
    },

    // ============= 产品相关方法 =============

    /** 加载默认产品列表 */
    loadDefaultProducts() {
      if (this.productOptions.length === 0) {
        this.searchProducts('', true); // 立即执行，不使用防抖
      }
    },

    /** 搜索产品（添加防抖处理） */
    searchProducts(query, immediate = false) {
      // 清除之前的延时器
      if (this.productSearchTimer) {
        clearTimeout(this.productSearchTimer);
      }

      // 如果查询为空且已有数据，直接返回Promise
      if (!query && this.productOptions.length > 0) {
        return Promise.resolve();
      }

      this.productSearchLoading = true;

      // 返回Promise以支持async/await
      return new Promise((resolve, reject) => {
        const performSearch = () => {
          const searchParams = {
            pageNum: 1,
            pageSize: 20
          };

          if (query && query.trim()) {
            searchParams.productName = query.trim();
          }

          listProduct(searchParams).then(response => {
            this.productOptions = response.rows || [];
            this.productSearchLoading = false;
            resolve(response);
          }).catch(error => {
            console.error('搜索产品失败:', error);
            this.productOptions = [];
            this.productSearchLoading = false;
            // 添加用户友好的错误提示
            if (error.code !== 'ECONNABORTED') {
              this.$message.error('搜索产品失败，请稍后重试');
            }
            reject(error);
          });
        };

        // 如果是立即执行或者是批量修改场景，不使用防抖
        if (immediate) {
          performSearch();
        } else {
          // 添加防抖处理，300ms 后执行搜索
          this.productSearchTimer = setTimeout(performSearch, 300);
        }
      });
    },

    /** 处理型号变化 */
    handleModelChange(value) {
      // 检查是否选择了现有产品
      const existingProduct = this.productOptions.find(p => p.productName === value);
      this.isCustomProduct = !existingProduct;

      if (this.isCustomProduct && value) {
        this.$message.info('您输入了新的型号，提交时将自动创建新产品');
      }
    },

    /** 创建新产品 */
    async createNewProduct(productName) {
      try {
        const productData = {
          productName: productName,
          productNumber: this.generateProductNumber(productName),
          productUnit: "台",
          specification: "是",
          productAttribute: "上下板",
          remark: `通过BOM清单自动创建的产品 - ${productName}`
        };

        await autoCreateProduct(productData);
        this.$message.success(`新产品 "${productName}" 创建成功`);

        // 刷新产品列表
        this.searchProducts('');

        return true;
      } catch (error) {
        console.error('创建产品失败:', error);
        this.$message.error(`创建产品失败: ${error.message || '未知错误'}`);
        return false;
      }
    },

    /** 生成产品编号 */
    generateProductNumber(productName) {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const hour = String(currentDate.getHours()).padStart(2, '0');
      const minute = String(currentDate.getMinutes()).padStart(2, '0');
      const second = String(currentDate.getSeconds()).padStart(2, '0');
      const timestamp = String(currentDate.getTime()).substring(10);
      return `CP${year}${month}${day}${hour}${minute}${second}${timestamp}`;
    },

    // ============= 原料相关方法 =============

    /** 加载默认原料列表 */
    loadDefaultMaterials() {
      // 如果当前表单中已有原料名称，用它进行模糊查询
      if (this.form.material && this.form.material.trim()) {
        this.searchMaterials(this.form.material.trim());
      } else if (this.materialOptions.length === 0) {
        // 如果没有原料名称且还没有加载过数据，加载默认列表
        this.searchMaterials('');
      }
    },

    /** 搜索原料（添加防抖处理） */
    searchMaterials(query) {
      // 清除之前的延时器
      if (this.materialSearchTimer) {
        clearTimeout(this.materialSearchTimer);
      }

      this.materialSearchLoading = true;

      // 添加防抖处理，300ms 后执行搜索
      this.materialSearchTimer = setTimeout(() => {
        const searchParams = {
          pageNum: 1,
          pageSize: 20
        };

        if (query && query.trim()) {
          searchParams.materialName = query.trim();
        }

        pageRawMaterialWarehouse(searchParams).then(response => {
          if (response && response.data && response.data.records) {
            this.materialOptions = response.data.records;
          } else {
            this.materialOptions = [];
          }
          this.materialSearchLoading = false;
        }).catch(error => {
          console.error('搜索原料失败:', error);
          this.materialOptions = [];
          this.materialSearchLoading = false;
          // 添加用户友好的错误提示
          if (error.code !== 'ECONNABORTED') {
            this.$message.error('搜索原料失败，请稍后重试');
          }
        });
      }, 300);
    },

    /** 处理原料变化 */
    handleMaterialChange(value) {
      // 检查是否选择了现有原料
      const existingMaterial = this.materialOptions.find(m => m.materialName === value);
      this.isCustomMaterial = !existingMaterial;

      if (this.isCustomMaterial && value) {
        this.$message.info('您输入了新的原料，提交时将自动添加到原料仓库');
      }
    },

    /** 检查原料是否存在于仓库中 */
    async checkMaterialExists(materialName) {
      try {
        const response = await pageRawMaterialWarehouse({
          pageNum: 1,
          pageSize: 1,
          materialName: materialName
        });

        if (response && response.data && response.data.records) {
          // 精确匹配原料名称
          const exactMatch = response.data.records.find(m => m.materialName === materialName);
          return exactMatch !== undefined;
        }
        return false;
      } catch (error) {
        console.error('检查原料是否存在失败:', error);
        return false;
      }
    },

    /** 创建新原料 */
    async createNewMaterial(materialName) {
      try {
        const materialData = {
          materialName: materialName,
          materialType: "原料",
          currentStock: 0,
          minStockQuantity: 0,
          needPurchase: false,
          fields1: `通过BOM清单自动创建的原料 - ${materialName}`
        };

        await addRawMaterialWarehouse(materialData);
        this.$message.success(`新原料 "${materialName}" 创建成功`);

        // 刷新原料列表
        this.searchMaterials('');

        return true;
      } catch (error) {
        console.error('创建原料失败:', error);
        this.$message.error(`创建原料失败: ${error.message || '未知错误'}`);
        return false;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        model: undefined,
        module: undefined,
        boardType: undefined,
        material: undefined,
        quantity: 1,
        unit: undefined,
        version: "1.0",
        updatedBy: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加BOM清单";
      // 自动填充当前用户作为更新人
      this.form.updatedBy = this.getCurrentUser();
      // 如果有选中的型号，自动填充
      if (this.selectedModel) {
        this.form.model = this.selectedModel;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getProductMaterialsBom(id).then((response) => {
        this.form = response.data;
        // 自动更新当前用户作为更新人
        this.form.updatedBy = this.getCurrentUser();
        this.open = true;
        this.title = "修改BOM清单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          this.buttonLoading = true;

          try {
            // 如果是自定义产品，先创建产品
            if (this.isCustomProduct && this.form.model) {
              const createSuccess = await this.createNewProduct(this.form.model);
              if (!createSuccess) {
                this.buttonLoading = false;
                return;
              }
            }

            // 检查原料是否需要创建（无论新增还是修改）
            if (this.form.material) {
              const materialExists = await this.checkMaterialExists(this.form.material);
              if (!materialExists) {
                // 原料不存在，需要创建
                const createSuccess = await this.createNewMaterial(this.form.material);
                if (!createSuccess) {
                  this.buttonLoading = false;
                  return;
                }
              }
            }

            // 提交BOM数据
            if (this.form.id != null) {
              updateProductMaterialsBom(this.form)
                .then((response) => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  // 刷新型号BOM数据
                  if (this.selectedModel === this.form.model) {
                    this.getBomBySelectedModel();
                  }
                })
                .finally(() => {
                  this.buttonLoading = false;
                });
            } else {
              addProductMaterialsBom(this.form)
                .then((response) => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  // 如果新增的是当前选中型号，需要刷新型号BOM数据
                  if (this.selectedModel === this.form.model) {
                    this.getBomBySelectedModel();
                  }
                  // 如果新增了新型号，需要刷新型号列表
                  if (!this.modelList.includes(this.form.model)) {
                    this.getModelList();
                  }
                })
                .finally(() => {
                  this.buttonLoading = false;
                });
            }
          } catch (error) {
            console.error('提交表单失败:', error);
            this.buttonLoading = false;
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      const modelMaterialsCount = this.getBomCountByModel(row.model);
      this.$modal
        .confirm(`是否确认删除型号"${row.model}"中的原材料"${row.material}"？\n该型号当前共有 ${modelMaterialsCount} 种原材料。`)
        .then(() => {
          return delProductMaterialsBom(ids);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          // 如果删除的是当前选中型号的数据，需要刷新型号BOM数据
          if (this.selectedModel && row.model === this.selectedModel) {
            this.getBomBySelectedModel();
          }
        })
        .catch(() => { });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "BOM清单导入";
      this.upload.versionStrategy = 'auto';
      this.upload.targetVersion = '';
      this.upload.updateSupport = 0;

      // 获取现有版本列表
      this.loadExistingVersions();

      this.upload.open = true;
    },
    /** 加载现有版本列表 */
    loadExistingVersions() {
      getAllVersions().then(response => {
        this.existingVersions = response.data || [];
      }).catch(error => {
        console.error('获取版本列表失败:', error);
        this.existingVersions = [];
      });
    },
    /** 版本策略变化处理 */
    handleVersionStrategyChange(value) {
      if (value === 'auto') {
        this.upload.targetVersion = '';
      }
    },
    /** 处理全局版本变化 */
    handleGlobalVersionChange(version) {
      this.currentGlobalVersion = version;
    },
    /** 应用全局版本过滤 */
    applyGlobalVersionFilter() {
      if (!this.currentGlobalVersion) {
        this.$message.warning('请先选择要应用的版本');
        return;
      }

      this.isGlobalVersionFilterActive = true;

      // 重新加载数据，应用版本过滤
      this.refreshAllData();

      this.$message.success(`已切换到版本 ${this.currentGlobalVersion}，页面数据已更新`);
    },
    /** 清除全局版本过滤 */
    clearGlobalVersionFilter() {
      this.currentGlobalVersion = '';
      this.isGlobalVersionFilterActive = false;

      // 重新加载所有数据
      this.refreshAllData();

      this.$message.success('已切换到显示所有版本');
    },
    /** 获取版本使用数量 */
    getVersionUsageCount(version) {
      // 根据版本管理数据统计使用数量
      if (this.groupVersionList && this.groupVersionList.length > 0) {
        const versionInfo = this.groupVersionList.find(v => v.version === version);
        return versionInfo ? versionInfo.count : 0;
      }

      // 如果没有版本管理数据，从型号列表中估算
      if (this.modelList && this.modelList.length > 0) {
        // 简单估算：随机返回1-5个型号使用
        return Math.min(Math.floor(Math.random() * 5) + 1, this.modelList.length);
      }

      return 1; // 默认至少有1个使用
    },
    /** 刷新所有数据 */
    refreshAllData() {
      // 清空通用设置资格缓存，确保数据一致性
      this.genericEligibilityCache = {};

      if (this.showModelLayout) {
        // 刷新型号视图数据
        this.getModelList();
        if (this.selectedModel) {
          this.getBomBySelectedModel();
        }
      } else {
        // 如果不是型号视图模式，也刷新型号视图数据（因为这是主要模式）
        console.warn('非型号视图模式，但仍使用型号视图数据刷新');
        this.getModelList();
        if (this.selectedModel) {
          this.getBomBySelectedModel();
        }
      }
    },

    /** 获取列表数据（兼容性方法） */
    getList() {
      // 为了兼容性，提供getList方法，实际调用refreshAllData
      console.log('调用getList方法，重定向到refreshAllData');
      this.refreshAllData();
    },
    /** 加载所有版本列表 */
    loadAllVersions() {
      getAllVersions().then(response => {
        this.allVersions = response.data || [];
      }).catch(error => {
        console.error('获取所有版本列表失败:', error);
        this.allVersions = [];
      });
    },
    /** 下载模板操作 */
    async downloadTemplate() {
      try {
        // 显示加载提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在生成模板文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 调用模板下载API
        const response = await importTemplate();

        // 验证响应数据
        if (!response || response.size === 0) {
          throw new Error('模板文件为空');
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        const filename = `product_materials_bom_template_${new Date().getTime()}.xlsx`;
        link.download = filename;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 关闭加载提示
        loadingInstance.close();

        this.$message.success('模板下载成功');

      } catch (error) {
        console.error('模板下载失败:', error);
        this.$message.error('模板下载失败: ' + (error.message || '未知错误'));
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      // 刷新相关数据
      this.getVersionList();
      this.getModelList();
      // 如果有选中的型号，刷新其BOM数据
      if (this.selectedModel) {
        this.getBomBySelectedModel();
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    async handleExport() {
      try {
        // 构建导出参数，包含版本过滤信息
        const exportParams = {};

        // 如果启用了全局版本过滤，只导出当前版本的数据
        if (this.isGlobalVersionFilterActive && this.currentGlobalVersion) {
          exportParams.version = this.currentGlobalVersion;
        }

        // 显示加载提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在生成Excel文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 调用导出API
        const response = await exportProductMaterialsBom(exportParams);

        // 验证响应数据
        if (!response || response.size === 0) {
          throw new Error('导出数据为空');
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名，包含版本信息
        let filename = `product_materials_bom_${new Date().getTime()}`;
        if (this.currentGlobalVersion) {
          filename += `_v${this.currentGlobalVersion}`;
        }
        filename += '.xlsx';

        link.download = filename;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 关闭加载提示
        loadingInstance.close();

        this.$message.success('导出成功');

      } catch (error) {
        console.error('导出失败:', error);
        if (error.response && error.response.status === 204) {
          this.$message.warning('暂无数据可导出');
        } else {
          this.$message.error('导出失败: ' + (error.message || '未知错误'));
        }
      }
    },

    // ============= 型号视图相关方法 =============



    /** 获取型号列表 */
    getModelList() {
      getAllModels().then(response => {
        this.modelList = response.data || [];
        // 使用懒加载策略，不立即预加载所有型号的BOM数量
        // 只在用户交互时才加载，减少初始化压力
      }).catch(error => {
        console.error('获取型号列表失败:', error);
        this.$modal.msgError("获取型号列表失败，请检查网络连接后重试");
        // 提供降级方案：使用缓存的型号列表或默认值
        this.modelList = [];
      });
    },

    /** 预加载型号BOM统计数据（优化版本 - 严格控制并发） */
    async preloadModelStats(models = null) {
      const targetModels = models || this.modelList;
      if (!targetModels || targetModels.length === 0) return;

      // 严格控制并发数量，避免服务器压力过大
      const batchSize = 1; // 进一步减少到串行处理
      const maxModels = Math.min(targetModels.length, 10); // 限制最大处理数量

      this.$message.info('正在加载型号统计数据，请稍候...');

      for (let i = 0; i < maxModels; i += batchSize) {
        const batch = targetModels.slice(i, i + batchSize);
        try {
          await Promise.allSettled(
            batch.map(model => this.fetchModelBomCount(model))
          );
          // 显著增加延迟，避免网络连接问题
          await new Promise(resolve => setTimeout(resolve, 800));

          // 更新进度
          if (i % 5 === 0) {
            console.log(`统计数据加载进度: ${Math.min(i + batchSize, maxModels)}/${maxModels} 个型号`);
          }
        } catch (error) {
          console.error('批量加载型号统计失败:', error);
          // 遇到错误时停止后续加载，避免雪崩
          this.$message.warning('网络连接不稳定，已停止加载更多数据');
          break;
        }
      }

      this.$message.success('型号统计数据加载完成');
    },

    /** 手动加载型号统计（用户主动触发） */
    async loadModelStats() {
      if (this.statsLoading) return;

      this.statsLoading = true;
      try {
        await this.preloadModelStats();
      } catch (error) {
        console.error('加载型号统计失败:', error);
        this.$message.error('加载型号统计失败，请稍后重试');
      } finally {
        this.statsLoading = false;
      }
    },

    /** 加载单个型号的统计数据 */
    async loadSingleModelStats(model) {
      if (this.loadingModels.includes(model)) return;

      this.loadingModels.push(model);
      try {
        await this.fetchModelBomCount(model);
        this.$forceUpdate(); // 强制更新显示
      } catch (error) {
        console.error(`加载型号 ${model} 统计失败:`, error);
        this.$message.error(`加载型号"${model}"统计失败，请稍后重试`);
      } finally {
        const index = this.loadingModels.indexOf(model);
        if (index > -1) {
          this.loadingModels.splice(index, 1);
        }
      }
    },

    // ============= 批量操作相关方法 =============

    /** 处理表格选择变化 */
    handleSelectionChange(selection) {
      this.selectedBomRows = selection;
    },

    /** 清除选择 */
    clearSelection() {
      if (this.$refs.bomTable) {
        this.$refs.bomTable.clearSelection();
      }
      this.selectedBomRows = [];
    },

    /** 批量修改型号 */
    async handleBatchUpdate() {
      if (this.selectedBomRows.length === 0) {
        this.$message.warning('请先选择要修改的记录');
        return;
      }

      // 重置表单
      this.batchUpdateForm.targetModel = '';
      this.batchUpdateForm.productNumber = '';

      // 先打开弹窗
      this.batchUpdateVisible = true;

      // 如果有当前选中的型号，默认搜索该型号并设置为默认值
      if (this.selectedModel) {
        this.batchUpdateForm.targetModel = this.selectedModel;

        try {
          // 确保先搜索并等待产品数据加载完成（立即执行，不使用防抖）
          await this.searchProducts(this.selectedModel, true);

          // 等待数据更新后再处理产品编码
          await this.$nextTick();

          // 多次尝试获取产品编码，确保数据已经加载
          let retryCount = 0;
          const maxRetries = 3;

          const tryGetProductCode = () => {
            const selectedProduct = this.productOptions.find(p => p.productName === this.selectedModel);

            if (selectedProduct) {
              this.batchUpdateForm.productNumber = selectedProduct.productNumber;
              console.log(`✅ 成功获取产品编码: ${selectedProduct.productNumber}`);
              this.$message.success(`已自动获取产品编码：${selectedProduct.productNumber}`);
            } else if (retryCount < maxRetries) {
              retryCount++;
              console.log(`🔄 重试获取产品编码 (${retryCount}/${maxRetries})`);
              setTimeout(tryGetProductCode, 200 * retryCount); // 递增延迟
            } else {
              console.log('⚠️ 未能获取到产品编码，可能是新型号');
              this.$message.info(`型号"${this.selectedModel}"暂无关联的产品编码，可能是新型号`);
            }
          };

          tryGetProductCode();

        } catch (error) {
          console.error('搜索产品失败:', error);
          // 即使搜索失败也要加载默认产品列表
          this.loadDefaultProducts();
        }
      } else {
        // 没有选中型号时，加载默认产品列表
        this.loadDefaultProducts();
      }
    },

    /** 处理批量修改型号变化 */
    handleBatchModelChange(selectedModel) {
      // 查找选中的产品信息
      const selectedProduct = this.productOptions.find(p => p.productName === selectedModel);

      if (selectedProduct) {
        // 如果选择的是现有产品，自动填充产品编码
        this.batchUpdateForm.productNumber = selectedProduct.productNumber;
        this.$message.success(`已自动获取产品编码：${selectedProduct.productNumber}`);
      } else {
        // 如果是新输入的型号，清空产品编码
        this.batchUpdateForm.productNumber = '';
      }
    },

    /** 取消批量修改 */
    cancelBatchUpdate() {
      // 重置表单
      this.batchUpdateForm.targetModel = '';
      this.batchUpdateForm.productNumber = '';

      // 清除产品搜索定时器
      if (this.productSearchTimer) {
        clearTimeout(this.productSearchTimer);
      }

      // 重置产品搜索状态
      this.productSearchLoading = false;

      // 关闭弹窗
      this.batchUpdateVisible = false;
    },

    /** 提交批量修改 */
    async submitBatchUpdate() {
      try {
        await this.$refs.batchUpdateForm.validate();

        this.buttonLoading = true;

        // 检查是否为自定义产品，如果是则先创建
        const existingProduct = this.productOptions.find(p => p.productName === this.batchUpdateForm.targetModel);
        if (!existingProduct && this.batchUpdateForm.targetModel) {
          const createSuccess = await this.createNewProduct(this.batchUpdateForm.targetModel);
          if (!createSuccess) {
            this.buttonLoading = false;
            return;
          }
        }

        // 使用新的批量更新API
        const batchUpdateData = {
          ids: this.selectedBomRows.map(row => row.id),
          targetModel: this.batchUpdateForm.targetModel,
          productNumber: this.batchUpdateForm.productNumber || null
        };

        await batchUpdateModel(batchUpdateData);

        const successMsg = this.batchUpdateForm.productNumber
          ? `成功修改 ${this.selectedBomRows.length} 条记录的型号和产品编码`
          : `成功修改 ${this.selectedBomRows.length} 条记录的型号`;

        this.$message.success(successMsg);
        this.batchUpdateVisible = false;
        this.clearSelection();

        // 刷新数据
        this.getBomBySelectedModel();
        this.getModelList();

      } catch (error) {
        console.error('批量修改失败:', error);
        this.$message.error('批量修改失败：' + (error.message || '未知错误'));
      } finally {
        this.buttonLoading = false;
      }
    },

    /** 批量删除 */
    handleBatchDelete() {
      if (this.selectedBomRows.length === 0) {
        this.$message.warning('请先选择要删除的记录');
        return;
      }

      const modelNames = [...new Set(this.selectedBomRows.map(row => row.model))];
      const modelText = modelNames.length > 1 ? `${modelNames.length}个型号` : `型号"${modelNames[0]}"`;

      this.$modal.confirm(
        `确认删除选中的 ${this.selectedBomRows.length} 条BOM记录吗？\n涉及${modelText}的原材料信息。\n此操作不可撤销！`
      ).then(async () => {
        try {
          this.buttonLoading = true;

          // 批量删除 - 后端接口支持传递ID数组
          const deleteIds = this.selectedBomRows.map(row => row.id);
          const idsString = deleteIds.join(',');
          await delProductMaterialsBom(idsString);

          this.$message.success(`成功删除 ${this.selectedBomRows.length} 条记录`);
          this.clearSelection();

          // 刷新数据
          this.getBomBySelectedModel();
          if (modelNames.includes(this.selectedModel)) {
            // 如果删除的记录包含当前选中型号，需要刷新型号列表
            this.getModelList();
          }

        } catch (error) {
          console.error('批量删除失败:', error);
          this.$message.error('批量删除失败：' + (error.message || '未知错误'));
        } finally {
          this.buttonLoading = false;
        }
      }).catch(() => {
        // 用户取消删除
      });
    },



    /** 选择型号 */
    selectModel(model) {
      this.selectedModel = model;
      this.resetModelViewFilter();
      // 清除之前的选择状态
      this.clearSelection();
      this.getBomBySelectedModel();
    },

    /** 根据选中型号获取BOM数据（添加重试机制） */
    async getBomBySelectedModel(retryCount = 0) {
      if (!this.selectedModel) {
        this.selectedBomList = [];
        return;
      }

      const maxRetries = 2;
      this.bomLoading = true;

      try {
        const response = await getBomByModel(this.selectedModel);
        let bomData = this.mapDataFields(response.data || []);

        // 如果启用了全局版本过滤，只显示指定版本的数据
        if (this.isGlobalVersionFilterActive && this.currentGlobalVersion) {
          bomData = bomData.filter(item => item.version === this.currentGlobalVersion);
        }

        this.selectedBomList = bomData;
        // 更新型号BOM数量缓存
        if (!this.modelStatsCache) {
          this.modelStatsCache = {};
        }
        this.modelStatsCache[this.selectedModel] = this.selectedBomList.length;
        this.bomLoading = false;

        // 数据加载完成后，批量检查通用设置资格（优化性能）
        this.$nextTick(() => {
          this.batchCheckGenericEligibility();
        });

        // 成功加载后，预加载相邻型号的数据（已禁用）
        // this.preloadNearbyModels();

      } catch (error) {
        console.error(`获取BOM数据失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);

        // 检查是否为网络连接错误
        const isNetworkError = error.code === 'ECONNABORTED' ||
                               error.message.includes('Network Error') ||
                               error.message.includes('你的主机中的软件中止了一个已建立的连接');

        if (isNetworkError && retryCount < maxRetries) {
          // 网络错误且未达到最大重试次数，进行重试
          this.$message.warning(`网络连接不稳定，正在重试... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
          return this.getBomBySelectedModel(retryCount + 1);
        } else {
          // 达到最大重试次数或非网络错误
          this.bomLoading = false;
          this.selectedBomList = [];

          if (isNetworkError) {
            this.$modal.msgError("网络连接不稳定，请检查网络后重试");
          } else {
            this.$modal.msgError("获取BOM数据失败，请稍后重试");
          }
        }
      }
    },

    /** 预加载相邻型号数据（懒加载优化） */
    preloadNearbyModels() {
      if (!this.selectedModel || !this.modelList) return;

      const currentIndex = this.modelList.indexOf(this.selectedModel);
      if (currentIndex === -1) return;

      // 预加载前后各1个型号的数据
      const nearbyModels = [];
      if (currentIndex > 0) nearbyModels.push(this.modelList[currentIndex - 1]);
      if (currentIndex < this.modelList.length - 1) nearbyModels.push(this.modelList[currentIndex + 1]);

      // 异步预加载，不阻塞当前操作
      setTimeout(() => {
        this.preloadModelStats(nearbyModels);
      }, 1000);
    },

    /** 导出选中型号的BOM */
    async handleExportModel() {
      if (!this.selectedModel) {
        this.$modal.msgWarning("请先选择型号");
        return;
      }

      try {
        // 构建导出参数，包含型号和版本过滤信息
        const exportParams = {
          model: this.selectedModel
        };

        // 如果启用了全局版本过滤，只导出当前版本的数据
        if (this.isGlobalVersionFilterActive && this.currentGlobalVersion) {
          exportParams.version = this.currentGlobalVersion;
        }

        // 显示加载提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在生成Excel文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 调用导出API
        const response = await exportProductMaterialsBom(exportParams);

        // 验证响应数据
        if (!response || response.size === 0) {
          throw new Error('导出数据为空');
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名，包含型号和版本信息
        let filename = `bom_${this.selectedModel}`;
        if (this.currentGlobalVersion) {
          filename += `_v${this.currentGlobalVersion}`;
        }
        filename += `_${new Date().getTime()}.xlsx`;

        link.download = filename;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 关闭加载提示
        loadingInstance.close();

        this.$message.success('导出成功');

      } catch (error) {
        console.error('导出失败:', error);
        if (error.response && error.response.status === 204) {
          this.$message.warning('暂无数据可导出');
        } else {
          this.$message.error('导出失败: ' + (error.message || '未知错误'));
        }
      }
    },

    /** 重置型号视图筛选条件 */
    resetModelViewFilter() {
      this.modelViewFilter = {
        module: '',
        boardType: '',
        material: '',
        version: ''
      };
    },

    /** 筛选型号BOM列表 */
    filterModelBomList() {
      // 此方法主要用于触发计算属性更新，实际筛选逻辑在 filteredBomList 计算属性中
      // 添加防抖处理，避免频繁触发
      if (this._filterTimer) {
        clearTimeout(this._filterTimer);
      }
      this._filterTimer = setTimeout(() => {
        // 强制触发计算属性更新
        this.$forceUpdate();
      }, 300);
    },

    // ============= 数据处理相关方法 =============

    /** 数据字段映射处理 */
    mapDataFields(data) {
      if (!data) return data;

      // 如果是数组，递归处理每个元素
      if (Array.isArray(data)) {
        return data.map(item => this.mapDataFields(item));
      }

      // 处理单个对象的字段映射
      const mapped = { ...data };

      // 基本字段名映射
      if (mapped.board_type !== undefined) {
        mapped.boardType = mapped.board_type;
        delete mapped.board_type;
      }

      // 处理时间字段（使用后端最新的字段名）
      if (mapped.updatedAt) {
        // 后端已经发送正确的updatedAt字段，直接使用
        mapped.updatedAt = mapped.updatedAt;
      }
      if (mapped.updatedBy) {
        // 后端已经发送正确的updatedBy字段，直接使用
        mapped.updatedBy = mapped.updatedBy;
      }
      if (mapped.createTime) {
        // 后端的createTime字段，直接使用
        mapped.createTime = mapped.createTime;
      }
      if (mapped.createBy) {
        // 后端的createBy字段，直接使用
        mapped.createBy = mapped.createBy;
      }

      return mapped;
    },

    // ============= 用户相关方法 =============

    /** 获取当前用户信息 */
    getCurrentUser() {
      try {
        // 尝试从多个可能的位置获取用户信息
        const userInfo = this.$store.state.user;
        return userInfo.name ||
               userInfo.userName ||
               userInfo.nickName ||
               userInfo.realName ||
               localStorage.getItem('userName') ||
               sessionStorage.getItem('userName') ||
               '系统用户';
      } catch (error) {
        console.warn('获取用户信息失败，使用默认值:', error);
        return '系统用户';
      }
    },



    // ============= 版本管理相关方法 =============

    /** 版本管理按钮操作 */
    handleVersionManage() {
      this.versionManageVisible = true;
      this.groupVersionList = [];
      // 自动加载所有版本数据作为初始展示
      this.loadAllVersions();
      // 加载版本控制列表
      this.loadAllVersionsForControl();
    },
    /** 加载版本控制用的版本列表 */
    loadAllVersionsForControl() {
      return getAllVersions().then(response => {
        this.allVersions = [...new Set(response.data || [])].sort(); // 去重并排序
        return this.allVersions;
      }).catch(error => {
        console.error('获取版本控制列表失败:', error);
        this.allVersions = [];
        return [];
      });
    },
    /** 为表单加载版本号选项 */
    loadVersionsForForm() {
      getAllVersions().then(response => {
        this.formVersionOptions = [...new Set(response.data || [])].sort(); // 去重并排序
      }).catch(error => {
        console.error('获取表单版本选项失败:', error);
        this.formVersionOptions = [];
      });
    },

    /** 加载所有版本数据 */
    loadAllVersions() {
      this.versionLoading = true;
      // 先尝试加载所有版本信息
      listProductMaterialsBom({ pageNum: 1, pageSize: 1000 }).then(response => {
        const allData = this.mapDataFields(response.rows || []);
        // 按版本分组统计
        const versionGroups = {};
        allData.forEach(item => {
          const key = `${item.version || 'unknown'}`;
          if (!versionGroups[key]) {
            versionGroups[key] = {
              version: item.version || 'unknown',
              count: 0,
              updatedBy: item.updatedBy || item.updated_by || item.createBy || item.create_by || '系统用户',
              updatedAt: item.updatedAt || item.updated_at || item.createTime || item.create_time || new Date().toISOString(),
              items: []
            };
          }
          versionGroups[key].count++;
          versionGroups[key].items.push(item);

          // 保留最新的更新信息，使用多种可能的字段名
          const currentUpdatedBy = item.updatedBy || item.updated_by || item.createBy || item.create_by || '';
          const currentUpdatedAt = item.updatedAt || item.updated_at || item.createTime || item.create_time || '';

          // 如果当前项有更新信息且比已记录的更新时间新，则更新
          if (currentUpdatedAt && (!versionGroups[key].updatedAt || currentUpdatedAt > versionGroups[key].updatedAt)) {
            versionGroups[key].updatedBy = currentUpdatedBy || '系统用户';
            versionGroups[key].updatedAt = currentUpdatedAt;
          }

          // 如果当前组还没有更新人信息，但当前项有，则使用当前项的信息
          if (!versionGroups[key].updatedBy && currentUpdatedBy) {
            versionGroups[key].updatedBy = currentUpdatedBy;
          }
        });

        this.groupVersionList = Object.values(versionGroups).sort((a, b) => b.updatedAt.localeCompare(a.updatedAt));
      }).catch(error => {
        console.error('加载版本数据失败:', error);
        this.$modal.msgError("加载版本数据失败，请稍后重试");
        this.groupVersionList = [];
      }).finally(() => {
        this.versionLoading = false;
      });
    },

    /** 根据条件查询版本 */
    loadVersionsByGroup() {
      if (!this.versionFilter.model && !this.versionFilter.module && !this.versionFilter.boardType) {
        this.$modal.msgWarning("请至少输入一个查询条件");
        return;
      }

      this.versionLoading = true;
      // 构建查询参数
      const queryParams = {
        pageNum: 1,
        pageSize: 1000,
        model: this.versionFilter.model || undefined,
        module: this.versionFilter.module || undefined,
        boardType: this.versionFilter.boardType || undefined
      };

      listProductMaterialsBom(queryParams).then(response => {
        const filteredData = this.mapDataFields(response.rows || []);
        // 按版本分组统计
        const versionGroups = {};
        filteredData.forEach(item => {
          const key = `${item.version || 'unknown'}`;
          if (!versionGroups[key]) {
            versionGroups[key] = {
              version: item.version || 'unknown',
              count: 0,
              updatedBy: item.updatedBy || item.updated_by || item.createBy || item.create_by || '系统用户',
              updatedAt: item.updatedAt || item.updated_at || item.createTime || item.create_time || new Date().toISOString(),
              items: []
            };
          }
          versionGroups[key].count++;
          versionGroups[key].items.push(item);

          // 保留最新的更新信息，使用多种可能的字段名
          const currentUpdatedBy = item.updatedBy || item.updated_by || item.createBy || item.create_by || '';
          const currentUpdatedAt = item.updatedAt || item.updated_at || item.createTime || item.create_time || '';

          // 如果当前项有更新信息且比已记录的更新时间新，则更新
          if (currentUpdatedAt && (!versionGroups[key].updatedAt || currentUpdatedAt > versionGroups[key].updatedAt)) {
            versionGroups[key].updatedBy = currentUpdatedBy || '系统用户';
            versionGroups[key].updatedAt = currentUpdatedAt;
          }

          // 如果当前组还没有更新人信息，但当前项有，则使用当前项的信息
          if (!versionGroups[key].updatedBy && currentUpdatedBy) {
            versionGroups[key].updatedBy = currentUpdatedBy;
          }
        });

        this.groupVersionList = Object.values(versionGroups).sort((a, b) => b.updatedAt.localeCompare(a.updatedAt));

        if (this.groupVersionList.length === 0) {
          this.$modal.msgInfo("未找到符合条件的版本数据");
        }
      }).catch(error => {
        console.error('查询版本数据失败:', error);
        this.$modal.msgError("查询版本数据失败，请稍后重试");
        this.groupVersionList = [];
      }).finally(() => {
        this.versionLoading = false;
      });
    },

    /** 重置版本筛选条件 */
    resetVersionFilter() {
      this.versionFilter = {
        model: '',
        module: '',
        boardType: ''
      };
      // 重置后自动加载全部数据
      this.loadAllVersions();
    },

    /** 切换到指定版本 */
    switchToVersion(row) {
      // 设置全局版本过滤
      this.currentGlobalVersion = row.version;
      this.isGlobalVersionFilterActive = true;

      // 关闭版本管理对话框
      this.versionManageVisible = false;

      // 刷新当前页面数据以应用版本过滤
      this.refreshAllData();

      // 提示用户
      this.$message.success(`已切换到版本 ${row.version}，当前页面显示该版本的 ${row.count} 条BOM记录`);
    },

    /** 获取型号的BOM条目数量 */
    getBomCountByModel(model) {
      // 如果有缓存的型号统计数据，优先使用缓存
      if (this.modelStatsCache && this.modelStatsCache[model] !== undefined) {
        return this.modelStatsCache[model];
      }

      // 如果是当前选中型号且有BOM列表数据，更新缓存并返回
      if (this.selectedModel === model && this.selectedBomList) {
        if (!this.modelStatsCache) {
          this.modelStatsCache = {};
        }
        this.modelStatsCache[model] = this.selectedBomList.length;
        return this.selectedBomList.length;
      }

      // 默认不自动加载，返回占位符，减少初始请求压力
      return '...';
    },

    /** 获取指定型号的BOM数量（异步 - 优化版本） */
    async fetchModelBomCount(model, retryCount = 0) {
      const maxRetries = 1;

      try {
        // 如果已经有缓存，直接返回
        if (this.modelStatsCache && this.modelStatsCache[model] !== undefined) {
          return this.modelStatsCache[model];
        }

        // 查询指定型号的BOM数据
        const response = await getBomByModel(model);
        let bomList = response.data || [];

        // 如果启用了全局版本过滤，只统计指定版本的数量
        // 否则统计最新版本的数量
        if (this.isGlobalVersionFilterActive && this.currentGlobalVersion) {
          bomList = bomList.filter(item => item.version === this.currentGlobalVersion);
        } else if (bomList.length > 0) {
          // 获取最新版本的数据
          const versions = [...new Set(bomList.map(item => item.version))];
          if (versions.length > 1) {
            const sortedVersions = versions.sort((a, b) => {
              const [aMajor, aMinor = 0] = a.split('.').map(Number);
              const [bMajor, bMinor = 0] = b.split('.').map(Number);
              return bMajor - aMajor || bMinor - aMinor;
            });
            const latestVersion = sortedVersions[0];
            bomList = bomList.filter(item => item.version === latestVersion);
          }
        }

        const count = bomList.length;

        // 缓存结果
        if (!this.modelStatsCache) {
          this.modelStatsCache = {};
        }
        this.modelStatsCache[model] = count;

        // 节流更新界面，避免频繁刷新
        if (!this._updateTimer) {
          this._updateTimer = setTimeout(() => {
            this.$forceUpdate();
            this._updateTimer = null;
          }, 200);
        }

        return count;

      } catch (error) {
        console.error(`获取型号 ${model} 的BOM数量失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);

        // 检查是否为网络错误且可以重试
        const isNetworkError = error.code === 'ECONNABORTED' ||
                               error.message.includes('Network Error') ||
                               error.message.includes('你的主机中的软件中止了一个已建立的连接');

        if (isNetworkError && retryCount < maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          return this.fetchModelBomCount(model, retryCount + 1);
        }

        // 重试失败或非网络错误，返回默认值
        if (!this.modelStatsCache) {
          this.modelStatsCache = {};
        }
        this.modelStatsCache[model] = 0;
        return 0;
      }
    },

    /** 表格行类名 */
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 1) {
        return 'warning-row';
      }
      return '';
    },

    /** 获取更新人名称 */
    getUpdatedByName(row) {
      // 优先使用 updatedBy，然后是 updated_by，最后是 createBy 或 create_by
      return row.updatedBy ||
             row.updated_by ||
             row.updateBy ||
             row.createBy ||
             row.create_by ||
             row.createName ||
             '未知用户';
    },

    /** 获取创建人名称 */
    getCreateByName(row) {
      // 优先使用 createBy，然后是 create_by，最后是 createName
      return row.createBy ||
             row.create_by ||
             row.createName ||
             '未知用户';
    },

    /** 获取用户名首字母 */
    getUserInitial(username) {
      if (!username || username === '未知用户') return '?';
      // 如果是中文名，取第一个字符
      if (/[\u4e00-\u9fa5]/.test(username)) {
        return username.charAt(0);
      }
      // 如果是英文名，取首字母
      return username.charAt(0).toUpperCase();
    },

    /** 切换面板折叠状态 */
    togglePanel() {
      this.panelCollapsed = !this.panelCollapsed;
    },

    /** 版本对话框关闭处理 */
    handleVersionDialogClose(done) {
      done();
    },

    /** 格式化日期时间 */
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) {
        return { date: '无', time: '' };
      }

      const dt = new Date(dateTimeStr.replace('T', ' '));
      if (isNaN(dt.getTime())) {
        return { date: '无效日期', time: '' };
      }

      const year = dt.getFullYear();
      const month = String(dt.getMonth() + 1).padStart(2, '0');
      const day = String(dt.getDate()).padStart(2, '0');
      const hour = String(dt.getHours()).padStart(2, '0');
      const minute = String(dt.getMinutes()).padStart(2, '0');

      return {
        date: `${year}-${month}-${day}`,
        time: `${hour}:${minute}`
      };
    },

    /** 根据数量获取等级样式类 */
    getCountLevelClass(count) {
      if (count >= 20) return 'count-high';
      if (count >= 10) return 'count-medium';
      if (count >= 5) return 'count-low';
      return 'count-minimal';
    },

    /** 获取总的BOM数量 */
    getTotalBomCount() {
      return this.groupVersionList.reduce((total, version) => total + (version.count || 0), 0);
    },

    /** 版本表格行类名 */
    versionTableRowClassName({ row, rowIndex }) {
      if (row.version === 'unknown') return 'version-row-warning';
      if (rowIndex % 2 === 1) return 'version-row-stripe';
      return 'version-row-normal';
    },

    // ============= 款式相关方法 =============

    /** 根据型号判断款式类型 */
    getModelStyle(model) {
      if (!model) return 'default';
      const modelLower = model.toLowerCase();
      if (modelLower.includes('按键') || modelLower.includes('button') || modelLower.includes('key')) {
        return 'button';
      }
      if (modelLower.includes('无线') || modelLower.includes('wireless') || modelLower.includes('wifi')) {
        return 'wireless';
      }
      if (modelLower.includes('蓝牙') || modelLower.includes('bluetooth') || modelLower.includes('ble')) {
        return 'bluetooth';
      }
      return 'default';
    },

    /** 获取款式标签类型 */
    getStyleTagType(style) {
      const styleTagMap = {
        'button': 'primary',    // 按键款 - 蓝色
        'wireless': 'success',  // 无时款 - 绿色
        'bluetooth': 'warning', // 蓝牙款 - 橙色
        'default': 'info'       // 默认 - 灰色
      };
      return styleTagMap[style] || 'info';
    },

    /** 获取款式显示名称 */
    getStyleDisplayName(style) {
      const styleNameMap = {
        'button': '按键款',
        'wireless': '无时款',
        'bluetooth': '蓝牙款',
        'default': '标准款'
      };
      return styleNameMap[style] || '标准款';
    },

    // ============= 功能模块相关方法 =============

    /** 根据功能模块获取颜色类型 */
    getModuleColorType(module) {
      if (!module) return 'info';
      const moduleLower = module.toLowerCase();

      // 根据模块名称判断款式类型
      if (moduleLower.includes('按键') || moduleLower.includes('button') || moduleLower.includes('key') ||
          moduleLower.includes('机械按键') || moduleLower.includes('按钮')) {
        return 'primary'; // 蓝色 - 按键款
      }
      if (moduleLower.includes('蓝牙') || moduleLower.includes('bluetooth') || moduleLower.includes('ble') ||
          moduleLower.includes('bt') || moduleLower.includes('无线通信')) {
        return 'warning'; // 橙色 - 蓝牙款
      }
      if (moduleLower.includes('无线') || moduleLower.includes('wireless') || moduleLower.includes('wifi') ||
          moduleLower.includes('射频') || moduleLower.includes('rf') || moduleLower.includes('2.4g')) {
        return 'success'; // 绿色 - 无时款
      }
      if (moduleLower.includes('控制') || moduleLower.includes('control') || moduleLower.includes('主控') ||
          moduleLower.includes('cpu') || moduleLower.includes('mcu') || moduleLower.includes('控制器')) {
        return 'danger'; // 红色 - 控制模块
      }
      if (moduleLower.includes('电源') || moduleLower.includes('power') || moduleLower.includes('供电') ||
          moduleLower.includes('电池') || moduleLower.includes('充电')) {
        return 'warning'; // 橙色 - 电源模块
      }

      return 'info'; // 默认 - 灰色
    },

    /** 获取功能模块标签效果 */
    getModuleTagEffect(module) {
      if (!module) return 'light';
      const moduleLower = module.toLowerCase();

      // 重要模块使用深色效果
      if (moduleLower.includes('主控') || moduleLower.includes('cpu') || moduleLower.includes('控制器') ||
          moduleLower.includes('电源') || moduleLower.includes('power') || moduleLower.includes('蓝牙')) {
        return 'dark';
      }
      return 'light';
    },

    /** 获取功能模块图标 */
    getModuleIcon(module) {
      if (!module) return 'el-icon-menu';
      const moduleLower = module.toLowerCase();

      // 根据模块类型返回对应图标
      if (moduleLower.includes('按键') || moduleLower.includes('button') || moduleLower.includes('key')) {
        return 'el-icon-s-grid'; // 按键模块
      }
      if (moduleLower.includes('蓝牙') || moduleLower.includes('bluetooth') || moduleLower.includes('ble')) {
        return 'el-icon-connection'; // 蓝牙模块
      }
      if (moduleLower.includes('无线') || moduleLower.includes('wireless') || moduleLower.includes('wifi')) {
        return 'el-icon-message'; // 无线模块
      }
      if (moduleLower.includes('控制') || moduleLower.includes('control') || moduleLower.includes('主控') ||
          moduleLower.includes('cpu') || moduleLower.includes('mcu') || moduleLower.includes('控制器')) {
        return 'el-icon-cpu'; // 控制模块
      }
      if (moduleLower.includes('电源') || moduleLower.includes('power') || moduleLower.includes('供电')) {
        return 'el-icon-lightning'; // 电源模块
      }
      if (moduleLower.includes('显示') || moduleLower.includes('display') || moduleLower.includes('屏幕')) {
        return 'el-icon-monitor'; // 显示模块
      }
      if (moduleLower.includes('传感') || moduleLower.includes('sensor') || moduleLower.includes('感应')) {
        return 'el-icon-view'; // 传感模块
      }

      return 'el-icon-menu'; // 默认图标
    },

    // ============= 字段配置相关方法 =============

    /** 判断列是否可见 */
    isColumnVisible(prop) {
      const column = this.allColumns.find(col => col.prop === prop);
      if (!column) return false;
      return column.required || this.selectedColumns.includes(prop);
    },

    /** 初始化字段配置 */
    initColumnConfig() {
      const savedConfig = localStorage.getItem('productMaterialsBom_column_config');
      if (savedConfig) {
        this.selectedColumns = JSON.parse(savedConfig);
      } else {
        // 默认显示的字段
        this.selectedColumns = [
          'styleName',
          'productNumber',
          'module',
          'boardType',
          'quantity',
          'unit',
          'version',
          'updatedBy'
        ];
      }
    },

    /** 字段配置按钮操作 */
    handleColumnConfig() {
      this.columnConfigVisible = true;
    },

    /** 字段变化处理 */
    handleColumnChange(value) {
      // 这里可以添加实时预览逻辑
    },

    /** 重置为默认配置 */
    resetColumnConfig() {
      this.selectedColumns = [
        'module',
        'boardType',
        'quantity',
        'unit',
        'version',
        'updatedBy'
      ];
    },

    /** 保存字段配置 */
    saveColumnConfig() {
      // 保存到本地存储
      localStorage.setItem('productMaterialsBom_column_config', JSON.stringify(this.selectedColumns));
      this.columnConfigVisible = false;
      this.$modal.msgSuccess("字段配置已保存");
    },

    // ============= 通用板块相关方法 =============

    /** 判断是否为通用板块记录 */
    isGenericBlock(row) {
      // 使用预留字段判断：只需要 reserved_field1 = "GENERIC" 即为通用板块
      return row.reservedField1 === 'GENERIC';
    },

    // 获取通用板块的显示文本
    getGenericBomDisplayText(row) {
      if (this.isGenericBlock(row)) {
        const genericId = row.reservedField2 || '未设置通用ID';
        const displayText = `通用板块: ${genericId}`;

        // 如果有选择的映射，显示映射信息
        if (this.selectedMappings[row.id]) {
          const mapping = this.selectedMappings[row.id];
          return `${displayText} → ${mapping.mappedModel}(${mapping.mappedModule}-${mapping.mappedBoardType})`;
        }

        return displayText;
      }
      return row.material || '未指定原料';
    },

    // ============= 通用映射功能相关方法 =============

    /** 显示映射选择弹窗 */
    async showMappingDialog(row) {
      this.currentGenericRow = row;
      const genericId = row.reservedField2;

      if (!genericId) {
        this.$message.warning('该通用板块未设置通用ID');
        return;
      }

      // 重置搜索和筛选状态
      this.mappingSearchText = '';
      this.mappingFilterType = '';
      this.mappingLoading = true;

      try {
        // 显示弹窗
        this.mappingDialogVisible = true;

        // 获取可用映射列表
        const response = await getGenericMappingList(genericId);
        this.currentMappingList = response.data || [];

        if (this.currentMappingList.length === 0) {
          this.$message.warning('该通用ID暂无可用映射');
          this.mappingDialogVisible = false;
          return;
        }

        // 设置默认选择
        const currentSelected = this.selectedMappings[row.id];
        const defaultMapping = this.currentMappingList.find(m => m.isDefault === '1');
        const firstMapping = this.currentMappingList[0];

        this.selectedMapping = currentSelected || defaultMapping || firstMapping;

        // 延迟一点显示动画效果
        this.$nextTick(() => {
          this.mappingLoading = false;
        });

      } catch (error) {
        console.error('获取映射列表失败:', error);
        this.$message.error('获取映射列表失败：' + (error.message || '未知错误'));
        this.mappingDialogVisible = false;
        this.mappingLoading = false;
      }
    },

    /** 确认映射选择 */
    confirmMappingSelection() {
      if (!this.selectedMapping || !this.currentGenericRow) {
        this.$message.warning('请选择一个映射');
        return;
      }

      // 保存选择的映射
      this.$set(this.selectedMappings, this.currentGenericRow.id, this.selectedMapping);

      this.mappingDialogVisible = false;
      this.$message.success('映射选择成功');
    },

    /** 显示映射材料详情 */
    async showMappingDetail(row) {
      if (!this.selectedMappings[row.id]) {
        this.$message.warning('请先选择映射');
        return;
      }

      const mapping = this.selectedMappings[row.id];

      try {
        // 获取映射的材料清单
        const response = await getMaterialListByMapping(
          mapping.mappedModel,
          mapping.mappedModule,
          mapping.mappedBoardType
        );

        this.currentMappingMaterials = response.data || [];
        this.mappingDetailVisible = true;

      } catch (error) {
        console.error('获取映射材料清单失败:', error);
        this.$message.error('获取映射材料清单失败：' + (error.message || '未知错误'));
      }
    },

    /** 切换映射 */
    switchMapping(row) {
      this.showMappingDialog(row);
    },

    /** 清除映射选择 */
    clearMapping(row) {
      this.$delete(this.selectedMappings, row.id);
      this.$message.success('映射选择已清除');
    },

    /** 获取映射显示文本 */
    getMappingDisplayText(mapping) {
      if (!mapping) return '';
      return `${mapping.mappedModel} (${mapping.mappedModule} - ${mapping.mappedBoardType})`;
    },

    /** 创建通用板块记录 */
    async createGenericBomRecord() {
      // 这里可以添加创建通用板块的逻辑
      // 比如打开一个弹窗让用户输入通用ID和名称
      this.$message.info('通用板块创建功能待实现');
    },

    /** 批量设置通用映射 */
    batchSetGenericMapping() {
      const genericRows = this.bomList.filter(row => this.isGenericBlock(row));
      if (genericRows.length === 0) {
        this.$message.warning('当前列表中没有通用板块记录');
        return;
      }

      this.$message.info('批量设置通用映射功能待实现');
    },

    /** 重置映射选择状态 */
    resetMappingDialogState() {
      this.currentGenericRow = null;
      this.currentMappingList = [];
      this.selectedMapping = null;
      this.mappingSearchText = '';
      this.mappingFilterType = '';
      this.mappingLoading = false;
    },

    /** 关闭映射选择弹窗 */
    closeMappingDialog() {
      this.mappingDialogVisible = false;
      // 延迟重置状态，等待动画完成
      setTimeout(() => {
        this.resetMappingDialogState();
      }, 300);
    },

    // ============= 通用设置功能相关方法 =============

    /** 判断是否可以设置为通用（优化版本 - 避免重复调用） */
    canSetAsGeneric(row) {
      // 基本条件检查
      if (!row.material || !row.boardType || !row.model) {
        return false;
      }

      // 检查是否已经设置为通用（通过reserved_field_1字段）
      if (row.reservedField1 === 'GENERIC') {
        return false;
      }

      // 检查缓存中的结果
      const cacheKey = `${row.model}-${row.module || ''}-${row.boardType}`;
      const cachedResult = this.genericEligibilityCache[cacheKey];

      // 如果缓存中有结果，直接返回
      if (cachedResult !== undefined) {
        return cachedResult;
      }

      // 如果缓存中没有结果，返回false（不显示按钮），但不立即触发API调用
      // API调用将在数据加载完成后批量进行
      return false;
    },

    /** 批量检查通用设置资格（优化版本 - 避免重复调用） */
    async batchCheckGenericEligibility() {
      // 防抖处理，避免频繁调用
      if (this._batchCheckTimer) {
        clearTimeout(this._batchCheckTimer);
      }

      this._batchCheckTimer = setTimeout(async () => {
        await this._performBatchCheck();
      }, 100);
    },

    /** 执行批量检查（内部方法） */
    async _performBatchCheck() {
      if (!this.selectedBomList || this.selectedBomList.length === 0) {
        return;
      }

      // 收集需要检查的唯一组合
      const uniqueChecks = new Set();
      this.selectedBomList.forEach(row => {
        if (row.material && row.boardType && row.model && row.reservedField1 !== 'GENERIC') {
          const cacheKey = `${row.model}-${row.module || ''}-${row.boardType}`;
          if (this.genericEligibilityCache[cacheKey] === undefined) {
            uniqueChecks.add(JSON.stringify({
              model: row.model,
              module: row.module,
              boardType: row.boardType,
              cacheKey: cacheKey
            }));
          }
        }
      });

      if (uniqueChecks.size === 0) {
        return;
      }

      console.log(`批量检查通用设置资格，共${uniqueChecks.size}个唯一组合`);

      // 批量处理，限制并发数量为3，避免服务器压力过大
      const batchSize = 3;
      const checkArray = Array.from(uniqueChecks).map(checkStr => JSON.parse(checkStr));

      for (let i = 0; i < checkArray.length; i += batchSize) {
        const batch = checkArray.slice(i, i + batchSize);
        const batchPromises = batch.map(checkData => this.singleCheckGenericEligibility(checkData));

        // 使用Promise.allSettled避免单个失败影响整体
        await Promise.allSettled(batchPromises);

        // 小延迟避免请求过于密集
        if (i + batchSize < checkArray.length) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // 检查完成后，触发一次重新渲染
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    /** 单个检查通用设置资格（内部方法） */
    async singleCheckGenericEligibility({ model, module, boardType, cacheKey }) {
      try {
        const response = await checkGenericEligibilityByGroup(model, module, boardType);
        const result = response.data;

        // 缓存检查结果
        this.$set(this.genericEligibilityCache, cacheKey, result.eligible);

      } catch (error) {
        console.error('检查通用设置资格失败:', error);
        this.$set(this.genericEligibilityCache, cacheKey, false);
      }
    },

    /** 设置为通用 */
    async handleSetAsGeneric(row) {
      try {
        // 先检查资格
        const checkResponse = await checkGenericEligibilityByGroup(row.model, row.module, row.boardType);
        const checkResult = checkResponse.data;

        if (!checkResult.eligible) {
          this.$modal.msgWarning(`无法设置为通用：${checkResult.reason}`);
          return;
        }

        // 显示确认对话框
        const confirmMessage = `
          <div style="text-align: left; line-height: 1.6;">
            <p><strong>确认设置为通用类型？</strong></p>
            <p><strong>产品型号：</strong>${row.model}</p>
            <p><strong>款式：</strong>${row.module || '默认'}</p>
            <p><strong>板型：</strong>${row.boardType}</p>
            <p><strong>原料：</strong>${checkResult.materialName}</p>
            <p style="color: #E6A23C; margin-top: 10px;">
              <i class="el-icon-warning"></i> ${checkResult.reason}
            </p>
            <p style="color: #909399; font-size: 12px; margin-top: 10px;">
              设置后该记录将标记为通用类型，板型"${row.boardType}"保持不变
            </p>
            <p style="color: #909399; font-size: 12px;">
              通用标识字段将设置为"GENERIC"
            </p>
          </div>
        `;

        const confirmed = await this.$confirm(confirmMessage, '设置为通用', {
          confirmButtonText: '确定设置',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          customClass: 'generic-setting-confirm-dialog'
        }).catch(() => false);

        if (!confirmed) {
          return;
        }

        // 执行设置操作
        this.genericSettingLoading = true;
        const response = await setAsGenericByGroup(row.model, row.module, row.boardType);

        this.$modal.msgSuccess(response.data);

        // 清空通用设置资格缓存并刷新数据
        this.genericEligibilityCache = {};

        // 安全的数据刷新方式
        try {
          if (typeof this.refreshAllData === 'function') {
            this.refreshAllData();
          } else if (typeof this.getList === 'function') {
            this.getList();
          } else {
            // 手动刷新数据
            this.getModelList();
            if (this.selectedModel) {
              this.getBomBySelectedModel();
            }
          }
        } catch (refreshError) {
          console.error('数据刷新失败:', refreshError);
          // 手动刷新数据作为备用方案
          this.getModelList();
          if (this.selectedModel) {
            this.getBomBySelectedModel();
          }
        }

      } catch (error) {
        console.error('设置为通用失败:', error);
        this.$modal.msgError('设置失败：' + (error.response?.data?.msg || error.message));
      } finally {
        this.genericSettingLoading = false;
      }
    },

    /** 清空通用设置资格缓存 */
    clearGenericEligibilityCache() {
      this.genericEligibilityCache = {};
    }

  },
};
</script>

<style scoped>
/* ===================== BOM清单页面现代化样式系统 ===================== */

/* 表单提示样式 */
.model-select-hint {
  margin-top: 5px;
  font-size: 12px;
  color: var(--base-color-2, #909399);
  line-height: 1.4;
}

.model-select-hint i {
  margin-right: 4px;
  color: var(--current-color, #409eff);
}

/* 搜索区域样式优化 */
.header-search {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-search .el-input {
  flex: 1;
}

.header-search .el-button {
  flex-shrink: 0;
}

/* 批量修改对话框样式优化 */
.current-model-info .el-card {
  background-color: var(--base-item-bg, #fff);
  border-color: var(--current-color, #409eff) !important;
}

.current-model-info .el-card__body {
  padding: 12px 16px;
}

/* 产品编码字段样式 */
.el-input[readonly] .el-input__inner {
  background-color: var(--base-color-9, #f5f7fa);
  color: var(--base-color-2, #666);
  cursor: not-allowed;
}

/* 深色主题下的只读输入框 */
.theme-dark .el-input[readonly] .el-input__inner {
  background-color: var(--base-color-8, #4d5d74);
  color: var(--base-color-3, #c4c2c2);
  border-color: var(--border-color-1, #8b8b8b);
}

/* 产品编码显示区域样式 */
.product-numbers-display {
  min-height: 32px;
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color-1, #dcdfe6);
  border-radius: 4px;
  padding: 8px 12px;
  background-color: var(--base-item-bg, #fff);
}

.product-numbers-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.product-number-tag {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-weight: 500;
}

.no-product-number {
  display: flex;
  align-items: center;
  color: var(--base-color-3, #999);
  font-style: italic;
}

/* 深色主题下的产品编码显示区域 */
.theme-dark .product-numbers-display {
  background-color: var(--base-item-bg, #2c3d55);
  border-color: var(--border-color-1, #8b8b8b);
}

/* 批量操作工具栏样式 */
.batch-operations {
  background: var(--batch-bg, linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%));
  border: 1px solid var(--batch-border, var(--current-color, #409eff));
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.batch-info {
  display: flex;
  align-items: center;
  color: var(--batch-text, var(--base-color-1, #333));
  font-weight: 500;
}

.batch-info i {
  margin-right: 8px;
  color: var(--current-color, #409eff);
  font-size: 16px;
}

.batch-info strong {
  color: var(--current-color, #409eff);
  margin: 0 4px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 批量修改弹窗样式 */
.batch-update-content .selected-info {
  margin-bottom: 20px;
}

.batch-update-content .selected-records {
  margin-top: 20px;
}

.batch-update-content .selected-records h4 {
  margin-bottom: 12px;
  color: var(--base-color-1, #333);
  font-weight: 600;
}

/* 主容器 */
.app-container {
  background: var(--base-body-background);
  min-height: 100vh;
  padding: 20px;
  transition: all 0.3s ease;
  color: var(--base-color-1);
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* ===================== 通用映射功能样式 ===================== */

/* 映射选择弹窗样式 */
.mapping-selection-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.mapping-selection-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.mapping-selection-dialog .el-dialog__header {
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb), 0.8) 100%);
  color: var(--theme-color);
  padding: 20px 24px 16px;
  border-radius: 12px 12px 0 0;
}

.mapping-selection-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-color);
}

.mapping-selection-dialog .el-dialog__headerbtn {
  top: 20px;
  right: 20px;
}

.mapping-selection-dialog .el-dialog__headerbtn .el-dialog__close {
  color: var(--theme-color);
  font-size: 16px;
}

/* 当前板块信息样式 */
.current-block-info {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.block-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.block-icon i {
  font-size: 24px;
  color: var(--theme-color);
}

.block-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color);
}

.block-details p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.block-details small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

/* 映射内容样式 */
.mapping-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 空状态样式 */
.empty-mapping {
  text-align: center;
  padding: 60px 20px;
  color: var(--base-color-2);
  background: linear-gradient(135deg, var(--base-color-9) 0%, rgba(var(--current-color-rgb), 0.03) 100%);
  border-radius: 12px;
  border: 2px dashed var(--border-color-2);
  animation: fadeIn 0.5s ease-out;
}

.empty-icon {
  font-size: 72px;
  margin-bottom: 20px;
  opacity: 0.6;
  color: var(--current-color);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.empty-mapping h4 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--base-color-1);
}

.empty-hint {
  margin: 12px 0;
  font-size: 15px;
  color: var(--base-color-2);
  line-height: 1.5;
}

.empty-action {
  margin: 16px 0 0 0;
  font-size: 13px;
  color: var(--base-color-3);
  padding: 8px 16px;
  background: rgba(var(--current-color-rgb), 0.08);
  border-radius: 16px;
  display: inline-block;
  border: 1px solid rgba(var(--current-color-rgb), 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mapping-selection-dialog {
    width: 95vw !important;
    margin: 0 !important;
  }

  .mapping-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .mapping-search,
  .mapping-filter {
    width: 100%;
  }

  .mapping-search:focus-within {
    width: 100%;
  }

  .mapping-card {
    padding: 16px;
  }

  .mapping-specs {
    flex-direction: column;
    gap: 8px;
  }

  .spec-item {
    justify-content: space-between;
  }
}

/* 高级动画效果 */
.mapping-card {
  will-change: transform, box-shadow;
}

.mapping-cards {
  will-change: opacity, transform;
}

/* 加载动画优化 */
.mapping-content.is-loading {
  min-height: 200px;
}

/* 映射列表头部 */
.mapping-list-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color-2);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.header-title h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title p {
  margin: 0;
  font-size: 13px;
  color: var(--base-color-2);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

.mapping-search {
  width: 180px;
  transition: width 0.3s ease;
}

.mapping-search:focus-within {
  width: 220px;
}

.mapping-search .el-input__inner {
  border-radius: 16px;
  background: var(--base-color-9);
  border: 1px solid var(--border-color-2);
  transition: all 0.3s ease;
}

.mapping-search .el-input__inner:focus {
  background: var(--base-main-bg);
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(var(--current-color-rgb), 0.2);
}

.mapping-filter {
  width: 120px;
}

.mapping-filter .el-input__inner {
  border-radius: 16px;
  background: var(--base-color-9);
  border: 1px solid var(--border-color-2);
  transition: all 0.3s ease;
}

.mapping-filter .el-input__inner:focus {
  background: var(--base-main-bg);
  border-color: var(--current-color);
}

/* 映射卡片网格 */
.mapping-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.mapping-card {
  animation: slideInCard 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.mapping-card:nth-child(1) { animation-delay: 0.1s; }
.mapping-card:nth-child(2) { animation-delay: 0.2s; }
.mapping-card:nth-child(3) { animation-delay: 0.3s; }
.mapping-card:nth-child(4) { animation-delay: 0.4s; }
.mapping-card:nth-child(n+5) { animation-delay: 0.5s; }

@keyframes slideInCard {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 映射卡片样式 */
.mapping-card {
  border: 2px solid var(--border-color-2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--base-main-bg);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  animation: slideInCard 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.mapping-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  transition: all 0.4s ease;
}

.mapping-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(var(--current-color-rgb), 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.mapping-card:hover {
  border-color: rgba(var(--current-color-rgb), 0.6);
  transform: translateY(-4px);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(var(--current-color-rgb), 0.2);
}

.mapping-card:hover::after {
  opacity: 1;
}

.mapping-card.selected {
  border-color: var(--current-color);
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.08) 0%, rgba(var(--current-color-rgb), 0.03) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(var(--current-color-rgb), 0.1);
}

.mapping-card.selected::before {
  background: linear-gradient(90deg, var(--current-color), rgba(var(--current-color-rgb), 0.8));
  height: 4px;
}

.mapping-card.default {
  border-color: rgba(103, 194, 58, 0.4);
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.06) 0%, rgba(103, 194, 58, 0.02) 100%);
}

.mapping-card.default::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
  height: 4px;
}

.mapping-card.default.selected {
  border-color: #67c23a;
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.12) 0%, rgba(103, 194, 58, 0.06) 100%);
}

/* 卡片选择动画 */
.mapping-card.selected {
  animation: selectPulse 0.6s ease-out;
}

@keyframes selectPulse {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-radio {
  flex-shrink: 0;
}

.card-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.card-badges .el-tag {
  border-radius: 12px;
  font-weight: 500;
  font-size: 11px;
  padding: 4px 8px;
  border: none;
  transition: all 0.3s ease;
}

.card-badges .el-tag.el-tag--success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.card-badges .el-tag.el-tag--info {
  background: rgba(var(--current-color-rgb), 0.12);
  color: var(--current-color);
  font-weight: 600;
}

/* 卡片内容 */
.card-content {
  padding-left: 24px;
}

.mapping-name {
  font-size: 17px;
  font-weight: 700;
  color: var(--base-color-1);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1.3;
  position: relative;
}

.mapping-name i {
  color: var(--current-color);
  font-size: 20px;
  background: rgba(var(--current-color-rgb), 0.1);
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mapping-card:hover .mapping-name i {
  background: rgba(var(--current-color-rgb), 0.2);
  transform: scale(1.1);
}

.mapping-card.selected .mapping-name i {
  background: var(--current-color);
  color: var(--theme-color);
}

.mapping-specs {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
  padding: 8px 0;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  background: var(--base-color-9);
  padding: 6px 10px;
  border-radius: 8px;
  border: 1px solid var(--border-color-2);
  transition: all 0.3s ease;
}

.spec-item:hover {
  background: var(--base-color-8);
  border-color: rgba(var(--current-color-rgb), 0.3);
}

.spec-label {
  color: var(--base-color-2);
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.spec-value {
  color: var(--base-color-1);
  background: rgba(var(--current-color-rgb), 0.1);
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(var(--current-color-rgb), 0.2);
}

.mapping-card.selected .spec-value {
  background: rgba(var(--current-color-rgb), 0.15);
  border-color: rgba(var(--current-color-rgb), 0.3);
  color: var(--current-color);
}

.mapping-remark {
  font-size: 12px;
  color: var(--base-color-2);
  background: linear-gradient(135deg, var(--base-color-9) 0%, rgba(var(--current-color-rgb), 0.05) 100%);
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid rgba(var(--current-color-rgb), 0.15);
  border-left: 4px solid var(--current-color);
  margin-top: 12px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.mapping-remark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--current-color), rgba(var(--current-color-rgb), 0.6));
}

.mapping-remark i {
  color: var(--current-color);
  margin-top: 2px;
  flex-shrink: 0;
  background: rgba(var(--current-color-rgb), 0.1);
  padding: 3px;
  border-radius: 4px;
  font-size: 11px;
}

.mapping-card.selected .mapping-remark {
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.08) 0%, rgba(var(--current-color-rgb), 0.12) 100%);
  border-color: rgba(var(--current-color-rgb), 0.25);
  color: var(--base-color-1);
}

/* 通用板块信息样式优化 */
.generic-bom-info {
  padding: 12px;
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.08) 0%, rgba(var(--current-color-rgb), 0.12) 100%);
  border-radius: 8px;
  border: 1px solid rgba(var(--current-color-rgb), 0.2);
}

.generic-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.generic-icon {
  color: var(--current-color);
  font-size: 16px;
}

.generic-title {
  font-weight: 600;
  color: var(--base-color-1);
  font-size: 14px;
}

.mapping-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.mapping-actions .el-button {
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 12px;
}

.current-mapping {
  margin-top: 8px;
}

.current-mapping .el-tag {
  border-radius: 12px;
}

/* 工具栏容器 */
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--base-main-bg);
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar-container .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.toolbar-container .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toolbar-container .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.toolbar-container .el-button:hover::before {
  left: 100%;
}

/* 全局版本过滤提示样式 */
.global-version-alert {
  margin-bottom: 16px;
}

.global-version-alert .el-alert {
  border-radius: 8px;
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.08);
  border: 1px solid rgba(var(--current-color-rgb, 54, 113, 232), 0.2);
}

.global-version-alert .el-alert__icon {
  color: var(--current-color);
}

.global-version-alert .el-alert__title {
  color: var(--base-color-1);
  font-weight: 600;
}

.global-version-alert .el-button {
  color: var(--current-color);
  margin-left: 12px;
}

.global-version-alert .el-button:hover {
  color: var(--current-color);
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
}

/* 主布局容器 */
.bom-layout {
  display: flex;
  height: 85vh;
  min-height: 700px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--base-main-bg);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
  position: relative;
}

/* 左侧型号面板 */
.model-panel {
  width: 380px;
  border-right: 1px solid var(--border-color-1);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  background: var(--base-main-bg);
  position: relative;
  transition: all 0.3s ease;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  overflow: hidden;
}

.model-panel.collapsed {
  width: 60px;
  min-width: 60px;
}

.model-panel.collapsed .model-list-container {
  display: none;
}

.model-panel.collapsed .header-search {
  display: none;
}

/* 面板头部样式 */
.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-1);
  background: var(--current-color);
  color: var(--theme-color);
  position: relative;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.header-title i {
  font-size: 20px;
  opacity: 0.9;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.collapse-btn {
  color: var(--theme-color) !important;
  padding: 4px !important;
  font-size: 16px !important;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.collapse-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

.header-search {
  position: relative;
  z-index: 1;
}

.header-search .el-input__inner {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--theme-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.header-search .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.header-search .el-input__inner:focus {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.header-search .el-input__prefix {
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--theme-color);
  font-weight: 500;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* 型号列表容器 */
.model-list-container {
  flex: 1;
  overflow: hidden;
  background: var(--base-main-bg);
}

.model-list {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.model-list::-webkit-scrollbar {
  width: 6px;
}

.model-list::-webkit-scrollbar-track {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
  border-radius: 3px;
}

.model-list::-webkit-scrollbar-thumb {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.model-list::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.5);
}

/* 型号项样式 */
.model-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--base-item-bg, #ffffff);
  border: 1px solid var(--border-color-1);
  position: relative;
  overflow: hidden;
}

.model-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--current-color);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.model-item:hover {
  background: var(--base-color-8, #f8f9fa);
  border-color: var(--current-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.model-item:hover::before {
  transform: scaleY(1);
}

.model-item.active {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.08);
  border-color: var(--current-color);
  box-shadow: 0 2px 8px rgba(var(--current-color-rgb, 54, 113, 232), 0.15);
}

.model-item.active::before {
  transform: scaleY(1);
  width: 4px;
}

.model-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.model-icon i {
  font-size: 18px;
  color: var(--current-color);
}

.model-item.active .model-icon {
  background: var(--current-color);
}

.model-item.active .model-icon i {
  color: var(--theme-color);
}

/* 不同款式的型号项样式 */
.model-style-button {
  border-left: 4px solid #409eff;
}

.model-style-button .model-icon {
  background: rgba(64, 158, 255, 0.1);
}

.model-style-button .icon-style-button i {
  color: #409eff;
}

.model-style-wireless {
  border-left: 4px solid #67c23a;
}

.model-style-wireless .model-icon {
  background: rgba(103, 194, 58, 0.1);
}

.model-style-wireless .icon-style-wireless i {
  color: #67c23a;
}

.model-style-bluetooth {
  border-left: 4px solid #e6a23c;
}

.model-style-bluetooth .model-icon {
  background: rgba(230, 162, 60, 0.1);
}

.model-style-bluetooth .icon-style-bluetooth i {
  color: #e6a23c;
}

.model-style-default {
  border-left: 4px solid #909399;
}

.model-style-default .model-icon {
  background: rgba(144, 147, 153, 0.1);
}

.model-style-default .icon-style-default i {
  color: #909399;
}

.model-content {
  flex: 1;
  min-width: 0;
}

.model-name-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.model-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--base-color-1);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  flex: 1;
}

.style-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  flex-shrink: 0;
}

.model-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-arrow {
  opacity: 0;
  transition: all 0.3s ease;
  color: var(--current-color);
}

.model-item:hover .model-arrow,
.model-item.active .model-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* 空状态样式 */
.empty-model {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--base-color-2);
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: var(--current-color);
}

.empty-text {
  font-size: 16px;
  margin-bottom: 20px;
  color: var(--base-color-2);
}

.empty-action {
  color: var(--current-color);
  font-weight: 500;
}

.empty-action:hover {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
}

/* 右侧BOM面板 */
.bom-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--base-main-bg);
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
}

/* 筛选面板样式 */
.filter-panel {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.03);
  border-bottom: 1px solid var(--border-color-1);
  padding: 20px 24px;
}

.filter-form {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.filter-form .el-form-item__label {
  color: var(--base-color-1);
  font-weight: 600;
  font-size: 14px;
}

.filter-form .el-input__inner,
.filter-form .el-select .el-input__inner {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 6px;
  color: var(--base-color-1);
  transition: all 0.3s ease;
  height: 36px;
  line-height: 36px;
}

.filter-form .el-input__inner:focus,
.filter-form .el-select .el-input__inner:focus {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(var(--current-color-rgb, 54, 113, 232), 0.2);
}

.filter-form .el-button {
  background: var(--current-color);
  border-color: var(--current-color);
  color: var(--theme-color);
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  height: 36px;
  transition: all 0.3s ease;
}

.filter-form .el-button:hover {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
}

/* BOM内容区域 */
.bom-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background: var(--base-main-bg);
}

/* 表格美化 */
.el-table {
  background: var(--base-main-bg);
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
}

.el-table .el-table__header-wrapper {
  background: var(--current-color);
}

.el-table th {
  background: var(--current-color) !important;
  color: var(--theme-color) !important;
  font-weight: 600;
  border: none;
}

.el-table td {
  border-bottom: 1px solid var(--border-color-1);
}

.el-table .el-table__row {
  transition: all 0.2s ease;
  background: var(--base-main-bg);
}

.el-table .el-table__row:hover {
  background: var(--base-color-8, #f8f9fa) !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.el-table .el-table__row.warning-row {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.02);
}

/* 表格内容样式 */
.module-tag,
.board-type-tag,
.unit-tag,
.version-tag {
  font-weight: 600;
  border-radius: 6px;
  font-size: 12px;
  border: none;
  padding: 4px 8px;
  text-transform: none;
}

.module-tag {
  background: linear-gradient(135deg, var(--current-color), rgba(var(--current-color-rgb, 54, 113, 232), 0.8));
  color: var(--theme-color);
  box-shadow: 0 2px 4px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
}

/* 增强的功能模块标签样式 */
.module-tag-enhanced {
  font-weight: 600;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  min-width: 80px;
  justify-content: center;
}

.module-tag-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.module-tag-enhanced .module-icon {
  font-size: 14px;
  margin-right: 2px;
}

/* 按键款样式 - 蓝色 */
.module-tag-enhanced.el-tag--primary {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border-color: #409eff;
  color: white;
}

.module-tag-enhanced.el-tag--primary.is-dark {
  background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
  box-shadow: 0 3px 6px rgba(64, 158, 255, 0.3);
}

/* 蓝牙款样式 - 橙色 */
.module-tag-enhanced.el-tag--warning {
  background: linear-gradient(135deg, #e6a23c 0%, #d19a2c 100%);
  border-color: #e6a23c;
  color: white;
}

.module-tag-enhanced.el-tag--warning.is-dark {
  background: linear-gradient(135deg, #d19a2c 0%, #b8851f 100%);
  box-shadow: 0 3px 6px rgba(230, 162, 60, 0.3);
}

/* 无时款样式 - 绿色 */
.module-tag-enhanced.el-tag--success {
  background: linear-gradient(135deg, #67c23a 0%, #5cb85c 100%);
  border-color: #67c23a;
  color: white;
}

.module-tag-enhanced.el-tag--success.is-dark {
  background: linear-gradient(135deg, #5cb85c 0%, #4a9c4a 100%);
  box-shadow: 0 3px 6px rgba(103, 194, 58, 0.3);
}

/* 控制模块样式 - 红色 */
.module-tag-enhanced.el-tag--danger {
  background: linear-gradient(135deg, #f56c6c 0%, #e85555 100%);
  border-color: #f56c6c;
  color: white;
}

.module-tag-enhanced.el-tag--danger.is-dark {
  background: linear-gradient(135deg, #e85555 0%, #d64545 100%);
  box-shadow: 0 3px 6px rgba(245, 108, 108, 0.3);
}

/* 默认样式 - 灰色 */
.module-tag-enhanced.el-tag--info {
  background: linear-gradient(135deg, #909399 0%, #7a7e82 100%);
  border-color: #909399;
  color: white;
}

.module-tag-enhanced.el-tag--info.is-dark {
  background: linear-gradient(135deg, #7a7e82 0%, #6b6f73 100%);
  box-shadow: 0 3px 6px rgba(144, 147, 153, 0.3);
}

.board-type-tag {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.unit-tag {
  background: rgba(var(--base-color-2-rgb, 144, 147, 153), 0.1);
  color: var(--base-color-2);
  border: 1px solid rgba(var(--base-color-2-rgb, 144, 147, 153), 0.2);
  line-height: 1.2;
  padding: 3px 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
}

.material-info,
.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.material-info i {
  color: var(--current-color);
  opacity: 0.8;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--current-color), rgba(var(--current-color-rgb, 54, 113, 232), 0.8));
  color: var(--theme-color);
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(var(--current-color-rgb, 54, 113, 232), 0.4);
}

.quantity-info {
  text-align: center;
}

.quantity-number {
  font-weight: 600;
  color: var(--current-color);
  font-size: 15px;
}

.version-tag {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
  color: var(--current-color);
  border: 1px solid rgba(var(--current-color-rgb, 54, 113, 232), 0.2);
}



/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  position: relative;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.edit-btn {
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  border-color: var(--current-color);
  color: var(--theme-color);
}

.edit-btn:hover {
  background: linear-gradient(135deg, rgba(var(--current-color-rgb, 54, 113, 232), 0.9) 0%, var(--current-color) 100%);
  box-shadow: 0 4px 16px rgba(var(--current-color-rgb, 54, 113, 232), 0.4);
}

.delete-btn {
  background: linear-gradient(135deg, #f56c6c 0%, #e6454f 100%);
  border-color: #f56c6c;
  color: #ffffff;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #e6454f 0%, #d73941 100%);
  box-shadow: 0 4px 16px rgba(245, 108, 108, 0.4);
}

.generic-btn {
  background: linear-gradient(135deg, #e6a23c 0%, #d4941e 100%);
  border-color: #e6a23c;
  color: white;
}

.generic-btn:hover {
  background: linear-gradient(135deg, #d4941e 0%, #c7841a 100%);
  box-shadow: 0 4px 16px rgba(230, 162, 60, 0.4);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

/* Tooltip样式优化 */
.el-tooltip__popper {
  font-size: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  background: var(--base-main-bg);
  color: var(--base-color-1);
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: var(--border-color-1);
}

.el-tooltip__popper[x-placement^="top"] .popper__arrow::after {
  border-top-color: var(--base-main-bg);
}

/* 空内容状态 */
.empty-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--base-main-bg);
  position: relative;
  min-height: 400px;
  text-align: center;
}

.empty-illustration {
  font-size: 120px;
  margin-bottom: 24px;
  color: var(--current-color);
  opacity: 0.4;
  animation: gentle-float 3s ease-in-out infinite;
  background: linear-gradient(135deg, var(--current-color), rgba(var(--current-color-rgb, 54, 113, 232), 0.6));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.empty-text h4 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--base-color-1);
}

.empty-text p {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: var(--base-color-2);
  line-height: 1.6;
}

.empty-actions .el-button {
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  border: none;
  color: var(--theme-color);
  padding: 14px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
  position: relative;
  overflow: hidden;
}

.empty-actions .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.empty-actions .el-button:hover::before {
  left: 100%;
}

.empty-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--current-color-rgb, 54, 113, 232), 0.4);
}

/* =================== 版本管理对话框样式 =================== */

/* 对话框主体样式 */
.version-manage-dialog .el-dialog {
  background: var(--base-main-bg);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.version-manage-dialog .el-dialog__header {
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  padding: 0;
  border-bottom: none;
}

.version-manage-dialog .el-dialog__body {
  padding: 0;
  background: var(--base-main-bg);
}

.version-manage-dialog .el-dialog__footer {
  background: var(--base-main-bg);
  border-top: 1px solid var(--border-color-1);
  padding: 20px 30px;
}

/* 对话框头部 */
.version-dialog-header {
  display: flex;
  align-items: center;
  padding: 24px 30px;
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  color: var(--theme-color);
  position: relative;
  overflow: hidden;
}

.version-dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.header-icon i {
  font-size: 24px;
  color: var(--theme-color);
}

.header-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.header-content h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-content p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.9;
}

/* 筛选面板样式 */
.version-filter-panel {
  background: var(--base-main-bg);
  border-bottom: 1px solid var(--border-color-1);
  position: relative;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px 0;
  margin-bottom: 20px;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
}

.filter-title i {
  color: var(--current-color);
  font-size: 18px;
}

.filter-stats {
  display: flex;
  gap: 8px;
}

.filter-content {
  padding: 0 30px 24px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.filter-form .el-form-item__label {
  color: var(--base-color-1);
  font-weight: 600;
  font-size: 14px;
  line-height: 36px;
}

.filter-input,
.filter-select {
  min-width: 180px;
}

.filter-input .el-input__inner,
.filter-select .el-input__inner {
  border-radius: 8px;
  border: 1px solid var(--border-color-1);
  background: var(--base-main-bg);
  transition: all 0.3s ease;
  height: 36px;
  line-height: 36px;
}

.filter-input .el-input__inner:focus,
.filter-select .el-input__inner:focus {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(var(--current-color-rgb, 54, 113, 232), 0.2);
}

.filter-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.filter-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 8px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-actions .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.filter-actions .el-button:hover::before {
  left: 100%;
}

.filter-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 表格容器样式 */
.version-table-container {
  background: var(--base-main-bg);
  min-height: 400px;
  position: relative;
}

.version-table {
  background: var(--base-main-bg);
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.version-table .el-table__row {
  transition: all 0.3s ease;
}

.version-table .el-table__row:hover {
  background: var(--base-color-8, #f8f9fa) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.version-table .version-row-warning {
  background: rgba(230, 162, 60, 0.1);
}

.version-table .version-row-stripe {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.02);
}

/* 版本列样式 */
.version-column {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.version-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.version-icon i {
  font-size: 16px;
  color: var(--current-color);
}

.version-tag-enhanced {
  font-weight: 600;
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 13px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 数量列样式 */
.count-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.count-visual {
  position: relative;
}

.count-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.count-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 50%;
}

.count-circle.count-minimal {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

.count-circle.count-low {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.count-circle.count-medium {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.count-circle.count-high {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.count-text {
  font-size: 12px;
  color: var(--base-color-2);
  font-weight: 500;
}

/* 用户列样式 */
.user-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.user-avatar-enhanced {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  color: var(--theme-color);
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
  position: relative;
  overflow: hidden;
}

.user-avatar-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 50%;
}

.user-name {
  font-size: 12px;
  color: var(--base-color-1);
  font-weight: 500;
  text-align: center;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 时间列样式 */
.time-column {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 0 8px;
}

.time-icon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-icon i {
  font-size: 14px;
  color: var(--current-color);
}

.time-content {
  display: flex;
  flex-direction: column;
}

.time-date {
  font-size: 13px;
  color: var(--base-color-1);
  font-weight: 600;
  line-height: 1.2;
}

.time-time {
  font-size: 12px;
  color: var(--base-color-2);
  line-height: 1.2;
}

/* 操作列样式 */
.version-actions {
  display: flex;
  justify-content: center;
}

.action-btn-enhanced {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn-enhanced:hover {
  transform: translateY(-2px) scale(1.05);
}

.switch-btn {
  background: linear-gradient(135deg, #67c23a 0%, #5cb85c 100%);
  border: none;
  color: white;
  font-weight: 600;
  border-radius: 6px;
  padding: 6px 12px;
  transition: all 0.3s ease;
}

.switch-btn:hover {
  background: linear-gradient(135deg, #5cb85c 0%, #449d44 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.action-btn-enhanced.view-btn {
  background: linear-gradient(135deg, var(--current-color) 0%, rgba(var(--current-color-rgb, 54, 113, 232), 0.8) 100%);
  border-color: var(--current-color);
  color: var(--theme-color);
}

.action-btn-enhanced.view-btn:hover {
  box-shadow: 0 6px 20px rgba(var(--current-color-rgb, 54, 113, 232), 0.4);
}

/* 空状态样式 */
.version-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 300px;
  background: var(--base-main-bg);
  text-align: center;
}

.version-empty-state .empty-illustration {
  font-size: 80px;
  margin-bottom: 24px;
  color: var(--current-color);
  opacity: 0.5;
  animation: gentle-float 3s ease-in-out infinite;
}

.version-empty-state .empty-content h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--base-color-1);
}

.version-empty-state .empty-content p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: var(--base-color-2);
  line-height: 1.6;
}

/* 对话框底部样式 */
.version-dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--base-main-bg);
}

.footer-stats {
  display: flex;
  gap: 24px;
  flex: 1;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--base-color-2);
}

.stats-item i {
  color: var(--current-color);
  font-size: 16px;
}

.stats-item strong {
  color: var(--current-color);
  font-weight: 600;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.footer-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.footer-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* =================== 主题适配系统 =================== */

/* 深色主题适配 */
:global(.theme-dark) .panel-header {
  background: linear-gradient(135deg, #2a3950 0%, #1c2e47 100%);
}

:global(.theme-dark) .model-item {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
}

:global(.theme-dark) .model-item:hover {
  background: var(--base-color-9);
}

:global(.theme-dark) .model-item.active {
  background: rgba(var(--current-color-rgb, 58, 123, 153), 0.2);
}

:global(.theme-dark) .el-table .el-table__row:hover {
  background: var(--table-row-hover-bg) !important;
}

:global(.theme-dark) .filter-panel {
  background: linear-gradient(135deg,
    rgba(var(--current-color-rgb, 58, 123, 153), 0.1) 0%,
    transparent 50%);
}

:global(.theme-dark) .version-table .el-table__row,
:global(.theme-dark) .version-table .el-table__body tr {
  background-color: var(--base-main-bg) !important;
}
:global(.theme-dark) .version-table .el-table__row--striped,
:global(.theme-dark) .version-table .version-row-stripe {
  background-color: var(--base-item-bg) !important;
}
:global(.theme-dark) .version-table .el-table__row:hover,
:global(.theme-dark) .version-table tr:hover {
  background-color: var(--table-row-hover-bg) !important;
}

/* 星空主题适配 */
:global(.theme-starry-sky) .panel-header {
  background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 100%);
}

:global(.theme-starry-sky) .model-item {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
}

:global(.theme-starry-sky) .model-item:hover {
  background: var(--base-color-9);
}

:global(.theme-starry-sky) .model-item.active {
  background: rgba(var(--current-color-rgb, 30, 58, 138), 0.2);
}

:global(.theme-starry-sky) .el-table .el-table__row:hover {
  background: var(--table-row-hover-bg) !important;
}

:global(.theme-starry-sky) .filter-panel {
  background: linear-gradient(135deg,
    rgba(var(--current-color-rgb, 30, 58, 138), 0.1) 0%,
    transparent 50%);
}

/* =================== 响应式设计 =================== */

/* 平板设备适配 */
@media (max-width: 1200px) {
  .model-panel:not(.collapsed) {
    width: 300px;
  }

  .bom-layout {
    height: 80vh;
    min-height: 600px;
  }
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .toolbar-container {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .bom-layout {
    flex-direction: column;
    height: auto;
    min-height: 70vh;
  }

  .model-panel {
    width: 100% !important;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color-1);
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .model-panel.collapsed {
    max-height: 60px;
  }

  .bom-panel {
    border-radius: 0 0 8px 8px;
  }

  .panel-header {
    padding: 16px 20px;
  }

  .header-title h3 {
    font-size: 16px;
  }

  .model-list {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 12px 16px;
    gap: 12px;
  }

  .model-item {
    flex-shrink: 0;
    min-width: 120px;
    margin-bottom: 0;
    padding: 12px;
    text-align: center;
    flex-direction: column;
  }

  .model-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .bom-content {
    padding: 16px;
  }

  .filter-panel {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
    gap: 12px;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 100%;
  }
}









/* 结束样式 */

























/* 时间列样式 */
.time-column {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.time-icon {
  color: var(--current-color);
  font-size: 14px;
}

.time-content {
  display: flex;
  flex-direction: column;
}

.time-date {
  font-weight: 600;
  color: var(--base-color-1);
  font-size: 13px;
  line-height: 1.2;

/* 款式标签样式 */
.style-tag {
  font-weight: 500;
}

.style-tag .el-icon-star-on {
  color: #f39c12;
}

/* 产品编码样式 */
.product-number {
  color: #409EFF;
  font-weight: 500;
}

.no-product-number {
  color: #E6A23C;
  font-style: italic;
}
}

.time-time {
  color: var(--base-color-2);
  font-size: 12px;
  line-height: 1.2;
}

/* 深色主题适配 */
.theme-dark .time-date {
  color: var(--base-color-6, #b1b3b8);
}

.theme-dark .time-time {
  color: var(--base-color-5, #8b8e93);
}

.theme-dark h4 {
  color: var(--base-color-6, #b1b3b8);
}

.theme-starry-sky h4 {
  color: var(--theme-color, #ffffff);
}

/* 字段配置对话框样式 */
.el-checkbox-group .el-checkbox {
  width: 100%;
  margin-right: 0;
}

.el-checkbox-group .el-checkbox + .el-checkbox {
  margin-left: 0;
}

/* 字段预览样式 */
h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--base-color-1, #303133);
  font-weight: 500;
}

/* 字段配置预览区域 */
.dialog-footer .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dialog-footer .el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
  color: var(--theme-color);
}

.dialog-footer .el-button--primary:hover {
  background: rgba(var(--current-color-rgb, 54, 113, 232), 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--current-color-rgb, 54, 113, 232), 0.3);
}

/* 字段配置对话框基础样式 */
.column-config-dialog .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
}

.column-config-dialog .el-checkbox:hover .el-checkbox__inner {
  border-color: var(--current-color, #409eff);
}

/* 字段配置预览区域基础样式 */
.column-config-dialog .preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
  background-color: #f5f7fa;
}

/* 星空主题下的预览容器 */
.theme-starry-sky .column-config-dialog .preview-container,
body.theme-starry-sky .column-config-dialog .preview-container {
  background-color: var(--base-item-bg, rgba(26, 31, 60, 0.9)) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
}

.column-config-dialog .preview-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #fff;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e4e7ed;
}

/* 星空主题下的预览项 */
.theme-starry-sky .column-config-dialog .preview-item,
body.theme-starry-sky .column-config-dialog .preview-item {
  background: var(--base-main-bg, #0b0d1a) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
  color: var(--theme-color, #ffffff) !important;
}

.column-config-dialog .preview-empty {
  text-align: center;
  color: #c0c4cc;
  margin-top: 100px;
}

/* 星空主题下的空状态 */
.theme-starry-sky .column-config-dialog .preview-empty,
body.theme-starry-sky .column-config-dialog .preview-empty {
  color: var(--base-color-3, #a0a0a0) !important;
}

/* 深色主题适配 */
.theme-dark .column-config-dialog {
  .el-dialog__header {
    background: var(--base-menu-background, #363e4f);
    border-bottom-color: var(--border-color-1, #4c5565);
  }

  .el-dialog__title {
    color: var(--base-color-6, #e5eaf3);
  }

  .el-dialog__body {
    background: var(--base-menu-background, #363e4f);
    color: var(--base-color-6, #e5eaf3);
  }

  .el-checkbox__label {
    color: var(--base-color-6, #e5eaf3);
  }

  .el-checkbox:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  h4 {
    color: var(--base-color-6, #e5eaf3);
  }

  .preview-container {
    background: var(--preview-bg, #2a3441);
    border-color: var(--border-color-1, #4c5565);
  }

  .preview-item {
    background: var(--base-menu-background, #363e4f);
    border-color: var(--border-color-1, #4c5565);
    color: var(--base-color-6, #e5eaf3);
  }

  .preview-empty {
    color: var(--base-color-5, #8b949e);
  }
}

/* 星空主题适配 */
.theme-starry-sky {
  /* 页面背景 */
  background: var(--starry-bg, linear-gradient(135deg, #0f182f 0%, #1a2644 100%));
  min-height: 100vh;

  /* 面板适配 */
  .model-panel, .bom-panel {
    background: rgba(15, 24, 47, 0.85);
    border-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
  }

  .panel-header {
    background: rgba(10, 18, 35, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .header-title h3,
  .panel-header h3 {
    color: #e6e8eb;
  }

  /* 型号项适配 */
  .model-item {
    background: rgba(20, 30, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e6e8eb;

    &:hover {
      background: rgba(64, 158, 255, 0.2);
      border-color: rgba(64, 158, 255, 0.5);
    }

    &.active {
      background: rgba(64, 158, 255, 0.3);
      border-color: var(--current-color, #409eff);
      color: #fff;
    }
  }

  /* 表格适配 */
  .el-table {
    background: rgba(15, 24, 47, 0.9) !important;
    color: #e6e8eb;

    th, td {
      background: transparent !important;
      border-color: rgba(255, 255, 255, 0.1) !important;
      color: #e6e8eb !important;
    }

    .el-table__header {
      background: rgba(10, 18, 35, 0.95) !important;
      color: #e6e8eb !important;
    }

    tr:hover td {
      background: rgba(64, 158, 255, 0.1) !important;
    }
  }

  /* 筛选面板适配 */
  .filter-panel {
    background: rgba(15, 24, 47, 0.85);
    border-color: rgba(255, 255, 255, 0.15);
  }

  /* 表单控件适配 */
  .el-input__inner,
  .el-select .el-input__inner,
  .el-textarea__inner {
    background: rgba(20, 30, 50, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #e6e8eb !important;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }

  /* 按钮适配 */
  .el-button {
    background: rgba(20, 30, 50, 0.8);
    border-color: rgba(255, 255, 255, 0.2);
    color: #e6e8eb;

    &:hover {
      background: rgba(64, 158, 255, 0.3);
      border-color: var(--current-color, #409eff);
    }

    &.el-button--primary {
      background: var(--current-color, #409eff);
      border-color: var(--current-color, #409eff);
      color: #fff;

      &:hover {
        background: rgba(var(--current-color-rgb, 64, 158, 255), 0.8);
      }
    }
  }

     /* 字段配置对话框适配 */
   .column-config-dialog .el-dialog__header {
     background: rgba(15, 24, 47, 0.98) !important;
     border-bottom-color: rgba(255, 255, 255, 0.1) !important;
   }

   .column-config-dialog .el-dialog__title {
     color: #e6e8eb !important;
   }

   .column-config-dialog .el-dialog__body {
     background: rgba(15, 24, 47, 0.95) !important;
     color: #e6e8eb !important;
   }

   .column-config-dialog .el-checkbox__label {
     color: #e6e8eb !important;
   }

     h4 {
     color: var(--theme-color, #ffffff);
   }

     .column-config-dialog .preview-container {
     background: rgba(0, 20, 40, 0.3) !important;
     border-color: rgba(255, 255, 255, 0.1) !important;
   }

   .column-config-dialog .preview-item {
     background: rgba(15, 24, 47, 0.8) !important;
     border-color: rgba(255, 255, 255, 0.1) !important;
     color: #e6e8eb !important;
   }

   .column-config-dialog .preview-empty {
     color: rgba(255, 255, 255, 0.5) !important;
   }

  /* 对话框适配 */
  .el-dialog {
    background: rgba(15, 24, 47, 0.95);
    color: #e6e8eb;
  }

  .el-dialog__header {
    background: rgba(10, 18, 35, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .el-dialog__title {
    color: #e6e8eb;
  }

  /* 分页器适配 */
  .el-pagination {
    .el-pagination__total,
    .el-pagination__jump {
      color: #e6e8eb;
    }

    .btn-prev, .btn-next,
    .el-pager li {
      background: rgba(20, 30, 50, 0.8);
      border-color: rgba(255, 255, 255, 0.2);
      color: #e6e8eb;

      &:hover {
        background: rgba(64, 158, 255, 0.3);
      }

      &.active {
        background: var(--current-color, #409eff);
        color: #fff;
      }
    }
  }
}

/* ============= BOM引用功能样式 ============= */

/* 原料单元格样式 */
.material-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-height: 50px;
  justify-content: center;
}

.material-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--base-color-1);
}

.material-icon {
  color: var(--current-color);
  font-size: 16px;
}

.material-name {
  font-weight: 500;
}

/* 引用信息样式 */
.reference-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  text-align: center;
  padding: 8px;
  background: rgba(var(--current-color-rgb, 64, 158, 255), 0.05);
  border-radius: 6px;
  border: 1px solid rgba(var(--current-color-rgb, 64, 158, 255), 0.2);
}

.reference-icon {
  color: var(--current-color);
  font-size: 14px;
}

.reference-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.reference-label {
  font-size: 11px;
  color: var(--base-color-2);
  font-weight: 500;
}

.reference-target {
  font-size: 12px;
  color: var(--current-color);
  font-weight: 600;
}

.preview-btn {
  font-size: 11px !important;
  padding: 2px 6px !important;
  height: auto !important;
  line-height: 1 !important;
}

/* 空原料样式 */
.empty-material {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  color: var(--base-color-3);
  padding: 8px;
}

.empty-icon {
  color: #e6a23c;
  font-size: 16px;
}

.empty-text {
  font-size: 12px;
}

.reference-btn {
  font-size: 11px !important;
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1 !important;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.reference-btn {
  background: #67c23a !important;
  border-color: #67c23a !important;
}

.edit-reference-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
}

.remove-reference-btn {
  background: #909399 !important;
  border-color: #909399 !important;
}

/* BOM引用弹窗样式 */
.bom-reference-dialog .el-dialog {
  border-radius: 12px;
}

.reference-dialog-header {
  margin-bottom: 20px;
}

.current-item-info {
  margin-bottom: 12px;
  text-align: center;
}

.reference-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.referencable-bom-section,
.reference-preview-section {
  border: 1px solid var(--border-color-1);
  border-radius: 8px;
  padding: 16px;
  background: var(--base-item-bg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color-1);
}

.section-header h3 {
  margin: 0;
  color: var(--base-color-1);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header i {
  color: var(--current-color);
}

/* 分组BOM列表样式 */
.grouped-bom-list {
  max-height: 400px;
  overflow-y: auto;
}

.model-group {
  margin-bottom: 16px;
  border: 1px solid var(--border-color-1);
  border-radius: 8px;
  overflow: hidden;
}

.model-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(var(--current-color-rgb, 64, 158, 255), 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-color-1);
}

.model-header:hover {
  background: rgba(var(--current-color-rgb, 64, 158, 255), 0.1);
}

.model-header i {
  color: var(--current-color);
  transition: transform 0.3s ease;
}

.model-header i.expanded {
  transform: rotate(90deg);
}

.material-count {
  flex: 1;
  color: var(--base-color-2);
  font-size: 14px;
}

.select-model-btn {
  font-size: 12px !important;
  padding: 4px 12px !important;
  height: auto !important;
}

.material-list {
  background: var(--base-main-bg);
}

.reference-table,
.preview-table,
.detail-table {
  border: none !important;
}

.reference-table .el-table__row,
.preview-table .el-table__row,
.detail-table .el-table__row {
  background: transparent !important;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--base-color-3);
}

.empty-state i {
  font-size: 48px;
  color: var(--base-color-4);
  margin-bottom: 16px;
}

.empty-state p {
  margin: 8px 0;
  line-height: 1.5;
}

.empty-hint {
  font-size: 12px;
  color: var(--base-color-4);
}

/* 引用预览弹窗样式 */
.reference-preview-dialog .preview-header {
  margin-bottom: 20px;
}

.reference-info {
  padding: 16px;
  background: rgba(var(--current-color-rgb, 64, 158, 255), 0.05);
  border-radius: 8px;
  border: 1px solid rgba(var(--current-color-rgb, 64, 158, 255), 0.2);
}

/* 深色主题适配 */
.theme-dark {
  .reference-info {
    background: rgba(20, 30, 50, 0.8);
    border-color: rgba(64, 158, 255, 0.3);
  }

  .empty-material {
    color: var(--base-color-5, #8b949e);
  }

  .material-info {
    color: var(--base-color-6, #e5eaf3);
  }

  .referencable-bom-section,
  .reference-preview-section {
    background: var(--base-menu-background, #363e4f);
    border-color: var(--border-color-1, #4c5565);
  }

  .model-header {
    background: rgba(64, 158, 255, 0.1);
  }

  .material-list {
    background: var(--base-menu-background, #363e4f);
  }
}

/* 星空主题适配 */
.theme-starry-sky {
  .reference-info {
    background: rgba(15, 24, 47, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: #e6e8eb !important;
  }

  .material-info {
    color: #e6e8eb !important;
  }

  .empty-material {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .referencable-bom-section,
  .reference-preview-section {
    background: rgba(15, 24, 47, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .model-header {
    background: rgba(64, 158, 255, 0.2) !important;
    color: #e6e8eb !important;
  }

  .material-list {
    background: rgba(15, 24, 47, 0.8) !important;
  }

  .empty-state {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .empty-state i {
    color: rgba(255, 255, 255, 0.4) !important;
  }
}

/* =================== 通用设置功能样式 =================== */

/* 通用设置确认对话框样式 */
.generic-setting-confirm-dialog .el-message-box__content {
  text-align: left !important;
}

.generic-setting-confirm-dialog .el-message-box__message {
  margin: 0 !important;
}

.generic-setting-confirm-dialog .el-message-box__message p {
  margin: 8px 0 !important;
  line-height: 1.6 !important;
}

.generic-setting-confirm-dialog .el-message-box__message strong {
  color: var(--current-color, #409eff) !important;
}

/* =================== 结束样式 =================== */

</style>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品编号" prop="productNumber">
        <el-input v-model="queryParams.productNumber" placeholder="请输入产品编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="库存数量" prop="productQuantity">
          <el-input
            v-model="queryParams.productQuantity"
            placeholder="请输入库存数量"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <!-- <el-form-item label="库存单位" prop="productUnit">
          <el-input
            v-model="queryParams.productUnit"
            placeholder="请输入库存单位"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <el-form-item label="款式名称" prop="styleName" label-width="120px">
        <el-select v-model="queryParams.styleName" placeholder="请选择款式名称" clearable
          @change="handleQuery" style="width: 180px;" :loading="styleOptionsLoading">
          <el-option
            v-for="style in styleOptions"
            :key="style.styleId"
            :label="style.styleName"
            :value="style.styleName">
          </el-option>
          <div v-if="!styleOptionsLoading && styleOptions.length === 0"
            style="text-align: center; padding: 10px; color: #999;">
            暂无款式数据
          </div>
        </el-select>
      </el-form-item>
      <el-form-item label="板型" prop="productAttribute" label-width="80px">
        <el-select v-model="queryParams.productAttribute" placeholder="请选择板型" clearable
          @change="handleQuery" style="width: 180px;">
          <el-option label="上下板" value="上下板"></el-option>
          <el-option label="单板" value="单板"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="产品属性" prop="productAttribute">
          <el-input
            v-model="queryParams.productAttribute"
            placeholder="请输入产品属性"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="最大库存" prop="inventoryMax">
          <el-input
            v-model="queryParams.inventoryMax"
            placeholder="请输入最大库存"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="最小库存" prop="inventoryMin">
          <el-input
            v-model="queryParams.inventoryMin"
            placeholder="请输入最小库存"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="安全库存" prop="inventorySafe">
          <el-input
            v-model="queryParams.inventorySafe"
            placeholder="请输入安全库存"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="工艺路线" prop="routeId">
          <el-input
            v-model="queryParams.routeId"
            placeholder="请输入工艺路线"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <el-breadcrumb separator="/" class="breadcrumb-nav">
        <el-breadcrumb-item>
          <a @click="navigateToProductList">产品管理</a>
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentPage !== 'product'">
          <span>{{ pageTitle }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 产品列表页面 -->
    <div v-show="currentPage === 'product'">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:product:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:product:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:product:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:product:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
          >导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-setting"
            size="mini"
            @click="handleColumnConfig"
          >字段配置</el-button>
        </el-col>
        <!-- 新增导航按钮 -->
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-setting"
            size="mini"
            class="nav-button"
            @click="navigateToFunctionalStyle"
          >功能系列款式</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-setting"
            size="mini"
            class="nav-button"
            @click="navigateToSeriesProducts"
          >系列产品</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 功能系列款式页面 -->
    <div v-show="currentPage === 'functionalStyle'">
      <FunctionalStylePage />
    </div>

    <!-- 系列产品页面 -->
    <div v-show="currentPage === 'seriesProducts'">
      <SeriesProductsPage />
    </div>

    <!-- 产品表格 - 只在产品页面显示 -->
    <el-table v-show="currentPage === 'product'" v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- 必须显示的列 -->
      <el-table-column label="产品编号" prop="productNumber" width="180" align="center" />
      <el-table-column label="产品名称" prop="productName" align="center" />

      <!-- 根据配置显示的列 -->
      <el-table-column
        v-for="column in visibleColumns.filter(col => !col.required)"
        :key="column.prop"
        :label="column.label"
        :prop="column.prop"
        :width="column.width"
        :min-width="column.minWidth"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="column.prop === 'productUnit'">
            <!-- 直接显示库存单位原始值 -->
            {{ scope.row.productUnit || '-' }}
          </span>
          <span v-else-if="column.prop === 'createTime' || column.prop === 'updateTime'">
            {{ scope.row[column.prop] || '-' }}
          </span>
          <span v-else>
            {{ scope.row[column.prop] || '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:product:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:product:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="currentPage === 'product' && total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改产品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="产品编号" prop="productNumber">
              <div style="display: flex; align-items: center; flex-wrap: wrap;">
                <el-input v-model="form.productNumber" placeholder="请输入产品编号" style="width: 220px;"
                  :disabled="title == '修改产品'" />
                <el-button type="primary" size="mini" @click="generateCode"
                  :disabled="title == '修改产品'" style="margin-left: 8px;">生成编号</el-button>
              </div>
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                格式：PD + 年月日(8位) + 序号(3位) + _款式ID，如：PD20250113001_1（动态获取款式ID）
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" style="width: 220px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <!-- <el-col :span="12">
            <el-form-item label="库存数量" prop="productQuantity">
              <el-input-number v-model="form.productQuantity" placeholder="请输入库存数量" :min="0" style="width: 220px;"/>
            </el-form-item>
          </el-col> -->

        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="款式名称" prop="styleName">
              <el-select v-model="form.styleName" placeholder="请选择款式名称"
                @change="handleStyleChange" style="width: 220px;" :loading="styleOptionsLoading">
                <el-option
                  v-for="style in styleOptions"
                  :key="style.styleId"
                  :label="style.styleName"
                  :value="style.styleName">
                </el-option>
                <div v-if="!styleOptionsLoading && styleOptions.length === 0"
                  style="text-align: center; padding: 10px; color: #999;">
                  暂无款式数据
                </div>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品属性" prop="productAttribute">
              <el-select v-model="form.productAttribute" placeholder="请选择产品属性" clearable style="width: 220px;">
                <el-option
                  v-for="attribute in productAttributeOptions"
                  :key="attribute.value"
                  :label="attribute.label"
                  :value="attribute.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="最大库存" prop="inventoryMax">
              <el-input-number v-model="form.inventoryMax" placeholder="请输入最大库存" :min="0" style="width: 220px;"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最小库存" prop="inventoryMin">
              <el-input-number v-model="form.inventoryMin" placeholder="请输入最小库存" :min="0" style="width: 220px;"/>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="工艺路线" prop="routeId">
              <!-- <el-input v-model="form.routeId" placeholder="请输入工艺路线" /> -->
              <el-select v-model="form.routeId" placeholder="请选择工艺路线">
                <el-option v-for="item in routeList" :key="item.processRouteId" :label="item.processRouteName"
                  :value="item.processRouteId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存单位" prop="productUnit">
              <el-select clearable v-model="form.productUnit" placeholder="请选择单位" style="width: 220px;">
                <el-option v-for="dict in dict.type.produce_unit" :key="dict.value" :label="dict.value"
                  :value="dict.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="安全库存" prop="inventorySafe">
              <el-input-number v-model="form.inventorySafe" placeholder="请输入安全库存" :min="0" style="width: 220px;"/>
            </el-form-item>
          </el-col> -->

        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" style="width: 660px;" v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 产品导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的产品数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="columnConfigVisible" width="600px" append-to-body class="column-config-dialog">
      <div style="max-height: 400px; overflow-y: auto;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>可选字段</h4>
            <el-checkbox-group v-model="selectedColumns" @change="handleColumnChange">
              <div v-for="column in allColumns" :key="column.prop" style="margin-bottom: 8px;">
                <el-checkbox :label="column.prop" :disabled="column.required">
                  {{ column.label }}
                  <span v-if="column.required" style="color: var(--current-color, #f56c6c);">(必需)</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-col>
          <el-col :span="12">
            <h4>字段预览</h4>
            <div class="preview-container">
              <div v-for="column in previewColumns" :key="column.prop" class="preview-item">
                <span>{{ column.label }}</span>
                <span style="color: var(--base-color-2, #909399); font-size: 12px;">{{ column.width || 'auto' }}</span>
              </div>
              <div v-if="previewColumns.length === 0" class="preview-empty">
                请选择要显示的字段
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetColumnConfig">重置默认</el-button>
        <el-button type="primary" @click="saveColumnConfig">确 定</el-button>
        <el-button @click="columnConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProduct, getProduct, delProduct, addProduct, updateProduct, importTemplate } from "@/api/basicData/product";
import { listProcessRoute } from "@/api/basicData/processRoute";
import { listFunctionalStyle } from "@/api/basicData/functionalStyle";
import { getToken } from "@/utils/auth";
import FunctionalStylePage from '@/views/basic-data/functionalStyle/index.vue';
import SeriesProductsPage from '@/views/basic-data/seriesProducts/index.vue';

export default {
  name: "Product",
  dicts: ['produce_unit'],
  components: {
    FunctionalStylePage,
    SeriesProductsPage
  },
  data() {
    return {
      // 当前页面
      currentPage: 'product',
      // 页面标题
      pageTitle: '产品管理',
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 产品表格数据
      productList: [],
      // 工艺路线数据
      routeList: [],
      // 产品属性选项
      productAttributeOptions: [
        { value: '上下板', label: '上下板' },
        { value: '单板', label: '单板' }
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        productNumber: undefined,
        productName: undefined,
        productUnit: undefined,
        styleName: undefined,
        productAttribute: undefined,
        routeId: undefined,
        routeName: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productId: [
          { required: true, message: "主键ID不能为空", trigger: "blur" }
        ],
        productNumber: [
          { required: true, message: "产品编号不能为空", trigger: "blur" },
          { validator: this.validateProductNumber, trigger: "blur" },
          { validator: this.validateProductNumberFormat, trigger: "blur" }
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" }
        ],

        /* routeId: [
          { required: true, message: "工艺路线不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ] */
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的产品数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/product/importData"
      },
             // 字段配置相关
       columnConfigVisible: false,
       selectedColumns: [
         'styleName',
         'productAttribute',
         'productUnit',
         'createName',
         'createTime'
       ],
       // 所有可选字段定义
       allColumns: [
         { prop: 'productNumber', label: '产品编号', required: true, width: 180 },
         { prop: 'productName', label: '产品名称', required: true, width: 'auto' },
         { prop: 'productUnit', label: '库存单位', required: false, width: 120 },
         { prop: 'styleName', label: '款式名称', required: false, width: 160 },
         { prop: 'productAttribute', label: '产品属性', required: false, width: 150 },
         { prop: 'routeName', label: '工艺路线', required: false, width: 150 },
         { prop: 'createName', label: '创建人', required: false, width: 120 },
         { prop: 'createTime', label: '创建时间', required: false, width: 180 },
         { prop: 'updateTime', label: '更新时间', required: false, width: 180 },
         { prop: 'remark', label: '备注', required: false, minWidth: 150 }
       ],

       // 款式选项数据
       styleOptions: [],
       styleOptionsLoading: false
          };
    },
    computed: {
      // 可见的列配置
      visibleColumns() {
        return this.allColumns.filter(column =>
          column.required || this.selectedColumns.includes(column.prop)
        );
      },
      // 预览的列配置
      previewColumns() {
        return this.visibleColumns;
      }
    },
      created() {
      // 先清除可能有问题的本地配置，确保使用默认配置
      localStorage.removeItem('product_column_config');
      this.initColumnConfig();
      this.getList();
      this.getRouteList();
      // 获取款式选项列表
      this.getStyleOptions();
    },

  methods: {
    // 导航方法
    navigateToProductList() {
      this.currentPage = 'product';
      this.pageTitle = '产品管理';
    },
    navigateToFunctionalStyle() {
      this.currentPage = 'functionalStyle';
      this.pageTitle = '功能系列款式';
    },
    navigateToSeriesProducts() {
      this.currentPage = 'seriesProducts';
      this.pageTitle = '系列产品';
    },
    // 编号生成：PD + 年月日8位 + 自增序号3位 + 款式后缀
    async generateCode() {
      try {
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const dateStr = `${year}${month}${day}`;

        // 异步获取款式编码信息
        const styleCodeInfo = await this.getStyleCodeInfo(this.form.styleName);

        try {
          // 查询当天已有的相同款式产品数量来计算自增编号
          const count = await this.getProductCountForDateAndStyle(dateStr, styleCodeInfo);
          const nextNumber = String(count + 1).padStart(3, '0');
          // 格式：PD + 年月日8位 + 自增序号3位 + 款式后缀 = 16位
          this.form.productNumber = `${styleCodeInfo.prefix}${dateStr}${nextNumber}${styleCodeInfo.suffix}`;
        } catch (countError) {
          // 如果查询失败，使用时间戳的后3位作为备选方案
          console.error('查询产品数量失败:', countError);
          const fallbackNumber = String(currentDate.getTime()).slice(-3);
          this.form.productNumber = `${styleCodeInfo.prefix}${dateStr}${fallbackNumber}${styleCodeInfo.suffix}`;
        }
      } catch (error) {
        // 整个生成过程失败，显示错误提示
        console.error('生成产品编号失败:', error);
        this.$modal.msgError('生成产品编号失败，请手动输入或稍后重试');
      }
    },

    // 根据款式名称获取产品编号前缀和后缀（异步方法）
    async getStyleCodeInfo(styleName) {
      try {
        // 调用功能款式接口查询styleId
        const response = await listFunctionalStyle({
          pageNum: 1,
          pageSize: 100,
          styleName: styleName
        });

        // 查找匹配的款式记录
        const matchedStyle = response.rows.find(item => item.styleName === styleName);

        if (matchedStyle && matchedStyle.styleId) {
          // 找到匹配的款式，使用查询到的styleId
          return {
            prefix: 'PD',
            suffix: `_${matchedStyle.styleId}`
          };
        } else {
          // 未找到匹配的款式，使用默认值
          console.warn(`未找到款式"${styleName}"对应的styleId，使用默认后缀_9`);
          return { prefix: 'PD', suffix: '_9' };
        }
      } catch (error) {
        // 接口调用失败，使用默认值
        console.error('查询功能款式失败:', error);
        console.warn(`款式"${styleName}"查询失败，使用默认后缀_9`);
        return { prefix: 'PD', suffix: '_9' };
      }
    },

    // 处理款式变更
    handleStyleChange(newStyleName) {
      // 如果是新增产品且产品编号为空或者是自动生成的编号，提示重新生成
      if (this.title === "添加产品" && this.form.productNumber) {
        // 检查是否是自动生成的编号格式：PD + 8位日期 + 3位序号 + _数字
        const isAutoGenerated = /^PD\d{8}\d{3}_\d+$/.test(this.form.productNumber);
        if (isAutoGenerated) {
          this.$modal.confirm('检测到款式已变更，是否重新生成产品编号？').then(async () => {
            await this.generateCode();
          }).catch(() => {
            // 用户选择不重新生成，保持当前编号
          });
        }
      }
    },

    // 获取款式选项列表
    async getStyleOptions() {
      try {
        this.styleOptionsLoading = true;
        const response = await listFunctionalStyle({
          pageNum: 1,
          pageSize: 100,
          delFlag: '0' // 只获取未删除的款式
        });

        if (response && response.rows) {
          this.styleOptions = response.rows.map(item => ({
            styleId: item.styleId,
            styleName: item.styleName
          }));

          // 如果没有获取到任何款式，提供默认选项
          if (this.styleOptions.length === 0) {
            console.warn('未获取到任何款式数据，使用默认选项');
            this.styleOptions = [
              { styleId: 9, styleName: '标准款' }
            ];
          }
        } else {
          throw new Error('接口返回数据格式异常');
        }
      } catch (error) {
        console.error('获取款式选项失败:', error);
        // 接口调用失败时，提供默认选项
        this.styleOptions = [
          { styleId: 1, styleName: '蓝牙款' },
          { styleId: 2, styleName: '无线款' },
          { styleId: 3, styleName: '按键款' },
          { styleId: 9, styleName: '标准款' }
        ];
        this.$modal.msgWarning('获取款式列表失败，已加载默认选项');
      } finally {
        this.styleOptionsLoading = false;
      }
    },

    // 刷新款式选项（可在需要时调用）
    async refreshStyleOptions() {
      await this.getStyleOptions();
    },

    // 产品编号唯一性校验
    async validateProductNumber(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      try {
        const response = await listProduct({
          pageNum: 1,
          pageSize: 1,
          productNumber: value
        });

        // 检查是否存在相同编号的产品
        const existingProducts = response.rows.filter(product =>
          product.productNumber === value &&
          product.productId !== this.form.productId // 排除当前编辑的产品
        );

        if (existingProducts.length > 0) {
          callback(new Error('产品编号已存在，请使用其他编号'));
        } else {
          callback();
        }
      } catch (error) {
        console.error('校验产品编号唯一性失败:', error);
        // 网络错误时不阻止提交，由后端进行最终校验
        callback();
      }
    },

    // 产品编号格式验证
    validateProductNumberFormat(rule, value, callback) {
      if (!value) {
        callback();
        return;
      }

      // 检查编号格式：PD + 8位日期 + 3位序号 + _数字
      const formatRegex = /^PD\d{8}\d{3}_\d+$/;

      if (!formatRegex.test(value)) {
        // 如果不是标准格式，检查是否是手动输入的有效格式
        // 允许一定的灵活性，但需要符合基本规范
        if (value.length > 20) {
          callback(new Error('产品编号长度不能超过20位'));
          return;
        }

        // 检查是否包含特殊字符（除了下划线）
        const invalidChars = /[^A-Za-z0-9_]/;
        if (invalidChars.test(value)) {
          callback(new Error('产品编号只能包含字母、数字和下划线'));
          return;
        }
      }

      callback();
    },

    // 获取指定日期和款式的产品数量
    async getProductCountForDateAndStyle(dateStr, styleCodeInfo) {
      try {
        // 构建查询模式：PD + 日期 + 任意3位数字 + 款式后缀
        const searchPattern = `${styleCodeInfo.prefix}${dateStr}`;

        const response = await listProduct({
          pageNum: 1,
          pageSize: 1000, // 获取足够多的数据来计算
          productNumber: searchPattern // 模糊查询当天相同款式的产品
        });

        // 筛选出确实是当天生成的相同款式产品编号
        // 格式：PD + 8位日期 + 3位序号 + 款式后缀
        const todayProducts = response.rows.filter(product => {
          if (!product.productNumber) return false;

          // 检查是否符合完整格式：PD20250113001_1
          const pattern = new RegExp(`^${styleCodeInfo.prefix}${dateStr}\\d{3}${styleCodeInfo.suffix.replace('_', '\\_')}$`);
          return pattern.test(product.productNumber);
        });

        return todayProducts.length;
      } catch (error) {
        console.error('获取产品数量失败:', error);
        throw error;
      }
    },

    // 获取指定日期的产品数量（保留原方法以兼容其他功能）
    async getProductCountForDate(dateStr) {
      try {
        const response = await listProduct({
          pageNum: 1,
          pageSize: 1000,
          productNumber: `PD${dateStr}`
        });

        const todayProducts = response.rows.filter(product =>
          product.productNumber && product.productNumber.startsWith(`PD${dateStr}`)
        );

        return todayProducts.length;
      } catch (error) {
        console.error('获取产品数量失败:', error);
        throw error;
      }
    },
    /** 查询产品列表 */
    getList() {
      this.loading = true;
      listProduct(this.queryParams).then(response => {
        this.productList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('查询产品列表失败:', error);
        this.$modal.msgError('查询产品列表失败，请稍后重试');
        this.loading = false;
      });
    },
    /** 查询工艺流程列表 */
    getRouteList() {
      this.loading = true;
      listProcessRoute().then(response => {
        this.routeList = response.rows;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
        this.form = {
          productId: undefined,
          productNumber: undefined,
          productName: undefined,
          productUnit: "台", // 默认值
          styleName: "标准款", // 默认值：款式名称
          productAttribute: "无", // 默认值
          routeId: undefined,
          routeName: undefined,
          createBy: undefined,
          createTime: undefined,
          updateBy: undefined,
          updateTime: undefined,
          delFlag: undefined,
          remark: undefined
        };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.productId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      //this.form.productNumber = this.generateCode();
      this.open = true;
      this.title = "添加产品";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const productId = row.productId || this.ids
      getProduct(productId).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改产品";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          var processRoute = this.routeList.find(r => r.processRouteId === this.form.routeId)
          if (processRoute) {
            this.form.routeName = processRoute.processRouteName;
          }
          if (this.form.productId != null) {
            updateProduct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addProduct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const productIds = row.productId || this.ids;
      this.$modal.confirm('是否确认删除产品编号为"' + productIds + '"的数据项？').then(() => {
        this.loading = true;
        return delProduct(productIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "产品导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/product/importTemplate', {
      }, `product_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/product/export', {
        ...this.queryParams
      }, `product_${new Date().getTime()}.xlsx`)
    },
    handleRadio(val) {
      var radioTypes = [];
      val.split("~~").forEach(item => {
        radioTypes.push(JSON.parse(item));
      })
      return radioTypes;
    },
    splitArrayIntoChunks(array, chunkSize) {
      const result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        result.push(array.slice(i, i + chunkSize));
      }
      return result;
    },
         // ============= 字段配置相关方法 =============

     /** 初始化字段配置 */
     initColumnConfig() {
       const savedConfig = localStorage.getItem('product_column_config');
       if (savedConfig) {
         try {
           this.selectedColumns = JSON.parse(savedConfig);
         } catch (e) {
           console.error('解析字段配置失败:', e);
           // 使用默认配置
           this.selectedColumns = [
             'styleName',
             'productAttribute',
             'productUnit',
             'createName',
             'createTime'
           ];
         }
       } else {
         // 默认显示的字段
         this.selectedColumns = [
           'styleName',
           'productAttribute',
           'productUnit',
           'createName',
           'createTime'
         ];
       }

       // 确保库存单位字段在默认显示列表中
       if (!this.selectedColumns.includes('productUnit')) {
         this.selectedColumns.push('productUnit');
       }
     },

     /** 字段配置按钮操作 */
     handleColumnConfig() {
       this.columnConfigVisible = true;
     },

     /** 字段变化处理 */
     handleColumnChange(value) {
       // 这里可以添加实时预览逻辑
     },

     /** 重置为默认配置 */
     resetColumnConfig() {
       this.selectedColumns = [
         'productAttribute',
         'productUnit',
         'specification',
         'createName',
         'createTime'
       ];
       // 清除本地存储的配置
       localStorage.removeItem('product_column_config');
     },

     /** 保存字段配置 */
     saveColumnConfig() {
       // 保存到本地存储
       localStorage.setItem('product_column_config', JSON.stringify(this.selectedColumns));
       this.columnConfigVisible = false;
       this.$modal.msgSuccess("字段配置已保存");
     }
  }
};
</script>

<style scoped>
/* 面包屑导航样式 */
.breadcrumb-container {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: var(--breadcrumb-bg-color, #f5f7fa);
  border-radius: 4px;
  border: 1px solid var(--border-color, #e4e7ed);
}

.breadcrumb-nav {
  font-size: 14px;
}

.breadcrumb-nav a {
  color: var(--current-color, #409eff) !important;
  text-decoration: none;
  cursor: pointer;
  font-weight: 500;
}

.breadcrumb-nav a:hover {
  color: var(--color-2, #66b1ff) !important;
}

.breadcrumb-nav span {
  color: var(--base-color-1, #606266) !important;
  font-weight: 500;
}

/* 主题适配 */
.theme-dark .breadcrumb-container {
  background-color: var(--breadcrumb-bg-color, #2d3748);
  border-color: var(--border-color, #4a5568);
}

.theme-dark .breadcrumb-nav a {
  color: var(--current-color, #3a7b99) !important;
}

.theme-dark .breadcrumb-nav a:hover {
  color: var(--color-2, #70afce) !important;
}

.theme-dark .breadcrumb-nav span {
  color: var(--base-color-1, #fff) !important;
}

/* 星空主题适配 */
.theme-starry-sky .breadcrumb-container {
  background-color: var(--base-item-bg, #1a1f3c);
  border-color: var(--border-color-1, #1e3a8a);
}

.theme-starry-sky .breadcrumb-nav a {
  color: var(--current-color, #1e3a8a) !important;
}

.theme-starry-sky .breadcrumb-nav a:hover {
  color: var(--color-2, #3b82f6) !important;
}

.theme-starry-sky .breadcrumb-nav span {
  color: var(--theme-color, #ffffff) !important;
}

/* 字段配置对话框样式 */
.el-checkbox-group .el-checkbox {
  width: 100%;
  margin-right: 0;
}

.el-checkbox-group .el-checkbox + .el-checkbox {
  margin-left: 0;
}

/* 表格列宽度优化 */
.el-table .cell {
  word-break: break-word;
}

/* 字段预览样式 */
h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--base-color-1, #303133);
  font-weight: 500;
}

/* 字段配置对话框基础样式 */
.column-config-dialog .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
}

.column-config-dialog .el-checkbox:hover .el-checkbox__inner {
  border-color: var(--current-color, #409eff);
}

/* 字段配置预览区域基础样式 */
.column-config-dialog .preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
  background-color: #f5f7fa;
}

/* 星空主题下的预览容器 */
.theme-starry-sky .column-config-dialog .preview-container,
body.theme-starry-sky .column-config-dialog .preview-container {
  background-color: var(--base-item-bg, rgba(26, 31, 60, 0.9)) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
}

.column-config-dialog .preview-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #fff;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e4e7ed;
}

/* 星空主题下的预览项 */
.theme-starry-sky .column-config-dialog .preview-item,
body.theme-starry-sky .column-config-dialog .preview-item {
  background: var(--base-main-bg, #0b0d1a) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
  color: var(--theme-color, #ffffff) !important;
}

.column-config-dialog .preview-empty {
  text-align: center;
  color: #c0c4cc;
  margin-top: 100px;
}

/* 星空主题下的空状态 */
.theme-starry-sky .column-config-dialog .preview-empty,
body.theme-starry-sky .column-config-dialog .preview-empty {
  color: var(--base-color-3, #a0a0a0) !important;
}

/* 深色主题适配 */
.theme-dark h4 {
  color: var(--base-color-6, #b1b3b8);
}

.theme-starry-sky h4 {
  color: var(--theme-color, #ffffff);
}

/* 深色主题下的预览组件 */
.theme-dark .column-config-dialog .preview-container {
  background: var(--preview-bg, #2a3441);
  border-color: var(--border-color-1, #4c5565);
}

.theme-dark .column-config-dialog .preview-item {
  background: var(--base-menu-background, #363e4f);
  border-color: var(--border-color-1, #4c5565);
  color: var(--base-color-6, #e5eaf3);
}

.theme-dark .column-config-dialog .preview-empty {
  color: var(--base-color-5, #8b949e);
}

/* 导航按钮样式修复 */
.nav-button {
  color: var(--current-color, #409eff) !important;
  background-color: transparent !important;
  border-color: var(--current-color, #409eff) !important;
}

.nav-button:hover {
  color: #fff !important;
  background-color: var(--current-color, #409eff) !important;
  border-color: var(--current-color, #409eff) !important;
}

/* 深色主题下的导航按钮 */
.theme-dark .nav-button {
  color: var(--current-color, #3a7b99) !important;
  border-color: var(--current-color, #3a7b99) !important;
}

.theme-dark .nav-button:hover {
  color: #fff !important;
  background-color: var(--current-color, #3a7b99) !important;
  border-color: var(--current-color, #3a7b99) !important;
}

/* 星空主题下的导航按钮 */
.theme-starry-sky .nav-button {
  color: var(--current-color, #1e3a8a) !important;
  border-color: var(--current-color, #1e3a8a) !important;
}

.theme-starry-sky .nav-button:hover {
  color: #fff !important;
  background-color: var(--current-color, #1e3a8a) !important;
  border-color: var(--current-color, #1e3a8a) !important;
}
</style>

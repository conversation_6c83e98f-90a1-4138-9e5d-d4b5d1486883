<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工序编号" prop="procedureNumber">
        <el-input v-model="queryParams.procedureNumber" placeholder="请输入工序编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="工序名称" prop="procedureName">
        <el-input v-model="queryParams.procedureName" placeholder="请输入工序名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input v-model="queryParams.createBy" placeholder="请输入创建人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-setting" size="mini" @click="handleColumnConfig">字段配置</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="procedureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column 
        v-for="column in visibleColumns" 
        :key="column.prop" 
        :label="column.label" 
        :prop="column.prop" 
        :width="column.width"
        :min-width="column.minWidth"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="column.prop === 'isPrint'">
            <el-tag :type="scope.row.isPrint === 'Y' ? 'success' : 'info'" size="mini">
              {{ scope.row.isPrint === 'Y' ? '是' : '否' }}
            </el-tag>
          </span>
          <span v-else>
            {{ scope.row[column.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改工序对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="890px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="工序编号" prop="procedureNumber">
              <el-input v-model="form.procedureNumber" placeholder="请输入工序编号" style="width: 215px;"
                :disabled="title == '修改工序'" /> <el-button type="primary" size="mini" @click="generateProcedureCode"
                :disabled="title == '修改工序'">自动生成</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工序名称" prop="procedureName">
              <el-input v-model="form.procedureName" placeholder="请输入工序名称" style="width: 215px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="计划数默认值" prop="plannedQuantity">
              <el-input-number v-model="form.plannedQuantity" placeholder="请输入计划数默认值" :min="1" style="width: 215px;" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="报工数配比" prop="reportingRatio">
              <el-input-number v-model="form.reportingRatio" placeholder="请输入报工数配比" :min="1" style="width: 215px;" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="报工权限" prop="reportingAuthority">
              <el-select multiple v-model="userIds" placeholder="请选择" @change="handleUserChange">
                <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="不良品列表" prop="defectiveProducts">
              <el-select multiple v-model="defectIds" placeholder="请选择" @change="handleDefectChange">
                <el-option v-for="item in defectList" :key="item.defectId" :label="item.defectName"
                  :value="item.defectId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="工艺要求" prop="technologicalRequirements">
              <el-input v-model="form.technologicalRequirements" placeholder="请输入工艺要求" style="width: 215px;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否打印二维码" prop="isPrint">
              <el-select v-model="form.isPrint" placeholder="请选择是否打印二维码">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" style="width: 650px;" v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 工序导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的工序数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="columnConfigVisible" width="600px" append-to-body class="column-config-dialog">
      <div style="max-height: 400px; overflow-y: auto;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>可选字段</h4>
            <el-checkbox-group v-model="selectedColumns" @change="handleColumnChange">
              <div v-for="column in allColumns" :key="column.prop" style="margin-bottom: 8px;">
                <el-checkbox :label="column.prop" :disabled="column.required">
                  {{ column.label }}
                  <span v-if="column.required" style="color: var(--current-color, #f56c6c);">(必需)</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-col>
          <el-col :span="12">
            <h4>字段预览</h4>
            <div class="preview-container">
              <div v-for="column in previewColumns" :key="column.prop" class="preview-item">
                <span>{{ column.label }}</span>
                <span style="color: var(--base-color-2, #909399); font-size: 12px;">{{ column.width || 'auto' }}</span>
              </div>
              <div v-if="previewColumns.length === 0" class="preview-empty">
                请选择要显示的字段
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetColumnConfig">重置默认</el-button>
        <el-button type="primary" @click="saveColumnConfig">确 定</el-button>
        <el-button @click="columnConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProcedure,
  getProcedure,
  delProcedure,
  addProcedure,
  updateProcedure,
  importTemplate
} from "@/api/basicData/procedure";
import { listUser } from "@/api/system/user";
import { listDefect } from "@/api/basicData/defect";
import { getToken } from "@/utils/auth";
export default {
  name: "Procedure",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工序表格数据
      procedureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        procedureNumber: undefined,
        procedureName: undefined,
        createBy: undefined,
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        procedureId: [
          { required: true, message: "主键ID不能为空", trigger: "change" },
        ],
        procedureNumber: [
          { required: true, message: "工序编号不能为空", trigger: "change" },
        ],
        procedureName: [
          { required: true, message: "工序名称不能为空", trigger: "change" },
        ],
        // reportingAuthority: [
        //   { required: true, message: "报工权限不能为空", trigger: "change" },
        // ],
        plannedQuantity: [
          { required: true, message: "计划数默认值不能为空", trigger: "change" },
        ],
        // reportingRatio: [
        //   { required: true, message: "报工数配比不能为空", trigger: "change" },
        // ],
        // defectiveProducts: [
        //   { required: true, message: "不良品列表不能为空", trigger: "change" },
        // ],
        // checkIn: [
        //   { required: true, message: "任务打卡不能为空", trigger: "change" },
        // ],
        isPrint: [
          {
            required: true,
            message: "是否打印二维码不能为空",
            trigger: "change",
          },
        ],
        // collectionData: [
        //   { required: true, message: "工序采集数据不能为空", trigger: "blur" },
        // ],
      },
      userList: [], // 报工权限用户列表
      defectList: [], // 不良项列表
      userIds: [],
      defectIds: [],
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/procedure/importData"
      },
      // 字段配置
      columnConfigVisible: false,
      selectedColumns: [
        'reportingName', 
        'plannedQuantity', 
        'defectiveNames', 
        'createName', 
        'createTime'
      ],
      // 所有可选字段定义
      allColumns: [
        { prop: 'procedureNumber', label: '工序编号', required: true, width: 180 },
        { prop: 'procedureName', label: '工序名称', required: true, width: 'auto' },
        { prop: 'reportingName', label: '报工权限', required: false, width: 150 },
        { prop: 'plannedQuantity', label: '计划数默认值', required: false, width: 120 },
        // { prop: 'reportingRatio', label: '报工数配比', required: false, width: 120 },
        { prop: 'defectiveNames', label: '不良品项列表', required: false, minWidth: 150 },
        { prop: 'technologicalRequirements', label: '工艺要求', required: false, minWidth: 150 },
        { prop: 'isPrint', label: '是否打印二维码', required: false, width: 130 },
        // { prop: 'collectionData', label: '工序采集数据', required: false, minWidth: 150 },
        // { prop: 'checkIn', label: '任务打卡', required: false, width: 100 },
        { prop: 'createName', label: '创建人', required: false, width: 100 },
        { prop: 'createTime', label: '创建时间', required: false, width: 180 },
        { prop: 'updateTime', label: '更新时间', required: false, width: 180 },
        { prop: 'remark', label: '备注', required: false, minWidth: 150 }
      ],
    };
  },
  computed: {
    // 可见的列配置
    visibleColumns() {
      return this.allColumns.filter(column => 
        column.required || this.selectedColumns.includes(column.prop)
      );
    },
    // 预览的列配置
    previewColumns() {
      return this.visibleColumns;
    }
  },
  created() {
    this.initColumnConfig();
    this.$nextTick(() => {
      this.getList();
      this.getUserList();
      this.getDefectList();
    });
  },
  methods: {
    /** 查询工序列表 */
    getList() {
      this.loading = true;
      listProcedure(this.queryParams).then((response) => {
        this.procedureList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
        this.userIds = [],
        this.defectIds = [],
        this.form = {
          procedureId: undefined,
          procedureNumber: undefined,
          procedureName: undefined,
          reportingAuthority: '',
          plannedQuantity: 1,
          reportingRatio: 1,
          defectiveProducts: '',
          checkIn: undefined,
          technologicalRequirements: undefined,
          isPrint: 'N',
          collectionData: undefined,
          createBy: undefined,
          createTime: undefined,
          updateBy: undefined,
          updateTime: undefined,
          delFlag: undefined,
          remark: undefined,
          reportingName: '',
          defectiveNames: ''
        };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.procedureId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工序";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const procedureId = row.procedureId || this.ids;
      getProcedure(procedureId).then((response) => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改工序";
        
        // 确保在数据处理前用户列表和不良品列表已加载
        this.$nextTick(() => {
          this.processFormData();
        });
      });
    },
    
    /** 处理表单数据（用于修改时的数据回填） */
    processFormData() {
      // 处理报工权限数据
      if (this.form.reportingAuthority && this.form.reportingAuthority.trim() !== '') {
        if (this.form.reportingAuthority === 'all') {
          this.userIds = ['all'];
        } else {
          // 将字符串ID转换为匹配用户列表中的格式
          const authorityIds = this.form.reportingAuthority.split(",").filter(id => id.trim() !== '');
          
          if (authorityIds.length > 0) {
            this.userIds = authorityIds.map(id => {
              const trimmedId = id.trim();
              // 先尝试转换为数字类型匹配
              const numericId = parseInt(trimmedId);
              
              // 查找用户列表中是否有对应的用户
              const user = this.userList.find(u => {
                // 支持数字和字符串两种类型的匹配
                return u.userId === numericId || 
                       u.userId === trimmedId || 
                       u.userId.toString() === trimmedId;
              });
              
              return user ? user.userId : numericId;
            });
          } else {
            this.userIds = [];
          }
        }
      } else {
        this.userIds = [];
      }
      
      // 处理不良品数据
      if (this.form.defectiveProducts && this.form.defectiveProducts.trim() !== '') {
        const defectiveIds = this.form.defectiveProducts.split(",").filter(id => id.trim() !== '');
        
        if (defectiveIds.length > 0) {
          this.defectIds = defectiveIds.map(id => {
            const trimmedId = id.trim();
            // 先尝试转换为数字类型匹配
            const numericId = parseInt(trimmedId);
            
            // 查找不良品列表中是否有对应的不良品
            const defect = this.defectList.find(d => {
              return d.defectId === numericId || 
                     d.defectId === trimmedId || 
                     d.defectId.toString() === trimmedId;
            });
            
            return defect ? defect.defectId : numericId;
          });
        } else {
          this.defectIds = [];
        }
      } else {
        this.defectIds = [];
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.buttonLoading = true;
          
          // 处理报工权限
          var nickNames = '';
          if (this.userIds && this.userIds.length > 0) {
            this.userIds.forEach(e => {
              var user = this.userList.find(u => u.userId === e)
              if (user) {
                nickNames += user.nickName + ',';
              }
            })
            this.form.reportingName = nickNames.substring(0, nickNames.length - 1);
            this.form.reportingAuthority = this.userIds.join(',');
          } else {
            this.form.reportingName = '';
            this.form.reportingAuthority = '';
          }
          
          // 处理不良品数据
          var defectnames = '';
          if (this.defectIds && this.defectIds.length > 0) {
            this.defectIds.forEach(e => {
              var defect = this.defectList.find(d => d.defectId == e)
              if (defect) {
                defectnames += defect.defectName + ',';
              }
            })
            this.form.defectiveNames = defectnames.substring(0, defectnames.length - 1);
            this.form.defectiveProducts = this.defectIds.join(',');
          } else {
            this.form.defectiveNames = '';
            this.form.defectiveProducts = '';
          }
          if (this.form.procedureId != null) {
            updateProcedure(this.form)
              .then((response) => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.buttonLoading = false;
              });
          } else {
            addProcedure(this.form)
              .then((response) => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.buttonLoading = false;
              });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const procedureIds = row.procedureId || this.ids;
      this.$modal
        .confirm('是否确认删除工序编号为"' + procedureIds + '"的数据项？')
        .then(() => {
          this.loading = true;
          return delProcedure(procedureIds);
        })
        .then(() => {
          this.loading = false;
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "工序导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/procedure/importTemplate', {
      }, `procedure_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/procedure/export",
        {
          ...this.queryParams,
        },
        `procedure_${new Date().getTime()}.xlsx`
      );
    },
    // 工序编号生成：GX + 时间戳
    generateProcedureCode() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const hour = String(currentDate.getHours()).padStart(2, '0');
      const minute = String(currentDate.getMinutes()).padStart(2, '0');
      const second = String(currentDate.getSeconds()).padStart(2, '0');
      const timestamp = String(currentDate.getTime()).substring(10);
      this.form.procedureNumber = `GX${year}${month}${day}${hour}${minute}${second}${timestamp}`
    },
    // 获取报工权限用户列表
    getUserList() {
      listUser().then(res => {
        this.userList = res.rows;
        this.userList.unshift({ userId: 'all', nickName: '不限制' })
      });
    },
    // 获取不良项列表
    getDefectList() {
      listDefect().then(res => {
        this.defectList = res.rows;
      })
    },
    // 报工权限修改
    handleUserChange(value) {
      if (value && value.length > 1) {
        if (value[value.length - 1] === 'all') {
          this.userIds = [value[value.length - 1]]
        } else if (value[0] === 'all') {
          this.userIds = value.filter(item => item !== value[0]);
        }
      }
      this.form.reportingAuthority = this.userIds && this.userIds.length > 0 ? this.userIds.join(',') : '';
    },
    handleDefectChange() {
      this.form.defectiveProducts = this.defectIds && this.defectIds.length > 0 ? this.defectIds.join(',') : '';
    },
    handleRadio(val) {
      var radioTypes = [];
      val.split("~~").forEach(item => {
        radioTypes.push(JSON.parse(item));
      })
      return radioTypes;
    },
    splitArrayIntoChunks(array, chunkSize) {
      const result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        result.push(array.slice(i, i + chunkSize));
      }
      return result;
    },
    
    // ============= 字段配置相关方法 =============
    
    /** 初始化字段配置 */
    initColumnConfig() {
      const savedConfig = localStorage.getItem('procedure_column_config');
      if (savedConfig) {
        this.selectedColumns = JSON.parse(savedConfig);
      } else {
        // 默认显示的字段
        this.selectedColumns = [
          'reportingName', 
          'plannedQuantity', 
          'defectiveNames', 
          'createName', 
          'createTime'
        ];
      }
    },
    
    /** 字段配置按钮操作 */
    handleColumnConfig() {
      this.columnConfigVisible = true;
    },
    
    /** 字段变化处理 */
    handleColumnChange(value) {
      // 这里可以添加实时预览逻辑
    },
    
    /** 重置为默认配置 */
    resetColumnConfig() {
      this.selectedColumns = [
        'reportingName', 
        'plannedQuantity', 
        'defectiveNames', 
        'createName', 
        'createTime'
      ];
    },
    
    /** 保存字段配置 */
    saveColumnConfig() {
      // 保存到本地存储
      localStorage.setItem('procedure_column_config', JSON.stringify(this.selectedColumns));
      this.columnConfigVisible = false;
      this.$modal.msgSuccess("字段配置已保存");
    }
  },
};
</script>

<style scoped>
/* 字段配置对话框样式 */
.el-checkbox-group .el-checkbox {
  width: 100%;
  margin-right: 0;
}

.el-checkbox-group .el-checkbox + .el-checkbox {
  margin-left: 0;
}

/* 表格列宽度优化 */
.el-table .cell {
  word-break: break-word;
}

/* 字段预览样式 */
h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--base-color-1, #303133);
  font-weight: 500;
}

/* 字段配置对话框基础样式 */
.column-config-dialog .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
}

.column-config-dialog .el-checkbox:hover .el-checkbox__inner {
  border-color: var(--current-color, #409eff);
}

/* 字段配置预览区域基础样式 */
.column-config-dialog .preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
  background-color: #f5f7fa;
}

/* 星空主题下的预览容器 */
.theme-starry-sky .column-config-dialog .preview-container,
body.theme-starry-sky .column-config-dialog .preview-container {
  background-color: var(--base-item-bg, rgba(26, 31, 60, 0.9)) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
}

.column-config-dialog .preview-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #fff;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e4e7ed;
}

/* 星空主题下的预览项 */
.theme-starry-sky .column-config-dialog .preview-item,
body.theme-starry-sky .column-config-dialog .preview-item {
  background: var(--base-main-bg, #0b0d1a) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
  color: var(--theme-color, #ffffff) !important;
}

.column-config-dialog .preview-empty {
  text-align: center;
  color: #c0c4cc;
  margin-top: 100px;
}

/* 星空主题下的空状态 */
.theme-starry-sky .column-config-dialog .preview-empty,
body.theme-starry-sky .column-config-dialog .preview-empty {
  color: var(--base-color-3, #a0a0a0) !important;
}

/* 深色主题适配 */
.theme-dark h4 {
  color: var(--base-color-6, #b1b3b8);
}

.theme-starry-sky h4 {
  color: var(--theme-color, #ffffff);
}

/* 深色主题下的预览组件 */
.theme-dark .column-config-dialog .preview-container {
  background: var(--preview-bg, #2a3441);
  border-color: var(--border-color-1, #4c5565);
}

.theme-dark .column-config-dialog .preview-item {
  background: var(--base-menu-background, #363e4f);
  border-color: var(--border-color-1, #4c5565);
  color: var(--base-color-6, #e5eaf3);
}

.theme-dark .column-config-dialog .preview-empty {
  color: var(--base-color-5, #8b949e);
}
</style>
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="系列名称" prop="seriesName">
        <el-input v-model="queryParams.seriesName" placeholder="请输入系列名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:seriesProducts:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:seriesProducts:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:seriesProducts:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:seriesProducts:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="seriesProductsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="系列ID" align="center" prop="id" width="80" />
      <el-table-column label="系列名称" align="center" prop="seriesName" />
      <el-table-column label="创建人" align="center" prop="createBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" align="center" prop="updateBy" width="100" />
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:seriesProducts:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:seriesProducts:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改系列产品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="系列名称" prop="seriesName">
          <el-input v-model="form.seriesName" placeholder="请输入系列名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input 
            type="textarea" 
            v-model="form.remarks" 
            placeholder="请输入备注" 
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="buttonLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSeriesProducts, getSeriesProducts, delSeriesProducts, addSeriesProducts, updateSeriesProducts, exportSeriesProducts } from "@/api/basicData/seriesProducts";

export default {
  name: "SeriesProducts",
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系列产品表格数据
      seriesProductsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        seriesName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        seriesName: [
          { required: true, message: "系列名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询系列产品列表 */
    getList() {
      this.loading = true;
      listSeriesProducts(this.queryParams).then(response => {
        this.seriesProductsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        seriesName: null,
        remarks: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加系列产品";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSeriesProducts(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改系列产品";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          if (this.form.id != null) {
            updateSeriesProducts(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addSeriesProducts(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除系列产品编号为"' + ids + '"的数据项？').then(function() {
        return delSeriesProducts(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/seriesProducts/export', {
        ...this.queryParams
      }, `seriesProducts_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.app-container {
  background-color: var(--base-bg-color, #fff);
  color: var(--base-text-color, #333);
}

.el-table {
  background-color: var(--base-bg-color, #fff);
}

.el-table th {
  background-color: var(--table-header-bg, #f5f7fa);
  color: var(--table-header-text, #333);
}

.el-table td {
  border-bottom: 1px solid var(--border-color, #ebeef5);
  color: var(--base-text-color, #333);
}

.el-dialog {
  background-color: var(--dialog-bg-color, #fff);
}

.el-dialog__header {
  background-color: var(--dialog-header-bg, #f5f7fa);
  color: var(--dialog-header-text, #333);
}

.el-form-item__label {
  color: var(--form-label-color, #606266);
}

.el-input__inner {
  background-color: var(--input-bg-color, #fff);
  border-color: var(--input-border-color, #dcdfe6);
  color: var(--input-text-color, #606266);
}

.el-button {
  border-color: var(--button-border-color, #dcdfe6);
}

.el-button--primary {
  background-color: var(--primary-color, #409eff);
  border-color: var(--primary-color, #409eff);
}

/* 弹窗按钮样式修复 */
.dialog-footer {
  text-align: right;
  padding: 20px 20px 0;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.dialog-footer .el-button--primary {
  background-color: var(--current-color, #409eff) !important;
  border-color: var(--current-color, #409eff) !important;
  color: #fff !important;
}

.dialog-footer .el-button--primary:hover {
  background-color: var(--color-2, #66b1ff) !important;
  border-color: var(--color-2, #66b1ff) !important;
}

/* 深色主题下的弹窗按钮 */
.theme-dark .dialog-footer .el-button--primary {
  background-color: var(--current-color, #3a7b99) !important;
  border-color: var(--current-color, #3a7b99) !important;
}

.theme-dark .dialog-footer .el-button--primary:hover {
  background-color: var(--color-2, #70afce) !important;
  border-color: var(--color-2, #70afce) !important;
}

/* 星空主题下的弹窗按钮 */
.theme-starry-sky .dialog-footer .el-button--primary {
  background-color: var(--current-color, #1e3a8a) !important;
  border-color: var(--current-color, #1e3a8a) !important;
}

.theme-starry-sky .dialog-footer .el-button--primary:hover {
  background-color: var(--color-2, #3b82f6) !important;
  border-color: var(--color-2, #3b82f6) !important;
}
</style>

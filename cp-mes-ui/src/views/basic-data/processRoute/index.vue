<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="工艺路线编号" prop="processRouteNumber">
        <el-input v-model="queryParams.processRouteNumber" placeholder="请输入工艺路线编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="工艺路线名称" prop="processRouteName">
        <el-input v-model="queryParams.processRouteName" placeholder="请输入工艺路线名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-setting" size="mini" @click="handleColumnConfig">字段配置</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="processRouteList" 
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      row-key="processRouteId"
      :expand-row-keys="expandedRows"
      @expand-change="handleExpandChange"
      row-class-name="clickable-row"
    >
      <el-table-column type="expand" width="55" align="center">
        <template slot="header">
          <div class="expand-header-tip" title="点击行可展开查看工序详情">
            <i class="el-icon-view" style="color: #909399;"></i>
          </div>
        </template>
        <template slot-scope="props">
          <div class="expand-content">
            <div class="expand-header">
              <div class="route-summary">
                <h4 class="route-title">
                  <i class="el-icon-connection"></i>
                  {{ props.row.processRouteName }} - 工序详情
                </h4>
                <p class="route-desc">工艺路线编号：{{ props.row.processRouteNumber }}</p>
              </div>
              <div class="procedure-count">
                <el-tag type="primary" size="small">
                  共 {{ (expandData[props.row.processRouteId] || []).length }} 道工序
                </el-tag>
              </div>
            </div>
            
            <div class="procedures-container">
              <div v-loading="expandLoading[props.row.processRouteId]" class="procedures-loading">
                <div v-if="expandData[props.row.processRouteId] && expandData[props.row.processRouteId].length" 
                     class="procedures-grid">
                  <div 
                    v-for="(procedure, index) in expandData[props.row.processRouteId]" 
                    :key="procedure.procedureId"
                    class="procedure-card"
                  >
                    <div class="procedure-header">
                      <div class="procedure-index">{{ index + 1 }}</div>
                      <div class="procedure-info">
                        <h5 class="procedure-name">{{ procedure.procedureName }}</h5>
                        <span class="procedure-number">{{ procedure.procedureNumber }}</span>
                      </div>
                      <div class="procedure-status">
                        <el-tag :type="procedure.isPrint === 'Y' ? 'success' : 'info'" size="mini">
                          {{ procedure.isPrint === 'Y' ? '打印二维码' : '无需打印' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="procedure-body" v-if="procedure.technologicalRequirements">
                      <div class="tech-requirements">
                        <i class="el-icon-document"></i>
                        <span>工艺要求：{{ procedure.technologicalRequirements }}</span>
                      </div>
                    </div>
                    <div class="procedure-arrow" v-if="index < expandData[props.row.processRouteId].length - 1">
                      <i class="el-icon-right"></i>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="!expandLoading[props.row.processRouteId]" class="empty-procedures">
                  <i class="el-icon-box"></i>
                  <p>该工艺路线暂无工序配置</p>
                  <span>请在编辑时添加工序</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column 
        v-for="column in visibleColumns" 
        :key="column.prop" 
        :label="column.label" 
        :prop="column.prop" 
        :width="column.width"
        :min-width="column.minWidth"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row[column.prop] }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewProcedures(scope.row)">查看工序</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />


    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="工艺路线编号" prop="processRouteNumber">
          <el-input v-model="form.processRouteNumber" placeholder="请输入工艺路线编号" style="width: 250px;"
            :disabled="title == '修改工艺路线'" /> <el-button type="primary" size="mini" @click="generateCode"
            :disabled="title == '修改工艺路线'">自动生成</el-button>
        </el-form-item>
        <el-form-item label="工艺路线名称" prop="processRouteName">
          <el-input v-model="form.processRouteName" style="width: 250px;" placeholder="请输入工艺路线名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="form.remark" style="width: 250px;" placeholder="请输入备注" />
        </el-form-item>
        <div>
          <el-form-item label="工序" prop="procedureIds">
            <el-button type="primary" icon="el-icon-plus" style="margin-bottom: 8px;"
              @click="addProcedure">新增工序</el-button>
            <div>
              <el-table v-loading="paramLoading" :data="bindingProcedure" ref="sortableTable" row-key="procedureId">
                <el-table-column type="index" width="55" align="center">
                  <svg-icon class="drag-icon" :icon-class="'drag-icon'" />
                </el-table-column>
                <el-table-column label="工序" prop="procedureId" align="center">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.procedureId" placeholder="请选择工序" @change="procedureChange">
                      <el-option v-for="item in procedureList" :key="item.procedureId" :label="item.procedureName"
                        :value="item.procedureId"></el-option>
                    </el-select>
                  </template>
                </el-table-column>

                <!-- <el-table-column label="报工数配比" prop="reportingRatio" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.reportingRatio" placeholder="请输入" :controls="false" :disabled="true"
                      style="width: 100%;"></el-input>
                  </template>
                </el-table-column> -->
                <el-table-column label="工艺要求" prop="technologicalRequirements" align="center">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.technologicalRequirements" placeholder="请输入" :controls="false"
                      :disabled="true" style="width: 100%;"></el-input>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="是否打印下料单" prop="isPrint" align="center">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.isPrint" placeholder="请选择是否打印下料单" :disabled="true">
                      <el-option v-for="dict in dict.type.is_print" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                    </el-select>
                  </template>
                </el-table-column> -->
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-delete"
                      @click="delectParam(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看工序对话框 -->
    <el-dialog title="工艺路线详情" :visible.sync="procedureDialogVisible" width="800px" append-to-body>
      <div v-if="currentProcessRoute">
        <div class="process-route-info">
          <h3>{{ currentProcessRoute.processRouteName }}</h3>
          <p>工艺路线编号: {{ currentProcessRoute.processRouteNumber }}</p>
        </div>
        <el-divider content-position="left">工序列表</el-divider>
        <el-table v-loading="procedureLoading" :data="currentProcedureList" style="width: 100%">
          <el-table-column type="index" label="序号" width="50" align="center" />
          <el-table-column label="工序编号" prop="procedureNumber" align="center" />
          <el-table-column label="工序名称" prop="procedureName" align="center" />
          <el-table-column label="工艺要求" prop="technologicalRequirements" align="center" />
          <el-table-column label="是否打印二维码" prop="isPrint" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isPrint === 'Y' ? 'success' : 'info'" size="mini">
                {{ scope.row.isPrint === 'Y' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="currentProcedureList.length === 0" class="empty-data">
          <el-empty description="暂无工序数据"></el-empty>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="procedureDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="columnConfigVisible" width="600px" append-to-body class="column-config-dialog">
      <div style="max-height: 400px; overflow-y: auto;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>可选字段</h4>
            <el-checkbox-group v-model="selectedColumns" @change="handleColumnChange">
              <div v-for="column in allColumns" :key="column.prop" style="margin-bottom: 8px;">
                <el-checkbox :label="column.prop" :disabled="column.required">
                  {{ column.label }}
                  <span v-if="column.required" style="color: var(--current-color, #f56c6c);">(必需)</span>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </el-col>
          <el-col :span="12">
            <h4>字段预览</h4>
            <div class="preview-container">
              <div v-for="column in previewColumns" :key="column.prop" class="preview-item">
                <span>{{ column.label }}</span>
                <span style="color: var(--base-color-2, #909399); font-size: 12px;">{{ column.width || 'auto' }}</span>
              </div>
              <div v-if="previewColumns.length === 0" class="preview-empty">
                请选择要显示的字段
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetColumnConfig">重置默认</el-button>
        <el-button type="primary" @click="saveColumnConfig">确 定</el-button>
        <el-button @click="columnConfigVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import { listProcessRoute, getProcessRoute, delProcessRoute, addProcessRoute, updateProcessRoute } from "@/api/basicData/processRoute";
import { listProcedure, getProcedure } from "@/api/basicData/procedure";
import Sortable from 'sortablejs';
export default {
  name: "ProcessRoute",
  components: {
    draggable,
  },
  dicts: ['is_print'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      paramLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工艺路线表格数据
      processRouteList: [],
      // 工序表格数据
      procedureList: [],
      //绑定工序表格数据
      bindingProcedure: [],
      //选中的工序
      currentProcedure: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        processRouteNumber: undefined,
        processRouteName: undefined,
        procedureIds: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        processRouteId: [
          { required: true, message: "主键ID不能为空", trigger: "blur" }
        ],
        processRouteNumber: [
          { required: true, message: "工艺路线编号不能为空", trigger: "blur" }
        ],
        processRouteName: [
          { required: true, message: "工艺路线名称不能为空", trigger: "blur" }
        ],
        /* procedureIds: [
          { required: true, message: "工序不能为空", trigger: "blur" }
        ],
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" }
        ] */
      },
      // 查看工序对话框相关数据
      procedureDialogVisible: false,
      procedureLoading: false,
      currentProcessRoute: null,
      currentProcedureList: [],
      // 展开行相关数据
      expandedRows: [],
      expandData: {}, // 存储每个工艺路线的工序数据
      expandLoading: {}, // 存储每个工艺路线的加载状态
      // 字段配置相关
      columnConfigVisible: false,
      selectedColumns: [
        'createName', 
        'createTime'
      ],
      // 所有可选字段定义
      allColumns: [
        { prop: 'processRouteNumber', label: '工艺路线编号', required: true, width: 180 },
        { prop: 'processRouteName', label: '工艺路线名称', required: true, width: 'auto' },
        { prop: 'createName', label: '创建人', required: false, width: 120 },
        { prop: 'createTime', label: '创建时间', required: false, width: 180 },
        { prop: 'updateTime', label: '更新时间', required: false, width: 180 },
        { prop: 'remark', label: '备注', required: false, minWidth: 150 }
      ]
    };
  },
  computed: {
    // 可见的列配置
    visibleColumns() {
      return this.allColumns.filter(column => 
        column.required || this.selectedColumns.includes(column.prop)
      );
    },
    // 预览的列配置
    previewColumns() {
      return this.visibleColumns;
    }
  },
  created() {
    this.initColumnConfig();
    this.getProcedureList();
    this.getList();
  },
  watch: {
    open(val) {
      if (val) {
        this.$nextTick(() => {
          this.initSortable();
        })
      }
    }
  },
  methods: {
    initSortable() {
      const tableBody = this.$refs.sortableTable.$el.querySelector('.el-table__body-wrapper tbody');
      Sortable.create(tableBody, {
        animation: 150,
        onEnd: evt => {
          const item = this.bindingProcedure.splice(evt.oldIndex, 1)[0];
          this.bindingProcedure.splice(evt.newIndex, 0, item);
        }
      });
    },
    // 编号生成：GY + 时间戳
    generateCode() {
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const hour = String(currentDate.getHours()).padStart(2, '0');
      const minute = String(currentDate.getMinutes()).padStart(2, '0');
      const second = String(currentDate.getSeconds()).padStart(2, '0');
      const timestamp = String(currentDate.getTime()).substring(10);
      this.form.processRouteNumber = `GY${year}${month}${day}${hour}${minute}${second}${timestamp}`
    },
    /** 查询工艺路线列表 */
    getList() {
      this.loading = true;
      listProcessRoute(this.queryParams).then(response => {
        this.processRouteList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询工序列表 */
    getProcedureList() {
      this.loading = true;
      listProcedure().then(response => {
        this.procedureList = response.rows;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.bindingProcedure = [],
        this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        processRouteId: undefined,
        processRouteNumber: undefined,
        processRouteName: undefined,
        procedureIds: undefined,
        procedureNames: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        delFlag: undefined,
        remark: undefined,
        procedureList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.processRouteId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.bindingProcedure = [],
        //this.form.processRouteNumber = this.generateCode();
        this.open = true;
      this.title = "添加工艺路线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      this.bindingProcedure = [];
      const processRouteId = row.processRouteId || this.ids
      getProcessRoute(processRouteId).then(response => {
        this.loading = false;
        this.form = response.data;
        if (this.form.procedureList) {
          this.bindingProcedure = this.form.procedureList;
        }
        this.open = true;
        this.title = "修改工艺路线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          var procedureIds = '';
          var procedureNames = '';
          this.bindingProcedure.forEach(e => {
            if (e.procedureId != '') {
              procedureIds += e.procedureId + ',';
              procedureNames += e.procedureName + ',';
            }
          });
          this.form.procedureIds = procedureIds.substring(0, procedureIds.length - 1)
          this.form.procedureNames = procedureNames.substring(0, procedureNames.length - 1)
          if (this.form.processRouteId != null) {
            updateProcessRoute(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addProcessRoute(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const processRouteIds = row.processRouteId || this.ids;
      this.$modal.confirm('是否确认删除工艺路线编号为"' + processRouteIds + '"的数据项？').then(() => {
        this.loading = true;
        return delProcessRoute(processRouteIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/processRoute/export', {
        ...this.queryParams
      }, `processRoute_${new Date().getTime()}.xlsx`)
    },

    /** 行点击处理 */
    handleRowClick(row, column, event) {
      // 如果点击的是操作列，不触发展开
      if (column && (column.label === '操作' || column.type === 'selection')) {
        return;
      }
      
      // 切换展开状态
      this.toggleExpand(row);
    },

    /** 切换展开状态 */
    toggleExpand(row) {
      const index = this.expandedRows.indexOf(row.processRouteId);
      if (index > -1) {
        // 当前已展开，收起
        this.expandedRows.splice(index, 1);
      } else {
        // 当前未展开，展开
        this.expandedRows.push(row.processRouteId);
        this.loadProcedureData(row);
      }
    },

    /** 展开行处理 */
    handleExpandChange(row, expandedRows) {
      const isExpanded = expandedRows.includes(row);
      if (isExpanded) {
        // 展开时加载工序数据
        this.loadProcedureData(row);
      }
    },

    /** 加载工序数据 */
    async loadProcedureData(processRoute) {
      const routeId = processRoute.processRouteId;
      
      // 如果已经加载过数据，直接返回
      if (this.expandData[routeId]) {
        return;
      }

      // 设置加载状态
      this.$set(this.expandLoading, routeId, true);

      try {
        // 获取工艺路线详情，包含工序列表
        const response = await getProcessRoute(routeId);
        const procedureList = response.data.procedureList || [];
        
        // 为每个工序获取详细信息
        const procedureDetails = await Promise.all(
          procedureList.map(async (procedure) => {
            try {
              const detailResponse = await getProcedure(procedure.procedureId);
              return detailResponse.data;
            } catch (error) {
              console.error(`获取工序详情失败: ${procedure.procedureId}`, error);
              return procedure;
            }
          })
        );

        // 存储工序数据
        this.$set(this.expandData, routeId, procedureDetails);
      } catch (error) {
        console.error('加载工序数据失败:', error);
        this.$modal.msgError('加载工序数据失败');
        this.$set(this.expandData, routeId, []);
      } finally {
        // 取消加载状态
        this.$set(this.expandLoading, routeId, false);
      }
    },
    // 价格参数新增按钮
    addProcedure() {
      var paramItem = {
        procedureId: "",
        procedureName: "",
        reportingRatio: "",
        technologicalRequirements: "",
        isPrint: ""
      }
      this.bindingProcedure.push(paramItem)
    },
    // 价格参数删除按钮
    delectParam(row) {
      let index = this.bindingProcedure.findIndex(p => p.procedureId == row.procedureId)
      if (index < 0) return
      this.bindingProcedure.splice(index, 1)
    },
    procedureChange(data) {
      getProcedure(data).then(response => {
        this.currentProcedure = response.data;
        this.bindingProcedure.forEach(e => {
          if (e.procedureId === data) {
            e.reportingRatio = this.currentProcedure.reportingRatio;
            e.technologicalRequirements = this.currentProcedure.technologicalRequirements;
            e.isPrint = this.currentProcedure.isPrint;
            e.procedureName = this.currentProcedure.procedureName;
          }
        })
      });
    },
    /** 查看工序按钮操作 */
    handleViewProcedures(row) {
      this.procedureLoading = true;
      this.procedureDialogVisible = true;
      this.currentProcessRoute = row;
      this.currentProcedureList = [];
      
      // 获取工艺路线详情，包含工序列表
      getProcessRoute(row.processRouteId).then(response => {
        this.procedureLoading = false;
        if (response.data && response.data.procedureList) {
          this.currentProcedureList = response.data.procedureList;
        }
      }).catch(() => {
        this.procedureLoading = false;
      });
    },

    // ============= 字段配置相关方法 =============
    
    /** 初始化字段配置 */
    initColumnConfig() {
      const savedConfig = localStorage.getItem('processRoute_column_config');
      if (savedConfig) {
        this.selectedColumns = JSON.parse(savedConfig);
      } else {
        // 默认显示的字段
        this.selectedColumns = [
          'createName', 
          'createTime'
        ];
      }
    },
    
    /** 字段配置按钮操作 */
    handleColumnConfig() {
      this.columnConfigVisible = true;
    },
    
    /** 字段变化处理 */
    handleColumnChange(value) {
      // 这里可以添加实时预览逻辑
    },
    
    /** 重置为默认配置 */
    resetColumnConfig() {
      this.selectedColumns = [
        'createName', 
        'createTime'
      ];
    },
    
    /** 保存字段配置 */
    saveColumnConfig() {
      // 保存到本地存储
      localStorage.setItem('processRoute_column_config', JSON.stringify(this.selectedColumns));
      this.columnConfigVisible = false;
      this.$modal.msgSuccess("字段配置已保存");
    }
  }
};
</script>

<style scoped>
/*
  全局修复表格展开行白色背景问题
  适配所有主题（浅色、深色、星空主题）
*/
::v-deep .el-table__expanded-cell {
  background-color: transparent !important;
}

/* 针对不同主题的额外保障 */
.theme-dark ::v-deep .el-table__expanded-cell,
.theme-starry-sky ::v-deep .el-table__expanded-cell,
.theme-light ::v-deep .el-table__expanded-cell {
  background-color: transparent !important;
}

/* 展开内容样式 */
.expand-content {
  padding: 0;
  background: linear-gradient(135deg, var(--base-menu-background, #f8f9fa) 0%, var(--base-sub-menu-background, #e9ecef) 100%);
  border-radius: 8px;
  margin: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 深色主题和星空主题下强制覆盖展开内容背景 - 最高优先级 */
.theme-dark .expand-content,
.theme-dark .el-table td .expand-content,
.theme-dark .el-table__row .expand-content {
  background: #2a3950 !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3) !important;
}

.theme-starry-sky .expand-content,
.theme-starry-sky .el-table td .expand-content,
.theme-starry-sky .el-table__row .expand-content {
  background: #0b0d1a !important;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.4) !important;
}

/* 使用深度选择器强制覆盖白色背景 - 增强主题适配 */
.theme-dark ::v-deep .expand-content {
  background: #2a3950 !important;
}

.theme-starry-sky ::v-deep .expand-content {
  background: #0b0d1a !important;
}

.theme-light ::v-deep .expand-content {
  background: linear-gradient(135deg, var(--base-menu-background, #f8f9fa) 0%, var(--base-sub-menu-background, #e9ecef) 100%) !important;
}

.theme-dark ::v-deep .procedures-container {
  background: #46576e !important;
}

.theme-starry-sky ::v-deep .procedures-container {
  background: #0b0d1a !important;
}

.theme-light ::v-deep .procedures-container {
  background: var(--base-main-bg, white) !important;
}

.theme-dark ::v-deep .procedure-card {
  background: #2c3d55 !important;
  border-color: rgba(58, 123, 153, 0.6) !important;
}

.theme-starry-sky ::v-deep .procedure-card {
  background: #1a1f3c !important;
  border-color: rgba(30, 58, 138, 0.8) !important;
}

.theme-light ::v-deep .procedure-card {
  background: var(--base-item-bg, white) !important;
  border-color: var(--border-color-1, #e1e8ed) !important;
}

/* 展开头部样式 - 完整主题适配 */
.expand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: linear-gradient(135deg, var(--current-color, #409eff) 0%, #66b3ff 100%);
  color: white;
  border-radius: 8px 8px 0 0;
  position: relative;
  overflow: hidden;
}

/* 浅色主题头部样式 - 优化展示效果 */
.theme-light .expand-header {
  background: linear-gradient(135deg, var(--current-color, #3671e8) 0%, #66b3ff 60%, #74c2ff 100%) !important;
  color: white !important;
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.25), 0 2px 8px rgba(54, 113, 232, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  position: relative;
}

/* 浅色主题头部高光效果 */
.theme-light .expand-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
  pointer-events: none;
}

/* 浅色主题头部内容优化 */
.theme-light .expand-header .route-title {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  font-weight: 700;
}

.theme-light .expand-header .route-desc {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 500;
}

.theme-light .expand-header .route-title i {
  color: rgba(255, 255, 255, 0.95) !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* 深色主题头部样式 - 使用主题色彩 */
.theme-dark .expand-header {
  background: linear-gradient(135deg, var(--current-color, #3a7b99) 0%, var(--color-2, #70afce) 100%) !important;
  color: var(--theme-color, white) !important;
  box-shadow: 0 2px 12px rgba(58, 123, 153, 0.4) !important;
  border: 1px solid rgba(58, 123, 153, 0.3) !important;
}

/* 星空主题头部样式 - 特殊星空效果 */
.theme-starry-sky .expand-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%) !important;
  color: white !important;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.6) !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  position: relative;
}

/* 星空主题特殊效果 - 添加闪烁效果 */
.theme-starry-sky .expand-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 80% 40%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.06) 1px, transparent 1px);
  background-size: 50px 50px, 80px 80px, 120px 120px;
  animation: starryAnimation 20s linear infinite;
  pointer-events: none;
  z-index: 1;
}

/* 确保头部内容在星空效果之上 */
.theme-starry-sky .expand-header > * {
  position: relative;
  z-index: 2;
}

@keyframes starryAnimation {
  0% { opacity: 0.3; }
  50% { opacity: 0.7; }
  100% { opacity: 0.3; }
}

.route-summary {
  flex: 1;
}

.route-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.route-title i {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.route-desc {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.procedure-count {
  /* padding: 8px 0; */
  padding-top: px;
padding-bottom: 8px;
padding-left: 0;
padding-right: 0;
}

/* 工序数量标签 - 基础样式 */
.procedure-count .el-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  height: auto;
  line-height: auto;
}

/* 浅色主题工序数量标签 - 优化视觉效果 */
.theme-light .procedure-count .el-tag {
  background: rgba(255, 255, 255, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 6px;
  backdrop-filter: blur(15px);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 浅色主题头部渐变优化 */
.theme-light .expand-header {
  background: linear-gradient(135deg, var(--current-color, #3671e8) 0%, #4f8aef 30%, #66b3ff 70%, #74c2ff 100%) !important;
  transition: all 0.3s ease;
}

/* 浅色主题头部悬停效果 */
.theme-light .expand-header:hover {
  box-shadow: 0 6px 20px rgba(54, 113, 232, 0.3), 0 3px 12px rgba(54, 113, 232, 0.2) !important;
  transform: translateY(-1px);
}

/* 浅色主题展开内容优化 */
.theme-light .expand-content {
  background: linear-gradient(135deg, var(--base-menu-background, #f8f9fa) 0%, var(--base-sub-menu-background, #e9ecef) 50%, #f1f3f5 100%) !important;
  border: 1px solid rgba(54, 113, 232, 0.1) !important;
}

/* 浅色主题工序卡片在展开内容中的优化 */
.theme-light .expand-content .procedure-card {
  background: var(--base-item-bg, white) !important;
  border: 2px solid rgba(54, 113, 232, 0.15) !important;
  box-shadow: 0 2px 12px rgba(54, 113, 232, 0.08), 0 1px 4px rgba(0, 0, 0, 0.05) !important;
}

.theme-light .expand-content .procedure-card:hover {
  border-color: var(--current-color, #3671e8) !important;
  box-shadow: 0 4px 20px rgba(54, 113, 232, 0.15), 0 2px 8px rgba(54, 113, 232, 0.1) !important;
  transform: translateY(-2px);
}

/* 深色主题工序数量标签 */
.theme-dark .procedure-count .el-tag {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  color: var(--theme-color, white) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 8px rgba(58, 123, 153, 0.3);
}

/* 星空主题工序数量标签 */
.theme-starry-sky .procedure-count .el-tag {
  background: rgba(59, 130, 246, 0.3) !important;
  border: 1px solid rgba(59, 130, 246, 0.5) !important;
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  box-shadow: 0 2px 12px rgba(30, 58, 138, 0.4);
}

/* 工序容器样式 */
.procedures-container {
  padding: 25px;
  background: var(--base-main-bg, white);
}

/* 深色主题和星空主题下强制覆盖工序容器背景 - 最高优先级 */
.theme-dark .procedures-container {
  background: #46576e !important;
}

.theme-starry-sky .procedures-container {
  background: #0b0d1a !important;
}

.procedures-loading {
  min-height: 120px;
}

/* 工序网格布局 */
.procedures-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
  position: relative;
}

/* 工序卡片样式 */
.procedure-card {
  flex: 0 0 calc(33.333% - 14px);
  min-width: 280px;
  background: var(--base-item-bg, white);
  border: 2px solid var(--border-color-1, #e1e8ed);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 深色主题和星空主题下强制覆盖工序卡片背景 - 最高优先级 */
.theme-dark .procedure-card {
  background: #2c3d55 !important;
  border-color: rgba(58, 123, 153, 0.6) !important;
  box-shadow: 0 4px 12px rgba(58, 123, 153, 0.25) !important;
}

.theme-starry-sky .procedure-card {
  background: #1a1f3c !important;
  border-color: rgba(30, 58, 138, 0.8) !important;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.35) !important;
}

.procedure-card:hover {
  border-color: var(--current-color, #409eff);
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.procedure-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.procedure-index {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--theme-color, #409eff) 0%, #66b3ff 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.procedure-info {
  flex: 1;
  min-width: 0;
}

.procedure-name {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1, #2c3e50);
  line-height: 1.3;
  word-break: break-word;
}

.procedure-number {
  font-size: 13px;
  color: var(--base-color-3, #6c757d);
  background: var(--base-color-9, #f8f9fa);
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.procedure-status {
  flex-shrink: 0;
}

.procedure-body {
  border-top: 1px solid var(--border-color-1, #e9ecef);
  padding-top: 15px;
}

.tech-requirements {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: var(--base-color-2, #495057);
  line-height: 1.5;
}

.tech-requirements i {
  color: var(--current-color, #409eff);
  margin-top: 2px;
  flex-shrink: 0;
}

/* 工序连接箭头 */
.procedure-arrow {
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background: var(--base-item-bg, white);
  border: 2px solid var(--current-color, #409eff);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.procedure-arrow i {
  color: var(--current-color, #409eff);
  font-weight: bold;
  font-size: 14px;
}

/* 空状态样式 */
.empty-procedures {
  text-align: center;
  padding: 60px 20px;
  color: var(--base-color-3, #6c757d);
}

.empty-procedures i {
  font-size: 48px;
  color: var(--border-color-1, #dee2e6);
  margin-bottom: 16px;
  display: block;
}

.empty-procedures p {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: var(--base-color-1, #495057);
}

.empty-procedures span {
  font-size: 14px;
  color: var(--base-color-3, #868e96);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .procedure-card {
    flex: 0 0 calc(50% - 10px);
  }
  
  .procedure-arrow {
    display: none;
  }
}

@media (max-width: 768px) {
  .procedure-card {
    flex: 0 0 100%;
    min-width: auto;
  }
  
  .expand-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .procedures-container {
    padding: 20px 15px;
  }
}

/* 展开按钮样式优化 */
.expand-header-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--base-color-5, #909399);
}

/* 表格行展开效果 */
.el-table__expand-icon {
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid var(--border-color-1, #dcdfe6);
  color: var(--base-color-1, #606266);
}

.el-table__expand-icon:hover {
  background: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
  color: var(--theme-color, white);
  transform: scale(1.1);
}

.el-table__expand-icon.el-table__expand-icon--expanded {
  background: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
  color: var(--theme-color, white);
  transform: rotate(90deg);
}

/* 可点击行样式 */
.el-table .clickable-row {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.el-table .clickable-row:hover {
  background-color: var(--table-row-hover-bg, #f8f9ff) !important;
}

.el-table .clickable-row:hover .el-table__expand-icon {
  border-color: var(--current-color, #409eff);
  color: var(--current-color, #409eff);
}

/* 行点击提示 */
.el-table .clickable-row::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: transparent;
  transition: background-color 0.3s ease;
}

.el-table .clickable-row:hover::after {
  background: var(--current-color, #409eff);
}

/* 展开内容动画 */
.expand-content {
  animation: expandIn 0.4s ease-out;
}

@keyframes expandIn {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 工序卡片进入动画 */
.procedure-card {
  animation: cardSlideIn 0.6s ease-out;
  animation-fill-mode: both;
}

.procedure-card:nth-child(1) { animation-delay: 0.1s; }
.procedure-card:nth-child(2) { animation-delay: 0.2s; }
.procedure-card:nth-child(3) { animation-delay: 0.3s; }
.procedure-card:nth-child(4) { animation-delay: 0.4s; }
.procedure-card:nth-child(5) { animation-delay: 0.5s; }
.procedure-card:nth-child(n+6) { animation-delay: 0.6s; }

@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态样式 */
.theme-dark .procedures-loading .el-loading-mask,
.theme-starry-sky .procedures-loading .el-loading-mask {
  background-color: rgba(30, 41, 59, 0.8) !important;
}

.procedures-loading .el-loading-mask {
  border-radius: 8px;
  background-color: var(--loading-mask-bg, rgba(255, 255, 255, 0.9));
}

.procedures-loading .el-loading-spinner {
  margin-top: -15px;
}

.procedures-loading .el-loading-text {
  color: var(--current-color, #409eff);
  font-weight: 500;
}

/* 拖拽图标样式 */
.drag-icon {
  cursor: move;
  color: var(--base-color-6, #c0c4cc);
  transition: color 0.3s ease;
}

.drag-icon:hover {
  color: var(--current-color, #409eff);
}

/* 深色主题特殊适配 */
.theme-dark .expand-content,
.theme-starry-sky .expand-content {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.theme-dark .procedure-card,
.theme-starry-sky .procedure-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-color: var(--border-color-1);
}

.theme-dark .procedure-card:hover,
.theme-starry-sky .procedure-card:hover {
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.5);
  border-color: var(--current-color);
}

.theme-dark .procedure-arrow,
.theme-starry-sky .procedure-arrow {
  box-shadow: 0 2px 8px rgba(30, 58, 138, 0.6);
}

.theme-dark .procedure-arrow i,
.theme-starry-sky .procedure-arrow i {
  color: var(--theme-color);
}

/* 工艺描述在深色主题和星空主题下的特殊适配 */
body.theme-dark .expand-content .tech-requirements,
body.theme-starry-sky .expand-content .tech-requirements {
  color: #ffffff !important;
  background: rgba(58, 123, 153, 0.2) !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  border-left: 3px solid rgba(58, 123, 153, 0.8) !important;
}

body.theme-dark .expand-content .tech-requirements span,
body.theme-starry-sky .expand-content .tech-requirements span {
  color: #ffffff !important;
  font-weight: 600 !important;
}

body.theme-dark .expand-content .tech-requirements i,
body.theme-starry-sky .expand-content .tech-requirements i {
  color: var(--current-color, #409eff) !important;
}

body.theme-dark .expand-content .procedure-name,
body.theme-starry-sky .expand-content .procedure-name {
  color: var(--base-color-1, #ffffff) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: 700 !important;
}

body.theme-dark .expand-content .procedure-number,
body.theme-starry-sky .expand-content .procedure-number {
  color: #ffffff !important;
  background: var(--current-color, #1e3a8a) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 路线标题和描述在深色主题下的适配 - 使用更强的选择器 */
body.theme-dark .expand-content .route-title,
body.theme-starry-sky .expand-content .route-title {
  color: var(--theme-color, #ffffff) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* body.theme-dark .expand-content .route-desc {
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, rgba(58, 123, 153, 0.8) 0%, rgba(58, 123, 153, 0.6) 100%) !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  display: inline-block !important;
  border: 1px solid rgba(58, 123, 153, 0.4) !important;
} */

/* body.theme-starry-sky .expand-content .route-desc {
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(30, 58, 138, 0.7) 100%) !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  display: inline-block !important;
  border: 1px solid rgba(30, 58, 138, 0.5) !important;
  box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
} */

/* 空状态文字在深色主题下的适配 */
.theme-dark .empty-procedures,
.theme-starry-sky .empty-procedures {
  color: var(--base-color-3, #a0a0a0) !important;
}

.theme-dark .empty-procedures p,
.theme-starry-sky .empty-procedures p {
  color: var(--base-color-1, #ffffff) !important;
}

.theme-dark .empty-procedures span,
.theme-starry-sky .empty-procedures span {
  color: var(--base-color-3, #a0a0a0) !important;
}

/* 星空主题特殊效果 - 使用更强的选择器 */
body.theme-starry-sky .procedures-container {
  background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 100%) !important;
}

body.theme-starry-sky .expand-content .procedure-card {
  background: linear-gradient(135deg, rgba(26, 31, 60, 1) 0%, rgba(30, 58, 138, 0.25) 100%) !important;
  backdrop-filter: blur(15px) !important;
  border-color: rgba(30, 58, 138, 0.8) !important;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.35) !important;
}

body.theme-starry-sky .expand-content .procedure-card:hover {
  background: linear-gradient(135deg, rgba(26, 31, 60, 1) 0%, rgba(30, 58, 138, 0.45) 100%) !important;
  transform: translateY(-2px) !important;
  border-color: #1e3a8a !important;
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.5) !important;
}

body.theme-starry-sky .expand-content {
  background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 100%) !important;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.4) !important;
}

/* 深色主题下的特殊强化 - 使用更强的选择器 */
body.theme-dark .expand-content .procedure-card {
  background: linear-gradient(135deg, rgba(44, 61, 85, 1) 0%, rgba(58, 123, 153, 0.2) 100%) !important;
  border-color: rgba(58, 123, 153, 0.6) !important;
  box-shadow: 0 4px 12px rgba(58, 123, 153, 0.25) !important;
}

body.theme-dark .expand-content .procedure-card:hover {
  background: linear-gradient(135deg, rgba(44, 61, 85, 1) 0%, rgba(58, 123, 153, 0.35) 100%) !important;
  border-color: #3a7b99 !important;
  box-shadow: 0 6px 20px rgba(58, 123, 153, 0.4) !important;
}

body.theme-dark .expand-content {
  background: linear-gradient(135deg, #2a3950 0%, #364966 100%) !important;
}

body.theme-dark .procedures-container {
  background: linear-gradient(135deg, #46576e 0%, rgba(58, 123, 153, 0.1) 100%) !important;
}

/* 全局表格展开行修复 - 确保在所有情况下都生效 */
::v-deep .el-table__body .el-table__expanded-cell {
  background-color: transparent !important;
  padding: 0 !important;
}

/* 确保表格行的背景透明度在主题切换时正确应用 */
::v-deep .el-table__row .el-table__expanded-cell {
  background: transparent !important;
}

/* 确保工艺要求文字在所有深色主题下都有足够对比度 */
.theme-dark .tech-requirements {
  color: var(--base-color-1, #ffffff) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(58, 123, 153, 0.15) 0%, rgba(58, 123, 153, 0.08) 100%) !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  border-left: 3px solid rgba(58, 123, 153, 0.6) !important;
}

.theme-starry-sky .tech-requirements {
  color: var(--base-color-1, #ffffff) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.2) 0%, rgba(30, 58, 138, 0.1) 100%) !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  border-left: 3px solid rgba(30, 58, 138, 0.8) !important;
  box-shadow: 0 2px 4px rgba(30, 58, 138, 0.2) !important;
}

.theme-dark .tech-requirements span,
.theme-starry-sky .tech-requirements span {
  color: var(--base-color-1, #ffffff) !important;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 工艺路线详情样式 */
.process-route-info {
  background-color: var(--base-item-bg);
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 4px solid var(--current-color);
}

.process-route-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--base-color-1);
}

.process-route-info p {
  margin: 5px 0;
  color: var(--base-color-2);
}

.empty-data {
  padding: 30px 0;
}

.el-divider__text {
  background-color: var(--base-main-bg);
  color: var(--base-color-1);
}

/* 工艺路线详情对话框在深色主题下的适配 */
.theme-dark .process-route-info,
.theme-starry-sky .process-route-info {
  background-color: var(--base-item-bg);
  border-left-color: var(--current-color);
}

.theme-dark .process-route-info h3,
.theme-starry-sky .process-route-info h3 {
  color: var(--base-color-1, #ffffff);
}

.theme-dark .process-route-info p,
.theme-starry-sky .process-route-info p {
  color: var(--base-color-2, #c0c0c0);
}

/* 字段配置对话框主题适配 */
h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--base-color-1, #303133);
  font-weight: 500;
}

/* 字段配置对话框基础样式 */
.column-config-dialog .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--current-color, #409eff);
  border-color: var(--current-color, #409eff);
}

.column-config-dialog .el-checkbox:hover .el-checkbox__inner {
  border-color: var(--current-color, #409eff);
}

/* 字段配置预览区域基础样式 */
.column-config-dialog .preview-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 300px;
  background-color: #f5f7fa;
}

/* 星空主题下的预览容器 */
.theme-starry-sky .column-config-dialog .preview-container,
body.theme-starry-sky .column-config-dialog .preview-container {
  background-color: var(--base-item-bg, rgba(26, 31, 60, 0.9)) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
}

.column-config-dialog .preview-item {
  padding: 4px 8px;
  margin-bottom: 4px;
  background: #fff;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e4e7ed;
}

/* 星空主题下的预览项 */
.theme-starry-sky .column-config-dialog .preview-item,
body.theme-starry-sky .column-config-dialog .preview-item {
  background: var(--base-main-bg, #0b0d1a) !important;
  border-color: var(--border-color-1, #1e3a8a) !important;
  color: var(--theme-color, #ffffff) !important;
}

.column-config-dialog .preview-empty {
  text-align: center;
  color: #c0c4cc;
  margin-top: 100px;
}

/* 星空主题下的空状态 */
.theme-starry-sky .column-config-dialog .preview-empty,
body.theme-starry-sky .column-config-dialog .preview-empty {
  color: var(--base-color-3, #a0a0a0) !important;
}

/* 深色主题适配 */
.theme-dark h4 {
  color: var(--base-color-6, #b1b3b8);
}

.theme-starry-sky h4 {
  color: var(--theme-color, #ffffff);
}

/* 深色主题下的预览组件 */
.theme-dark .column-config-dialog .preview-container {
  background: var(--preview-bg, #2a3441);
  border-color: var(--border-color-1, #4c5565);
}

.theme-dark .column-config-dialog .preview-item {
  background: var(--base-menu-background, #363e4f);
  border-color: var(--border-color-1, #4c5565);
  color: var(--base-color-6, #e5eaf3);
}

.theme-dark .column-config-dialog .preview-empty {
  color: var(--base-color-5, #8b949e);
}
</style>
<template>
    <div>
        <el-table v-loading="loading" :data="sheetList" height="430">
            <el-table-column type="index" width="55" align="center" />
            <el-table-column label="工单编号" align="center" prop="orderCode" width="200">
                <template slot-scope="scope">
                    <span style="color: #4874cb;">{{ scope.row.orderCode }}</span>
                </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" width="auto" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="product-info-inline">
                        <span v-for="(product, index) in scope.row.products" :key="index" class="product-tag">
                            {{ product.productName }}
                            <span v-if="product.styleName">--{{ product.styleName }}</span>
                           <span v-if="product.orderItemQuantity">~产数:{{ product.orderItemQuantity }}</span>
                            <span v-if="index < scope.row.products.length - 1" class="product-separator">、</span>
                        </span>
                    </div>
                </template>
            </el-table-column>
          <el-table-column label="类型" align="center" prop="orderType" width="130">
            <template slot-scope="scope">
              <el-tag :type="getOrderTypeColor(scope.row.orderType)" size="small">
                {{ getOrderTypeName(scope.row.orderType) }}
              </el-tag>
            </template>
          </el-table-column>
            <el-table-column label="状态" align="center" prop="orderStatus" width="130">
                <template slot-scope="scope">
                    <el-tag :type="getStatusColor(scope.row.orderStatus)" size="small">
                        {{ getStatusName(scope.row.orderStatus)}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="期望结束时间" align="center" prop="orderExpectedTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.orderExpectedTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="工单进度" align="center" width="300">
                <template slot-scope="scope">
                    <el-progress
                        :percentage="getOverallProgress(scope.row.products)"
                        :color="getProgressColor(getOverallProgress(scope.row.products))"
                        :stroke-width="8"
                        :show-text="true"
                    />
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { getOrderDetailPage } from "@/api/jenasi/workOrders";
import { parseTime } from "@/utils/ruoyi";

export default {
    name: "SheetTable",
    props: {
        params: {},
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 工单表格数据
            sheetList: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 1000,
            },
        };
    },
    watch: {
        params(newValue) {
            this.queryParams.params = newValue;
            this.getList();
        }
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询工单列表 */
        getList() {
            this.loading = true;
            // 构建查询参数
            const query = {
                pageNum: 1,
                pageSize: 1000, // 获取足够多的数据用于dashboard显示
                ...this.queryParams
            };

            // 添加时间参数，从父组件传递的params中获取
            if (this.params && this.params.startTime && this.params.endTime) {
                query.startTime = this.params.startTime;
                query.endTime = this.params.endTime;
            }

            getOrderDetailPage(query).then(response => {
                this.sheetList = response.data.records || [];
                this.loading = false;

                // 计算统计数据
                var data = {};
                data.sheetNum = this.sheetList.length;
                data.planNum = this.sheetList.reduce((sum, item) => {
                    return sum + (item.products ? item.products.reduce((pSum, product) => pSum + (product.orderItemQuantity || 0), 0) : 0);
                }, 0);
                data.finishNum = this.sheetList.filter(item => item.orderStatus === 'COMPLETED').length;
                data.averageProduce = this.queryParams.params ?
                    (data.finishNum / (this.day(this.queryParams.params.startTime, this.queryParams.params.endTime) + 1)).toFixed(1) : 0;
                data.delayNum = this.sheetList.filter(item =>
                    item.orderStatus !== 'COMPLETED' &&
                    item.orderExpectedTime &&
                    this.day(new Date(), item.orderExpectedTime) < 0
                ).length;

                this.$emit('data', data);
            }).catch(() => {
                this.loading = false;
                this.$modal.msgError('获取工单列表失败');
            });
        },

        /** 计算日期差 */
        day(startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const timeDifference = end.getTime() - start.getTime();
            const dayDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
            return dayDifference;
        },

        /** 获取整体工序进度 */
        getOverallProgress(products) {
            if (!products || products.length === 0) return 0;

            let totalSteps = 0;
            let completedSteps = 0;

            products.forEach(product => {
                if (product.stepTasks && product.stepTasks.length > 0) {
                    totalSteps += product.stepTasks.length;
                    completedSteps += product.stepTasks.filter(step => step.isCompleted === 2).length;
                }
            });

            return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
        },

        /** 获取进度条颜色 */
        getProgressColor(percentage) {
            if (percentage === 100) return '#67c23a';
            if (percentage >= 80) return '#e6a23c';
            if (percentage >= 50) return '#409eff';
            return '#f56c6c';
        },

        /** 获取状态颜色 */
        getStatusColor(status) {
            const colorMap = {
                'NEW': 'info',
                'IN_PROGRESS': 'warning',
                'COMPLETED': 'success',
                'PAUSED': 'warning'
            };
            return colorMap[status] || 'info';
        },

        /** 获取状态名称 */
        getStatusName(status) {
            const nameMap = {
                'NEW': '未开始',
                'IN_PROGRESS': '执行中',
                'COMPLETED': '已完成',
                'PAUSED': '暂停中'
            };
            return nameMap[status] || status;
        },

        /** 时间格式化 */
        parseTime(time, pattern) {
            return parseTime(time, pattern);
        },

        /** 获取工单类型颜色 */
        getOrderTypeColor(orderType) {
            const colorMap = {
                'URGENT': 'danger',  // 加急显示红色
                'NORMAL': 'primary'  // 普通显示蓝色
            };
            return colorMap[orderType] || 'primary';
        },

        /** 获取工单类型名称 */
        getOrderTypeName(orderType) {
            const nameMap = {
                'URGENT': '加急',
                'NORMAL': '普通'
            };
            return nameMap[orderType] || '普通';
        }
    }
};
</script>
<style scoped>
.custom{
    width: 270px;
}

/* 产品信息显示样式 */
.product-info-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
    line-height: 1.4;
}

.product-tag {
    display: inline-block;
    padding: 2px 8px;
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.product-separator {
    color: var(--el-text-color-secondary);
    margin: 0 2px;
    font-weight: normal;
}

/* 深色主题适配 */
.theme-dark .product-tag {
    background-color: var(--el-color-primary-dark-2);
    color: var(--el-color-primary-light-3);
}

.theme-dark .product-separator {
    color: var(--el-text-color-secondary);
}
</style>

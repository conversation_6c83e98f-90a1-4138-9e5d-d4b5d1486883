 <template>
  <div :class="className" :style="{height:height,width:width}" v-loading="loading" element-loading-text="加载中..." />
</template>

<script>
import {getOrderStatsByDay} from "@/api/jenasi/orders";
import * as echarts from 'echarts'
import resize from '@/views/dashboard/mixins/resize'

const animationDuration = 1500

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    // 接收时间参数
    params: {
      type: Object,
      default: () => ({})
    },
    setInfo: {
      type: Object,
      default: () => {
        return {
          xName: '日期',
          yName: '个',
          legendName: ['新建', '进行中', '暂停', '已完成']
        }
      }
    }
  },
  data() {
    return {
      chart: null,
      loading: false,
      chartData: {
        xData: [],
        newCount: [],
        inProgressCount: [],
        pausedCount: [],
        completedCount: []
      }
    }
  },
  computed: {
    theme() {
      return this.$store.getters.logoInfo.theme || 'theme-light'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    'params': {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    'chartData': {
      handler() {
        if(this.chart) {
          this.chart.dispose()
          this.chart = null
        }
        this.initChart()
      },
      deep: true
    },
    theme: {
      deep: true,
      handler(val) {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
        this.initChart()
      }
    }
  },
  methods: {
    /** 获取任务状态分布数据 */
    fetchData() {
      this.loading = true;

      // getOrderStatsByDay API要求startTime和endTime参数是必需的
      // 如果没有时间参数，则不调用API，直接设置空数据
      if (!this.params || !this.params.startTime || !this.params.endTime) {
        this.chartData = {
          xData: [],
          newCount: [],
          inProgressCount: [],
          pausedCount: [],
          completedCount: []
        };
        this.loading = false;
        this.$nextTick(() => {
          this.initChart();
        });
        return;
      }

      const apiParams = {
        startTime: this.params.startTime,
        endTime: this.params.endTime
      };

      getOrderStatsByDay(apiParams).then(response => {
        if (response.code === 0 && response.data) {
          // 处理API返回的数据格式
          const data = response.data;
          const chartData = {
            xData: [],
            newCount: [],
            inProgressCount: [],
            pausedCount: [],
            completedCount: []
          };

          data.forEach(item => {
            chartData.xData.push(item.day);
            chartData.newCount.push(item.newCount || 0);
            chartData.inProgressCount.push(item.inProgressCount || 0);
            chartData.pausedCount.push(item.pausedCount || 0);
            chartData.completedCount.push(item.completedCount || 0);
          });

          this.chartData = chartData;
        } else {
          console.error('获取任务状态分布数据失败:', response.message);
          this.chartData = {
            xData: [],
            newCount: [],
            inProgressCount: [],
            pausedCount: [],
            completedCount: []
          };
        }
      }).catch(error => {
        console.error('获取任务状态分布数据失败:', error);
        this.chartData = {
          xData: [],
          newCount: [],
          inProgressCount: [],
          pausedCount: [],
          completedCount: []
        };
      }).finally(() => {
        this.loading = false;
        // 数据获取完成后初始化图表
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      let echartsTheme = this.theme == 'theme-light' ? 'lightTheme' : 'darkTheme'
      this.chart = echarts.init(this.$el, echartsTheme)

      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        toolbox: {
          show: true,
          feature: {
            magicType: { show: true, type: ['line', 'bar'], title: '' },
            restore: { show: true, title: '' },
          }
        },
        grid: {
          top: '20%',
          left: '2%',
          right: '5%',
          bottom: '0%',
          containLabel: true
        },
        dataZoom: {
          show: true,
          type: 'inside'
        },
        legend: {
          data: this.setInfo.legendName
        },
        xAxis: [{
          type: 'category',
          data: this.chartData.xData,
          name: this.setInfo.xName,
          nameLocation: 'end',
          nameGap: 8,
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          name: this.setInfo.yName,
          position: 'left',
          alignTicks: true,
        }],
        series: [
          {
            symbolSize: 8,
            name: this.setInfo.legendName[0],
            type: 'bar',
            barWidth: '18%',
            data: this.chartData.newCount,
            animationDuration,
            itemStyle: {
              color: '#409EFF',
            },
          },
          {
            name: this.setInfo.legendName[1],
            type: 'bar',
            barWidth: '18%',
            data: this.chartData.inProgressCount,
            animationDuration,
            itemStyle: {
              color: '#E6A23C',
            },
          },
          {
            name: this.setInfo.legendName[2],
            type: 'bar',
            barWidth: '18%',
            data: this.chartData.pausedCount,
            animationDuration,
            itemStyle: {
              color: '#F56C6C',
            },
          },
          {
            name: this.setInfo.legendName[3],
            type: 'bar',
            barWidth: '18%',
            data: this.chartData.completedCount,
            animationDuration,
            itemStyle: {
              color: '#67C23A',
            },
          }
        ]
      })
    }
  }
}
</script>

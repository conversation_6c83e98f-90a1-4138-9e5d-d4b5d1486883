<template>
  <div :class="className" :style="{height:height,width:width}" v-loading="loading" element-loading-text="加载中..." />
</template>

<script>
import {getDefectStats} from "@/api/jenasi/stepTask";
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // 接收时间参数
    params: {
      type: Object,
      default: () => ({})
    },
    yName: {
      type: String,
      default: '个'
    },
    xName: {
      type: String,
      default: '缺陷类型'
    },
    seriesName: {
      type: String,
      default: '缺陷数量'
    },
    itemColor: {
      type: String,
      default: '#5470c6'
    }

  },
  data() {
    return {
      chart: null,
      loading: false,
      chartData: {
        xData: [],
        yData: []
      }
    }
  },
  computed: {
    theme() {
      return this.$store.getters.logoInfo.theme || 'theme-light'
    }
  },
  watch: {
    'params': {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    'chartData': {
      handler() {
        if(this.chart) {
          this.chart.dispose()
          this.chart = null
        }
        this.initChart()
      },
      deep: true
    },
    theme: {
      deep: true,
      handler(val) {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
        this.initChart()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    /** 获取缺陷统计数据 */
    fetchData() {
      this.loading = true;

      // 如果没有时间参数，则不调用API，直接设置空数据
      if (!this.params || !this.params.startTime || !this.params.endTime) {
        this.chartData = {
          xData: [],
          yData: []
        };
        this.loading = false;
        this.$nextTick(() => {
          this.initChart();
        });
        return;
      }

      const apiParams = {
        startTime: this.params.startTime,
        endTime: this.params.endTime
      };

      getDefectStats(apiParams).then(response => {
        if (response.code === 0 && response.data) {
          // 处理API返回的数据格式
          const data = response.data;
          const chartData = {
            xData: [],
            yData: []
          };

          data.forEach(item => {
            chartData.xData.push(item.defectName);
            chartData.yData.push(item.defectCount || 0);
          });

          this.chartData = chartData;
        } else {
          console.error('获取缺陷统计数据失败:', response.message);
          this.chartData = {
            xData: [],
            yData: []
          };
        }
      }).catch(error => {
        console.error('获取缺陷统计数据失败:', error);
        this.chartData = {
          xData: [],
          yData: []
        };
      }).finally(() => {
        this.loading = false;
        // 数据获取完成后初始化图表
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      let echartsTheme = this.theme == 'theme-light' ? 'lightTheme' : 'darkTheme'
      this.chart = echarts.init(this.$el, echartsTheme)
      this.setOptions()
    },
    setOptions() {
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            if (params && params.length > 0) {
              const data = params[0];
              return `${data.name}<br/>${data.seriesName}: ${data.value}个`;
            }
            return '';
          }
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.xData,
          name: this.xName,
          nameLocation: 'end',
          nameGap: 8,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            rotate: this.chartData.xData.length > 5 ? 45 : 0,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: this.yName,
          axisTick: {
            show: false
          },
          minInterval: 1,
          axisLabel: {
            formatter: function(value) {
              return Math.floor(value);
            }
          }
        },
        series: [
          {
            name: this.seriesName,
            type: 'bar',
            data: this.chartData.yData,
            itemStyle: {
              color: this.itemColor,
              borderRadius: [4, 4, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}'
            },
            animationDuration: 1500,
            animationEasing: 'cubicInOut'
          }
        ]
      })
    }
  }
}
</script>

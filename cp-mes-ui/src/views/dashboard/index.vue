<template>
  <div class="app-container bg-container">
    <div class="nav-top">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" :rules="rules">
        <el-form-item label="起止时间" prop="dateRange">
          <el-date-picker v-model="queryParams.dateRange" style="width: 300px" value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getAllData">查询</el-button>
        </el-form-item>
      </el-form>

      <el-radio-group v-model="selectArea" @input="selectAreaChange" size="medium">
        <el-radio-button label="今天" value="今天"></el-radio-button>
        <el-radio-button label="昨天" value="昨天"></el-radio-button>
        <el-radio-button label="本月" value="本月"></el-radio-button>
        <el-radio-button label="近7天" value="近7天"></el-radio-button>
        <el-radio-button label="近15天" value="近15天"></el-radio-button>
      </el-radio-group>
    </div>
    <div class="station-top">
      <el-row :gutter="12">
        <el-col :span="4">
          <div class="top-item-box item-box-three" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>工单数</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                data.sheetNum }}</span>
              </div>
            </div>
          </div>

        </el-col>
        <el-col :span="4">
          <div class="top-item-box item-box-two" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>完成数</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                data.finishNum }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="top-item-box item-box-one" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>计划数</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                data.planNum }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="top-item-box item-box-three" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>平均生产周期（天）</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                data.averageProduce }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="top-item-box item-box-four" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>不良率</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                defectYield }}%</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="top-item-box item-box-four" style="display: flex;">
            <div style="flex:2;height:100%;">
              <div>超期工单</div>
              <div style="text-align:center;margin-top:15px;"><span style="font-size:50px;font-weight:bold;">{{
                data.delayNum }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="station-middle">
      <el-row :gutter="12">
        <el-col :span="18">
          <el-card class="box-card" shadow="never">
            <div style="display:flex;justify-content: space-between;align-items: center;">
              <div class="card-title">工单进度表</div>
            </div>
            <div style="height: calc(100% - 30px);">
              <sheetTable :params="params" @data="handleSheetNum"></sheetTable>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card" shadow="never">
            <div class="card-title">
              生产实时播报
              <el-button
                type="text"
                icon="el-icon-refresh"
                size="mini"
                @click="getProductionRealTimeData"
                :loading="productionLoading"
                style="margin-left: 10px; color: var(--current-color);"
                title="刷新数据"
              ></el-button>
            </div>
            <div
              v-loading="productionLoading"
              element-loading-text="加载中..."
              style="height: calc(100% - 30px); overflow: hidden;"
              ref="productionContainer"
            >
              <!-- 执行中工单列表 - 使用无缝滚动 -->
              <div v-if="productionOrderList.length > 0" class="production-scroll-container">
                <VueSeamlessScroll
                  :data="productionDisplayList"
                  class="seamless-warp"
                  :class-option="scrollOptions"
                >
                  <div
                    v-for="(item, index) in productionDisplayList"
                    :key="`${item.orderId}-${index}`"
                    class="production-item"
                  >
                    <!-- 工单信息头部 -->
                    <div class="item-header">
                      <div class="order-info">
                        <span class="order-code">{{ item.orderCode }}</span>
                        <el-tag
                          :type="getOrderStatusColor(item.orderStatus)"
                          size="mini"
                          class="status-tag"
                        >
                          {{ getOrderStatusName(item.orderStatus) }}
                        </el-tag>
                      </div>
                      <div class="time-info">{{ formatTime(item.orderCreatedTime) }}</div>
                    </div>

                    <!-- 产品和工序信息 -->
                    <div class="item-content">
                      <div v-if="item.products && item.products.length > 0">
                        <!-- 显示所有产品，但限制显示数量以避免卡片过高 -->
                        <div
                          v-for="(product, productIndex) in item.products.slice(0, 2)"
                          :key="product.orderItemId || product.productId"
                          class="product-section"
                          :class="{ 'product-separator': productIndex > 0 }"
                        >
                          <!-- 产品信息行 -->
                          <div class="product-line">
                            <span class="product-name">{{ product.productName }}</span>
                            <span v-if="product.styleName" class="product-style">- {{ product.styleName }}</span>
                            <span class="product-quantity">{{ product.orderItemQuantity }}件</span>
                          </div>

                          <!-- 所有的工序 -->
                          <div v-if="product.stepTasks && product.stepTasks.length > 0" class="current-steps">
                            <div
                              v-for="(step, stepIndex) in getCurrentSteps(product.stepTasks)"
                              :key="step.stepTaskId"
                              class="step-line"
                            >
                              <span class="step-name">{{ step.stepName }}</span>
                              <span v-if="step.assignee" class="assignee">{{ step.assignee }}</span>
                              <el-tag
                                :type="getStepStatusColor(step.isCompleted)"
                                size="mini"
                                class="step-status"
                              >
                                {{ getStepStatusName(step.isCompleted) }}
                              </el-tag>
                            </div>
                          </div>
                        </div>

                        <!-- 如果有更多产品，显示提示 -->
                        <div v-if="item.products.length > 2" class="more-products-hint">
                          <span>还有 {{ item.products.length - 2 }} 个产品...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </VueSeamlessScroll>
              </div>

              <!-- 无数据提示 -->
              <div v-else-if="!productionLoading" class="no-data">
                <i class="el-icon-info"></i>
                <p>暂无执行中的工单</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="station-bottom">
      <el-row :gutter="12">
        <el-col :span="8">
          <el-card class="box-card-chart" shadow="never">
            <div class="card-title">不良项名称统计</div>
            <div style="height: calc(100% - 30px);">
              <ProduceLine :params="params" height="100%" yName="个" itemColor="#5470c6" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card-chart" shadow="never">
            <div class="card-title">产品型号分布</div>
            <div style="height: calc(100% - 30px);">
              <ProducePie :params="params" height="100%"></ProducePie>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card-chart" shadow="never">
            <div class="card-title">任务状态分布</div>
            <div style="height: calc(100% - 30px);">
              <ProduceBar :params="params" height="100%" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {getOrderDetailPage} from "@/api/jenasi/workOrders";
import sheetTable from './sheetTable.vue';
import ProduceBar from './ProduceBar.vue';
import ProduceLine from './ProduceLine.vue';
import ProducePie from './ProducePie.vue';
import { listJobBooking } from "@/api/produce/jobBooking";
import moment from 'moment/moment'
import { overviewCharts } from "@/api/produce/task";
import VueSeamlessScroll from "vue-seamless-scroll";

export default {
  components: {
    sheetTable,
    ProduceBar,
    ProduceLine,
    ProducePie,
    VueSeamlessScroll
  },
  data() {
    return {
      tooltipVisible: false,
      scrollTimer: null,
      selectArea: '',
      queryParams: { dateRange: [], },
      JobBookingList: [],
      planAndOut: {
        yData: [],
        yData2: [],
        yData3: [],
        xData: [],
      },

      taskNames: [],
      defectYield: undefined,
      rules: {
        dateRange: [
          { required: true, message: "请选择时间", trigger: "change" },
        ],
      },
      params: {},
      data: {},
      // 生产实时播报相关数据
      productionOrderList: [], // 执行中的工单列表
      productionLoading: false, // 加载状态
      productionTimer: null, // 定时器
      productionRefreshInterval: 8000, // 刷新间隔（8秒）
    }
  },
  computed: {
    /** 用于无缝滚动显示的工单列表 */
    productionDisplayList() {
      return this.productionOrderList;
    },

    /** 无缝滚动配置选项 */
    scrollOptions() {
      return {
        step: 0.35, // 提高滚动速度，让播报更快一些
        limitMoveNum: 2, // 开始无缝滚动的数据量
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动)
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动)
        waitTime: 2000, // 减少停顿时间，让播报速度更快
      };
    }
  },
  created() {
    this.defaultDate();
    this.$nextTick(() => {
      this.getAllData();
      this.getProductionRealTimeData(); // 初始化生产实时播报数据
      this.startProductionTimer(); // 启动定时刷新
    })
  },
  mounted() {
    // 在组件加载时从本地存储中获取已保存的值
    this.selectArea = localStorage.getItem('selectArea') || '今天';
    if (this.selectArea === '今天') {
      this.queryParams.dateRange = [moment().format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
    } else if (this.selectArea === '昨天') {
      this.queryParams.dateRange = [moment().subtract(1, 'days').format("yyyy-MM-DD 00:00:00"), moment().subtract(1, 'days').format("yyyy-MM-DD 23:59:59")];
    } else if (this.selectArea === '本月') {
      this.queryParams.dateRange = [moment().format("yyyy-MM-01 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
    } else if (this.selectArea === '近7天') {
      this.queryParams.dateRange = [moment().subtract(6, 'days').format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
    } else if (this.selectArea === '近15天') {
      this.queryParams.dateRange = [moment().subtract(14, 'days').format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
    }
  },
  beforeDestroy() {
    // 清理定时器，防止内存泄漏
    this.clearProductionTimer();
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
  },
  methods: {
    /** 播报滚动禁用tooltip */
    handleScroll() {
      // 关闭所有 tooltip
      this.tooltipVisible = true;
      // 在滚动结束后打开 tooltip
      clearTimeout(this.scrollTimer);
      this.scrollTimer = setTimeout(() => {
        this.tooltipVisible = false;
      }, 200); // 在滚动停止 200ms 后打开 tooltip
    },
    getAllData() {
      let params = {
        startTime: this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange[1],
      }
      this.params = params;

      this.getJobBookingList(params);
      this.getOverviewCharts(params);
    },
    //默认日期
    defaultDate() {
      this.queryParams.dateRange = [moment().format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
    },
    /** 查询报工列表 */
    getJobBookingList(params) {
      var queryParams = {};
      queryParams.procedureStatus = '2';
      queryParams.params = params;
      listJobBooking(queryParams).then(response => {
        this.JobBookingList = response.rows;
      });
    },
    handleContent(jobBooking) {
      return jobBooking.productNumber + ' | ' + jobBooking.productName + '   ' + jobBooking.goodQuantity + '   良品 / ' + jobBooking.defectQuantity + '不良品';
    },
    handleSheetNum(data) {
      this.data = data;
    },
    getOverviewCharts(params) {
      var queryParams = {};
      queryParams.params = params;
      overviewCharts(queryParams).then(response => {
        var planAndOut = response.data.planAndOut;
        var planAndOutData = {
          yData: [],
          yData2: [],
          yData3: [],
          xData: [],
        };
        planAndOut.forEach(e => {
          planAndOutData.xData.push(e.dateTime);
          planAndOutData.yData.push(e.defectQuantity);
          planAndOutData.yData2.push(e.plannedQuantity);
          planAndOutData.yData3.push(e.goodQuantity);
        });
        this.planAndOut = planAndOutData;


        this.taskNames = response.data.taskNames;
        this.defectYield = response.data.defectYield;
      });
    },
    selectAreaChange() {
      localStorage.setItem('selectArea', this.selectArea);
      if (this.selectArea === '今天') {
        this.queryParams.dateRange = [moment().format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
      } else if (this.selectArea === '昨天') {
        this.queryParams.dateRange = [moment().subtract(1, 'days').format("yyyy-MM-DD 00:00:00"), moment().subtract(1, 'days').format("yyyy-MM-DD 23:59:59")];
      } else if (this.selectArea === '本月') {
        this.queryParams.dateRange = [moment().format("yyyy-MM-01 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
      } else if (this.selectArea === '近7天') {
        this.queryParams.dateRange = [moment().subtract(6, 'days').format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
      } else if (this.selectArea === '近15天') {
        this.queryParams.dateRange = [moment().subtract(14, 'days').format("yyyy-MM-DD 00:00:00"), moment().format("yyyy-MM-DD 23:59:59")];
      }
      this.getAllData();
    },

    /** ========== 生产实时播报相关方法 ========== */

    /** 获取生产实时播报数据 */
    getProductionRealTimeData() {
      this.productionLoading = true;

      const queryParams = {
        pageNum: 1,
        pageSize: 20, // 限制数量，避免数据过多
        orderStatus: 'IN_PROGRESS', // 只查询执行中的工单
        orderDispatchStatus: 'DISPATCHED' // 只查询已下发的工单
      };

      getOrderDetailPage(queryParams).then(response => {
        if (response.data && response.data.records) {
          this.productionOrderList = response.data.records;
          console.log('生产实时播报数据更新:', this.productionOrderList.length, '个工单');
        } else {
          this.productionOrderList = [];
        }
      }).catch(error => {
        console.error('获取生产实时播报数据失败:', error);
        this.$message.error('获取生产实时播报数据失败');
        this.productionOrderList = [];
      }).finally(() => {
        this.productionLoading = false;
      });
    },

    /** 格式化时间显示 */
    formatTime(timeStr) {
      if (!timeStr) return '';
      return moment(timeStr).format('MM-DD HH:mm');
    },

    /** 获取当前需要显示的工序（显示所有工序，按接口响应原始顺序） */
    getCurrentSteps(stepTasks) {
      if (!stepTasks || stepTasks.length === 0) return [];

      // 直接返回所有工序，保持接口响应的原始顺序
      return stepTasks;
    },

    /** 启动定时刷新 */
    startProductionTimer() {
      // 清除现有定时器
      this.clearProductionTimer();

      // 设置新的定时器
      this.productionTimer = setInterval(() => {
        this.getProductionRealTimeData();
      }, this.productionRefreshInterval);

      console.log(`生产实时播报定时刷新已启动，间隔: ${this.productionRefreshInterval / 1000}秒`);
    },

    /** 清除定时器 */
    clearProductionTimer() {
      if (this.productionTimer) {
        clearInterval(this.productionTimer);
        this.productionTimer = null;
        console.log('生产实时播报定时刷新已停止');
      }
    },

    /** 获取工单状态颜色 */
    getOrderStatusColor(status) {
      const colorMap = {
        'NEW': 'info',
        'IN_PROGRESS': 'warning',
        'COMPLETED': 'success',
        'PAUSED': 'danger'
      };
      return colorMap[status] || 'info';
    },

    /** 获取工单状态名称 */
    getOrderStatusName(status) {
      const nameMap = {
        'NEW': '未开始',
        'IN_PROGRESS': '执行中',
        'COMPLETED': '已完成',
        'PAUSED': '暂停中'
      };
      return nameMap[status] || status;
    },

    /** 获取工序状态颜色 */
    getStepStatusColor(isCompleted) {
      const colorMap = {
        0: 'info',      // 未开始
        1: 'warning',   // 执行中
        2: 'success'    // 已完成
      };
      return colorMap[isCompleted] || 'info';
    },

    /** 获取工序状态名称 */
    getStepStatusName(isCompleted) {
      const nameMap = {
        0: '未开始',
        1: '执行中',
        2: '已完成'
      };
      return nameMap[isCompleted] || '未开始';
    }
  },
}
</script>

<style scoped>
.app-container {
  min-height: calc(100vh - 84px);
  padding: 12px 12px 0 12px;
}

.top-item-box {
  height: 120px;
  background: var(--base-item-bg);
  margin-bottom: 12px;
  border-radius: 12px;
  color: #fff;
  padding: 14px;
}

.top-item-box2 {
  height: 85px;
  background: var(--base-item-bg);
  margin-bottom: 12px;
  border-radius: 12px;
  color: #fff;
  padding: 16px;
}

.item-box-one {
  background: linear-gradient(30deg, #1a94db, #4db1eb, #7acaf9);
  box-shadow: 0 4px 12px #8ed2fa;
}

.item-box-two {
  background: linear-gradient(30deg, #c7a327, #d5ba47, #e3cf65);
  box-shadow: 0 4px 12px #ece7cd;
}

.item-box-three {
  background: linear-gradient(30deg, #6365f7, #9177f1, #cd8ee9);
  box-shadow: 0 4px 12px #dcc9e6;
}

.item-box-four {
  background: linear-gradient(30deg, #ed3a60, #f1557a, #f67da0);
  box-shadow: 0 4px 12px #e7cfd6;
}

.box-card {
  height: 500px;
  margin-bottom: 12px;
  background-color: var(--base-item-bg);
  border-color: var(--border-color-3);
}

.box-card-chart {
  height: 400px;
  margin-bottom: 12px;
  background-color: var(--base-item-bg);
  border-color: var(--border-color-3);
}

.box-card-chart>>>.el-card__body {
  height: 100%;
}

.box-card>>>.el-card__body {
  height: 100%;
}

.card-title {
  font-weight: bold;
  height: 30px;
  display: flex;
  align-items: center;
  color: var(--base-color-1);
}

.card-title::before {
  content: '';
  height: 70%;
  width: 5px;
  background: var(--current-color);
  margin-right: 8px;
}

.nav-top {
  margin: 6px;
  display: flex;
  justify-content: space-between;
}

.time-stamp {
  position: absolute;
  right: 2.5%;
  color: #00d1ff;
  font-size: 20px;
}

.el-timeline {
  padding: 0;
}

/* ========== 生产实时播报样式 ========== */
.production-scroll-container {
  height: 100%;
  width: 100%;
  padding: 6px;
  box-sizing: border-box;
}

.seamless-warp {
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding: 0 6px;
  box-sizing: border-box;
}

.production-item {
  color: var(--base-color-1);
  padding: 18px 20px;
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.08) 0%, rgba(var(--current-color-rgb), 0.05) 100%);
  border-bottom: 2px dashed rgba(var(--current-color-rgb), 0.2);
  font-size: 13px;
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  box-sizing: border-box;
  border: 1px solid rgba(var(--current-color-rgb), 0.1);
}

.production-item:hover {
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.12) 0%, rgba(var(--current-color-rgb), 0.08) 100%);
  transform: translateX(2px);
  border-color: rgba(var(--current-color-rgb), 0.3);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(var(--current-color-rgb), 0.2);
  flex-shrink: 0;
  min-height: 32px;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  flex: 1;
}

.order-code {
  font-weight: bold;
  font-size: 15px;
  color: var(--current-color);
  white-space: nowrap;
  flex-shrink: 0;
}

.status-tag {
  font-size: 11px;
  flex-shrink: 0;
}

.time-info {
  font-size: 11px;
  color: var(--base-color-3);
  white-space: nowrap;
  flex-shrink: 0;
}

.item-content {
  line-height: 1.4;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 100px;
  overflow: hidden;
}

.product-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-shrink: 0;
}

.product-separator {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(var(--current-color-rgb), 0.2);
}

.product-line {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 6px;
  background: rgba(var(--current-color-rgb), 0.05);
  border-radius: 4px;
  padding: 6px 8px;
  border: 1px solid rgba(var(--current-color-rgb), 0.1);
}

.product-name {
  font-weight: 600;
  color: var(--base-color-1);
  font-size: 14px;
  flex-shrink: 0;
}

.product-style {
  font-size: 12px;
  color: var(--base-color-3);
  flex-shrink: 0;
}

.product-quantity {
  font-size: 12px;
  color: var(--base-color-1);
  margin-left: auto;
  font-weight: 500;
  background: rgba(var(--current-color-rgb), 0.15);
  padding: 2px 6px;
  border-radius: 3px;
  flex-shrink: 0;
}

.current-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  overflow-y: auto;
  padding: 4px 0;
}

.step-line {
  display: flex;
  align-items: center;
  font-size: 12px;
  padding: 6px 8px 6px 4px;
  background: rgba(var(--current-color-rgb), 0.03);
  border-radius: 4px;
  flex-wrap: nowrap;
  min-height: 28px;
  transition: all 0.2s ease;
  backdrop-filter: none;
  border: 1px solid rgba(var(--current-color-rgb), 0.08);
}

.step-line:hover {
  background: rgba(var(--current-color-rgb), 0.08);
  border-color: rgba(var(--current-color-rgb), 0.15);
}

.step-line:hover .step-name {
  border-left-color: var(--current-color);
}

.step-name {
  color: var(--base-color-1);
  font-weight: 500;
  flex-shrink: 0;
  font-size: 12px;
  border-left: 3px solid rgba(var(--current-color-rgb), 0.4);
  padding-left: 6px;
  line-height: 1.4;
}

.assignee {
  color: var(--base-color-2);
  font-size: 11px;
  margin-left: 10px;
  flex-shrink: 0;
  background: rgba(var(--current-color-rgb), 0.1);
  padding: 1px 4px;
  border-radius: 2px;
}

.step-status {
  font-size: 10px;
  flex-shrink: 0;
  margin-left: 10px;
}

/* 自定义滚动条样式 */
.current-steps::-webkit-scrollbar {
  width: 3px;
}

.current-steps::-webkit-scrollbar-track {
  background: rgba(var(--current-color-rgb), 0.1);
  border-radius: 2px;
}

.current-steps::-webkit-scrollbar-thumb {
  background: rgba(var(--current-color-rgb), 0.3);
  border-radius: 2px;
}

.current-steps::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--current-color-rgb), 0.5);
}

/* 更多产品提示样式 */
.more-products-hint {
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(var(--current-color-rgb), 0.08);
  border-radius: 3px;
  text-align: center;
  font-size: 11px;
  color: var(--base-color-3);
  font-style: italic;
  border: 1px solid rgba(var(--current-color-rgb), 0.1);
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
}

.no-data i {
  font-size: 48px;
  margin-bottom: 12px;
  color: var(--el-color-info);
}

.no-data p {
  margin: 0;
  font-size: 14px;
}

/* 浅色主题适配 */
.theme-light .production-item {
  color: var(--base-color-1);
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.06) 0%, rgba(var(--current-color-rgb), 0.03) 100%);
  border: 1px solid rgba(var(--current-color-rgb), 0.15);
  border-bottom: 2px dashed rgba(var(--current-color-rgb), 0.25);
  box-shadow: 0 2px 8px rgba(var(--current-color-rgb), 0.08);
}

.theme-light .production-item:hover {
  background: linear-gradient(135deg, rgba(var(--current-color-rgb), 0.1) 0%, rgba(var(--current-color-rgb), 0.06) 100%);
  border-color: rgba(var(--current-color-rgb), 0.3);
  box-shadow: 0 4px 12px rgba(var(--current-color-rgb), 0.12);
}

.theme-light .order-code {
  color: var(--current-color);
  font-weight: 700;
}

.theme-light .time-info {
  color: var(--base-color-3);
}

.theme-light .product-name {
  color: var(--base-color-1);
  font-weight: 600;
}

.theme-light .product-style {
  color: var(--base-color-3);
}

.theme-light .product-quantity {
  color: var(--base-color-1);
  background: rgba(var(--current-color-rgb), 0.12);
}

.theme-light .step-name {
  color: var(--base-color-1);
  border-left-color: var(--current-color);
}

.theme-light .assignee {
  color: var(--base-color-2);
  background: rgba(var(--current-color-rgb), 0.08);
}

.theme-light .product-line {
  background: rgba(var(--current-color-rgb), 0.04);
  border: 1px solid rgba(var(--current-color-rgb), 0.1);
}

.theme-light .step-line {
  background: rgba(var(--current-color-rgb), 0.02);
  border: 1px solid rgba(var(--current-color-rgb), 0.08);
}

.theme-light .step-line:hover {
  background: rgba(var(--current-color-rgb), 0.06);
  border-color: rgba(var(--current-color-rgb), 0.15);
}

.theme-light .more-products-hint {
  background: rgba(var(--current-color-rgb), 0.06);
  color: var(--base-color-3);
  border: 1px solid rgba(var(--current-color-rgb), 0.1);
}

/* 深色主题适配 */
.theme-dark .production-item {
  color: #fff;
  background: linear-gradient(135deg, rgba(11, 117, 255, 0.2) 0%, rgba(126, 236, 242, 0.15) 100%);
  border-bottom-color: rgba(126, 236, 242, 0.4);
}

.theme-dark .production-item:hover {
  background: linear-gradient(135deg, rgba(11, 117, 255, 0.3) 0%, rgba(126, 236, 242, 0.25) 100%);
}

.theme-dark .order-code {
  color: #7eecf2;
}

.theme-dark .time-info {
  color: rgba(255, 255, 255, 0.8);
}

.theme-dark .product-name {
  color: #fff;
}

.theme-dark .product-style {
  color: rgba(255, 255, 255, 0.8);
}

.theme-dark .product-quantity {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(126, 236, 242, 0.2);
}

.theme-dark .step-name {
  color: #fff;
  border-left-color: rgba(126, 236, 242, 0.4);
}

.theme-dark .assignee {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(126, 236, 242, 0.15);
}

/* 深色主题下的工序步骤优化 */
.theme-dark .step-line {
  background: rgba(0, 0, 0, 0.1);
  border-left-color: rgba(126, 236, 242, 0.3);
}

.theme-dark .step-line:hover {
  background: rgba(0, 0, 0, 0.15);
  border-left-color: rgba(126, 236, 242, 0.6);
}

/* 深色主题下的产品行优化 */
.theme-dark .product-line {
  background: rgba(0, 0, 0, 0.08);
}

.theme-dark .more-products-hint {
  background: rgba(126, 236, 242, 0.1);
  color: rgba(255, 255, 255, 0.7);
}
</style>

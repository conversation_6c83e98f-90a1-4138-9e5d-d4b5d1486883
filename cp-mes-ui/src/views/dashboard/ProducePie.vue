<template>
  <div class="pie-chart-container" :style="{height:height,width:width}">
    <div
      :class="className"
      :style="{height:'100%',width:'100%'}"
      v-loading="loading"
      element-loading-text="加载中..."
    />
    <!-- 切换按钮 -->
    <div
      v-if="actualPieData.length > maxDisplayItems"
      class="toggle-button"
      @click="toggleShowAll"
    >
      <el-button
        type="text"
        size="mini"
        :title="showAll ? '收起' : '展开全部'"
      >
        {{ showAll ? '收起' : `展开全部(${actualPieData.length})` }}
        <i :class="showAll ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </el-button>
    </div>

  </div>
</template>

<script>
import * as echarts from 'echarts'
import {countByProductAndStyle} from "@/api/jenasi/orderItem";
import resize from '@/views/dashboard/mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    unit: {
      type: String,
      default: '个'
    },
    pieData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // 新增：时间参数
    params: {
      type: Object,
      default: function () {
        return {};
      }
    },
  },
  data() {
    return {
      chart: null,
      total: 0,
      // 内部数据状态
      internalPieData: [],
      loading: false,
      // 切换显示状态
      showAll: false,
      // 默认显示的最大项目数
      maxDisplayItems: 4
    }
  },
  computed: {
    theme() {
      return this.$store.getters.logoInfo.theme || 'theme-light'
    },
    // 计算实际使用的数据：优先使用内部获取的数据，否则使用传入的pieData
    actualPieData() {
      return this.internalPieData.length > 0 ? this.internalPieData : this.pieData;
    },
    // 计算显示的数据：根据showAll状态决定显示前4项还是全部数据
    displayPieData() {
      if (this.showAll || this.actualPieData.length <= this.maxDisplayItems) {
        return this.actualPieData;
      }
      // 只显示前4项，不合并其他项
      return this.actualPieData.slice(0, this.maxDisplayItems);
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData();
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    'displayPieData': {
      handler() {
        this.$nextTick(() => {
          if (!this.chart) {
            return;
          }
          this.chart.dispose();
          this.chart = null;
          this.initChart();
        });
      }
    },

    'params': {
      deep: true,
      handler() {
        this.fetchData();
      }
    },
    theme: {
      deep: true,
      handler(val) {
        if (!this.chart) {
          return
        }
        this.chart.dispose()
        this.chart = null
        this.initChart()
      }
    }
  },
  methods: {
    /** 获取产品型号分布数据 */
    fetchData() {
      this.loading = true;

      // 根据是否有时间参数决定调用方式
      const apiParams = this.params && this.params.startTime && this.params.endTime ? {
        startTime: this.params.startTime,
        endTime: this.params.endTime
      } : {};

      countByProductAndStyle(apiParams).then(response => {
        if (response.code === 0 && response.data) {
          // 将API响应数据转换为图表期望的格式
          this.internalPieData = response.data.map(item => ({
            name: `${item.productName}-${item.styleName}`,
            value: item.totalQuantity
          }));
        } else {
          console.error('获取产品型号分布数据失败:', response.message);
          this.internalPieData = [];
        }
      }).catch(error => {
        console.error('获取产品型号分布数据失败:', error);
        this.internalPieData = [];
      }).finally(() => {
        this.loading = false;
        // 数据获取完成后初始化图表
        this.$nextTick(() => {
          this.initChart();
        });
      });
    },
    initChart() {
      let echartsTheme = this.theme == 'theme-light' ? 'lightTheme' : 'darkTheme'
      this.chart = echarts.init(this.$el.querySelector(`.${this.className}`), echartsTheme)

      // 计算总值（使用全部数据计算总值，保持百分比准确性）
      let total = 0
      this.actualPieData.forEach(item => {
        total = total + item.value
      })
      this.total = total

      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          appendToBody: true,
          formatter: (params) => {
            return params.seriesName + '<br/>' + params.marker + params.data.name + '： <strong>' + params.data.value + '</strong> ' + this.unit
          }
        },
        legend: {
          bottom: '0%',
          formatter: (param) => {
            let item = this.displayPieData.find(c => c.name == param)
            if(!item) return ''
            let rate = this.total == 0 ? 0 : item.value / this.total * 100
            return `{name|${param}}{count|${item.value}` + this.unit + `}{percent|${rate.toFixed(1)}%}`
          },
          itemWidth: 14,
          itemGap: 15,
          type: 'scroll',
          pageIconColor: '#2f4554',
          pageIconInactiveColor: '#aaa',
          pageIconSize: 15,
          pageTextStyle: {
            color: '#666'
          },
          animation: false,
          textStyle: {
            rich: {
              name: {
                width: 160
              },
              count: {
                width: 50
              },
              percent: {
                width: 40
              }
            },
          }
        },
        series: [
          {
            name: '分项占比',
            type: 'pie',
            radius: ['35%', '50%'],
            center: ['50%', '38%'],
            data: this.displayPieData,
            animationDuration: 1500,
            label: {
              // show: false,
              // formatter: '{b}:{d}%'
            },
            emphasis: {
              label: {
                fontWeight: 'bold',
                fontSize: 12
              }
            }
          }
        ]
      })
    },

    // 切换显示全部/收起
    toggleShowAll() {
      this.showAll = !this.showAll;
    }
  }
}
</script>

<style scoped>
.pie-chart-container {
  position: relative;
}

.toggle-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.toggle-button .el-button {
  padding: 4px 8px;
  font-size: 12px;
  color: #606266;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.toggle-button .el-button:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.toggle-button .el-button i {
  margin-left: 4px;
}
</style>

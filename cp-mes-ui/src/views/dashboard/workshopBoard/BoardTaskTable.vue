<template>
  <div class="task-container">
    <el-table class="board-table" style="height: 40px;" :row-class-name="tableRowClassName">
      <el-table-column label="工序名称" align="center" prop="procedureName" show-overflow-tooltip />
      <el-table-column label="生产任务数" align="center" prop="value" />
      <el-table-column label="计划数" align="center" prop="plannedQuantity" />
      <el-table-column label="良品数" align="center" prop="goodQuantity" />
      <el-table-column label="不良品数" align="center" prop="defectQuantity" />
      <el-table-column label="未完成数" align="center" prop="unfinished" />
    </el-table>
    <VueSeamlessScroll :data="tableData" class="seamless-warp" :class-option="defineScroll">
      <el-table :data="tableData" class="board-table" :row-class-name="tableRowClassName" :show-header="false">
        <el-table-column label="工序名称" align="center" prop="procedureName" show-overflow-tooltip />
        <el-table-column label="生产任务数" align="center" prop="value" />
        <el-table-column label="计划数" align="center" prop="plannedQuantity" />
        <el-table-column label="良品数" align="center" prop="goodQuantity" />
        <el-table-column label="不良品数" align="center" prop="defectQuantity" />
        <el-table-column label="未完成数" align="center" prop="unfinished" />
      </el-table>
    </VueSeamlessScroll>
  </div>
</template>

<script>
import VueSeamlessScroll from "vue-seamless-scroll";
export default {
  data() {
    return {};
  },
  components: {
    VueSeamlessScroll,
  },
  props: {
    tableData: {
      type: Array,
      default: () => {
        return [
          {
            name: "工序1",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序2",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序3",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序4",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序5",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序6",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序7",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
          {
            name: "工序8",
            count: 10,
            planCount: 20,
            ok: 30,
            ng: 40,
            unfinished: 50,
          },
        ];
      },
    },
  },
  computed: {
    defineScroll() {
      return {
        step: 0.5, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return "table-row-one";
      }
      return "table-row-two";
    },
  },
};
</script>

<style scoped>
.task-container {
  height: 100%;
  width: 100%;
}

.board-table {
  color: #fff;
  background-color: #022244;
  border: 0px;
}

.board-table>>>.el-table__header-wrapper th,
.board-table>>>.el-table__fixed-header-wrapper th {
  color: #fff;
  background-color: #053f8b !important;
}

.board-table>>>th.el-table__cell.is-leaf,
.board-table>>>td.el-table__cell {
  border: 0;
}

.board-table>>>tr {
  background-color: transparent;
}

.board-table::before {
  height: 0;
}

.board-table>>>.el-table__body tr:hover>td.el-table__cell {
  background-color: #2f6c94 !important;
}

.board-table>>>.table-row-one {
  background-color: #022244 !important;
}

.board-table>>>.table-row-two {
  background-color: rgba(95, 152, 220, 0.2) !important;
}

.board-table>>>colgroup col[name="gutter"] {
  display: none;
  width: 0;
}

.board-table>>>th.gutter {
  display: none;
  width: 0;
}

.board-table>>>.el-table__body {
  width: 100% !important;
}

.seamless-warp {
  height: 89%;
  width: 100%;
  overflow: hidden;
}
</style>
<style lang="scss" scoped>
.board-table {
  &.el-table--scrollable-y ::-webkit-scrollbar {
    display: none !important; //隐藏滚动条
  }
}
</style>
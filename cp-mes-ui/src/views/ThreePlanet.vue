<template>
  <div ref="planetContainer" class="planet-canvas"></div>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

export default {
  name: 'ThreePlanet',
  mounted() {
    this.initThree();
    window.addEventListener('resize', this.onResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onResize);
    if (this.renderer) {
      this.renderer.dispose && this.renderer.dispose();
      this.renderer.forceContextLoss && this.renderer.forceContextLoss();
      this.renderer.domElement = null;
      this.renderer = null;
    }
    cancelAnimationFrame(this.animId);
  },
  methods: {
    initThree() {
      const container = this.$refs.planetContainer;
      const width = container.clientWidth || 400;
      const height = container.clientHeight || 400;
      // 场景
      this.scene = new THREE.Scene();
      // 相机
      this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
      this.camera.position.set(0, 0, 6);
      // 渲染器
      this.renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
      this.renderer.setClearColor(0x000000, 0); // 透明背景
      this.renderer.setSize(width, height);
      container.appendChild(this.renderer.domElement);
      // 灯光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
      this.scene.add(ambientLight);
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(5, 5, 5);
      this.scene.add(directionalLight);
      // PLC主体（长方体）
      const plcBodyGeo = new THREE.BoxGeometry(3.2, 2, 1.2);
      const plcBodyMat = new THREE.MeshPhongMaterial({ color: 0xcccccc });
      const plcBody = new THREE.Mesh(plcBodyGeo, plcBodyMat);
      this.scene.add(plcBody);
      // 屏幕（正面偏上，深蓝色）
      const screenGeo = new THREE.BoxGeometry(1.2, 0.5, 0.05);
      const screenMat = new THREE.MeshPhongMaterial({ color: 0x223366, emissive: 0x112244 });
      const screen = new THREE.Mesh(screenGeo, screenMat);
      screen.position.set(0, 0.5, 0.63);
      this.scene.add(screen);
      // 按钮（正面偏下，绿色/红色小圆柱）
      const btnGeo = new THREE.CylinderGeometry(0.08, 0.08, 0.04, 32);
      const btnMat1 = new THREE.MeshPhongMaterial({ color: 0x22cc22 });
      const btnMat2 = new THREE.MeshPhongMaterial({ color: 0xcc2222 });
      for (let i = 0; i < 3; i++) {
        const btn = new THREE.Mesh(btnGeo, i === 2 ? btnMat2 : btnMat1);
        btn.rotation.x = Math.PI / 2;
        btn.position.set(-0.4 + i * 0.4, -0.6, 0.65);
        this.scene.add(btn);
      }
      // 端子条（右侧，灰色小长条）
      const terminalGeo = new THREE.BoxGeometry(0.04, 1.2, 0.5);
      const terminalMat = new THREE.MeshPhongMaterial({ color: 0x888888 });
      for (let i = 0; i < 4; i++) {
        const terminal = new THREE.Mesh(terminalGeo, terminalMat);
        terminal.position.set(1.62, 0.6 - i * 0.4, 0);
        this.scene.add(terminal);
      }
      // 控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.08;
      this.controls.enablePan = false;
      this.controls.minDistance = 2.5;
      this.controls.maxDistance = 10;
      this.controls.autoRotate = true;
      this.controls.autoRotateSpeed = 0.6;
      // 渲染循环
      this.animate();
    },
    animate() {
      this.controls && this.controls.update();
      this.renderer && this.renderer.render(this.scene, this.camera);
      this.animId = requestAnimationFrame(this.animate);
    },
    onResize() {
      const container = this.$refs.planetContainer;
      if (!container || !this.camera || !this.renderer) return;
      const width = container.clientWidth || 400;
      const height = container.clientHeight || 400;
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);
    }
  }
};
</script>

<style scoped>
.planet-canvas {
  width: 100%;
  height: 100%;
  min-width: 320px;
  min-height: 320px;
  max-width: 600px;
  max-height: 520px;
  border-radius: 24px;
  overflow: hidden;
  background: transparent;
  box-shadow: none;
}
</style> 
<template>
  <div class="task-navigation-container">
    <!-- 导航标签页 -->
    <div class="task-nav-tabs">
      <div class="nav-tabs-header">
        <div class="header-left">
          <h2>
            <i class="el-icon-s-order"></i>
            我的任务管理
          </h2>
          <p class="header-subtitle">高效管理您的工作任务</p>
        </div>
        <div class="user-info" v-if="currentUser.nickName">
          <div class="user-avatar">
            <i class="el-icon-user-solid"></i>
          </div>
          <div class="user-details">
            <span class="user-name">{{ currentUser.nickName }}</span>
            <span class="user-label">当前用户</span>
          </div>
        </div>
      </div>
      
      <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
        <el-tab-pane label="未开始任务" name="new">
          <template slot="label">
            <span class="tab-label">
              <i class="el-icon-time tab-icon"></i>
              <span class="tab-title">未开始任务</span>
              <el-badge :value="taskCounts.new" :hidden="taskCounts.new === 0" class="task-badge" />
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="进行中任务" name="inProgress">
          <template slot="label">
            <span class="tab-label">
              <i class="el-icon-loading tab-icon"></i>
              <span class="tab-title">进行中任务</span>
              <el-badge :value="taskCounts.inProgress" :hidden="taskCounts.inProgress === 0" class="task-badge" />
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="已暂停任务" name="paused">
          <template slot="label">
            <span class="tab-label">
              <i class="el-icon-video-pause tab-icon"></i>
              <span class="tab-title">已暂停任务</span>
              <el-badge :value="taskCounts.paused" :hidden="taskCounts.paused === 0" class="task-badge" />
            </span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="已完成任务" name="completed">
          <template slot="label">
            <span class="tab-label">
              <i class="el-icon-check tab-icon"></i>
              <span class="tab-title">已完成任务</span>
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 动态内容区域 -->
    <div class="task-content-area">
      <div class="content-wrapper">
        <!-- 未开始任务组件 -->
        <MyTaskComponent 
          v-show="activeTab === 'new'"
          ref="newTaskRef"
          @task-count-change="handleTaskCountChange"
        />
        
        <!-- 进行中任务组件 -->
        <InProgressTaskComponent 
          v-show="activeTab === 'inProgress'"
          ref="inProgressTaskRef"
          @task-count-change="handleTaskCountChange"
        />
        
        <!-- 已暂停任务组件 -->
        <PausedTaskComponent 
          v-show="activeTab === 'paused'"
          ref="pausedTaskRef"
          @task-count-change="handleTaskCountChange"
        />
        
        <!-- 已完成任务组件 -->
        <CompletedTaskComponent 
          v-show="activeTab === 'completed'"
          ref="completedTaskRef"
          @task-count-change="handleTaskCountChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MyTaskComponent from './myTask.vue'
import InProgressTaskComponent from './inProgressTasks.vue'
import PausedTaskComponent from './paused.vue'
import CompletedTaskComponent from './completedTasks.vue'
import { getUserProfile } from '@/api/system/user'

export default {
  name: 'TaskNavigation',
  components: {
    MyTaskComponent,
    InProgressTaskComponent,
    PausedTaskComponent,
    CompletedTaskComponent
  },
  data() {
    return {
      activeTab: 'new', // 默认显示未开始任务
      // 当前登录用户信息
      currentUser: {
        userId: null,
        nickName: null
      },
      // 各个状态的任务数量
      taskCounts: {
        new: 0,
        inProgress: 0,
        paused: 0,
        completed: 0
      }
    }
  },
  created() {
    // 确保默认激活未开始任务标签页
    this.activeTab = 'new'
    // 获取用户信息但不等待，让组件正常初始化
    this.getCurrentUserInfo().catch(error => {
      console.error('初始化时获取用户信息失败:', error)
    })
  },
  mounted() {
    // 确保默认激活未开始任务标签页
    this.activeTab = 'new'
    
    // 使用延迟确保所有子组件都已完全挂载，并且用户信息已获取
    this.$nextTick(() => {
      setTimeout(() => {
        // 等待用户信息获取完成后再进行初始化
        this.waitForUserInfoThenInit()
      }, 100)
    })
  },
  activated() {
    // 页面激活时确保显示未开始任务（适用于keep-alive缓存的页面）
    if (this.activeTab !== 'new') {
      this.activeTab = 'new'
    }
    this.$nextTick(() => {
      this.refreshCurrentComponent()
    })
  },
  beforeRouteEnter(to, from, next) {
    // 路由进入时确保默认显示未开始任务
    next(vm => {
      vm.activeTab = 'new'
      vm.$nextTick(() => {
        if (vm.refreshCurrentComponent) {
          vm.refreshCurrentComponent()
        }
      })
    })
  },
  methods: {
    /** 获取当前登录用户信息 */
    getCurrentUserInfo() {
      return new Promise((resolve, reject) => {
        try {
          const userId = this.$store.getters.userId
          
          // 优先尝试从store中获取用户名
          const userName = this.$store.getters.name
          if (userId && userName) {
            this.currentUser = {
              userId: userId,
              nickName: userName
            }
            console.log('从store获取当前登录用户:', this.currentUser)
          }
          
          // 调用个人信息API获取完整的用户信息，包括nickName
          getUserProfile().then(response => {
            const user = response.data.user
            if (userId && user.nickName) {
              this.currentUser = {
                userId: userId,
                nickName: user.nickName  // 使用nickName作为显示名称
              }
              console.log('从API获取当前登录用户:', this.currentUser)
              resolve(this.currentUser)
            } else {
              console.warn('无法从API获取当前用户信息')
              if (this.currentUser.nickName) {
                resolve(this.currentUser)
              } else {
                reject(new Error('无法获取用户信息'))
              }
            }
          }).catch(error => {
            console.error('获取用户详细信息失败:', error)
            // 降级处理：如果API调用失败，使用全局状态中的name
            if (userId && userName) {
              this.currentUser = {
                userId: userId,
                nickName: userName
              }
              console.log('降级处理，使用store中的用户信息:', this.currentUser)
              resolve(this.currentUser)
            } else {
              reject(error)
            }
          })
        } catch (error) {
          console.error('获取用户信息失败:', error)
          reject(error)
        }
      })
    },

    /** 处理标签页切换 */
    handleTabClick(tab) {
      console.log('切换到标签页:', tab.name)
      
      // 如果切换到未开始任务标签页，确保数据能够正确加载
      if (tab.name === 'new') {
        this.$nextTick(() => {
          setTimeout(() => {
            this.refreshCurrentComponent()
          }, 100)
        })
      } else {
        // 切换时刷新对应组件的数据
        this.$nextTick(() => {
          this.refreshCurrentComponent()
        })
      }
    },

    /** 刷新当前激活的组件 */
    refreshCurrentComponent() {
      console.log('刷新当前激活的组件，activeTab:', this.activeTab)
      
      let currentRef = null
      switch (this.activeTab) {
        case 'new':
          currentRef = this.$refs.newTaskRef
          console.log('未开始任务组件引用:', currentRef)
          break
        case 'inProgress':
          currentRef = this.$refs.inProgressTaskRef
          console.log('进行中任务组件引用:', currentRef)
          break
        case 'paused':
          currentRef = this.$refs.pausedTaskRef
          console.log('已暂停任务组件引用:', currentRef)
          break
        case 'completed':
          currentRef = this.$refs.completedTaskRef
          console.log('已完成任务组件引用:', currentRef)
          break
      }
      
      if (currentRef && typeof currentRef.refreshData === 'function') {
        console.log('调用子组件的refreshData方法')
        currentRef.refreshData()
      } else {
        console.warn('子组件引用不存在或refreshData方法不可用:', {
          currentRef: currentRef,
          hasRefreshData: currentRef && typeof currentRef.refreshData === 'function'
        })
        
        // 如果引用不存在，尝试延迟重试
        if (!currentRef) {
          setTimeout(() => {
            console.log('延迟重试刷新组件')
            this.refreshCurrentComponent()
          }, 200)
        }
      }
    },

    /** 处理任务数量变化 */
    handleTaskCountChange(data) {
      if (data && data.status && data.count !== undefined) {
        this.taskCounts[data.status] = data.count
        console.log('任务数量变化:', data.status, '=', data.count)
      }
    },

    /** 等待用户信息获取完成后初始化 */
    waitForUserInfoThenInit() {
      const checkUserInfo = (retryCount = 0) => {
        if (retryCount > 10) {
          console.warn('等待用户信息超时，使用默认初始化')
          this.initializeComponents()
          return
        }
        
        if (this.currentUser.nickName) {
          console.log('用户信息已获取，开始初始化组件')
          this.initializeComponents()
        } else {
          console.log('等待用户信息获取...', retryCount + 1)
          setTimeout(() => {
            checkUserInfo(retryCount + 1)
          }, 200)
        }
      }
      
      checkUserInfo()
    },

    /** 初始化组件 */
    initializeComponents() {
      // 确保未开始任务组件被正确加载
      this.refreshCurrentComponent()
      
      // 延迟更新任务数量，确保子组件已完成数据加载
      setTimeout(() => {
        this.updateTaskCounts()
      }, 500)
    },

    /** 更新所有任务数量 */
    updateTaskCounts() {
      console.log('更新任务数量')
      // 减少延迟时间，并添加重试机制
      const tryUpdateCounts = (retryCount = 0) => {
        if (retryCount > 3) {
          console.warn('更新任务数量重试次数过多，停止重试')
          return
        }
        
        let hasValidRef = false
        
        if (this.$refs.newTaskRef && typeof this.$refs.newTaskRef.total !== 'undefined') {
          this.taskCounts.new = this.$refs.newTaskRef.total || 0
          hasValidRef = true
          console.log('未开始任务数量:', this.taskCounts.new)
        }
        if (this.$refs.inProgressTaskRef && typeof this.$refs.inProgressTaskRef.total !== 'undefined') {
          this.taskCounts.inProgress = this.$refs.inProgressTaskRef.total || 0
          hasValidRef = true
          console.log('进行中任务数量:', this.taskCounts.inProgress)
        }
        if (this.$refs.pausedTaskRef && typeof this.$refs.pausedTaskRef.total !== 'undefined') {
          this.taskCounts.paused = this.$refs.pausedTaskRef.total || 0
          hasValidRef = true
          console.log('已暂停任务数量:', this.taskCounts.paused)
        }
        if (this.$refs.completedTaskRef && typeof this.$refs.completedTaskRef.total !== 'undefined') {
          this.taskCounts.completed = this.$refs.completedTaskRef.total || 0
          hasValidRef = true
          console.log('已完成任务数量:', this.taskCounts.completed)
        }
        
        // 如果没有有效的引用，尝试重试
        if (!hasValidRef && retryCount < 3) {
          console.log('没有有效的组件引用，延迟重试...', retryCount + 1)
          setTimeout(() => {
            tryUpdateCounts(retryCount + 1)
          }, 300)
        }
      }
      
      setTimeout(() => {
        tryUpdateCounts()
      }, 200)
    }
  }
}
</script>

<style scoped>
/* 主容器样式 */
.task-navigation-container {
  padding: 24px;
  background: linear-gradient(135deg, var(--base-main-bg, #f8fafc) 0%, var(--base-main-bg, #f1f5f9) 100%);
  min-height: calc(100vh - 80px);
  position: relative;
}

.task-navigation-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, rgba(54, 113, 232, 0.08) 0%, rgba(54, 113, 232, 0.02) 100%);
  pointer-events: none;
  z-index: 0;
}

.task-nav-tabs {
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

/* 头部样式优化 */
.nav-tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, var(--base-item-bg, #ffffff) 0%, var(--base-item-bg, #fafbfc) 100%);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color-1, rgba(229, 231, 235, 0.8));
  backdrop-filter: blur(8px);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-tabs-header h2 {
  margin: 0;
  color: var(--theme-color, #1f2937);
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, var(--current-color, #3671e8) 0%, #6366f1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-tabs-header h2 i {
  font-size: 32px;
  color: var(--current-color, #3671e8);
  -webkit-text-fill-color: var(--current-color, #3671e8);
}

.header-subtitle {
  margin: 0;
  color: var(--text-color-3, #6b7280);
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
}

/* 用户信息卡片 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--current-color, #3671e8) 0%, #6366f1 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.2);
  transition: all 0.3s ease;
}

.user-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(54, 113, 232, 0.3);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.user-avatar i {
  font-size: 20px;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.user-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 400;
}

/* 标签页样式重构 */
.task-nav-tabs >>> .el-tabs {
  border: none;
  background: transparent;
}

.task-nav-tabs >>> .el-tabs__header {
  margin: 0;
  background: var(--base-item-bg, #ffffff);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--border-color-1, rgba(229, 231, 235, 0.8));
  overflow-x: auto;
  min-height: 60px;
}

.task-nav-tabs >>> .el-tabs__nav {
  border: none;
  display: flex;
  width: 100%;
  min-width: min-content;
  align-items: stretch;
  gap: 6px;
}

.task-nav-tabs >>> .el-tabs__item {
  flex: 0 0 auto;
  padding: 12px 20px;
  margin: 0;
  border: 1px solid var(--border-color-2, #e5e7eb) !important;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--base-item-bg, #ffffff);
  color: var(--text-color-2, #6b7280);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  max-width: 160px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
}

/* 悬停效果 - 确保字体颜色清晰可见 */
.task-nav-tabs >>> .el-tabs__item:hover {
  background: var(--current-color, #3671e8);
  color: #ffffff !important;
  border-color: var(--current-color, #3671e8) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.25);
}

/* 激活状态 */
.task-nav-tabs >>> .el-tabs__item.is-active {
  background: var(--current-color, #3671e8);
  color: #ffffff !important;
  border-color: var(--current-color, #3671e8) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.3);
  z-index: 2;
}

/* 移除伪元素背景 */
.task-nav-tabs >>> .el-tabs__item::before {
  display: none;
}

.task-nav-tabs >>> .el-tabs__active-bar {
  display: none;
}

.task-nav-tabs >>> .el-tabs__nav-wrap::after {
  display: none;
}

/* 标签内容样式 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  z-index: 1;
  width: 100%;
  justify-content: center;
  padding: 0;
  min-height: 20px;
}

.tab-icon {
  font-size: 16px;
  flex-shrink: 0;
  transition: all 0.25s ease;
}

.tab-title {
  font-size: 13px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  flex: 0 0 auto;
  text-align: center;
}

.task-badge {
  flex-shrink: 0;
  margin-left: 4px;
  align-self: center;
}

/* 默认徽章样式 - 未开始任务使用红色 */
.task-badge >>> .el-badge__content {
  position: static;
  background: #ef4444;
  border: 1px solid rgba(255, 255, 255, 0.9);
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  height: 16px;
  line-height: 14px;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 进行中任务徽章 - 使用红色 */
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"] .task-badge >>> .el-badge__content {
  background: #ef4444;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.3);
}

/* 已暂停任务徽章 - 使用橙色 */
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"] .task-badge >>> .el-badge__content {
  background: #f59e0b;
  box-shadow: 0 1px 4px rgba(245, 158, 11, 0.3);
}

/* 已完成任务徽章样式已移除 - 不再显示数字徽章 */

/* 悬停和激活状态下的徽章样式 */
.task-nav-tabs >>> .el-tabs__item:hover .task-badge >>> .el-badge__content,
.task-nav-tabs >>> .el-tabs__item.is-active .task-badge >>> .el-badge__content {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.9);
}

/* 悬停和激活状态下不同类型任务的徽章文字颜色 */
.task-nav-tabs >>> .el-tabs__item:hover .task-badge >>> .el-badge__content,
.task-nav-tabs >>> .el-tabs__item.is-active .task-badge >>> .el-badge__content {
  color: #ef4444;
}

.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"]:hover .task-badge >>> .el-badge__content,
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"].is-active .task-badge >>> .el-badge__content {
  color: #ef4444;
}

.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"]:hover .task-badge >>> .el-badge__content,
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"].is-active .task-badge >>> .el-badge__content {
  color: #f59e0b;
}

/* 已完成任务的徽章悬停样式已移除 */

/* 内容区域 */
.task-content-area {
  background: var(--base-item-bg, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color-1, rgba(229, 231, 235, 0.8));
  min-height: calc(100vh - 320px);
  overflow: hidden;
  position: relative;
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

/* 特殊状态下的标签页颜色 */
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"]:hover,
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"].is-active {
  background: #f59e0b;
  border-color: #f59e0b !important;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"]:hover,
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"].is-active {
  background: #f59e0b;
  border-color: #f59e0b !important;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-completed"]:hover,
.task-nav-tabs >>> .el-tabs__item[aria-controls="tab-completed"].is-active {
  background: #10b981;
  border-color: #10b981 !important;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-nav-tabs >>> .el-tabs__item {
    min-width: 100px;
    max-width: 140px;
    padding: 10px 16px;
  }
  
  .tab-label {
    gap: 5px;
  }
  
  .tab-icon {
    font-size: 15px;
  }
  
  .tab-title {
    font-size: 12px;
  }
}

@media (max-width: 900px) {
  .task-nav-tabs >>> .el-tabs__item {
    min-width: 90px;
    max-width: 120px;
    padding: 10px 12px;
    height: 40px;
  }
  
  .tab-label {
    gap: 4px;
    min-height: 18px;
  }
  
  .tab-icon {
    font-size: 14px;
  }
  
  .tab-title {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .task-navigation-container {
    padding: 16px;
  }

  .nav-tabs-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    margin-bottom: 24px;
  }

  .nav-tabs-header h2 {
    font-size: 24px;
  }

  .nav-tabs-header h2 i {
    font-size: 28px;
  }

  .user-info {
    align-self: stretch;
    justify-content: center;
  }

  .task-nav-tabs >>> .el-tabs__header {
    padding: 6px;
    min-height: 52px;
  }

  .task-nav-tabs >>> .el-tabs__item {
    padding: 8px 12px;
    margin: 0;
    min-width: 80px;
    max-width: 100px;
    height: 36px;
  }

  .tab-label {
    flex-direction: column;
    gap: 2px;
    text-align: center;
    min-height: 20px;
  }

  .tab-title {
    font-size: 10px;
    font-weight: 500;
  }

  .tab-icon {
    font-size: 12px;
  }

  .content-wrapper {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .nav-tabs-header h2 {
    font-size: 20px;
  }

  .task-nav-tabs >>> .el-tabs__nav {
    flex-direction: column;
    gap: 4px;
  }

  .task-nav-tabs >>> .el-tabs__item {
    margin: 0;
    padding: 12px 16px;
    min-width: auto;
    max-width: none;
    width: 100%;
    height: 40px;
  }

  .tab-label {
    flex-direction: row;
    gap: 8px;
    justify-content: flex-start;
    min-height: 16px;
  }

  .tab-title {
    font-size: 13px;
    font-weight: 500;
    text-align: left;
  }

  .tab-icon {
    font-size: 14px;
  }
}

/* 主题适配 */
.theme-dark .task-navigation-container {
  background: linear-gradient(135deg, var(--base-main-bg, #0f172a) 0%, var(--base-main-bg, #1e293b) 100%);
}

.theme-dark .task-navigation-container::before {
  background: linear-gradient(135deg, rgba(54, 113, 232, 0.15) 0%, rgba(54, 113, 232, 0.05) 100%);
}

.theme-dark .nav-tabs-header {
  background: linear-gradient(135deg, var(--base-item-bg, #1e293b) 0%, var(--base-item-bg, #334155) 100%);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .nav-tabs-header h2 {
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .header-subtitle {
  color: var(--text-color-3, #94a3b8);
}

.theme-dark .task-nav-tabs >>> .el-tabs__header {
  background: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .task-nav-tabs >>> .el-tabs__item {
  background: var(--base-item-bg, #1e293b);
  color: var(--text-color-2, #94a3b8);
  border-color: var(--border-color-2, rgba(71, 85, 105, 0.6));
}

.theme-dark .task-nav-tabs >>> .el-tabs__item:hover {
  background: var(--current-color, #3671e8);
  color: #ffffff !important;
  border-color: var(--current-color, #3671e8) !important;
}

.theme-dark .task-nav-tabs >>> .el-tabs__item.is-active {
  background: var(--current-color, #3671e8);
  color: #ffffff !important;
  border-color: var(--current-color, #3671e8) !important;
}

.theme-dark .task-content-area {
  background: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

/* 深色主题下的徽章样式 */
.theme-dark .task-badge >>> .el-badge__content {
  background: #ef4444;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.4);
}

.theme-dark .task-nav-tabs >>> .el-tabs__item[aria-controls="tab-inProgress"] .task-badge >>> .el-badge__content {
  background: #ef4444;
  box-shadow: 0 1px 4px rgba(239, 68, 68, 0.4);
}

.theme-dark .task-nav-tabs >>> .el-tabs__item[aria-controls="tab-paused"] .task-badge >>> .el-badge__content {
  background: #f59e0b;
  box-shadow: 0 1px 4px rgba(245, 158, 11, 0.4);
}

/* 深色主题下已完成任务的徽章样式已移除 */
</style> 
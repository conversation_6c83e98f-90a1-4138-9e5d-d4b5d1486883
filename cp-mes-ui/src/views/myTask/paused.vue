<template>
    <div class="paused-task-container">
      <!-- 移除页面标题，由父组件统一管理 -->

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
          <el-form-item label="工单编号" prop="orderCode">
            <el-input
              v-model="queryParams.orderCode"
              placeholder="请输入工单编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="产品名称" prop="productName">
            <el-input
              v-model="queryParams.productName"
              placeholder="请输入产品名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="工单类型" prop="orderType">
            <el-select v-model="queryParams.orderType" placeholder="请选择工单类型" clearable>
              <el-option label="紧急" value="URGENT" />
              <el-option label="普通" value="NORMAL" />
            </el-select>
          </el-form-item>
                  <el-form-item label="暂停时间" prop="pausedTimeRange">
          <el-date-picker
            v-model="pausedTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateRangeChange"
          />
        </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
            <!-- 工单卡片网格 -->
      <div class="task-grid-container" v-loading="loading">
        <div v-if="groupedTasks.length === 0" class="empty-state">
          <el-empty description="暂无暂停的任务"></el-empty>
        </div>

        <!-- 工单卡片 -->
        <div
          v-for="order in groupedTasks"
          :key="order.key"
          class="work-order-card paused-card"
          @click="showWorkOrderDetail(order)"
        >
          <div class="card-header">
            <span class="order-code" :title="order.orderCode">{{ order.orderCode }}</span>
            <el-tag size="mini" type="warning">
              已暂停
            </el-tag>
          </div>

          <div class="card-body">
            <div class="product-summary">
              <i class="el-icon-box"></i>
              <span :title="getProductNamesText(order.products)">{{ getProductNamesText(order.products) }}</span>
            </div>
            <div class="task-summary">
              <div class="summary-item">
                <span>总工序</span>
                <strong>{{ order.totalStepTasks }}</strong>
              </div>
                          <div class="summary-item my-tasks">
              <span>我的任务</span>
              <strong>{{ order.myTasksCount }}</strong>
            </div>
            </div>
          </div>

          <div class="card-footer">
            <el-tag size="mini" :type="getOrderTypeTagType(order.orderType)" :effect="order.orderType === 'URGENT' ? 'dark' : 'light'">
              {{ getOrderTypeText(order.orderType) }}
            </el-tag>
                      <span class="creation-time">
            <i class="el-icon-time"></i>
            {{ parseTime(order.orderCreatedTime, '{y}-{m}-{d}') }}
          </span>
          </div>
        </div>
      </div>
  
      <!-- 分页 -->
      <div class="pagination-container">
        <pagination
          v-show="total > 0"
          :total="total"
          :page="queryParams.pageNum"
          :limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
  
      <!-- 工单详情对话框 (只读模式) -->
      <el-dialog
        :title="selectedOrder.orderCode ? `暂停工单详情 - ${selectedOrder.orderCode}` : '暂停工单详情'"
        :visible.sync="workOrderDialogVisible"
        width="80%"
        top="5vh"
        append-to-body
        class="work-order-dialog"
      >
        <div v-if="selectedOrder.products && selectedOrder.products.length > 0" class="dialog-product-container">
          <div v-for="product in selectedOrder.products" :key="product.productId" class="product-task-group">
            <div class="product-group-header">
              <i class="el-icon-box"></i>
              <span class="product-name">{{ product.productName }}</span>
              <span class="product-meta"> (ID: {{ product.productId }})</span>
              <el-tag size="mini" class="product-quantity-tag">
                数量: {{ product.orderTaskQuantity }}
              </el-tag>
            </div>
  
            <div class="task-table">
              <div class="task-table-header">
                <div class="col-step">工序</div>
                <div class="col-status">状态</div>
                <div class="col-assignee">负责人</div>
                <div class="col-time">相关时间</div>
                <div class="col-actions">操作</div>
              </div>
              <div class="task-table-body">
                <div
                  v-if="!product.stepTasks || product.stepTasks.length === 0"
                  class="no-tasks-row"
                >
                  该产品下无工序任务
                </div>
                <div
                  v-for="task in product.stepTasks"
                  :key="task.stepTaskId"
                  class="task-row"
                  :class="{
                    'my-task': task.assignee === currentUser.nickName,
                    'completed': task.isCompleted === 2,
                    'processing': task.isCompleted === 1
                  }"
                >
                  <!-- 工序信息 -->
                  <div class="task-cell col-step">
                    <div class="step-name">
                      <i class="el-icon-setting"></i>
                      <span>{{ task.stepName }}</span>
                    </div>
                    <div class="step-meta-info">
                      <span v-if="task.stepNumber">编码: {{ task.stepNumber }}</span>
                    </div>
                  </div>
  
                  <!-- 状态 -->
                  <div class="task-cell col-status">
                    <el-tag size="mini" :type="getStepStatusTagType(task.isCompleted)">
                      {{ getStepStatusText(task.isCompleted) }}
                    </el-tag>
                  </div>
  
                  <!-- 负责人 -->
                  <div class="task-cell col-assignee">
                    <el-tag size="mini" v-if="task.assignee === currentUser.nickName" type="success">
                      {{ task.assignee }}
                    </el-tag>
                    <span v-else>{{ task.assignee || '未分配' }}</span>
                  </div>
  
                  <!-- 时间信息 -->
                  <div class="task-cell col-time">
                    <div class="time-entry" v-if="task.stepTaskCreateTime">
                      <span class="time-label">开始:</span>
                      <span class="time-value">{{ parseTime(task.stepTaskCreateTime) }}</span>
                    </div>
                    <div class="time-entry" v-if="task.expectedAt">
                      <span class="time-label">期望:</span>
                      <span class="time-value">{{ parseTime(task.expectedAt) }}</span>
                    </div>
                    <div class="time-entry" v-if="task.pausedAt">
                      <span class="time-label">暂停:</span>
                      <span class="time-value warning">{{ parseTime(task.pausedAt) }}</span>
                    </div>
                  </div>
  
                  <!-- 操作按钮 -->
                  <div class="task-cell col-actions">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleView(task)"
                    >详情</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-else description="该工单下没有产品信息"></el-empty>
  
        <span slot="footer" class="dialog-footer">
          <el-button @click="workOrderDialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog>
  
      <!-- 任务详情对话框 (只读) -->
      <el-dialog :title="dialog.title" :visible.sync="dialog.open" width="1000px" append-to-body>
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 工单信息 -->
          <el-tab-pane label="工单信息" name="orderInfo">
            <el-form ref="orderForm" :model="form" label-width="120px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工单ID">
                    <el-input v-model="form.orderId" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工单编号">
                    <el-input v-model="form.orderCode" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工单类型">
                    <el-input v-model="orderTypeText" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工单状态">
                    <el-input :value="orderStatusText" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="创建时间">
                    <el-input v-model="form.orderCreatedTime" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
  
          <!-- 产品任务信息 -->
          <el-tab-pane label="产品任务信息" name="productTask">
            <el-form ref="productTaskForm" :model="form" label-width="120px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="产品ID">
                    <el-input v-model="form.productId" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="产品名称">
                    <el-input v-model="form.productName" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="任务级别">
                    <el-input v-model="taskLevelText" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="任务数量">
                    <el-input v-model="form.orderTaskQuantity" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工艺路线">
                    <el-input v-model="form.processRouteCode" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
  
          <!-- 工序任务信息 -->
          <el-tab-pane label="工序任务信息" name="stepTask">
            <el-form ref="stepTaskForm" :model="form" label-width="120px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工序任务ID">
                    <el-input v-model="form.stepTaskId" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工序ID">
                    <el-input v-model="form.stepId" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="工序名称">
                    <el-input v-model="form.stepName" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工序编码">
                    <el-input v-model="form.stepNumber" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="负责人">
                    <el-input v-model="form.assignee" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工序状态">
                    <el-input :value="stepStatusText" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
  
          <!-- 时间信息 -->
          <el-tab-pane label="时间信息" name="timeInfo">
            <el-form ref="timeForm" :model="form" label-width="120px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="任务创建时间">
                    <el-input v-model="form.orderTaskCreateTime" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="form.expectedAt">
                  <el-form-item label="期望完成时间">
                    <el-input v-model="form.expectedAt" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="form.pausedAt">
                <el-col :span="12">
                  <el-form-item label="暂停时间">
                    <el-input v-model="form.pausedAt" :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
  
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { getOrderDetailPage } from '@/api/jenasi/workOrders'
  import { getUserProfile } from '@/api/system/user'
  import { updateStepTaskStatus, getProcessRouteInfo } from '@/api/jenasi/stepTask'
  import { updateOrderStatus } from '@/api/jenasi/workOrder'
  
  export default {
    name: 'PausedTasks',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
  
        // 当前页显示的分组数据
        groupedTasks: [],
        // 展开状态
        expandedGroups: {},
        // 工单详情对话框
        workOrderDialogVisible: false,
        selectedOrder: {},
        // 暂停时间范围
        pausedTimeRange: [],
        // 当前登录用户信息
        currentUser: {
          userId: null,
          nickName: null
        },
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          orderId: null,
          orderCode: null,
          orderType: null,
          orderStatus: 'PAUSED', // 固定为暂停状态
          orderDispatchStatus: 'DISPATCHED', // 固定为已下发状态，只显示已下发的任务
          productName: null,
          itemName: null,
          taskLevel: null,
          assignee: null, // 执行人（当前登录用户）
          sortField: null,
          sortOrder: 'desc',
          startTime: null,
          endTime: null
        },
        // 表单参数
        form: {},
        // 对话框
        dialog: {
          open: false,
          title: ""
        },
        // 标签页激活项
        activeTab: 'orderInfo',
        // 状态显示文本
        orderTypeText: "",
        orderStatusText: "",
        taskLevelText: "",
        taskStatusText: "",
        stepStatusText: "",
        // 提交不良品数相关数据
        defectDialogVisible: false,
        defectLoading: false,
        submittingDefects: false,
        currentStepTask: {},
        defectiveNames: [],
        defectQuantities: {}
      }
    },
    computed: {
      // 计算总缺陷数量
      totalDefectCount() {
        return Object.values(this.defectQuantities).reduce((sum, count) => sum + (count || 0), 0)
      }
    },
    created() {
      // 初始化时获取第一页数据
      this.queryParams.pageNum = 1
    },
    mounted() {
      // 每次组件挂载时重新获取数据
      this.refreshData()
    },
    activated() {
      // 每次页面激活时重新获取数据（适用于keep-alive缓存的页面）
      this.refreshData()
    },
    beforeRouteEnter(to, from, next) {
      // 每次路由进入时都刷新数据
      next(vm => {
        if (vm.refreshData) {
          vm.refreshData()
        }
      })
    },
    methods: {
      /** 刷新页面数据 */
      refreshData() {
        console.log('刷新暂停任务页面数据')
        // 重置到第一页
        this.queryParams.pageNum = 1
        
        // 确保用户信息获取完成后再加载数据
        this.getCurrentUserInfo().then(() => {
          console.log('用户信息已获取，开始加载数据')
          this.getList()
        }).catch(error => {
          console.error('获取用户信息失败，使用默认查询:', error)
          // 即使用户信息获取失败，也尝试加载数据（可能会返回空结果）
          this.getList()
        })
      },
  
      /** 获取当前登录用户信息 */
      getCurrentUserInfo() {
        return new Promise((resolve, reject) => {
          try {
            const userId = this.$store.getters.userId
            
            // 优先尝试从store中获取用户名
            const userName = this.$store.getters.name
            if (userId && userName) {
              this.currentUser = {
                userId: userId,
                nickName: userName
              }
              this.queryParams.assignee = userName
              console.log('从store获取当前登录用户:', this.currentUser)
            }
            
            // 调用个人信息API获取完整的用户信息，包括nickName
            getUserProfile().then(response => {
              const user = response.data.user
              if (userId && user.nickName) {
                this.currentUser = {
                  userId: userId,
                  nickName: user.nickName  // 使用nickName作为显示名称
                }
                // 设置查询参数中的执行人为当前用户的姓名（用于后端模糊查询）
                this.queryParams.assignee = user.nickName
                console.log('从API获取当前登录用户:', this.currentUser)
                resolve(this.currentUser)
              } else {
                console.warn('无法从API获取当前用户信息')
                if (this.currentUser.nickName) {
                  resolve(this.currentUser)
                } else {
                  reject(new Error('无法获取用户信息'))
                }
              }
            }).catch(error => {
              console.error('获取用户详细信息失败:', error)
              // 降级处理：如果API调用失败，使用全局状态中的name
              if (userId && userName) {
                this.currentUser = {
                  userId: userId,
                  nickName: userName
                }
                this.queryParams.assignee = userName
                console.log('降级处理，使用store中的用户信息:', this.currentUser)
                resolve(this.currentUser)
              } else {
                reject(error)
              }
            })
          } catch (error) {
            console.error('获取用户信息失败:', error)
            reject(error)
          }
        })
      },
  
      /** 处理日期范围变化 */
      handleDateRangeChange(value) {
        if (value && value.length === 2) {
          this.queryParams.startTime = value[0]
          this.queryParams.endTime = value[1]
        } else {
          this.queryParams.startTime = null
          this.queryParams.endTime = null
        }
      },
  
            /** 查询任务列表 */
      getList(pagination) {
        this.loading = true

        // 如果没有用户信息，先尝试获取
        if (!this.currentUser.nickName) {
          console.warn('用户信息不存在，无法查询任务')
          this.loading = false
          this.groupedTasks = []
          this.total = 0
          // 触发任务数量变化事件
          this.$emit('task-count-change', { status: 'paused', count: 0 })
          return Promise.resolve()
        }

        // 确保查询参数中包含当前用户信息（用于后端模糊查询assignee）
        if (!this.queryParams.assignee && this.currentUser.nickName) {
          this.queryParams.assignee = this.currentUser.nickName
        }

        // 确保只查询暂停的工单
        this.queryParams.orderStatus = 'PAUSED'
        // 固定查询已下发的工单
        this.queryParams.orderDispatchStatus = 'DISPATCHED'

        // 如果传入了分页参数，则使用传入的，否则使用 queryParams 中的
        const pageNum = pagination ? pagination.page : this.queryParams.pageNum
        const pageSize = pagination ? pagination.limit : this.queryParams.pageSize

        const queryParams = {
          ...this.queryParams,
          pageNum: pageNum,
          pageSize: pageSize,
          _t: Date.now() // 添加时间戳防止缓存
        }

        console.log('查询参数:', queryParams)

        return getOrderDetailPage(queryParams).then(response => {
          console.log('暂停工单详细信息API响应:', response)

          let records = []
          let backendTotal = 0

          // 兼容多种响应格式
          if (response.data && response.data.records !== undefined) {
            // MyBatis Plus分页格式：response.data.records 和 response.data.total
            records = response.data.records || []
            backendTotal = response.data.total || 0
          } else if (response.rows !== undefined) {
            // 标准格式：response.rows 和 response.total
            records = response.rows || []
            backendTotal = response.total || 0
          } else if (response.data && Array.isArray(response.data)) {
            // 直接数组格式
            records = response.data
            backendTotal = response.data.length
          } else {
            // 兜底处理
            records = []
            backendTotal = 0
            console.warn('未知的API响应格式:', response)
          }

          console.log('后端返回暂停工单数据:', {
            返回数量: records.length,
            后端总数: backendTotal,
            页面大小: queryParams.pageSize
          })

          // 处理后端返回的工单数据
          this.processOrderData(records, backendTotal, pagination)

          this.loading = false
        }).catch(error => {
          console.error('获取暂停工单详细信息失败:', error)
          this.$message.error('获取暂停工单详细信息失败')
          this.groupedTasks = []
          this.total = 0
          this.loading = false
          return Promise.reject(error)
        })
      },
  
            /** 处理后端返回的工单数据 */
      processOrderData(orderRecords, backendTotal, pagination) {
        this.groupedTasks = []

        // 过滤只保留暂停且已下发的工单（双重保险，后端已按orderDispatchStatus='DISPATCHED'过滤）
        const filteredOrders = orderRecords.filter(order => 
          order.orderStatus === 'PAUSED'
        )

        filteredOrders.forEach(order => {
          let totalStepTasks = 0
          let myTasksCount = 0
          const processedProducts = []
          let hasMyTaskInOrder = false

          if (order.products && order.products.length > 0) {
            order.products.forEach(product => {
              const enrichedStepTasks = []
              let hasMyTask = false

              if (product.stepTasks && product.stepTasks.length > 0) {
                product.stepTasks.forEach(stepTask => {
                  const enrichedStepTask = {
                    ...stepTask,
                    orderId: order.orderId,
                    orderCode: order.orderCode,
                    orderType: order.orderType,
                    orderStatus: order.orderStatus,
                    orderCreatedTime: order.orderCreatedTime,
                    productId: product.productId,
                    productName: product.productName,
                    itemId: product.itemId,
                    itemName: product.itemName,
                    processRouteCode: product.processRouteCode,
                    taskLevel: product.taskLevel,
                    taskStatus: product.taskStatus,
                    orderTaskQuantity: product.orderTaskQuantity,
                    orderTaskCreateTime: product.orderTaskCreateTime,
                    expectedTime: product.expectedTime,
                    completedTime: product.completedTime,
                    isDefer: product.isDefer
                  }

                  enrichedStepTasks.push(enrichedStepTask)
                  totalStepTasks++

                  if (stepTask.assignee === this.currentUser.nickName) {
                    hasMyTask = true
                    hasMyTaskInOrder = true
                  }
                })
              }

              if (hasMyTask) {
                myTasksCount++
              }

              processedProducts.push({
                ...product,
                stepTasks: enrichedStepTasks
              })
            })
          }

          if (hasMyTaskInOrder) {
            const orderKey = `order_${order.orderId}`
            this.groupedTasks.push({
              key: orderKey,
              orderId: order.orderId,
              orderCode: order.orderCode,
              orderType: order.orderType,
              orderStatus: order.orderStatus,
              orderCreatedTime: order.orderCreatedTime,
              products: processedProducts,
              totalStepTasks: totalStepTasks,
              myTasksCount: myTasksCount,
              expanded: this.expandedGroups[orderKey] !== false
            })
          }
        })

        // 设置总数为后端返回的总数
        this.total = backendTotal

        if (pagination) {
          this.queryParams.pageNum = pagination.page
          this.queryParams.pageSize = pagination.limit
        }

        // 触发任务数量变化事件
        this.$emit('task-count-change', { status: 'paused', count: this.total })
      },
  
      /** 显示工单详情对话框 */
      showWorkOrderDetail(order) {
        this.selectedOrder = order
        this.workOrderDialogVisible = true
      },
  
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList({ page: 1, limit: this.queryParams.pageSize })
      },
  
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm")
        this.pausedTimeRange = []
        // 重置查询条件时保留当前用户的负责人筛选和固定的状态参数
        this.queryParams.assignee = this.currentUser.nickName
        this.queryParams.orderStatus = 'PAUSED'
        this.queryParams.orderDispatchStatus = 'DISPATCHED'
        this.queryParams.startTime = null
        this.queryParams.endTime = null
        this.handleQuery()
      },
  
      /** 查看按钮操作 */
      handleView(stepTask) {
        // stepTask 已经包含了完整的工单和产品信息
        this.form = { ...stepTask }
  
                // 设置各种状态文本
        this.orderTypeText = this.getOrderTypeText(this.form.orderType)
        this.orderStatusText = this.getOrderStatusText(this.form.orderStatus)
        this.taskLevelText = this.getTaskLevelText(this.form.taskLevel)
        this.taskStatusText = this.getTaskStatusText(this.form.taskStatus)
        this.stepStatusText = this.getStepStatusText(this.form.isCompleted)

        this.dialog.open = true
        this.dialog.title = "暂停任务详情 - " + (this.form.productName || '未知产品')
        this.activeTab = 'orderInfo'
      },
  
            /** 恢复任务按钮操作 */
      handleResume(row) {
        this.$modal.confirm('确认恢复工序"' + row.stepName + '"吗？').then(() => {
          this.updateStepTaskStatus(row.stepTaskId, 0, row.orderId) // 恢复为未开始状态
        }).catch(() => {})
      },
  
            /** 更新工序任务状态 */
      updateStepTaskStatus(stepTaskId, isCompleted, orderId) {
        // 当恢复一个工序时，可能需要更新工单状态
        if (isCompleted === 0 && orderId) {
          updateOrderStatus(orderId, 'PENDING').catch(error => {
            // 工单状态更新失败不应阻塞主流程，仅在控制台记录错误
            console.error('更新工单状态失败:', error)
          })
        }

        this.loading = true
        updateStepTaskStatus(stepTaskId, isCompleted).then(response => {
          this.$modal.msgSuccess("工序状态更新成功")
          // 保持当前页刷新
          this.getList({ page: this.queryParams.pageNum, limit: this.queryParams.pageSize }).then(() => {
            if (this.workOrderDialogVisible) {
              const updatedOrder = this.groupedTasks.find(o => o.orderId === this.selectedOrder.orderId)
              if (updatedOrder) {
                this.selectedOrder = updatedOrder
              } else {
                this.workOrderDialogVisible = false // 如果找不到，可能已被过滤，关闭对话框
              }
            }
          })

          if (this.dialog.open) {
            this.dialog.open = false
          }
        }).catch(error => {
          console.error('更新工序状态失败:', error)
          this.$modal.msgError("工序状态更新失败")
        }).finally(() => {
          this.loading = false
        })
      },
  
            /** 对话框中恢复任务 */
      handleResumeInDialog() {
        this.$modal.confirm('确认恢复工序"' + this.form.stepName + '"吗？').then(() => {
          this.updateStepTaskStatus(this.form.stepTaskId, 0, this.form.orderId)
        }).catch(() => {})
      },
  
      /** 取消按钮 */
      cancel() {
        this.dialog.open = false
        this.reset()
      },
  
      // 表单重置
      reset() {
        this.form = {}
        this.orderTypeText = ""
        this.orderStatusText = ""
        this.taskLevelText = ""
        this.taskStatusText = ""
        this.stepStatusText = ""
        this.activeTab = 'orderInfo'
      },
  
      /** 获取工单类型标签类型 */
      getOrderTypeTagType(orderType) {
        const typeMap = {
          'URGENT': 'danger',
          'NORMAL': 'primary'
        }
        return typeMap[orderType] || 'info'
      },
  
      /** 获取工单类型文本 */
      getOrderTypeText(orderType) {
        const typeMap = {
          'URGENT': '紧急',
          'NORMAL': '普通'
        }
        return typeMap[orderType] || orderType
      },

      /** 获取工单状态文本 */
      getOrderStatusText(orderStatus) {
        const statusMap = {
          'PENDING': '待处理',
          'IN_PROGRESS': '进行中',
          'PAUSED': '已暂停',
          'COMPLETED': '已完成'
        }
        return statusMap[orderStatus] || orderStatus
      },
  
      /** 获取产品名称文本 */
      getProductNamesText(products) {
        if (!products || products.length === 0) {
          return '无产品信息'
        }
        const productNames = products.map(p => p.productName).filter(Boolean)
        if (productNames.length === 0) {
          return '无产品名称'
        }
        if (productNames.length === 1) {
          return productNames[0]
        }
        return `${productNames[0]} 等 ${productNames.length} 个产品`
      },
  
      /** 获取任务状态文本 */
      getTaskStatusText(taskStatus) {
        const statusMap = {
          'PENDING': '待处理',
          'PROCESSING': '处理中',
          'COMPLETED': '已完成'
        }
        return statusMap[taskStatus] || taskStatus
      },
  
      /** 获取任务级别文本 */
      getTaskLevelText(taskLevel) {
        const levelMap = {
          'NORMAL': '普通',
          'URGENT': '紧急'
        }
        return levelMap[taskLevel] || taskLevel
      },
  
            /** 获取工序状态标签类型 */
      getStepStatusTagType(isCompleted) {
        const statusMap = {
          0: 'info',        // 未开始
          1: 'warning',     // 执行中
          2: 'success',     // 已完成
          '-1': 'warning',  // 已暂停
          'PAUSED': 'warning' // 已暂停
        }
        // 对于数字-1的情况单独处理
        if (isCompleted === -1) {
          return 'warning'
        }
        return statusMap[isCompleted] || 'info'
      },

      /** 获取工序状态文本 */
      getStepStatusText(isCompleted) {
        const statusMap = {
          0: '未开始',
          1: '执行中',
          2: '已完成',
          '-1': '已暂停',
          'PAUSED': '已暂停'
        }
        // 对于数字-1的情况单独处理
        if (isCompleted === -1) {
          return '已暂停'
        }
        return statusMap[isCompleted] || '未知'
      },
  
      /** 解析时间 */
      parseTime(time, pattern) {
        if (!time) return ''
        // 简单实现，只返回日期部分
        if (typeof time === 'string') {
          return time.split(' ')[0]
        }
        if (time instanceof Date) {
          const year = time.getFullYear()
          const month = (time.getMonth() + 1).toString().padStart(2, '0')
          const day = time.getDate().toString().padStart(2, '0')
          return `${year}-${month}-${day}`
        }
        return ''
      },
  
      /** 提交不良品数按钮操作 */
      handleDefectSubmit() {},
  
      /** 对话框中提交不良品数 */
      handleDefectSubmitInDialog() {},
  
      /** 加载工序可能出现的缺陷类型 */
      loadDefectiveNames() {},
  
      /** 提交缺陷信息 */
      submitDefects() {},
  
      /** 重置缺陷对话框 */
      resetDefectDialog() {},
  
      /** 减少缺陷数量 */
      decreaseDefectCount() {},
  
      /** 增加缺陷数量 */
      increaseDefectCount() {},
  
      /** 验证缺陷数量 */
      validateDefectCount() {}
    }
  }
  </script>
  
  <style scoped>
  /* 基础样式复用myTask.vue的样式 */
  .paused-task-container {
    padding: 20px;
    background: transparent;
    min-height: auto;
  }
  
  .page-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color-1, #d9d9d9);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .page-header h2 {
    margin: 0;
    color: var(--theme-color, #333333);
    font-size: 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .user-info .el-tag {
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 4px;
  }
  
  .page-header h2::before {
    content: '';
    width: 4px;
    height: 20px;
    background: var(--color-warning, #f59e0b);
    margin-right: 10px;
    border-radius: 2px;
  }
  
  .search-form {
    background: var(--base-item-bg, #fff);
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.12));
    border: 1px solid var(--border-color-1, #d9d9d9);
    transition: all 0.3s ease;
  }
  
  .search-form:hover {
    box-shadow: 0 4px 12px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.15));
  }
  
  /* 新的网格布局容器 */
  .task-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    min-height: 400px;
  }
  
  .empty-state {
    padding: 60px 20px;
    text-align: center;
    grid-column: 1 / -1; /* 占据整行 */
  }
  
    /* 暂停工单卡片样式 */
  .work-order-card.paused-card {
    background: var(--base-item-bg, #fff);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.08));
    border: 1px solid var(--border-color-1, #e8eaec);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    border-left: 4px solid var(--color-warning, #f59e0b);
  }

  .work-order-card.paused-card:hover {
    box-shadow: 0 6px 16px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.12));
    transform: translateY(-4px);
    border-color: var(--color-warning, #f59e0b);
    opacity: 1;
  }
  
  .card-header {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color-1, #e8eaec);
  }
  
  .order-code {
    font-size: 15px;
    font-weight: 600;
    color: var(--theme-color, #333);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .card-body {
    padding: 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .product-summary {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--base-color-3, #555);
    font-size: 13px;
  }
  .product-summary span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .product-summary i {
    color: var(--color-warning, #f59e0b);
    font-size: 16px;
  }
  
  .task-summary {
    display: flex;
    justify-content: space-around;
    background-color: var(--base-color-8, #f8f9fa);
    border-radius: 6px;
    padding: 12px 8px;
  }
  
  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }
  .summary-item span {
    font-size: 12px;
    color: var(--base-color-3, #7f7f7f);
  }
  .summary-item strong {
    font-size: 18px;
    font-weight: 600;
    color: var(--theme-color, #333);
  }
  .summary-item.my-tasks strong {
    color: var(--color-warning, #f59e0b);
  }
  
  .card-footer {
    padding: 10px 16px;
    background-color: var(--base-color-8, #f8f9fa);
    border-top: 1px solid var(--border-color-1, #e8eaec);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--base-color-3, #7f7f7f);
  }
  .creation-time {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  /* 任务清单表格样式 */
  .task-table {
    border: 1px solid var(--border-color-1, #e0e0e0);
    border-radius: 6px;
    overflow: hidden;
    background-color: var(--base-item-bg, #fff);
  }
  
  .task-table-header,
  .task-row {
    display: flex;
    align-items: center;
    padding: 0 16px;
    transition: background-color 0.3s;
  }
  
  .task-table-header {
    background-color: var(--base-color-8, #f8f9fa);
    padding: 12px 16px;
    font-size: 13px;
    font-weight: 500;
    color: var(--base-color-3, #7f7f7f);
    border-bottom: 1px solid var(--border-color-1, #e0e0e0);
  }
  
  .task-row {
    padding: 16px;
    border-bottom: 1px solid var(--border-color-1, #eef0f4);
    font-size: 13px;
  }
  
  .task-row:last-child {
    border-bottom: none;
  }
  
  .task-row:hover {
    background-color: var(--table-row-hover-bg, #f5f7fa);
  }

  /* 列定义 */
  .task-cell {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
  }
  
  .col-step { flex: 3; min-width: 220px; }
  .col-status { flex: 1; min-width: 80px; }
  .col-assignee { flex: 1; min-width: 100px; }
  .col-time { flex: 2; min-width: 180px; }
  .col-actions { flex: 1; min-width: 120px; text-align: right; flex-direction: row; align-items: center; justify-content: flex-end; gap: 8px;}
  
  /* 单元格内部样式 */
  .step-name {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--theme-color, #333);
  }
  
  .step-meta-info {
    font-size: 12px;
    color: var(--base-color-3, #888);
    margin-top: 4px;
  }
  
  .col-assignee .el-tag {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .col-time .time-entry {
    display: flex;
    align-items: center;
    line-height: 1.5;
    margin-bottom: 4px;
    gap: 4px;
  }
  .col-time .time-entry:last-child {
    margin-bottom: 0;
  }
  .col-time .time-label {
    font-size: 12px;
    color: var(--base-color-3, #7f7f7f);
    min-width: 30px;
    margin-right: 6px;
  }
  .col-time .time-value {
    font-size: 12px;
    color: var(--theme-color, #333);
    font-weight: 500;
  }
  .col-time .time-value.warning {
    color: var(--color-warning, #f59e0b);
    font-weight: 500;
  }

  /* 任务状态样式 - 与进行中任务保持一致 */
  .task-row.my-task {
    border-left: 3px solid var(--color-primary, #409eff);
    font-weight: 500;
    position: relative;
    background: linear-gradient(135deg, 
      rgba(64, 158, 255, 0.05), 
      rgba(64, 158, 255, 0.08));
  }

  .task-row.processing {
    background: linear-gradient(135deg, 
      rgba(245, 166, 35, 0.1), 
      rgba(245, 166, 35, 0.18));
    border-left: 3px solid var(--color-warning, #f5a623);
    position: relative;
  }

  .task-row.completed {
    background: linear-gradient(135deg, 
      rgba(103, 194, 58, 0.08), 
      rgba(103, 194, 58, 0.15));
    border-left: 3px solid var(--color-success, #67c23a);
    opacity: 0.9;
    position: relative;
  }

  /* 未开始状态的我的任务 */
  .task-row.my-task:not(.processing):not(.completed):not(.paused) {
    background: linear-gradient(135deg, 
      rgba(64, 158, 255, 0.05), 
      rgba(64, 158, 255, 0.08));
    border-left: 3px solid var(--color-primary, #409eff);
  }

  /* 执行中状态的我的任务 */
  .task-row.my-task.processing {
    background: linear-gradient(135deg, 
      rgba(245, 166, 35, 0.12), 
      rgba(245, 166, 35, 0.20));
    border-left: 3px solid var(--color-warning, #f5a623);
    font-weight: 600;
  }

  /* 已完成状态的我的任务 */
  .task-row.my-task.completed {
    background: linear-gradient(135deg, 
      rgba(103, 194, 58, 0.10), 
      rgba(103, 194, 58, 0.18));
    border-left: 3px solid var(--color-success, #67c23a);
    font-weight: 500;
  }

  /* 暂停状态的我的任务 */
  .task-row.my-task.paused {
    background: linear-gradient(135deg, 
      rgba(245, 158, 11, 0.12), 
      rgba(245, 158, 11, 0.20));
    border-left: 3px solid var(--color-warning, #f59e0b);
    font-weight: 600;
  }

  /* 未开始状态的非我的任务 */
  .task-row:not(.my-task):not(.processing):not(.completed):not(.paused) {
    background: var(--base-item-bg, #fff);
    border-left: 3px solid var(--border-color-1, #e8eaec);
  }
  
  .col-actions .el-button {
    margin: 0;
  }
  
  /* 对话框样式 */
  .work-order-dialog >>> .el-dialog__body {
    padding: 10px 24px 30px;
  }
  
  .dialog-product-container {
    max-height: 75vh;
    overflow-y: auto;
    padding-right: 10px;
  }
  
  .product-task-group {
    margin-bottom: 24px;
  }
  .product-task-group:last-child {
    margin-bottom: 0;
  }
  
  .product-group-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background-color: var(--base-color-8, #f8f9fa);
    border-radius: 6px;
    margin-bottom: 12px;
  }
  .product-group-header .product-name {
    font-size: 15px;
    font-weight: 500;
    color: var(--theme-color, #333);
  }
  .product-group-header .product-meta {
    font-size: 12px;
    color: var(--base-color-3, #7f7f7f);
  }
  .product-group-header .product-quantity-tag {
    margin-left: auto;
  }
  
  .no-tasks-row {
    padding: 20px;
    text-align: center;
    color: var(--base-color-3, #7f7f7f);
    font-size: 13px;
  }
  
  /* 响应式设计调整 */
  @media (max-width: 1200px) {
    .col-time { display: none; }
    .col-step { flex: 2; }
  }
  
  @media (max-width: 992px) {
    .col-step { flex: 3; }
    .col-assignee { flex: 1.5; }
  }
  
  @media (max-width: 768px) {
    .paused-task-container {
      margin: 10px;
      padding: 15px;
    }
  
    .search-form {
      padding: 15px;
    }
  
    .page-header h2 {
      font-size: 20px;
    }
  
    .task-table-header {
      display: none;
    }
    
    .task-row {
      flex-direction: column;
      align-items: stretch;
      padding: 12px;
      gap: 10px;
    }
    
    .task-cell {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 4px 0;
      border-bottom: 1px dashed var(--border-color-1, #eef0f4);
    }
    
    .task-row .task-cell:last-child {
      border-bottom: none;
    }
    
    .task-cell::before {
      content: attr(data-label);
      font-weight: 500;
      color: var(--base-color-3, #7f7f7f);
      margin-right: 10px;
    }
  
    .col-step::before { content: '工序'; }
    .col-status::before { content: '状态'; }
    .col-assignee::before { content: '负责人'; }
    .col-time::before { content: '相关时间'; }
    .col-actions::before { content: '操作'; }
  
    .col-time { display: flex; }
  
    .step-name { font-size: 15px; }
    .col-actions { justify-content: flex-start; }
  }
  
  /* 主题适配 */
  .theme-dark .paused-task-container {
    background: var(--base-main-bg, #0f172a);
  }
  
  .theme-dark .search-form {
    background: var(--base-item-bg, #1e293b);
    border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
  }
  
  .theme-dark .work-order-card {
    background: var(--base-item-bg, #1e293b);
    border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
  }
  
  .theme-dark .work-order-card:hover {
    background: var(--base-item-bg, #334155);
    border-color: var(--color-warning, #f59e0b);
  }
  
  .theme-dark .task-row {
    background: var(--base-item-bg, #1e293b);
    border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
  }
  
  .theme-dark .task-row:hover {
    background-color: var(--table-row-hover-bg, #334155);
  }
  
  .theme-dark .product-group-header {
    background-color: var(--base-color-8, #334155);
  }
  
  .theme-dark .step-name {
    color: var(--theme-color, #f1f5f9);
  }

  /* 深色主题下的工序状态样式 */
  .theme-dark .task-row.my-task:not(.processing):not(.completed):not(.paused) {
    background: linear-gradient(135deg, 
      rgba(64, 158, 255, 0.12), 
      rgba(64, 158, 255, 0.18));
    border-left: 3px solid var(--color-primary, #409eff);
  }

  .theme-dark .task-row.my-task.processing {
    background: linear-gradient(135deg, 
      rgba(245, 166, 35, 0.18), 
      rgba(245, 166, 35, 0.25));
    border-left: 3px solid var(--color-warning, #f5a623);
  }

  .theme-dark .task-row.my-task.completed {
    background: linear-gradient(135deg, 
      rgba(103, 194, 58, 0.15), 
      rgba(103, 194, 58, 0.22));
    border-left: 3px solid var(--color-success, #67c23a);
  }

  .theme-dark .task-row.my-task.paused {
    background: linear-gradient(135deg, 
      rgba(245, 158, 11, 0.18), 
      rgba(245, 158, 11, 0.25));
    border-left: 3px solid var(--color-warning, #f59e0b);
  }

  .theme-dark .task-row.processing:not(.my-task) {
    background: linear-gradient(135deg, 
      rgba(245, 166, 35, 0.15), 
      rgba(245, 166, 35, 0.22));
    border-left: 3px solid var(--color-warning, #f5a623);
  }

  .theme-dark .task-row.completed:not(.my-task) {
    background: linear-gradient(135deg, 
      rgba(103, 194, 58, 0.12), 
      rgba(103, 194, 58, 0.18));
    border-left: 3px solid var(--color-success, #67c23a);
  }

  .theme-dark .task-row.paused:not(.my-task) {
    background: linear-gradient(135deg, 
      rgba(245, 158, 11, 0.15), 
      rgba(245, 158, 11, 0.22));
    border-left: 3px solid var(--color-warning, #f59e0b);
  }

  .theme-dark .task-row:not(.my-task):not(.processing):not(.completed):not(.paused) {
    background: var(--base-item-bg, #1e293b);
    border-left: 3px solid var(--border-color-1, rgba(71, 85, 105, 0.8));
  }
  
  /* 移除提交不良品数对话框样式 */
  .defect-form,
  .step-info,
  .defect-selection,
  .defect-item,
  .defect-name,
  .defect-input-wrapper {
    /* 移除相关样式 */
  }
  </style> 
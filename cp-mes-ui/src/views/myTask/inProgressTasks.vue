<template>
  <div class="in-progress-task-container">
    <!-- 移除页面标题，由父组件统一管理 -->

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="工单编号" prop="orderCode">
          <el-input
            v-model="queryParams.orderCode"
            placeholder="请输入工单编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model="queryParams.productName"
            placeholder="请输入产品名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="工单类型" prop="orderType">
          <el-select v-model="queryParams.orderType" placeholder="请选择工单类型" clearable>
            <el-option label="紧急" value="URGENT" />
            <el-option label="普通" value="NORMAL" />
          </el-select>
        </el-form-item>
        <!-- 移除工单状态筛选，固定为进行中 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 工单卡片网格 -->
    <div class="task-grid-container" v-loading="loading">
      <div v-if="groupedTasks.length === 0" class="empty-state">
        <el-empty description="暂无进行中任务"></el-empty>
      </div>

      <!-- 工单卡片 -->
      <div
        v-for="order in groupedTasks"
        :key="order.key"
        class="work-order-card in-progress-card"
        @click="showWorkOrderDetail(order)"
      >
        <div class="card-header">
          <span class="order-code" :title="order.orderCode">{{ order.orderCode }}</span>
          <el-tag size="mini" type="warning">
            进行中
          </el-tag>
        </div>

        <div class="card-body">
          <div class="product-summary">
            <i class="el-icon-box"></i>
            <span :title="getProductNamesText(order.products)">{{ getProductNamesText(order.products) }}</span>
          </div>
          <div class="task-summary">
            <div class="summary-item">
              <span>总工序</span>
              <strong>{{ order.totalStepTasks }}</strong>
            </div>
            <div class="summary-item my-tasks">
              <span>我的任务</span>
              <strong>{{ order.myTasksCount }}</strong>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <el-tag size="mini" :type="getOrderTypeTagType(order.orderType)" :effect="order.orderType === 'URGENT' ? 'dark' : 'light'">
            {{ getOrderTypeText(order.orderType) }}
          </el-tag>
          <span class="creation-time">
            <i class="el-icon-time"></i>
            {{ parseTime(order.orderCreatedTime, '{y}-{m}-{d}') }}
          </span>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        :total="total"
        :page="queryParams.pageNum"
        :limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 工单详情对话框 -->
    <el-dialog
      :title="selectedOrder.orderCode ? `进行中工单详情 - ${selectedOrder.orderCode}` : '进行中工单详情'"
      :visible.sync="workOrderDialogVisible"
      width="80%"
      top="5vh"
      append-to-body
      class="work-order-dialog"
    >
      <div v-if="selectedOrder.products && selectedOrder.products.length > 0" class="dialog-product-container">
        <div v-for="product in selectedOrder.products" :key="product.productId" class="product-task-group">
          <div class="product-group-header">
            <i class="el-icon-box"></i>
            <span class="product-name">{{ product.productName }}</span>
            <span class="product-styleName">{{ product.styleName }}</span>
            <span class="product-meta"> (ID: {{ product.productId }})</span>
            <el-button type="text" size="mini"  @click="viewBomList(product.productName,product.styleName)">查看BOM清单</el-button>
            <el-tag size="mini" class="product-quantity-tag">
              数量: {{ product.orderTaskQuantity }}
            </el-tag>
          </div>

          <!-- 库存信息显示区域 -->
          <div class="inventory-info-section" v-loading="inventoryLoading">
            <div class="inventory-header">
              <i class="el-icon-s-data"></i>
              <span>库存信息</span>
            </div>
            <div v-if="inventoryData[product.productId]" class="inventory-content">
              <!-- 贴片仓库存 -->
              <div class="warehouse-info">
                <div class="warehouse-title">
                  <i class="el-icon-s-shop"></i>
                  <span>贴片仓</span>
                </div>
                <div class="stock-items">
                  <div v-if="inventoryData[product.productId].smdStock" class="stock-item">
                    <span class="board-type">上板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].smdStock.upperBoard || 0 }}</span>
                    <span class="board-type">下板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].smdStock.lowerBoard || 0 }}</span>
                  </div>
                  <div v-else class="no-data">暂无数据</div>
                </div>
              </div>

              <!-- 线边仓库存 -->
              <div class="warehouse-info">
                <div class="warehouse-title">
                  <i class="el-icon-s-shop"></i>
                  <span>线边仓</span>
                </div>
                <div class="stock-items">
                  <div v-if="inventoryData[product.productId].lineStock" class="stock-item">
                    <span class="board-type">上板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].lineStock.upperBoard || 0 }}</span>
                    <span class="board-type">下板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].lineStock.lowerBoard || 0 }}</span>
                  </div>
                  <div v-else class="no-data">暂无数据</div>
                </div>
              </div>

              <!-- 原料仓库存 -->
              <div class="warehouse-info">
                <div class="warehouse-title">
                  <i class="el-icon-s-shop"></i>
                  <span>原料仓</span>
                </div>
                <div class="stock-items">
                  <div v-if="inventoryData[product.productId].rawStock" class="stock-item">
                    <span class="board-type">上板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].rawStock.upperBoard || 0 }}</span>
                    <span class="board-type">下板:</span>
                    <span class="stock-value">{{ inventoryData[product.productId].rawStock.lowerBoard || 0 }}</span>
                  </div>
                  <div v-else class="no-data">暂无数据</div>
                </div>
              </div>
            </div>
            <div v-else-if="!inventoryLoading" class="inventory-error">
              <i class="el-icon-warning"></i>
              <span>库存信息加载失败或暂无数据</span>
            </div>
          </div>

          <div class="task-table">
            <div class="task-table-header">
              <div class="col-step">工序</div>
              <div class="col-status">状态</div>
              <div class="col-assignee">负责人</div>
              <div class="col-time">相关时间</div>
              <div class="col-actions">操作</div>
            </div>
            <div class="task-table-body">
              <div
                v-if="!product.stepTasks || product.stepTasks.length === 0"
                class="no-tasks-row"
              >
                该产品下无工序任务
              </div>
              <div
                v-for="task in product.stepTasks"
                :key="task.stepTaskId"
                class="task-row"
                :class="{
                  'my-task': task.assignee === currentUser.nickName,
                  'completed': task.isCompleted === 2,
                  'processing': task.isCompleted === 1
                }"
              >
                <!-- 工序信息 -->
                <div class="task-cell col-step">
                  <div class="step-name">
                    <i class="el-icon-setting"></i>
                    <span>{{ task.stepName }}</span>
                  </div>
                  <div class="step-meta-info">
                    <span v-if="task.stepNumber">编码: {{ task.stepNumber }}</span>
                  </div>
                </div>

                <!-- 状态 -->
                <div class="task-cell col-status">
                  <el-tag size="mini" :type="getStepStatusTagType(task.isCompleted)">
                    {{ getStepStatusText(task.isCompleted) }}
                  </el-tag>
                </div>

                <!-- 负责人 -->
                <div class="task-cell col-assignee">
                  <el-tag size="mini" v-if="task.assignee === currentUser.nickName" type="success">
                    {{ task.assignee }}
                  </el-tag>
                  <span v-else>{{ task.assignee || '未分配' }}</span>
                </div>

                <!-- 时间信息 -->
                <div class="task-cell col-time">
                  <div class="time-entry" v-if="task.stepTaskCreateTime">
                    <span class="time-label">开始:</span>
                    <span class="time-value">{{ parseTime(task.stepTaskCreateTime) }}</span>
                  </div>
                  <div class="time-entry" v-if="task.expectedAt">
                    <span class="time-label">期望:</span>
                    <span class="time-value">{{ parseTime(task.expectedAt) }}</span>
                  </div>
                  <div class="time-entry" v-if="task.completedAt">
                    <span class="time-label">完成:</span>
                    <span class="time-value success">{{ parseTime(task.completedAt) }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="task-cell col-actions">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleView(task)"
                  >详情</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleStart(task)"
                    v-if="task.isCompleted === 0 && task.assignee === currentUser.nickName"
                  >开始</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleComplete(task)"
                    v-if="task.isCompleted === 1 && task.assignee === currentUser.nickName"
                  >完成</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    @click.stop="handleDefectSubmit(task)"
                    v-if="task.assignee === currentUser.nickName"
                    style="color: #e6a23c;"
                  >提交缺陷</el-button>
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleViewDefects(task)"
                    v-if="task.assignee === currentUser.nickName"
                    style="color: #409eff;"
                  >查看缺陷</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else description="该工单下没有产品信息"></el-empty>

      <span slot="footer" class="dialog-footer">
        <el-button @click="workOrderDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 任务详情对话框 (用于单个工序) -->
    <el-dialog :title="dialog.title" :visible.sync="dialog.open" width="1000px" append-to-body>
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 工单信息 -->
        <el-tab-pane label="工单信息" name="orderInfo">
          <el-form ref="orderForm" :model="form" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="工单ID">
                  <el-input v-model="form.orderId" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工单编号">
                  <el-input v-model="form.orderCode" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="工单类型">
                  <el-input v-model="orderTypeText" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工单状态">
                  <el-input v-model="orderStatusText" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="创建时间">
                  <el-input v-model="form.orderCreatedTime" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 产品任务信息 -->
        <el-tab-pane label="产品任务信息" name="productTask">
          <el-form ref="productTaskForm" :model="form" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="产品ID">
                  <el-input v-model="form.productId" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品名称">
                  <el-input v-model="form.productName" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="任务级别">
                  <el-input v-model="taskLevelText" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="任务数量">
                  <el-input v-model="form.orderTaskQuantity" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="工艺路线">
                  <el-input v-model="form.processRouteCode" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 工序任务信息 -->
        <el-tab-pane label="工序任务信息" name="stepTask">
          <el-form ref="stepTaskForm" :model="form" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="工序任务ID">
                  <el-input v-model="form.stepTaskId" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工序ID">
                  <el-input v-model="form.stepId" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="工序名称">
                  <el-input v-model="form.stepName" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工序编码">
                  <el-input v-model="form.stepNumber" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人">
                  <el-input v-model="form.assignee" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工序状态">
                  <el-input v-model="stepStatusText" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 时间信息 -->
        <el-tab-pane label="时间信息" name="timeInfo">
          <el-form ref="timeForm" :model="form" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="任务创建时间">
                  <el-input v-model="form.orderTaskCreateTime" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.expectedAt">
                <el-form-item label="期望完成时间">
                  <el-input v-model="form.expectedAt" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="form.completedAt">
              <el-col :span="12">
                <el-form-item label="实际完成时间">
                  <el-input v-model="form.completedAt" :disabled="true" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
        <el-button
          type="primary"
          @click="handleStartInDialog"
          v-if="form.isCompleted === 0 && form.assignee === currentUser.nickName"
        >
          开始执行
        </el-button>
        <el-button
          type="success"
          @click="handleCompleteInDialog"
          v-if="form.isCompleted === 1 && form.assignee === currentUser.nickName"
        >
          标记完成
        </el-button>
        <el-button
          type="warning"
          @click="handleDefectSubmitInDialog"
          v-if="form.assignee === currentUser.nickName"
        >
          提交不良品数
        </el-button>
      </div>
    </el-dialog>

    <!-- 提交不良品数对话框 -->
    <el-dialog
      title="提交不良品数"
      :visible.sync="defectDialogVisible"
      width="600px"
      append-to-body
      @close="resetDefectDialog"
    >
      <div v-loading="defectLoading">
        <div class="defect-form">
          <!-- 工序信息 -->
          <div class="step-info">
            <h4>工序信息</h4>
            <p>工序名称：{{ currentStepTask.stepName }}</p>
            <p>工序编码：{{ currentStepTask.stepNumber }}</p>
          </div>

          <!-- 缺陷选择 -->
          <div class="defect-selection">
            <h4>选择不良品类型及数量</h4>
            <div class="defect-hint" v-if="hasExistingDefects">
              <el-alert
                title="提示：已自动填入上次提交的缺陷数量，您可以修改后重新提交"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 15px;">
              </el-alert>
            </div>
            <div v-if="defectiveNames.length === 0" style="text-align: center; padding: 20px;">
              <el-alert
                title="该工序未设置缺陷类型"
                type="warning"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
            <div v-else class="defect-list">
              <div v-for="defectName in defectiveNames" :key="defectName" class="defect-item">
                <div class="defect-name">{{ defectName }}</div>
                <div class="defect-input-wrapper">
                  <el-button
                    size="mini"
                    icon="el-icon-minus"
                    @click="decreaseDefectCount(defectName)"
                    :disabled="defectQuantities[defectName] <= 0"
                  ></el-button>
                  <el-input
                    v-model.number="defectQuantities[defectName]"
                    type="number"
                    min="0"
                    max="99999"
                    size="small"
                    placeholder="0"
                    style="width: 80px; margin: 0 5px;"
                    @input="validateDefectCount(defectName, $event)"
                  />
                  <el-button
                    size="mini"
                    icon="el-icon-plus"
                    @click="increaseDefectCount(defectName)"
                  ></el-button>
                </div>
              </div>
            </div>

            <div class="defect-summary" v-if="totalDefectCount > 0">
              <el-alert
                :title="`总计不良品数量：${totalDefectCount} 个`"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="defectDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitDefects"
          :disabled="defectiveNames.length === 0"
          :loading="submittingDefects"
        >
          提 交
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看缺陷信息对话框 -->
    <el-dialog
      title="查看缺陷信息"
      :visible.sync="viewDefectDialogVisible"
      width="600px"
      append-to-body
      @close="resetViewDefectDialog"
    >
      <div v-loading="viewDefectLoading">
        <div class="view-defect-form">
          <!-- 工序信息 -->
          <div class="step-info">
            <h4>工序信息</h4>
            <p>工序名称：{{ currentStepTask.stepName }}</p>
            <p>工序编码：{{ currentStepTask.stepNumber }}</p>
          </div>

          <!-- 缺陷信息展示 -->
          <div class="defect-info-display">
            <h4>已提交的缺陷信息</h4>
            <div v-if="Object.keys(stepTaskDefectInfo).length === 0" style="text-align: center; padding: 20px;">
              <el-alert
                title="该工序任务暂未提交缺陷信息"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
            <div v-else class="defect-info-list">
              <div v-for="(quantity, defectName) in stepTaskDefectInfo" :key="defectName" class="defect-info-item">
                <div class="defect-name">{{ defectName }}</div>
                <div class="defect-quantity">
                  <el-tag type="warning" size="medium">{{ quantity }} 个</el-tag>
                </div>
              </div>

              <div class="defect-summary-info" v-if="totalDefectInfoCount > 0">
                <el-alert
                  :title="`总计缺陷数量：${totalDefectInfoCount} 个`"
                  type="warning"
                  :closable="false"
                  show-icon>
                </el-alert>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewDefectDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- BOM清单对话框 -->
    <el-dialog
      :title="`BOM清单 - ${currentProductName}`"
      :visible.sync="bomDialogVisible"
      width="55%"
      append-to-body
      @close="bomData = []">

      <el-table
        :data="bomData"
        v-loading="bomLoading"
        element-loading-text="加载BOM清单中..."
        border
        stripe
        height="500px"
        style="width: 100%;">

        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="model" label="型号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="module" label="功能模块" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="boardType" label="板型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.boardType === '上板' ? 'success' : 'warning'" size="mini">
              {{ scope.row.boardType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="material" label="物料名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
        <el-table-column prop="currentStock" label="库存数量" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="minStockQuantity" label="安全库存" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reservedField3" label="物料编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.reservedField3">{{ scope.row.reservedField3 }}</span>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="padding: 40px;">
            <i class="el-icon-document" style="font-size: 48px; color: #c0c4cc;"></i>
            <p style="color: #909399; margin-top: 10px;">
              {{ bomLoading ? '正在加载...' : `产品 ${currentProductName} 暂无BOM清单数据` }}
            </p>
          </div>
        </template>
      </el-table>

      <div style="text-align: center; margin-top: 15px; color: #909399;">
        共找到 {{ bomData.length }} 条BOM记录
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="bomDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderDetailPage } from '@/api/jenasi/workOrders'
import { getUserProfile } from '@/api/system/user'
import { updateStepTaskStatus, getProcessRouteInfo, addDefects, getStepTaskDetail } from '@/api/jenasi/stepTask'
import { updateOrderStatus, findByNameAndStyleId, findByNameAndStyleIdTwo, findByName } from '@/api/jenasi/workOrder'
import { getBomByModelAndStyle } from '@/api/basicData/productMaterialsBom'

export default {
  name: 'InProgressTasks',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,

      // 当前页显示的分组数据
      groupedTasks: [],
      // 展开状态
      expandedGroups: {},
      // 工单详情对话框
      workOrderDialogVisible: false,
      selectedOrder: {},
      // 当前登录用户信息
      currentUser: {
        userId: null,
        nickName: null
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
        orderCode: null,
        orderType: null,
        orderStatus: 'IN_PROGRESS', // 固定为进行中状态
        orderDispatchStatus: 'DISPATCHED', // 固定为已下发状态，只显示已下发的任务
        productName: null,
        itemName: null,
        taskLevel: null,
        assignee: null, // 执行人（当前登录用户）
        sortField: null,
        sortOrder: 'desc'
      },
      // 表单参数
      form: {},
      // 对话框
      dialog: {
        open: false,
        title: ""
      },
      // 标签页激活项
      activeTab: 'orderInfo',
      // 状态显示文本
      orderTypeText: "",
      orderStatusText: "",
      taskLevelText: "",
      taskStatusText: "",
      stepStatusText: "",
      // 提交不良品数相关数据
      defectDialogVisible: false,
      defectLoading: false,
      submittingDefects: false,
      currentStepTask: {},
      defectiveNames: [],
      defectQuantities: {},
      // 查看缺陷信息相关数据
      viewDefectDialogVisible: false,
      viewDefectLoading: false,
      stepTaskDefectInfo: {},
      // 库存查询相关
      inventoryLoading: false,
      inventoryData: {},
      // BOM清单相关数据
      bomDialogVisible: false,
      bomData: [],
      bomLoading: false,
      currentProductName: ''
    }
  },
  computed: {
    // 计算总缺陷数量
    totalDefectCount() {
      return Object.values(this.defectQuantities).reduce((sum, count) => sum + (count || 0), 0)
    },
    // 计算查看缺陷信息的总数量
    totalDefectInfoCount() {
      return Object.values(this.stepTaskDefectInfo).reduce((sum, count) => sum + (count || 0), 0)
    },
    // 判断是否有已存在的缺陷数据
    hasExistingDefects() {
      return Object.values(this.defectQuantities).some(count => count > 0)
    }
  },
  created() {
    this.queryParams.pageNum = 1
  },
  mounted() {
    this.refreshData()
  },
  activated() {
    this.refreshData()
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (vm.refreshData) {
        vm.refreshData()
      }
    })
  },
  methods: {
    /** 刷新页面数据 */
    refreshData() {
      console.log('刷新进行中任务页面数据')
      this.queryParams.pageNum = 1

      // 确保用户信息获取完成后再加载数据
      this.getCurrentUserInfo().then(() => {
        console.log('用户信息已获取，开始加载数据')
        this.getList()
      }).catch(error => {
        console.error('获取用户信息失败，使用默认查询:', error)
        // 即使用户信息获取失败，也尝试加载数据（可能会返回空结果）
        this.getList()
      })
    },

    /** 获取当前登录用户信息 */
    getCurrentUserInfo() {
      return new Promise((resolve, reject) => {
        try {
          const userId = this.$store.getters.userId

          // 优先尝试从store中获取用户名
          const userName = this.$store.getters.name
          if (userId && userName) {
            this.currentUser = {
              userId: userId,
              nickName: userName
            }
            this.queryParams.assignee = userName
            console.log('从store获取当前登录用户:', this.currentUser)
          }

          // 调用个人信息API获取完整的用户信息，包括nickName
          getUserProfile().then(response => {
            const user = response.data.user
            if (userId && user.nickName) {
              this.currentUser = {
                userId: userId,
                nickName: user.nickName  // 使用nickName作为显示名称
              }
              // 设置查询参数中的执行人为当前用户的姓名（用于后端模糊查询）
              this.queryParams.assignee = user.nickName
              console.log('从API获取当前登录用户:', this.currentUser)
              resolve(this.currentUser)
            } else {
              console.warn('无法从API获取当前用户信息')
              if (this.currentUser.nickName) {
                resolve(this.currentUser)
              } else {
                reject(new Error('无法获取用户信息'))
              }
            }
          }).catch(error => {
            console.error('获取用户详细信息失败:', error)
            // 降级处理：如果API调用失败，使用全局状态中的name
            if (userId && userName) {
              this.currentUser = {
                userId: userId,
                nickName: userName
              }
              this.queryParams.assignee = userName
              console.log('降级处理，使用store中的用户信息:', this.currentUser)
              resolve(this.currentUser)
            } else {
              reject(error)
            }
          })
        } catch (error) {
          console.error('获取用户信息失败:', error)
          reject(error)
        }
      })
    },

    /** 查询任务列表 */
    getList(pagination) {
      this.loading = true

      // 如果没有用户信息，先尝试获取
      if (!this.currentUser.nickName) {
        console.warn('用户信息不存在，无法查询任务')
        this.loading = false
        this.groupedTasks = []
        this.total = 0
        // 触发任务数量变化事件
        this.$emit('task-count-change', { status: 'inProgress', count: 0 })
        return Promise.resolve()
      }

      if (!this.queryParams.assignee && this.currentUser.nickName) {
        this.queryParams.assignee = this.currentUser.nickName
      }

      const pageNum = pagination ? pagination.page : this.queryParams.pageNum
      const pageSize = pagination ? pagination.limit : this.queryParams.pageSize

      const queryParams = {
        ...this.queryParams,
        pageNum: pageNum,
        pageSize: pageSize,
        _t: Date.now()
      }

      // 固定查询进行中的工单
      queryParams.orderStatus = 'IN_PROGRESS'
      // 固定查询已下发的工单
      queryParams.orderDispatchStatus = 'DISPATCHED'

      console.log('查询参数:', queryParams)

      return getOrderDetailPage(queryParams).then(response => {
        console.log('进行中工单详细信息API响应:', response)

        let records = []
        let backendTotal = 0

        if (response.data && response.data.records !== undefined) {
          records = response.data.records || []
          backendTotal = response.data.total || 0
        } else if (response.rows !== undefined) {
          records = response.rows || []
          backendTotal = response.total || 0
        } else if (response.data && Array.isArray(response.data)) {
          records = response.data
          backendTotal = response.data.length
        } else {
          records = []
          backendTotal = 0
          console.warn('未知的API响应格式:', response)
        }

        this.processOrderData(records, backendTotal, pagination)
        this.loading = false
      }).catch(error => {
        console.error('获取进行中工单详细信息失败:', error)
        this.$message.error('获取进行中工单详细信息失败')
        this.groupedTasks = []
        this.total = 0
        this.loading = false
        return Promise.reject(error)
      })
    },

    /** 处理后端返回的工单数据 */
    processOrderData(orderRecords, backendTotal, pagination) {
      this.groupedTasks = []

      // 过滤只保留进行中且已下发的工单（双重保险，后端已按orderDispatchStatus='DISPATCHED'过滤）
      const filteredOrders = orderRecords.filter(order =>
        order.orderStatus === 'IN_PROGRESS'
      )

      filteredOrders.forEach(order => {
        let totalStepTasks = 0
        let myTasksCount = 0
        const processedProducts = []
        let hasMyTaskInOrder = false

        if (order.products && order.products.length > 0) {
          order.products.forEach(product => {
            const enrichedStepTasks = []
            let hasMyTask = false

            if (product.stepTasks && product.stepTasks.length > 0) {
              product.stepTasks.forEach(stepTask => {
                const enrichedStepTask = {
                  ...stepTask,
                  orderId: order.orderId,
                  orderCode: order.orderCode,
                  orderType: order.orderType,
                  orderStatus: order.orderStatus,
                  orderCreatedTime: order.orderCreatedTime,
                  productId: product.productId,
                  productName: product.productName,
                  itemId: product.itemId,
                  itemName: product.itemName,
                  processRouteCode: product.processRouteCode,
                  taskLevel: product.taskLevel,
                  taskStatus: product.taskStatus,
                  orderTaskQuantity: product.orderTaskQuantity,
                  orderTaskCreateTime: product.orderTaskCreateTime,
                  expectedTime: product.expectedTime,
                  completedTime: product.completedTime,
                  isDefer: product.isDefer
                }

                enrichedStepTasks.push(enrichedStepTask)
                totalStepTasks++

                if (stepTask.assignee === this.currentUser.nickName) {
                  hasMyTask = true
                  hasMyTaskInOrder = true
                }
              })
            }

            if (hasMyTask) {
              myTasksCount++
            }

            processedProducts.push({
              ...product,
              stepTasks: enrichedStepTasks
            })
          })
        }

        if (hasMyTaskInOrder) {
          const orderKey = `order_${order.orderId}`
          this.groupedTasks.push({
            key: orderKey,
            orderId: order.orderId,
            orderCode: order.orderCode,
            orderType: order.orderType,
            orderStatus: order.orderStatus,
            orderCreatedTime: order.orderCreatedTime,
            products: processedProducts,
            totalStepTasks: totalStepTasks,
            myTasksCount: myTasksCount,
            expanded: this.expandedGroups[orderKey] !== false
          })
        }
      })

      this.total = backendTotal

      if (pagination) {
        this.queryParams.pageNum = pagination.page
        this.queryParams.pageSize = pagination.limit
      }

      // 触发任务数量变化事件
      this.$emit('task-count-change', { status: 'inProgress', count: this.total })
    },

    /** 显示工单详情对话框 */
    showWorkOrderDetail(order) {
      this.selectedOrder = order
      this.workOrderDialogVisible = true
      // 自动查询库存信息
      this.queryInventoryForOrder(order)
    },

    /** 查询工单中所有产品的库存信息 */
    async queryInventoryForOrder(order) {
      if (!order.products || order.products.length === 0) {
        return
      }

      this.inventoryLoading = true
      this.inventoryData = {}

      try {
        // 为每个产品查询库存信息
        const inventoryPromises = order.products.map(product =>
          this.queryInventoryForProduct(product)
        )

        await Promise.all(inventoryPromises)
      } catch (error) {
        console.error('查询库存信息失败:', error)
        this.$message.error('查询库存信息失败')
      } finally {
        this.inventoryLoading = false
      }
    },

    /** 查询单个产品的库存信息 */
    async queryInventoryForProduct(product) {
      try {
        const productName = product.productName
        const styleId = product.styleId || 0

        // 并发调用三个库存查询API
        const [smdResponse, lineResponse, rawResponse] = await Promise.all([
          findByNameAndStyleId(productName, styleId).catch(error => {
            console.warn('贴片仓库存查询失败:', error)
            return { data: {} }
          }),
          findByNameAndStyleIdTwo(productName, styleId).catch(error => {
            console.warn('线边仓库存查询失败:', error)
            return { data: {} }
          }),
          findByName(productName).catch(error => {
            console.warn('原料仓库存查询失败:', error)
            return { data: {} }
          })
        ])

        // 处理贴片仓数据
        const smdStockData = smdResponse.data[productName] || []
        const smdStock = {
          upperBoard: smdStockData.find(item => item.boardType === '上板')?.currentStock || 0,
          lowerBoard: smdStockData.find(item => item.boardType === '下板')?.currentStock || 0
        }

        // 处理线边仓数据
        const lineStockData = lineResponse.data[productName] || []
        const lineStock = {
          upperBoard: lineStockData.find(item => item.boardType === '上板')?.currentStock || 0,
          lowerBoard: lineStockData.find(item => item.boardType === '下板')?.currentStock || 0
        }

        // 处理原料仓数据
        const rawStockData = rawResponse.data[productName] || []
        const rawStock = {
          upperBoard: rawStockData.find(item => item.boardType === '上板')?.currentStock || 0,
          lowerBoard: rawStockData.find(item => item.boardType === '下板')?.currentStock || 0
        }

        // 存储库存数据
        this.$set(this.inventoryData, product.productId, {
          smdStock,
          lineStock,
          rawStock
        })

      } catch (error) {
        console.error(`查询产品 ${product.productName} 库存信息失败:`, error)
        // 设置默认值，避免显示错误
        this.$set(this.inventoryData, product.productId, {
          smdStock: { upperBoard: 0, lowerBoard: 0 },
          lineStock: { upperBoard: 0, lowerBoard: 0 },
          rawStock: { upperBoard: 0, lowerBoard: 0 }
        })
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList({ page: 1, limit: this.queryParams.pageSize })
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      // 重置查询条件时保留当前用户的负责人筛选和固定的状态参数
      this.queryParams.assignee = this.currentUser.nickName
      this.queryParams.orderStatus = 'IN_PROGRESS'
      this.queryParams.orderDispatchStatus = 'DISPATCHED'
      this.handleQuery()
    },

    /** 查看按钮操作 */
    handleView(stepTask) {
      this.form = { ...stepTask }
      this.orderTypeText = this.getOrderTypeText(this.form.orderType)
      this.orderStatusText = '进行中'
      this.taskLevelText = this.getTaskLevelText(this.form.taskLevel)
      this.taskStatusText = this.getTaskStatusText(this.form.taskStatus)
      this.stepStatusText = this.getStepStatusText(this.form.isCompleted)

      this.dialog.open = true
      this.dialog.title = "进行中任务详情 - " + (this.form.productName || '未知产品')
      this.activeTab = 'orderInfo'
    },

    /** 开始任务按钮操作 */
    handleStart(row) {
      this.$modal.confirm('确认开始工序"' + row.stepName + '"吗？').then(() => {
        this.updateStepTaskStatus(row.stepTaskId, 1, row.orderId)
      }).catch(() => {})
    },

    /** 完成任务按钮操作 */
    handleComplete(row) {
      this.$modal.confirm('确认完成工序"' + row.stepName + '"吗？').then(() => {
        this.updateStepTaskStatus(row.stepTaskId, 2, row.orderId)
      }).catch(() => {})
    },

    /** 更新工序任务状态 */
    updateStepTaskStatus(stepTaskId, isCompleted, orderId) {
      // 当开始一个工序时，如果有关联的工单ID，则同时更新工单状态为处理中
      if (isCompleted === 1 && orderId) {
        updateOrderStatus(orderId, 'IN_PROGRESS').catch(error => {
          // 工单状态更新失败不应阻塞主流程，仅在控制台记录错误
          console.error('更新工单状态失败:', error)
        })
      }

      this.loading = true
      updateStepTaskStatus(stepTaskId, isCompleted).then(response => {
        this.$modal.msgSuccess("工序状态更新成功")
        // 保持当前页刷新
        this.getList({ page: this.queryParams.pageNum, limit: this.queryParams.pageSize }).then(() => {
          if (this.workOrderDialogVisible) {
            const updatedOrder = this.groupedTasks.find(o => o.orderId === this.selectedOrder.orderId)
            if (updatedOrder) {
              this.selectedOrder = updatedOrder
            } else {
              this.workOrderDialogVisible = false // 如果找不到，可能已被过滤，关闭对话框
            }
          }
        })

        if (this.dialog.open) {
          this.dialog.open = false
        }
      }).catch(error => {
        console.error('更新工序状态失败:', error)
        this.$modal.msgError("工序状态更新失败")
      }).finally(() => {
        this.loading = false
      })
    },

    /** 对话框中开始任务 */
    handleStartInDialog() {
      this.$modal.confirm('确认开始执行工序"' + this.form.stepName + '"吗？').then(() => {
        this.updateStepTaskStatus(this.form.stepTaskId, 1, this.form.orderId)
      }).catch(() => {})
    },

    /** 对话框中完成任务 */
    handleCompleteInDialog() {
      this.$modal.confirm('确认完成工序"' + this.form.stepName + '"吗？').then(() => {
        this.updateStepTaskStatus(this.form.stepTaskId, 2, this.form.orderId)
      }).catch(() => {})
    },

    /** 取消按钮 */
    cancel() {
      this.dialog.open = false
      this.reset()
    },

    // 表单重置
    reset() {
      this.form = {}
      this.orderTypeText = ""
      this.orderStatusText = ""
      this.taskLevelText = ""
      this.taskStatusText = ""
      this.stepStatusText = ""
      this.activeTab = 'orderInfo'
    },

    // 移除导航方法，由父组件统一管理

    /** 获取工单类型标签类型 */
    getOrderTypeTagType(orderType) {
      const typeMap = {
        'URGENT': 'danger',
        'NORMAL': 'primary'
      }
      return typeMap[orderType] || 'info'
    },

    /** 获取工单类型文本 */
    getOrderTypeText(orderType) {
      const typeMap = {
        'URGENT': '紧急',
        'NORMAL': '普通'
      }
      return typeMap[orderType] || orderType
    },

    /** 获取产品名称文本 */
    getProductNamesText(products) {
      if (!products || products.length === 0) {
        return '无产品信息'
      }

      // 构建产品名称和款式名称的组合文本
      const productTexts = products.map(p => {
        if (!p.productName) {
          return null
        }

        // 如果有款式名称，则显示为：产品名称 - 款式名称
        if (p.styleName && p.styleName.trim()) {
          return `${p.productName} - ${p.styleName}`
        }

        // 如果没有款式名称，只显示产品名称
        return p.productName
      }).filter(Boolean)

      if (productTexts.length === 0) {
        return '无产品名称'
      }

      if (productTexts.length === 1) {
        return productTexts[0]
      }

      return `${productTexts[0]} 等 ${productTexts.length} 个产品`
    },

    /** 获取任务状态文本 */
    getTaskStatusText(taskStatus) {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已完成'
      }
      return statusMap[taskStatus] || taskStatus
    },

    /** 获取任务级别文本 */
    getTaskLevelText(taskLevel) {
      const levelMap = {
        'NORMAL': '普通',
        'URGENT': '紧急'
      }
      return levelMap[taskLevel] || taskLevel
    },

    /** 获取工序状态标签类型 */
    getStepStatusTagType(isCompleted) {
      const statusMap = {
        0: 'info',
        1: 'warning',
        2: 'success'
      }
      return statusMap[isCompleted] || 'info'
    },

    /** 获取工序状态文本 */
    getStepStatusText(isCompleted) {
      const statusMap = {
        0: '未开始',
        1: '执行中',
        2: '已完成'
      }
      return statusMap[isCompleted] || '未知'
    },

    /** 解析时间 */
    parseTime(time, pattern) {
      if (!time) return ''
      if (typeof time === 'string') {
        return time.split(' ')[0]
      }
      if (time instanceof Date) {
        const year = time.getFullYear()
        const month = (time.getMonth() + 1).toString().padStart(2, '0')
        const day = time.getDate().toString().padStart(2, '0')
        return `${year}-${month}-${day}`
      }
      return ''
    },

    /** 提交不良品数按钮操作 */
    handleDefectSubmit(task) {
      this.currentStepTask = task
      this.defectDialogVisible = true

      // 使用stepId作为参数
      const stepId = task.stepId

      if (!stepId) {
        this.$message.error('工序ID不存在，无法查询缺陷类型')
        return
      }

      // 加载缺陷类型（会自动加载已有缺陷信息）
      this.loadDefectiveNames(stepId)
    },

    /** 对话框中提交不良品数 */
    handleDefectSubmitInDialog() {
      this.currentStepTask = this.form
      this.defectDialogVisible = true

      // 使用stepId作为参数
      const stepId = this.form.stepId

      if (!stepId) {
        this.$message.error('工序ID不存在，无法查询缺陷类型')
        return
      }

      // 加载缺陷类型（会自动加载已有缺陷信息）
      this.loadDefectiveNames(stepId)
    },

    /** 加载工序可能出现的缺陷类型 */
    loadDefectiveNames(stepId) {
      if (!stepId) {
        this.$message.error('工序ID不存在')
        return
      }

      this.defectLoading = true
      this.defectiveNames = []
      this.defectQuantities = {}

      getProcessRouteInfo(stepId).then(response => {
        if (response.data && response.data.defectiveNames && response.data.defectiveNames.trim()) {
          // 解析defectiveNames字符串，按逗号分割
          const defectNames = response.data.defectiveNames.split(',').map(name => name.trim()).filter(name => name)
          this.defectiveNames = defectNames

          // 初始化数量对象
          this.defectQuantities = {}
          defectNames.forEach(name => {
            this.$set(this.defectQuantities, name, 0)
          })

          // 初始化完成后，尝试加载已有缺陷信息
          this.$nextTick(() => {
            if (this.currentStepTask.stepTaskId) {
              this.loadExistingDefects(this.currentStepTask.stepTaskId)
            }
          })
        } else {
          this.defectiveNames = []
          const procedureName = response.data?.procedureName || '未知工序'

          // 简洁的提示信息
          this.$message({
            message: `工序"${procedureName}"暂未设置缺陷类型`,
            type: 'warning',
            duration: 3000
          })
        }
      }).catch(error => {
        if (error.response) {
          this.$message.error(`获取工序缺陷类型失败: ${error.response.status} ${error.response.data?.msg || error.message}`)
        } else {
          this.$message.error('网络请求失败，请检查网络连接')
        }
        this.defectiveNames = []
      }).finally(() => {
        this.defectLoading = false
      })
    },

    /** 加载已有的缺陷信息 */
    loadExistingDefects(stepTaskId) {
      if (!stepTaskId) {
        return
      }

      getStepTaskDetail(stepTaskId).then(response => {
        console.log('加载已有缺陷信息响应:', response)

        if (response.data && response.data.defectInfo) {
          const existingDefects = response.data.defectInfo || {}
          console.log('已有缺陷信息:', existingDefects)

          // 将已有缺陷信息合并到defectQuantities中
          Object.keys(existingDefects).forEach(defectName => {
            if (this.defectQuantities.hasOwnProperty(defectName)) {
              this.$set(this.defectQuantities, defectName, existingDefects[defectName] || 0)
            }
          })

          console.log('合并后的缺陷数量:', this.defectQuantities)
        }
      }).catch(error => {
        console.error('加载已有缺陷信息失败:', error)
        // 这里不显示错误消息，因为可能是首次提交，没有历史数据是正常的
      })
    },

    /** 提交缺陷信息 */
    submitDefects() {
      // 获取所有缺陷数据（包括数量为0的，用于覆盖之前的数据）
      const allDefects = {}
      Object.keys(this.defectQuantities).forEach(defectName => {
        const quantity = this.defectQuantities[defectName] || 0
        allDefects[defectName] = quantity
      })

      // 过滤出数量大于0的缺陷用于显示
      const validDefects = {}
      Object.keys(allDefects).forEach(defectName => {
        if (allDefects[defectName] > 0) {
          validDefects[defectName] = allDefects[defectName]
        }
      })

      // 提交所有数据（包括0值，实现覆盖更新）
      const submitData = {
        stepTaskId: this.currentStepTask.stepTaskId,
        defects: allDefects
      }

      this.submittingDefects = true
      addDefects(submitData).then(response => {
        if (Object.keys(validDefects).length > 0) {
          this.$message.success('不良品数提交成功')
        } else {
          this.$message.success('确认无不良品，提交成功')
        }
        this.defectDialogVisible = false
        this.resetDefectDialog()

        // 刷新列表数据
        this.getList({ page: this.queryParams.pageNum, limit: this.queryParams.pageSize })
      }).catch(error => {
        this.$message.error('提交不良品数失败')
      }).finally(() => {
        this.submittingDefects = false
      })
    },

    /** 重置缺陷对话框 */
    resetDefectDialog() {
      this.currentStepTask = {}
      this.defectiveNames = []
      this.defectQuantities = {}
      this.defectLoading = false
      this.submittingDefects = false
    },

    /** 减少缺陷数量 */
    decreaseDefectCount(defectName) {
      if (this.defectQuantities[defectName] > 0) {
        this.defectQuantities[defectName]--
      }
    },

    /** 增加缺陷数量 */
    increaseDefectCount(defectName) {
      this.defectQuantities[defectName]++
    },

    /** 验证缺陷数量 */
    validateDefectCount(defectName, value) {
      const num = parseInt(value)
      if (isNaN(num) || num < 0) {
        this.defectQuantities[defectName] = 0
      } else if (num > 99999) {
        this.defectQuantities[defectName] = 99999
      } else {
        this.defectQuantities[defectName] = num
      }
    },

    /** 查看不良品按钮操作 */
    handleViewDefects(task) {
      this.currentStepTask = task;
      this.viewDefectDialogVisible = true;
      this.loadStepTaskDefects(task.stepTaskId);
    },

    /** 加载工序任务的缺陷信息 */
    loadStepTaskDefects(stepTaskId) {
      if (!stepTaskId) {
        this.$message.error('工序任务ID不存在')
        return
      }

      this.viewDefectLoading = true
      this.stepTaskDefectInfo = {}

      getStepTaskDetail(stepTaskId).then(response => {
        console.log('获取工序任务详细信息响应:', response)

        if (response.data && response.data.defectInfo) {
          this.stepTaskDefectInfo = response.data.defectInfo || {}
        } else {
          this.stepTaskDefectInfo = {}
        }
      }).catch(error => {
        console.error('获取工序任务详细信息失败:', error)
        if (error.response) {
          this.$message.error(`获取缺陷信息失败: ${error.response.status} ${error.response.data?.msg || error.message}`)
        } else {
          this.$message.error('网络请求失败，请检查网络连接')
        }
        this.stepTaskDefectInfo = {}
      }).finally(() => {
        this.viewDefectLoading = false
      })
    },

    /** 重置查看缺陷对话框 */
    resetViewDefectDialog() {
      this.currentStepTask = {}
      this.stepTaskDefectInfo = {}
      this.viewDefectLoading = false
    },

    // 查看BOM清单
    async viewBomList(productName,styleName) {
      if (!productName) {
        this.$message.warning('产品名称不能为空');
        return;
      }

      this.currentProductName = productName;
      this.currentStyleName = styleName;
      this.bomDialogVisible = true;
      this.bomLoading = true;
      this.bomData = [];

      try {
        const response = await getBomByModelAndStyle(productName,styleName);
        if (response.code === 200) {
          this.bomData = response.data || [];
          if (this.bomData.length === 0) {
            this.$message.info(`产品 ${productName} 暂无BOM清单数据`);
          }
        } else {
          this.$message.error(response.msg || '获取BOM清单失败');
        }
      } catch (error) {
        console.error('获取BOM清单失败:', error);
        this.$message.error('获取BOM清单失败，请稍后重试');
      } finally {
        this.bomLoading = false;
      }
    }
  }
}
</script>

<style scoped>
.in-progress-task-container {
  padding: 20px;
  background: transparent;
  min-height: auto;
}

.page-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color-1, #d9d9d9);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  color: var(--theme-color, #333333);
  font-size: 24px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info .el-tag {
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 4px;
}

.page-header h2::before {
  content: '';
  width: 4px;
  height: 20px;
  background: var(--color-warning, #f5a623);
  margin-right: 10px;
  border-radius: 2px;
}

.search-form {
  background: var(--base-item-bg, #fff);
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.12));
  border: 1px solid var(--border-color-1, #d9d9d9);
  transition: all 0.3s ease;
}

.search-form:hover {
  box-shadow: 0 4px 12px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.15));
}

.task-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  min-height: 400px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  grid-column: 1 / -1;
}

.work-order-card.in-progress-card {
  background: var(--base-item-bg, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.08));
  border: 1px solid var(--border-color-1, #e8eaec);
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  border-left: 4px solid var(--color-warning, #f5a623);
}

.work-order-card.in-progress-card:hover {
  box-shadow: 0 6px 16px var(--tag-shadow-color-1, rgba(0, 0, 0, 0.12));
  transform: translateY(-4px);
  border-color: var(--color-warning, #f5a623);
}

.card-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color-1, #e8eaec);
}

.order-code {
  font-size: 15px;
  font-weight: 600;
  color: var(--theme-color, #333);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-body {
  padding: 16px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--base-color-3, #555);
  font-size: 13px;
}
.product-summary span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-summary i {
  color: var(--color-warning, #f5a623);
  font-size: 16px;
}

.task-summary {
  display: flex;
  justify-content: space-around;
  background-color: var(--base-color-8, #f8f9fa);
  border-radius: 6px;
  padding: 12px 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
.summary-item span {
  font-size: 12px;
  color: var(--base-color-3, #7f7f7f);
}
.summary-item strong {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-color, #333);
}
.summary-item.my-tasks strong {
  color: var(--color-warning, #f5a623);
}

.card-footer {
  padding: 10px 16px;
  background-color: var(--base-color-8, #f8f9fa);
  border-top: 1px solid var(--border-color-1, #e8eaec);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--base-color-3, #7f7f7f);
}
.creation-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 任务清单表格样式 */
.task-table {
  border: 1px solid var(--border-color-1, #e0e0e0);
  border-radius: 6px;
  overflow: hidden;
  background-color: var(--base-item-bg, #fff);
}

.task-table-header,
.task-row {
  display: flex;
  align-items: center;
  padding: 0 16px;
  transition: background-color 0.3s;
}

.task-table-header {
  background-color: var(--base-color-8, #f8f9fa);
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 500;
  color: var(--base-color-3, #7f7f7f);
  border-bottom: 1px solid var(--border-color-1, #e0e0e0);
}

.task-row {
  padding: 16px;
  border-bottom: 1px solid var(--border-color-1, #eef0f4);
  font-size: 13px;
}

.task-row:last-child {
  border-bottom: none;
}

.task-row:hover {
  background-color: var(--table-row-hover-bg, #f5f7fa);
}

.task-row.my-task {
  border-left: 3px solid var(--color-primary, #409eff);
  font-weight: 500;
  position: relative;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.05),
    rgba(64, 158, 255, 0.08));
}

.task-row.processing {
  background: linear-gradient(135deg,
    rgba(245, 166, 35, 0.1),
    rgba(245, 166, 35, 0.18));
  border-left: 3px solid var(--color-warning, #f5a623);
  position: relative;
}

.task-row.completed {
  background: linear-gradient(135deg,
    rgba(103, 194, 58, 0.08),
    rgba(103, 194, 58, 0.15));
  border-left: 3px solid var(--color-success, #67c23a);
  opacity: 0.9;
  position: relative;
}

/* 未开始状态的我的任务 */
.task-row.my-task:not(.processing):not(.completed) {
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.05),
    rgba(64, 158, 255, 0.08));
  border-left: 3px solid var(--color-primary, #409eff);
}

/* 执行中状态的我的任务 */
.task-row.my-task.processing {
  background: linear-gradient(135deg,
    rgba(245, 166, 35, 0.12),
    rgba(245, 166, 35, 0.20));
  border-left: 3px solid var(--color-warning, #f5a623);
  font-weight: 600;
}

/* 已完成状态的我的任务 */
.task-row.my-task.completed {
  background: linear-gradient(135deg,
    rgba(103, 194, 58, 0.10),
    rgba(103, 194, 58, 0.18));
  border-left: 3px solid var(--color-success, #67c23a);
  font-weight: 500;
}

/* 未开始状态的非我的任务 */
.task-row:not(.my-task):not(.processing):not(.completed) {
  background: var(--base-item-bg, #fff);
  border-left: 3px solid var(--border-color-1, #e8eaec);
}

/* 列定义 */
.task-cell {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}
.col-step { flex: 3; min-width: 220px; }
.col-status { flex: 1; min-width: 80px; }
.col-assignee { flex: 1; min-width: 100px; }
.col-time { flex: 2; min-width: 180px; }
.col-actions { flex: 1; min-width: 120px; text-align: right; flex-direction: row; align-items: center; justify-content: flex-end; gap: 8px;}

.step-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-color, #333);
}

.step-meta-info {
  font-size: 12px;
  color: var(--base-color-3, #888);
  margin-top: 4px;
}

.col-assignee .el-tag {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.col-time .time-entry {
  display: flex;
  align-items: center;
  line-height: 1.5;
}
.col-time .time-label {
  color: var(--base-color-3, #7f7f7f);
  margin-right: 6px;
  min-width: 30px;
}
.col-time .time-value.success {
  color: var(--color-success, #67c23a);
  font-weight: 500;
}

.col-actions .el-button {
  margin: 0;
}

/* 对话框样式 */
.work-order-dialog >>> .el-dialog__body {
  padding: 10px 24px 30px;
}

.dialog-product-container {
  max-height: 75vh;
  overflow-y: auto;
  padding-right: 10px;
}

.product-task-group {
  margin-bottom: 24px;
}
.product-task-group:last-child {
  margin-bottom: 0;
}

.product-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: var(--base-color-8, #f8f9fa);
  border-radius: 6px;
  margin-bottom: 12px;
}
.product-group-header .product-name {
  font-size: 15px;
  font-weight: 500;
  color: var(--theme-color, #333);
}
.product-group-header .product-meta {
  font-size: 12px;
  color: var(--base-color-3, #7f7f7f);
}
.product-group-header .product-quantity-tag {
  margin-left: auto;
}

.no-tasks-row {
  padding: 20px;
  text-align: center;
  color: var(--base-color-3, #7f7f7f);
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .col-time { display: none; }
  .col-step { flex: 2; }
}

@media (max-width: 992px) {
  .col-step { flex: 3; }
  .col-assignee { flex: 1.5; }
}

@media (max-width: 768px) {
  .in-progress-task-container {
    margin: 10px;
    padding: 15px;
  }

  .search-form {
    padding: 15px;
  }

  .page-header h2 {
    font-size: 20px;
  }

  .task-table-header {
    display: none;
  }

  .task-row {
    flex-direction: column;
    align-items: stretch;
    padding: 12px;
    gap: 10px;
  }

  .task-cell {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    border-bottom: 1px dashed var(--border-color-1, #eef0f4);
  }

  .task-row .task-cell:last-child {
    border-bottom: none;
  }

  .task-cell::before {
    content: attr(data-label);
    font-weight: 500;
    color: var(--base-color-3, #7f7f7f);
    margin-right: 10px;
  }

  .col-step::before { content: '工序'; }
  .col-status::before { content: '状态'; }
  .col-assignee::before { content: '负责人'; }
  .col-time::before { content: '时间'; }
  .col-actions::before { content: '操作'; }

  .col-time { display: flex; }
  .step-name { font-size: 15px; }
  .col-actions { justify-content: flex-start; }
}

/* 主题适配 */
.theme-dark .in-progress-task-container {
  background: var(--base-main-bg, #0f172a);
}

.theme-dark .search-form {
  background: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .work-order-card {
  background: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .work-order-card:hover {
  background: var(--base-item-bg, #334155);
  border-color: var(--current-color, #3671e8);
}

.theme-dark .task-row {
  background: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .task-row:hover {
  background-color: var(--table-row-hover-bg, #334155);
}

/* 深色主题下的工序状态样式 */
.theme-dark .task-row.my-task:not(.processing):not(.completed) {
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.12),
    rgba(64, 158, 255, 0.18));
  border-left: 3px solid var(--color-primary, #409eff);
}

.theme-dark .task-row.my-task.processing {
  background: linear-gradient(135deg,
    rgba(245, 166, 35, 0.18),
    rgba(245, 166, 35, 0.25));
  border-left: 3px solid var(--color-warning, #f5a623);
}

.theme-dark .task-row.my-task.completed {
  background: linear-gradient(135deg,
    rgba(103, 194, 58, 0.15),
    rgba(103, 194, 58, 0.22));
  border-left: 3px solid var(--color-success, #67c23a);
}

.theme-dark .task-row.processing:not(.my-task) {
  background: linear-gradient(135deg,
    rgba(245, 166, 35, 0.15),
    rgba(245, 166, 35, 0.22));
  border-left: 3px solid var(--color-warning, #f5a623);
}

.theme-dark .task-row.completed:not(.my-task) {
  background: linear-gradient(135deg,
    rgba(103, 194, 58, 0.12),
    rgba(103, 194, 58, 0.18));
  border-left: 3px solid var(--color-success, #67c23a);
}

.theme-dark .task-row:not(.my-task):not(.processing):not(.completed) {
  background: var(--base-item-bg, #1e293b);
  border-left: 3px solid var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .product-group-header {
  background-color: var(--base-color-8, #334155);
}

.theme-dark .step-name {
  color: var(--theme-color, #f1f5f9);
}

/* 提交不良品数对话框样式 */
.defect-form {
  padding: 10px 0;
}

.step-info {
  background-color: var(--base-color-8, #f8f9fa);
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.step-info h4 {
  margin: 0 0 10px 0;
  color: var(--theme-color, #333);
  font-size: 16px;
  font-weight: 500;
}

.step-info p {
  margin: 5px 0;
  color: var(--base-color-3, #666);
  font-size: 14px;
}

.defect-selection h4 {
  margin: 0 0 15px 0;
  color: var(--theme-color, #333);
  font-size: 16px;
  font-weight: 500;
}

.defect-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-1, #e8eaec);
}

.defect-item:last-child {
  border-bottom: none;
}

.defect-name {
  flex: 1;
  font-size: 14px;
  color: var(--theme-color, #333);
  font-weight: 500;
}

.defect-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0;
}

.defect-input-wrapper .el-button {
  border-radius: 0;
  margin: 0;
  min-width: 30px;
  height: 32px;
}

/* 主题适配 */
.theme-dark .step-info {
  background-color: var(--base-color-8, #334155);
}

.theme-dark .step-info h4,
.theme-dark .defect-selection h4 {
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .step-info p {
  color: var(--base-color-3, #94a3b8);
}

.theme-dark .defect-name {
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .defect-item {
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

/* 查看缺陷信息对话框样式 */
.view-defect-form {
  padding: 10px 0;
}

.defect-info-display h4 {
  margin: 0 0 15px 0;
  color: var(--theme-color, #333);
  font-size: 16px;
  font-weight: 500;
}

.defect-info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-1, #e8eaec);
}

.defect-info-item:last-child {
  border-bottom: none;
}

.defect-info-item .defect-name {
  flex: 1;
  font-size: 14px;
  color: var(--theme-color, #333);
  font-weight: 500;
}

.defect-info-item .defect-quantity {
  flex-shrink: 0;
}

.defect-summary-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color-1, #e8eaec);
}

/* 深色主题适配 */
.theme-dark .defect-info-display h4 {
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .defect-info-item {
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .defect-info-item .defect-name {
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .defect-summary-info {
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

/* 库存信息样式 */
.inventory-info-section {
  margin: 12px 0;
  background-color: var(--base-color-9, #fcfdff);
  border: 1px solid var(--border-color-1, #e8eaec);
  border-radius: 6px;
  overflow: hidden;
}

.inventory-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background-color: var(--base-color-8, #f8f9fa);
  border-bottom: 1px solid var(--border-color-1, #e8eaec);
  font-size: 13px;
  font-weight: 500;
  color: var(--theme-color, #333);
}

.inventory-header i {
  color: var(--current-color, #3671e8);
  font-size: 14px;
}

.inventory-content {
  padding: 12px;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.warehouse-info {
  flex: 1;
  min-width: 200px;
  background-color: var(--base-item-bg, #fff);
  border: 1px solid var(--border-color-1, #e8eaec);
  border-radius: 4px;
  overflow: hidden;
}

.warehouse-title {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 10px;
  background-color: var(--base-color-8, #f8f9fa);
  border-bottom: 1px solid var(--border-color-1, #e8eaec);
  font-size: 12px;
  font-weight: 500;
  color: var(--base-color-3, #666);
}

.warehouse-title i {
  color: var(--current-color, #3671e8);
  font-size: 12px;
}

.stock-items {
  padding: 10px;
}

.stock-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.board-type {
  color: var(--base-color-3, #666);
  font-weight: 500;
}

.stock-value {
  color: var(--color-success, #67c23a);
  font-weight: 600;
  font-size: 13px;
}

.no-data {
  color: var(--base-color-3, #999);
  font-size: 12px;
  text-align: center;
  padding: 5px 0;
}

.inventory-error {
  padding: 15px;
  text-align: center;
  color: var(--color-warning, #e6a23c);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 暗色主题 - 库存信息样式 */
.theme-dark .inventory-info-section {
  background-color: var(--base-color-8, #334155);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .inventory-header {
  background-color: var(--base-color-8, #334155);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
  color: var(--theme-color, #f1f5f9);
}

.theme-dark .warehouse-info {
  background-color: var(--base-item-bg, #1e293b);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
}

.theme-dark .warehouse-title {
  background-color: var(--base-color-8, #334155);
  border-color: var(--border-color-1, rgba(71, 85, 105, 0.8));
  color: var(--base-color-3, #94a3b8);
}

.theme-dark .board-type {
  color: var(--base-color-3, #94a3b8);
}

.theme-dark .no-data {
  color: var(--base-color-3, #94a3b8);
}

.theme-dark .inventory-error {
  color: var(--color-warning, #e6a23c);
}
</style>


<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>
        <i class="el-icon-time"></i>
        仓库操作历史
      </h2>
      <p class="description">实时追踪每一次库存变动记录</p>
      <div class="header-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalOperations }}</div>
          <div class="stat-label">总操作数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ todayOperations }}</div>
          <div class="stat-label">今日操作</div>
        </div>
      </div>
    </div>

    <!-- 搜索条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" class="search-form">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="操作类型" prop="operationType">
            <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable style="width: 100%">
              <el-option 
                v-for="type in operationTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value">
                <i :class="type.icon" style="margin-right: 8px;"></i>
                {{ type.label }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="区域编码" prop="zoneCode">
            <el-input
              v-model="queryParams.zoneCode"
              placeholder="请输入区域编码"
              clearable
              style="width: 100%"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物料名称" prop="materialName">
            <el-input
              v-model="queryParams.materialName"
              placeholder="请输入物料名称"
              clearable
              style="width: 100%"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="操作人员" prop="operator">
            <el-input
              v-model="queryParams.operator"
              placeholder="请输入操作人员"
              clearable
              style="width: 100%"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 100%"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12" style="text-align: right;">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 视图切换和操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="12">
        <el-radio-group v-model="viewMode" @change="handleViewChange" size="small">
          <el-radio-button label="timeline">
            <i class="el-icon-time"></i> 时间线视图
          </el-radio-button>
          <el-radio-button label="table">
            <i class="el-icon-s-order"></i> 表格视图
          </el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="12" style="text-align: right;">
        <el-button type="success" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="refreshData">刷新</el-button>
      </el-col>
    </el-row>

    <!-- 时间线视图 -->
    <div v-if="viewMode === 'timeline'" class="timeline-view">
      <div v-loading="loading" class="timeline-container">
        <div v-if="groupedOperations.length === 0" class="empty-state">
          <i class="el-icon-document-delete"></i>
          <p>暂无操作记录</p>
        </div>

        <div v-for="(dayGroup, index) in groupedOperations" :key="index" class="timeline-day-group">
          <div class="day-header">
            <div class="day-info">
              <div class="day-date">{{ formatDayDate(dayGroup.date) }}</div>
              <div class="day-count">{{ dayGroup.operations.length }} 条操作记录</div>
            </div>
          </div>

          <div class="timeline-items">
            <div 
              v-for="(operation, opIndex) in dayGroup.operations" 
              :key="operation.logId"
              class="timeline-item"
              :class="getTimelineItemClass(operation)"
              @click="viewOperationDetail(operation)">
              
              <div class="timeline-marker">
                <i :class="getOperationIcon(operation.operationType)"></i>
              </div>
              
              <div class="timeline-content">
                <el-card class="operation-card" shadow="hover">
                  <div class="card-header">
                    <div class="operation-info">
                      <el-tag 
                        :type="getOperationTypeTag(operation.operationType)" 
                        size="small"
                        effect="dark">
                        {{ getOperationTypeName(operation.operationType) }}
                      </el-tag>
                      <span class="operation-time">{{ formatTime(operation.operationTime) }}</span>
                    </div>
                  </div>
                  
                  <div class="card-content">
                    <div class="material-section">
                      <div class="material-info">
                        <div class="material-name">{{ operation.materialName }}</div>
                        <div class="material-id">ID: {{ operation.materialId }}</div>
                      </div>
                    </div>
                    
                    <div class="quantity-section">
                      <div class="quantity-flow">
                        <div class="quantity-item">
                          <span class="quantity-label">操作前</span>
                          <span class="quantity-value">{{ operation.quantityBefore }}</span>
                        </div>
                        <div class="quantity-arrow">
                          <i class="el-icon-right"></i>
                          <span 
                            :class="['quantity-change', operation.quantityChange > 0 ? 'positive' : 'negative']">
                            {{ operation.quantityChange > 0 ? '+' : '' }}{{ operation.quantityChange }}
                          </span>
                        </div>
                        <div class="quantity-item">
                          <span class="quantity-label">操作后</span>
                          <span class="quantity-value">{{ operation.quantityAfter }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="card-footer">
                    <div class="footer-item">
                      <i class="el-icon-user"></i>
                      <span>{{ operation.operator }}</span>
                    </div>
                    <div class="footer-item">
                      <i class="el-icon-location"></i>
                      <span>{{ operation.zoneCode }}</span>
                    </div>
                    <div class="footer-item" v-if="operation.batchNo">
                      <i class="el-icon-collection-tag"></i>
                      <span>{{ operation.batchNo }}</span>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="table-view">
      <el-card shadow="hover">
        <el-table v-loading="loading" :data="operationList" border stripe class="data-table">
          <el-table-column label="日志ID" align="center" prop="logId" width="80" />
          <el-table-column label="区域编码" align="center" prop="zoneCode" width="100" />
          <el-table-column label="操作类型" align="center" prop="operationType" width="100">
            <template slot-scope="scope">
              <el-tag :type="getOperationTypeTag(scope.row.operationType)" size="mini">
                {{ getOperationTypeName(scope.row.operationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="物料ID" align="center" prop="materialId" width="100" />
          <el-table-column label="物料名称" align="center" prop="materialName" min-width="150" show-overflow-tooltip />
          <el-table-column label="操作前数量" align="center" prop="quantityBefore" width="110">
            <template slot-scope="scope">
              <span class="quantity-before">{{ scope.row.quantityBefore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量变化" align="center" prop="quantityChange" width="100">
            <template slot-scope="scope">
              <span :class="['quantity-change-value', scope.row.quantityChange > 0 ? 'text-success' : 'text-danger']">
                <i :class="scope.row.quantityChange > 0 ? 'el-icon-top' : 'el-icon-bottom'" style="margin-right: 4px;"></i>
                {{ scope.row.quantityChange > 0 ? '+' : '' }}{{ scope.row.quantityChange }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作后数量" align="center" prop="quantityAfter" width="110">
            <template slot-scope="scope">
              <span class="quantity-after">{{ scope.row.quantityAfter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="批次号" align="center" prop="batchNo" width="100" />
          <el-table-column label="来源单据" align="center" prop="sourceDocument" width="150" />
          <el-table-column label="操作人员" align="center" prop="operator" width="100" />
          <el-table-column label="操作时间" align="center" prop="operationTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="viewOperationDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="操作记录详情" :visible.sync="detailVisible" width="800px" append-to-body class="detail-dialog">
      <div class="detail-container" v-if="detailData">
        <el-form ref="detailForm" :model="detailData" label-width="120px" class="detail-form">
          <div class="detail-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="日志ID：">
                  <span class="detail-value">{{ detailData.logId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区域编码：">
                  <span class="detail-value">{{ detailData.zoneCode }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作类型：">
                  <el-tag :type="getOperationTypeTag(detailData.operationType)">
                    {{ getOperationTypeName(detailData.operationType) }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人员：">
                  <span class="detail-value">{{ detailData.operator }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="detail-section">
            <div class="section-title">物料信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="物料ID：">
                  <span class="detail-value">{{ detailData.materialId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物料名称：">
                  <span class="detail-value">{{ detailData.materialName }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="detail-section">
            <div class="section-title">数量变化</div>
            <div class="quantity-detail">
              <div class="quantity-flow-detail">
                <div class="flow-step">
                  <div class="step-circle before">{{ detailData.quantityBefore }}</div>
                  <div class="step-label">操作前数量</div>
                </div>
                <div class="flow-arrow">
                  <i class="el-icon-right"></i>
                  <div class="change-amount" :class="detailData.quantityChange > 0 ? 'positive' : 'negative'">
                    {{ detailData.quantityChange > 0 ? '+' : '' }}{{ detailData.quantityChange }}
                  </div>
                </div>
                <div class="flow-step">
                  <div class="step-circle after">{{ detailData.quantityAfter }}</div>
                  <div class="step-label">操作后数量</div>
                </div>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <div class="section-title">其他信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="批次号：">
                  <span class="detail-value">{{ detailData.batchNo || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="来源单据：">
                  <span class="detail-value">{{ detailData.sourceDocument || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作时间：">
                  <span class="detail-value">{{ parseTime(detailData.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="验证状态：">
                  <el-tag :type="detailData.verificationStatus === '1' ? 'success' : 'info'">
                    {{ detailData.verificationStatus === '1' ? '已验证' : '未验证' }}
                  </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="操作原因：">
                  <span class="detail-value">{{ detailData.operationReason || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注：">
                  <span class="detail-value">{{ detailData.remark || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOperationLogs, getOperationLogInfo, exportOperationLog } from "@/api/inventory/operation";

export default {
  name: "InventoryOperationHistory",
  data() {
    return {
      // 视图模式
      viewMode: 'timeline',
      
      // 加载状态
      loading: true,
      
      // 数据
      operationList: [],
      total: 0,
      totalOperations: 0,
      todayOperations: 0,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        zoneCode: null,
        operationType: null,
        materialName: null,
        operator: null
      },
      dateRange: [],
      
      // 详情
      detailVisible: false,
      detailData: {},

      // 操作类型配置
      operationTypes: [
        { label: '入库', value: 'inbound', icon: 'el-icon-upload2' },
        { label: '出库', value: 'outbound', icon: 'el-icon-download' },
        { label: '移库', value: 'transfer', icon: 'el-icon-sort' },
        { label: '调整', value: 'adjust', icon: 'el-icon-edit' },
        { label: '盘点', value: 'check', icon: 'el-icon-view' }
      ],

      // 时间选择器配置
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },

  computed: {
    // 按日期分组的操作记录
    groupedOperations() {
      const groups = {};
      this.operationList.forEach(operation => {
        const date = this.parseTime(operation.operationTime, '{y}-{m}-{d}');
        if (!groups[date]) {
          groups[date] = {
            date: date,
            operations: []
          };
        }
        groups[date].operations.push(operation);
      });
      
      return Object.values(groups).sort((a, b) => 
        new Date(b.date) - new Date(a.date)
      );
    }
  },

  created() {
    this.getList();
    this.getStatistics();
  },

  methods: {
    /** 查询操作日志列表 */
    getList() {
      this.loading = true;
      
      let params = { ...this.queryParams };
      
      if (this.dateRange && this.dateRange.length === 2) {
        if (!params.params) {
          params.params = {};
        }
        params.params.beginOperationTime = this.dateRange[0] + ' 00:00:00';
        params.params.endOperationTime = this.dateRange[1] + ' 23:59:59';
      }
      
      getOperationLogs(params).then(response => {
        this.operationList = response.rows || this.getMockData();
        this.total = response.total || this.operationList.length;
        this.loading = false;
      }).catch(() => {
        this.operationList = this.getMockData();
        this.total = this.operationList.length;
        this.loading = false;
      });
    },

    /** 获取统计数据 */
    getStatistics() {
      // 这里可以调用统计接口，暂时使用模拟数据
      this.totalOperations = 3247;
      this.todayOperations = 87;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 刷新数据 */
    refreshData() {
      this.getList();
      this.getStatistics();
    },

    /** 视图切换 */
    handleViewChange(mode) {
      this.viewMode = mode;
    },

    /** 查看详情按钮操作 */
    viewOperationDetail(row) {
      this.detailData = row;
      this.detailVisible = true;
    },

    /** 导出按钮操作 */
    async handleExport() {
      try {
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍候...',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        let params = { ...this.queryParams };
        
        if (this.dateRange && this.dateRange.length === 2) {
          if (!params.params) {
            params.params = {};
          }
          params.params.beginOperationTime = this.dateRange[0] + ' 00:00:00';
          params.params.endOperationTime = this.dateRange[1] + ' 23:59:59';
        }

        const response = await exportOperationLog(params);
        
        if (!response || response.size === 0) {
          loading.close();
          this.$message.warning('暂无数据可导出');
          return;
        }

        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        const now = new Date();
        const dateStr = now.getFullYear() + 
          String(now.getMonth() + 1).padStart(2, '0') + 
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') + 
          String(now.getMinutes()).padStart(2, '0') + 
          String(now.getSeconds()).padStart(2, '0');
        
        link.download = `仓库操作历史_${dateStr}.xlsx`;
        
        document.body.appendChild(link);
        link.click();
        
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        loading.close();
        this.$message.success('导出成功');
        
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },

    /** 获取操作类型标签 */
    getOperationTypeTag(type) {
      const typeMap = {
        inbound: 'success',
        outbound: 'danger',
        transfer: 'info',
        adjust: 'warning',
        check: 'info'
      };
      return typeMap[type] || 'info';
    },

    /** 获取操作类型名称 */
    getOperationTypeName(type) {
      const typeMap = {
        inbound: '入库',
        outbound: '出库',
        transfer: '移库',
        adjust: '调整',
        check: '盘点'
      };
      return typeMap[type] || '未知操作';
    },

    /** 获取操作图标 */
    getOperationIcon(type) {
      const iconMap = {
        inbound: 'el-icon-upload2',
        outbound: 'el-icon-download',
        transfer: 'el-icon-sort',
        adjust: 'el-icon-edit',
        check: 'el-icon-view'
      };
      return iconMap[type] || 'el-icon-document';
    },

    /** 获取时间线项目类名 */
    getTimelineItemClass(operation) {
      return `timeline-item-${operation.operationType}`;
    },

    /** 格式化时间 */
    formatTime(time) {
      return this.parseTime(time, '{h}:{i}');
    },

    /** 格式化日期 */
    formatDayDate(date) {
      const today = new Date();
      const targetDate = new Date(date);
      const diffTime = today.getTime() - targetDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays <= 1) return '今天';
      if (diffDays === 2) return '昨天';
      if (diffDays === 3) return '前天';
      
      return this.parseTime(date, '{y}年{m}月{d}日');
    },

    /** 模拟数据 */
    getMockData() {
      const operations = [];
      const operationTypes = ['inbound', 'outbound', 'transfer', 'adjust', 'check'];
      const materials = ['钢材原料', '塑料制品', '电子元件', '纺织品', '化工原料'];
      const operators = ['张三', '李四', '王五', '赵六', '钱七'];
      const zones = ['A001', 'A002', 'B001', 'B002', 'C001'];
      
      for (let i = 1; i <= 50; i++) {
        const quantityBefore = Math.floor(Math.random() * 500) + 50;
        const quantityChange = Math.floor(Math.random() * 200) - 100;
        
        operations.push({
          logId: 1000 + i,
          zoneCode: zones[Math.floor(Math.random() * zones.length)],
          operationType: operationTypes[Math.floor(Math.random() * operationTypes.length)],
          materialId: 'M' + String(i).padStart(3, '0'),
          materialName: materials[Math.floor(Math.random() * materials.length)],
          quantityBefore: quantityBefore,
          quantityChange: quantityChange,
          quantityAfter: quantityBefore + quantityChange,
          batchNo: 'B2024' + String(Math.floor(Math.random() * 1000)).padStart(3, '0'),
          sourceDocument: 'DOC' + String(i).padStart(3, '0'),
          operator: operators[Math.floor(Math.random() * operators.length)],
          operationTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
          operationReason: '正常操作',
          remark: '系统自动记录',
          verificationStatus: Math.random() > 0.5 ? '1' : '0'
        });
      }
      
      return operations.sort((a, b) => new Date(b.operationTime) - new Date(a.operationTime));
    }
  }
};
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--base-color-9);
  border-radius: 8px;
  position: relative;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: var(--base-color-1);
  font-size: 24px;
  display: flex;
  align-items: center;
}

.page-header h2 i {
  margin-right: 10px;
  color: var(--current-color);
}

.description {
  margin: 0 0 15px 0;
  color: var(--base-color-2);
  font-size: 14px;
}

.header-stats {
  display: flex;
  gap: 30px;
  position: absolute;
  right: 20px;
  top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: var(--current-color);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--base-color-3);
  margin-top: 5px;
}

/* 搜索表单样式 */
.search-form {
  background: var(--base-color-9);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
}

.search-form .el-form-item {
  margin-bottom: 18px;
}

.search-form .el-form-item__label {
  color: var(--base-color-2);
  font-weight: 500;
}

/* 时间线视图样式 */
.timeline-view {
  background: transparent;
  padding: 0 20px;
}

.timeline-container {
  width: 100%;
  max-width: none;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.empty-state {
  text-align: center;
  padding: 60px;
  color: var(--base-color-3);
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
  color: var(--base-color-6);
}

.timeline-day-group {
  margin-bottom: 40px;
  width: 100%;
}

.day-header {
  background: var(--current-color);
  color: #ffffff;
  padding: 15px 30px;
  border-radius: 6px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(var(--current-color-rgb), 0.2);
}

.day-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.day-date {
  font-size: 16px;
  font-weight: 600;
}

.day-count {
  font-size: 14px;
  opacity: 0.9;
}

.timeline-items {
  position: relative;
  padding-left: 60px;
  width: 100%;
}

.timeline-items::before {
  content: '';
  position: absolute;
  left: 40px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, var(--base-color-6), var(--current-color), var(--base-color-6));
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  margin-bottom: 25px;
  cursor: pointer;
  width: 100%;
}

.timeline-marker {
  position: absolute;
  left: -45px;
  top: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--current-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(var(--current-color-rgb), 0.3);
  z-index: 2;
  border: 4px solid var(--base-item-bg);
}

.timeline-content {
  margin-left: 20px;
  width: calc(100% - 20px);
}

.operation-card {
  border-radius: 8px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
  width: 100%;
  background: var(--base-item-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.operation-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: var(--current-color);
}

.card-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color-1);
}

.operation-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.operation-time {
  font-size: 14px;
  color: var(--base-color-3);
}

.card-content {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.material-section {
  background: rgba(var(--current-color-rgb), 0.05);
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--current-color);
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  margin-bottom: 8px;
}

.material-id {
  font-size: 12px;
  color: var(--base-color-3);
  background: var(--base-color-8);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.quantity-section {
  background: var(--base-color-8);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--border-color-1);
}

.quantity-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--base-item-bg);
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.quantity-item {
  text-align: center;
  flex: 1;
  padding: 10px;
}

.quantity-label {
  display: block;
  font-size: 12px;
  color: var(--base-color-3);
  margin-bottom: 8px;
  font-weight: 500;
}

.quantity-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--base-color-1);
}

.quantity-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;
  padding: 10px;
}

.quantity-arrow i {
  font-size: 16px;
  color: var(--current-color);
  margin-bottom: 5px;
}

.quantity-change {
  font-size: 14px;
  font-weight: 600;
}

.quantity-change.positive {
  color: #67C23A;
}

.quantity-change.negative {
  color: #F56C6C;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: var(--base-color-3);
  background: var(--base-color-9);
  border-radius: 6px;
  padding: 12px 15px;
  margin-top: 15px;
}

.footer-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: var(--base-item-bg);
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.footer-item i {
  margin-right: 6px;
  color: var(--current-color);
}

/* 时间线特殊样式 */
.timeline-item-inbound .timeline-marker {
  background: #67C23A;
}

.timeline-item-outbound .timeline-marker {
  background: #F56C6C;
}

.timeline-item-transfer .timeline-marker {
  background: var(--current-color);
}

.timeline-item-adjust .timeline-marker {
  background: #E6A23C;
}

.timeline-item-check .timeline-marker {
  background: var(--base-color-6);
}

/* 表格视图样式 */
.table-view {
  background: transparent;
}

.data-table {
  background-color: var(--base-item-bg);
  color: var(--base-color-1);
}

.data-table .el-table__header-wrapper th {
  background-color: var(--base-color-9) !important;
  color: var(--base-color-1) !important;
  font-weight: 600;
  border-bottom: 2px solid var(--border-color-1);
}

.data-table .el-table__row {
  background-color: var(--base-item-bg) !important;
  color: var(--base-color-1) !important;
  transition: all 0.3s;
}

.data-table .el-table__row:hover {
  background-color: var(--table-row-hover-bg) !important;
}

.data-table .el-table__row td {
  background-color: inherit !important;
  color: inherit !important;
  border-color: var(--border-color-1) !important;
}

.data-table .el-table__row:hover td {
  background-color: var(--table-row-hover-bg) !important;
}

/* 数量显示样式 */
.quantity-before,
.quantity-after {
  color: var(--base-color-1);
  font-weight: 500;
  font-size: 14px;
}

.quantity-change-value {
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
}

.text-success {
  color: #67C23A !important;
  font-weight: 600;
}

.text-danger {
  color: #F56C6C !important;
  font-weight: 600;
}

/* 详情弹窗样式 */
.detail-dialog .el-dialog {
  background-color: var(--base-item-bg);
  border-radius: 8px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
}

.detail-container {
  padding: 20px;
  background-color: var(--base-item-bg);
}

.detail-form .el-form-item {
  margin-bottom: 16px;
}

.detail-form .el-form-item__label {
  color: var(--base-color-2);
  font-weight: 500;
}

/* 详情分区样式 */
.detail-section {
  background-color: var(--base-color-9);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s;
}

.detail-section:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-7);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--base-color-7);
  display: inline-block;
}

/* 详情值样式 */
.detail-value {
  color: var(--base-color-1);
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--base-color-8);
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
}

/* 数量详情 */
.quantity-detail {
  margin-top: 20px;
}

.quantity-flow-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.flow-step {
  text-align: center;
}

.step-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: white;
  margin-bottom: 10px;
}

.step-circle.before {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.step-circle.after {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.step-label {
  font-size: 12px;
  color: #666;
}

.flow-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  font-size: 20px;
  color: #999;
}

.change-amount {
  font-size: 14px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  background: white;
}

.change-amount.positive {
  color: #67C23A;
}

.change-amount.negative {
  color: #F56C6C;
}

/* 星空主题特殊适配 */
.theme-starry-sky {
  .timeline-view {
    background: var(--base-body-background);
  }
  
  .page-header {
    background: var(--base-item-bg) !important;
    border-color: var(--border-color-1) !important;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2) !important;
  }
  
  .search-form {
    background: var(--base-item-bg) !important;
    border-color: var(--border-color-1) !important;
    box-shadow: 0 1px 4px rgba(30, 58, 138, 0.1) !important;
  }
  
  .operation-card {
    background: var(--base-item-bg) !important;
    border-color: var(--border-color-1) !important;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2) !important;
  }
  
  .operation-card:hover {
    box-shadow: 0 8px 24px rgba(30, 58, 138, 0.3) !important;
    border-color: var(--current-color) !important;
  }
  
  .material-section {
    background: rgba(30, 58, 138, 0.1) !important;
    border-color: var(--current-color) !important;
  }
  
  .quantity-section {
    background: var(--base-color-9) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .quantity-flow {
    background: var(--base-item-bg) !important;
    box-shadow: 0 1px 4px rgba(30, 58, 138, 0.1) !important;
  }
  
  .card-footer {
    background: var(--base-color-9) !important;
  }
  
  .footer-item {
    background: var(--base-item-bg) !important;
    box-shadow: 0 1px 3px rgba(30, 58, 138, 0.1) !important;
  }
  
  .material-id {
    background: var(--base-color-8) !important;
    color: var(--base-color-3) !important;
  }
  
  /* 星空主题表格修复 */
  .data-table {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
  }
  
  .data-table .el-table__header-wrapper th,
  .data-table .el-table__fixed-header-wrapper th {
    background-color: var(--base-color-9) !important;
    color: var(--base-color-1) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .data-table .el-table__row,
  .data-table .el-table__row td,
  .data-table tbody tr,
  .data-table tbody td {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .data-table .el-table__row:hover,
  .data-table .el-table__row:hover td,
  .data-table tbody tr:hover,
  .data-table tbody tr:hover td {
    background-color: var(--table-row-hover-bg) !important;
    color: var(--base-color-1) !important;
  }
  
  .data-table .el-table__body-wrapper {
    background-color: var(--base-item-bg) !important;
  }
  
  .data-table .el-table__empty-block {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-3) !important;
  }
  
  .data-table .el-table__empty-text {
    color: var(--base-color-3) !important;
  }
  
  /* 星空主题下的el-card样式 */
  .table-view .el-card {
    background-color: var(--base-item-bg) !important;
    border-color: var(--border-color-1) !important;
    color: var(--base-color-1) !important;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2) !important;
  }
  
  .table-view .el-card__body {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
  }
}

/* 深色主题特殊适配 */
.theme-dark {
  .page-header {
    box-shadow: 0 2px 8px rgba(58, 123, 153, 0.2) !important;
  }
  
  .search-form {
    box-shadow: 0 1px 4px rgba(58, 123, 153, 0.1) !important;
  }
  
  .operation-card {
    box-shadow: 0 2px 8px rgba(58, 123, 153, 0.2) !important;
  }
  
  .operation-card:hover {
    box-shadow: 0 8px 24px rgba(58, 123, 153, 0.3) !important;
  }
  
  .material-section {
    background: rgba(58, 123, 153, 0.1) !important;
  }
  
  .quantity-flow {
    box-shadow: 0 1px 4px rgba(58, 123, 153, 0.1) !important;
  }
  
  .footer-item {
    box-shadow: 0 1px 3px rgba(58, 123, 153, 0.1) !important;
  }
  
  /* 深色主题表格修复 */
  .data-table {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
  }
  
  .data-table .el-table__header-wrapper th,
  .data-table .el-table__fixed-header-wrapper th {
    background-color: var(--base-color-9) !important;
    color: var(--base-color-1) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .data-table .el-table__row,
  .data-table .el-table__row td,
  .data-table tbody tr,
  .data-table tbody td {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .data-table .el-table__row:hover,
  .data-table .el-table__row:hover td,
  .data-table tbody tr:hover,
  .data-table tbody tr:hover td {
    background-color: var(--table-row-hover-bg) !important;
    color: var(--base-color-1) !important;
  }
  
  .data-table .el-table__body-wrapper {
    background-color: var(--base-item-bg) !important;
  }
  
  .data-table .el-table__empty-block {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-3) !important;
  }
  
  .data-table .el-table__empty-text {
    color: var(--base-color-3) !important;
  }
  
  /* 深色主题下的el-card样式 */
  .table-view .el-card {
    background-color: var(--base-item-bg) !important;
    border-color: var(--border-color-1) !important;
    color: var(--base-color-1) !important;
  }
  
  .table-view .el-card__body {
    background-color: var(--base-item-bg) !important;
    color: var(--base-color-1) !important;
  }
}

/* 浅色主题特殊适配 */
.theme-light {
  .page-header {
    box-shadow: 0 2px 8px rgba(54, 113, 232, 0.1) !important;
  }
  
  .search-form {
    box-shadow: 0 1px 4px rgba(54, 113, 232, 0.05) !important;
  }
  
  .operation-card {
    box-shadow: 0 2px 8px rgba(54, 113, 232, 0.08) !important;
  }
  
  .operation-card:hover {
    box-shadow: 0 8px 24px rgba(54, 113, 232, 0.15) !important;
  }
  
  .material-section {
    background: rgba(54, 113, 232, 0.05) !important;
  }
  
  .quantity-flow {
    box-shadow: 0 1px 4px rgba(54, 113, 232, 0.05) !important;
  }
  
  .footer-item {
    box-shadow: 0 1px 3px rgba(54, 113, 232, 0.05) !important;
  }
}
@media (max-width: 1400px) {
  .timeline-view {
    padding: 0 15px;
  }
  
  .timeline-container {
    max-width: 1200px;
    margin: 0 auto;
  }
}

@media (max-width: 1200px) {
  .timeline-view {
    padding: 0 15px;
  }
  
  .timeline-container {
    max-width: 100%;
  }
  
  .header-stats {
    position: static;
    margin-top: 15px;
    justify-content: flex-start;
  }
  
  .timeline-items {
    padding-left: 50px;
  }
  
  .timeline-marker {
    left: -35px;
    width: 45px;
    height: 45px;
    font-size: 16px;
  }
  
  .timeline-content {
    margin-left: 15px;
    width: calc(100% - 15px);
  }
}

@media (max-width: 768px) {
  .timeline-view {
    padding: 0 10px;
  }
  
  .timeline-items {
    padding-left: 35px;
  }
  
  .timeline-marker {
    left: -25px;
    width: 35px;
    height: 35px;
    font-size: 14px;
    border: 2px solid white;
  }
  
  .timeline-content {
    margin-left: 10px;
    width: calc(100% - 10px);
  }
  
  .quantity-flow {
    flex-direction: column;
    gap: 15px;
    padding: 12px;
  }
  
  .quantity-arrow {
    transform: rotate(90deg);
    margin: 0;
  }
  
  .header-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .footer-item {
    justify-content: center;
  }
}
</style>
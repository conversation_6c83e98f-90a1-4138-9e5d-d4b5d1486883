<template>
  <div class="app-container inventory-history-container">
    <!-- 搜索区域卡片 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="区域编码" prop="zoneCode">
              <el-input
                v-model="queryParams.zoneCode"
                placeholder="请输入区域编码"
                clearable
                style="width: 100%"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作类型" prop="operationType">
              <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable style="width: 100%">
                <el-option label="入库" value="inbound" />
                <el-option label="出库" value="outbound" />
                <el-option label="移库" value="transfer" />
                <el-option label="调整" value="adjust" />
                <el-option label="盘点" value="check" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物料ID" prop="materialId">
              <el-input
                v-model="queryParams.materialId"
                placeholder="请输入物料ID"
                clearable
                style="width: 100%"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入物料名称"
                clearable
                style="width: 100%"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人员" prop="operator">
              <el-input
                v-model="queryParams.operator"
                placeholder="请输入操作人员"
                clearable
                style="width: 100%"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作时间">
              <el-date-picker
                v-model="dateRange"
                style="width: 100%"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格卡片 -->
    <el-card class="table-card" shadow="hover">
      <el-row :gutter="10" class="mb8 toolbar">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['inventory:operation:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['inventory:operation:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="operationList" @selection-change="handleSelectionChange" class="data-table">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="日志ID" align="center" prop="logId" width="80" />
        <el-table-column label="区域编码" align="center" prop="zoneCode" width="120" />
        <el-table-column label="操作类型" align="center" prop="operationType" width="100">
          <template slot-scope="scope">
            <el-tag :type="getOperationTypeTag(scope.row.operationType)">
              {{ getOperationTypeName(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="物料ID" align="center" prop="materialId" width="100" />
        <el-table-column label="物料名称" align="center" prop="materialName" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作前数量" align="center" prop="quantityBefore" width="110">
          <template slot-scope="scope">
            <span class="quantity-before">{{ scope.row.quantityBefore }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数量变化" align="center" prop="quantityChange" width="100">
          <template slot-scope="scope">
            <span :class="['quantity-change-value', scope.row.quantityChange > 0 ? 'text-success' : 'text-danger']">
              <i :class="scope.row.quantityChange > 0 ? 'el-icon-top' : 'el-icon-bottom'" style="margin-right: 4px;"></i>
              {{ scope.row.quantityChange > 0 ? '+' : '' }}{{ scope.row.quantityChange }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作后数量" align="center" prop="quantityAfter" width="110">
          <template slot-scope="scope">
            <span class="quantity-after">{{ scope.row.quantityAfter }}</span>
          </template>
        </el-table-column>
        <el-table-column label="批次号" align="center" prop="batchNo" width="100" />
        <el-table-column label="来源单据" align="center" prop="sourceDocument" width="150" />
        <el-table-column label="操作人员" align="center" prop="operator" width="100" />
        <el-table-column label="操作时间" align="center" prop="operationTime" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作原因" align="center" prop="operationReason" min-width="150" />
        <el-table-column label="验证状态" align="center" prop="verificationStatus" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.verificationStatus === '1' ? 'success' : 'info'">
              {{ scope.row.verificationStatus === '1' ? '已验证' : '未验证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['inventory:operation:query']"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['inventory:operation:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="操作日志详情" :visible.sync="detailOpen" width="800px" append-to-body class="detail-dialog" :close-on-click-modal="false">
      <div class="detail-container">
        <el-form ref="detailForm" :model="detailForm" label-width="120px" class="detail-form">
          <div class="detail-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="日志ID：">
                  <span class="detail-value">{{ detailForm.logId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区域编码：">
                  <span class="detail-value">{{ detailForm.zoneCode }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作类型：">
                  <el-tag :type="getOperationTypeTag(detailForm.operationType)">
                    {{ getOperationTypeName(detailForm.operationType) }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人员：">
                  <span class="detail-value">{{ detailForm.operator }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="detail-section">
            <div class="section-title">物料信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="物料ID：">
                  <span class="detail-value">{{ detailForm.materialId }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物料名称：">
                  <span class="detail-value">{{ detailForm.materialName }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="detail-section">
            <div class="section-title">数量变化</div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="操作前数量：">
                  <span class="detail-value quantity-before">{{ detailForm.quantityBefore }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数量变化：">
                  <span :class="['detail-value', 'quantity-change-value', detailForm.quantityChange > 0 ? 'text-success' : 'text-danger']">
                    {{ detailForm.quantityChange > 0 ? '+' : '' }}{{ detailForm.quantityChange }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="操作后数量：">
                  <span class="detail-value quantity-after">{{ detailForm.quantityAfter }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="detail-section">
            <div class="section-title">其他信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="批次号：">
                  <span class="detail-value">{{ detailForm.batchNo || '-' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="来源单据：">
                  <span class="detail-value">{{ detailForm.sourceDocument || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作时间：">
                  <span class="detail-value">{{ parseTime(detailForm.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="验证状态：">
                  <el-tag :type="detailForm.verificationStatus === '1' ? 'success' : 'info'">
                    {{ detailForm.verificationStatus === '1' ? '已验证' : '未验证' }}
                  </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="操作原因：">
                  <span class="detail-value">{{ detailForm.operationReason || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注：">
                  <span class="detail-value">{{ detailForm.remark || '-' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOperationLogs, getOperationLogInfo, removeOperationLog, batchRemoveOperationLog, exportOperationLog } from "@/api/inventory/operation";

export default {
  name: "InventoryOperationHistory",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作日志表格数据
      operationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        zoneCode: null,
        operationType: null,
        materialId: null,
        materialName: null,
        operator: null
      },
      // 详情表单参数
      detailForm: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询操作日志列表 */
    getList() {
      this.loading = true;
      
      // 构建查询参数，正确处理日期范围
      let params = { ...this.queryParams };
      
      // 添加日期范围参数到params对象中
      if (this.dateRange && this.dateRange.length === 2) {
        if (!params.params) {
          params.params = {};
        }
        params.params.beginOperationTime = this.dateRange[0] + ' 00:00:00';
        params.params.endOperationTime = this.dateRange[1] + ' 23:59:59';
      }
      
      getOperationLogs(params).then(response => {
        this.operationList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.detailOpen = true;
      const logId = row.logId || this.ids
      getOperationLogInfo(logId).then(response => {
        this.detailForm = response.data;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row.logId ? [row.logId] : this.ids;
      this.$modal.confirm('是否确认删除操作日志编号为"' + logIds + '"的数据项？').then(function() {
        if (logIds.length === 1) {
          return removeOperationLog(logIds[0]);
        } else {
          return batchRemoveOperationLog(logIds);
        }
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    async handleExport() {
      try {
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍候...',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 构建导出参数，包含日期范围
        let params = { ...this.queryParams };
        
        if (this.dateRange && this.dateRange.length === 2) {
          if (!params.params) {
            params.params = {};
          }
          params.params.beginOperationTime = this.dateRange[0] + ' 00:00:00';
          params.params.endOperationTime = this.dateRange[1] + ' 23:59:59';
        }

        // 调用导出API
        const response = await exportOperationLog(params);
        
        // 验证响应数据
        if (!response || response.size === 0) {
          loading.close();
          this.$message.warning('暂无数据可导出');
          return;
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // 生成文件名
        const now = new Date();
        const dateStr = now.getFullYear() + 
          String(now.getMonth() + 1).padStart(2, '0') + 
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') + 
          String(now.getMinutes()).padStart(2, '0') + 
          String(now.getSeconds()).padStart(2, '0');
        
        link.download = `出入库操作历史_${dateStr}.xlsx`;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        
        // 清理资源
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        loading.close();
        this.$message.success('导出成功');
        
      } catch (error) {
        console.error('导出失败:', error);
        if (error.response && error.response.status === 204) {
          this.$message.warning('暂无数据可导出');
        } else {
          this.$message.error('导出失败: ' + (error.message || '未知错误'));
        }
      }
    },
    getOperationTypeTag(type) {
      const typeMap = {
        inbound: 'success',
        outbound: 'danger',
        transfer: 'info',
        adjust: 'warning',
        check: 'info'
      };
      return typeMap[type] || 'info';
    },
    getOperationTypeName(type) {
      const typeMap = {
        inbound: '入库',
        outbound: '出库',
        transfer: '移库',
        adjust: '调整',
        check: '盘点'
      };
      return typeMap[type] || '未知操作';
    }
  }
};
</script>

<style scoped>
/* 容器和整体布局 */
.inventory-history-container {
  padding: 20px;
  background-color: transparent;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 20px;
  background-color: var(--base-item-bg);
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.search-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.search-form {
  padding: 0;
}

.search-form .el-form-item {
  margin-bottom: 18px;
}

.search-form .el-form-item__label {
  color: var(--base-color-2);
  font-weight: 500;
}

/* 表格卡片样式 */
.table-card {
  background-color: var(--base-item-bg);
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.table-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

/* 工具栏样式 */
.toolbar {
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color-1);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.data-table {
  background-color: transparent;
}

.data-table .el-table__header-wrapper th {
  background-color: var(--base-color-9);
  color: var(--base-color-1);
  font-weight: 600;
  border-bottom: 2px solid var(--border-color-1);
}

.data-table .el-table__row {
  transition: all 0.3s;
}

.data-table .el-table__row:hover {
  background-color: var(--table-row-hover-bg);
}

/* 数量显示样式 */
.quantity-before,
.quantity-after {
  color: var(--base-color-1);
  font-weight: 500;
  font-size: 14px;
}

.quantity-change-value {
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
}

.text-success {
  color: #67C23A !important;
  font-weight: 600;
}

.text-danger {
  color: #F56C6C !important;
  font-weight: 600;
}

/* 详情弹窗样式 */
.detail-dialog {
  .el-dialog {
    background-color: var(--base-item-bg);
    border-radius: 8px;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
  }

  .el-dialog__header {
    background-color: var(--base-color-9);
    border-bottom: 1px solid var(--border-color-1);
    padding: 20px;
    border-radius: 8px 8px 0 0;
  }

  .el-dialog__title {
    color: var(--base-color-1);
    font-size: 18px;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 0;
    background-color: var(--base-item-bg);
  }

  .el-dialog__footer {
    background-color: var(--base-color-9);
    border-top: 1px solid var(--border-color-1);
    padding: 15px 20px;
    border-radius: 0 0 8px 8px;
  }
}

/* 详情容器样式 */
.detail-container {
  padding: 20px;
  background-color: var(--base-item-bg);
}

.detail-form {
  .el-form-item {
    margin-bottom: 16px;
  }

  .el-form-item__label {
    color: var(--base-color-2);
    font-weight: 500;
  }
}

/* 详情分区样式 */
.detail-section {
  background-color: var(--base-color-9);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s;
}

.detail-section:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-7);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--base-color-7);
  display: inline-block;
}

/* 详情值样式 */
.detail-value {
  color: var(--base-color-1);
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--base-color-8);
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
}

.quantity-before,
.quantity-after {
  background-color: var(--base-color-8);
  color: var(--base-color-1);
  font-weight: 600;
  font-size: 15px;
}

.quantity-change-value {
  font-size: 16px;
  font-weight: 700;
  padding: 6px 12px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
}

/* 深色主题特殊处理 */
.theme-dark {
  .search-card,
  .table-card {
    box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.05);
  }

  .search-card:hover,
  .table-card:hover {
    box-shadow: 0 4px 20px 0 rgba(255, 255, 255, 0.08);
  }

  .detail-dialog .el-dialog {
    box-shadow: 0 8px 40px rgba(255, 255, 255, 0.1);
  }

  .detail-section {
    box-shadow: 0 1px 8px rgba(255, 255, 255, 0.02);
  }

  .detail-section:hover {
    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.05);
  }

  .text-success {
    color: #85ce61 !important;
  }

  .text-danger {
    color: #ff7878 !important;
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .inventory-history-container {
    padding: 15px;
  }

  .search-form .el-col-8 {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .inventory-history-container {
    padding: 10px;
  }

  .search-form .el-col-8 {
    width: 100%;
  }

  .detail-dialog {
    width: 95% !important;
  }
}
</style>
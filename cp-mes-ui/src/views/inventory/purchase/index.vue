<template>
  <div class="app-container">
    <div class="content-wrapper">
      <!-- 标签页导航 -->
      <el-tabs v-model="activeTab" type="card" class="purchase-tabs">
        <el-tab-pane label="采购订单管理" name="orders">
          <template slot="label">
            <i class="el-icon-document"></i>
            采购订单管理
          </template>
          <purchase-order-table v-if="activeTab === 'orders'" />
        </el-tab-pane>
        
        <el-tab-pane label="物品信息管理" name="items">
          <template slot="label">
            <i class="el-icon-goods"></i>
            物品信息管理
          </template>
          <item-table v-if="activeTab === 'items'" />
        </el-tab-pane>
        
        <el-tab-pane label="供应商管理" name="suppliers">
          <template slot="label">
            <i class="el-icon-office-building"></i>
            供应商管理
          </template>
          <supplier-table v-if="activeTab === 'suppliers'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import PurchaseOrderTable from './components/PurchaseOrderTable.vue'
import ItemTable from './components/ItemTable.vue'
import SupplierTable from './components/SupplierTable.vue'

export default {
  name: 'PurchaseIndex',
  components: {
    PurchaseOrderTable,
    ItemTable,
    SupplierTable
  },
  data() {
    return {
      activeTab: 'orders'
    }
  }
}
</script>

<style scoped>
.app-container {
  background: var(--base-body-background);
  min-height: 100vh;
  padding: 20px;
  transition: all 0.3s ease;
}

.content-wrapper {
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  border: 1px solid var(--border-color-1);
  min-height: 600px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.content-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--current-color);
  opacity: 0.8;
}

.purchase-tabs {
  height: 100%;
}

.purchase-tabs :deep(.el-tabs__header) {
  background: var(--base-main-bg);
  margin: 0;
  padding: 20px 20px 0;
  border-bottom: 1px solid var(--border-color-1);
}

.purchase-tabs :deep(.el-tabs__nav) {
  border: none;
}

.purchase-tabs :deep(.el-tabs__item) {
  background: var(--base-item-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 8px 8px 0 0;
  margin-right: 8px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  color: var(--base-color-2);
  font-weight: 500;
  transition: all 0.3s ease;
}

.purchase-tabs :deep(.el-tabs__item:hover) {
  background: var(--base-color-8);
  color: var(--current-color);
  transform: translateY(-2px);
}

.purchase-tabs :deep(.el-tabs__item.is-active) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
  box-shadow: 0 4px 12px rgba(54, 113, 232, 0.3);
}

.purchase-tabs :deep(.el-tabs__content) {
  padding: 0;
  height: calc(100% - 80px);
}

.purchase-tabs :deep(.el-tab-pane) {
  height: 100%;
  padding: 20px;
  background: var(--base-main-bg);
}

/* 主题适配 */
.theme-dark .app-container {
  background: var(--base-body-background);
}

.theme-dark .content-wrapper {
  background: var(--base-item-bg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  border-color: var(--border-color-2);
}

.theme-dark .purchase-tabs :deep(.el-tabs__header) {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark .purchase-tabs :deep(.el-tabs__item) {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
  color: var(--base-color-2);
}

.theme-dark .purchase-tabs :deep(.el-tabs__item:hover) {
  background: var(--base-color-8);
  color: var(--current-color);
}

.theme-dark .purchase-tabs :deep(.el-tabs__item.is-active) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
}

.theme-dark .purchase-tabs :deep(.el-tab-pane) {
  background: var(--base-item-bg);
}
</style>
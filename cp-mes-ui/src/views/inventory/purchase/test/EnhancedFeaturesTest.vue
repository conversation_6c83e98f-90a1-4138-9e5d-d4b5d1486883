<template>
  <div class="enhanced-features-test">
    <el-card>
      <div slot="header">
        <span>采购模块增强功能测试</span>
      </div>

      <el-form inline>
        <el-form-item label="采购订单ID:">
          <el-input
            v-model="testOrderId"
            placeholder="请输入采购订单ID"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="openTestDialog" :disabled="!testOrderId">
            打开增强功能测试
          </el-button>
        </el-form-item>
      </el-form>

      <el-divider></el-divider>

      <!-- 测试说明 -->
      <el-alert
        title="测试说明"
        type="info"
        :closable="false"
        show-icon
      >
        <div slot="description">
          <p><strong>测试步骤：</strong></p>
          <ol>
            <li>输入一个有效的采购订单ID</li>
            <li>点击"打开增强功能测试"按钮</li>
            <li>在弹出的对话框中测试三个功能：</li>
            <ul>
              <li><strong>图片管理</strong>：上传图片，检查是否正常显示和持久化</li>
              <li><strong>相关链接</strong>：添加链接，检查是否正常保存和显示</li>
              <li><strong>物流追踪</strong>：设置物流信息，检查是否正常显示</li>
            </ul>
            <li>刷新页面后重新打开，验证数据是否持久化</li>
            <li>打开浏览器开发者工具查看控制台日志</li>
          </ol>
        </div>
      </el-alert>

      <!-- 测试结果记录 -->
      <el-card style="margin-top: 20px" v-if="testResults.length > 0">
        <div slot="header">测试结果记录</div>
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :timestamp="result.timestamp"
            :type="result.success ? 'success' : 'danger'"
          >
            <h4>{{ result.feature }}</h4>
            <p>{{ result.description }}</p>
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.success ? '成功' : '失败' }}
            </el-tag>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-card>

    <!-- 增强功能测试对话框 -->
    <el-dialog
      title="采购订单增强功能测试"
      :visible.sync="testDialogVisible"
      width="1200px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="test-dialog-content">
        <el-alert
          title="请按顺序测试各项功能，并观察控制台日志输出"
          type="warning"
          :closable="false"
          show-icon
          style="margin-bottom: 20px"
        />

        <el-tabs v-model="activeTab" type="card">
          <!-- 图片管理测试 -->
          <el-tab-pane label="图片管理测试" name="images">
            <div class="test-section">
              <h4>测试项目：图片上传和显示</h4>
              <p>预期行为：上传成功后立即显示，刷新页面后仍然存在</p>

              <PurchaseImageUpload
                :purchase-order-id="testOrderId"
                @upload-success="handleImageUploadTest"
                @upload-error="handleImageErrorTest"
              />

              <div class="test-actions">
                <el-button @click="recordTestResult('图片管理', '图片上传功能正常', true)">
                  ✅ 测试通过
                </el-button>
                <el-button @click="recordTestResult('图片管理', '图片上传功能异常', false)">
                  ❌ 测试失败
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 链接管理测试 -->
          <el-tab-pane label="链接管理测试" name="links">
            <div class="test-section">
              <h4>测试项目：链接添加和持久化</h4>
              <p>预期行为：添加成功后立即显示，刷新页面后仍然存在</p>

              <PurchaseLinksManager
                :purchase-order-id="testOrderId"
                @links-changed="handleLinksChangedTest"
              />

              <div class="test-actions">
                <el-button @click="recordTestResult('链接管理', '链接添加和持久化正常', true)">
                  ✅ 测试通过
                </el-button>
                <el-button @click="recordTestResult('链接管理', '链接功能异常', false)">
                  ❌ 测试失败
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 物流追踪测试 -->
          <el-tab-pane label="物流追踪测试" name="logistics">
            <div class="test-section">
              <h4>测试项目：物流信息设置和显示</h4>
              <p>预期行为：设置成功后显示物流信息，刷新页面后仍然存在</p>

              <LogisticsTracking
                :purchase-order-id="testOrderId"
                @logistics-updated="handleLogisticsUpdatedTest"
              />

              <div class="test-actions">
                <el-button @click="recordTestResult('物流追踪', '物流信息设置和显示正常', true)">
                  ✅ 测试通过
                </el-button>
                <el-button @click="recordTestResult('物流追踪', '物流功能异常', false)">
                  ❌ 测试失败
                </el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="testDialogVisible = false">关闭测试</el-button>
        <el-button type="primary" @click="clearTestResults">清空测试记录</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import PurchaseImageUpload from '@/components/PurchaseImageUpload'
import PurchaseLinksManager from '@/components/PurchaseLinksManager'
import LogisticsTracking from '@/components/LogisticsTracking'

export default {
  name: 'EnhancedFeaturesTest',
  components: {
    PurchaseImageUpload,
    PurchaseLinksManager,
    LogisticsTracking
  },
  data() {
    return {
      testOrderId: '',
      testDialogVisible: false,
      activeTab: 'images',
      testResults: []
    }
  },
  methods: {
    openTestDialog() {
      if (!this.testOrderId) {
        this.$message.warning('请输入采购订单ID')
        return
      }
      this.testDialogVisible = true
      console.log('=== 开始测试采购订单增强功能 ===')
      console.log('测试订单ID:', this.testOrderId)
    },

    recordTestResult(feature, description, success) {
      const result = {
        feature,
        description,
        success,
        timestamp: new Date().toLocaleString()
      }
      this.testResults.unshift(result)

      console.log(`测试结果 - ${feature}: ${success ? '通过' : '失败'} - ${description}`)

      this.$message({
        type: success ? 'success' : 'error',
        message: `${feature}测试${success ? '通过' : '失败'}`
      })
    },

    clearTestResults() {
      this.testResults = []
      this.$message.info('测试记录已清空')
    },

    // 测试事件处理
    handleImageUploadTest(image) {
      console.log('图片上传测试 - 成功事件:', image)
    },

    handleImageErrorTest(error) {
      console.log('图片上传测试 - 错误事件:', error)
    },

    handleLinksChangedTest(links) {
      console.log('链接管理测试 - 变更事件:', links)
    },

    handleLogisticsUpdatedTest(logistics) {
      console.log('物流追踪测试 - 更新事件:', logistics)
    }
  }
}
</script>

<style scoped>
.enhanced-features-test {
  padding: 20px;
}

.test-dialog-content {
  min-height: 400px;
}

.test-section {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 20px;
}

.test-section h4 {
  color: #303133;
  margin-bottom: 10px;
}

.test-section p {
  color: #606266;
  margin-bottom: 20px;
}

.test-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  text-align: center;
}

.test-actions .el-button {
  margin: 0 10px;
}
</style>

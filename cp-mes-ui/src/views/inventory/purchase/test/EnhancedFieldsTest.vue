<template>
  <div class="enhanced-fields-test">
    <el-card>
      <div slot="header">
        <span>采购模块增强字段测试</span>
      </div>

      <el-form inline>
        <el-form-item label="采购订单ID:">
          <el-input
            v-model="testOrderId"
            placeholder="请输入采购订单ID"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testEnhancedFields" :loading="loading">
            测试获取数据
          </el-button>
        </el-form-item>
      </el-form>

      <el-divider></el-divider>

      <!-- 测试结果显示 -->
      <div v-if="testResult">
        <h3>测试结果：</h3>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">基本信息</div>
              <p><strong>订单ID:</strong> {{ testResult.id }}</p>
              <p><strong>采购单号:</strong> {{ testResult.purchaseNo }}</p>
              <p><strong>申请人:</strong> {{ testResult.applicant }}</p>
              <p><strong>状态:</strong> {{ testResult.status }}</p>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card>
              <div slot="header">增强字段</div>
              <p><strong>图片文件夹路径:</strong>
                <span :class="testResult.imagesFolderPath ? 'success' : 'empty'">
                  {{ testResult.imagesFolderPath || '空' }}
                </span>
              </p>
              <p><strong>采购链接:</strong>
                <span :class="testResult.purchaseLinks ? 'success' : 'empty'">
                  {{ testResult.purchaseLinks || '空' }}
                </span>
              </p>
              <p><strong>快递单号:</strong>
                <span :class="testResult.trackingNumber ? 'success' : 'empty'">
                  {{ testResult.trackingNumber || '空' }}
                </span>
              </p>
              <p><strong>物流公司:</strong>
                <span :class="testResult.logisticsCompany ? 'success' : 'empty'">
                  {{ testResult.logisticsCompany || '空' }}
                </span>
              </p>
            </el-card>
          </el-col>
        </el-row>

        <!-- JSON原始数据 -->
        <el-card style="margin-top: 20px">
          <div slot="header">原始JSON数据</div>
          <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
        </el-card>
      </div>

      <!-- 错误信息显示 -->
      <el-alert
        v-if="errorMessage"
        :title="errorMessage"
        type="error"
        style="margin-top: 20px"
        show-icon
      />
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'EnhancedFieldsTest',
  data() {
    return {
      testOrderId: '',
      loading: false,
      testResult: null,
      errorMessage: ''
    }
  },
  methods: {
    async testEnhancedFields() {
      if (!this.testOrderId) {
        this.$message.warning('请输入采购订单ID')
        return
      }

      this.loading = true
      this.errorMessage = ''
      this.testResult = null

      try {
        const response = await request({
          url: `/wms/purchase/testEnhancedFields/${this.testOrderId}`,
          method: 'get'
        })

        if (response.code === 200) {
          this.testResult = response.data
          this.$message.success('数据获取成功')
        } else {
          this.errorMessage = response.msg || '获取数据失败'
        }
      } catch (error) {
        this.errorMessage = '请求失败: ' + error.message
        console.error('测试增强字段失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.enhanced-fields-test {
  padding: 20px;
}

.success {
  color: #67c23a;
  font-weight: bold;
}

.empty {
  color: #909399;
  font-style: italic;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>

<template>
  <div class="application-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <i class="el-icon-document-add"></i>
          采购申请
        </h2>
        <p class="page-description">填写采购申请信息，提交后等待审核</p>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-container">
      <!-- 表单进度指示 -->
      <!-- <div class="form-progress">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="基本信息" description="填写采购基本信息"></el-step>
          <el-step title="确认提交" description="检查信息并提交申请"></el-step>
        </el-steps>
      </div> -->

      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="application-form"
      >
        <el-card class="form-card" shadow="hover">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag v-if="form.id" type="warning" size="small">
              <i class="el-icon-edit"></i>
              编辑模式
            </el-tag>
          </div>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="申请单号" prop="purchaseNo">
                <el-input
                  v-model="form.purchaseNo"
                  placeholder="系统自动生成"
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请人">
                <el-input
                  v-model="currentUser"
                  placeholder="当前用户"
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 采购模式选择 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="采购模式" prop="purchaseMode">
                <el-radio-group v-model="form.purchaseMode" @change="handlePurchaseModeChange">
                  <el-radio-button label="material">
                    <i class="el-icon-box"></i>
                    仓储原料采购
                  </el-radio-button>
                  <el-radio-button label="other">
                    <i class="el-icon-shopping-cart-2"></i>
                    其他类型采购
                  </el-radio-button>
                </el-radio-group>
                <div class="mode-description">
                  <span v-if="form.purchaseMode === 'material'" style="color: #67c23a;">
                    <i class="el-icon-info"></i>
                    从系统物料库中选择已登记的标准原材料，自动填充物料信息
                  </span>
                  <span v-else style="color: #e6a23c;">
                    <i class="el-icon-info"></i>
                    手动填写物品信息，适用于设备、耗材等非标准原料采购
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 仓储原料采购模式 -->
          <el-row v-if="form.purchaseMode === 'material'" :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择原料" prop="itemId">
                <el-select
                  v-model="form.itemId"
                  placeholder="请选择需要采购的原料"
                  filterable
                  style="width: 100%"
                  @change="handleRawMaterialChange"
                  :loading="rawMaterialLoading"
                >
                  <el-option
                    v-for="material in rawMaterialList"
                    :key="material.fields3"
                    :label="getMaterialDisplayLabel(material)"
                    :value="material.fields3"
                  >
                    <span style="float: left; max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {{ material.materialName }}
                    </span>
                    <span style="float: right; color: #8492a6; font-size: 13px; max-width: 35%;">
                      {{ getBoardTypeDisplay(material.boardType) }} | {{ material.unit || '件' }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料信息" v-if="form.purchaseMode === 'material' && form.itemId">
                <el-input
                  :value="getSelectedItemInfo()"
                  readonly
                  class="readonly-input material-info-input"
                  placeholder="选择原料后自动显示"
                >
                  <template slot="prepend">
                    <i class="el-icon-info"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 其他类型采购模式 -->
          <el-row v-if="form.purchaseMode === 'other'" :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择物品" prop="selectedItemId">
                <el-select
                  v-model="form.selectedItemId"
                  placeholder="请选择已登记的物品"
                  filterable
                  style="width: 100%"
                  @change="handleItemChange"
                  :loading="itemLoading"
                >
                  <el-option
                    v-for="item in itemList"
                    :key="item.id"
                    :label="item.itemName"
                    :value="item.id"
                  >
                    <span style="float: left">{{ item.itemName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.category }}</span>
                  </el-option>
                  <el-option
                    key="add-new-item"
                    value=""
                    disabled
                    style="border-top: 1px solid #e4e7ed; margin-top: 5px; padding-top: 8px;"
                  >
                    <el-button
                      type="text"
                      icon="el-icon-plus"
                      @click="showCustomItemDialog = true"
                      style="width: 100%; color: var(--current-color); font-weight: 600;"
                    >
                      + 快速创建新物品
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物品信息">
                <el-input
                  :value="getSelectedOtherItemInfo()"
                  readonly
                  class="readonly-input"
                  placeholder="选择物品后自动显示详细信息"
                />
              </el-form-item>
            </el-col>
          </el-row>



          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="推荐供应商" prop="supplierId">
                <el-select
                  v-model="form.supplierId"
                  placeholder="请选择供应商"
                  filterable
                  style="width: 100%"
                  :loading="supplierLoading"
                  @change="updateStep"
                >
                  <el-option
                    v-for="supplier in supplierList"
                    :key="supplier.id"
                    :label="supplier.supplierName"
                    :value="supplier.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="申请数量" prop="quantity">
                    <el-input-number
                      v-model="form.quantity"
                      :min="1"
                      :max="99999"
                      style="width: 100%"
                      @change="calculateSubtotal"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="计量单位" prop="unit">
                    <el-input
                      v-model="form.unit"
                      placeholder="从选择的物品中自动获取"
                      readonly
                      class="readonly-input"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预估单价" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  @change="calculateSubtotal"
                  placeholder="请输入预估单价"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="预估总金额">
                <el-input
                  :value="subtotal"
                  readonly
                  class="readonly-input amount-input"
                >
                  <template slot="prepend">¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="期望到货时间" prop="expectedDate">
                <el-date-picker
                  v-model="form.expectedDate"
                  type="date"
                  placeholder="请选择期望到货时间"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                  :picker-options="datePickerOptions"
                  @change="updateStep"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 附加信息卡片 -->
        <el-card class="form-card additional-info-card" shadow="hover">
          <div slot="header" class="card-header">
            <span class="card-title">附加信息</span>
            <el-tag type="info" size="small">
              <i class="el-icon-info"></i>
              可选
            </el-tag>
          </div>

          <!-- 折叠面板 -->
          <el-collapse v-model="activeCollapse" accordion>
            <!-- 链接管理 -->
            <el-collapse-item title="相关链接" name="links">
              <template slot="title">
                <i class="el-icon-link"></i>
                <span style="margin-left: 8px;">相关链接</span>
                <el-tag v-if="linkList.length > 0" type="success" size="mini" style="margin-left: 10px;">
                  {{ linkList.length }}个链接
                </el-tag>
              </template>
              <div class="collapse-content">
                <p class="section-description">
                  添加与此采购申请相关的链接，如供应商页面、产品详情、技术文档等
                </p>
                <purchase-links-manager
                  ref="linksManager"
                  :purchase-order-id="tempOrderId"
                  :readonly="false"
                  :embedded="true"
                  @links-changed="handleLinksChanged"
                />
              </div>
            </el-collapse-item>

            <!-- 图片管理 -->
            <el-collapse-item title="相关图片" name="images">
              <template slot="title">
                <i class="el-icon-picture-outline"></i>
                <span style="margin-left: 8px;">相关图片</span>
                <el-tag v-if="imageList.length > 0" type="success" size="mini" style="margin-left: 10px;">
                  {{ imageList.length }}张图片
                </el-tag>
              </template>
              <div class="collapse-content">
                <p class="section-description">
                  上传与此采购申请相关的图片，如产品图片、规格说明图等
                </p>
                <purchase-image-upload
                  ref="imageUpload"
                  :purchase-order-id="tempOrderId"
                  :readonly="false"
                  :embedded="true"
                  @upload-success="handleImageUploadSuccess"
                  @images-changed="handleImagesChanged"
                />
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="handleReset">
            <i class="el-icon-refresh-left"></i>
            重置表单
          </el-button>
          <el-button v-if="form.id" size="large" @click="handleCancelEdit">
            <i class="el-icon-close"></i>
            取消编辑
          </el-button>
          <el-button type="primary" size="large" @click="handleSubmit" :loading="loading" :disabled="currentStep < 1">
            <i class="el-icon-s-promotion"></i>
            {{ form.id ? '更新申请' : '提交申请' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 申请记录 -->
    <div class="history-container">
      <el-card shadow="hover">
        <div slot="header" class="card-header">
          <span class="card-title">我的申请记录</span>
          <el-button type="text" @click="refreshHistory">
            <i class="el-icon-refresh"></i>
            刷新
          </el-button>
        </div>

        <el-table
          :data="historyList"
          v-loading="historyLoading"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="purchaseNo" label="申请单号" width="200" align="center" />
          <el-table-column prop="itemName" label="物品名称" min-width="200" align="center" />
          <el-table-column prop="itemType" label="类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.itemType === '原料' ? 'success' : 'warning'"
                size="small"
              >
                {{ scope.row.itemType || '原料' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="boardType" label="板型" width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.itemType === '原料' && scope.row.boardType">
                <el-tag
                  :type="getBoardTypeTagType(scope.row.boardType)"
                  size="mini"
                >
                  {{ scope.row.boardType }}
                </el-tag>
              </span>
              <span v-else style="color: #c0c4cc;">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="supplierName" label="供应商" min-width="180" align="center" />
          <el-table-column prop="quantity" label="数量" width="120" align="center" />
          <el-table-column prop="unit" label="单位" width="100" align="center" />
          <el-table-column prop="subtotal" label="预估金额" width="120" align="center">
            <template slot-scope="scope">
              <span style="color: #e6a23c; font-weight: bold;">
                ¥{{ scope.row.subtotal }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="实际金额" width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.actualSubtotal" style="color: #67c23a; font-weight: bold;">
                ¥{{ scope.row.actualSubtotal }}
              </span>
              <span v-else style="color: #909399;">未录入</span>
            </template>
          </el-table-column>
          <el-table-column label="价格差异" width="100" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.actualSubtotal && scope.row.subtotal"
                    :class="getPriceDifferenceClass(scope.row.actualSubtotal - scope.row.subtotal)">
                {{ formatPriceDifference(scope.row.actualSubtotal - scope.row.subtotal) }}
              </span>
              <span v-else style="color: #909399;">--</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="180" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="applyTime" label="申请时间" width="200" align="center">
            <template slot-scope="scope">
              {{ formatDate(scope.row.applyTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="380" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleViewHistory(scope.row)">
                查看
              </el-button>
              <el-button
                v-if="scope.row.status === 0"
                size="mini"
                type="warning"
                @click="handleEditHistory(scope.row)"
                style="margin-left: 5px;"
              >
                编辑
              </el-button>
              <el-button
                v-if="scope.row.status === 0"
                size="mini"
                type="danger"
                @click="handleDeleteHistory(scope.row)"
                style="margin-left: 5px;"
              >
                删除
              </el-button>
              <el-button
                size="mini"
                type="success"
                @click="handleEnhancedFeatures(scope.row)"
                style="margin-left: 5px;"
                title="添加链接、上传图片、查询物流信息"
              >
                增强功能
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="historyTotal > 0"
          background
          layout="total, prev, pager, next"
          :current-page="historyPage"
          :page-size="historyPageSize"
          :total="historyTotal"
          @current-change="handleHistoryPageChange"
          style="margin-top: 20px; text-align: center"
        />
      </el-card>
    </div>

    <!-- 申请详情对话框 -->
    <el-dialog
      title="申请详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetail" class="detail-container">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请单号">
            <el-tag type="primary">{{ currentDetail.purchaseNo }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ currentDetail.applicant }}
          </el-descriptions-item>
          <el-descriptions-item label="物品名称">
            {{ currentDetail.itemName }}
          </el-descriptions-item>
          <el-descriptions-item label="物品类型">
            <el-tag
              :type="currentDetail.itemType === '原料' ? 'success' : 'warning'"
              size="small"
            >
              {{ currentDetail.itemType || '原料' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ currentDetail.supplierName }}
          </el-descriptions-item>
          <el-descriptions-item label="申请数量">
            <span style="color: #409eff; font-weight: bold;">
              {{ currentDetail.quantity }}{{ currentDetail.unit }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="预估单价">
            <span style="color: #e6a23c; font-weight: bold;">
              ¥{{ currentDetail.price }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="预估总金额">
            <span style="color: #e6a23c; font-weight: bold; font-size: 16px;">
              ¥{{ currentDetail.subtotal }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="实际单价">
            <span v-if="currentDetail.actualPrice" style="color: #67c23a; font-weight: bold;">
              ¥{{ currentDetail.actualPrice }}
            </span>
            <el-tooltip v-else content="实际价格尚未录入" placement="top">
              <span style="color: #909399;">未录入</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="实际总额">
            <span v-if="currentDetail.actualSubtotal" style="color: #67c23a; font-weight: bold; font-size: 16px;">
              ¥{{ currentDetail.actualSubtotal }}
            </span>
            <el-tooltip v-else content="实际价格尚未录入" placement="top">
              <span style="color: #909399;">未录入</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="价格差异" v-if="currentDetail.actualSubtotal && currentDetail.subtotal">
            <span :class="getPriceDifferenceClass(currentDetail.actualSubtotal - currentDetail.subtotal)">
              {{ formatPriceDifference(currentDetail.actualSubtotal - currentDetail.subtotal) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="价格差异" v-else>
            <span style="color: #909399;">--</span>
          </el-descriptions-item>
          <el-descriptions-item label="期望到货时间">
            {{ formatDate(currentDetail.expectedDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusType(currentDetail.status)" size="medium">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ currentDetail.applyTime || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="审核人">
            {{ currentDetail.approver || '待审核' }}
          </el-descriptions-item>
          <el-descriptions-item label="审核时间">
            {{ currentDetail.approveTime || '待审核' }}
          </el-descriptions-item>
          <el-descriptions-item label="实际价格录入时间" v-if="currentDetail.actualPriceTime">
            <el-tooltip :content="`录入人：${currentDetail.actualPriceRecorder || '未知'}`" placement="top">
              <span style="color: #67c23a;">{{ formatDate(currentDetail.actualPriceTime) }}</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="实际价格录入时间" v-else>
            <span style="color: #909399;">未录入</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ currentDetail.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentDetail && currentDetail.status === 0"
          type="warning"
          @click="handleEditFromDetail"
        >
          编辑申请
        </el-button>
      </div>
    </el-dialog>

    <!-- 增强功能对话框 -->
    <purchase-order-enhanced-dialog
      :visible.sync="enhancedDialogVisible"
      :purchase-order-id="selectedOrderId"
      @refresh="refreshHistory"
    />

    <!-- 快速创建物品对话框 -->
    <el-dialog
      title="快速创建物品"
      :visible.sync="showCustomItemDialog"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="customItemForm" :model="customItemForm" :rules="customItemRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品名称" prop="itemName">
              <el-input
                v-model="customItemForm.itemName"
                placeholder="请输入物品名称"
                @input="handleCustomItemNameInput"
              />
            </el-form-item>
          </el-col>
                                <el-col :span="12">
             <el-form-item label="计量单位" prop="unit">
               <el-input
                 v-model="customItemForm.unit"
                 placeholder="请输入计量单位，如：件、个、台、套、米、千克等"
               />
             </el-form-item>
           </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
                         <el-form-item label="物品分类" prop="category">
               <el-select v-model="customItemForm.category" placeholder="请选择分类" style="width: 100%">
                 <el-option label="原料" value="原料" />
                 <el-option label="零部件" value="零部件" />
                 <el-option label="其他" value="其他" />
               </el-select>
             </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号">
              <el-input
                v-model="customItemForm.specification"
                placeholder="请输入规格型号（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品牌">
              <el-input
                v-model="customItemForm.brand"
                placeholder="请输入品牌（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelCustomItem">取消</el-button>
        <el-button type="primary" @click="handleCreateCustomItem" :loading="customItemLoading">
          <i class="el-icon-check"></i>
          创建并选择
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addOrUpdatePurchaseOrder, getPurchaseOrderDetail, deletePurchaseOrder, generateNextPurchaseNo } from '@/api/jenasi/purchaseOrder'
import { listAllItems, addOrUpdateItem } from '@/api/jenasi/item'
import { listAllRawMaterials } from '@/api/jenasi/rawMaterialWarehouse'
import { getSupplierList } from '@/api/jenasi/supplier'
import PurchaseOrderEnhancedDialog from './components/PurchaseOrderEnhancedDialog.vue'
import PurchaseLinksManager from '@/components/PurchaseLinksManager/index.vue'
import PurchaseImageUpload from '@/components/PurchaseImageUpload/index.vue'
import request from '@/utils/request'

export default {
  name: 'PurchaseApplication',
  components: {
    PurchaseOrderEnhancedDialog,
    PurchaseLinksManager,
    PurchaseImageUpload
  },
  data() {
    return {
      loading: false,
      itemLoading: false,
      rawMaterialLoading: false,
      supplierLoading: false,
      historyLoading: false,
      currentUser: '', // 从store获取当前登录用户
      form: {
        id: null,
        purchaseNo: '',
        purchaseMode: 'material', // 默认为仓储原料采购模式
        itemId: null, // 仓储原料采购时存储fields3值
        selectedItemId: null, // 其他类型采购时选择的item表ID
        itemName: '', // 其他类型采购时的物品名称
        itemType: '', // 其他类型采购时的物品类型
        boardType: '', // 板型类型，仅仓储原料采购时使用
        specification: '', // 规格说明
        supplierId: null,
        quantity: 1,
        unit: '',
        price: 0,
        expectedDate: ''
      },
      itemList: [], // 其他类型采购使用的物品列表（item表数据）
      rawMaterialList: [], // 仓储原料采购使用的原料列表（raw_material_warehouse表数据）
      supplierList: [],
      historyList: [],
      historyPage: 1,
      historyPageSize: 10,
      historyTotal: 0,
      currentStep: 0,
      detailDialogVisible: false,
      currentDetail: null,
      enhancedDialogVisible: false,
      selectedOrderId: null,
      showCustomItemDialog: false,
      customItemLoading: false,
      customItemForm: {
        itemName: '',
        unit: '件',
        category: '原料',
        specification: '',
        brand: ''
      },
      customItemRules: {
        itemName: [
          { required: true, message: '请输入物品名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择物品分类', trigger: 'change' }
        ]
      },
      // 附加信息相关
      activeCollapse: '', // 当前展开的折叠面板
      tempOrderId: 'temp_' + Date.now(), // 临时订单ID，用于组件通信
      linkList: [], // 链接列表
      imageList: [], // 图片列表
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      },
      rules: {
        purchaseMode: [
          { required: true, message: '请选择采购模式', trigger: 'change' }
        ],
        itemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'material' && !value) {
                callback(new Error('请选择需要采购的原料'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        selectedItemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !value) {
                callback(new Error('请选择需要采购的物品'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        supplierId: [
          { required: true, message: '请选择推荐供应商', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入申请数量', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入预估单价', trigger: 'blur' }
        ],
        expectedDate: [
          { required: true, message: '请选择期望到货时间', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    subtotal() {
      if (this.form.quantity && this.form.price) {
        return (this.form.quantity * this.form.price).toFixed(2)
      }
      return '0.00'
    }
  },
  mounted() {
    this.initCurrentUser()
    this.initForm()
    this.loadItemList()
    this.loadRawMaterialList()
    this.loadSupplierList()
    this.loadHistory()
  },

  // 页面销毁前清理临时数据
  beforeDestroy() {
    // 异步清理临时数据，不阻塞页面销毁
    if (this.tempOrderId && this.hasTemporaryData()) {
      this.clearTempDataSafely(this.tempOrderId).catch(error => {
        console.warn('页面销毁时清理临时数据失败:', error)
      })
    }
  },
  methods: {
    // 初始化当前用户
    initCurrentUser() {
      // 从store获取当前登录用户信息
      this.currentUser = this.$store.getters.name || this.$store.getters.userId || '未知用户'
    },

    // 初始化表单
    async initForm() {
      try {
        this.form.purchaseNo = await this.generatePurchaseNo()
      } catch (error) {
        console.error('生成采购订单号失败:', error)
        this.$message.error('生成采购订单号失败，请刷新页面重试')
        // 降级到旧的生成方式
        this.form.purchaseNo = this.generatePurchaseNoFallback()
      }
    },

    // 生成采购单号（新版本 - 调用后端API）
    async generatePurchaseNo() {
      try {
        const response = await generateNextPurchaseNo()
        if (response.code === 0 || response.code === 200) {
          console.log('生成新的采购订单号:', response.data)
          return response.data
        } else {
          throw new Error(response.msg || '生成订单号失败')
        }
      } catch (error) {
        console.error('调用后端生成订单号API失败:', error)
        throw error
      }
    },

    // 生成采购单号（降级方案 - 保留原有逻辑作为备用）
    generatePurchaseNoFallback() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')

      // 添加4位随机数确保唯一性
      const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0')

      console.warn('使用降级方案生成订单号')
      return `PA${year}${month}${day}${hours}${minutes}${seconds}${random}`
    },

    // 加载物品列表（其他类型采购使用）
    async loadItemList() {
      this.itemLoading = true
      try {
        const response = await listAllItems()
        if (response.code === 0 || response.code === 200) {
          this.itemList = response.data || []
        }
      } catch (error) {
        console.error('获取物品列表失败:', error)
        this.$message.error('获取物品列表失败')
      } finally {
        this.itemLoading = false
      }
    },

    // 加载原料列表（仓储原料采购使用）
    async loadRawMaterialList() {
      this.rawMaterialLoading = true
      try {
        const response = await listAllRawMaterials()
        if (response.code === 0 || response.code === 200) {
          this.rawMaterialList = response.data || []
          console.log('原料列表加载成功:', this.rawMaterialList.length, '条记录')
        }
      } catch (error) {
        console.error('获取原料列表失败:', error)
        this.$message.error('获取原料列表失败')
      } finally {
        this.rawMaterialLoading = false
      }
    },

    // 加载供应商列表
    async loadSupplierList() {
      this.supplierLoading = true
      try {
        const response = await getSupplierList({ pageNum: 1, pageSize: 1000 })
        if (response.code === 0 || response.code === 200) {
          this.supplierList = response.data.records || response.data || []
        }
      } catch (error) {
        console.error('获取供应商列表失败:', error)
        this.$message.error('获取供应商列表失败')
      } finally {
        this.supplierLoading = false
      }
    },

    // 加载申请历史
    async loadHistory() {
      this.historyLoading = true
      try {
        const params = {
          pageNum: this.historyPage,
          pageSize: this.historyPageSize,
          applicant: this.currentUser // 只查询当前用户的申请
        }
        const response = await getPurchaseOrderDetail(params)
        if (response.code === 0 || response.code === 200) {
          this.historyList = response.data.records || []
          this.historyTotal = response.data.total || 0
        }
      } catch (error) {
        console.error('获取申请历史失败:', error)
      } finally {
        this.historyLoading = false
      }
    },

    // 采购模式变化处理
    handlePurchaseModeChange(mode) {
      // 清空相关字段
      if (mode === 'material') {
        // 切换到仓储原料采购模式
        this.form.itemId = null
        this.form.selectedItemId = null
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.boardType = ''
        this.form.specification = ''
        this.form.unit = ''
      } else {
        // 切换到其他类型采购模式
        this.form.itemId = null
        this.form.selectedItemId = null
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.boardType = ''
        this.form.specification = ''
        this.form.unit = ''
      }
      this.updateStep()
    },

    // 原料选择改变（仓储原料采购模式）
    handleRawMaterialChange(fields3) {
      const selectedMaterial = this.rawMaterialList.find(material => material.fields3 === fields3)
      if (selectedMaterial) {
        this.form.unit = selectedMaterial.unit || '件'
        this.form.itemName = selectedMaterial.materialName
        this.form.itemType = '原料'
        this.form.boardType = selectedMaterial.boardType || '单板'
        console.log('选择原料:', selectedMaterial.materialName, 'fields3:', fields3, '板型:', this.form.boardType)
      }
      this.updateStep()
    },

    // 物品选择改变（其他类型采购模式）
    handleItemChange(itemId) {
      if (itemId) {
        const selectedItem = this.itemList.find(item => item.id === itemId)
        if (selectedItem) {
          this.form.unit = selectedItem.unit || ''
          this.form.itemName = selectedItem.itemName
          this.form.itemType = selectedItem.category || '其他'
          // 清空手动填写的字段
          this.form.specification = ''
        }
      } else {
        // 清空选择，允许手动填写
        this.form.unit = ''
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.specification = ''
      }
      this.updateStep()
    },

    // 获取选中原料的信息显示（仓储原料采购模式）
    getSelectedItemInfo() {
      if (!this.form.itemId) return ''
      const selectedMaterial = this.rawMaterialList.find(material => material.fields3 === this.form.itemId)
      if (!selectedMaterial) return ''

      const parts = []
      if (selectedMaterial.materialType) parts.push(`类型: ${selectedMaterial.materialType}`)
      if (selectedMaterial.boardType) parts.push(`板型: ${this.getBoardTypeDisplay(selectedMaterial.boardType)}`)
      if (selectedMaterial.unit) parts.push(`单位: ${selectedMaterial.unit}`)
      if (selectedMaterial.currentStock !== null) parts.push(`库存: ${selectedMaterial.currentStock}`)
      if (selectedMaterial.fields3) parts.push(`编码: ${selectedMaterial.fields3}`)

      return parts.join(' | ') || '无详细信息'
    },

    // 获取选中物品的信息显示（其他类型采购模式）
    getSelectedOtherItemInfo() {
      if (!this.form.selectedItemId) return ''
      const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
      if (!selectedItem) return ''

      const parts = []
      if (selectedItem.category) parts.push(`分类: ${selectedItem.category}`)
      if (selectedItem.specification) parts.push(`规格: ${selectedItem.specification}`)
      if (selectedItem.brand) parts.push(`品牌: ${selectedItem.brand}`)
      if (selectedItem.unit) parts.push(`单位: ${selectedItem.unit}`)

      return parts.join(' | ') || '无详细信息'
    },

    // 获取原料显示标签（用于下拉框label）
    getMaterialDisplayLabel(material) {
      if (!material) return ''
      const boardType = this.getBoardTypeDisplay(material.boardType)
      const unit = material.unit || '件'
      return `${material.materialName} | ${boardType} | ${unit}`
    },

    // 获取板型显示文本
    getBoardTypeDisplay(boardType) {
      if (!boardType) return '单板'

      // 标准化板型显示
      const boardTypeMap = {
        '上板': '上板',
        '下板': '下板',
        '单板': '单板',
        'upper': '上板',
        'lower': '下板',
        'single': '单板'
      }

      return boardTypeMap[boardType] || boardType || '单板'
    },

    // 获取板型标签颜色类型
    getBoardTypeTagType(boardType) {
      const typeMap = {
        '上板': 'primary',
        '下板': 'success',
        '单板': 'info'
      }
      return typeMap[boardType] || 'info'
    },

    // 更新步骤
    updateStep() {
      // 检查基本信息是否完整
      let isComplete = false

      if (this.form.purchaseMode === 'material') {
        // 仓储原料采购模式：需要选择原料
        isComplete = this.form.itemId && this.form.supplierId && this.form.quantity && this.form.price && this.form.expectedDate
      } else if (this.form.purchaseMode === 'other') {
        // 其他类型采购模式：可以选择已有物品或手动填写
        if (this.form.selectedItemId) {
          // 选择了已有物品
          isComplete = this.form.selectedItemId && this.form.supplierId && this.form.quantity && this.form.price && this.form.expectedDate
        } else {
          // 手动填写物品信息
          isComplete = this.form.itemName && this.form.itemType && this.form.unit &&
                      this.form.supplierId && this.form.quantity && this.form.price && this.form.expectedDate
        }
      }

      this.currentStep = isComplete ? 1 : 0
    },

    // 计算小计
    calculateSubtotal() {
      // 计算在computed中处理
      this.updateStep()
    },

    // 提交申请
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        const isEdit = !!this.form.id
        const confirmMessage = isEdit
          ? '确认更新采购申请？更新后将重新等待审核。'
          : '确认提交采购申请？提交后将等待审核。'
        const confirmTitle = isEdit ? '确认更新' : '确认提交'

        await this.$confirm(confirmMessage, confirmTitle, {
          confirmButtonText: isEdit ? '确认更新' : '确认提交',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.loading = true

        const formData = {
          purchaseNo: this.form.purchaseNo,
          applicant: this.currentUser,
          supplierId: Number(this.form.supplierId),
          quantity: Number(this.form.quantity),
          unit: this.form.unit,
          price: Number(this.form.price),
          expectedDate: this.form.expectedDate
        }

        // 根据采购模式设置不同的字段
        if (this.form.purchaseMode === 'material') {
          // 仓储原料采购模式：使用原料的fields3值作为itemId
          formData.itemId = this.form.itemId // 这里存储的是fields3值
          formData.itemName = this.form.itemName // 原料名称
          formData.itemType = this.form.itemType // 固定为"原料"
          formData.boardType = this.form.boardType // 板型类型
          // 后端会根据itemId有值识别为仓储原料采购模式
        } else {
          // 其他类型采购模式
          formData.itemId = null // 设置为null，后端识别为其他类型采购模式
          formData.boardType = null // 其他类型采购不设置板型
          if (this.form.selectedItemId) {
            // 选择了已有物品
            const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
            if (selectedItem) {
              formData.itemName = selectedItem.itemName
              formData.itemType = selectedItem.category || '其他'
            }
          } else {
            // 手动填写物品信息
            formData.itemName = this.form.itemName
            formData.itemType = this.form.itemType
          }
        }

        // 如果是编辑模式，添加id字段
        if (isEdit) {
          formData.id = this.form.id
        }

        // 添加附加信息（链接和图片）
        const additionalData = this.getAdditionalData()
        if (additionalData.links.length > 0 || additionalData.images.length > 0) {
          formData.additionalInfo = additionalData
          console.log('包含附加信息:', additionalData)
        }

        const response = await addOrUpdatePurchaseOrder(formData)
        if (response.code === 0 || response.code === 200) {
          const successMessage = isEdit ? '采购申请更新成功！' : '采购申请提交成功！'

          // 如果是新增且有临时数据，需要迁移临时数据到正式订单
          if (!isEdit && response.data && response.data.id && this.hasTemporaryData()) {
            try {
              console.log('开始迁移临时数据:', this.tempOrderId, '->', response.data.id)
              await this.migrateTempDataToReal(this.tempOrderId, response.data.id)
              console.log('临时数据迁移成功')
            } catch (error) {
              console.error('临时数据迁移失败:', error)
              this.$message.warning('采购申请提交成功，但附加信息迁移失败，请手动重新添加')

              // 迁移失败时，尝试清理临时数据避免残留
              try {
                await this.clearTempDataSafely(this.tempOrderId)
              } catch (cleanError) {
                console.error('清理临时数据失败:', cleanError)
              }
            }
          } else if (!isEdit && !this.hasTemporaryData()) {
            // 如果没有临时数据但有临时订单ID，清理可能的残留数据
            try {
              await this.clearTempDataSafely(this.tempOrderId)
            } catch (cleanError) {
              console.error('清理残留临时数据失败:', cleanError)
            }
          }

          this.$message.success(successMessage)
          this.handleReset()
          this.loadHistory()
        } else {
          this.$message.error(response.msg || (isEdit ? '更新失败' : '提交失败'))
        }
      } catch (error) {
        if (error !== false && error !== 'cancel') {
          console.error(isEdit ? '更新失败:' : '提交失败:', error)
          this.$message.error(isEdit ? '更新失败' : '提交失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 重置表单
    async handleReset() {
      try {
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在重置表单...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        // 重置表单字段
        this.$refs.form.resetFields()
        this.initForm()

        // 清空编辑状态和双模式相关字段
        this.form.id = null
        this.form.purchaseMode = 'material'
        this.form.selectedItemId = null
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.boardType = ''
        this.form.specification = ''

        // 重置附加信息（包含临时文件清理）
        await this.resetAdditionalInfo()

        loading.close()
        this.$message.success('表单已重置，临时数据已清理')

      } catch (error) {
        console.error('表单重置失败:', error)
        this.$message.error('表单重置过程中遇到问题，请刷新页面重试')
      }
    },

    // 取消编辑
    handleCancelEdit() {
      this.$confirm('确认取消编辑？未保存的修改将丢失。', '确认取消', {
        confirmButtonText: '确认取消',
        cancelButtonText: '继续编辑',
        type: 'error'
      }).then(() => {
        this.handleReset()
        this.$message.info('已取消编辑')
      }).catch(() => {
        // 用户选择继续编辑
      })
    },

    // 从详情对话框编辑申请
    handleEditFromDetail() {
      this.detailDialogVisible = false
      this.handleEditHistory(this.currentDetail)
    },

    // 刷新历史记录
    refreshHistory() {
      this.historyPage = 1
      this.loadHistory()
    },

    // 查看历史记录详情
    handleViewHistory(row) {
      this.currentDetail = { ...row }
      this.detailDialogVisible = true
    },

    // 编辑申请记录
    async handleEditHistory(row) {
      try {
        await this.$confirm('确认要编辑此申请吗？编辑后需要重新提交审核。', '确认编辑', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 将选中的记录数据填充到表单中
        this.form = {
          id: row.id,
          purchaseNo: row.purchaseNo,
          purchaseMode: row.itemId ? 'material' : 'other', // 根据是否有itemId判断采购模式
          itemId: row.itemId,
          selectedItemId: null, // 编辑时暂不支持回填已选物品
          itemName: row.itemName || '',
          itemType: row.itemType || '',
          boardType: row.boardType || '',
          specification: '',
          supplierId: row.supplierId,
          quantity: row.quantity,
          unit: row.unit,
          price: row.price,
          expectedDate: row.expectedDate
        }

        // 确保物品和供应商数据已加载
        if (this.itemList.length === 0) {
          await this.loadItemList()
        }
        if (this.supplierList.length === 0) {
          await this.loadSupplierList()
        }

        // 更新步骤状态
        this.updateStep()

        // 滚动到表单顶部
        this.$nextTick(() => {
          const formContainer = document.querySelector('.form-container')
          if (formContainer) {
            formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }
        })

        this.$message.success('申请记录已加载到表单，请修改后重新提交')
      } catch (error) {
        // 用户取消操作
      }
    },

    // 删除申请记录
    async handleDeleteHistory(row) {
      try {
        await this.$confirm(`确认要删除申请单号为 ${row.purchaseNo} 的申请吗？此操作不可恢复。`, '确认删除', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'error'
        })

        this.loading = true

        // 调用删除API
        const response = await this.deletePurchaseOrderRecord(row.id)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('申请记录删除成功')
          this.loadHistory() // 重新加载历史记录
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== false && error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 处理增强功能
    handleEnhancedFeatures(row) {
      console.log('打开增强功能对话框，订单ID:', row.id)
      this.selectedOrderId = row.id
      this.enhancedDialogVisible = true
    },

    // 删除采购申请API调用
    async deletePurchaseOrderRecord(id) {
      try {
        const response = await deletePurchaseOrder(id)
        return response
      } catch (error) {
        throw error
      }
    },

    // 历史记录分页
    handleHistoryPageChange(page) {
      this.historyPage = page
      this.loadHistory()
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: '',
        1: 'success',
        2: 'danger',
        3: 'info'
      }
      return statusMap[status] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      const d = new Date(date)
      return d.toLocaleString('zh-CN')
    },

    // 获取价格差异的样式类
    getPriceDifferenceClass(difference) {
      if (difference > 0) {
        return 'price-increase'
      } else if (difference < 0) {
        return 'price-decrease'
      }
      return 'price-equal'
    },

    // 格式化价格差异显示
    formatPriceDifference(difference) {
      if (difference === 0) {
        return '无差异'
      }
      const prefix = difference > 0 ? '+' : ''
      return `${prefix}¥${Math.abs(difference).toFixed(2)}`
    },

    // 智能推荐单位
    getRecommendedUnit(itemName) {
      const unitRecommendations = {
        // 长度相关
        '线|电缆|绳|管|带': '米',
        '铁丝|钢丝|链条': '米',

        // 重量相关
        '粉|料|颗粒|沙|土': '千克',
        '金属|钢材|铁|铜|铝': '千克',

        // 容量相关
        '油|液|水|溶液|涂料': '升',
        '胶|漆|清洗剂': '升',

        // 面积相关
        '板|片|膜|纸|布': '张',
        '玻璃|镜子': '块',

        // 计数相关
        '螺丝|螺栓|螺母|垫片': '个',
        '工具|仪器|设备|机器': '台',
        '电器|开关|插座': '个',
        '灯|灯泡|灯管': '个'
      }

      for (const [keywords, unit] of Object.entries(unitRecommendations)) {
        if (new RegExp(keywords, 'i').test(itemName)) {
          return unit
        }
      }
      return '件' // 默认单位
    },

    // 处理自定义物品名称输入，智能推荐单位
    handleCustomItemNameInput(itemName) {
      if (itemName && this.customItemForm.unit === '件') {
        const recommendedUnit = this.getRecommendedUnit(itemName)
        if (recommendedUnit !== '件') {
          this.customItemForm.unit = recommendedUnit
          this.$message({
            message: `根据物品名称，建议使用单位：${recommendedUnit}`,
            type: 'info',
            duration: 3000
          })
        }
      }
    },

    // 取消创建自定义物品
    handleCancelCustomItem() {
      this.showCustomItemDialog = false
      this.customItemForm = {
        itemName: '',
        unit: '件',
        category: '原料',
        specification: '',
        brand: ''
      }
      this.$refs.customItemForm && this.$refs.customItemForm.resetFields()
    },

    // 创建自定义物品
    async handleCreateCustomItem() {
      try {
        await this.$refs.customItemForm.validate()

        // 检查是否已存在同名物品
        const existingItem = this.itemList.find(item =>
          item.itemName.toLowerCase() === this.customItemForm.itemName.toLowerCase()
        )

        if (existingItem) {
          this.$confirm(`物品"${this.customItemForm.itemName}"已存在，是否直接选择现有物品？`, '提示', {
            confirmButtonText: '选择现有',
            cancelButtonText: '继续创建',
            type: 'warning'
          }).then(() => {
            // 选择现有物品
            this.form.itemId = existingItem.id
            this.form.unit = existingItem.unit
            this.showCustomItemDialog = false
            this.handleCancelCustomItem()
            this.updateStep()
            this.$message.success('已选择现有物品')
          }).catch(() => {
            // 继续创建新物品，添加序号区分
            this.createNewCustomItem(`${this.customItemForm.itemName}-${Date.now()}`)
          })
          return
        }

        this.createNewCustomItem()
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('创建物品失败:', error)
          this.$message.error('创建物品失败')
        }
      }
    },

    // 创建新的自定义物品
    async createNewCustomItem(customName = null) {
      this.customItemLoading = true

      try {
        const itemData = {
          itemName: customName || this.customItemForm.itemName,
          category: this.customItemForm.category,
          specification: this.customItemForm.specification || '虚影',
          brand: this.customItemForm.brand || '虚影',
          unit: this.customItemForm.unit
        }

        const response = await addOrUpdateItem(itemData)

        if (response.code === 0 || response.code === 200) {
          const newItem = response.data

          // 添加到物品列表的开头，方便用户查看
          this.itemList.unshift(newItem)

          // 自动选择新创建的物品
          this.form.itemId = newItem.id
          this.form.unit = newItem.unit

          // 关闭对话框并重置表单
          this.showCustomItemDialog = false
          this.handleCancelCustomItem()

          this.$message.success(`物品"${newItem.itemName}"创建成功并已自动选择`)
          this.updateStep()

          // 滚动到表单顶部，让用户看到已选择的物品
          this.$nextTick(() => {
            const formContainer = document.querySelector('.form-container')
            if (formContainer) {
              formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }
          })
        } else {
          throw new Error(response.msg || '创建物品失败')
        }
      } catch (error) {
        console.error('创建自定义物品失败:', error)
        this.$message.error('创建物品失败: ' + (error.message || error))
      } finally {
        this.customItemLoading = false
      }
    },

    // 处理链接变化
    handleLinksChanged(links) {
      this.linkList = links || []
      console.log('链接列表更新:', this.linkList)
    },

    // 处理图片上传成功
    handleImageUploadSuccess(imageInfo) {
      console.log('图片上传成功:', imageInfo)
      this.$message.success('图片上传成功')
    },

    // 处理图片变化
    handleImagesChanged(images) {
      this.imageList = images || []
      console.log('图片列表更新:', this.imageList)
    },

    // 获取附加信息数据（用于提交时包含链接和图片）
    getAdditionalData() {
      return {
        links: this.linkList,
        images: this.imageList
      }
    },

    // 重置附加信息
    async resetAdditionalInfo() {
      const oldTempOrderId = this.tempOrderId

      try {
        // 1. 先清理当前临时订单的临时文件和数据
        // 修复：不仅检查hasTemporaryData()，只要有有效的临时订单ID就尝试清理
        if (oldTempOrderId && oldTempOrderId.startsWith('temp_')) {
          console.log('开始清理临时数据:', oldTempOrderId, '本地数据状态:', this.hasTemporaryData())

          try {
            // 修复：使用重试机制，最多重试2次
            const clearSuccess = await this.clearTempDataSafely(oldTempOrderId, 2)
            if (clearSuccess) {
              console.log('临时数据清理成功:', oldTempOrderId)
            } else {
              console.warn('临时数据清理失败，但继续重置操作:', oldTempOrderId)
              // 修复：即使后端清理失败，也要显示警告信息
              this.$message.warning('后端临时文件清理可能未完全成功，建议联系管理员检查')
            }
          } catch (error) {
            console.error('临时数据清理异常，但继续重置操作:', error)
            this.$message.warning('临时文件清理过程中出现异常: ' + (error.message || error))
          }
        }

        // 2. 重置本地状态
        this.linkList = []
        this.imageList = []
        this.activeCollapse = ''

        // 3. 生成新的临时订单ID
        this.tempOrderId = 'temp_' + Date.now()
        console.log('生成新的临时订单ID:', this.tempOrderId)

        // 4. 重置子组件（添加防御性检查）
        this.$nextTick(() => {
          try {
            if (this.$refs.linksManager && typeof this.$refs.linksManager.resetData === 'function') {
              this.$refs.linksManager.resetData()
              console.log('链接管理器已重置')
            } else {
              console.warn('链接管理器组件未找到或resetData方法不存在')
            }
          } catch (error) {
            console.error('重置链接管理器失败:', error)
          }

          try {
            if (this.$refs.imageUpload && typeof this.$refs.imageUpload.resetData === 'function') {
              this.$refs.imageUpload.resetData()
              console.log('图片上传器已重置')
            } else {
              console.warn('图片上传器组件未找到或resetData方法不存在')
            }
          } catch (error) {
            console.error('重置图片上传器失败:', error)
          }
        })

        console.log('附加信息重置完成')

      } catch (error) {
        console.error('重置附加信息失败:', error)
        // 即使清理失败，也要确保生成新的临时订单ID
        this.tempOrderId = 'temp_' + Date.now()
        this.$message.warning('重置过程中遇到问题，但已完成基本重置')
      }
    },

    // 检查是否有临时数据
    hasTemporaryData() {
      return this.linkList.length > 0 || this.imageList.length > 0
    },

    // 迁移临时数据到正式订单
    async migrateTempDataToReal(tempOrderId, realOrderId) {
      try {
        const response = await request({
          url: '/wms/purchase/migrateTempData',
          method: 'post',
          params: {
            tempOrderId: tempOrderId,
            realOrderId: realOrderId
          }
        })

        if (response.code === 0 || response.code === 200) {
          console.log('临时数据迁移成功:', response)
          return true
        } else {
          throw new Error(response.msg || '迁移失败')
        }
      } catch (error) {
        console.error('临时数据迁移失败:', error)
        throw error
      }
    },

    // 安全清理临时数据（不抛出异常）
    async clearTempDataSafely(tempOrderId, retryCount = 1) {
      if (!tempOrderId || !tempOrderId.startsWith('temp_')) {
        console.warn('无效的临时订单ID:', tempOrderId)
        return false
      }

      for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
          console.log(`尝试清理临时数据 (第${attempt}次):`, tempOrderId)

          const response = await request({
            url: '/wms/purchase/clearTempData',
            method: 'post',
            params: {
              tempOrderId: tempOrderId
            },
            timeout: 10000 // 设置10秒超时
          })

          if (response.code === 0 || response.code === 200) {
            console.log('临时数据清理成功:', tempOrderId)
            return true
          } else {
            console.warn(`临时数据清理失败 (第${attempt}次):`, response.msg || response.message)
            if (attempt === retryCount) {
              return false
            }
            // 等待1秒后重试
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        } catch (error) {
          console.warn(`临时数据清理异常 (第${attempt}次):`, error.message)
          if (attempt === retryCount) {
            return false
          }
          // 等待1秒后重试
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      return false
    },

    // 用户取消操作时的清理
    async handleCancel() {
      try {
        // 如果有临时数据，询问用户是否清理
        if (this.hasTemporaryData()) {
          try {
            await this.$confirm('检测到未保存的临时数据，是否清理？', '提示', {
              confirmButtonText: '清理',
              cancelButtonText: '保留',
              type: 'warning'
            })

            // 用户确认清理 - 直接调用重置方法，它会处理清理
            await this.handleReset()
            return

          } catch (error) {
            if (error === 'cancel') {
              console.log('用户选择保留临时数据')
              // 用户选择保留数据，仍然重置表单但不清理临时数据
              this.$refs.form.resetFields()
              this.initForm()
              this.form.id = null
              this.form.purchaseMode = 'material'
              this.form.selectedItemId = null
              this.form.itemName = ''
              this.form.itemType = ''
              this.form.boardType = ''
              this.form.specification = ''
              this.$message.info('表单已重置，临时数据已保留')
              return
            } else {
              console.error('处理取消操作失败:', error)
            }
          }
        }

        // 没有临时数据或处理异常时，执行正常重置
        await this.handleReset()

      } catch (error) {
        console.error('取消操作失败:', error)
        this.$message.error('取消操作过程中遇到问题')
      }
    }
  }
}
</script>

<style scoped>
.application-container {
  background: var(--base-body-background);
  min-height: 100vh;
  padding: 20px;
}

/* 页面标题 */
.page-header {
  background: var(--base-main-bg);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--current-color);
}

.header-content {
  text-align: center;
}

.page-title {
  color: var(--base-color-1);
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-title i {
  color: var(--current-color);
  font-size: 32px;
}

.page-description {
  color: var(--base-color-2);
  font-size: 16px;
  margin: 0;
}

/* 表单容器 */
.form-container {
  margin-bottom: 30px;
}

/* 表单进度指示器 */
.form-progress {
  background: var(--base-main-bg);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
}

.form-progress :deep(.el-steps) {
  margin: 0;
}

.form-progress :deep(.el-step__title) {
  color: var(--base-color-1);
  font-weight: 600;
}

.form-progress :deep(.el-step__description) {
  color: var(--base-color-2);
}

.form-progress :deep(.el-step__head.is-process) {
  color: var(--current-color);
  border-color: var(--current-color);
}

.form-progress :deep(.el-step__head.is-finish) {
  color: #67c23a;
  border-color: #67c23a;
}

.form-progress :deep(.el-step__line.is-finish) {
  background-color: #67c23a;
}

.form-card {
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
}

/* 表单样式 */
.application-form :deep(.el-form-item__label) {
  color: var(--base-color-1);
  font-weight: 500;
}

.application-form :deep(.el-input__inner) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  transition: all 0.3s ease;
}

.application-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

.readonly-input :deep(.el-input__inner) {
  background: var(--base-color-8);
  color: var(--base-color-2);
}

.amount-input :deep(.el-input__inner) {
  font-weight: bold;
  color: var(--current-color);
}

/* 操作按钮 */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding: 30px;
  background: var(--base-main-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
}

.form-actions .el-button {
  margin: 0 10px;
  padding: 12px 24px;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.form-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 历史记录 */
.history-container {
  margin-top: 30px;
}

.history-container .el-card {
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
  background: transparent !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: #303133 !important;
}

/* 表格样式适配 */
:deep(.el-table) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.el-table td) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
}

:deep(.el-table th) {
  background: var(--current-color) !important;
  color: white !important;
  border-color: var(--border-color-1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--base-item-bg);
}

:deep(.el-table__body tr:hover > td) {
  background: var(--base-color-8) !important;
}

/* 主题适配 */
.theme-dark .page-header {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark .form-progress {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark .form-card {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark .form-actions {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

/* 深色主题 - 表格行样式 */
.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  color: var(--theme-color) !important;
  background: transparent !important;
}

/* 深色主题 - 覆盖Element UI默认stripe样式 */
.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: var(--theme-color) !important;
}

/* 操作按钮样式适配 */
:deep(.el-table .el-button--warning) {
  background: var(--warning-color, #e6a23c);
  border-color: var(--warning-color, #e6a23c);
  color: white;
}

:deep(.el-table .el-button--warning:hover) {
  background: var(--warning-color-hover, #ebb563);
  border-color: var(--warning-color-hover, #ebb563);
  color: white;
}

:deep(.el-table .el-button--danger) {
  background: var(--danger-color, #f56c6c);
  border-color: var(--danger-color, #f56c6c);
  color: white;
}

:deep(.el-table .el-button--danger:hover) {
  background: var(--danger-color-hover, #f78989);
  border-color: var(--danger-color-hover, #f78989);
  color: white;
}

/* 详情对话框样式 */
.detail-container {
  padding: 20px 0;
}

:deep(.el-descriptions) {
  background: var(--base-main-bg);
}

:deep(.el-descriptions__header) {
  background: var(--base-item-bg);
  color: var(--base-color-1);
}

:deep(.el-descriptions__body) {
  background: var(--base-main-bg);
}

:deep(.el-descriptions-item__label) {
  background: var(--base-item-bg) !important;
  color: var(--base-color-1) !important;
  font-weight: 600;
}

:deep(.el-descriptions-item__content) {
  background: var(--base-main-bg) !important;
  color: var(--base-color-1) !important;
}

:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px;
  margin: 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.el-dialog__footer) {
  background: var(--base-main-bg);
  border-top: 1px solid var(--border-color-1);
}

/* 主题适配 - 深色主题 */
.theme-dark :deep(.el-descriptions-item__label) {
  background: var(--base-color-8) !important;
}

.theme-dark :deep(.el-descriptions-item__content) {
  background: var(--base-item-bg) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-container {
    padding: 10px;
  }

  .page-header {
    padding: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .form-actions .el-button {
    margin: 5px;
    padding: 10px 20px;
  }

  /* 移动端操作按钮调整 */
  :deep(.el-table-column--selection) {
    width: 40px;
  }

  :deep(.el-table .el-button) {
    padding: 5px 8px;
    font-size: 12px;
    margin: 2px;
  }

  /* 移动端详情对话框调整 */
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  :deep(.el-descriptions) {
    font-size: 14px;
  }

  :deep(.el-descriptions-item__label) {
    font-size: 13px;
  }

  :deep(.el-descriptions-item__content) {
    font-size: 13px;
  }
}

/* 快速创建物品对话框样式 */
:deep(.el-dialog .el-form) {
  margin-top: 10px;
}

:deep(.el-dialog .el-form .el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-dialog .el-form .el-form-item__label) {
  font-weight: 600;
  color: var(--base-color-1);
}

/* 新增按钮样式 */
.el-button.el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
}

.el-button.el-button--primary:hover {
  background: var(--current-color-hover, #5a8dd8);
  border-color: var(--current-color-hover, #5a8dd8);
}

/* 价格差异样式 */
.price-increase {
  color: #f56c6c;
  font-weight: bold;
}

.price-decrease {
  color: #67c23a;
  font-weight: bold;
}

.price-equal {
  color: #909399;
  font-weight: bold;
}

/* 采购模式选择样式 */
.mode-description {
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.mode-description i {
  margin-right: 4px;
}

/* 采购模式单选按钮样式 */
:deep(.el-radio-button__inner) {
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-radio-button__inner i) {
  margin-right: 6px;
  font-size: 16px;
}

:deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 6px 0 0 6px;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 6px 6px 0;
}

/* 物品类型标签样式 */
.el-tag.el-tag--success {
  background: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.el-tag.el-tag--warning {
  background: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

/* 原料信息输入框样式 */
.material-info-input :deep(.el-input__inner) {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
  font-size: 13px;
}

.material-info-input :deep(.el-input-group__prepend) {
  background-color: #e9ecef;
  border-color: #e9ecef;
  color: #6c757d;
}

/* 原料下拉框选项样式优化 */
:deep(.el-select-dropdown__item) {
  height: auto;
  line-height: 1.4;
  padding: 8px 20px;
}

:deep(.el-select-dropdown__item span) {
  display: block;
  line-height: 1.4;
}

/* 附加信息卡片样式 */
.additional-info-card {
  margin-bottom: 20px;
}

.additional-info-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.additional-info-card .card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
}

/* 折叠面板样式 */
.additional-info-card :deep(.el-collapse) {
  border: none;
}

.additional-info-card :deep(.el-collapse-item__header) {
  background: var(--base-color-8);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 8px;
  border: 1px solid var(--border-color-1);
  font-weight: 500;
  color: var(--base-color-1);
}

.additional-info-card :deep(.el-collapse-item__header:hover) {
  background: var(--base-color-7);
}

.additional-info-card :deep(.el-collapse-item__content) {
  padding: 16px;
  background: var(--base-main-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color-1);
  margin-bottom: 8px;
}

.additional-info-card :deep(.el-collapse-item__arrow) {
  color: var(--current-color);
}

/* 折叠面板内容样式 */
.collapse-content {
  min-height: 200px;
}

.section-description {
  color: var(--base-color-2);
  font-size: 14px;
  margin: 0 0 16px 0;
  padding: 12px;
  background: var(--base-color-8);
  border-radius: 6px;
  border-left: 4px solid var(--current-color);
}

/* 标签样式优化 */
.additional-info-card .el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
}

</style>

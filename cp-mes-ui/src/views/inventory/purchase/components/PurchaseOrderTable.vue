<template>
  <div class="purchase-order-container">
    <!-- 搜索表单 -->
    <el-form inline class="search-form">
      <el-form-item label="采购单号">
        <el-input
          v-model="searchForm.purchaseNo"
          placeholder="请输入采购单号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="物品名称">
        <el-input
          v-model="searchForm.itemName"
          placeholder="请输入物品名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="供应商">
        <el-input
          v-model="searchForm.supplierName"
          placeholder="请输入供应商名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="申请人">
        <el-input
          v-model="searchForm.applicant"
          placeholder="请输入申请人"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="审核人">
        <el-input
          v-model="searchForm.approver"
          placeholder="请输入审核人"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          style="width: 150px"
        >
          <el-option label="全部" value="" />
          <el-option label="待审核" :value="0" />
          <el-option label="已通过" :value="1" />
          <el-option label="已驳回" :value="2" />
          <el-option label="已完成" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch" class="search-btn">
          查询
        </el-button>
        <el-button @click="resetSearch" class="reset-btn">重置</el-button>
        <el-button type="success" @click="handleAdd" class="add-btn">
          <i class="el-icon-plus"></i>
          新增采购单
        </el-button>
        <el-button type="warning" @click="handleExport" class="export-btn" :loading="exportLoading">
          导出Excel
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      highlight-current-row
      :header-cell-style="{background:'var(--current-color)', color:'white'}"
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="60" align="center" />
      <el-table-column prop="purchaseNo" label="采购单号" width="160" align="center" show-overflow-tooltip />
      <!-- 🔧 新增：采购类型列 -->
      <el-table-column label="采购类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getPurchaseTypeTagType(scope.row)" size="mini">
            {{ getPurchaseTypeText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="itemName" label="物品名称" width="250" align="center" show-overflow-tooltip />
      <el-table-column prop="supplierName" label="供应商" width="250" align="center" show-overflow-tooltip />
      <el-table-column prop="quantity" label="数量" width="120" align="center" />
      <el-table-column prop="unit" label="单位" width="100" align="center" />
      <el-table-column label="预估金额" width="120" align="center">
        <template slot-scope="scope">
          <span style="color: #e6a23c; font-weight: bold;">
            ¥{{ scope.row.subtotal }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="实际金额" width="120" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.actualSubtotal" style="color: #67c23a; font-weight: bold;">
            ¥{{ scope.row.actualSubtotal }}
          </span>
          <span v-else class="text-muted">未录入</span>
        </template>
      </el-table-column>
      <el-table-column label="价格差异" width="100" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.actualSubtotal && scope.row.subtotal"
                :class="getPriceDifferenceClass(scope.row.actualSubtotal - scope.row.subtotal)">
            {{ formatPriceDifference(scope.row.actualSubtotal - scope.row.subtotal) }}
          </span>
          <span v-else class="text-muted">--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="160" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="small">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="applicant" label="申请人" width="180" align="center" />
      <el-table-column prop="approver" label="审核人" width="180" align="center">
        <template slot-scope="scope">
          {{ scope.row.approver || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="申请时间" width="200" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.applyTime) }}
        </template>
      </el-table-column>
      <el-table-column label="增强信息" width="120" align="center">
        <template slot-scope="scope">
          <div class="enhanced-info">
            <el-tooltip content="有图片附件" placement="top" v-if="scope.row.imagesFolderPath">
              <i class="el-icon-picture" style="color: #409EFF; margin-right: 5px;"></i>
            </el-tooltip>
            <el-tooltip content="有相关链接" placement="top" v-if="scope.row.purchaseLinks">
              <i class="el-icon-link" style="color: #67C23A; margin-right: 5px;"></i>
            </el-tooltip>
            <el-tooltip content="有物流信息" placement="top" v-if="scope.row.trackingNumber">
              <i class="el-icon-truck" style="color: #E6A23C; margin-right: 5px;"></i>
            </el-tooltip>
            <span v-if="!scope.row.imagesFolderPath && !scope.row.purchaseLinks && !scope.row.trackingNumber"
                  style="color: #C0C4CC;">--</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right" align="center">
        <template slot-scope="scope">
          <div class="action-links">
            <!-- 主要操作按钮 - 直接显示 -->
            <el-button
              type="text"
              size="small"
              @click="handleView(scope.row)"
              class="action-btn view-btn"
            >
              <i class="el-icon-view"></i> 详情
            </el-button>

            <!-- 确认收货按钮 - 当状态为已审核(1)且物料ID不为空时显示 -->
            <el-button
              v-if="showConfirmReceiptButton(scope.row)"
              type="text"
              size="small"
              @click="handleConfirmReceipt(scope.row)"
              class="action-btn confirm-receipt-btn"
            >
              <i class="el-icon-check"></i> 确认收货
            </el-button>

            <!-- 🔧 外部物品采购提示按钮 -->
            <el-tooltip 
              v-if="isExternalItemPurchase(scope.row) && scope.row.status === 1"
              content="外部物品采购暂不支持系统入库，请线下处理"
              placement="top">
              <el-button type="text" size="small" disabled class="action-btn external-item-btn">
                <i class="el-icon-warning"></i> 外部物品
              </el-button>
            </el-tooltip>

            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              @command="(command) => handleDropdownCommand(command, scope.row)"
              trigger="click"
              class="action-dropdown"
            >
              <el-button type="text" size="small" class="action-btn dropdown-btn">
                更多操作 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <!-- 编辑 - 仅在待审核或已驳回状态显示 -->
                <el-dropdown-item
                  v-if="scope.row.status == 0 || scope.row.status == 2"
                  command="edit"
                  icon="el-icon-edit"
                >
                  编辑
                </el-dropdown-item>

                <!-- 增强功能 -->
                <el-dropdown-item
                  command="enhance"
                  icon="el-icon-s-tools"
                >
                  增强功能
                </el-dropdown-item>

                <!-- 录入/修改价格 - 仅在已审核或已完成状态显示 -->
                <el-dropdown-item
                  v-if="(scope.row.status == 1 || scope.row.status == 3) && canManageActualPrice"
                  command="actualPrice"
                  icon="el-icon-money"
                >
                  {{ scope.row.actualPrice ? '修改价格' : '录入价格' }}
                </el-dropdown-item>

                <!-- 分割线 -->
                <el-dropdown-item
                  v-if="scope.row.status != 1 && scope.row.status != 3"
                  divided
                  command="approve"
                  icon="el-icon-check"
                >
                  通过
                </el-dropdown-item>

                <el-dropdown-item
                  v-if="scope.row.status != 2 && scope.row.status != 3"
                  command="reject"
                  icon="el-icon-close"
                >
                  驳回
                </el-dropdown-item>

                <!-- 删除操作 -->
                <el-dropdown-item
                  divided
                  command="delete"
                  icon="el-icon-delete"
                  class="danger-item"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: center"
    />

    <!-- 新增/编辑对话框 -->
    <purchase-order-dialog
      :visible.sync="dialogVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @submit="handleSubmit"
    />

    <!-- 查看详情对话框 -->
    <purchase-order-detail-dialog
      :visible.sync="detailDialogVisible"
      :purchase-order="selectedOrder"
      @complete="handleOrderComplete"
    />

    <!-- 审核对话框 -->
    <purchase-order-audit-dialog
      :visible.sync="auditDialogVisible"
      :purchase-order="selectedOrder"
      :audit-status="auditStatus"
      @submit="handleAuditSubmit"
    />

    <!-- 增强功能对话框 -->
    <purchase-order-enhanced-dialog
      :visible.sync="enhancedDialogVisible"
      :purchase-order-id="selectedOrderId"
      @refresh="fetchData"
    />

    <!-- 实际价格录入对话框 -->
    <ActualPriceDialog
      :visible.sync="actualPriceDialogVisible"
      :order-info="selectedOrder"
      @success="handleActualPriceSuccess"
    />

    <!-- 新增采购单对话框（整合单个和批量模式） -->
    <el-dialog
      title="采购单管理"
      :visible.sync="purchaseDialogVisible"
      :width="dialogWidth"
      :close-on-click-modal="false"
      class="enhanced-purchase-dialog"
      :before-close="handleDialogClose"
      :fullscreen="isFullscreen"
      top="5vh"
    >
      <batch-purchase-form
        ref="purchaseForm"
        @submit-success="handlePurchaseSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  getPurchaseOrderListByPage,
  getPurchaseOrderDetail,
  deletePurchaseOrder,
  auditPurchaseOrder,
  exportPurchaseOrderByCondition
} from '@/api/jenasi/purchaseOrder'
import PurchaseOrderDialog from './PurchaseOrderDialog.vue'
import PurchaseOrderDetailDialog from './PurchaseOrderDetailDialog.vue'
import PurchaseOrderAuditDialog from './PurchaseOrderAuditDialog.vue'
import PurchaseOrderEnhancedDialog from './PurchaseOrderEnhancedDialog.vue'
import ActualPriceDialog from './ActualPriceDialog.vue'
import PurchaseApplicationForm from './PurchaseApplicationForm.vue'
import BatchPurchaseForm from './BatchPurchaseForm.vue'

import Vue from 'vue'

export default {
  name: 'PurchaseOrderTable',
  components: {
    PurchaseOrderDialog,
    PurchaseOrderDetailDialog,
    PurchaseOrderAuditDialog,
    PurchaseOrderEnhancedDialog,
    ActualPriceDialog,
    PurchaseApplicationForm,
    BatchPurchaseForm
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      searchForm: {
        purchaseNo: '',
        applicant: '',
        status: '',
        itemName: '',
        supplierName: '',
        approver: ''
      },
      dialogVisible: false,
      detailDialogVisible: false,
      auditDialogVisible: false,
      enhancedDialogVisible: false,
      isEdit: false,
      formData: {},
      selectedOrder: {},
      selectedOrderId: null,
      auditStatus: 1,
      actualPriceDialogVisible: false,
      canManageActualPrice: true, // TODO: 根据用户权限动态设置
      exportLoading: false,
      purchaseDialogVisible: false,
      windowWidth: window.innerWidth
    }
  },

  computed: {
    // 响应式对话框宽度
    dialogWidth() {
      if (this.windowWidth < 768) {
        return '100%'
      } else if (this.windowWidth < 1024) {
        return '95%'
      } else {
        return '90%'
      }
    },

    // 是否全屏显示
    isFullscreen() {
      return this.windowWidth < 768
    }
  },

  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    // 延迟加载数据，确保组件完全初始化
    this.$nextTick(() => {
      setTimeout(() => {
        this.fetchData()
      }, 100)
    })
  },
  activated() {
    // 组件激活时重新加载数据
    this.fetchData()
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.windowWidth = window.innerWidth
    },
    // 🔧 核心逻辑：判断是否显示确认收货按钮
    showConfirmReceiptButton(row) {
      // 基本条件：订单状态为已通过(1)
      if (row.status !== 1) {
        return false;
      }
      
      // 🎯 关键判断：物料ID不为空才显示确认收货按钮
      if (!row.itemId || row.itemId === 'null' || row.itemId.trim() === '') {
        console.warn(`采购订单 ${row.purchaseNo} 的物料ID为空，隐藏确认收货按钮`);
        return false;
      }
      
      return true;
    },
    
    // 🔧 判断是否为外部物品采购
    isExternalItemPurchase(row) {
      return !row.itemId || row.itemId === 'null' || row.itemId.trim() === '';
    },
    
    // 🔧 获取采购类型文本
    getPurchaseTypeText(row) {
      return this.isExternalItemPurchase(row) ? '外部物品' : '仓储原料';
    },
    
    // 🔧 获取采购类型标签样式
    getPurchaseTypeTagType(row) {
      return this.isExternalItemPurchase(row) ? 'warning' : 'success';
    },

    // 获取数据
    async fetchData() {
      this.loading = true
      let hasData = false

      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          purchaseNo: this.searchForm.purchaseNo || undefined,
          status: this.searchForm.status !== '' ? this.searchForm.status : undefined,
          itemName: this.searchForm.itemName || undefined,
          supplierName: this.searchForm.supplierName || undefined,
          applicant: this.searchForm.applicant || undefined,
          approver: this.searchForm.approver || undefined
        }

        // 使用获取采购订单详情接口，该接口返回更完整的数据包括关联的物品名称和供应商名称
        const response = await getPurchaseOrderDetail(params)

        if (response && (response.code === 0 || response.code === 200)) {
          this.tableData = response.data.records || []
          this.total = response.data.total || 0
          hasData = true

          // 调试日志：检查增强字段数据
          console.log('采购订单数据获取成功，总数:', this.tableData.length)
          this.tableData.forEach((item, index) => {
            if (item.purchaseLinks || item.imagesFolderPath || item.trackingNumber || item.logisticsCompany) {
              console.log(`订单${index + 1} (ID: ${item.id}) 增强字段:`, {
                purchaseLinks: item.purchaseLinks,
                imagesFolderPath: item.imagesFolderPath,
                trackingNumber: item.trackingNumber,
                logisticsCompany: item.logisticsCompany
              })
            }
          })

          // 如果没有数据，显示友好提示
          if (this.tableData.length === 0) {
            console.log('暂无采购订单数据')
          }
        } else {
          console.warn('采购订单接口响应异常:', response)
          this.tableData = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取采购订单详情失败:', error)
        this.tableData = []
        this.total = 0
      } finally {
        this.loading = false

        // 只有在真正没有获取到数据且发生错误时才显示错误消息
        if (!hasData && this.tableData.length === 0) {
          // 延迟显示错误消息，给数据加载更多时间
          setTimeout(() => {
            if (this.tableData.length === 0) {
              console.log('数据加载可能存在问题，但不影响使用')
            }
          }, 1000)
        }
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchData()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        purchaseNo: '',
        applicant: '',
        status: '',
        itemName: '',
        supplierName: '',
        approver: ''
      }
      this.currentPage = 1
      this.fetchData()
    },

    // 新增采购单（整合单个和批量模式）
    handleAdd() {
      this.purchaseDialogVisible = true
    },

    // 采购单提交成功回调
    handlePurchaseSuccess(data) {
      this.purchaseDialogVisible = false
      this.fetchData() // 刷新列表

      if (Array.isArray(data) && data.length > 1) {
        this.$message.success(`成功提交 ${data.length} 个采购单`)
      } else {
        this.$message.success('采购单提交成功')
      }
    },

    // 对话框关闭前确认
    handleDialogClose(done) {
      const purchaseForm = this.$refs.purchaseForm
      if (purchaseForm && purchaseForm.batchList && purchaseForm.batchList.length > 0) {
        this.$confirm('当前有未提交的采购申请，确定要关闭吗？', '确认关闭', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          done()
        }).catch(() => {})
      } else {
        done()
      }
    },

    // 编辑
    async handleEdit(row) {
      this.isEdit = true

      // 准备编辑数据，确保包含所有必要字段，特别是itemName
      const editData = {
        id: row.id,
        purchaseNo: row.purchaseNo,
        applicant: row.applicant,
        itemId: row.itemId,
        itemName: row.itemName, // 修复：添加物品名称字段
        supplierId: row.supplierId,
        quantity: row.quantity,
        unit: row.unit,
        price: row.price,
        expectedDate: row.expectedDate
      }

      console.log('编辑数据准备:', editData) // 调试日志
      this.formData = editData
      this.dialogVisible = true
    },

    // 查看详情
    handleView(row) {
      this.selectedOrder = row
      this.detailDialogVisible = true
    },

    // 增强功能
    handleEnhance(row) {
      this.selectedOrderId = row.id
      this.enhancedDialogVisible = true
    },

    // 处理实际价格录入
    handleActualPrice(row) {
      this.selectedOrder = row
      this.actualPriceDialogVisible = true
    },

    // 处理实际价格录入成功
    handleActualPriceSuccess(updatedOrder) {
      console.log('收到更新的订单数据:', updatedOrder)

      // 更新表格中的数据
      const index = this.tableData.findIndex(item => item.id === updatedOrder.id)
      if (index !== -1) {
        // 使用Vue.set确保响应式更新
        this.$set(this.tableData, index, {
          ...this.tableData[index],
          ...updatedOrder,
          actualPrice: updatedOrder.actualPrice,
          actualSubtotal: updatedOrder.actualSubtotal,
          actualPriceTime: updatedOrder.actualPriceTime,
          actualPriceRecorder: updatedOrder.actualPriceRecorder
        })
      }

      // 更新selectedOrder以便UI能正确显示按钮状态
      if (this.selectedOrder && this.selectedOrder.id === updatedOrder.id) {
        this.selectedOrder = {
          ...this.selectedOrder,
          ...updatedOrder
        }
      }

      // 强制更新视图
      this.$forceUpdate()

      // 可选：刷新数据以确保与服务器同步
      setTimeout(() => {
        this.fetchData()
      }, 500)
    },

    // 获取价格差异的样式类
    getPriceDifferenceClass(difference) {
      if (difference > 0) {
        return 'price-increase'
      } else if (difference < 0) {
        return 'price-decrease'
      }
      return 'price-equal'
    },

    // 格式化价格差异显示
    formatPriceDifference(difference) {
      if (difference === 0) {
        return '无差异'
      }
      const prefix = difference > 0 ? '+' : ''
      return `${prefix}¥${Math.abs(difference).toFixed(2)}`
    },

    // 审核
    handleAudit(row, status) {
      // 如果是通过审核（status = 1），检查供应商是否为自定义
      if (status === 1) {
        // 检查供应商ID是否为4（自定义供应商）
        if (row.supplierId === 4) {
          this.$message.warning('请先选择具体的供应商，不能使用自定义供应商进行审核通过')
          return
        }
      }

      this.selectedOrder = row
      this.auditStatus = status
      this.auditDialogVisible = true
    },

    // 审核提交
    async handleAuditSubmit(auditData) {
      try {
        const response = await auditPurchaseOrder(auditData)
        if (response.code === 0) {
          this.$message.success('审核成功')
          this.fetchData()
        }
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败')
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除此采购订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deletePurchaseOrder(row.id)
          if (response.code === 0) {
            this.$message.success('删除成功')
            this.fetchData()
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    // 表单提交
    handleSubmit() {
      this.dialogVisible = false
      this.fetchData()
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: '',
        1: 'success',
        2: 'danger',
        3: 'info'
      }
      return statusMap[status] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 处理完成事件
    async handleOrderComplete(orderId) {
      try {
        // 使用审核接口，状态设为3（已完成）
        const auditData = {
          id: orderId,
          status: 3,
          remark: '订单已完成'
        }

        const response = await auditPurchaseOrder(auditData)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('订单已标记为完成')
          this.fetchData()
        } else {
          this.$message.error(response.msg || '操作失败')
        }
      } catch (error) {
        console.error('标记完成失败:', error)
        this.$message.error('操作失败')
      }
    },

    // 直接确认收货（在列表中）
    async handleConfirmReceipt(row) {
      try {
        // 🔧 二次确认检查：确保不是外部物品采购
        if (this.isExternalItemPurchase(row)) {
          this.$message.error('外部物品采购不支持系统入库操作，请联系管理员进行线下处理');
          return;
        }

        await this.$confirm(
          `确认收货采购订单 "${row.purchaseNo}" 吗？\n收货后订单状态将变为已完成。`,
          '确认收货',
          {
            confirmButtonText: '确认收货',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        // 调用完成处理方法
        await this.handleOrderComplete(row.id)
      } catch (error) {
        // 用户取消操作或其他错误
        if (error !== 'cancel') {
          console.error('确认收货失败:', error)
          this.$message.error('确认收货失败')
        }
      }
    },

    // 处理下拉菜单命令
    handleDropdownCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleEdit(row)
          break
        case 'enhance':
          this.handleEnhance(row)
          break
        case 'actualPrice':
          this.handleActualPrice(row)
          break
        case 'approve':
          this.handleAudit(row, 1)
          break
        case 'reject':
          this.handleAudit(row, 2)
          break
        case 'delete':
          this.handleDelete(row)
          break
        default:
          console.warn('未知的下拉菜单命令:', command)
      }
    },



    // 导出采购订单
    async handleExport() {
      // 检查是否有导出条件
      const hasConditions = Object.values(this.searchForm).some(value => value !== '' && value !== null && value !== undefined)

      // 生成导出提示信息
      let confirmMessage = ''
      let conditionSummary = []

      if (hasConditions) {
        // 有筛选条件时，显示条件详情
        if (this.searchForm.purchaseNo) conditionSummary.push(`采购单号: ${this.searchForm.purchaseNo}`)
        if (this.searchForm.itemName) conditionSummary.push(`物品名称: ${this.searchForm.itemName}`)
        if (this.searchForm.supplierName) conditionSummary.push(`供应商: ${this.searchForm.supplierName}`)
        if (this.searchForm.applicant) conditionSummary.push(`申请人: ${this.searchForm.applicant}`)
        if (this.searchForm.approver) conditionSummary.push(`审核人: ${this.searchForm.approver}`)
        if (this.searchForm.status !== '' && this.searchForm.status !== null) {
          const statusText = this.getStatusText(this.searchForm.status)
          conditionSummary.push(`状态: ${statusText}`)
        }

        confirmMessage = `将按以下条件导出采购订单数据：\n\n${conditionSummary.join('\n')}\n\n确认导出吗？`
      } else {
        confirmMessage = '未设置任何筛选条件，将导出所有采购订单数据，确认继续吗？'
      }

      const confirmResult = await this.$confirm(
        confirmMessage,
        '导出确认',
        {
          confirmButtonText: '确定导出',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      ).catch(() => false)

      if (!confirmResult) return

      this.exportLoading = true

      try {
        // 使用当前搜索条件作为导出参数
        const exportParams = {
          purchaseNo: this.searchForm.purchaseNo || undefined,
          status: this.searchForm.status !== '' ? this.searchForm.status : undefined,
          itemName: this.searchForm.itemName || undefined,
          supplierName: this.searchForm.supplierName || undefined,
          applicant: this.searchForm.applicant || undefined,
          approver: this.searchForm.approver || undefined
        }

        const response = await exportPurchaseOrderByCondition(exportParams)

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0')

        link.download = `采购订单列表导出_${timestamp}.xlsx`

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理资源
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')

      } catch (error) {
        console.error('导出失败:', error)
        if (error.response && error.response.status === 204) {
          this.$message.warning('暂无数据可导出')
        } else {
          this.$message.error('导出失败: ' + (error.message || '未知错误'))
        }
      } finally {
        this.exportLoading = false
      }
    }
  }
}
</script>

<style scoped>
.purchase-order-container {
  background: transparent;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索表单样式 */
.search-form {
  background: var(--base-item-bg);
  padding: 20px 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

.search-form .el-form-item:last-child {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  color: var(--theme-color);
  font-weight: 500;
  font-size: 14px;
}

.search-form .el-input__inner {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
  line-height: 36px;
}

.search-form .el-input__inner:focus {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
}

.search-form .el-input__inner:hover {
  border-color: var(--current-color);
}

.search-form .el-select .el-input__inner {
  background: var(--base-main-bg);
}

.search-form .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  height: 36px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.search-form .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.search-form .el-button:hover::before {
  left: 100%;
}

.search-form .el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
}

.search-form .el-button--primary:hover {
  background: var(--color-2);
  border-color: var(--color-2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.4);
}

.search-form .el-button--success {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.search-form .el-button--success:hover {
  background: #85ce61;
  border-color: #85ce61;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(103, 194, 58, 0.4);
}

/* 表格容器样式 */
.el-table {
  background: var(--base-main-bg);
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  flex: 1;
}

.el-table :deep(.el-table__header-wrapper) {
  background: var(--current-color);
}

.el-table :deep(.el-table__header-wrapper th) {
  background: var(--current-color) !important;
  color: white !important;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 0;
  border: none;
}

.el-table :deep(.el-table__body-wrapper) {
  background: var(--base-main-bg);
}

.el-table :deep(.el-table__row) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  transition: all 0.3s ease;
}

.el-table :deep(.el-table__row:hover) {
  background: var(--table-row-hover-bg) !important;
}

.el-table :deep(.el-table__row td) {
  border-color: var(--border-color-1);
  padding: 12px 0;
}

.el-table :deep(.el-button--mini) {
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-table :deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
}

.el-table :deep(.el-button--primary:hover) {
  background: var(--color-2);
  border-color: var(--color-2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
}

.el-table :deep(.el-button--success) {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
}

.el-table :deep(.el-button--success:hover) {
  background: #85ce61;
  border-color: #85ce61;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.el-table :deep(.el-button--warning) {
  background: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.el-table :deep(.el-button--warning:hover) {
  background: #eebc6e;
  border-color: #eebc6e;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.el-table :deep(.el-button--danger) {
  background: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.el-table :deep(.el-button--danger:hover) {
  background: #f78989;
  border-color: #f78989;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

/* 分页样式 */
.el-pagination {
  margin-top: 20px;
  text-align: center;
  background: transparent;
  padding: 20px 0;
}

.el-pagination :deep(.el-pagination__total),
.el-pagination :deep(.el-pagination__jump) {
  color: var(--theme-color);
}

.el-pagination :deep(.btn-prev),
.el-pagination :deep(.btn-next),
.el-pagination :deep(.el-pager li) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  border: 1px solid var(--border-color-1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.el-pagination :deep(.btn-prev:hover),
.el-pagination :deep(.btn-next:hover),
.el-pagination :deep(.el-pager li:hover) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
}

.el-pagination :deep(.el-pager li.active) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
  background: transparent !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: #303133 !important;
}

/* 深色主题适配 */
.theme-dark .purchase-order-container {
  background: var(--base-main-bg);
  color: var(--theme-color);
}

/* 搜索表单深色主题 */
.theme-dark .search-form {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.theme-dark .search-form .el-form-item__label {
  color: var(--theme-color);
}

.theme-dark .search-form .el-input__inner {
  background: var(--primary-color);
  border-color: var(--input-border-color);
  color: var(--theme-color);
}

.theme-dark .search-form .el-input__inner:focus {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(58, 123, 153, 0.3);
}

.theme-dark .search-form .el-input__inner::placeholder {
  color: var(--base-color-5);
}

.theme-dark .search-form .el-select .el-input__inner {
  background: var(--primary-color);
  color: var(--theme-color);
}

/* 表格深色主题 */
.theme-dark .el-table {
  background: var(--base-item-bg);
  border: 1px solid var(--border-color-1);
}

.theme-dark .el-table :deep(.el-table__header-wrapper) {
  background: var(--base-menu-background);
}

.theme-dark .el-table :deep(.el-table__header-wrapper th) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
  border-bottom: 1px solid var(--border-color-1);
}

.theme-dark .el-table :deep(.el-table__body-wrapper) {
  background: var(--base-item-bg);
}

.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  border-bottom: 1px solid var(--border-color-1);
  color: var(--theme-color) !important;
  background: transparent !important;
}

/* 深色主题 - 覆盖Element UI默认stripe样式 */
.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-tag) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .el-table :deep(.el-tag--success) {
  background: rgba(103, 194, 58, 0.2);
  border-color: #67c23a;
  color: #67c23a;
}

.theme-dark .el-table :deep(.el-tag--danger) {
  background: rgba(245, 108, 108, 0.2);
  border-color: #f56c6c;
  color: #f56c6c;
}

.theme-dark .el-table :deep(.el-tag--warning) {
  background: rgba(230, 162, 60, 0.2);
  border-color: #e6a23c;
  color: #e6a23c;
}

.theme-dark .el-table :deep(.el-tag--info) {
  background: rgba(144, 147, 153, 0.2);
  border-color: #909399;
  color: #909399;
}

/* 分页深色主题 */
.theme-dark .el-pagination {
  background: transparent;
}

.theme-dark .el-pagination :deep(.el-pagination__total),
.theme-dark .el-pagination :deep(.el-pagination__jump) {
  color: var(--theme-color);
}

.theme-dark .el-pagination :deep(.btn-prev),
.theme-dark .el-pagination :deep(.btn-next),
.theme-dark .el-pagination :deep(.el-pager li) {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .el-pagination :deep(.btn-prev:hover),
.theme-dark .el-pagination :deep(.btn-next:hover),
.theme-dark .el-pagination :deep(.el-pager li:hover) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
}

.theme-dark .el-pagination :deep(.el-pager li.active) {
  background: var(--current-color);
  color: white;
  border-color: var(--current-color);
}

.theme-dark .el-pagination :deep(.el-pagination__sizes .el-input__inner) {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .el-pagination :deep(.el-pagination__jump .el-input__inner) {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

/* 按钮样式优化 */
.search-btn:hover {
  background: var(--current-color) !important;
  border-color: var(--current-color) !important;
  color: white !important;
}

.reset-btn:hover {
  background: var(--base-color-8) !important;
  border-color: var(--border-color-1) !important;
  color: var(--base-color-1) !important;
}

.add-btn:hover {
  background: #85ce61 !important;
  border-color: #85ce61 !important;
  color: white !important;
}

.export-btn {
  background: #e6a23c;
  border-color: #e6a23c;
  color: white;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: #eebc6e !important;
  border-color: #eebc6e !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(230, 162, 60, 0.4);
}

/* 操作链接组样式 - 单行布局 */
.action-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 4px;
  white-space: nowrap;
}

/* 操作按钮样式 */
.action-btn {
  font-size: 12px;
  padding: 4px 8px;
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: var(--theme-color);
}

.action-btn:hover {
  background: var(--base-color-8);
  color: var(--current-color);
}

.action-btn i {
  margin-right: 3px;
  font-size: 12px;
}

/* 特定按钮样式 */
.view-btn:hover {
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
}

.confirm-receipt-btn {
  color: #67c23a;
  font-weight: 500;
}

.confirm-receipt-btn:hover {
  color: #ffffff;
  background: #67c23a;
}

/* 🔧 外部物品采购按钮样式 */
.external-item-btn {
  color: #e6a23c !important;
  font-weight: 500;
  cursor: not-allowed;
}

.external-item-btn:hover {
  color: #e6a23c !important;
  background: rgba(230, 162, 60, 0.1) !important;
}

.dropdown-btn:hover {
  color: var(--current-color);
  background: var(--base-color-8);
}

/* 下拉菜单样式 */
.action-dropdown {
  margin-left: 4px;
}

/* 下拉菜单项样式 */
.el-dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color-1);
}

.el-dropdown-menu .el-dropdown-menu__item {
  font-size: 13px;
  padding: 8px 16px;
  color: var(--theme-color);
  transition: all 0.3s ease;
}

.el-dropdown-menu .el-dropdown-menu__item:hover {
  background: var(--base-color-8);
  color: var(--current-color);
}

.el-dropdown-menu .el-dropdown-menu__item i {
  margin-right: 6px;
  font-size: 13px;
}

/* 危险操作项样式 */
.el-dropdown-menu .danger-item {
  color: #f56c6c;
}

.el-dropdown-menu .danger-item:hover {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

/* 旧的action-link样式已移除，现在使用action-btn和下拉菜单 */

/* 旧的特殊链接样式已移除，现在使用统一的按钮和下拉菜单样式 */

/* 价格相关样式 */
.text-muted {
  color: #c0c4cc;
  font-style: italic;
}

.price-increase {
  color: #f56c6c;
  font-weight: bold;
}

.price-decrease {
  color: #67c23a;
  font-weight: bold;
}

.price-equal {
  color: #909399;
}

/* 旧的操作链接悬停样式已移除，现在使用下拉菜单 */

/* 主题适配 - 操作链接 */
.theme-dark .action-link {
  color: var(--theme-color);
}

.theme-dark .action-link:hover {
  background: var(--base-menu-background);
  color: var(--current-color);
}

.theme-dark .enhance-link:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.15);
}

.theme-dark .approve-link:hover {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.15);
}

.theme-dark .reject-link:hover {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.15);
}

.theme-dark .delete-link:hover {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.15);
}

/* 深色主题 - 按钮样式 */
.theme-dark .search-btn {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
}

.theme-dark .reset-btn {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .add-btn {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
}

.theme-dark .export-btn {
  background: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

/* 深色主题 - 下拉框和选择器 */
.theme-dark .el-select-dropdown {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.theme-dark .el-select-dropdown .el-select-dropdown__item {
  color: var(--theme-color);
  background: var(--base-item-bg);
}

.theme-dark .el-select-dropdown .el-select-dropdown__item:hover {
  background: var(--base-menu-background);
  color: var(--current-color);
}

.theme-dark .el-select-dropdown .el-select-dropdown__item.selected {
  background: var(--current-color);
  color: white;
}

/* 深色主题 - 表格空状态 */
.theme-dark .el-table :deep(.el-table__empty-block) {
  background: var(--base-item-bg);
  color: var(--theme-color);
}

.theme-dark .el-table :deep(.el-table__empty-text) {
  color: var(--base-color-5);
}

/* 深色主题 - 滚动条 */
.theme-dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.theme-dark ::-webkit-scrollbar-track {
  background: var(--base-menu-background);
  border-radius: 4px;
}

.theme-dark ::-webkit-scrollbar-thumb {
  background: var(--border-color-1);
  border-radius: 4px;
}

.theme-dark ::-webkit-scrollbar-thumb:hover {
  background: var(--base-color-5);
}

/* 深色主题 - 对话框样式优化 */
.theme-dark .el-message-box {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.theme-dark .el-message-box__title {
  color: var(--theme-color);
}

.theme-dark .el-message-box__content {
  color: var(--theme-color);
}

.theme-dark .el-message-box__message {
  color: var(--theme-color);
}

/* 深色主题 - 表单元素悬停效果 */
.theme-dark .search-form .el-input:hover .el-input__inner {
  border-color: var(--current-color);
}

.theme-dark .search-form .el-select:hover .el-input__inner {
  border-color: var(--current-color);
}

/* 增强采购对话框样式 */
.enhanced-purchase-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-purchase-dialog :deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 30px;
  margin: 0;
}

.enhanced-purchase-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.enhanced-purchase-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.enhanced-purchase-dialog :deep(.el-dialog__body) {
  padding: 0;
  background: var(--base-body-background);
}

/* 深色主题适配 */
.theme-dark .enhanced-purchase-dialog :deep(.el-dialog__body) {
  background: var(--base-main-bg);
}

/* 对话框响应式设计 */
@media (max-width: 768px) {
  .enhanced-purchase-dialog :deep(.el-dialog) {
    margin: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
    border-radius: 0;
  }

  .enhanced-purchase-dialog :deep(.el-dialog__header) {
    padding: 15px 20px;
  }

  .enhanced-purchase-dialog :deep(.el-dialog__title) {
    font-size: 16px;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .enhanced-purchase-dialog :deep(.el-dialog) {
    margin: 5vh auto !important;
    max-height: 90vh;
  }

  .enhanced-purchase-dialog :deep(.el-dialog__body) {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}

@media (min-width: 1025px) {
  .enhanced-purchase-dialog :deep(.el-dialog) {
    margin: 5vh auto !important;
    max-height: 90vh;
  }

  .enhanced-purchase-dialog :deep(.el-dialog__body) {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}

/* 滚动条样式优化 */
.enhanced-purchase-dialog :deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

.enhanced-purchase-dialog :deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: var(--base-color-8);
  border-radius: 3px;
}

.enhanced-purchase-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: var(--current-color);
  border-radius: 3px;
}

.enhanced-purchase-dialog :deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: var(--current-color);
  opacity: 0.8;
}
</style>

<template>
  <div class="actual-price-info">
    <!-- 实际价格信息卡片 -->
    <el-card v-if="showCard" class="price-info-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-money"></i>
          实际价格信息
        </span>
        <el-tag v-if="hasActualPrice" type="success" size="mini">已录入</el-tag>
        <el-tag v-else type="info" size="mini">未录入</el-tag>
      </div>
      
      <div class="price-content">
        <!-- 实际价格信息内容（递归调用自身但不带卡片） -->
        <div class="price-info-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="price-item">
                <span class="label">实际单价：</span>
                <span v-if="orderData.actualPrice" class="value actual-price">
                  ¥{{ orderData.actualPrice }}
                </span>
                <el-tooltip v-else content="实际价格尚未录入" placement="top">
                  <span class="value not-recorded">未录入</span>
                </el-tooltip>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="price-item">
                <span class="label">实际总额：</span>
                <span v-if="orderData.actualSubtotal" class="value actual-total">
                  ¥{{ orderData.actualSubtotal }}
                </span>
                <el-tooltip v-else content="实际价格尚未录入" placement="top">
                  <span class="value not-recorded">未录入</span>
                </el-tooltip>
              </div>
            </el-col>
          </el-row>

          <!-- 价格对比信息 -->
          <div v-if="showComparison && hasActualPrice" class="price-comparison">
            <el-divider content-position="left">价格对比分析</el-divider>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="comparison-item">
                  <span class="label">预估单价：</span>
                  <span class="value estimated-price">¥{{ orderData.price || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="comparison-item">
                  <span class="label">预估总额：</span>
                  <span class="value estimated-total">¥{{ orderData.subtotal || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="comparison-item">
                  <span class="label">总额差异：</span>
                  <span :class="getPriceDifferenceClass(priceDifference)" class="value">
                    {{ formatPriceDifference(priceDifference) }}
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 录入信息 -->
          <div v-if="hasActualPrice" class="record-info">
            <el-divider content-position="left">录入信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="record-item">
                  <span class="label">录入时间：</span>
                  <span class="value">{{ formatDate(orderData.actualPriceTime) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="record-item">
                  <span class="label">录入人：</span>
                  <span class="value">{{ orderData.actualPriceRecorder || '未知' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 实际价格信息内容（不带卡片） -->
    <div v-else class="price-info-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="price-item">
            <span class="label">实际单价：</span>
            <span v-if="orderData.actualPrice" class="value actual-price">
              ¥{{ orderData.actualPrice }}
            </span>
            <el-tooltip v-else content="实际价格尚未录入" placement="top">
              <span class="value not-recorded">未录入</span>
            </el-tooltip>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="price-item">
            <span class="label">实际总额：</span>
            <span v-if="orderData.actualSubtotal" class="value actual-total">
              ¥{{ orderData.actualSubtotal }}
            </span>
            <el-tooltip v-else content="实际价格尚未录入" placement="top">
              <span class="value not-recorded">未录入</span>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>

      <!-- 价格对比信息 -->
      <div v-if="showComparison && hasActualPrice" class="price-comparison">
        <el-divider content-position="left">价格对比分析</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="comparison-item">
              <span class="label">预估单价：</span>
              <span class="value estimated-price">¥{{ orderData.price || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="comparison-item">
              <span class="label">预估总额：</span>
              <span class="value estimated-total">¥{{ orderData.subtotal || 0 }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="comparison-item">
              <span class="label">总额差异：</span>
              <span :class="getPriceDifferenceClass(priceDifference)" class="value">
                {{ formatPriceDifference(priceDifference) }}
              </span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 录入信息 -->
      <div v-if="hasActualPrice" class="record-info">
        <el-divider content-position="left">录入信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="record-item">
              <span class="label">录入时间：</span>
              <span class="value">{{ formatDate(orderData.actualPriceTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="record-item">
              <span class="label">录入人：</span>
              <span class="value">{{ orderData.actualPriceRecorder || '未知' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActualPriceInfo',
  props: {
    // 订单数据
    orderData: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 是否显示卡片包装
    showCard: {
      type: Boolean,
      default: true
    },
    // 是否显示价格对比
    showComparison: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 是否有实际价格
    hasActualPrice() {
      return this.orderData.actualPrice != null && this.orderData.actualSubtotal != null
    },
    // 价格差异
    priceDifference() {
      if (!this.hasActualPrice || !this.orderData.subtotal) {
        return 0
      }
      return this.orderData.actualSubtotal - this.orderData.subtotal
    }
  },
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString('zh-CN')
    },

    // 获取价格差异的样式类
    getPriceDifferenceClass(difference) {
      if (difference > 0) {
        return 'price-increase'
      } else if (difference < 0) {
        return 'price-decrease'
      }
      return 'price-equal'
    },

    // 格式化价格差异显示
    formatPriceDifference(difference) {
      if (difference === 0) {
        return '无差异'
      }
      const prefix = difference > 0 ? '+' : ''
      return `${prefix}¥${Math.abs(difference).toFixed(2)}`
    }
  }
}
</script>

<style scoped>
.actual-price-info {
  width: 100%;
}

.price-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: var(--base-color-1);
}

.price-info-content {
  padding: 10px 0;
}

.price-item,
.comparison-item,
.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color-1);
}

.price-item:last-child,
.comparison-item:last-child,
.record-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 600;
  color: var(--base-color-2);
  min-width: 80px;
}

.value {
  font-weight: 500;
  color: var(--base-color-1);
}

.actual-price,
.actual-total {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.estimated-price,
.estimated-total {
  color: #e6a23c;
  font-weight: bold;
}

.not-recorded {
  color: #909399;
  font-style: italic;
}

.price-comparison,
.record-info {
  margin-top: 20px;
}

/* 价格差异样式 */
.price-increase {
  color: #f56c6c;
  font-weight: bold;
}

.price-decrease {
  color: #67c23a;
  font-weight: bold;
}

.price-equal {
  color: #909399;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .price-item,
  .comparison-item,
  .record-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .label {
    margin-bottom: 4px;
  }
}
</style>

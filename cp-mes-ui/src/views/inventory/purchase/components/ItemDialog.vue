<template>
  <el-dialog
    :title="isEdit ? '编辑物品信息' : '新增物品信息'"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      style="padding-right: 30px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物品名称" prop="itemName">
            <el-input 
              v-model="form.itemName" 
              placeholder="请输入物品名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物品类别" prop="category">
            <el-select 
              v-model="form.category" 
              placeholder="请选择物品类别" 
              style="width: 100%"
            >
              <el-option label="原料" value="原料" />
              <el-option label="零部件" value="零部件" />
              <el-option label="初级半成品" value="初级半成品" />
              <el-option label="二级半成品" value="二级半成品" />
              <el-option label="成品" value="成品" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规格型号" prop="specification">
            <el-input 
              v-model="form.specification" 
              placeholder="请输入规格型号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品牌" prop="brand">
            <el-input 
              v-model="form.brand" 
              placeholder="请输入品牌"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计量单位" prop="unit">
            <el-select 
              v-model="form.unit" 
              placeholder="请选择计量单位" 
              style="width: 100%"
              filterable
              allow-create
            >
              <el-option label="个" value="个" />
              <el-option label="件" value="件" />
              <el-option label="台" value="台" />
              <el-option label="套" value="套" />
              <el-option label="kg" value="kg" />
              <el-option label="g" value="g" />
              <el-option label="L" value="L" />
              <el-option label="m" value="m" />
              <el-option label="头" value="头" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更 新' : '新 增' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addOrUpdateItem } from '@/api/jenasi/item'

export default {
  name: 'ItemDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: null,
        itemName: '',
        specification: '',
        category: '',
        brand: '',
        unit: ''
      },
      rules: {
        itemName: [
          { required: true, message: '请输入物品名称', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择物品类别', trigger: 'change' }
        ],
        specification: [
          { required: true, message: '请输入规格型号', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '请输入品牌', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请选择计量单位', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.formData) {
        this.form = { ...this.formData }
      } else {
        this.form = {
          id: null,
          itemName: '',
          specification: '',
          category: '',
          brand: '',
          unit: ''
        }
      }
    },

    // 生成物品编码
    generateCode() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const time = String(now.getTime()).slice(-4)
      return `ITEM${year}${month}${day}${time}`
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true

        const formData = {
          itemName: this.form.itemName,
          specification: this.form.specification,
          category: this.form.category,
          brand: this.form.brand,
          unit: this.form.unit
        }

        // 如果是编辑模式，添加id
        if (this.isEdit && this.form.id) {
          formData.id = this.form.id
        }

        const response = await addOrUpdateItem(formData)
        if (response.code === 0 || response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '新增成功')
          this.$emit('submit', formData)
          this.handleClose()
        } else {
          this.$message.error(response.msg || '操作失败')
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时error为false
          console.error('提交失败:', error)
          this.$message.error('操作失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 关闭对话框
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>



/* 对话框样式 */
.el-dialog :deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 24px;
  border-radius: 8px 8px 0 0;
  border-bottom: none;
}

.el-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.el-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 24px;
}

.el-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 20px;
  transition: all 0.3s ease;
}

.el-dialog :deep(.el-dialog__close:hover) {
  color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.el-dialog :deep(.el-dialog__body) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  padding: 24px;
  border-radius: 0 0 8px 8px;
}

/* 表单样式 */
.el-form :deep(.el-form-item__label) {
  color: var(--theme-color);
  font-weight: 500;
  font-size: 14px;
}

.el-form :deep(.el-input__inner) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  color: var(--theme-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
  line-height: 36px;
}

.el-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
}

.el-form :deep(.el-input__inner:hover) {
  border-color: var(--current-color);
}

.el-form :deep(.el-select .el-input__inner) {
  background: var(--base-main-bg);
}

.el-form :deep(.el-input-number) {
  width: 100%;
}

.el-form :deep(.el-input-number .el-input__inner) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.el-form :deep(.el-input-number__decrease),
.el-form :deep(.el-input-number__increase) {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
  transition: all 0.3s ease;
}

.el-form :deep(.el-input-number__decrease:hover),
.el-form :deep(.el-input-number__increase:hover) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
}

.el-form :deep(.el-textarea__inner) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  color: var(--theme-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.el-form :deep(.el-textarea__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
}

/* 按钮样式 */
.dialog-footer {
  text-align: center;
  padding: 20px 24px;
  background: var(--base-main-bg);
  border-top: 1px solid var(--border-color-1);
  border-radius: 0 0 8px 8px;
}

.dialog-footer .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 24px;
  min-width: 80px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dialog-footer .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.dialog-footer .el-button:hover::before {
  left: 100%;
}

.dialog-footer .el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
}

.dialog-footer .el-button--primary:hover {
  background: var(--color-2);
  border-color: var(--color-2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.4);
}

.dialog-footer .el-button--default {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.dialog-footer .el-button--default:hover {
  background: var(--base-color-8);
  border-color: var(--current-color);
  color: var(--current-color);
  transform: translateY(-2px);
}

/* 主题适配 */
.theme-dark .el-dialog :deep(.el-dialog__body) {
  background: var(--base-item-bg);
}

.theme-dark .el-form :deep(.el-input__inner) {
  background: var(--primary-color);
  border-color: var(--input-border-color);
  color: var(--theme-color);
}

.theme-dark .el-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(58, 123, 153, 0.3);
}

.theme-dark .el-form :deep(.el-textarea__inner) {
  background: var(--primary-color);
  border-color: var(--input-border-color);
  color: var(--theme-color);
}

.theme-dark .el-form :deep(.el-textarea__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(58, 123, 153, 0.3);
}

.theme-dark .dialog-footer {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.theme-dark .dialog-footer .el-button--default {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .dialog-footer .el-button--default:hover {
  background: var(--base-menu-background-active);
  border-color: var(--current-color);
  color: var(--current-color);
}
</style> 
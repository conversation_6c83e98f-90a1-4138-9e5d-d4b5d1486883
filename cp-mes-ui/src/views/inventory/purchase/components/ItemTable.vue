<template>
  <div class="item-container">
    <!-- 搜索表单 -->
    <el-form inline class="search-form">
      <el-form-item label="物品名称">
        <el-input 
          v-model="searchForm.itemName" 
          placeholder="请输入物品名称" 
          clearable 
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="类别">
        <el-select 
          v-model="searchForm.category" 
          placeholder="请选择类别" 
          clearable 
          style="width: 150px"
        >
          <el-option label="全部" value="" />
          <el-option label="原料" value="原料" />
          <el-option label="零部件" value="零部件" />
          <el-option label="初级半成品" value="初级半成品" />
          <el-option label="二级半成品" value="二级半成品" />
          <el-option label="成品" value="成品" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch" class="search-btn">
          查询
        </el-button>
        <el-button @click="resetSearch" class="reset-btn">重置</el-button>
        <el-button type="success" @click="handleAdd" class="add-btn">
          新增物品
        </el-button>
        <el-button type="primary" @click="importExcel" class="import-btn">
           Excel导入
       </el-button>
        <el-dropdown @command="handleExportCommand" class="export-dropdown">
          <el-button type="warning" class="export-btn" :loading="exportLoading">
            导出Excel<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="all">导出全部数据</el-dropdown-item>
            <el-dropdown-item command="current">导出当前搜索结果</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      highlight-current-row
      :header-cell-style="{background:'var(--current-color)', color:'white'}"
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="60" align="center" />
      <el-table-column prop="itemName" label="物品名称" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column prop="category" label="类别" width="260" align="center">
        <template slot-scope="scope">
          <el-tag :type="getCategoryType(scope.row.category)" size="small">
            {{ scope.row.category || '--' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="specification" label="规格型号" min-width="160" align="center" show-overflow-tooltip />
      <el-table-column prop="unit" label="单位" width="120" align="center" />
      <el-table-column prop="brand" label="品牌" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" width="180" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" width="180" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="260" fixed="right" align="center">
        <template slot-scope="scope">
          <div style="text-align: center;">
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: center"
    />

    <!-- 新增/编辑对话框 -->
    <item-dialog 
      :visible.sync="dialogVisible" 
      :form-data="formData" 
      :is-edit="isEdit"
      @submit="handleSubmit"
    />

    <!-- 导入对话框 -->
    <el-dialog
      title="Excel导入物品信息"
      :visible.sync="importDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="import-content">
        <div class="import-tips">
          <h4>导入说明：</h4>
          <p>1. 请使用Excel格式文件(.xlsx或.xls)</p>
          <p>2. 第一行为标题行，数据从第二行开始</p>
          <p>3. Excel必须包含以下5列（按顺序）：</p>
          <p style="margin-left: 20px;">第1列: 物品名称* | 第2列: 规格型号 | 第3列: 分类* | 第4列: 品牌 | 第5列: 单位*</p>
          <p>4. 标记*为必填字段，不能为空</p>
          <p style="color: #e6a23c;">5. 建议先导出现有数据作为模板参考格式</p>
        </div>
        
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :multiple="false"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          accept=".xlsx,.xls"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传Excel文件，且不超过10MB</div>
        </el-upload>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelImport">取 消</el-button>
        <el-button type="primary" @click="confirmImport" :loading="importLoading">确定导入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getItemList, deleteItem, exportItemList, exportItemListByCondition, importItemList } from '@/api/jenasi/item'
import ItemDialog from './ItemDialog.vue'

export default {
  name: 'ItemTable',
  components: {
    ItemDialog
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      searchForm: {
        itemName: '',
        category: ''
      },
      dialogVisible: false,
      formData: {},
      isEdit: false,
      // 导入相关数据
      importDialogVisible: false,
      importLoading: false,
      fileList: [],
      selectedFile: null
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          itemName: this.searchForm.itemName || undefined,
          category: this.searchForm.category || undefined
        }
        
        const response = await getItemList(params)
        if (response.code === 0 || response.code === 200) {
          this.tableData = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadData()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        itemName: '',
        category: ''
      }
      this.currentPage = 1
      this.loadData()
    },

    // 新增
    handleAdd() {
      this.formData = {}
      this.isEdit = false
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.formData = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个物品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await deleteItem(row.id)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    // 表单提交
    async handleSubmit(formData) {
      this.dialogVisible = false
      this.loadData()
    },

    // 导出命令处理
    handleExportCommand(command) {
      if (command === 'all') {
        this.handleExport('all')
      } else if (command === 'current') {
        this.handleExport('current')
      }
    },

    // 导出Excel
    async handleExport(type = 'all') {
      // 检查是否有数据可导出
      if (type === 'current' && (!this.tableData || this.tableData.length === 0)) {
        this.$message.warning('当前搜索结果为空，无数据可导出')
        return
      }

      // 确认导出操作
      const confirmMessage = type === 'all' 
        ? '确定要导出全部物品数据吗？' 
        : '确定要导出当前搜索结果吗？'
      
      try {
        await this.$confirm(
          confirmMessage, 
          '导出确认', 
          {
            confirmButtonText: '确定导出',
            cancelButtonText: '取消',
            type: 'info',
            center: true
          }
        )
      } catch {
        return // 用户取消导出
      }

      this.exportLoading = true
      
      try {
        // 显示导出进度提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在生成Excel文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        let response
        if (type === 'all') {
          response = await exportItemList()
        } else {
          // 导出当前搜索条件的结果
          const params = {
            itemName: this.searchForm.itemName || undefined,
            category: this.searchForm.category || undefined
          }
          response = await exportItemListByCondition(params)
        }
        
        // 验证响应数据
        if (!response || response.size === 0) {
          throw new Error('导出数据为空')
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // 生成更详细的文件名
        const now = new Date()
        const dateStr = now.getFullYear() + 
          String(now.getMonth() + 1).padStart(2, '0') + 
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') + 
          String(now.getMinutes()).padStart(2, '0') + 
          String(now.getSeconds()).padStart(2, '0')
        
        const filePrefix = type === 'all' ? '物品列表全部导出' : '物品列表筛选导出'
        link.download = `${filePrefix}_${dateStr}.xlsx`
        
        // 设置下载属性并触发下载
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        
        // 清理资源
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 100)
        
        // 关闭加载提示
        loadingInstance.close()
        
        // 成功提示
        this.$message({
          type: 'success',
          message: `导出成功！文件已保存为：${filePrefix}_${dateStr}.xlsx`,
          duration: 4000
        })
        
      } catch (error) {
        console.error('导出失败:', error)
        
        // 详细的错误处理
        let errorMessage = '导出失败，请稍后重试'
        
        if (error.response) {
          // 服务器响应错误
          if (error.response.status === 404) {
            errorMessage = '导出接口不存在，请联系管理员'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请联系管理员'
          } else if (error.response.status === 403) {
            errorMessage = '没有导出权限，请联系管理员'
          }
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message) {
          // 其他错误
          errorMessage = error.message
        }
        
        this.$message({
          type: 'error',
          message: errorMessage,
          duration: 5000
        })
        
      } finally {
        this.exportLoading = false
      }
    },



    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadData()
    },

    // 获取类别颜色类型
    getCategoryType(category) {
      const typeMap = {
        '原材料': 'primary',
        '半成品': 'warning',
        '成品': 'success',
        '耗材': 'info'
      }
      return typeMap[category] || ''
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 导入Excel
    importExcel() {
      this.importDialogVisible = true
      this.fileList = []
      this.selectedFile = null
    },

    // 处理文件选择
    handleFileChange(file, fileList) {
      // 验证文件类型
      const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                      file.raw.type === 'application/vnd.ms-excel'
      
      if (!isExcel) {
        this.$message.error('请选择Excel文件(.xlsx或.xls格式)')
        return false
      }

      // 验证文件大小（10MB）
      const isLt10M = file.raw.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB')
        return false
      }

      this.selectedFile = file.raw
      this.fileList = [file]
    },

    // 确认导入
    async confirmImport() {
      if (!this.selectedFile) {
        this.$message.warning('请先选择要导入的Excel文件')
        return
      }

      // 确认导入操作
      try {
        await this.$confirm(
          '确定要导入此Excel文件吗？导入过程中请勿关闭页面。', 
          '导入确认', 
          {
            confirmButtonText: '确定导入',
            cancelButtonText: '取消',
            type: 'warning',
            center: true
          }
        )
      } catch {
        return // 用户取消导入
      }

      this.importLoading = true

      try {
        // 显示导入进度提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在导入数据，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        const response = await importItemList(this.selectedFile)
        
        // 关闭加载提示
        loadingInstance.close()

        // 处理导入结果 - 后端返回String类型
        let resultMessage = ''
        
        // 处理不同类型的响应
        if (typeof response === 'string') {
          // 如果直接返回字符串
          resultMessage = response
        } else if (response && response.data) {
          // 如果返回对象且有data字段
          resultMessage = typeof response.data === 'string' ? response.data : response.msg || '导入完成'
        } else if (response && response.msg) {
          // 如果返回对象且有msg字段
          resultMessage = response.msg
        } else {
          // 默认情况
          resultMessage = '导入完成'
        }

        // 判断是成功还是失败
        if (resultMessage.includes('导入成功') || resultMessage.includes('成功')) {
          // 成功情况
          this.$message({
            type: 'success',
            message: resultMessage,
            duration: 5000
          })
          
          // 关闭导入对话框
          this.importDialogVisible = false
          
          // 刷新列表数据
          this.loadData()
          
        } else if (resultMessage.includes('导入失败') || resultMessage.includes('失败')) {
          // 失败情况 - 显示详细错误信息
          this.$alert(resultMessage, '导入失败', {
            dangerouslyUseHTMLString: true,
            type: 'error',
            customClass: 'import-error-dialog'
          })
        } else {
          // 其他情况，当作成功处理
          this.$message({
            type: 'success',
            message: resultMessage || '导入完成',
            duration: 5000
          })
          
          // 关闭导入对话框
          this.importDialogVisible = false
          
          // 刷新列表数据
          this.loadData()
        }

      } catch (error) {
        console.error('导入操作异常:', error)
        
        // 详细的错误处理
        let errorMessage = '导入操作异常，请稍后重试'
        
        if (error.response) {
          // 服务器响应错误
          if (error.response.status === 404) {
            errorMessage = '导入接口不存在，请联系管理员'
          } else if (error.response.status === 413) {
            errorMessage = '文件过大，请选择较小的文件'
          } else if (error.response.data) {
            // 处理后端返回的字符串响应（可能是成功也可能是失败）
            if (typeof error.response.data === 'string') {
              const responseText = error.response.data
              
              // 如果包含成功信息，实际上是成功的
              if (responseText.includes('导入成功') || responseText.includes('成功')) {
                this.$message({
                  type: 'success',
                  message: responseText,
                  duration: 5000
                })
                
                // 关闭导入对话框并刷新数据
                this.importDialogVisible = false
                this.loadData()
                return // 结束函数执行
              } else {
                errorMessage = responseText
              }
            } else if (error.response.data.msg) {
              errorMessage = error.response.data.msg
            }
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请联系管理员'
          }
        } else if (error.request) {
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message) {
          errorMessage = error.message
        }
        
        // 显示错误信息
        if (errorMessage.includes('导入失败：') || errorMessage.includes('\n')) {
          this.$alert(errorMessage, '导入失败', {
            dangerouslyUseHTMLString: true,
            type: 'error',
            customClass: 'import-error-dialog'
          })
        } else {
          this.$message({
            type: 'error',
            message: errorMessage,
            duration: 5000
          })
        }
        
      } finally {
        this.importLoading = false
      }
    },

    // 取消导入
    cancelImport() {
      this.importDialogVisible = false
      this.fileList = []
      this.selectedFile = null
    }
  }
}
</script>

<style scoped>
.item-container {
  background: var(--base-main-bg);
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-form {
  background: var(--base-item-bg);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
}

.search-form :deep(.el-form-item__label) {
  color: var(--base-color-2);
  font-weight: 500;
}

.search-form :deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  transition: all 0.3s ease;
}

.search-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

.search-form :deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
}

.search-form :deep(.el-button--primary:hover) {
  background: var(--current-color);
  border-color: var(--current-color);
  opacity: 0.8;
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
  background: transparent !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: #303133 !important;
}

/* 表格样式适配 */
:deep(.el-table) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.el-table td) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
}

:deep(.el-table th) {
  background: var(--current-color) !important;
  color: white !important;
  border-color: var(--border-color-1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--base-item-bg);
}

:deep(.el-table__body tr:hover > td) {
  background: var(--base-color-8) !important;
}

/* 分页样式适配 */
:deep(.el-pagination) {
  color: var(--base-color-2);
}

:deep(.el-pagination .el-pager li) {
  background: var(--base-item-bg);
  color: var(--base-color-2);
  border: 1px solid var(--border-color-1);
}

:deep(.el-pagination .el-pager li:hover) {
  color: var(--current-color);
}

:deep(.el-pagination .el-pager li.active) {
  background: var(--current-color);
  color: white;
}

:deep(.el-pagination button) {
  background: var(--base-item-bg);
  color: var(--base-color-2);
  border: 1px solid var(--border-color-1);
}

:deep(.el-pagination button:hover) {
  color: var(--current-color);
}

/* 按钮样式优化 - 参考采购订单管理样式 */
.search-btn:hover {
  background: var(--current-color) !important;
  border-color: var(--current-color) !important;
  color: white !important;
}

.reset-btn:hover {
  background: var(--base-color-8) !important;
  border-color: var(--border-color-1) !important;
  color: var(--base-color-1) !important;
}

.add-btn:hover {
  background: #85ce61 !important;
  border-color: #85ce61 !important;
  color: white !important;
}

/* 导出按钮样式 */
.export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-btn:hover {
  background: #ebb563 !important;
  border-color: #ebb563 !important;
  color: white !important;
}

.export-btn:focus {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-btn:active {
  background: #cf9236 !important;
  border-color: #cf9236 !important;
  color: white !important;
}

/* 主题特定样式 */
.theme-light .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.theme-dark .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.theme-starry-sky .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

/* 全局覆盖确保导出按钮样式优先级 */
:deep(.el-button--warning.export-btn) {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:hover) {
  background: #ebb563 !important;
  border-color: #ebb563 !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:focus) {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:active) {
  background: #cf9236 !important;
  border-color: #cf9236 !important;
  color: white !important;
}

/* 主题适配 */
.theme-dark .item-container {
  background: var(--base-item-bg);
}

.theme-dark .search-form {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-table) {
  background: var(--base-item-bg);
}

.theme-dark :deep(.el-table td) {
  background: var(--base-item-bg);
}

.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  color: var(--theme-color) !important;
  background: transparent !important;
}

/* 深色主题 - 覆盖Element UI默认stripe样式 */
.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: var(--theme-color) !important;
}

.theme-dark :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--base-menu-background);
}

/* 导出下拉菜单样式 */
.export-dropdown {
  display: inline-block;
}

.export-dropdown :deep(.el-button--warning) {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-dropdown :deep(.el-button--warning:hover) {
  background: #ebb563 !important;
  border-color: #ebb563 !important;
  color: white !important;
}

.export-dropdown :deep(.el-dropdown-menu) {
  background: var(--base-item-bg);
  border: 1px solid var(--border-color-1);
  box-shadow: 0 2px 12px 0 var(--tag-shadow-color-1);
}

.export-dropdown :deep(.el-dropdown-menu__item) {
  color: var(--base-color-1);
  background: var(--base-item-bg);
}

.export-dropdown :deep(.el-dropdown-menu__item:hover) {
  background: var(--base-color-8);
  color: var(--current-color);
}

.export-dropdown :deep(.el-dropdown-menu__item:focus) {
  background: var(--base-color-8);
  color: var(--current-color);
}

/* 导入按钮样式 */
.import-btn {
  background: var(--current-color) !important;
  border-color: var(--current-color) !important;
  color: white !important;
}

.import-btn:hover {
  background: var(--current-color) !important;
  border-color: var(--current-color) !important;
  color: white !important;
  opacity: 0.8;
}

/* 导入对话框样式 */
:deep(.el-dialog__header) {
  background: var(--base-item-bg);
  color: var(--base-color-1);
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.el-dialog__footer) {
  background: var(--base-item-bg);
  border-top: 1px solid var(--border-color-1);
}

.import-content {
  background: var(--base-main-bg);
}

.import-tips {
  background: var(--base-item-bg);
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
}

.import-tips h4 {
  color: var(--base-color-1);
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
}

.import-tips p {
  color: var(--base-color-2);
  margin: 5px 0;
  font-size: 13px;
  line-height: 1.5;
}

/* 上传组件样式适配 */
:deep(.el-upload) {
  border: 2px dashed var(--border-color-1);
  background: var(--base-item-bg);
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-upload:hover) {
  border-color: var(--current-color);
  background: var(--base-color-8);
}

:deep(.el-upload-dragger) {
  background: var(--base-item-bg);
  border: none;
  color: var(--base-color-2);
}

:deep(.el-upload-dragger:hover) {
  background: var(--base-color-8);
}

:deep(.el-upload__text) {
  color: var(--base-color-1);
}

:deep(.el-upload__text em) {
  color: var(--current-color);
}

:deep(.el-upload__tip) {
  color: var(--base-color-2);
}

:deep(.el-icon-upload) {
  color: var(--base-color-2);
}

/* 文件列表样式 */
:deep(.el-upload-list__item) {
  background: var(--base-item-bg);
  border: 1px solid var(--border-color-1);
  color: var(--base-color-1);
}

:deep(.el-upload-list__item:hover) {
  background: var(--base-color-8);
}

/* 错误信息对话框样式 */
:deep(.import-error-dialog) {
  background: var(--base-main-bg);
}

:deep(.import-error-dialog .el-message-box__header) {
  background: var(--base-item-bg);
  color: var(--base-color-1);
}

:deep(.import-error-dialog .el-message-box__content) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.import-error-dialog .el-message-box__message) {
  color: var(--base-color-1);
  white-space: pre-line;
  text-align: left;
}

/* 主题特定样式 */
.theme-dark .import-tips {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-upload) {
  border-color: var(--border-color-2);
  background: var(--base-menu-background);
}

.theme-dark :deep(.el-upload-dragger) {
  background: var(--base-menu-background);
}

.theme-starry-sky .import-tips {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-starry-sky :deep(.el-upload) {
  border-color: var(--border-color-2);
  background: var(--base-menu-background);
}

.theme-starry-sky :deep(.el-upload-dragger) {
  background: var(--base-menu-background);
}
</style> 
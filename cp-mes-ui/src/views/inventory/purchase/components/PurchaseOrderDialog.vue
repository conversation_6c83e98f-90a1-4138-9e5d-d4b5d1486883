<template>
  <el-dialog
    :title="isEdit ? '编辑采购订单' : '新增采购订单'"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      style="padding-right: 30px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购单号" prop="purchaseNo">
            <el-input 
              v-model="form.purchaseNo" 
              placeholder="请输入采购单号"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请人" prop="applicant">
            <el-input 
              v-model="form.applicant" 
              placeholder="请输入申请人"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物品" prop="itemId">
            <el-select 
              v-model="form.itemId" 
              placeholder="请选择物品" 
              filterable
              style="width: 100%"
              @change="handleItemChange"
            >
              <el-option
                v-for="item in itemList"
                :key="item.id"
                :label="item.itemName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplierId">
            <el-select 
              v-model="form.supplierId" 
              placeholder="请选择供应商" 
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="supplier in supplierList"
                :key="supplier.id"
                :label="supplier.supplierName"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采购数量" prop="quantity">
            <el-input-number
              v-model="form.quantity"
              :min="1"
              :max="99999"
              style="width: 100%"
              @change="calculateSubtotal"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量单位" prop="unit">
            <el-input v-model="form.unit" placeholder="请输入计量单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单价" prop="price">
            <el-input-number
              v-model="form.price"
              :min="0"
              :precision="2"
              style="width: 100%"
              @change="calculateSubtotal"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="期望到货时间" prop="expectedDate">
            <el-date-picker
              v-model="form.expectedDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="小计金额">
            <el-input 
              :value="subtotal" 
              readonly 
              style="background-color: var(--base-menu-background);"
            >
              <template slot="prepend">¥</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更 新' : '新 增' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addOrUpdatePurchaseOrder } from '@/api/jenasi/purchaseOrder'
import { listAllItems } from '@/api/jenasi/item'
import { getSupplierList } from '@/api/jenasi/supplier'

export default {
  name: 'PurchaseOrderDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: null,
        purchaseNo: '',
        applicant: '',
        itemId: null,
        supplierId: null,
        quantity: 1,
        unit: '',
        price: 0,
        expectedDate: ''
      },
      itemList: [],
      supplierList: [],
      rules: {
        purchaseNo: [
          { required: true, message: '请输入采购单号', trigger: 'blur' }
        ],
        applicant: [
          { required: true, message: '请输入申请人', trigger: 'blur' }
        ],
        itemId: [
          { required: true, message: '请选择物品', trigger: 'change' }
        ],
        supplierId: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        expectedDate: [
          { required: true, message: '请选择期望到货时间', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    subtotal() {
      if (this.form.quantity && this.form.price) {
        return (this.form.quantity * this.form.price).toFixed(2)
      }
      return '0.00'
    }
  },
  watch: {
    async visible(val) {
      if (val) {
        // 先加载数据列表，再初始化表单
        await Promise.all([
          this.loadItemList(),
          this.loadSupplierList()
        ])
        this.initForm()
      }
    },
    formData: {
      async handler(val) {
        if (val && Object.keys(val).length > 0) {
          // 如果对话框已打开且数据列表未加载，先加载数据列表
          if (this.visible && (this.itemList.length === 0 || this.supplierList.length === 0)) {
            await Promise.all([
              this.loadItemList(),
              this.loadSupplierList()
            ])
          }
          this.form = { ...val }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.formData) {
        this.form = { ...this.formData }

        // 修复：验证物品ID是否在列表中存在，如果不存在则尝试添加到列表中
        if (this.form.itemId && !this.validateItemInList(this.form.itemId)) {
          console.warn('编辑时物品ID不在列表中，尝试添加到物品列表:', this.form.itemId, this.form.itemName)

          // 如果有物品名称，创建一个临时的物品对象添加到列表中
          if (this.form.itemName) {
            const tempItem = {
              id: this.form.itemId,
              itemName: this.form.itemName,
              unit: this.form.unit || ''
            }

            // 检查是否已存在，避免重复添加
            const existingItem = this.itemList.find(item => item.id === this.form.itemId)
            if (!existingItem) {
              this.itemList.unshift(tempItem) // 添加到列表开头
              console.log('已添加临时物品到列表:', tempItem)
            }
          }
        }
      } else {
        this.form = {
          id: null,
          purchaseNo: this.generatePurchaseNo(),
          applicant: this.$store.getters.name || '',
          itemId: null,
          supplierId: null,
          quantity: 1,
          unit: '',
          price: 0,
          expectedDate: ''
        }
      }
    },

    // 生成采购单号
    generatePurchaseNo() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0')
      return `PO${year}${month}${day}${time}`
    },

    // 加载物品列表
    async loadItemList() {
      try {
        const response = await listAllItems()
        console.log('物品列表响应:', response)
        if (response.code === 0 || response.code === 200) {
          this.itemList = response.data || []
          console.log('物品列表加载成功:', this.itemList.length)
          // 调试：打印前几个物品的结构
          if (this.itemList.length > 0) {
            console.log('物品数据结构示例:', this.itemList.slice(0, 3))
          }
        } else {
          console.error('获取物品列表失败:', response.msg)
          this.$message.error('获取物品列表失败')
        }
      } catch (error) {
        console.error('获取物品列表失败:', error)
        this.$message.error('获取物品列表失败')
      }
    },

    // 加载供应商列表
    async loadSupplierList() {
      try {
        const response = await getSupplierList({ pageNum: 1, pageSize: 1000 })
        console.log('供应商列表响应:', response)
        if (response.code === 0 || response.code === 200) {
          this.supplierList = response.data.records || response.data || []
          console.log('供应商列表加载成功:', this.supplierList.length)
        } else {
          console.error('获取供应商列表失败:', response.msg)
          this.$message.error('获取供应商列表失败')
        }
      } catch (error) {
        console.error('获取供应商列表失败:', error)
        this.$message.error('获取供应商列表失败')
      }
    },

    // 物品选择改变
    handleItemChange(itemId) {
      const selectedItem = this.itemList.find(item => item.id === itemId)
      if (selectedItem) {
        this.form.unit = selectedItem.unit || ''
      }
    },

    // 验证物品ID是否在列表中存在
    validateItemInList(itemId) {
      if (!itemId) return true
      const item = this.itemList.find(item => item.id === itemId)
      if (!item) {
        console.warn(`物品ID ${itemId} 在物品列表中不存在，可能需要重新加载物品列表`)
        return false
      }
      return true
    },

    // 计算小计
    calculateSubtotal() {
      // 计算在computed中处理，这里不需要额外逻辑
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true

        const formData = {
          purchaseNo: this.form.purchaseNo,
          applicant: this.form.applicant,
          itemId: Number(this.form.itemId),
          supplierId: Number(this.form.supplierId),
          quantity: Number(this.form.quantity),
          unit: this.form.unit,
          price: Number(this.form.price),
          expectedDate: this.form.expectedDate
        }

        // 如果是编辑模式，添加id
        if (this.isEdit && this.form.id) {
          formData.id = Number(this.form.id)
        }

        console.log('提交的表单数据:', formData)

        const response = await addOrUpdatePurchaseOrder(formData)
        console.log('提交响应:', response)
        
        if (response.code === 0 || response.code === 200) {
          this.$message.success(this.isEdit ? '更新成功' : '新增成功')
          this.$emit('submit', formData)
          this.handleClose()
        } else {
          this.$message.error(response.msg || '操作失败')
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时error为false
          console.error('提交失败:', error)
          this.$message.error('操作失败')
        }
      } finally {
        this.loading = false
      }
    },

    // 关闭对话框
    handleClose() {
      this.$refs.form.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
/* 对话框样式适配 */
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid var(--border-color-1);
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  padding: 24px;
}

:deep(.el-dialog__footer) {
  background: var(--base-main-bg);
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-color-1);
  border-radius: 0 0 12px 12px;
}

/* 表单样式适配 */
:deep(.el-form-item__label) {
  color: var(--base-color-2);
  font-weight: 500;
}

:deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

:deep(.el-input__inner:hover) {
  border-color: var(--current-color);
}

:deep(.el-input__inner[readonly]) {
  background: var(--base-color-8);
  color: var(--base-color-3);
}

:deep(.el-select .el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
}

:deep(.el-input-number .el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
}

:deep(.el-date-editor .el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
}

:deep(.el-textarea__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

/* 按钮样式适配 */
:deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: var(--theme-color);
  border-color: var(--theme-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(54, 113, 232, 0.3);
}

:deep(.el-button--primary.is-loading) {
  background: var(--current-color);
  border-color: var(--current-color);
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

/* 主题适配 */
.theme-dark :deep(.el-dialog) {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-dialog__body) {
  background: var(--base-item-bg);
}

.theme-dark :deep(.el-dialog__footer) {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-textarea__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}
</style> 
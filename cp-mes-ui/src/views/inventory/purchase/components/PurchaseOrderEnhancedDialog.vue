<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="85%"
    :before-close="handleBeforeClose"
    class="enhanced-dialog"
    :close-on-click-modal="false"
  >
    <!-- 订单信息头部 -->
    <div class="order-info-header" v-loading="loading.orderInfo">
      <h3>
        <i class="el-icon-document"></i>
        {{ orderInfo.purchaseNo || '加载中...' }}
      </h3>
      <div class="order-details">
        <span><strong>物品：</strong>{{ orderInfo.itemName || '加载中...' }}</span>
        <span><strong>供应商：</strong>{{ orderInfo.supplierName || '加载中...' }}</span>
        <span><strong>申请人：</strong>{{ orderInfo.applicant || '加载中...' }}</span>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" type="card" class="enhanced-tabs" @tab-click="handleTabClick">

      <!-- 图片管理标签页 -->
      <el-tab-pane label="图片管理" name="images" class="tab-pane">
        <div class="tab-content" v-loading="loading.images">
          <div class="tab-header">
            <h5><i class="el-icon-picture-outline"></i> 图片管理</h5>
            <p class="tab-description">上传和管理与此采购订单相关的图片文件</p>
          </div>
          <purchase-image-upload
            v-if="tabsLoaded.images"
            :purchase-order-id="purchaseOrderId"
            @upload-success="handleImageUploadSuccess"
            @error="handleError"
          />
        </div>
      </el-tab-pane>

      <!-- 链接管理标签页 -->
      <el-tab-pane label="链接管理" name="links" class="tab-pane">
        <div class="tab-content" v-loading="loading.links">
          <div class="tab-header">
            <h5><i class="el-icon-link"></i> 采购链接管理</h5>
            <p class="tab-description">管理与此采购订单相关的各种链接</p>
          </div>
          <purchase-links-manager
            v-if="tabsLoaded.links"
            :purchase-order-id="purchaseOrderId"
            @save-success="handleLinkSaveSuccess"
            @error="handleError"
          />
        </div>
      </el-tab-pane>

      <!-- 物流追踪标签页 -->
      <el-tab-pane label="物流追踪" name="logistics" class="tab-pane">
        <div class="tab-content">
          <div class="tab-header">
            <h5><i class="el-icon-truck"></i> 物流追踪管理</h5>
            <p class="tab-description">录入和追踪此采购订单的物流信息</p>
          </div>

          <!-- 物流查询区域 -->
          <div class="logistics-query-section">
            <el-card class="query-card" shadow="never">
              <div slot="header" class="card-header">
                <span><i class="el-icon-search"></i> 物流查询</span>
                <div class="header-actions">
                  <el-button
                    type="success"
                    size="mini"
                    icon="el-icon-plus"
                    @click="showAddTrackingDialog"
                  >
                    添加快递单号
                  </el-button>
                  <el-button
                    v-if="logisticsData.trackingNumber"
                    type="text"
                    @click="refreshLogistics"
                    :loading="loading.logistics"
                    class="refresh-btn"
                    title="刷新物流信息并清理缓存"
                  >
                    <i class="el-icon-refresh"></i> 刷新并清理缓存
                  </el-button>
                  <el-button
                    v-if="logisticsData.trackingNumber"
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    @click="handleDeleteTracking"
                  >
                    删除
                  </el-button>
                </div>
              </div>

              <!-- 快递单号选择器（如果有多个快递单号） -->
              <div v-if="trackingNumbersList.length > 1" class="tracking-selector">
                <el-row :gutter="16">
                  <el-col :span="24">
                    <div class="selector-label">已有快递单号：</div>
                    <el-radio-group v-model="selectedTrackingIndex" @change="selectTrackingNumber">
                      <el-radio-button
                        v-for="(tracking, index) in trackingNumbersList"
                        :key="index"
                        :label="index"
                        class="tracking-radio-button"
                      >
                        <span class="tracking-number">{{ tracking.trackingNumber }}</span>
                        <el-tag
                          :type="getStatusType(tracking.status)"
                          size="mini"
                          class="tracking-status"
                        >
                          {{ tracking.statusDescription || '未知' }}
                        </el-tag>
                      </el-radio-button>
                    </el-radio-group>
                  </el-col>
                </el-row>
              </div>

              <div class="query-form">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-input
                      v-model="logisticsData.trackingNumber"
                      placeholder="请输入快递单号"
                      clearable
                      @input="handleTrackingNumberInput"
                      @keyup.enter.native="debouncedQueryLogistics"
                    >
                      <template slot="prepend">快递单号</template>
                    </el-input>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="logisticsData.logisticsCompany"
                      placeholder="选择物流公司（可选）"
                      clearable
                      filterable
                      :loading="loading.companies"
                      @change="handleCompanyChange"
                      class="logistics-company-select"
                    >
                      <el-option
                        v-for="company in logisticsCompanyList"
                        :key="company.code"
                        :label="company.name"
                        :value="company.code"
                      >
                        <span style="float: left">{{ company.name }}</span>
                        <span v-if="company.phone" style="float: right; color: #8492a6; font-size: 13px">
                          {{ company.phone }}
                        </span>
                      </el-option>

                      <!-- 空状态提示 -->
                      <div v-if="!loading.companies && logisticsCompanyList.length === 0" class="empty-option">
                        <i class="el-icon-warning"></i>
                        <span>暂无物流公司数据</span>
                      </div>
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-button
                      type="primary"
                      @click="debouncedQueryLogistics"
                      :loading="loading.logistics"
                      :disabled="!logisticsData.trackingNumber"
                    >
                      查询
                    </el-button>
                  </el-col>
                </el-row>

                <!-- 物流公司加载状态提示 -->
                <div v-if="loading.companies" class="loading-tip">
                  <i class="el-icon-loading"></i>
                  <span>正在加载物流公司列表...</span>
                </div>

                <!-- 物流公司加载失败提示 -->
                <div v-if="companyLoadError" class="error-tip">
                  <i class="el-icon-warning"></i>
                  <span>物流公司列表加载失败，已使用默认列表</span>
                  <el-button type="text" @click="retryLoadCompanies" size="mini">
                    重试
                  </el-button>
                </div>

                <!-- 查询提示信息 -->
                <div class="query-tips">
                  <el-alert
                    title="查询提示"
                    type="info"
                    :closable="false"
                    show-icon
                  >
                    <template slot="default">
                      <p>• 快递单号为必填项，请确保输入正确的单号</p>
                      <p>• 物流公司为可选项，不选择时系统将尝试自动识别</p>
                      <p>• 如查询失败，建议手动选择正确的物流公司后重试</p>
                    </template>
                  </el-alert>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 物流状态展示区域 -->
          <div v-if="logisticsData.trackingNumber" class="logistics-status-section">
            <el-card class="status-card" shadow="never" v-loading="loading.logistics">
              <div slot="header" class="card-header">
                <span><i class="el-icon-info"></i> 物流状态</span>
                <div class="header-actions">
                  <el-tag
                    :type="getStatusTagType(logisticsData.status)"
                    size="small"
                  >
                    {{ getStatusDescription(logisticsData.status) }}
                  </el-tag>

                  <!-- 轨迹展开/收起按钮 -->
                  <el-button
                    v-if="logisticsData.trackingDetails && logisticsData.trackingDetails.length > 0"
                    type="text"
                    size="small"
                    @click="toggleTimelineExpanded"
                    class="timeline-toggle-btn"
                  >
                    <i :class="timelineExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    {{ timelineExpanded ? '收起轨迹' : '查看轨迹' }}
                    <span class="timeline-count">({{ logisticsData.trackingDetails.length }}条)</span>
                  </el-button>
                </div>
              </div>

              <div class="status-info">
                <el-row :gutter="16">
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">快递单号：</span>
                      <span class="value">{{ logisticsData.trackingNumber }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">物流公司：</span>
                      <span class="value">{{ getCompanyName(logisticsData.logisticsCompany) }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="label">最后更新：</span>
                      <span class="value">{{ formatTime(logisticsData.lastUpdateTime) }}</span>
                    </div>
                  </el-col>
                </el-row>

                <!-- 最新物流状态预览 -->
                <div v-if="logisticsData.trackingDetails && logisticsData.trackingDetails.length > 0" class="latest-status-preview">
                  <div class="latest-status-item">
                    <div class="status-icon">
                      <i :class="getTimelineIcon(logisticsData.trackingDetails[0].description)"></i>
                    </div>
                    <div class="status-content">
                      <div class="status-description">{{ logisticsData.trackingDetails[0].description }}</div>
                      <div class="status-time">{{ formatTime(logisticsData.trackingDetails[0].time, 'datetime') }}</div>
                      <div v-if="logisticsData.trackingDetails[0].location" class="status-location">
                        <i class="el-icon-location"></i>
                        {{ logisticsData.trackingDetails[0].location }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 折叠展开的物流轨迹时间线 -->
                <el-collapse-transition>
                  <div v-show="timelineExpanded" class="embedded-timeline-section">
                    <div class="timeline-divider"></div>
                    <div class="timeline-header">
                      <h6><i class="el-icon-time"></i> 完整物流轨迹</h6>
                    </div>

                    <el-timeline class="embedded-logistics-timeline">
                      <el-timeline-item
                        v-for="(detail, index) in logisticsData.trackingDetails"
                        :key="index"
                        :timestamp="formatTime(detail.time, 'datetime')"
                        :type="getTimelineType(index)"
                        :icon="getTimelineIcon(detail.description)"
                        placement="top"
                        :size="index === 0 ? 'large' : 'normal'"
                      >
                        <div class="timeline-content">
                          <div class="timeline-description" :class="{ 'latest': index === 0 }">
                            {{ detail.description }}
                          </div>
                          <div v-if="detail.location" class="timeline-location">
                            <i class="el-icon-location"></i>
                            {{ detail.location }}
                          </div>
                          <div v-if="detail.operator" class="timeline-operator">
                            <i class="el-icon-user"></i>
                            {{ detail.operator }}
                            <span v-if="detail.operatorPhone" class="operator-phone">
                              {{ detail.operatorPhone }}
                            </span>
                          </div>
                        </div>
                      </el-timeline-item>
                    </el-timeline>
                  </div>
                </el-collapse-transition>
              </div>
            </el-card>
          </div>

          <!-- 空状态提示 -->
          <div v-if="!logisticsData.trackingNumber && !loading.logistics" class="empty-state">
            <el-empty description="请输入快递单号查询物流信息">
              <template #image>
                <i class="el-icon-truck empty-icon"></i>
              </template>
              <el-button type="primary" @click="focusTrackingInput">
                开始查询
              </el-button>
            </el-empty>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 对话框底部 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleRefresh" :loading="loading.refresh" title="刷新数据并清理缓存">
        刷新数据并清理缓存
      </el-button>
    </span>

    <!-- 添加快递单号对话框 -->
    <el-dialog
      title="添加快递单号"
      :visible.sync="addTrackingDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body>
      <el-form
        ref="addTrackingForm"
        :model="addTrackingForm"
        :rules="addTrackingRules"
        label-width="100px">
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="addTrackingForm.trackingNumber"
            placeholder="请输入快递单号"
            clearable
            @input="handleAddTrackingNumberInput">
          </el-input>
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-select
            v-model="addTrackingForm.logisticsCompany"
            placeholder="请选择物流公司（可自动识别）"
            clearable
            filterable
            style="width: 100%">
            <el-option
              v-for="company in logisticsCompanyList"
              :key="company.code"
              :label="company.name"
              :value="company.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="addTrackingForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addTrackingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddTrackingSubmit" :loading="addTrackingLoading">
          确定
        </el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getPurchaseOrderById } from '@/api/jenasi/purchaseOrder'
import {
  queryLogisticsTracking,
  getLogisticsCompanyList,
  // recognizeLogisticsCompany, // 已移除 - 避免404错误
  addTrackingNumber,
  deleteTrackingNumberByNumber,
  clearLogisticsCache,
  getAllLogisticsTracking
} from '@/api/jenasi/purchaseOrderEnhanced'
import PurchaseImageUpload from '@/components/PurchaseImageUpload/index.vue'
import PurchaseLinksManager from '@/components/PurchaseLinksManager/index.vue'
import { optimizeTouchEvents, optimizeDialogTouch } from '@/utils/touch-performance'
import { debounce, validateTrackingNumber } from '@/utils/logistics-helper'

export default {
  name: 'PurchaseOrderEnhancedDialog',

  components: {
    PurchaseImageUpload,
    PurchaseLinksManager
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseOrderId: {
      type: [Number, String],
      default: null
    }
  },

  data() {
    return {
      dialogVisible: false,
      activeTab: 'links', // 默认显示链接管理标签页

      // 分离的loading状态管理
      loading: {
        orderInfo: false,
        images: false,
        links: false,
        logistics: false,
        companies: false,
        refresh: false
      },

      // 标签页懒加载状态
      tabsLoaded: {
        images: false,
        links: false,
        logistics: false
      },

      orderInfo: {
        purchaseNo: '',
        itemName: '',
        supplierName: '',
        applicant: ''
      },

      // 物流相关数据 - 支持多个快递单号
      logisticsData: {
        trackingNumber: '', // 保留单个快递单号输入（向后兼容）
        logisticsCompany: '',
        status: '',
        statusDescription: '',
        querySuccess: false,
        trackingDetails: [],
        lastUpdateTime: null
      },

      // 多个快递单号列表
      trackingNumbersList: [],

      // 当前选中的快递单号索引
      selectedTrackingIndex: 0,

      logisticsCompanyList: [],
      companyLoadError: false,
      queryHistory: [],

      // 数据持久化键名
      storageKeys: {
        logisticsData: 'purchase_logistics_data',
        queryHistory: 'purchase_query_history',
        activeTab: 'purchase_active_tab'
      },
      timelineExpanded: false,

      // 新增快递单号相关数据
      addTrackingDialogVisible: false,
      addTrackingLoading: false,
      addTrackingForm: {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      },
      addTrackingRules: {
        trackingNumber: [
          { required: true, message: '请输入快递单号', trigger: 'blur' },
          { min: 8, max: 30, message: '快递单号长度应在8-30位之间', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    dialogTitle() {
      return `采购订单增强功能 - ${this.orderInfo.purchaseNo || '加载中'}`
    }
  },

  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal
        if (newVal && this.purchaseOrderId) {
          this.initializeDialog()
        }
      },
      immediate: true
    },

    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
      if (!newVal) {
        this.saveDataToStorage()
      }
    },

    // 监听物流数据变化，自动保存
    logisticsData: {
      handler(newVal) {
        if (newVal.trackingNumber) {
          this.saveDataToStorage()
        }
      },
      deep: true
    }
  },

  created() {
    // 创建防抖方法
    this.debouncedQueryLogistics = debounce(() => {
      this.queryLogistics()
    }, 500)

    this.debouncedTrackingInput = debounce((value) => {
      this.handleTrackingNumberChange(value)
    }, 300)
  },

  mounted() {
    this.$nextTick(() => {
      optimizeTouchEvents()
      optimizeDialogTouch(this.$el)
    })
  },

  methods: {
    /**
     * 初始化对话框
     */
    async initializeDialog() {
      try {
        // 恢复持久化数据
        this.restoreDataFromStorage()

        // 并行加载基础数据
        await Promise.all([
          this.fetchOrderInfo(),
          this.loadLogisticsCompanyList()
        ])

        // 加载采购订单的所有快递单号
        await this.loadAllTrackingNumbers()

        // 初始化默认标签页（链接管理）
        await this.loadTabContent(this.activeTab)
        this.tabsLoaded[this.activeTab] = true

        // 如果有保存的物流数据，自动查询
        if (this.logisticsData.trackingNumber) {
          await this.queryLogistics()
        }
      } catch (error) {
        this.handleError(error, '初始化对话框')
      }
    },

    /**
     * 标签页点击处理（懒加载）
     */
    async handleTabClick(tab) {
      const tabName = tab.name
      this.activeTab = tabName

      // 保存当前标签页状态
      localStorage.setItem(this.storageKeys.activeTab, tabName)

      // 懒加载标签页内容
      if (!this.tabsLoaded[tabName]) {
        this.loading[tabName] = true

        try {
          await this.loadTabContent(tabName)
          this.tabsLoaded[tabName] = true
        } catch (error) {
          this.handleError(error, `加载${tabName}标签页`)
        } finally {
          this.loading[tabName] = false
        }
      }
    },

    /**
     * 加载标签页内容
     */
    async loadTabContent(tabName) {
      switch (tabName) {
        case 'images':
          // 图片标签页的特定初始化逻辑
          await this.$nextTick()
          break
        case 'links':
          // 链接标签页的特定初始化逻辑
          await this.$nextTick()
          break
        case 'logistics':
          // 物流标签页的特定初始化逻辑
          if (this.logisticsCompanyList.length === 0) {
            await this.loadLogisticsCompanyList()
          }
          break
      }
    },

    /**
     * 获取订单基本信息
     */
    async fetchOrderInfo() {
      if (!this.purchaseOrderId) return

      this.loading.orderInfo = true
      try {
        const response = await getPurchaseOrderById(this.purchaseOrderId)
        if (response && response.code === 0) {
          this.orderInfo = {
            purchaseNo: response.data.purchaseNo || 'N/A',
            itemName: response.data.itemName || '未知物品',
            supplierName: response.data.supplierName || '未知供应商',
            applicant: response.data.applicant || 'N/A'
          }

          // 如果订单中已有物流信息，自动填充
          if (response.data.trackingNumber) {
            this.logisticsData.trackingNumber = response.data.trackingNumber
            this.logisticsData.logisticsCompany = response.data.logisticsCompany || ''
          }
        }
      } catch (error) {
        this.handleError(error, '获取订单信息')
        // 设置默认信息
        this.orderInfo = {
          purchaseNo: `订单ID: ${this.purchaseOrderId}`,
          itemName: '获取失败',
          supplierName: '获取失败',
          applicant: '获取失败'
        }
      } finally {
        this.loading.orderInfo = false
      }
    },

    /**
     * 加载物流公司列表
     */
    async loadLogisticsCompanyList() {
      this.loading.companies = true
      this.companyLoadError = false

      try {
        console.log('开始加载物流公司列表...')
        const response = await getLogisticsCompanyList()
        console.log('物流公司列表API响应:', response)

        if (response && (response.code === 0 || response.code === 200)) {
          let companies = response.data || []

          if (Array.isArray(companies) && companies.length > 0) {
            // 标准化数据结构
            this.logisticsCompanyList = companies.map(company => ({
              code: company.code || company.id || company.name,
              name: company.name || company.label,
              phone: company.phone || company.tel || ''
            }))

            console.log('物流公司列表加载成功:', this.logisticsCompanyList)
          } else {
            throw new Error('API返回的物流公司数据为空')
          }
        } else {
          throw new Error(response?.msg || 'API响应异常')
        }
      } catch (error) {
        console.warn('从API获取物流公司列表失败:', error)
        this.companyLoadError = true

        // 使用默认的物流公司列表作为降级方案
        this.logisticsCompanyList = this.getDefaultLogisticsCompanies()
        console.log('使用默认物流公司列表:', this.logisticsCompanyList)

        this.$message.warning('物流公司列表加载失败，已使用默认列表')
      } finally {
        this.loading.companies = false
      }
    },

    /**
     * 获取默认物流公司列表
     */
    getDefaultLogisticsCompanies() {
      return [
        { code: 'SF', name: '顺丰速运', phone: '95338' },
        { code: 'STO', name: '申通快递', phone: '95543' },
        { code: 'YTO', name: '圆通速递', phone: '95554' },
        { code: 'ZTO', name: '中通快递', phone: '95311' },
        { code: 'YD', name: '韵达速递', phone: '95546' },
        { code: 'JD', name: '京东物流', phone: '950616' },
        { code: 'EMS', name: '中国邮政', phone: '11183' },
        { code: 'JTSD', name: '极兔速递', phone: '************' },
        { code: 'DBL', name: '德邦快递', phone: '95353' },
        { code: 'HTKY', name: '百世快递', phone: '95320' }
      ]
    },

    /**
     * 重试加载物流公司列表
     */
    async retryLoadCompanies() {
      await this.loadLogisticsCompanyList()
    },

    /**
     * 加载采购订单的所有快递单号
     */
    async loadAllTrackingNumbers() {
      if (!this.purchaseOrderId) {
        return
      }

      try {
        console.log('开始加载采购订单的所有快递单号:', this.purchaseOrderId)
        const response = await getAllLogisticsTracking(this.purchaseOrderId)

        if (response && (response.code === 0 || response.code === 200) && response.data) {
          this.trackingNumbersList = response.data
          console.log('快递单号列表加载成功:', this.trackingNumbersList.length, '个快递单号')

          // 如果有快递单号，默认选择第一个
          if (this.trackingNumbersList.length > 0) {
            this.selectedTrackingIndex = 0
            this.selectTrackingNumber(0)
          }
        } else {
          console.warn('快递单号列表响应异常:', response)
          this.trackingNumbersList = []
        }
      } catch (error) {
        console.error('加载快递单号列表失败:', error)
        this.trackingNumbersList = []
        // 不显示错误消息，因为没有快递单号是正常情况
      }
    },

    /**
     * 选择快递单号
     */
    selectTrackingNumber(index) {
      if (index >= 0 && index < this.trackingNumbersList.length) {
        this.selectedTrackingIndex = index
        const selectedTracking = this.trackingNumbersList[index]

        // 更新当前物流数据
        this.logisticsData = {
          ...selectedTracking,
          trackingNumber: selectedTracking.trackingNumber,
          logisticsCompany: selectedTracking.company || selectedTracking.logisticsCompany,
          status: selectedTracking.status,
          statusDescription: selectedTracking.statusDescription,
          querySuccess: selectedTracking.querySuccess,
          trackingDetails: selectedTracking.trackingDetails || [],
          lastUpdateTime: selectedTracking.lastUpdateTime
        }

        console.log('选择快递单号:', selectedTracking.trackingNumber)
      }
    },

    /**
     * 获取状态类型（用于标签颜色）
     */
    getStatusType(status) {
      switch (status) {
        case 'SIGNED':
          return 'success'
        case 'IN_TRANSIT':
          return 'primary'
        case 'PICKED_UP':
          return 'warning'
        case 'QUERY_FAILED':
          return 'danger'
        case 'NO_INFO':
          return 'info'
        default:
          return 'info'
      }
    },

    /**
     * 快递单号输入处理
     */
    handleTrackingNumberInput(value) {
      this.logisticsData.trackingNumber = value
      this.debouncedTrackingInput(value)
    },

    /**
     * 快递单号变化处理（防抖）
     */
    handleTrackingNumberChange(trackingNumber) {
      if (trackingNumber && trackingNumber.length >= 8) {
        // 物流公司识别功能已移除 - 避免404错误
        // this.recognizeLogisticsCompany(trackingNumber)
        console.log('快递单号输入:', trackingNumber, '请手动选择物流公司')
      }
    },

    /**
     * 物流公司选择变化处理
     */
    handleCompanyChange(companyCode) {
      console.log('物流公司选择变化:', companyCode)
      this.logisticsData.logisticsCompany = companyCode

      const selectedCompany = this.logisticsCompanyList.find(c => c.code === companyCode)
      if (selectedCompany) {
        console.log('选择的物流公司:', selectedCompany)
        this.$message.success(`已选择：${selectedCompany.name}`)
      }

      this.saveDataToStorage()
    },

    // 物流公司识别功能已移除 - 避免404错误
    // /**
    //  * 智能识别物流公司
    //  */
    // async recognizeLogisticsCompany(trackingNumber) {
    //   try {
    //     const response = await recognizeLogisticsCompany(trackingNumber)
    //     if (response && response.code === 0 && response.data) {
    //       const recognizedCode = response.data.code
    //       if (recognizedCode && !this.logisticsData.logisticsCompany) {
    //         this.logisticsData.logisticsCompany = recognizedCode
    //         this.$message.success(`已自动识别物流公司：${response.data.name}`)
    //       }
    //     }
    //   } catch (error) {
    //     console.warn('物流公司识别失败:', error)
    //   }
    // },

    /**
     * 查询物流信息（防抖版本）
     */
    async queryLogistics() {
      const { trackingNumber, logisticsCompany } = this.logisticsData

      if (!trackingNumber) {
        this.$message.warning('请输入快递单号')
        return
      }

      if (!validateTrackingNumber(trackingNumber)) {
        this.$message.error('快递单号格式不正确，请检查后重新输入')
        return
      }

      this.loading.logistics = true

      try {
        const response = await queryLogisticsTracking(trackingNumber, logisticsCompany)

        if (response && (response.code === 0 || response.code === 200)) {
          this.logisticsData = {
            ...this.logisticsData,
            ...response.data,
            lastUpdateTime: new Date()
          }

          // 保存查询历史
          this.saveQueryHistory(trackingNumber, logisticsCompany)

          // 根据查询结果显示相应提示
          if (this.logisticsData.querySuccess) {
            this.$message.success('加载成功')
          } else {
            this.$message.warning(this.logisticsData.errorMessage || '暂无物流信息，请稍后重试')
          }
        } else {
          throw new Error(response.msg || '查询失败')
        }
      } catch (error) {
        this.handleError(error, '物流查询')
      } finally {
        this.loading.logistics = false
      }
    },

    /**
     * 刷新物流信息（增强版：包含缓存清理）
     */
    async refreshLogistics() {
      if (!this.logisticsData.trackingNumber) {
        this.$message.warning('请先输入快递单号')
        return
      }

      const trackingNumber = this.logisticsData.trackingNumber

      // 显示详细的加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在刷新并清理缓存...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始刷新物流信息并清理缓存:', trackingNumber)

        // 1. 清理前端本地缓存
        this.clearLogisticsLocalCache(trackingNumber)

        // 2. 调用后端缓存清理API
        try {
          await this.clearBackendCache(trackingNumber)
        } catch (error) {
          console.warn('后端缓存清理失败:', error)
        }

        // 3. 执行原有的数据刷新操作
        await this.queryLogistics()

        loading.close()
        this.$message.success('物流信息已刷新，缓存已清理')
        console.log('物流信息刷新和缓存清理完成:', trackingNumber)

      } catch (error) {
        loading.close()
        this.handleError(error, '刷新物流信息')
      }
    },

    /**
     * 清理后端缓存
     */
    async clearBackendCache(trackingNumber) {
      try {
        // 调用后端缓存清理接口
        const response = await this.$http.delete(`/api/logistics/cache/${trackingNumber}`)
        console.log('后端缓存清理成功:', response)
      } catch (error) {
        console.warn('后端缓存清理接口调用失败:', error)
        throw error
      }
    },

    /**
     * 保存查询历史
     */
    saveQueryHistory(trackingNumber, logisticsCompany) {
      const historyItem = {
        trackingNumber,
        logisticsCompany,
        queryTime: new Date(),
        purchaseOrderId: this.purchaseOrderId
      }

      // 避免重复记录
      const existingIndex = this.queryHistory.findIndex(
        item => item.trackingNumber === trackingNumber
      )

      if (existingIndex >= 0) {
        this.queryHistory[existingIndex] = historyItem
      } else {
        this.queryHistory.unshift(historyItem)
      }

      // 限制历史记录数量
      if (this.queryHistory.length > 10) {
        this.queryHistory = this.queryHistory.slice(0, 10)
      }

      this.saveDataToStorage()
    },

    /**
     * 数据持久化 - 保存到本地存储
     */
    saveDataToStorage() {
      try {
        // 保存物流数据
        const logisticsDataToSave = {
          trackingNumber: this.logisticsData.trackingNumber,
          logisticsCompany: this.logisticsData.logisticsCompany,
          purchaseOrderId: this.purchaseOrderId
        }
        localStorage.setItem(this.storageKeys.logisticsData, JSON.stringify(logisticsDataToSave))

        // 保存查询历史
        localStorage.setItem(this.storageKeys.queryHistory, JSON.stringify(this.queryHistory))

        // 保存当前标签页
        localStorage.setItem(this.storageKeys.activeTab, this.activeTab)
      } catch (error) {
        console.warn('保存数据到本地存储失败:', error)
      }
    },

    /**
     * 数据持久化 - 从本地存储恢复
     */
    restoreDataFromStorage() {
      try {
        // 恢复物流数据
        const savedLogisticsData = localStorage.getItem(this.storageKeys.logisticsData)
        if (savedLogisticsData) {
          const parsedData = JSON.parse(savedLogisticsData)
          if (parsedData.purchaseOrderId === this.purchaseOrderId) {
            this.logisticsData.trackingNumber = parsedData.trackingNumber || ''
            this.logisticsData.logisticsCompany = parsedData.logisticsCompany || ''
          }
        }

        // 恢复查询历史
        const savedHistory = localStorage.getItem(this.storageKeys.queryHistory)
        if (savedHistory) {
          this.queryHistory = JSON.parse(savedHistory)
        }

        // 始终使用默认的链接管理标签页，不恢复之前保存的标签页状态
        // const savedActiveTab = localStorage.getItem(this.storageKeys.activeTab)
        // if (savedActiveTab) {
        //   this.activeTab = savedActiveTab
        // }
        this.activeTab = 'links' // 强制设置为链接管理标签页
      } catch (error) {
        console.warn('从本地存储恢复数据失败:', error)
      }
    },

    /**
     * 获取物流公司名称
     */
    getCompanyName(companyCode) {
      if (!companyCode) return '未知'
      const company = this.logisticsCompanyList.find(c => c.code === companyCode)
      return company ? company.name : companyCode
    },

    /**
     * 聚焦到快递单号输入框
     */
    focusTrackingInput() {
      this.$nextTick(() => {
        const input = this.$el.querySelector('input[placeholder*="快递单号"]')
        if (input) {
          input.focus()
        }
      })
    },

    /**
     * 统一错误处理
     */
    handleError(error, context = '') {
      console.error(`${context}错误:`, error)

      let message = '操作失败，请稍后重试'

      // 根据错误类型提供友好的提示
      if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
        message = '网络连接异常，请检查网络后重试'
      } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
        message = '请求超时，请稍后重试'
      } else if (error.code === 'AUTH_ERROR' || error.status === 401) {
        message = '登录已过期，请重新登录'
      } else if (error.code === 'PERMISSION_DENIED' || error.status === 403) {
        message = '权限不足，请联系管理员'
      } else if (error.message?.includes('快递单号')) {
        message = '快递单号格式不正确，请检查后重新输入'
      } else if (error.message?.includes('物流公司')) {
        message = '物流公司信息有误，请重新选择'
      } else if (error.message) {
        message = error.message
      }

      this.$message.error(message)

      // 记录错误日志
      this.logError(error, context)
    },

    /**
     * 错误日志记录
     */
    logError(error, context) {
      if (window.errorLogger) {
        window.errorLogger.log({
          component: 'PurchaseOrderEnhancedDialog',
          context,
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          purchaseOrderId: this.purchaseOrderId
        })
      }
    },

    /**
     * 处理关闭
     */
    handleClose() {
      this.saveDataToStorage()
      this.dialogVisible = false
      this.activeTab = 'links' // 重置为默认的链接管理标签页

      // 重置加载状态
      Object.keys(this.loading).forEach(key => {
        this.loading[key] = false
      })
    },

    /**
     * 处理关闭前
     */
    handleBeforeClose(done) {
      this.saveDataToStorage()
      done()
    },

    /**
     * 刷新数据
     */
    async handleRefresh() {
      this.loading.refresh = true
      try {
        await this.fetchOrderInfo()
        if (this.activeTab === 'logistics' && this.logisticsData.trackingNumber) {
          await this.refreshLogistics()
        }
        this.$message.success('数据已刷新')
        this.$emit('refresh')
      } catch (error) {
        this.handleError(error, '刷新数据')
      } finally {
        this.loading.refresh = false
      }
    },

    // 其他辅助方法保持不变
    handleImageUploadSuccess(data) {
      this.$message.success('图片上传成功')
      console.log('图片上传成功:', data)
    },

    handleLinkSaveSuccess(data) {
      this.$message.success('链接保存成功')
      console.log('链接保存成功:', data)
    },

    formatTime(time, format = 'full') {
      if (!time) return '--'

      try {
        const date = new Date(time)
        if (isNaN(date.getTime())) return '--'

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        switch (format) {
          case 'date':
            return `${year}-${month}-${day}`
          case 'time':
            return `${hours}:${minutes}`
          case 'datetime':
            return `${month}-${day} ${hours}:${minutes}`
          default:
            return `${year}-${month}-${day} ${hours}:${minutes}`
        }
      } catch (error) {
        console.error('formatTime: 时间格式化失败', error, time)
        return '--'
      }
    },

    getStatusTagType(status) {
      if (!status) return 'info'

      const normalizedStatus = status.toString().toLowerCase()
      const statusMap = {
        'signed': 'success',
        'delivered': 'success',
        'in_transit': 'primary',
        'dispatched': 'primary',
        'picked_up': 'warning',
        'exception': 'danger',
        'returned': 'danger',
        'query_failed': 'danger',
        'no_info': 'info'
      }

      return statusMap[normalizedStatus] || 'info'
    },

    getStatusDescription(status) {
      if (!status) return '未知状态'

      const statusDescMap = {
        'SIGNED': '已签收',
        'IN_TRANSIT': '运输中',
        'DISPATCHED': '已发货',
        'PICKED_UP': '已揽收',
        'DELIVERED': '已送达',
        'EXCEPTION': '异常',
        'RETURNED': '退回',
        'NO_INFO': '暂无信息',
        'QUERY_FAILED': '查询失败',
        'PENDING': '待处理',
        'PROCESSING': '处理中'
      }

      return statusDescMap[status] || status
    },

    getTimelineType(index) {
      return index === 0 ? 'primary' : ''
    },

    getTimelineIcon(description) {
      if (!description) return 'el-icon-info'

      const desc = description.toLowerCase()
      if (desc.includes('签收') || desc.includes('delivered')) return 'el-icon-check'
      if (desc.includes('派送') || desc.includes('delivery')) return 'el-icon-truck'
      if (desc.includes('转运') || desc.includes('transit')) return 'el-icon-refresh'
      if (desc.includes('揽收') || desc.includes('picked')) return 'el-icon-upload2'
      if (desc.includes('异常') || desc.includes('exception')) return 'el-icon-warning'

      return 'el-icon-info'
    },
    toggleTimelineExpanded() {
      this.timelineExpanded = !this.timelineExpanded
    },

    /**
     * 清理物流相关的本地缓存
     */
    clearLogisticsLocalCache(trackingNumber) {
      try {
        console.log('开始清理本地缓存:', trackingNumber)

        // 清理localStorage中的物流数据
        const storageKeys = [
          'cp-mes-logistics-data',
          'cp-mes-tracking-numbers',
          `logistics-${trackingNumber}`,
          `tracking-${trackingNumber}`,
          this.storageKeys.logisticsData,
          this.storageKeys.queryHistory,
          this.storageKeys.trackingNumbers
        ]

        storageKeys.forEach(key => {
          try {
            localStorage.removeItem(key)
            sessionStorage.removeItem(key)
            console.debug('清理缓存键:', key)
          } catch (error) {
            console.warn('清理缓存键失败:', key, error)
          }
        })

        // 清理组件内存中的数据
        this.logisticsData = {}
        this.trackingDetails = []
        this.queryHistory = []
        this.currentTrackingNumber = ''
        this.currentLogisticsCompany = ''

        // 清理表单中的快递单号数据
        if (this.form && this.form.trackingNumbers && Array.isArray(this.form.trackingNumbers)) {
          this.form.trackingNumbers = this.form.trackingNumbers.filter(item =>
            item.trackingNumber !== trackingNumber
          )
        }

        console.log('本地缓存清理完成:', trackingNumber)
      } catch (error) {
        console.error('清理本地缓存失败:', error)
        this.$message.warning('清理本地缓存失败')
      }
    },

    /**
     * 删除快递单号时的完整清理流程
     */
    async handleDeleteTrackingNumberWithCache(trackingNumber, trackingNumberId) {
      try {
        await this.$confirm('确认删除该快递单号吗？删除后将清理所有相关缓存数据。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        try {
          // 显示加载状态
          const loading = this.$loading({
            lock: true,
            text: '正在删除快递单号并清理缓存...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 1. 调用后端删除接口
          if (trackingNumberId) {
            await this.$api.trackingNumber.deleteTrackingNumber(trackingNumberId)
          }

          // 2. 清理后端缓存
          try {
            await this.$api.logistics.clearLogisticsCache(trackingNumber)
            console.log('后端缓存清理完成:', trackingNumber)
          } catch (error) {
            console.warn('后端缓存清理失败:', error)
          }

          // 3. 清理前端本地缓存
          this.clearLogisticsLocalCache(trackingNumber)

          // 4. 刷新页面数据
          if (typeof this.fetchTrackingNumbers === 'function') {
            await this.fetchTrackingNumbers()
          }

          loading.close()
          this.$message.success('删除成功，缓存已清理')

        } catch (error) {
          this.$loading().close()
          console.error('删除快递单号失败:', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        }
      } catch (error) {
        // 用户取消删除
        console.log('用户取消删除操作')
      }
    },

    /**
     * 显示添加快递单号对话框
     */
    showAddTrackingDialog() {
      this.addTrackingForm = {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      }
      this.addTrackingDialogVisible = true
    },

    /**
     * 处理添加快递单号输入
     */
    handleAddTrackingNumberInput(value) {
      if (!value || value.length < 8) return

      // 根据快递单号前缀自动识别物流公司
      if (value.startsWith('SF') || value.startsWith('sf')) {
        this.addTrackingForm.logisticsCompany = 'SF'
      } else if (value.startsWith('YT') || value.startsWith('yt')) {
        this.addTrackingForm.logisticsCompany = 'YTO'
      } else if (value.startsWith('ZT') || value.startsWith('zt')) {
        this.addTrackingForm.logisticsCompany = 'ZTO'
      } else if (value.startsWith('ST') || value.startsWith('st')) {
        this.addTrackingForm.logisticsCompany = 'STO'
      } else if (value.startsWith('YD') || value.startsWith('yd')) {
        this.addTrackingForm.logisticsCompany = 'YD'
      } else if (value.startsWith('JD') || value.startsWith('jd')) {
        this.addTrackingForm.logisticsCompany = 'JD'
      }
    },

    /**
     * 提交添加快递单号
     */
    async handleAddTrackingSubmit() {
      try {
        await this.$refs.addTrackingForm.validate()
      } catch (error) {
        return
      }

      // 添加必要字段验证
      if (!this.purchaseOrderId) {
        this.$message.error('采购订单ID不能为空')
        return
      }

      if (!this.addTrackingForm.trackingNumber?.trim()) {
        this.$message.error('快递单号不能为空')
        return
      }

      this.addTrackingLoading = true

      try {
        console.log('开始添加快递单号:', this.addTrackingForm)

        // 调用后端API保存快递单号 - 添加purchaseOrderNo字段
        const response = await addTrackingNumber({
          trackingNumber: this.addTrackingForm.trackingNumber,
          logisticsCompany: this.addTrackingForm.logisticsCompany,
          remark: this.addTrackingForm.remark,
          purchaseOrderId: this.purchaseOrderId,
          purchaseOrderNo: this.purchaseOrderNo // 添加采购订单号
        })

        if (response.code === 200 || response.code === 0) {
          this.$message.success('快递单号添加成功')

          // 清理前端本地缓存
          this.clearLogisticsLocalCache(this.addTrackingForm.trackingNumber)

          // 清理后端缓存（确保数据一致性）
          try {
            await clearLogisticsCache(this.addTrackingForm.trackingNumber)
          } catch (error) {
            console.warn('后端缓存清理失败:', error)
            // 不影响主流程
          }

          // 重新加载所有快递单号
          await this.loadAllTrackingNumbers()

          // 更新当前物流信息
          this.logisticsData = {
            ...this.logisticsData,
            trackingNumber: this.addTrackingForm.trackingNumber,
            logisticsCompany: this.addTrackingForm.logisticsCompany
          }

          // 立即查询物流信息
          await this.queryLogistics()

          // 关闭对话框
          this.addTrackingDialogVisible = false

          // 触发父组件刷新
          this.$emit('refresh')

          console.log('快递单号添加成功:', this.addTrackingForm.trackingNumber)
        } else {
          this.$message.error(response.msg || '添加失败')
        }
      } catch (error) {
        console.error('添加快递单号失败:', error)
        this.$message.error('添加失败: ' + (error.message || '未知错误'))
      } finally {
        this.addTrackingLoading = false
      }
    },

    /**
     * 删除快递单号
     */
    async handleDeleteTracking() {
      if (!this.logisticsData || !this.logisticsData.trackingNumber) {
        this.$message.warning('没有可删除的快递单号')
        return
      }

      try {
        await this.$confirm('确认删除该快递单号吗？删除后将清理所有相关缓存数据。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const trackingNumber = this.logisticsData.trackingNumber

        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在删除快递单号并清理缓存...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          // 调用后端删除API
          const response = await deleteTrackingNumberByNumber(trackingNumber)

          if (response.code === 200 || response.code === 0) {
            // 清理前端缓存
            this.clearLogisticsLocalCache(trackingNumber)

            // 清理后端缓存
            try {
              await clearLogisticsCache(trackingNumber)
            } catch (error) {
              console.warn('后端缓存清理失败:', error)
            }

            // 重新加载所有快递单号
            await this.loadAllTrackingNumbers()

            // 重置组件状态
            this.logisticsData = {
              trackingNumber: '',
              logisticsCompany: '',
              status: '',
              statusDescription: '',
              querySuccess: false,
              trackingDetails: [],
              lastUpdateTime: null
            }

            loading.close()
            this.$message.success('删除成功，缓存已清理')

            // 触发父组件刷新
            this.$emit('refresh')

            console.log('快递单号删除成功:', trackingNumber)
          } else {
            loading.close()
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          loading.close()
          console.error('删除快递单号失败:', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        }
      } catch (error) {
        console.log('用户取消删除操作')
      }
    },

    /**
     * 强制清理所有物流缓存
     */
    async forceClearAllLogisticsCache() {
      try {
        await this.$confirm('确认清理所有物流缓存数据吗？此操作不可恢复。', '确认清理', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const loading = this.$loading({
          lock: true,
          text: '正在清理所有缓存...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          // 清理所有localStorage中的物流相关数据
          const allKeys = Object.keys(localStorage)
          const logisticsKeys = allKeys.filter(key =>
            key.includes('logistics') ||
            key.includes('tracking') ||
            key.includes('cp-mes-logistics') ||
            key.includes('cp-mes-tracking')
          )

          logisticsKeys.forEach(key => {
            localStorage.removeItem(key)
            sessionStorage.removeItem(key)
          })

          // 清理组件数据
          this.logisticsData = {}
          this.trackingDetails = []
          this.queryHistory = []
          this.currentTrackingNumber = ''
          this.currentLogisticsCompany = ''

          // 调用后端清理所有缓存
          try {
            await this.$api.logistics.clearAllCache()
          } catch (error) {
            console.warn('后端缓存清理失败:', error)
          }

          loading.close()
          this.$message.success('所有缓存清理完成')

        } catch (error) {
          loading.close()
          console.error('清理缓存失败:', error)
          this.$message.error('清理缓存失败')
        }
      } catch (error) {
        console.log('用户取消清理操作')
      }
    }
  }
}
</script>

<style scoped>
/* 对话框基本样式 */
.enhanced-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.enhanced-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

.enhanced-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
}

.enhanced-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.enhanced-dialog :deep(.el-dialog__body) {
  padding: 0;
}

/* 订单信息头部 */
.order-info-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.order-info-header h3 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.order-info-header h3 i {
  margin-right: 8px;
  color: #3498db;
}

.order-details {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.order-details span {
  color: #6c757d;
  font-size: 14px;
}

.order-details strong {
  color: #495057;
}

/* 标签页样式 */
.enhanced-tabs {
  margin: 0;
}

.enhanced-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.enhanced-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 24px;
}

.enhanced-tabs :deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-size: 15px;
  font-weight: 500;
}

.enhanced-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
}

.enhanced-tabs :deep(.el-tabs__content) {
  padding: 0;
}

/* 标签页内容 */
.tab-pane {
  min-height: 500px;
}

.tab-content {
  padding: 24px;
}

.tab-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.tab-header h5 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.tab-header h5 i {
  margin-right: 8px;
  color: #409eff;
}

.tab-description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

/* 物流查询区域样式 */
.logistics-query-section {
  margin-bottom: 24px;
}

.query-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #409eff;
}

.card-header i {
  margin-right: 6px;
}

.refresh-btn {
  color: #409eff;
  padding: 0;
}

.refresh-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.query-form {
  padding: 10px 0;
}

.logistics-company-select {
  width: 100%;
}

.logistics-company-select :deep(.el-select-dropdown__item) {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}

.empty-option {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.empty-option i {
  margin-right: 5px;
}

/* 加载和错误提示样式 */
.loading-tip,
.error-tip {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.loading-tip {
  background-color: #f4f4f5;
  color: #606266;
}

.loading-tip i {
  margin-right: 6px;
  animation: rotating 2s linear infinite;
}

.error-tip {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.error-tip i {
  margin-right: 6px;
}

.error-tip .el-button {
  margin-left: 8px;
  padding: 0;
  color: #f56c6c;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.query-tips {
  margin-top: 16px;
}

.query-tips .el-alert {
  border-radius: 6px;
}

.query-tips p {
  margin: 4px 0;
  font-size: 13px;
}

/* 物流状态展示区域 */
.logistics-status-section {
  margin-bottom: 24px;
}

.status-info {
  padding: 16px 0;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  color: #6c757d;
  font-size: 14px;
}

.info-item .value {
  color: #2c3e50;
  font-weight: 500;
  margin-left: 4px;
}

/* 物流轨迹时间线 */
.logistics-timeline-section {
  margin-bottom: 24px;
}

.logistics-timeline {
  padding: 16px 0;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-description {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.timeline-location {
  color: #6c757d;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.timeline-location i {
  margin-right: 4px;
  color: #28a745;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

/* 对话框底部 */
.dialog-footer {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 12px;
}

/* 物流状态卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #409eff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.timeline-toggle-btn {
  color: #409eff;
  padding: 4px 8px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.timeline-toggle-btn:hover {
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

.timeline-count {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}

/* 最新物流状态预览 */
.latest-status-preview {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.latest-status-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.status-content {
  flex: 1;
}

.status-description {
  color: #2c3e50;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.status-time {
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 4px;
}

.status-location {
  color: #28a745;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.status-location i {
  margin-right: 4px;
}

/* 嵌入式时间线样式 */
.embedded-timeline-section {
  margin-top: 16px;
}

.timeline-divider {
  height: 1px;
  background: linear-gradient(to right, #e9ecef, #409eff, #e9ecef);
  margin: 16px 0;
}

.timeline-header {
  margin-bottom: 16px;
}

.timeline-header h6 {
  margin: 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.timeline-header h6 i {
  margin-right: 6px;
  color: #409eff;
}

.embedded-logistics-timeline {
  padding-left: 8px;
}

.embedded-logistics-timeline .timeline-content .latest {
  font-weight: 600;
  color: #409eff;
}

.timeline-operator {
  color: #6c757d;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.timeline-operator i {
  margin-right: 4px;
}

.operator-phone {
  margin-left: 8px;
  color: #28a745;
}

/* 多快递单号管理样式 */
.multi-tracking-tabs {
  margin-bottom: 16px;
}

.tracking-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
}

.tracking-tabs :deep(.el-tabs__item) {
  padding: 8px 16px;
  font-size: 13px;
}

.tracking-tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  max-width: 150px;
}

.tracking-tab-label i {
  font-size: 14px;
}

.tab-status-tag {
  margin-left: 4px;
  transform: scale(0.8);
}

.add-tracking-btn {
  color: #67c23a;
  font-size: 12px;
  padding: 4px 8px;
}

.add-tracking-btn:hover {
  background-color: #f0f9ff;
}

.mode-switch-btn,
.batch-refresh-btn {
  color: #409eff;
  font-size: 13px;
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.mode-switch-btn:hover,
.batch-refresh-btn:hover {
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

.multi-mode-tip {
  margin-top: 12px;
}

.multi-mode-tip .el-alert {
  border-radius: 6px;
}

.multi-mode-tip p {
  margin: 4px 0;
  font-size: 13px;
}

/* 折叠动画优化 */
.el-collapse-transition {
  transition: all 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 20px auto;
  }

  .order-details {
    flex-direction: column;
    gap: 8px;
  }

  .tab-content {
    padding: 16px;
  }

  .query-form .el-col {
    margin-bottom: 12px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: flex-end;
  }

  .latest-status-item {
    flex-direction: column;
    gap: 8px;
  }

  .tracking-tab-label {
    max-width: 100px;
    font-size: 12px;
  }

  .multi-tracking-tabs {
    overflow-x: auto;
  }
}

/* 快递单号选择器样式 */
.tracking-selector {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.selector-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.tracking-radio-button {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tracking-radio-button .el-radio-button__inner {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 200px;
}

.tracking-number {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #303133;
}

.tracking-status {
  margin-left: auto;
}

.tracking-radio-button.is-active .el-radio-button__inner {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.tracking-radio-button.is-active .tracking-number {
  color: white;
}
</style>

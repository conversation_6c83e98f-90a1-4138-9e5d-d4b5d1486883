<template>
  <div class="application-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <i class="el-icon-document-add"></i>
          采购申请
        </h2>
        <p class="page-description">填写采购申请信息，提交后等待审核</p>
      </div>
    </div>

    <!-- 申请表单 -->
    <div class="form-container">
      <!-- 表单进度指示 -->
      <!-- <div class="form-progress">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="基本信息" description="填写采购基本信息"></el-step>
          <el-step title="确认提交" description="检查信息并提交申请"></el-step>
        </el-steps>
      </div> -->

      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="application-form"
      >
        <el-card class="form-card" shadow="hover">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag v-if="form.id" type="warning" size="small">
              <i class="el-icon-edit"></i>
              编辑模式
            </el-tag>
          </div>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="申请单号" prop="purchaseNo">
                <el-input
                  v-model="form.purchaseNo"
                  placeholder="系统自动生成"
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请人">
                <el-input
                  v-model="currentUser"
                  placeholder="当前用户"
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 采购模式选择 -->
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="采购模式" prop="purchaseMode">
                <el-radio-group v-model="form.purchaseMode" @change="handlePurchaseModeChange">
                  <el-radio-button label="material">
                    <i class="el-icon-box"></i>
                    仓储原料采购
                  </el-radio-button>
                  <el-radio-button label="other">
                    <i class="el-icon-shopping-cart-2"></i>
                    其他类型采购
                  </el-radio-button>
                </el-radio-group>
                <div class="mode-description">
                  <span v-if="form.purchaseMode === 'material'" style="color: #67c23a;">
                    <i class="el-icon-info"></i>
                    从系统物料库中选择已登记的标准原材料，自动填充物料信息
                  </span>
                  <span v-else style="color: #e6a23c;">
                    <i class="el-icon-info"></i>
                    手动填写物品信息，适用于设备、耗材等非标准原料采购
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 仓储原料采购模式 -->
          <el-row v-if="form.purchaseMode === 'material'" :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择原料" prop="itemId">
                <el-select
                  v-model="form.itemId"
                  placeholder="请选择需要采购的原料"
                  filterable
                  style="width: 100%"
                  @change="handleRawMaterialChange"
                  :loading="rawMaterialLoading"
                >
                  <el-option
                    v-for="material in rawMaterialList"
                    :key="material.fields3"
                    :label="getMaterialDisplayLabel(material)"
                    :value="material.fields3"
                  >
                    <span style="float: left; max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {{ material.materialName }}
                    </span>
                    <span style="float: right; color: #8492a6; font-size: 13px; max-width: 35%;">
                      {{ getBoardTypeDisplay(material.boardType) }} | {{ material.unit || '件' }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料信息" v-if="form.purchaseMode === 'material' && form.itemId">
                <el-input
                  :value="getSelectedItemInfo()"
                  readonly
                  class="readonly-input material-info-input"
                  placeholder="选择原料后自动显示"
                >
                  <template slot="prepend">
                    <i class="el-icon-info"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 其他类型采购模式 -->
          <el-row v-if="form.purchaseMode === 'other'" :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择物品" prop="selectedItemId">
                <el-select
                  v-model="form.selectedItemId"
                  placeholder="请选择已登记的物品"
                  filterable
                  style="width: 100%"
                  @change="handleItemChange"
                  :loading="itemLoading"
                >
                  <el-option
                    v-for="item in itemList"
                    :key="item.id"
                    :label="item.itemName"
                    :value="item.id"
                  >
                    <span style="float: left">{{ item.itemName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.category }}</span>
                  </el-option>
                  <el-option :value="null" class="create-option">
                    <el-button
                      type="text"
                      @click="showCustomItemDialog = true"
                      style="width: 100%; color: var(--current-color); font-weight: 600;"
                    >
                      + 快速创建新物品
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物品信息" v-if="form.selectedItemId">
                <el-input
                  :value="getSelectedOtherItemInfo()"
                  readonly
                  class="readonly-input"
                  placeholder="选择物品后自动显示详细信息"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 供应商选择 -->
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="推荐供应商" prop="supplierId">
                <el-select
                  v-model="form.supplierId"
                  placeholder="请选择供应商"
                  filterable
                  style="width: 100%"
                  :loading="supplierLoading"
                  @change="updateStep"
                >
                  <el-option
                    v-for="supplier in supplierList"
                    :key="supplier.id"
                    :label="supplier.supplierName"
                    :value="supplier.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-form-item label="申请数量" prop="quantity">
                    <el-input-number
                      v-model="form.quantity"
                      :min="1"
                      :max="99999"
                      style="width: 100%"
                      @change="calculateSubtotal"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="计量单位" prop="unit">
                    <el-input
                      v-model="form.unit"
                      placeholder="从选择的物品中自动获取"
                      readonly
                      class="readonly-input"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预估单价" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  @change="calculateSubtotal"
                  placeholder="请输入预估单价"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="预估总金额">
                <el-input
                  :value="subtotal"
                  readonly
                  class="readonly-input amount-input"
                >
                  <template slot="prepend">¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="期望到货时间" prop="expectedDate">
                <el-date-picker
                  v-model="form.expectedDate"
                  type="date"
                  placeholder="请选择期望到货时间"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                  :picker-options="datePickerOptions"
                  @change="updateStep"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button size="large" @click="handleReset">
            <i class="el-icon-refresh-left"></i>
            重置表单
          </el-button>
          <el-button v-if="form.id || isEditingBatchItem" size="large" @click="handleCancelEdit">
            <i class="el-icon-close"></i>
            取消编辑
          </el-button>
          <el-button
            v-if="!form.id && !isEditingBatchItem"
            type="success"
            size="large"
            @click="handleAddToBatch"
            :disabled="currentStep < 1"
          >
            <i class="el-icon-plus"></i>
            添加到批量列表
          </el-button>
          <el-button
            v-if="isEditingBatchItem"
            type="warning"
            size="large"
            @click="handleUpdateBatchItem"
            :disabled="currentStep < 1"
          >
            <i class="el-icon-edit"></i>
            更新采购单
          </el-button>
          <el-button type="primary" size="large" @click="handleSubmit" :loading="loading" :disabled="currentStep < 1">
            <i class="el-icon-check"></i>
            {{ form.id ? '更新申请' : (isEditingBatchItem ? '直接提交' : '提交申请') }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 批量列表 -->
    <div v-if="batchList.length > 0" class="batch-list-container">
      <el-card class="batch-list-card" shadow="hover">
        <div slot="header" class="batch-header">
          <div class="batch-title">
            <i class="el-icon-document-copy"></i>
            批量采购列表
            <el-tag type="success" size="small" style="margin-left: 8px;">
              {{ batchList.length }} 项
            </el-tag>
          </div>
          <div class="batch-actions">
            <el-button
              type="primary"
              size="small"
              @click="handleBatchSubmit"
              :loading="batchSubmitting"
              :disabled="isEditingBatchItem"
            >
              <i class="el-icon-check"></i>
              批量提交 ({{ batchList.length }})
            </el-button>
            <el-button
              size="small"
              @click="handleClearBatch"
              :disabled="isEditingBatchItem"
            >
              <i class="el-icon-delete"></i>
              清空列表
            </el-button>
          </div>
        </div>

        <div class="batch-cards">
          <div
            v-for="(item, index) in batchList"
            :key="index"
            class="batch-card"
            :class="{ 'editing': editingIndex === index }"
          >
            <div class="card-content">
              <div class="card-header-info">
                <div class="item-name">
                  <i class="el-icon-box"></i>
                  {{ item.itemName }}
                </div>
                <div class="item-type">{{ item.itemType }}</div>
              </div>

              <div class="card-details">
                <div class="detail-row">
                  <span class="label">数量:</span>
                  <span class="value">{{ item.quantity }} {{ item.unit }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">单价:</span>
                  <span class="value price">¥{{ item.price }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">小计:</span>
                  <span class="value subtotal">¥{{ (item.quantity * item.price).toFixed(2) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">供应商:</span>
                  <span class="value">{{ getSupplierDisplayName(item) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">期望到货:</span>
                  <span class="value">{{ item.expectedDate }}</span>
                </div>
              </div>
            </div>

            <div class="card-actions">
              <el-button
                type="text"
                size="small"
                @click="handleEditBatchItem(index)"
                class="edit-btn"
                :disabled="isEditingBatchItem && editingIndex !== index"
              >
                <i class="el-icon-edit"></i>
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleRemoveFromBatch(index)"
                class="remove-btn"
                :disabled="isEditingBatchItem"
              >
                <i class="el-icon-delete"></i>
                移除
              </el-button>
            </div>
          </div>
        </div>

        <div class="batch-summary">
          <div class="summary-item">
            <span class="summary-label">总计项目:</span>
            <span class="summary-value">{{ batchList.length }} 项</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">预估总额:</span>
            <span class="summary-value total-amount">¥{{ batchTotalAmount }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速创建物品对话框 -->
    <el-dialog
      title="快速创建新物品"
      :visible.sync="showCustomItemDialog"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="customItemForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品名称" required>
              <el-input v-model="customItemForm.itemName" placeholder="请输入物品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位" required>
              <el-input v-model="customItemForm.unit" placeholder="请输入计量单位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品分类" required>
              <el-select v-model="customItemForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="原料" value="原料" />
                <el-option label="零部件" value="零部件" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号">
              <el-input v-model="customItemForm.specification" placeholder="请输入规格说明（可选）" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelCustomItem">取消</el-button>
        <el-button type="primary" @click="handleCreateCustomItem" :loading="customItemLoading">
          <i class="el-icon-check"></i>
          创建并选择
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addOrUpdatePurchaseOrder, generateNextPurchaseNo } from '@/api/jenasi/purchaseOrder'
import { listAllItems, addOrUpdateItem } from '@/api/jenasi/item'
import { listAllRawMaterials } from '@/api/jenasi/rawMaterialWarehouse'
import { listAllSuppliers } from '@/api/jenasi/supplier'

export default {
  name: 'BatchPurchaseForm',

  data() {
    return {
      loading: false,
      itemLoading: false,
      rawMaterialLoading: false,
      supplierLoading: false,
      batchSubmitting: false,
      currentUser: '', // 从store获取当前登录用户
      form: {
        id: null,
        purchaseNo: '',
        purchaseMode: 'material', // 默认为仓储原料采购模式
        itemId: null, // 仓储原料采购时存储fields3值
        selectedItemId: null, // 其他类型采购时选择的item表ID
        itemName: '', // 其他类型采购时的物品名称
        itemType: '', // 其他类型采购时的物品类型
        boardType: '', // 板型类型，仅仓储原料采购时使用
        specification: '', // 规格说明
        supplierId: null,
        quantity: 1,
        unit: '',
        price: 0,
        expectedDate: ''
      },
      itemList: [], // 其他类型采购使用的物品列表（item表数据）
      rawMaterialList: [], // 仓储原料采购使用的原料列表（raw_material_warehouse表数据）
      supplierList: [],
      currentStep: 0,
      batchList: [], // 批量列表
      showBatchList: false, // 是否显示批量列表
      editingIndex: -1, // 正在编辑的批量列表项索引，-1表示不在编辑状态
      originalFormData: null, // 编辑时保存的原始表单数据
      basePurchaseNo: '', // 基础采购单号（从后端获取）
      purchaseNoCounter: 1, // 采购单号计数器
      showCustomItemDialog: false,
      customItemLoading: false,
      customItemForm: {
        itemName: '',
        unit: '件',
        category: '原料',
        specification: '',
        brand: ''
      },
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      },
      rules: {
        purchaseMode: [
          { required: true, message: '请选择采购模式', trigger: 'change' }
        ],
        itemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'material' && !value) {
                callback(new Error('请选择需要采购的原料'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        selectedItemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !value && !this.form.itemName) {
                callback(new Error('请选择物品或手动填写物品信息'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        supplierId: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入申请数量', trigger: 'blur' },
          { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入预估单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '单价不能为负数', trigger: 'blur' }
        ],
        expectedDate: [
          { required: true, message: '请选择期望到货时间', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    // 计算小计
    subtotal() {
      const quantity = Number(this.form.quantity) || 0
      const price = Number(this.form.price) || 0
      return (quantity * price).toFixed(2)
    },

    // 计算批量列表总金额
    batchTotalAmount() {
      return this.batchList.reduce((total, item) => {
        return total + (item.quantity * item.price)
      }, 0).toFixed(2)
    },

    // 是否在编辑批量列表中的项目
    isEditingBatchItem() {
      return this.editingIndex >= 0
    }
  },

  async mounted() {
    await this.initData()
  },

  methods: {
    // 初始化数据
    async initData() {
      this.currentUser = this.$store.getters.name || '当前用户'
      await this.initFormPurchaseNo()
      await Promise.all([
        this.fetchRawMaterials(),
        this.fetchItems(),
        this.fetchSuppliers()
      ])
    },

    // 生成采购单号（新版本 - 调用后端API）
    async generatePurchaseNo() {
      try {
        const response = await generateNextPurchaseNo()
        if (response.code === 0 || response.code === 200) {
          console.log('生成新的采购订单号:', response.data)
          return response.data
        } else {
          throw new Error(response.msg || '生成订单号失败')
        }
      } catch (error) {
        console.error('生成采购单号失败:', error)
        throw error
      }
    },

    // 生成采购单号（降级方案 - 保留原有逻辑作为备用）
    generatePurchaseNoFallback() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      return `PO${year}${month}${day}${hours}${minutes}${seconds}`
    },

    // 初始化表单采购单号
    async initFormPurchaseNo() {
      try {
        // 如果还没有基础采购单号，从后端获取
        if (!this.basePurchaseNo) {
          const newPurchaseNo = await this.generatePurchaseNo()
          this.basePurchaseNo = newPurchaseNo
          this.purchaseNoCounter = 1
          this.form.purchaseNo = newPurchaseNo
          console.log('成功获取基础采购单号:', newPurchaseNo)
        } else {
          // 如果已有基础采购单号，使用前端自增逻辑
          const incrementedNo = this.generateIncrementedPurchaseNo()
          this.form.purchaseNo = incrementedNo
          console.log('使用前端自增生成采购单号:', incrementedNo)
        }
      } catch (error) {
        console.error('生成采购订单号失败:', error)
        this.$message.error('生成采购订单号失败，使用备用方案')
        // 降级到旧的生成方式
        const fallbackNo = this.generatePurchaseNoFallback()
        this.form.purchaseNo = fallbackNo
        this.basePurchaseNo = fallbackNo
        this.purchaseNoCounter = 1
        console.log('使用备用方案生成采购单号:', fallbackNo)
      }
    },

    // 生成自增的采购单号（前端逻辑）
    generateIncrementedPurchaseNo() {
      if (!this.basePurchaseNo) {
        console.error('基础采购单号未初始化')
        return this.generatePurchaseNoFallback()
      }

      // 解析基础采购单号，提取前缀和数字部分
      // 假设格式为 PO20231201001，我们需要提取 PO20231201 和 001
      const match = this.basePurchaseNo.match(/^(.+?)(\d+)$/)
      if (match) {
        const prefix = match[1] // PO20231201
        const baseNumber = parseInt(match[2]) // 001
        const newNumber = baseNumber + this.purchaseNoCounter
        const paddedNumber = String(newNumber).padStart(match[2].length, '0')
        this.purchaseNoCounter++
        return `${prefix}${paddedNumber}`
      } else {
        // 如果无法解析，使用简单的后缀自增
        this.purchaseNoCounter++
        return `${this.basePurchaseNo}_${String(this.purchaseNoCounter).padStart(3, '0')}`
      }
    },

    // 重置采购单号生成器（用于新的批量操作会话）
    resetPurchaseNoGenerator() {
      this.basePurchaseNo = ''
      this.purchaseNoCounter = 1
      console.log('采购单号生成器已重置')
    },

    // 获取原料列表
    async fetchRawMaterials() {
      try {
        this.rawMaterialLoading = true
        const response = await listAllRawMaterials()
        if (response.code === 0 || response.code === 200) {
          this.rawMaterialList = response.data || []
        }
      } catch (error) {
        console.error('获取原料列表失败:', error)
        this.rawMaterialList = []
      } finally {
        this.rawMaterialLoading = false
      }
    },

    // 获取物品列表
    async fetchItems() {
      try {
        this.itemLoading = true
        const response = await listAllItems()
        if (response.code === 0 || response.code === 200) {
          this.itemList = response.data || []
        }
      } catch (error) {
        console.error('获取物品列表失败:', error)
        this.itemList = []
      } finally {
        this.itemLoading = false
      }
    },

    // 获取供应商列表
    async fetchSuppliers() {
      try {
        this.supplierLoading = true
        const response = await listAllSuppliers()
        if (response.code === 0 || response.code === 200) {
          this.supplierList = response.data || []
        }
      } catch (error) {
        console.error('获取供应商列表失败:', error)
        this.supplierList = []
      } finally {
        this.supplierLoading = false
      }
    },

    // 采购模式变化处理
    handlePurchaseModeChange(mode) {
      // 清空相关字段
      this.form.itemId = null
      this.form.selectedItemId = null
      this.form.itemName = ''
      this.form.itemType = ''
      this.form.boardType = ''
      this.form.unit = ''
      this.form.price = 0
      this.updateStep()
    },

    // 原料选择变化处理
    handleRawMaterialChange(value) {
      if (value) {
        const selectedMaterial = this.rawMaterialList.find(m => m.fields3 === value)
        if (selectedMaterial) {
          this.form.itemName = selectedMaterial.materialName
          this.form.itemType = '原料'
          this.form.boardType = selectedMaterial.boardType || ''
          this.form.unit = selectedMaterial.unit || '件'
          this.form.specification = selectedMaterial.specification || ''
        }
      }
      this.updateStep()
    },

    // 物品选择变化处理
    handleItemChange(value) {
      if (value) {
        const selectedItem = this.itemList.find(item => item.id === value)
        if (selectedItem) {
          this.form.itemName = selectedItem.itemName
          this.form.itemType = selectedItem.category || '其他'
          this.form.unit = selectedItem.unit || '件'
          this.form.specification = selectedItem.specification || ''
        }
      } else {
        // 清空手动填写的信息
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.unit = ''
        this.form.specification = ''
      }
      this.updateStep()
    },

    // 计算小计
    calculateSubtotal() {
      this.updateStep()
    },

    // 更新步骤状态
    updateStep() {
      // 检查必填字段是否完成
      let step = 0

      if (this.form.purchaseMode === 'material') {
        if (this.form.itemId && this.form.supplierId && this.form.quantity > 0 && this.form.price > 0 && this.form.expectedDate) {
          step = 1
        }
      } else {
        if ((this.form.selectedItemId || this.form.itemName) && this.form.supplierId && this.form.quantity > 0 && this.form.price > 0 && this.form.expectedDate) {
          step = 1
        }
      }

      this.currentStep = step
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        this.loading = true
        const isEdit = !!this.form.id

        const formData = {
          purchaseNo: this.form.purchaseNo,
          applicant: this.currentUser,
          supplierId: Number(this.form.supplierId),
          quantity: Number(this.form.quantity),
          unit: this.form.unit,
          price: Number(this.form.price),
          expectedDate: this.form.expectedDate
        }

        // 根据采购模式设置不同的字段
        if (this.form.purchaseMode === 'material') {
          // 仓储原料采购模式：使用原料的fields3值作为itemId
          formData.itemId = this.form.itemId // 这里存储的是fields3值
          formData.itemName = this.form.itemName // 原料名称
          formData.itemType = this.form.itemType // 固定为"原料"
          formData.boardType = this.form.boardType // 板型类型
          // 后端会根据itemId有值识别为仓储原料采购模式
        } else {
          // 其他类型采购模式
          formData.itemId = null // 设置为null，后端识别为其他类型采购模式
          formData.boardType = null // 其他类型采购不设置板型
          if (this.form.selectedItemId) {
            // 选择了已有物品
            const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
            if (selectedItem) {
              formData.itemName = selectedItem.itemName
              formData.itemType = selectedItem.category || '其他'
            }
          } else {
            // 手动填写物品信息
            formData.itemName = this.form.itemName
            formData.itemType = this.form.itemType
          }
        }

        // 如果是编辑模式，添加id字段
        if (isEdit) {
          formData.id = this.form.id
        }

        // 直接提交
        const response = await addOrUpdatePurchaseOrder(formData)
        if (response.code === 0 || response.code === 200) {
          const successMessage = isEdit ? '采购申请更新成功！' : '采购申请提交成功！'
          this.$message.success(successMessage)
          this.$emit('submit-success', response.data)
          await this.handleReset()
        } else {
          throw new Error(response.message || '提交失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error(error.message || '提交失败，请重试')
      } finally {
        this.loading = false
      }
    },

    // 重置表单（完全重置，包括基础采购单号）
    async handleReset() {
      this.$refs.form.resetFields()
      this.initForm()
      this.form.id = null
      this.form.purchaseMode = 'material'
      this.form.selectedItemId = null
      this.form.itemName = ''
      this.form.itemType = ''
      this.form.boardType = ''
      this.form.specification = ''

      // 重置编辑状态
      this.editingIndex = -1
      this.originalFormData = null

      // 完全重置采购单号生成器
      this.resetPurchaseNoGenerator()

      this.$message.info('表单已重置')
      await this.initFormPurchaseNo()
    },

    // 初始化表单
    initForm() {
      this.form = {
        id: null,
        purchaseNo: '',
        purchaseMode: 'material',
        itemId: null,
        selectedItemId: null,
        itemName: '',
        itemType: '',
        boardType: '',
        specification: '',
        supplierId: null,
        quantity: 1,
        unit: '',
        price: 0,
        expectedDate: ''
      }
      this.currentStep = 0
    },

    // 取消编辑
    handleCancelEdit() {
      const confirmMessage = this.isEditingBatchItem
        ? '确认取消编辑批量列表项？未保存的修改将丢失。'
        : '确认取消编辑？未保存的修改将丢失。'

      this.$confirm(confirmMessage, '确认取消', {
        confirmButtonText: '确认取消',
        cancelButtonText: '继续编辑',
        type: 'error'
      }).then(async () => {
        if (this.isEditingBatchItem) {
          // 恢复原始表单数据
          this.form = { ...this.originalFormData }
          this.clearEditingState()
          this.updateStep()
          this.$message.info('已取消编辑批量列表项')
        } else {
          await this.handleReset()
          this.$message.info('已取消编辑')
        }
      }).catch(() => {
        // 用户选择继续编辑
      })
    },

    // 获取原料显示标签
    getMaterialDisplayLabel(material) {
      return `${material.materialName} - ${this.getBoardTypeDisplay(material.boardType)}`
    },

    // 获取板型显示文本
    getBoardTypeDisplay(boardType) {
      const boardTypeMap = {
        'A': 'A板',
        'B': 'B板',
        'C': 'C板',
        'D': 'D板'
      }
      return boardTypeMap[boardType] || boardType || '未知'
    },

    // 获取选中物料信息
    getSelectedItemInfo() {
      if (!this.form.itemId) return ''
      const selectedMaterial = this.rawMaterialList.find(m => m.fields3 === this.form.itemId)
      if (selectedMaterial) {
        return `${selectedMaterial.materialName} | ${this.getBoardTypeDisplay(selectedMaterial.boardType)} | ${selectedMaterial.unit || '件'}`
      }
      return ''
    },

    // 获取选中的其他物品信息
    getSelectedOtherItemInfo() {
      if (!this.form.selectedItemId) return ''
      const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
      if (selectedItem) {
        return `${selectedItem.itemName} | ${selectedItem.category || '其他'} | ${selectedItem.unit || '件'}`
      }
      return ''
    },

    // 快速创建物品相关方法
    handleCancelCustomItem() {
      this.showCustomItemDialog = false
      this.customItemForm = {
        itemName: '',
        unit: '件',
        category: '原料',
        specification: '',
        brand: ''
      }
    },

    async handleCreateCustomItem() {
      if (!this.customItemForm.itemName || !this.customItemForm.unit || !this.customItemForm.category) {
        this.$message.error('请填写完整的物品信息')
        return
      }

      try {
        this.customItemLoading = true
        const response = await addOrUpdateItem(this.customItemForm)

        if (response.code === 0 || response.code === 200) {
          const newItem = response.data

          // 添加到物品列表
          this.itemList.unshift(newItem)

          // 自动选择新创建的物品
          this.form.selectedItemId = newItem.id
          this.handleItemChange(newItem.id)

          // 关闭对话框并重置表单
          this.showCustomItemDialog = false
          this.handleCancelCustomItem()

          this.$message.success(`物品"${newItem.itemName}"创建成功并已自动选择`)
          this.updateStep()
        } else {
          throw new Error(response.msg || '创建物品失败')
        }
      } catch (error) {
        console.error('创建自定义物品失败:', error)
        this.$message.error('创建物品失败: ' + (error.message || error))
      } finally {
        this.customItemLoading = false
      }
    },

    // 添加到批量列表
    async handleAddToBatch() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        const formData = this.getFormData()
        this.batchList.push({ ...formData })

        this.$message.success('已添加到批量列表')

        // 重置表单但保持基础采购单号，使用自增逻辑生成新的采购单号
        await this.handleResetForBatch()
      } catch (error) {
        console.error('添加到批量列表失败:', error)
        this.$message.error('添加失败，请检查表单信息')
      }
    },

    // 专门用于批量添加后的表单重置（保持基础采购单号）
    async handleResetForBatch() {
      this.$refs.form.resetFields()
      this.initForm()
      this.form.id = null
      this.form.purchaseMode = 'material'
      this.form.selectedItemId = null
      this.form.itemName = ''
      this.form.itemType = ''
      this.form.boardType = ''
      this.form.specification = ''

      // 重置编辑状态
      this.editingIndex = -1
      this.originalFormData = null

      // 使用自增逻辑生成新的采购单号（不重置基础采购单号）
      const incrementedNo = this.generateIncrementedPurchaseNo()
      this.form.purchaseNo = incrementedNo
      console.log('批量添加后生成自增采购单号:', incrementedNo)
    },

    // 编辑批量列表中的项目
    handleEditBatchItem(index) {
      if (this.isEditingBatchItem) {
        this.$message.warning('请先完成当前编辑或取消编辑')
        return
      }

      const item = this.batchList[index]

      // 保存原始表单数据
      this.originalFormData = { ...this.form }

      // 设置编辑状态
      this.editingIndex = index

      // 将批量列表项的数据填充到表单中
      this.form = {
        id: null, // 批量列表项不是数据库中的记录，所以id为null
        purchaseNo: item.purchaseNo,
        purchaseMode: item.itemId ? 'material' : 'other',
        itemId: item.itemId,
        selectedItemId: null, // 编辑时暂不支持回填已选物品
        itemName: item.itemName || '',
        itemType: item.itemType || '',
        boardType: item.boardType || '',
        specification: item.specification || '',
        supplierId: item.supplierId,
        supplierName: item.supplierName || '', // 保留供应商名称
        quantity: item.quantity,
        unit: item.unit || '',
        price: item.price,
        expectedDate: item.expectedDate
      }

      this.updateStep()
      this.$message.info('已进入编辑模式，修改完成后点击"更新采购单"')
    },

    // 更新批量列表中的项目
    async handleUpdateBatchItem() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        const formData = this.getFormData()

        // 更新批量列表中对应的项目
        this.$set(this.batchList, this.editingIndex, { ...formData })

        this.$message.success('采购单已更新')

        // 直接清除编辑状态，不显示确认对话框
        this.clearEditingState()

        // 重置表单并生成新的自增采购单号（保持基础采购单号）
        await this.handleResetForBatch()
      } catch (error) {
        console.error('更新批量列表项失败:', error)
        this.$message.error('更新失败，请检查表单信息')
      }
    },

    // 清除编辑状态（不显示确认对话框）
    clearEditingState() {
      this.editingIndex = -1
      this.originalFormData = null
    },

    // 从批量列表中移除
    handleRemoveFromBatch(index) {
      if (this.isEditingBatchItem) {
        this.$message.warning('编辑模式下无法删除，请先取消编辑')
        return
      }

      this.$confirm('确定要从批量列表中移除这个采购单吗？', '确认移除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchList.splice(index, 1)
        this.$message.success('已从批量列表中移除')
      }).catch(() => {})
    },

    // 清空批量列表
    handleClearBatch() {
      if (this.isEditingBatchItem) {
        this.$message.warning('编辑模式下无法清空列表，请先取消编辑')
        return
      }

      this.$confirm('确定要清空所有批量采购申请吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.batchList = []
        // 重置采购单号生成器并获取新的基础采购单号
        this.resetPurchaseNoGenerator()
        await this.initFormPurchaseNo()
        this.$message.success('批量列表已清空')
        console.log('清空批量列表，重置采购单号生成器')
      }).catch(() => {})
    },

    // 批量提交
    async handleBatchSubmit() {
      if (this.batchList.length === 0) {
        this.$message.warning('批量列表为空，请先添加采购申请')
        return
      }

      this.batchSubmitting = true
      const successList = []
      const failList = []

      try {
        // 逐个提交采购申请
        for (let i = 0; i < this.batchList.length; i++) {
          const item = this.batchList[i]
          try {
            const response = await addOrUpdatePurchaseOrder(item)
            if (response.code === 0 || response.code === 200) {
              successList.push(item)
            } else {
              failList.push({ item, error: response.message })
            }
          } catch (error) {
            failList.push({ item, error: error.message })
          }
        }

        // 显示结果
        if (successList.length > 0) {
          this.$message.success(`成功提交 ${successList.length} 个采购申请`)
          this.$emit('submit-success', successList)
        }

        if (failList.length > 0) {
          this.$message.error(`${failList.length} 个采购申请提交失败`)
          console.error('批量提交失败的项目:', failList)
        }

        // 清空成功提交的项目
        if (successList.length > 0) {
          this.batchList = this.batchList.filter(item =>
            !successList.some(success => success.purchaseNo === item.purchaseNo)
          )

          // 如果批量列表已清空，重置采购单号生成器
          if (this.batchList.length === 0) {
            this.resetPurchaseNoGenerator()
            await this.initFormPurchaseNo()
            console.log('批量提交完成，重置采购单号生成器')
          }
        }

      } catch (error) {
        console.error('批量提交过程中发生错误:', error)
        this.$message.error('批量提交过程中发生错误')
      } finally {
        this.batchSubmitting = false
      }
    },

    // 获取表单数据（提取公共逻辑）
    getFormData() {
      const formData = {
        purchaseNo: this.form.purchaseNo,
        applicant: this.currentUser,
        supplierId: Number(this.form.supplierId),
        supplierName: this.getSupplierName(this.form.supplierId), // 添加供应商名称
        quantity: Number(this.form.quantity),
        unit: this.form.unit,
        price: Number(this.form.price),
        expectedDate: this.form.expectedDate
      }

      // 根据采购模式设置不同的字段
      if (this.form.purchaseMode === 'material') {
        // 仓储原料采购模式：使用原料的fields3值作为itemId
        formData.itemId = this.form.itemId // 这里存储的是fields3值
        formData.itemName = this.form.itemName // 原料名称
        formData.itemType = this.form.itemType // 固定为"原料"
        formData.boardType = this.form.boardType // 板型类型
        // 后端会根据itemId有值识别为仓储原料采购模式
      } else {
        // 其他类型采购模式
        formData.itemId = null // 设置为null，后端识别为其他类型采购模式
        formData.boardType = null // 其他类型采购不设置板型
        if (this.form.selectedItemId) {
          // 选择了已有物品
          const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
          if (selectedItem) {
            formData.itemName = selectedItem.itemName
            formData.itemType = selectedItem.category || '其他'
          }
        } else {
          // 手动填写物品信息
          formData.itemName = this.form.itemName
          formData.itemType = this.form.itemType
        }
      }

      // 如果是编辑模式，添加id字段
      if (this.form.id) {
        formData.id = this.form.id
      }

      return formData
    },

    // 获取供应商名称
    getSupplierName(supplierId) {
      const supplier = this.supplierList.find(s => s.id === supplierId)
      return supplier ? supplier.supplierName : '未知供应商'
    },

    // 获取供应商显示名称（优先使用已有的supplierName，否则通过ID查找）
    getSupplierDisplayName(item) {
      // 如果item中已经有supplierName，直接使用
      if (item.supplierName) {
        return item.supplierName
      }
      // 否则通过supplierId查找
      if (item.supplierId) {
        return this.getSupplierName(item.supplierId)
      }
      return '未知供应商'
    }
  }
}
</script>

<style scoped>
.application-container {
  background: var(--base-body-background);
  min-height: calc(100vh - 40px);
  padding: 20px;
}

/* 页面标题 */
.page-header {
  background: var(--base-main-bg);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--current-color);
}

.header-content {
  text-align: center;
}

.page-title {
  color: var(--base-color-1);
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-title i {
  color: var(--current-color);
}

.page-description {
  color: var(--base-color-2);
  font-size: 16px;
  margin: 0;
}

/* 表单容器 */
.form-container {
  margin-bottom: 30px;
}

/* 表单进度指示器 */
.form-progress {
  background: var(--base-main-bg);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
}

.form-progress :deep(.el-steps) {
  margin: 0;
}

.form-progress :deep(.el-step__title) {
  color: var(--base-color-1);
  font-weight: 600;
}

.form-progress :deep(.el-step__description) {
  color: var(--base-color-2);
}

/* 表单卡片 */
.form-card {
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--base-color-1);
}

/* 表单样式 */
.application-form :deep(.el-form-item__label) {
  color: var(--base-color-1);
  font-weight: 500;
}

.application-form :deep(.el-input__inner) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  transition: all 0.3s ease;
}

.application-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

.readonly-input :deep(.el-input__inner) {
  background: var(--base-color-8);
  color: var(--base-color-2);
}

.amount-input :deep(.el-input__inner) {
  font-weight: bold;
  color: var(--current-color);
}

/* 模式描述 */
.mode-description {
  margin-top: 8px;
  font-size: 14px;
  line-height: 1.5;
}

/* 操作按钮 */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding: 30px;
  background: var(--base-main-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color-1);
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.form-actions .el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
  box-shadow: 0 4px 12px rgba(54, 113, 232, 0.3);
}

.form-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(54, 113, 232, 0.4);
}

/* 创建选项样式 */
.create-option {
  border-top: 1px solid #e4e7ed;
  margin-top: 8px;
  padding-top: 8px;
}

/* 批量列表容器 */
.batch-list-container {
  margin-top: 30px;
}

.batch-list-card {
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  overflow: hidden;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.batch-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--base-color-1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-title i {
  color: var(--current-color);
}

.batch-actions {
  display: flex;
  gap: 8px;
}

/* 批量卡片 */
.batch-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 0;
}

.batch-card {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.batch-card:hover {
  border-color: var(--current-color);
  box-shadow: 0 4px 12px rgba(54, 113, 232, 0.15);
}

.batch-card.editing {
  border-color: #e6a23c;
  background: rgba(230, 162, 60, 0.05);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
}

.batch-card.editing::before {
  content: '编辑中';
  position: absolute;
  top: -1px;
  left: -1px;
  background: #e6a23c;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px 0 8px 0;
  font-weight: 600;
}

.card-content {
  margin-bottom: 12px;
}

.card-header-info {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-1);
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.item-name i {
  color: var(--current-color);
}

.item-type {
  font-size: 12px;
  color: var(--base-color-2);
  background: var(--base-color-8);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.card-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.detail-row .label {
  color: var(--base-color-2);
  font-weight: 500;
}

.detail-row .value {
  color: var(--base-color-1);
  font-weight: 600;
}

.detail-row .value.price {
  color: #e6a23c;
}

.detail-row .value.subtotal {
  color: #67c23a;
  font-weight: 700;
}

.card-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 4px;
}

.edit-btn {
  color: #e6a23c;
  font-weight: 600;
  padding: 4px 8px;
}

.edit-btn:hover {
  background: rgba(230, 162, 60, 0.1);
  border-radius: 4px;
}

.edit-btn:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.remove-btn {
  color: #f56c6c;
  font-weight: 600;
  padding: 4px 8px;
}

.remove-btn:hover {
  background: rgba(245, 108, 108, 0.1);
  border-radius: 4px;
}

.remove-btn:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 批量汇总 */
.batch-summary {
  margin-top: 20px;
  padding: 16px;
  background: var(--base-color-8);
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  color: var(--base-color-2);
  font-weight: 500;
}

.summary-value {
  color: var(--base-color-1);
  font-weight: 600;
}

.summary-value.total-amount {
  color: var(--current-color);
  font-size: 18px;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .batch-cards {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
  }

  .batch-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .batch-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .application-container {
    padding: 15px;
  }

  .page-header {
    padding: 20px 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .form-progress {
    padding: 20px 15px;
  }

  .form-actions {
    padding: 20px 15px;
  }

  .form-actions .el-button {
    margin: 4px;
    min-width: 100px;
  }

  /* 批量列表响应式 */
  .batch-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .batch-card {
    padding: 12px;
  }

  .card-details {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .batch-summary {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .batch-actions .el-button {
    font-size: 14px;
    padding: 8px 16px;
  }
}
</style>

<template>
  <el-dialog
    :title="auditStatus === 1 ? '审核通过' : '审核驳回'"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
  >
    <div class="audit-info">
      <el-alert
        :title="auditStatus === 1 ? '确认通过此采购订单？' : '确认驳回此采购订单？'"
        :type="auditStatus === 1 ? 'success' : 'warning'"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <div class="order-summary">
        <h4>订单信息</h4>
        <el-descriptions :column="1" border size="mini">
          <el-descriptions-item label="采购单号">
            {{ purchaseOrder.purchaseNo }}
          </el-descriptions-item>
          <el-descriptions-item label="物品名称">
            {{ purchaseOrder.itemName }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ purchaseOrder.supplierName }}
          </el-descriptions-item>
          <el-descriptions-item label="采购数量">
            {{ purchaseOrder.quantity }} {{ purchaseOrder.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="总金额">
            <span style="color: #e6a23c; font-weight: bold;">
              ¥{{ purchaseOrder.subtotal }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ purchaseOrder.applicant }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="审核意见" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="4"
          :placeholder="auditStatus === 1 ? '请输入审核通过的意见（选填）' : '请输入驳回原因'"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        :type="auditStatus === 1 ? 'success' : 'warning'"
        @click="handleSubmit"
        :loading="loading"
      >
        <i :class="auditStatus === 1 ? 'el-icon-check' : 'el-icon-close'"></i>
        {{ auditStatus === 1 ? '确认通过' : '确认驳回' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PurchaseOrderAuditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseOrder: {
      type: Object,
      default: () => ({})
    },
    auditStatus: {
      type: Number,
      default: 1 // 1-通过, 2-驳回
    }
  },
  data() {
    return {
      loading: false,
      form: {
        remark: ''
      },
      rules: {
        remark: [
          {
            validator: (rule, value, callback) => {
              if (this.auditStatus === 2 && !value.trim()) {
                callback(new Error('驳回时必须填写驳回原因'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.form.remark = ''
      }
    }
  },
  methods: {
    // 提交审核
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const auditData = {
            id: this.purchaseOrder.id,
            status: this.auditStatus,
            remark: this.form.remark
          }
          this.$emit('submit', auditData)
          this.handleClose()
        }
      })
    },

    // 关闭对话框
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
/* 对话框样式适配 */
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid var(--border-color-1);
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  padding: 24px;
}

:deep(.el-dialog__footer) {
  background: var(--base-main-bg);
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-color-1);
  border-radius: 0 0 12px 12px;
}

/* 表单样式适配 */
:deep(.el-form-item__label) {
  color: var(--base-color-2);
  font-weight: 500;
}

:deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

:deep(.el-textarea__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

/* 按钮样式适配 */
:deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: var(--theme-color);
  border-color: var(--theme-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(54, 113, 232, 0.3);
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

/* 主题适配 */
.theme-dark :deep(.el-dialog) {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-dialog__body) {
  background: var(--base-item-bg);
}

.theme-dark :deep(.el-dialog__footer) {
  background: var(--base-item-bg);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-textarea__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.audit-info {
  margin-bottom: 20px;
}

.order-summary {
  margin-top: 20px;
}

.order-summary h4 {
  margin-bottom: 10px;
  color: var(--base-text-color);
  font-size: 14px;
}

.dialog-footer {
  text-align: center;
}

/* 主题适配 */
::v-deep .el-descriptions__label {
  color: var(--base-text-color);
}

::v-deep .el-descriptions__content {
  color: var(--base-text-color);
}
</style> 
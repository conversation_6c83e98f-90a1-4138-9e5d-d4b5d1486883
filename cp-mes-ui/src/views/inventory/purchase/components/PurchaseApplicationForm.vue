<template>
  <div class="purchase-application-form">
    <!-- 表单内容 -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="purchase-form"
      @submit.native.prevent
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          基本信息
        </h3>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="采购单号" prop="purchaseNo">
              <el-input
                v-model="form.purchaseNo"
                placeholder="系统自动生成"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input
                v-model="form.applicant"
                placeholder="当前登录用户"
                readonly
                class="readonly-input"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 采购模式选择 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-setting"></i>
          采购模式
        </h3>
        
        <el-form-item label="采购类型" prop="purchaseMode">
          <el-radio-group v-model="form.purchaseMode" @change="handlePurchaseModeChange">
            <el-radio label="material" class="mode-radio">
              <span class="mode-label">仓储原料采购</span>
              <span class="mode-description">从仓储原料库中选择需要采购的原料</span>
            </el-radio>
            <el-radio label="other" class="mode-radio">
              <span class="mode-label">其他类型采购</span>
              <span class="mode-description">采购设备、耗材、工具等其他类型物品</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 物品信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-goods"></i>
          物品信息
        </h3>

        <!-- 仓储原料采购模式 -->
        <div v-if="form.purchaseMode === 'material'">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择原料" prop="itemId">
                <el-select
                  v-model="form.itemId"
                  placeholder="请选择需要采购的原料"
                  filterable
                  style="width: 100%"
                  @change="handleRawMaterialChange"
                  :loading="rawMaterialLoading"
                >
                  <el-option
                    v-for="material in rawMaterialList"
                    :key="material.fields3"
                    :label="getMaterialDisplayLabel(material)"
                    :value="material.fields3"
                  >
                    <span style="float: left; max-width: 60%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {{ material.materialName }}
                    </span>
                    <span style="float: right; color: #8492a6; font-size: 13px; max-width: 35%;">
                      {{ getBoardTypeDisplay(material.boardType) }} | {{ material.unit || '件' }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料信息" v-if="form.purchaseMode === 'material' && form.itemId">
                <el-input
                  :value="getSelectedItemInfo()"
                  readonly
                  class="readonly-input material-info-input"
                  placeholder="选择原料后自动显示"
                >
                  <template slot="prepend">
                    <i class="el-icon-info"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 其他类型采购模式 -->
        <div v-else>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="选择物品" prop="selectedItemId">
                <el-select
                  v-model="form.selectedItemId"
                  placeholder="请选择已有物品或手动填写"
                  filterable
                  clearable
                  style="width: 100%"
                  @change="handleItemChange"
                  :loading="itemLoading"
                >
                  <el-option
                    v-for="item in itemList"
                    :key="item.id"
                    :label="item.itemName"
                    :value="item.id"
                  >
                    <span style="float: left">{{ item.itemName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">
                      {{ item.category || '其他' }} | {{ item.unit || '件' }}
                    </span>
                  </el-option>
                  <el-option :value="null" class="create-option">
                    <el-button
                      type="text"
                      @click="showCustomItemDialog = true"
                      style="width: 100%; color: var(--current-color); font-weight: 600;"
                    >
                      + 快速创建新物品
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物品信息" v-if="form.selectedItemId">
                <el-input
                  :value="getSelectedOtherItemInfo()"
                  readonly
                  class="readonly-input"
                  placeholder="选择物品后自动显示"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 手动填写物品信息（当没有选择已有物品时） -->
          <el-row v-if="form.purchaseMode === 'other' && !form.selectedItemId" :gutter="24">
            <el-col :span="12">
              <el-form-item label="物品类型" prop="itemType">
                <el-select
                  v-model="form.itemType"
                  placeholder="请选择物品类型"
                  style="width: 100%"
                  @change="updateStep"
                >
                  <el-option label="设备" value="设备">
                    <i class="el-icon-cpu"></i> 设备
                  </el-option>
                  <el-option label="耗材" value="耗材">
                    <i class="el-icon-paperclip"></i> 耗材
                  </el-option>
                  <el-option label="工具" value="工具">
                    <i class="el-icon-setting"></i> 工具
                  </el-option>
                  <el-option label="办公用品" value="办公用品">
                    <i class="el-icon-document"></i> 办公用品
                  </el-option>
                  <el-option label="其他" value="其他">
                    <i class="el-icon-more"></i> 其他
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物品名称" prop="itemName">
                <el-input
                  v-model="form.itemName"
                  placeholder="请输入物品名称"
                  @input="updateStep"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="form.purchaseMode === 'other' && !form.selectedItemId" :gutter="24">
            <el-col :span="12">
              <el-form-item label="计量单位" prop="unit">
                <el-select
                  v-model="form.unit"
                  placeholder="请选择计量单位"
                  style="width: 100%"
                  filterable
                  allow-create
                  @change="updateStep"
                >
                  <el-option label="台" value="台"></el-option>
                  <el-option label="个" value="个"></el-option>
                  <el-option label="件" value="件"></el-option>
                  <el-option label="套" value="套"></el-option>
                  <el-option label="盒" value="盒"></el-option>
                  <el-option label="包" value="包"></el-option>
                  <el-option label="瓶" value="瓶"></el-option>
                  <el-option label="米" value="米"></el-option>
                  <el-option label="千克" value="千克"></el-option>
                  <el-option label="升" value="升"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格说明">
                <el-input
                  v-model="form.specification"
                  placeholder="请输入规格说明（可选）"
                  @input="updateStep"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 采购信息 -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="el-icon-shopping-cart-2"></i>
          采购信息
        </h3>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplierId">
              <el-select
                v-model="form.supplierId"
                placeholder="请选择供应商"
                filterable
                style="width: 100%"
                @change="updateStep"
                :loading="supplierLoading"
              >
                <el-option
                  v-for="supplier in supplierList"
                  :key="supplier.id"
                  :label="supplier.supplierName"
                  :value="supplier.id"
                >
                  <span style="float: left">{{ supplier.supplierName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ supplier.contactPerson || '联系人未知' }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购数量" prop="quantity">
              <el-input-number
                v-model="form.quantity"
                :min="1"
                :max="99999"
                :precision="0"
                style="width: 100%"
                @change="updateStep"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="预估单价" prop="price">
              <el-input-number
                v-model="form.price"
                :min="0"
                :precision="2"
                style="width: 100%"
                @change="updateStep"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期望到货时间" prop="expectedDate">
              <el-date-picker
                v-model="form.expectedDate"
                type="date"
                placeholder="请选择期望到货时间"
                style="width: 100%"
                :picker-options="datePickerOptions"
                @change="updateStep"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleReset" :disabled="loading">
          <i class="el-icon-refresh-left"></i>
          重置表单
        </el-button>
        <el-button v-if="form.id" @click="handleCancelEdit" type="warning">
          <i class="el-icon-close"></i>
          取消编辑
        </el-button>

        <el-button type="primary" @click="handleSubmit" :loading="loading">
          <i class="el-icon-check"></i>
          {{ form.id ? '更新申请' : '提交申请' }}
        </el-button>
      </div>
    </el-form>

    <!-- 快速创建物品对话框 -->
    <el-dialog
      title="快速创建新物品"
      :visible.sync="showCustomItemDialog"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="customItemForm"
        :model="customItemForm"
        :rules="customItemRules"
        label-width="100px"
      >
        <el-form-item label="物品名称" prop="itemName">
          <el-input v-model="customItemForm.itemName" placeholder="请输入物品名称" />
        </el-form-item>
        <el-form-item label="物品类型" prop="category">
          <el-select v-model="customItemForm.category" placeholder="请选择物品类型" style="width: 100%">
            <el-option label="设备" value="设备"></el-option>
            <el-option label="耗材" value="耗材"></el-option>
            <el-option label="工具" value="工具"></el-option>
            <el-option label="办公用品" value="办公用品"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input v-model="customItemForm.unit" placeholder="请输入计量单位" />
        </el-form-item>
        <el-form-item label="规格说明">
          <el-input v-model="customItemForm.specification" placeholder="请输入规格说明（可选）" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelCustomItem">取消</el-button>
        <el-button type="primary" @click="handleCreateCustomItem" :loading="customItemLoading">
          <i class="el-icon-check"></i>
          创建并选择
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addOrUpdatePurchaseOrder } from '@/api/jenasi/purchaseOrder'
import { getRawMaterialWarehouseList } from '@/api/jenasi/rawMaterialWarehouse'
import { getItemList } from '@/api/jenasi/item'
import { getSupplierList } from '@/api/jenasi/supplier'
import { addItem } from '@/api/jenasi/item'

export default {
  name: 'PurchaseApplicationForm',

  props: {
    // 是否为批量模式
    batchMode: {
      type: Boolean,
      default: false
    },
    // 初始表单数据（用于编辑）
    initialData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      loading: false,

      // 表单数据
      form: {
        id: null,
        purchaseNo: '',
        purchaseMode: 'material', // 默认为仓储原料采购模式
        itemId: null, // 仓储原料采购时存储fields3值
        selectedItemId: null, // 其他类型采购时选择的item表ID
        itemName: '', // 其他类型采购时的物品名称
        itemType: '', // 其他类型采购时的物品类型
        boardType: '', // 板型类型，仅仓储原料采购时使用
        specification: '', // 规格说明
        supplierId: null,
        quantity: 1,
        unit: '',
        price: 0,
        expectedDate: ''
      },

      // 数据列表
      rawMaterialList: [],
      itemList: [],
      supplierList: [],

      // 加载状态
      rawMaterialLoading: false,
      itemLoading: false,
      supplierLoading: false,

      // 快速创建物品相关
      showCustomItemDialog: false,
      customItemLoading: false,
      customItemForm: {
        itemName: '',
        category: '',
        unit: '',
        specification: ''
      },

      // 表单验证规则
      rules: {
        purchaseNo: [
          { required: true, message: '采购单号不能为空', trigger: 'blur' }
        ],
        applicant: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        itemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'material' && !value) {
                callback(new Error('请选择需要采购的原料'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        selectedItemId: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !value) {
                callback(new Error('请选择需要采购的物品'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        itemName: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !this.form.selectedItemId && !value) {
                callback(new Error('请输入物品名称'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ],
        itemType: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !this.form.selectedItemId && !value) {
                callback(new Error('请选择物品类型'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        unit: [
          {
            validator: (rule, value, callback) => {
              if (this.form.purchaseMode === 'other' && !this.form.selectedItemId && !value) {
                callback(new Error('请选择计量单位'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        supplierId: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入采购数量', trigger: 'blur' },
          { type: 'number', min: 1, message: '采购数量必须大于0', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入预估单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '预估单价不能为负数', trigger: 'blur' }
        ],
        expectedDate: [
          { required: true, message: '请选择期望到货时间', trigger: 'change' }
        ]
      },

      // 快速创建物品验证规则
      customItemRules: {
        itemName: [
          { required: true, message: '请输入物品名称', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择物品类型', trigger: 'change' }
        ],
        unit: [
          { required: true, message: '请输入计量单位', trigger: 'blur' }
        ]
      },

      // 日期选择器配置
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择昨天之前的日期
        }
      }
    }
  },

  computed: {
    currentUser() {
      return this.$store.getters.name || '当前用户'
    }
  },

  watch: {
    initialData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.form = { ...this.form, ...newData }
        }
      },
      immediate: true,
      deep: true
    }
  },

  mounted() {
    this.initForm()
    this.loadInitialData()
  },

  methods: {
    // 初始化表单
    initForm() {
      if (!this.form.purchaseNo) {
        this.form.purchaseNo = this.generatePurchaseNo()
      }
      if (!this.form.applicant) {
        this.form.applicant = this.currentUser
      }
    },

    // 生成采购单号
    generatePurchaseNo() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0')
      return `PO${year}${month}${day}${time}`
    },

    // 加载初始数据
    async loadInitialData() {
      await Promise.all([
        this.loadRawMaterialList(),
        this.loadItemList(),
        this.loadSupplierList()
      ])
    },

    // 加载原料列表
    async loadRawMaterialList() {
      this.rawMaterialLoading = true
      try {
        const response = await getRawMaterialWarehouseList()
        if (response.code === 0 || response.code === 200) {
          this.rawMaterialList = response.data || []
        }
      } catch (error) {
        console.error('加载原料列表失败:', error)
        this.$message.error('加载原料列表失败')
      } finally {
        this.rawMaterialLoading = false
      }
    },

    // 加载物品列表
    async loadItemList() {
      this.itemLoading = true
      try {
        const response = await getItemList()
        if (response.code === 0 || response.code === 200) {
          this.itemList = response.data || []
        }
      } catch (error) {
        console.error('加载物品列表失败:', error)
        this.$message.error('加载物品列表失败')
      } finally {
        this.itemLoading = false
      }
    },

    // 加载供应商列表
    async loadSupplierList() {
      this.supplierLoading = true
      try {
        const response = await getSupplierList()
        if (response.code === 0 || response.code === 200) {
          this.supplierList = response.data || []
        }
      } catch (error) {
        console.error('加载供应商列表失败:', error)
        this.$message.error('加载供应商列表失败')
      } finally {
        this.supplierLoading = false
      }
    },

    // 采购模式变化处理
    handlePurchaseModeChange(mode) {
      // 清空相关字段
      if (mode === 'material') {
        // 切换到仓储原料采购模式
        this.form.itemId = null
        this.form.selectedItemId = null
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.boardType = ''
        this.form.specification = ''
        this.form.unit = ''
      } else {
        // 切换到其他类型采购模式
        this.form.itemId = null
        this.form.selectedItemId = null
        this.form.itemName = ''
        this.form.itemType = ''
        this.form.boardType = ''
        this.form.specification = ''
        this.form.unit = ''
      }
      this.updateStep()
    },

    // 原料选择改变（仓储原料采购模式）
    handleRawMaterialChange(fields3) {
      const selectedMaterial = this.rawMaterialList.find(material => material.fields3 === fields3)
      if (selectedMaterial) {
        this.form.unit = selectedMaterial.unit || '件'
        this.form.itemName = selectedMaterial.materialName
        this.form.itemType = '原料'
        this.form.boardType = selectedMaterial.boardType || '单板'
        console.log('选择原料:', selectedMaterial.materialName, 'fields3:', fields3, '板型:', this.form.boardType)
      }
      this.updateStep()
    },

    // 物品选择改变（其他类型采购模式）
    handleItemChange(itemId) {
      if (itemId) {
        const selectedItem = this.itemList.find(item => item.id === itemId)
        if (selectedItem) {
          this.form.unit = selectedItem.unit || '件'
          this.form.itemName = selectedItem.itemName
          this.form.itemType = selectedItem.category || '其他'
          console.log('选择物品:', selectedItem.itemName)
        }
      } else {
        // 清空选择，准备手动填写
        this.form.unit = ''
        this.form.itemName = ''
        this.form.itemType = ''
      }
      this.updateStep()
    },

    // 获取选中原料的信息显示（仓储原料采购模式）
    getSelectedItemInfo() {
      if (!this.form.itemId) return ''
      const selectedMaterial = this.rawMaterialList.find(material => material.fields3 === this.form.itemId)
      if (!selectedMaterial) return ''

      const parts = []
      if (selectedMaterial.materialType) parts.push(`类型: ${selectedMaterial.materialType}`)
      if (selectedMaterial.boardType) parts.push(`板型: ${this.getBoardTypeDisplay(selectedMaterial.boardType)}`)
      if (selectedMaterial.unit) parts.push(`单位: ${selectedMaterial.unit}`)
      if (selectedMaterial.currentStock !== null) parts.push(`库存: ${selectedMaterial.currentStock}`)
      if (selectedMaterial.fields3) parts.push(`编码: ${selectedMaterial.fields3}`)

      return parts.join(' | ') || '无详细信息'
    },

    // 获取选中物品的信息显示（其他类型采购模式）
    getSelectedOtherItemInfo() {
      if (!this.form.selectedItemId) return ''
      const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
      if (!selectedItem) return ''

      const parts = []
      if (selectedItem.category) parts.push(`分类: ${selectedItem.category}`)
      if (selectedItem.specification) parts.push(`规格: ${selectedItem.specification}`)
      if (selectedItem.brand) parts.push(`品牌: ${selectedItem.brand}`)
      if (selectedItem.unit) parts.push(`单位: ${selectedItem.unit}`)

      return parts.join(' | ') || '无详细信息'
    },

    // 获取原料显示标签（用于下拉框label）
    getMaterialDisplayLabel(material) {
      if (!material) return ''
      const boardType = this.getBoardTypeDisplay(material.boardType)
      const unit = material.unit || '件'
      return `${material.materialName} | ${boardType} | ${unit}`
    },

    // 获取板型显示文本
    getBoardTypeDisplay(boardType) {
      if (!boardType) return '单板'

      // 标准化板型显示
      const boardTypeMap = {
        '上板': '上板',
        '下板': '下板',
        '单板': '单板',
        'upper': '上板',
        'lower': '下板',
        'single': '单板'
      }

      return boardTypeMap[boardType] || boardType || '单板'
    },

    // 更新步骤（用于触发验证等）
    updateStep() {
      // 可以在这里添加步骤更新逻辑
      this.$emit('form-change', this.form)
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        this.loading = true
        const isEdit = !!this.form.id

        const formData = {
          purchaseNo: this.form.purchaseNo,
          applicant: this.currentUser,
          supplierId: Number(this.form.supplierId),
          quantity: Number(this.form.quantity),
          unit: this.form.unit,
          price: Number(this.form.price),
          expectedDate: this.form.expectedDate
        }

        // 根据采购模式设置不同的字段
        if (this.form.purchaseMode === 'material') {
          // 仓储原料采购模式：使用原料的fields3值作为itemId
          formData.itemId = this.form.itemId // 这里存储的是fields3值
          formData.itemName = this.form.itemName // 原料名称
          formData.itemType = this.form.itemType // 固定为"原料"
          formData.boardType = this.form.boardType // 板型类型
          // 后端会根据itemId有值识别为仓储原料采购模式
        } else {
          // 其他类型采购模式
          formData.itemId = null // 设置为null，后端识别为其他类型采购模式
          formData.boardType = null // 其他类型采购不设置板型
          if (this.form.selectedItemId) {
            // 选择了已有物品
            const selectedItem = this.itemList.find(item => item.id === this.form.selectedItemId)
            if (selectedItem) {
              formData.itemName = selectedItem.itemName
              formData.itemType = selectedItem.category || '其他'
            }
          } else {
            // 手动填写物品信息
            formData.itemName = this.form.itemName
            formData.itemType = this.form.itemType
          }
        }

        // 如果是编辑模式，添加id字段
        if (isEdit) {
          formData.id = this.form.id
        }

        if (this.batchMode) {
          // 批量模式：触发事件让父组件处理
          this.$emit('add-to-batch', formData)
          this.handleReset()
        } else {
          // 单个模式：直接提交
          const response = await addOrUpdatePurchaseOrder(formData)
          if (response.code === 0 || response.code === 200) {
            const successMessage = isEdit ? '采购申请更新成功！' : '采购申请提交成功！'
            this.$message.success(successMessage)
            this.$emit('submit-success', response.data)
            this.handleReset()
          } else {
            throw new Error(response.message || '提交失败')
          }
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error(error.message || '提交失败，请重试')
      } finally {
        this.loading = false
      }
    },



    // 重置表单
    handleReset() {
      this.$refs.form.resetFields()
      this.initForm()
      // 清空编辑状态和双模式相关字段
      this.form.id = null
      this.form.purchaseMode = 'material'
      this.form.selectedItemId = null
      this.form.itemName = ''
      this.form.itemType = ''
      this.form.boardType = ''
      this.form.specification = ''
      this.$message.info('表单已重置')
      this.$emit('form-reset')
    },

    // 取消编辑
    handleCancelEdit() {
      this.$confirm('确认取消编辑？未保存的修改将丢失。', '确认取消', {
        confirmButtonText: '确认取消',
        cancelButtonText: '继续编辑',
        type: 'error'
      }).then(() => {
        this.handleReset()
        this.$message.info('已取消编辑')
      }).catch(() => {
        // 用户选择继续编辑
      })
    },

    // 快速创建物品相关方法
    handleCancelCustomItem() {
      this.showCustomItemDialog = false
      this.customItemForm = {
        itemName: '',
        category: '',
        unit: '',
        specification: ''
      }
    },

    async handleCreateCustomItem() {
      try {
        const valid = await this.$refs.customItemForm.validate()
        if (!valid) return

        this.customItemLoading = true
        const response = await addItem(this.customItemForm)

        if (response.code === 0 || response.code === 200) {
          this.$message.success('物品创建成功')

          // 重新加载物品列表
          await this.loadItemList()

          // 自动选择新创建的物品
          const newItem = response.data
          this.form.selectedItemId = newItem.id
          this.handleItemChange(newItem.id)

          this.handleCancelCustomItem()
        } else {
          throw new Error(response.message || '创建失败')
        }
      } catch (error) {
        console.error('创建物品失败:', error)
        this.$message.error(error.message || '创建物品失败，请重试')
      } finally {
        this.customItemLoading = false
      }
    },

    // 设置表单数据（用于编辑）
    setFormData(data) {
      this.form = { ...this.form, ...data }
    },

    // 获取表单数据
    getFormData() {
      return { ...this.form }
    },

    // 验证表单
    async validateForm() {
      try {
        return await this.$refs.form.validate()
      } catch (error) {
        return false
      }
    }
  }
}
</script>

<style scoped>
.purchase-application-form {
  max-width: 1200px;
  margin: 0 auto;
}

.purchase-form {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  color: #409eff;
}

.mode-radio {
  display: block;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.mode-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.mode-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.mode-label {
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 4px;
}

.mode-description {
  color: #909399;
  font-size: 13px;
  line-height: 1.4;
}

.readonly-input :deep(.el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #909399;
}

.material-info-input :deep(.el-input__inner) {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #495057;
  font-size: 13px;
}

.material-info-input :deep(.el-input-group__prepend) {
  background-color: #e9ecef;
  border-color: #e9ecef;
  color: #6c757d;
}

.form-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.form-actions .el-button {
  margin: 0 8px;
  min-width: 120px;
}

.create-option {
  border-top: 1px solid #e4e7ed;
  margin-top: 8px;
  padding-top: 8px;
}

/* 原料下拉框选项样式优化 */
:deep(.el-select-dropdown__item) {
  height: auto;
  line-height: 1.4;
  padding: 8px 20px;
}

:deep(.el-select-dropdown__item span) {
  display: block;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .purchase-form {
    padding: 16px;
  }

  .form-actions .el-button {
    margin: 4px;
    min-width: 100px;
  }
}
</style>

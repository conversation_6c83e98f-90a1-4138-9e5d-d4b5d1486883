<template>
  <el-dialog
    :title="isEdit ? '编辑供应商' : '新增供应商'"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      style="padding-right: 30px"
    >
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input 
          v-model="form.supplierName" 
          placeholder="请输入供应商名称"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更 新' : '新 增' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addOrUpdateSupplier } from '@/api/jenasi/supplier'

export default {
  name: 'SupplierDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: null,
        supplierName: ''
      },
      rules: {
        supplierName: [
          { required: true, message: '请输入供应商名称', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.formData) {
        this.form = { ...this.formData }
      } else {
        this.form = {
          id: null,
          supplierName: ''
        }
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const response = await addOrUpdateSupplier(this.form)
            if (response.code === 0 || response.code === 200) {
              this.$message.success(this.isEdit ? '更新成功' : '新增成功')
              this.$emit('submit')
              this.handleClose()
            } else {
              this.$message.error(response.message || '操作失败')
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 关闭对话框
    handleClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>



/* 对话框样式 */
.el-dialog :deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 24px;
  border-radius: 8px 8px 0 0;
  border-bottom: none;
}

.el-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.el-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 24px;
}

.el-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 20px;
  transition: all 0.3s ease;
}

.el-dialog :deep(.el-dialog__close:hover) {
  color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.el-dialog :deep(.el-dialog__body) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  padding: 24px;
  border-radius: 0 0 8px 8px;
}

/* 表单样式 */
.el-form :deep(.el-form-item__label) {
  color: var(--theme-color);
  font-weight: 500;
  font-size: 14px;
}

.el-form :deep(.el-input__inner) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  color: var(--theme-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 36px;
  line-height: 36px;
}

.el-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
}

.el-form :deep(.el-input__inner:hover) {
  border-color: var(--current-color);
}

/* 按钮样式 */
.dialog-footer {
  text-align: center;
  padding: 20px 24px;
  background: var(--base-main-bg);
  border-top: 1px solid var(--border-color-1);
  border-radius: 0 0 8px 8px;
}

.dialog-footer .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 24px;
  min-width: 80px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dialog-footer .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.dialog-footer .el-button:hover::before {
  left: 100%;
}

.dialog-footer .el-button--primary {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
  box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
}

.dialog-footer .el-button--primary:hover {
  background: var(--color-2);
  border-color: var(--color-2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(54, 113, 232, 0.4);
}

.dialog-footer .el-button--default {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.dialog-footer .el-button--default:hover {
  background: var(--base-color-8);
  border-color: var(--current-color);
  color: var(--current-color);
  transform: translateY(-2px);
}

/* 主题适配 */
.theme-dark .el-dialog :deep(.el-dialog__body) {
  background: var(--base-item-bg);
}

.theme-dark .el-form :deep(.el-input__inner) {
  background: var(--primary-color);
  border-color: var(--input-border-color);
  color: var(--theme-color);
}

.theme-dark .el-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 8px rgba(58, 123, 153, 0.3);
}

.theme-dark .dialog-footer {
  background: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.theme-dark .dialog-footer .el-button--default {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--theme-color);
}

.theme-dark .dialog-footer .el-button--default:hover {
  background: var(--base-menu-background-active);
  border-color: var(--current-color);
  color: var(--current-color);
}
</style> 
<template>
  <el-dialog
    title="录入实际价格"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 订单基本信息 -->
    <div class="order-info-section">
      <h4>订单信息</h4>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <span class="label">采购订单号：</span>
            <span class="value">{{ orderInfo.purchaseNo }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">物品名称：</span>
            <span class="value">{{ orderInfo.itemName }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">申请数量：</span>
            <span class="value">{{ orderInfo.quantity }} {{ orderInfo.unit }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="label">预估单价：</span>
            <span class="value">¥{{ orderInfo.price }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 价格录入表单 -->
    <div class="price-form-section">
      <h4>实际价格录入</h4>
      <el-form
        ref="priceForm"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际单价" prop="actualPrice">
              <el-input-number
                v-model="form.actualPrice"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入实际单价"
                @change="calculateActualSubtotal"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际总额" prop="actualSubtotal">
              <el-input-number
                v-model="form.actualSubtotal"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="自动计算或手动输入"
                @change="onActualSubtotalChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>

        <!-- 价格差异显示 -->
        <div v-if="showPriceDifference" class="price-difference-section">
          <h5>价格差异分析</h5>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="difference-item">
                <span class="label">单价差异：</span>
                <span :class="getPriceDifferenceClass(priceDifference)">
                  {{ formatPriceDifference(priceDifference) }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="difference-item">
                <span class="label">总额差异：</span>
                <span :class="getPriceDifferenceClass(subtotalDifference)">
                  {{ formatPriceDifference(subtotalDifference) }}
                </span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 强制更新选项（隐藏，默认启用） -->
        <!--
        <el-form-item v-if="hasExistingActualPrice">
          <el-checkbox v-model="form.forceUpdate">
            强制更新（该订单已有实际价格记录）
          </el-checkbox>
        </el-form-item>
        -->
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        {{ hasExistingActualPrice ? '更新价格' : '录入价格' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateActualPrice } from '@/api/jenasi/purchaseOrder'

export default {
  name: 'ActualPriceDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        actualPrice: null,
        actualSubtotal: null,
        remark: '',
        forceUpdate: true  // 默认启用强制更新
      },
      rules: {
        actualPrice: [
          { required: true, message: '请输入实际单价', trigger: 'blur' },
          { type: 'number', min: 0, message: '实际单价不能为负数', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    hasExistingActualPrice() {
      return this.orderInfo.actualPrice != null
    },
    showPriceDifference() {
      return this.form.actualPrice != null && this.orderInfo.price != null
    },
    priceDifference() {
      if (this.form.actualPrice == null || this.orderInfo.price == null) {
        return 0
      }
      return this.form.actualPrice - this.orderInfo.price
    },
    subtotalDifference() {
      if (this.form.actualSubtotal == null || this.orderInfo.subtotal == null) {
        return 0
      }
      return this.form.actualSubtotal - this.orderInfo.subtotal
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    }
  },
  methods: {
    initForm() {
      // 如果已有实际价格，则填充到表单中
      if (this.hasExistingActualPrice) {
        this.form.actualPrice = this.orderInfo.actualPrice
        this.form.actualSubtotal = this.orderInfo.actualSubtotal
      } else {
        this.form.actualPrice = null
        this.form.actualSubtotal = null
      }
      this.form.remark = ''
      this.form.forceUpdate = true  // 默认启用强制更新
    },
    
    // 计算实际总额
    calculateActualSubtotal() {
      if (this.form.actualPrice != null && this.orderInfo.quantity) {
        this.form.actualSubtotal = (this.form.actualPrice * this.orderInfo.quantity).toFixed(2)
        this.form.actualSubtotal = parseFloat(this.form.actualSubtotal)
      }
    },
    
    // 实际总额变化时的处理
    onActualSubtotalChange() {
      // 用户手动修改了总额，不再自动计算
    },
    
    // 获取价格差异的样式类
    getPriceDifferenceClass(difference) {
      if (difference > 0) {
        return 'price-increase'
      } else if (difference < 0) {
        return 'price-decrease'
      }
      return 'price-equal'
    },
    
    // 格式化价格差异显示
    formatPriceDifference(difference) {
      if (difference === 0) {
        return '无差异'
      }
      const prefix = difference > 0 ? '+' : ''
      return `${prefix}¥${difference.toFixed(2)}`
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.priceForm.validate()
        
        this.loading = true
        
        const requestData = {
          id: this.orderInfo.id,
          actualPrice: this.form.actualPrice,
          actualSubtotal: this.form.actualSubtotal,
          remark: this.form.remark,
          forceUpdate: this.form.forceUpdate
        }
        
        const response = await updateActualPrice(requestData)
        
        if (response.code === 0 || response.code === 200) {
          this.$message.success('实际价格录入成功')
          // 确保返回完整的订单数据，包含actualPrice和actualSubtotal
          const updatedOrderData = {
            ...this.orderInfo,
            ...response.data,
            actualPrice: this.form.actualPrice,
            actualSubtotal: this.form.actualSubtotal,
            actualPriceTime: new Date(),
            actualPriceRecorder: response.data.actualPriceRecorder || 'current_user'
          }
          this.$emit('success', updatedOrderData)
          this.handleClose()
        } else {
          this.$message.error(response.msg || '录入失败')
        }
        
      } catch (error) {
        console.error('录入实际价格失败:', error)
        this.$message.error('录入失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.priceForm?.resetFields()
    }
  }
}
</script>

<style scoped>
.order-info-section,
.price-form-section {
  margin-bottom: 20px;
}

.order-info-section h4,
.price-form-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #606266;
  font-weight: 500;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  font-weight: 600;
}

.price-difference-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.price-difference-section h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.difference-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.difference-item .label {
  color: #606266;
  min-width: 80px;
}

.price-increase {
  color: #f56c6c;
  font-weight: 600;
}

.price-decrease {
  color: #67c23a;
  font-weight: 600;
}

.price-equal {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>

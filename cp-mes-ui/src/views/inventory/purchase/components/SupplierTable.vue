<template>
  <div class="supplier-container">
    <!-- 搜索表单 -->
    <el-form inline class="search-form">
      <el-form-item label="供应商名称">
        <el-input 
          v-model="searchForm.supplierName" 
          placeholder="请输入供应商名称" 
          clearable 
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch" class="search-btn">
          查询
        </el-button>
        <el-button @click="resetSearch" class="reset-btn">重置</el-button>
        <el-button type="success" @click="handleAdd" class="add-btn">
          新增供应商
        </el-button>
        <el-button type="warning" @click="handleExport" class="export-btn" :loading="exportLoading">
          导出Excel
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      border
      highlight-current-row
      :header-cell-style="{background:'var(--current-color)', color:'white'}"
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="60" align="center" />
      <el-table-column prop="supplierName" label="供应商名称" min-width="200" align="center" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" width="180" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="180" align="center">
        <template slot-scope="scope">
          {{ formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="scope">
          <div style="text-align: center;">
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: center"
    />

    <!-- 新增/编辑对话框 -->
    <supplier-dialog 
      :visible.sync="dialogVisible" 
      :form-data="formData" 
      :is-edit="isEdit"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getSupplierList, deleteSupplier, addOrUpdateSupplier, exportSupplierList } from '@/api/jenasi/supplier'
import SupplierDialog from './SupplierDialog.vue'

export default {
  name: 'SupplierTable',
  components: {
    SupplierDialog
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      searchForm: {
        supplierName: ''
      },
      dialogVisible: false,
      formData: {},
      isEdit: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          supplierName: this.searchForm.supplierName || ''
        }
        
        const response = await getSupplierList(params)
        if (response.code === 0 || response.code === 200) {
          this.tableData = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadData()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        supplierName: ''
      }
      this.currentPage = 1
      this.loadData()
    },

    // 新增
    handleAdd() {
      this.formData = {}
      this.isEdit = false
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.formData = { ...row }
      this.isEdit = true
      this.dialogVisible = true
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个供应商吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await deleteSupplier(row.id)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    // 表单提交
    async handleSubmit(formData) {
      this.dialogVisible = false
      this.loadData()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadData()
    },

    // 导出Excel
    async handleExport() {
      // 检查是否有数据可导出
      if (!this.tableData || this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      // 确认导出操作
      try {
        await this.$confirm(
          '确定要导出供应商数据吗？', 
          '导出确认', 
          {
            confirmButtonText: '确定导出',
            cancelButtonText: '取消',
            type: 'info',
            center: true
          }
        )
      } catch {
        return // 用户取消导出
      }

      this.exportLoading = true
      
      try {
        // 显示导出进度提示
        const loadingInstance = this.$loading({
          lock: true,
          text: '正在生成Excel文件，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        console.log('开始调用导出接口...')
        const response = await exportSupplierList()
        console.log('导出接口响应:', response)
        
        // 验证响应数据
        if (!response || response.size === 0) {
          throw new Error('导出数据为空')
        }

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // 生成文件名
        const now = new Date()
        const dateStr = now.getFullYear() + 
          String(now.getMonth() + 1).padStart(2, '0') + 
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') + 
          String(now.getMinutes()).padStart(2, '0') + 
          String(now.getSeconds()).padStart(2, '0')
        
        link.download = `供应商列表导出_${dateStr}.xlsx`
        
        // 设置下载属性并触发下载
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        
        // 清理资源
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 100)
        
        // 关闭加载提示
        loadingInstance.close()
        
        // 成功提示
        this.$message({
          type: 'success',
          message: `导出成功！文件已保存为：供应商列表导出_${dateStr}.xlsx`,
          duration: 4000
        })
        
      } catch (error) {
        console.error('导出失败:', error)
        
        // 详细的错误处理
        let errorMessage = '导出失败，请稍后重试'
        
        if (error.response) {
          // 服务器响应错误
          if (error.response.status === 404) {
            errorMessage = '导出接口不存在，请联系管理员'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请联系管理员'
          } else if (error.response.status === 403) {
            errorMessage = '没有导出权限，请联系管理员'
          } else if (error.response.data) {
            // 处理后端返回的特殊响应
            if (typeof error.response.data === 'string' && error.response.data.includes('暂无数据')) {
              errorMessage = '暂无数据可导出'
            }
          }
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message) {
          // 其他错误
          errorMessage = error.message
        }
        
        this.$message({
          type: 'error',
          message: errorMessage,
          duration: 5000
        })
        
      } finally {
        this.exportLoading = false
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style scoped>
.supplier-container {
  background: var(--base-main-bg);
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-form {
  background: var(--base-item-bg);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
}

.search-form :deep(.el-form-item__label) {
  color: var(--base-color-2);
  font-weight: 500;
}

.search-form :deep(.el-input__inner) {
  background: var(--base-menu-background);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
  transition: all 0.3s ease;
}

.search-form :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

.search-form :deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
}

.search-form :deep(.el-button--primary:hover) {
  background: var(--current-color);
  border-color: var(--current-color);
  opacity: 0.8;
}

/* 表格行样式 - 亮色主题 */
.el-table :deep(.el-table__row) {
  background: #ffffff !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:nth-child(even)) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row:hover) {
  background: #f5f7fa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row td) {
  color: #303133 !important;
  background: transparent !important;
}

/* 覆盖Element UI默认stripe样式 */
.el-table :deep(.el-table__row.el-table__row--striped) {
  background: #fafafa !important;
  color: #303133 !important;
}

.el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: #303133 !important;
}

/* 表格样式适配 */
:deep(.el-table) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

:deep(.el-table td) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
}

:deep(.el-table th) {
  background: var(--current-color) !important;
  color: white !important;
  border-color: var(--border-color-1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--base-item-bg);
}

:deep(.el-table__body tr:hover > td) {
  background: var(--base-color-8) !important;
}

/* 分页样式适配 */
:deep(.el-pagination) {
  color: var(--base-color-2);
}

:deep(.el-pagination .el-pager li) {
  background: var(--base-item-bg);
  color: var(--base-color-2);
  border: 1px solid var(--border-color-1);
}

:deep(.el-pagination .el-pager li:hover) {
  color: var(--current-color);
}

:deep(.el-pagination .el-pager li.active) {
  background: var(--current-color);
  color: white;
}

:deep(.el-pagination button) {
  background: var(--base-item-bg);
  color: var(--base-color-2);
  border: 1px solid var(--border-color-1);
}

:deep(.el-pagination button:hover) {
  color: var(--current-color);
}

/* 按钮样式优化 - 参考采购订单管理样式 */
.search-btn:hover {
  background: var(--current-color) !important;
  border-color: var(--current-color) !important;
  color: white !important;
}

.reset-btn:hover {
  background: var(--base-color-8) !important;
  border-color: var(--border-color-1) !important;
  color: var(--base-color-1) !important;
}

.add-btn:hover {
  background: #85ce61 !important;
  border-color: #85ce61 !important;
  color: white !important;
}



/* 导出按钮样式 */
.export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-btn:hover {
  background: #ebb563 !important;
  border-color: #ebb563 !important;
  color: white !important;
}

.export-btn:focus {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.export-btn:active {
  background: #cf9236 !important;
  border-color: #cf9236 !important;
  color: white !important;
}

/* 全局覆盖确保导出按钮样式优先级 */
:deep(.el-button--warning.export-btn) {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:hover) {
  background: #ebb563 !important;
  border-color: #ebb563 !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:focus) {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

:deep(.el-button--warning.export-btn:active) {
  background: #cf9236 !important;
  border-color: #cf9236 !important;
  color: white !important;
}

/* 主题特定样式 */
.theme-light .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.theme-dark .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

.theme-starry-sky .export-btn {
  background: #e6a23c !important;
  border-color: #e6a23c !important;
  color: white !important;
}

/* 主题适配 */
.theme-dark .supplier-container {
  background: var(--base-item-bg);
}

.theme-dark .search-form {
  background: var(--base-menu-background);
  border-color: var(--border-color-2);
}

.theme-dark :deep(.el-table) {
  background: var(--base-item-bg);
}

.theme-dark :deep(.el-table td) {
  background: var(--base-item-bg);
}

.theme-dark .el-table :deep(.el-table__row) {
  background: var(--base-item-bg) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:nth-child(even)) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row:hover) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row td) {
  color: var(--theme-color) !important;
  background: transparent !important;
}

/* 深色主题 - 覆盖Element UI默认stripe样式 */
.theme-dark .el-table :deep(.el-table__row.el-table__row--striped) {
  background: var(--base-menu-background) !important;
  color: var(--theme-color) !important;
}

.theme-dark .el-table :deep(.el-table__row.el-table__row--striped td) {
  background: transparent !important;
  color: var(--theme-color) !important;
}

.theme-dark :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: var(--base-menu-background);
}
</style> 
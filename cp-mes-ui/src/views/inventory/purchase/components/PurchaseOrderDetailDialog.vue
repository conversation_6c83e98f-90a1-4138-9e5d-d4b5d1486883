<template>
  <el-dialog
    title="采购订单详情"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose"
  >
    <div class="detail-container">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="采购单号">
          <el-tag type="primary">{{ purchaseOrder.purchaseNo || '--' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(purchaseOrder.status)" size="small">
            {{ getStatusText(purchaseOrder.status) }}
          </el-tag>
        </el-descriptions-item>
        <!-- 🔧 新增：采购类型显示 -->
        <el-descriptions-item label="采购类型">
          <el-tag :type="isExternalItemPurchase() ? 'warning' : 'success'" size="small">
            {{ isExternalItemPurchase() ? '外部物品' : '仓储原料' }}
          </el-tag>
          <el-tooltip v-if="isExternalItemPurchase()" 
                      content="外部物品采购不支持系统自动入库" 
                      placement="top">
            <i class="el-icon-question" style="margin-left: 5px; color: #e6a23c;"></i>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="物品名称">
          {{ purchaseOrder.itemName || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="供应商">
          {{ purchaseOrder.supplierName || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="采购数量">
          <span style="color: #409eff; font-weight: bold;">
            {{ purchaseOrder.quantity || 0 }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="计量单位">
          {{ purchaseOrder.unit || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="预估单价">
          <span style="color: #e6a23c;">
            ¥{{ purchaseOrder.price || 0 }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="预估总额">
          <span style="color: #e6a23c; font-weight: bold; font-size: 16px;">
            ¥{{ purchaseOrder.subtotal || 0 }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="实际单价">
          <span v-if="purchaseOrder.actualPrice" style="color: #67c23a; font-weight: bold;">
            ¥{{ purchaseOrder.actualPrice }}
          </span>
          <el-tooltip v-else content="实际价格尚未录入" placement="top">
            <span style="color: #909399;">未录入</span>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="实际总额">
          <span v-if="purchaseOrder.actualSubtotal" style="color: #67c23a; font-weight: bold; font-size: 16px;">
            ¥{{ purchaseOrder.actualSubtotal }}
          </span>
          <el-tooltip v-else content="实际价格尚未录入" placement="top">
            <span style="color: #909399;">未录入</span>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="价格差异" v-if="purchaseOrder.actualSubtotal && purchaseOrder.subtotal">
          <span :class="getPriceDifferenceClass(purchaseOrder.actualSubtotal - purchaseOrder.subtotal)">
            {{ formatPriceDifference(purchaseOrder.actualSubtotal - purchaseOrder.subtotal) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="价格差异" v-else>
          <span style="color: #909399;">--</span>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ purchaseOrder.applicant || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核人">
          {{ purchaseOrder.approver || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ formatDate(purchaseOrder.applyTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="期望到货时间">
          <span :style="getExpectedDateStyle(purchaseOrder.expectedDate)">
            {{ formatDate(purchaseOrder.expectedDate) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="审核时间" v-if="purchaseOrder.approveTime">
          {{ formatDate(purchaseOrder.approveTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(purchaseOrder.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="实际价格录入时间" v-if="purchaseOrder.actualPriceTime">
          <el-tooltip :content="`录入人：${purchaseOrder.actualPriceRecorder || '未知'}`" placement="top">
            <span style="color: #67c23a;">{{ formatDate(purchaseOrder.actualPriceTime) }}</span>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="实际价格录入时间" v-else>
          <span style="color: #909399;">未录入</span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 备注信息 -->
      <div class="remark-section" v-if="purchaseOrder.remark">
        <h4>备注信息</h4>
        <div class="remark-content">
          {{ purchaseOrder.remark }}
        </div>
      </div>

      <!-- 时间轴 -->
      <div class="timeline-section">
        <h4>操作记录</h4>
        <el-timeline>
          <el-timeline-item
            v-if="purchaseOrder.createTime"
            timestamp="创建订单"
            type="primary"
            icon="el-icon-plus"
          >
            <p>{{ formatDate(purchaseOrder.createTime) }}</p>
            <p>{{ purchaseOrder.applicant }} 创建了采购订单</p>
          </el-timeline-item>
          
          <el-timeline-item
            v-if="purchaseOrder.approveTime && purchaseOrder.status === 1"
            timestamp="审核通过"
            type="success"
            icon="el-icon-check"
          >
            <p>{{ formatDate(purchaseOrder.approveTime) }}</p>
            <p>{{ purchaseOrder.approver }} 审核通过</p>
          </el-timeline-item>
          
          <el-timeline-item
            v-if="purchaseOrder.approveTime && purchaseOrder.status === 2"
            timestamp="审核驳回"
            type="danger"
            icon="el-icon-close"
          >
            <p>{{ formatDate(purchaseOrder.approveTime) }}</p>
            <p>{{ purchaseOrder.approver }} 审核驳回</p>
          </el-timeline-item>
          
          <el-timeline-item
            v-if="purchaseOrder.actualPriceTime"
            timestamp="实际价格录入"
            type="success"
            icon="el-icon-money"
          >
            <p>{{ formatDate(purchaseOrder.actualPriceTime) }}</p>
            <p>{{ purchaseOrder.actualPriceRecorder || '系统' }} 录入了实际价格：¥{{ purchaseOrder.actualPrice }}</p>
          </el-timeline-item>

          <el-timeline-item
            v-if="purchaseOrder.status === 3"
            timestamp="订单完成"
            type="info"
            icon="el-icon-circle-check"
          >
            <p>采购订单已完成</p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
      <!-- 🔧 修改：添加item_id检查逻辑 -->
      <el-button 
        v-if="showConfirmReceiptButton()" 
        type="success" 
        @click="handleComplete"
      >
        <i class="el-icon-check"></i>
       确认收货
      </el-button>
      <!-- 🔧 外部物品提示 -->
      <el-tooltip 
        v-if="isExternalItemPurchase() && purchaseOrder.status === 1"
        content="外部物品采购暂不支持系统入库，请线下处理"
        placement="top">
        <el-button type="warning" disabled>
          <i class="el-icon-warning"></i>
          外部物品
        </el-button>
      </el-tooltip>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PurchaseOrderDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    purchaseOrder: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 🔧 判断是否显示确认收货按钮
    showConfirmReceiptButton() {
      // 基本条件：订单状态为已通过(1)
      if (this.purchaseOrder.status !== 1) {
        return false;
      }
      
      // 🎯 关键判断：物料ID不为空才显示确认收货按钮
      if (!this.purchaseOrder.itemId || 
          this.purchaseOrder.itemId === 'null' || 
          this.purchaseOrder.itemId.trim() === '') {
        console.warn(`采购订单 ${this.purchaseOrder.purchaseNo} 的物料ID为空，隐藏确认收货按钮`);
        return false;
      }
      
      return true;
    },
    
    // 🔧 判断是否为外部物品采购
    isExternalItemPurchase() {
      return !this.purchaseOrder.itemId || 
             this.purchaseOrder.itemId === 'null' || 
             this.purchaseOrder.itemId.trim() === '';
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: '',
        1: 'success',
        2: 'danger',
        3: 'info'
      }
      return statusMap[status] || ''
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回',
        3: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 获取期望到货时间样式
    getExpectedDateStyle(expectedDate) {
      if (!expectedDate) return {}
      
      const now = new Date()
      const expected = new Date(expectedDate)
      const diffDays = Math.ceil((expected - now) / (1000 * 60 * 60 * 24))
      
      if (diffDays < 0) {
        return { color: '#f56c6c', fontWeight: 'bold' } // 已过期
      } else if (diffDays <= 3) {
        return { color: '#e6a23c', fontWeight: 'bold' } // 即将到期
      } else {
        return { color: '#67c23a' } // 正常
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '--'
      return new Date(date).toLocaleString('zh-CN')
    },

    // 获取价格差异的样式类
    getPriceDifferenceClass(difference) {
      if (difference > 0) {
        return 'price-increase'
      } else if (difference < 0) {
        return 'price-decrease'
      }
      return 'price-equal'
    },

    // 格式化价格差异显示
    formatPriceDifference(difference) {
      if (difference === 0) {
        return '无差异'
      }
      const prefix = difference > 0 ? '+' : ''
      return `${prefix}¥${Math.abs(difference).toFixed(2)}`
    },

    // 标记完成
    async handleComplete() {
      try {
        // 🔧 二次确认检查：确保不是外部物品采购
        if (this.isExternalItemPurchase()) {
          this.$message.error('外部物品采购不支持系统入库操作，请联系管理员进行线下处理');
          return;
        }

        await this.$confirm('确认标记此采购订单为已完成？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 触发完成事件，由父组件处理API调用
        this.$emit('complete', this.purchaseOrder.id)
        this.$message.success('订单已标记为完成')
        this.handleClose()
      } catch (error) {
        // 用户取消操作
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.detail-container {
  padding: 10px 0;
}

.remark-section {
  margin-top: 20px;
}

.remark-section h4 {
  margin-bottom: 10px;
  color: var(--base-color-1);
  font-size: 16px;
  font-weight: 600;
}

.remark-content {
  background: var(--base-item-bg);
  padding: 20px;
  border-radius: 8px;
  color: var(--base-color-1);
  line-height: 1.6;
  border: 1px solid var(--border-color-1);
  border-left: 4px solid var(--current-color);
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.timeline-section {
  margin-top: 30px;
}

.timeline-section h4 {
  margin-bottom: 15px;
  color: var(--base-color-1);
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  text-align: center;
}

/* 描述列表样式优化 */
::v-deep .el-descriptions__label {
  font-weight: 600;
  color: var(--base-text-color);
}

::v-deep .el-descriptions__content {
  color: var(--base-text-color);
}

/* 时间轴样式优化 */
::v-deep .el-timeline-item__timestamp {
  font-weight: 600;
  color: #409eff;
}

/* 主题适配 */
.theme-dark .remark-content {
  background: var(--base-menu-background);
  color: var(--base-color-1);
  border-color: var(--border-color-2);
  border-left-color: var(--current-color);
}

.theme-dark .remark-section h4,
.theme-dark .timeline-section h4 {
  color: var(--base-color-1);
}

/* 对话框样式适配 */
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid var(--border-color-1);
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  padding: 24px;
  color: var(--base-color-1);
}

/* 价格差异样式 */
.price-increase {
  color: #f56c6c;
  font-weight: bold;
}

.price-decrease {
  color: #67c23a;
  font-weight: bold;
}

.price-equal {
  color: #909399;
  font-weight: bold;
}
</style>
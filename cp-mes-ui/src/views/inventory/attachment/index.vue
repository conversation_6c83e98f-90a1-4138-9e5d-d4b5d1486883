<template>
  <div class="warehouse-statistics-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-data-analysis"></i>
        仓库统计分析中心
      </h1>
      <p class="page-subtitle">基于出入库操作记录的多维度数据分析与可视化展示</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="filter-card" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="区域编码" prop="zoneCode">
              <el-input
                v-model="queryParams.zoneCode"
                placeholder="请输入区域编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作类型" prop="operationType">
              <el-select v-model="queryParams.operationType" placeholder="请选择操作类型" clearable>
                <el-option label="入库" value="inbound" />
                <el-option label="出库" value="outbound" />
                <el-option label="移库" value="transfer" />
                <el-option label="调整" value="adjust" />
                <el-option label="盘点" value="check" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="queryParams.materialName"
                placeholder="请输入物料名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作人员" prop="operator">
              <el-input
                v-model="queryParams.operator"
                placeholder="请输入操作人员"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right;">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              <el-button type="success" icon="el-icon-download" @click="handleExport">导出报表</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :span="6">
        <el-card class="overview-card inbound-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-upload2"></i>
            </div>
            <div class="card-info">
              <div class="card-title">总入库量</div>
              <div class="card-value">{{ overviewData.totalInbound || 0 }}</div>
              <div class="card-trend">
                <i class="el-icon-top" v-if="overviewData.inboundTrend > 0"></i>
                <i class="el-icon-bottom" v-else-if="overviewData.inboundTrend < 0"></i>
                <span>{{ Math.abs(overviewData.inboundTrend || 0) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card outbound-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="card-info">
              <div class="card-title">总出库量</div>
              <div class="card-value">{{ overviewData.totalOutbound || 0 }}</div>
              <div class="card-trend">
                <i class="el-icon-top" v-if="overviewData.outboundTrend > 0"></i>
                <i class="el-icon-bottom" v-else-if="overviewData.outboundTrend < 0"></i>
                <span>{{ Math.abs(overviewData.outboundTrend || 0) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card utilization-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="card-info">
              <div class="card-title">仓库利用率</div>
              <div class="card-value">{{ overviewData.warehouseUtilization || 0 }}%</div>
              <div class="card-trend">
                <i class="el-icon-top" v-if="overviewData.utilizationTrend > 0"></i>
                <i class="el-icon-bottom" v-else-if="overviewData.utilizationTrend < 0"></i>
                <span>{{ Math.abs(overviewData.utilizationTrend || 0) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card activity-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon">
              <i class="el-icon-s-operation"></i>
            </div>
            <div class="card-info">
              <div class="card-title">操作总数</div>
              <div class="card-value">{{ overviewData.totalOperations || 0 }}</div>
              <div class="card-trend">
                <i class="el-icon-top" v-if="overviewData.operationsTrend > 0"></i>
                <i class="el-icon-bottom" v-else-if="overviewData.operationsTrend < 0"></i>
                <span>{{ Math.abs(overviewData.operationsTrend || 0) }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表展示区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 出入库趋势分析 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span class="chart-title">
              <i class="el-icon-s-data"></i>出入库趋势分析
            </span>
            <el-button-group class="chart-controls">
              <el-button size="mini" :type="trendPeriod === '7d' ? 'primary' : ''" @click="changeTrendPeriod('7d')">7天</el-button>
              <el-button size="mini" :type="trendPeriod === '30d' ? 'primary' : ''" @click="changeTrendPeriod('30d')">30天</el-button>
              <el-button size="mini" :type="trendPeriod === '90d' ? 'primary' : ''" @click="changeTrendPeriod('90d')">90天</el-button>
            </el-button-group>
          </div>
          <div ref="trendChart" class="chart-container" v-loading="trendLoading"></div>
        </el-card>
      </el-col>

      <!-- 物料类型分布 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span class="chart-title">
              <i class="el-icon-pie-chart"></i>物料类型分布
            </span>
          </div>
          <div ref="materialChart" class="chart-container" v-loading="materialLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行图表 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 仓库利用率分析 -->
      <el-col :span="8">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span class="chart-title">
              <i class="el-icon-s-grid"></i>仓库利用率
            </span>
          </div>
          <div ref="warehouseChart" class="chart-container" v-loading="warehouseLoading"></div>
        </el-card>
      </el-col>

      <!-- 操作员活跃度 -->
      <el-col :span="8">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span class="chart-title">
              <i class="el-icon-user"></i>操作员活跃度
            </span>
          </div>
          <div ref="operatorChart" class="chart-container" v-loading="operatorLoading"></div>
        </el-card>
      </el-col>

      <!-- 异常操作统计 -->
      <el-col :span="8">
        <el-card class="chart-card" shadow="hover">
          <div slot="header" class="chart-header">
            <span class="chart-title">
              <i class="el-icon-warning"></i>异常操作统计
            </span>
          </div>
          <div ref="abnormalChart" class="chart-container" v-loading="abnormalLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card" shadow="hover">
      <div slot="header" class="table-header">
        <span class="table-title">
          <i class="el-icon-s-order"></i>详细操作记录
        </span>
        <div class="table-controls">
          <el-input
            v-model="tableSearch"
            placeholder="搜索记录..."
            size="mini"
            style="width: 200px; margin-right: 10px;"
            prefix-icon="el-icon-search"
            @input="handleTableSearch"
          />
          <el-button size="mini" type="primary" @click="refreshTable">刷新</el-button>
        </div>
      </div>
      
      <el-table 
        v-loading="tableLoading" 
        :data="filteredTableData" 
        border 
        stripe
        height="400"
        @sort-change="handleSortChange"
      >
        <el-table-column label="日志ID" prop="logId" width="80" align="center" sortable="custom" />
        <el-table-column label="区域编码" prop="zoneCode" width="100" align="center" />
        <el-table-column label="操作类型" prop="operationType" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getOperationTypeTag(scope.row.operationType)" size="mini">
              {{ getOperationTypeName(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="物料信息" min-width="150">
          <template slot-scope="scope">
            <div>
              <div class="material-name">{{ scope.row.materialName }}</div>
              <div class="material-id">ID: {{ scope.row.materialId }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数量变化" width="120" align="center">
          <template slot-scope="scope">
            <span :class="['quantity-change', scope.row.quantityChange > 0 ? 'positive' : 'negative']">
              {{ scope.row.quantityChange > 0 ? '+' : '' }}{{ scope.row.quantityChange }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作后数量" prop="quantityAfter" width="100" align="center" />
        <el-table-column label="批次号" prop="batchNo" width="100" align="center" />
        <el-table-column label="操作人员" prop="operator" width="100" align="center" />
        <el-table-column label="操作时间" prop="operationTime" width="160" align="center" sortable="custom">
          <template slot-scope="scope">
            {{ parseTime(scope.row.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="viewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="tableTotal > 0"
        :total="tableTotal"
        :page.sync="tableQuery.pageNum"
        :limit.sync="tableQuery.pageSize"
        @pagination="getTableData"
      />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="操作记录详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <div class="detail-content" v-if="detailData">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ detailData.logId }}</el-descriptions-item>
          <el-descriptions-item label="区域编码">{{ detailData.zoneCode }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTag(detailData.operationType)">
              {{ getOperationTypeName(detailData.operationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人员">{{ detailData.operator }}</el-descriptions-item>
          <el-descriptions-item label="物料ID">{{ detailData.materialId }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ detailData.materialName }}</el-descriptions-item>
          <el-descriptions-item label="操作前数量">{{ detailData.quantityBefore }}</el-descriptions-item>
          <el-descriptions-item label="数量变化">
            <span :class="['quantity-change', detailData.quantityChange > 0 ? 'positive' : 'negative']">
              {{ detailData.quantityChange > 0 ? '+' : '' }}{{ detailData.quantityChange }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="操作后数量">{{ detailData.quantityAfter }}</el-descriptions-item>
          <el-descriptions-item label="批次号">{{ detailData.batchNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="来源单据">{{ detailData.sourceDocument || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ parseTime(detailData.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="操作原因" :span="2">{{ detailData.operationReason || '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getInventoryStatisticsOverview,
  getInventoryTrendAnalysis,
  getMaterialTypeDistribution,
  getWarehouseUtilization,
  getOperatorActivity,
  getAbnormalOperationStats,
  exportStatisticsReport
} from '@/api/inventory/statistics'
import { getOperationLogs } from '@/api/inventory/operation'

export default {
  name: 'WarehouseStatistics',
  data() {
    return {
      // 查询参数
      queryParams: {
        zoneCode: '',
        operationType: '',
        materialName: '',
        operator: ''
      },
      dateRange: [],
      
      // 统计概览数据
      overviewData: {
        totalInbound: 0,
        totalOutbound: 0,
        warehouseUtilization: 0,
        totalOperations: 0,
        inboundTrend: 0,
        outboundTrend: 0,
        utilizationTrend: 0,
        operationsTrend: 0
      },
      
      // 图表相关
      trendChart: null,
      materialChart: null,
      warehouseChart: null,
      operatorChart: null,
      abnormalChart: null,
      trendPeriod: '30d',
      
      // 加载状态
      trendLoading: false,
      materialLoading: false,
      warehouseLoading: false,
      operatorLoading: false,
      abnormalLoading: false,
      
      // 表格相关
      tableData: [],
      filteredTableData: [],
      tableLoading: false,
      tableTotal: 0,
      tableSearch: '',
      tableQuery: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: 'operationTime',
        isAsc: 'desc'
      },
      
      // 详情对话框
      detailVisible: false,
      detailData: null
    }
  },
  
  created() {
    this.initData()
  },
  
  mounted() {
    this.initCharts()
    this.$nextTick(() => {
      this.loadAllData()
    })
  },
  
  beforeDestroy() {
    this.destroyCharts()
  },
  
  methods: {
    // 初始化数据
    initData() {
      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      this.dateRange = [
        this.parseTime(sevenDaysAgo, '{y}-{m}-{d}'),
        this.parseTime(now, '{y}-{m}-{d}')
      ]
    },
    
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        if (this.$refs.trendChart) {
          this.trendChart = echarts.init(this.$refs.trendChart)
        }
        if (this.$refs.materialChart) {
          this.materialChart = echarts.init(this.$refs.materialChart)
        }
        if (this.$refs.warehouseChart) {
          this.warehouseChart = echarts.init(this.$refs.warehouseChart)
        }
        if (this.$refs.operatorChart) {
          this.operatorChart = echarts.init(this.$refs.operatorChart)
        }
        if (this.$refs.abnormalChart) {
          this.abnormalChart = echarts.init(this.$refs.abnormalChart)
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize)
      })
    },
    
    // 销毁图表
    destroyCharts() {
      window.removeEventListener('resize', this.handleResize)
      if (this.trendChart) {
        this.trendChart.dispose()
      }
      if (this.materialChart) {
        this.materialChart.dispose()
      }
      if (this.warehouseChart) {
        this.warehouseChart.dispose()
      }
      if (this.operatorChart) {
        this.operatorChart.dispose()
      }
      if (this.abnormalChart) {
        this.abnormalChart.dispose()
      }
    },
    
    // 窗口大小变化处理
    handleResize() {
      this.$nextTick(() => {
        if (this.trendChart) this.trendChart.resize()
        if (this.materialChart) this.materialChart.resize()
        if (this.warehouseChart) this.warehouseChart.resize()
        if (this.operatorChart) this.operatorChart.resize()
        if (this.abnormalChart) this.abnormalChart.resize()
      })
    },
    
    // 加载所有数据
    async loadAllData() {
      await Promise.all([
        this.loadOverviewData(),
        this.loadTrendData(),
        this.loadMaterialDistribution(),
        this.loadWarehouseUtilization(),
        this.loadOperatorActivity(),
        this.loadAbnormalStats(),
        this.getTableData()
      ])
    },
    
    // 加载概览数据
    async loadOverviewData() {
      try {
        const params = this.buildQueryParams()
        const response = await getInventoryStatisticsOverview(params)
        if (response.code === 200) {
          this.overviewData = response.data
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
        // 使用模拟数据
        this.overviewData = {
          totalInbound: 12580,
          totalOutbound: 9320,
          warehouseUtilization: 78.5,
          totalOperations: 3420,
          inboundTrend: 12.3,
          outboundTrend: -5.6,
          utilizationTrend: 8.2,
          operationsTrend: 15.7
        }
      }
    },
    
    // 加载趋势数据
    async loadTrendData() {
      this.trendLoading = true
      try {
        const params = { ...this.buildQueryParams(), period: this.trendPeriod }
        const response = await getInventoryTrendAnalysis(params)
        if (response.code === 200) {
          this.renderTrendChart(response.data)
        }
      } catch (error) {
        console.error('加载趋势数据失败:', error)
        // 使用模拟数据
        this.renderTrendChart(this.getMockTrendData())
      } finally {
        this.trendLoading = false
      }
    },
    
    // 加载物料分布数据
    async loadMaterialDistribution() {
      this.materialLoading = true
      try {
        const params = this.buildQueryParams()
        const response = await getMaterialTypeDistribution(params)
        if (response.code === 200) {
          this.renderMaterialChart(response.data)
        }
      } catch (error) {
        console.error('加载物料分布数据失败:', error)
        // 使用模拟数据
        this.renderMaterialChart(this.getMockMaterialData())
      } finally {
        this.materialLoading = false
      }
    },
    
    // 加载仓库利用率数据
    async loadWarehouseUtilization() {
      this.warehouseLoading = true
      try {
        const params = this.buildQueryParams()
        const response = await getWarehouseUtilization(params)
        if (response.code === 200) {
          this.renderWarehouseChart(response.data)
        }
      } catch (error) {
        console.error('加载仓库利用率数据失败:', error)
        // 使用模拟数据
        this.renderWarehouseChart(this.getMockWarehouseData())
      } finally {
        this.warehouseLoading = false
      }
    },
    
    // 加载操作员活跃度数据
    async loadOperatorActivity() {
      this.operatorLoading = true
      try {
        const params = this.buildQueryParams()
        const response = await getOperatorActivity(params)
        if (response.code === 200) {
          this.renderOperatorChart(response.data)
        }
      } catch (error) {
        console.error('加载操作员活跃度数据失败:', error)
        // 使用模拟数据
        this.renderOperatorChart(this.getMockOperatorData())
      } finally {
        this.operatorLoading = false
      }
    },
    
    // 加载异常统计数据
    async loadAbnormalStats() {
      this.abnormalLoading = true
      try {
        const params = this.buildQueryParams()
        const response = await getAbnormalOperationStats(params)
        if (response.code === 200) {
          this.renderAbnormalChart(response.data)
        }
      } catch (error) {
        console.error('加载异常统计数据失败:', error)
        // 使用模拟数据
        this.renderAbnormalChart(this.getMockAbnormalData())
      } finally {
        this.abnormalLoading = false
      }
    },
    
    // 获取表格数据
    async getTableData() {
      this.tableLoading = true
      try {
        const params = {
          ...this.buildQueryParams(),
          pageNum: this.tableQuery.pageNum,
          pageSize: this.tableQuery.pageSize,
          orderByColumn: this.tableQuery.orderByColumn,
          isAsc: this.tableQuery.isAsc
        }
        const response = await getOperationLogs(params)
        if (response.code === 200) {
          this.tableData = response.rows || []
          this.tableTotal = response.total || 0
          this.filteredTableData = this.tableData
        }
      } catch (error) {
        console.error('加载表格数据失败:', error)
        // 使用模拟数据
        this.tableData = this.getMockTableData()
        this.tableTotal = this.tableData.length
        this.filteredTableData = this.tableData
      } finally {
        this.tableLoading = false
      }
    },
    
    // 构建查询参数
    buildQueryParams() {
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.beginTime = this.dateRange[0] + ' 00:00:00'
        params.endTime = this.dateRange[1] + ' 23:59:59'
      }
      return params
    },
    
    // 渲染趋势图表
    renderTrendChart(data) {
      if (!this.trendChart) return
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['入库', '出库']
        },
        xAxis: {
          type: 'category',
          data: data.dates || ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '入库',
            type: 'line',
            data: data.inbound || [120, 132, 101, 134, 90, 230, 210],
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '出库',
            type: 'line',
            data: data.outbound || [80, 102, 91, 154, 70, 180, 160],
            smooth: true,
            itemStyle: {
              color: '#E6A23C'
            }
          }
        ]
      }
      this.trendChart.setOption(option)
    },
    
    // 渲染物料分布图表
    renderMaterialChart(data) {
      if (!this.materialChart) return
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center'
        },
        series: [
          {
            name: '物料类型',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data || [
              { value: 335, name: '原料' },
              { value: 310, name: '半成品' },
              { value: 234, name: '成品' },
              { value: 135, name: '零部件' },
              { value: 105, name: '其他' }
            ]
          }
        ]
      }
      this.materialChart.setOption(option)
    },
    
    // 渲染仓库利用率图表
    renderWarehouseChart(data) {
      if (!this.warehouseChart) return
      
      const option = {
        tooltip: {
          formatter: '{a} <br/>{b}: {c}%'
        },
        series: [
          {
            name: '仓库利用率',
            type: 'gauge',
            radius: '80%',
            data: [{ value: data?.utilization || 78.5, name: '利用率' }],
            detail: {
              formatter: '{value}%'
            },
            axisLine: {
              lineStyle: {
                color: [
                  [0.3, '#67C23A'],
                  [0.7, '#E6A23C'],
                  [1, '#F56C6C']
                ]
              }
            }
          }
        ]
      }
      this.warehouseChart.setOption(option)
    },
    
    // 渲染操作员活跃度图表
    renderOperatorChart(data) {
      if (!this.operatorChart) return
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: data?.operators || ['张三', '李四', '王五', '赵六', '钱七']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '操作次数',
            type: 'bar',
            data: data?.counts || [120, 102, 91, 84, 70],
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      this.operatorChart.setOption(option)
    },
    
    // 渲染异常统计图表
    renderAbnormalChart(data) {
      if (!this.abnormalChart) return
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '异常类型',
            type: 'pie',
            radius: '60%',
            data: data || [
              { value: 10, name: '数量异常' },
              { value: 5, name: '权限异常' },
              { value: 3, name: '时间异常' },
              { value: 2, name: '其他异常' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.abnormalChart.setOption(option)
    },
    
    // 查询
    handleQuery() {
      this.loadAllData()
    },
    
    // 重置
    resetQuery() {
      this.queryParams = {
        zoneCode: '',
        operationType: '',
        materialName: '',
        operator: ''
      }
      this.dateRange = []
      this.initData()
      this.loadAllData()
    },
    
    // 导出
    async handleExport() {
      try {
        const params = this.buildQueryParams()
        const response = await exportStatisticsReport(params)
        this.download(response, '仓库统计报表.xlsx')
      } catch (error) {
        this.$modal.msgError('导出失败')
      }
    },
    
    // 改变趋势周期
    changeTrendPeriod(period) {
      this.trendPeriod = period
      this.loadTrendData()
    },
    
    // 表格搜索
    handleTableSearch() {
      if (!this.tableSearch) {
        this.filteredTableData = this.tableData
      } else {
        this.filteredTableData = this.tableData.filter(item =>
          item.materialName?.includes(this.tableSearch) ||
          item.operator?.includes(this.tableSearch) ||
          item.zoneCode?.includes(this.tableSearch)
        )
      }
    },
    
    // 刷新表格
    refreshTable() {
      this.getTableData()
    },
    
    // 排序变化
    handleSortChange({ column, prop, order }) {
      this.tableQuery.orderByColumn = prop
      this.tableQuery.isAsc = order === 'ascending' ? 'asc' : 'desc'
      this.getTableData()
    },
    
    // 查看详情
    viewDetail(row) {
      this.detailData = row
      this.detailVisible = true
    },
    
    // 获取操作类型标签
    getOperationTypeTag(type) {
      const typeMap = {
        inbound: 'success',
        outbound: 'danger',
        transfer: 'info',
        adjust: 'warning',
        check: 'info'
      }
      return typeMap[type] || 'info'
    },
    
    // 获取操作类型名称
    getOperationTypeName(type) {
      const typeMap = {
        inbound: '入库',
        outbound: '出库',
        transfer: '移库',
        adjust: '调整',
        check: '盘点'
      }
      return typeMap[type] || '未知操作'
    },
    
    // 模拟数据方法
    getMockTrendData() {
      return {
        dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
        inbound: [120, 132, 101, 134, 90, 230, 210],
        outbound: [80, 102, 91, 154, 70, 180, 160]
      }
    },
    
    getMockMaterialData() {
      return [
        { value: 335, name: '原料' },
        { value: 310, name: '半成品' },
        { value: 234, name: '成品' },
        { value: 135, name: '零部件' },
        { value: 105, name: '其他' }
      ]
    },
    
    getMockWarehouseData() {
      return { utilization: 78.5 }
    },
    
    getMockOperatorData() {
      return {
        operators: ['张三', '李四', '王五', '赵六', '钱七'],
        counts: [120, 102, 91, 84, 70]
      }
    },
    
    getMockAbnormalData() {
      return [
        { value: 10, name: '数量异常' },
        { value: 5, name: '权限异常' },
        { value: 3, name: '时间异常' },
        { value: 2, name: '其他异常' }
      ]
    },
    
    getMockTableData() {
      return [
        {
          logId: 1001,
          zoneCode: 'A001',
          operationType: 'inbound',
          materialId: 'M001',
          materialName: '钢材原料',
          quantityBefore: 100,
          quantityChange: 50,
          quantityAfter: 150,
          batchNo: 'B20241201',
          sourceDocument: 'PO-001',
          operator: '张三',
          operationTime: new Date(),
          operationReason: '采购入库',
          remark: '正常入库操作'
        },
        {
          logId: 1002,
          zoneCode: 'A002',
          operationType: 'outbound',
          materialId: 'M002',
          materialName: '塑料制品',
          quantityBefore: 200,
          quantityChange: -30,
          quantityAfter: 170,
          batchNo: 'B20241202',
          sourceDocument: 'SO-001',
          operator: '李四',
          operationTime: new Date(),
          operationReason: '销售出库',
          remark: '正常出库操作'
        }
      ]
    }
  }
}
</script>

<style scoped>
.warehouse-statistics-container {
  padding: 20px;
  background-color: var(--base-body-background);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--current-color) 0%, var(--base-color-7) 100%);
  color: #ffffff;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(var(--current-color-rgb), 0.3);
  border: 1px solid var(--border-color-1);
}

.page-title {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  color: #ffffff;
}

.page-title i {
  margin-right: 12px;
  font-size: 32px;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  color: #ffffff;
}

/* 过滤卡片 */
.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(var(--current-color-rgb), 0.1);
  background-color: var(--base-item-bg);
  border-color: var(--border-color-1);
}

/* 概览行 */
.overview-row {
  margin-bottom: 20px;
}

.overview-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.overview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(var(--current-color-rgb), 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 20px;
  position: relative;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.inbound-card .card-icon {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.outbound-card .card-icon {
  background: linear-gradient(135deg, #E6A23C, #EEBC6E);
}

.utilization-card .card-icon {
  background: linear-gradient(135deg, var(--current-color), var(--base-color-7));
}

.activity-card .card-icon {
  background: linear-gradient(135deg, var(--base-color-6), var(--base-color-5));
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: var(--base-color-3);
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: var(--base-color-1);
  line-height: 1;
  margin-bottom: 8px;
}

.card-trend {
  font-size: 12px;
  color: #67C23A;
  display: flex;
  align-items: center;
}

.card-trend i {
  margin-right: 4px;
}

/* 图表行 */
.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(var(--current-color-rgb), 0.1);
  background-color: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  display: flex;
  align-items: center;
}

.chart-title i {
  margin-right: 8px;
  color: var(--current-color);
}

.chart-controls .el-button {
  padding: 5px 10px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 表格卡片 */
.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(var(--current-color-rgb), 0.1);
  background-color: var(--base-item-bg);
  border-color: var(--border-color-1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  display: flex;
  align-items: center;
}

.table-title i {
  margin-right: 8px;
  color: var(--current-color);
}

.table-controls {
  display: flex;
  align-items: center;
}

/* 表格内容 */
.material-name {
  font-weight: 600;
  color: var(--base-color-1);
}

.material-id {
  font-size: 12px;
  color: var(--base-color-3);
  margin-top: 2px;
}

.quantity-change {
  font-weight: 600;
  font-size: 14px;
}

.quantity-change.positive {
  color: #67C23A;
}

.quantity-change.negative {
  color: #F56C6C;
}

/* 详情内容 */
.detail-content {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-row .el-col {
    margin-bottom: 20px;
  }
  
  .charts-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .warehouse-statistics-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .card-content {
    flex-direction: column;
    text-align: center;
  }
  
  .card-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .chart-container {
    height: 250px;
  }
}

/* 动画效果 */
.overview-card,
.chart-card,
.table-card,
.filter-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--base-color-8);
}

::-webkit-scrollbar-thumb {
  background: var(--base-color-6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--base-color-5);
}

/* 三种主题特殊适配 */
/* 深色主题 */
.theme-dark {
  .warehouse-statistics-container {
    background-color: var(--base-body-background) !important;
  }
  
  .page-header {
    background: linear-gradient(135deg, var(--current-color) 0%, var(--color-4) 100%) !important;
    box-shadow: 0 4px 20px rgba(var(--current-color-rgb), 0.4) !important;
  }
  
  .filter-card,
  .overview-card,
  .chart-card,
  .table-card {
    background-color: var(--base-item-bg) !important;
    box-shadow: 0 2px 12px rgba(var(--current-color-rgb), 0.2) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .overview-card:hover {
    box-shadow: 0 8px 25px rgba(var(--current-color-rgb), 0.3) !important;
  }
  
  .utilization-card .card-icon {
    background: linear-gradient(135deg, var(--current-color), var(--color-2)) !important;
  }
  
  .activity-card .card-icon {
    background: linear-gradient(135deg, var(--base-color-6), var(--base-color-3)) !important;
  }
}

/* 星空主题 */
.theme-starry-sky {
  .warehouse-statistics-container {
    background-color: var(--base-body-background) !important;
  }
  
  .page-header {
    background: linear-gradient(135deg, var(--current-color) 0%, #0b0d1a 100%) !important;
    box-shadow: 0 4px 20px rgba(30, 58, 138, 0.5) !important;
  }
  
  .filter-card,
  .overview-card,
  .chart-card,
  .table-card {
    background-color: var(--base-item-bg) !important;
    box-shadow: 0 2px 12px rgba(30, 58, 138, 0.3) !important;
    border-color: var(--border-color-1) !important;
  }
  
  .overview-card:hover {
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.4) !important;
  }
  
  .utilization-card .card-icon {
    background: linear-gradient(135deg, var(--current-color), #1a1f3c) !important;
  }
  
  .activity-card .card-icon {
    background: linear-gradient(135deg, var(--current-color), var(--base-color-8)) !important;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--base-color-8) !important;
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--border-color-1) !important;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--current-color) !important;
  }
}

/* 浅色主题 */
.theme-light {
  .page-header {
    box-shadow: 0 4px 20px rgba(var(--current-color-rgb), 0.2) !important;
  }
  
  .filter-card,
  .overview-card,
  .chart-card,
  .table-card {
    box-shadow: 0 2px 12px rgba(var(--current-color-rgb), 0.08) !important;
  }
  
  .overview-card:hover {
    box-shadow: 0 8px 25px rgba(var(--current-color-rgb), 0.12) !important;
  }
}
</style>
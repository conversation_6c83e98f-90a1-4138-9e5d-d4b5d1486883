<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>出入库分析 - {{ selectedYear }}年{{ selectedMonth }}月统计</h2>
      <p class="description">本页面显示指定月份的出入库统计分析数据</p>
    </div>

    <!-- 搜索条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px" class="search-form">
      <el-form-item label="查询月份" prop="selectedDate">
        <el-date-picker
          v-model="selectedDate"
          type="month"
          placeholder="选择月份"
          format="yyyy年MM月"
          value-format="yyyy-MM"
          @change="handleDateChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input 
          v-model="queryParams.materialName" 
          placeholder="请输入物料名称" 
          clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="物料类型" prop="materialType">
        <el-select v-model="queryParams.materialType" placeholder="请选择物料类型" clearable>
          <el-option label="成品" value="成品"></el-option>
          <el-option label="半成品" value="半成品"></el-option>
          <el-option label="一级半成品" value="一级半成品"></el-option>
          <el-option label="二级半成品" value="二级半成品"></el-option>
          <el-option label="零部件" value="零部件"></el-option>
          <el-option label="原料" value="原料"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-refresh" size="mini" @click="getAnalysisData">刷新</el-button>
      </el-col>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card in-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-upload2"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">总入库数量</div>
              <div class="stat-value in-value">{{ totalInQuantity }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card out-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">总出库数量</div>
              <div class="stat-value out-value">{{ totalOutQuantity }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card type-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-menu"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">物料种类</div>
              <div class="stat-value type-value">{{ materialTypes.length }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card total-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">记录总数</div>
              <div class="stat-value total-value">{{ total }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据概览 -->
    <el-row :gutter="20" class="mb20" v-if="analysisData.length > 0">
      <el-col :span="24">
        <el-card class="overview-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="overview-title">
              <i class="el-icon-pie-chart"></i>
              数据概览
            </span>
          </div>
          <div class="overview-content">
            <div class="overview-item">
              <span class="overview-label">入库占比：</span>
              <el-progress 
                :percentage="getInPercentage()" 
                :color="'#67C23A'"
                :show-text="true"
                :format="() => `${getInPercentage()}% (${totalInQuantity}件)`">
              </el-progress>
            </div>
            <div class="overview-item">
              <span class="overview-label">出库占比：</span>
              <el-progress 
                :percentage="getOutPercentage()" 
                :color="'#E6A23C'"
                :show-text="true"
                :format="() => `${getOutPercentage()}% (${totalOutQuantity}件)`">
              </el-progress>
            </div>
            <div class="overview-item">
              <span class="overview-label">库存流转：</span>
              <el-tag :type="getFlowStatus().type" size="medium">
                {{ getFlowStatus().text }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="analysisData" border>
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料类型" align="center" prop="materialType">
        <template slot-scope="scope">
          <el-tag :type="getMaterialTypeTagType(scope.row.materialType)">
            {{ scope.row.materialType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="applicationType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.applicationType === '入库记录' ? 'success' : 'warning'">
            {{ scope.row.applicationType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总数量" align="center" prop="totalQuantity">
        <template slot-scope="scope">
          <span class="quantity">{{ scope.row.totalQuantity }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination 
      v-show="total > 0" 
      :total="total" 
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize" 
      @pagination="getAnalysisData" />

    <!-- 详细分析区域 -->
    <el-row :gutter="20" class="mt20" v-if="analysisData.length > 0">
      <el-col :span="12">
        <el-card class="analysis-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="analysis-title">
              <i class="el-icon-pie-chart"></i>
              物料类型分布
            </span>
          </div>
          <div class="material-type-list">
            <div 
              v-for="(type, index) in getMaterialTypeStats()" 
              :key="index"
              class="material-type-item">
              <div class="type-info">
                <el-tag :type="getMaterialTypeTagType(type.name)" size="small">
                  {{ type.name }}
                </el-tag>
                <span class="type-count">{{ type.count }}种</span>
              </div>
              <div class="type-progress">
                <el-progress 
                  :percentage="type.percentage" 
                  :show-text="false"
                  :stroke-width="8">
                </el-progress>
                <span class="percentage-text">{{ type.percentage }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="analysis-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="analysis-title">
              <i class="el-icon-s-data"></i>
              出入库明细统计
            </span>
          </div>
          <div class="inout-stats">
            <div class="stat-row">
              <span class="stat-label">入库记录数：</span>
              <span class="stat-number in-number">{{ getInRecordCount() }}条</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">出库记录数：</span>
              <span class="stat-number out-number">{{ getOutRecordCount() }}条</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">平均入库量：</span>
              <span class="stat-number">{{ getAvgInQuantity() }}件/次</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">平均出库量：</span>
              <span class="stat-number">{{ getAvgOutQuantity() }}件/次</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">最大单次入库：</span>
              <span class="stat-number highlight">{{ getMaxInQuantity() }}件</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">最大单次出库：</span>
              <span class="stat-number highlight">{{ getMaxOutQuantity() }}件</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getMonthlySummary, getCurrentMonthCensus } from "@/api/jenasi/inOutRequest";

export default {
  name: "InOutAnalysis",
  data() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 分析数据
      analysisData: [],
      // 统计数据
      totalInQuantity: 0,
      totalOutQuantity: 0,
      materialTypes: [],
      // 选择的年月
      selectedYear: 2025,
      selectedMonth: 6,
      selectedDate: `2025-06`, // 改为2025年6月，有数据
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialName: '',
        materialType: '',
      },
    };
  },
  created() {
    this.getAnalysisData();
  },

  methods: {
    /** 查询出入库分析数据 */
    getAnalysisData() {
      this.loading = true;
      const params = {
        year: this.selectedYear,
        month: this.selectedMonth,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        materialName: this.queryParams.materialName || '', // 总是传递materialName，空字符串也可以
        materialType: this.queryParams.materialType || ''  // 传递materialType，后端会根据是否为空决定是否过滤
      };
      
      getCurrentMonthCensus(params).then(response => {
        // 根据MyBatis Plus分页格式处理数据
        if (response.data && response.data.records) {
          this.analysisData = response.data.records;
          this.total = response.data.total;
        } else if (Array.isArray(response.data)) {
          // 兼容原格式
          this.analysisData = response.data;
          this.total = response.data.length;
        } else {
          this.analysisData = [];
          this.total = 0;
        }
        
        this.calculateStatistics();
        this.loading = false;
      }).catch((error) => {
        this.loading = false;
        this.$modal.msgError("获取出入库分析数据失败");
        console.error('Error fetching analysis data:', error);
        
        // 如果接口调用失败，使用模拟数据（仅开发环境）
        if (process.env.NODE_ENV === 'development') {
          const mockData = [
          {
            "materialName": "cs",
            "materialType": "成品",
            "applicationType": "出库记录",
            "totalQuantity": 10
          },
          {
            "materialName": "cs",
            "materialType": "二级半成品",
            "applicationType": "出库记录",
            "totalQuantity": 20
          },
          {
            "materialName": "cs",
            "materialType": "二级半成品",
            "applicationType": "入库记录",
            "totalQuantity": 20
          },
          {
            "materialName": "cs",
            "materialType": "零部件",
            "applicationType": "出库记录",
            "totalQuantity": 10
          },
          {
            "materialName": "cs",
            "materialType": "零部件",
            "applicationType": "入库记录",
            "totalQuantity": 20
          },
          {
            "materialName": "cs",
            "materialType": "原料",
            "applicationType": "出库记录",
            "totalQuantity": 30
          },
          {
            "materialName": "cs",
            "materialType": "原料",
            "applicationType": "入库记录",
            "totalQuantity": 60
          },
          {
            "materialName": "csgo",
            "materialType": "半成品",
            "applicationType": "入库记录",
            "totalQuantity": 4
          },
          {
            "materialName": "csgo",
            "materialType": "一级半成品",
            "applicationType": "出库记录",
            "totalQuantity": 20
          },
          {
            "materialName": "csgo",
            "materialType": "一级半成品",
            "applicationType": "入库记录",
            "totalQuantity": 30
          }
        ];

          this.analysisData = mockData;
          this.total = mockData.length;
          this.calculateStatistics();
          this.loading = false;
        }
      });
    },
    
    /** 计算统计数据 */
    calculateStatistics() {
      this.totalInQuantity = this.analysisData
        .filter(item => item.applicationType === '入库记录')
        .reduce((sum, item) => sum + item.totalQuantity, 0);
      
      this.totalOutQuantity = this.analysisData
        .filter(item => item.applicationType === '出库记录')
        .reduce((sum, item) => sum + item.totalQuantity, 0);
      
      this.materialTypes = [...new Set(this.analysisData.map(item => item.materialType))];
    },

    /** 获取物料类型标签颜色 */
    getMaterialTypeTagType(type) {
      const typeMap = {
        '成品': 'success',
        '半成品': 'warning',
        '一级半成品': 'info',
        '二级半成品': 'warning',
        '零部件': 'primary',
        '原料': 'danger'
      };
      return typeMap[type] || '';
    },

    /** 获取入库占比 */
    getInPercentage() {
      const total = this.totalInQuantity + this.totalOutQuantity;
      return total > 0 ? Math.round((this.totalInQuantity / total) * 100) : 0;
    },

    /** 获取出库占比 */
    getOutPercentage() {
      const total = this.totalInQuantity + this.totalOutQuantity;
      return total > 0 ? Math.round((this.totalOutQuantity / total) * 100) : 0;
    },

    /** 获取库存流转状态 */
    getFlowStatus() {
      if (this.totalInQuantity > this.totalOutQuantity) {
        return { type: 'success', text: '库存增长' };
      } else if (this.totalInQuantity < this.totalOutQuantity) {
        return { type: 'warning', text: '库存减少' };
      } else if (this.totalInQuantity === this.totalOutQuantity && this.totalInQuantity > 0) {
        return { type: 'info', text: '库存平衡' };
      } else {
        return { type: 'info', text: '暂无数据' };
      }
    },

    /** 获取物料类型统计 */
    getMaterialTypeStats() {
      const typeCount = {};
      this.analysisData.forEach(item => {
        if (typeCount[item.materialType]) {
          typeCount[item.materialType]++;
        } else {
          typeCount[item.materialType] = 1;
        }
      });

      const total = Object.values(typeCount).reduce((sum, count) => sum + count, 0);
      
      return Object.entries(typeCount).map(([name, count]) => ({
        name,
        count,
        percentage: total > 0 ? Math.round((count / total) * 100) : 0
      })).sort((a, b) => b.count - a.count);
    },

    /** 获取入库记录数 */
    getInRecordCount() {
      return this.analysisData.filter(item => item.applicationType === '入库记录').length;
    },

    /** 获取出库记录数 */
    getOutRecordCount() {
      return this.analysisData.filter(item => item.applicationType === '出库记录').length;
    },

    /** 获取平均入库量 */
    getAvgInQuantity() {
      const inRecords = this.analysisData.filter(item => item.applicationType === '入库记录');
      if (inRecords.length === 0) return 0;
      const total = inRecords.reduce((sum, item) => sum + item.totalQuantity, 0);
      return Math.round(total / inRecords.length);
    },

    /** 获取平均出库量 */
    getAvgOutQuantity() {
      const outRecords = this.analysisData.filter(item => item.applicationType === '出库记录');
      if (outRecords.length === 0) return 0;
      const total = outRecords.reduce((sum, item) => sum + item.totalQuantity, 0);
      return Math.round(total / outRecords.length);
    },

    /** 获取最大单次入库量 */
    getMaxInQuantity() {
      const inRecords = this.analysisData.filter(item => item.applicationType === '入库记录');
      if (inRecords.length === 0) return 0;
      return Math.max(...inRecords.map(item => item.totalQuantity));
    },

    /** 获取最大单次出库量 */
    getMaxOutQuantity() {
      const outRecords = this.analysisData.filter(item => item.applicationType === '出库记录');
      if (outRecords.length === 0) return 0;
      return Math.max(...outRecords.map(item => item.totalQuantity));
    },

    /** 日期选择器变化事件 */
    handleDateChange(value) {
      if (value) {
        const [year, month] = value.split('-');
        this.selectedYear = parseInt(year);
        this.selectedMonth = parseInt(month);
        this.queryParams.pageNum = 1; // 重置页码
        this.getAnalysisData();
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getAnalysisData();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        materialName: '',
        materialType: '',
      };
      this.getAnalysisData();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('wms/inOutRequest/export', {}, `出入库分析_${this.selectedYear}年${this.selectedMonth}月_${new Date().getTime()}.xlsx`)
    },


  }
};
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 搜索表单样式 */
.search-form {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* 统计卡片样式 */
.stat-card {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.in-card .stat-icon {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.out-card .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #EEBC6E);
}

.type-card .stat-icon {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.total-card .stat-icon {
  background: linear-gradient(135deg, #909399, #B1B3B8);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
}

.in-value {
  color: #67C23A;
}

.out-value {
  color: #E6A23C;
}

.type-value {
  color: #409EFF;
}

.total-value {
  color: #909399;
}

/* 概览卡片样式 */
.overview-card {
  border-radius: 8px;
}

.overview-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.overview-title i {
  margin-right: 8px;
  color: #409EFF;
}

.overview-content {
  padding: 10px 0;
}

.overview-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.overview-item:last-child {
  margin-bottom: 0;
}

.overview-label {
  width: 100px;
  font-size: 14px;
  color: #606266;
  margin-right: 20px;
}

.overview-item .el-progress {
  flex: 1;
  margin-right: 20px;
}

/* 表格样式优化 */
.quantity {
  font-weight: bold;
  color: #67C23A;
  font-size: 16px;
}

/* 分析卡片样式 */
.analysis-card {
  border-radius: 8px;
}

.analysis-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.analysis-title i {
  margin-right: 8px;
  color: #409EFF;
}

/* 物料类型分布样式 */
.material-type-list {
  padding: 10px 0;
}

.material-type-item {
  margin-bottom: 20px;
}

.material-type-item:last-child {
  margin-bottom: 0;
}

.type-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.type-count {
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.type-progress {
  display: flex;
  align-items: center;
}

.type-progress .el-progress {
  flex: 1;
  margin-right: 10px;
}

.percentage-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
  text-align: right;
}

/* 出入库统计样式 */
.inout-stats {
  padding: 10px 0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-number {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.in-number {
  color: #67C23A;
}

.out-number {
  color: #E6A23C;
}

.highlight {
  color: #409EFF;
  font-size: 18px;
}

/* 工具类 */
.mt20 {
  margin-top: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>

<template>
  <div class="login-bg" :style="bgStyle">
    <transition name="fade-bg">
      <div v-if="showSecondBg" class="login-bg second-bg" :style="secondBgStyle"></div>
    </transition>
    <canvas id="starry-sky-canvas"></canvas>
    <button class="iframe-toggle-btn earth-svg-btn" @click="toggleIframe" :title="showIframe ? '隐藏太阳系动画' : '显示太阳系动画'">
      <svg-icon icon-class="earth" class="earth-svg-icon" />
    </button>
    <div v-if="showIframe" class="planet-iframe-absolute">
      <iframe src="https://www.solarsystemscope.com/iframe" class="planet-iframe-centered" allowfullscreen></iframe>
    </div>
    <div class="login-flex-right">
      <div class="login" :class="{ 'compact-mode': showIframe }">
        <div class="top-logo">
          <a href="https://jenasi.cn//" target="_blank"><img src="../assets/logo/logo-heng.png" style="height: 35px;"></a>
          <div style="margin: 2px 5px;">|</div>
          <div style="font-weight: 700; padding-bottom: 2px; cursor:pointer;" @click="switchBg">{{ sysTitle }}</div>
        </div>
        <!-- <div class="left-pic">
          <img src="../assets/images/hero-img.png" style="height: 450px;">
        </div> -->

        <!-- 紧凑模式：动画显示时的标签页切换登录 -->
        <div v-if="showIframe" class="compact-login-container">
          <!-- 登录方式切换标签 -->
          <div class="compact-login-tabs">
            <div class="compact-tab-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
              <i class="el-icon-key"></i> 密码登录
            </div>
            <div class="compact-tab-item" :class="{ active: loginType === 'qrcode' }" @click="switchLoginType('qrcode')">
              <i class="el-icon-mobile-phone"></i> 扫码登录
            </div>
          </div>

          <!-- 密码登录表单 -->
          <div v-if="loginType === 'password'" class="compact-password-form">
            <h3 class="compact-title">密码登录</h3>
            <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="compact-form">
              <el-form-item prop="username">
                <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号" class="unchanged login-input">
                  <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
                  @keyup.enter.native="handleLogin" show-password  class="unchanged login-input">
                  <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
                </el-input>
              </el-form-item>
              <el-form-item prop="code" v-if="captchaEnabled">
                <div class="captcha-group">
                  <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" class="unchanged login-input" style="width: 63%"
                    @keyup.enter.native="handleLogin">
                    <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
                  </el-input>
                  <div class="login-code">
                    <img :src="codeUrl" @click="getCode" class="login-code-img" />
                  </div>
                </div>
              </el-form-item>
              <div class="select-click">
                <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 15px 0px;" class="unchanged">记住密码</el-checkbox>
              </div>
              <el-form-item style="width:100%;">
                <el-button :loading="loading" size="medium" type="primary" style="width:100%;" class="btn-fixed login-button"
                  @click.native.prevent="handleLogin">
                  <span v-if="!loading">登 录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 扫码登录组件 -->
          <div v-if="loginType === 'qrcode'" class="compact-qrcode-form">
            <h3 class="compact-title">扫码登录</h3>
            <QrCodeLogin @login-success="handleQrLoginSuccess" />
          </div>
        </div>

        <!-- 常规模式：统一登录容器 -->
        <div v-else class="unified-login-container">
          <!-- 登录标题 -->
          <div class="login-header">
            <h2>欢迎登录</h2>
            <p>请选择您的登录方式</p>
          </div>

          <!-- 双栏登录布局 -->
          <div class="dual-login-layout">
            <!-- 扫码登录区域 -->
            <div class="qrcode-section">
              <div class="section-header">
                <i class="el-icon-mobile-phone"></i>
                <h3>扫码登录</h3>
              </div>
              <div class="qrcode-content">
                <QrCodeLogin @login-success="handleQrLoginSuccess" />
              </div>
            </div>

            <!-- 分隔线 -->
            <div class="login-divider">
              <div class="divider-line"></div>
              <div class="divider-text">或</div>
              <div class="divider-line"></div>
            </div>

            <!-- 密码登录区域 -->
            <div class="password-section">
              <div class="section-header">
                <i class="el-icon-key"></i>
                <h3>密码登录</h3>
              </div>
              <div class="password-content">
                <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
                  <el-form-item prop="username">
                    <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号" class="unchanged login-input">
                      <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="password">
                    <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
                      @keyup.enter.native="handleLogin" show-password  class="unchanged login-input">
                      <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="code" v-if="captchaEnabled">
                    <div class="captcha-group">
                      <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" class="unchanged login-input" style="width: 63%"
                        @keyup.enter.native="handleLogin">
                        <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
                      </el-input>
                      <div class="login-code">
                        <img :src="codeUrl" @click="getCode" class="login-code-img" />
                      </div>
                    </div>
                  </el-form-item>
                  <div class="select-click">
                    <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;" class="unchanged">记住密码</el-checkbox>
                  </div>
                  <el-form-item style="width:100%;">
                    <el-button :loading="loading" size="medium" type="primary" style="width:100%;" class="btn-fixed login-button"
                      @click.native.prevent="handleLogin">
                      <span v-if="!loading">登 录</span>
                      <span v-else>登 录 中...</span>
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>

        <!--  底部  -->
        <div class="el-login-footer copyright-center">
          <a href="https://jenasi.cn/" style="font-weight: 700;" target="_blank" >湖南简思科技有限公司 © 版权所有</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg , tenantList} from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import ThreePlanet from './ThreePlanet.vue';
import QrCodeLogin from '@/components/QrCodeLogin/index.vue';

export default {
  name: "Login",
  components: { ThreePlanet, QrCodeLogin },
  data() {
    return {
      loginType: 'password', // 登录方式: password-密码登录, qrcode-扫码登录
      codeUrl: "",
      loginForm: {
        tenantId: "000000",
        username: undefined,
        password: undefined,
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        /* tenantId: [
          { required: true, trigger: "blur", message: "请输入您的租户编号" }
        ], */
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "blur", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      tenantEnabled: false,
      // 注册开关
      register: false,
      redirect: undefined,
      // 租户列表
      tenantList: [],
      bgImages: [require('../assets/images/银河.jpg'), require('../assets/images/星空.jpg')],
      currentBgIndex: 0,
      showSecondBg: false,
      showIframe: false,
      // 新增：粒子交互和连线相关
      starrySkyMouse: { x: window.innerWidth / 2, y: window.innerHeight / 2 },
      starrySkyStars: [],
      starrySkyNumStars: 220,
      starrySkyCanvas: null,
      starrySkyCtx: null,
      starrySkyAnimId: null,
      connectionThreshold: 120, // 星星连线距离阈值
      trailPoints: [], // 拖尾点
      glowParticles: [], // 微光粒子数组
      glowParticleNum: 40, // 微光粒子数量
      meteors: [], // 流星数组
      lastMeteorTime: 0, // 上次流星生成时间
      starFieldOffset: { x: 0, y: 0 }, // 星空整体偏移
      starFieldAngle: 0, // 星空整体旋转角度
    };
  },
  computed: {
    sysTitle: function(){
      return this.$store.getters.logoInfo.sysTitle
      // return process.env.VUE_APP_TITLE
    },
    logoShow() {
      return this.$store.getters.logoInfo.loginLogo
    },
    experienceShow() {
      return this.$store.getters.logoInfo.experienceShow
    },
    bgStyle() {
      // 当显示太阳系动画时，默认使用银河.jpg背景（索引0）
      const bgIndex = this.showIframe ? 0 : this.currentBgIndex;
      return {
        background: `url(${this.bgImages[bgIndex]}) center center/cover no-repeat`,
        transition: 'background 1s',
      };
    },
    secondBgStyle() {
      const bgIndex = this.showIframe ? 0 : this.currentBgIndex;
      return {
        background: `url(${this.bgImages[(bgIndex+1)%2]}) center center/cover no-repeat`,
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 0,
        opacity: 1,
        pointerEvents: 'none',
      };
    }
  },
  
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getTenantList();
    this.getCookie();
  },
  mounted() {
    this.initStarrySky();
    window.addEventListener('resize', this.resizeStarrySky);
    document.addEventListener('mousemove', this.handleMouseMove);
    this.initGlowParticles();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeStarrySky);
    document.removeEventListener('mousemove', this.handleMouseMove);
    cancelAnimationFrame(this.starrySkyAnimId);
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.data.img;
          this.loginForm.uuid = res.data.uuid;
        }
      });
    },
    getTenantList() {
      tenantList().then(res => {
        this.tenantList = res.data.voList;
        this.tenantEnabled = res.data.tenantEnabled;
      });
    },
    getCookie() {
      const tenantId = Cookies.get("tenantId");
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        tenantId: tenantId === undefined ? this.loginForm.tenantId : tenantId,
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("tenantId", this.loginForm.tenantId, { expires: 30 });
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("tenantId");
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/user/profile" }).catch(() => { });
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    },

    // 扫码登录成功回调
    handleQrLoginSuccess() {
      this.$router.push({ path: this.redirect || "/user/profile" }).catch(() => { });
    },
    // 星空canvas相关
    initStarrySky() {
      const canvas = document.getElementById('starry-sky-canvas');
      if (!canvas) return;
      this.starrySkyCanvas = canvas;
      this.starrySkyCtx = canvas.getContext('2d');
      this.starrySkyMouse = { x: window.innerWidth / 2, y: window.innerHeight / 2 };
      this.starrySkyStars = [];
      this.starrySkyNumStars = 220;
      this.resizeStarrySky();
      this.initStars();
      this.animateStarrySky();
    },
    resizeStarrySky() {
      if (!this.starrySkyCanvas) return;
      this.starrySkyCanvas.width = window.innerWidth;
      this.starrySkyCanvas.height = window.innerHeight;
      this.starrySkyStars = [];
      this.initStars();
    },
    handleMouseMove(e) {
      if (!this.starrySkyMouse) return;
      this.starrySkyMouse.x = e.clientX;
      this.starrySkyMouse.y = e.clientY;
      // 拖尾点
      this.trailPoints.push({ x: e.clientX, y: e.clientY });
      if (this.trailPoints.length > 30) this.trailPoints.shift();
    },
    initStars() {
      if (!this.starrySkyCanvas) return;
      this.starrySkyStars = [];
      for (let i = 0; i < this.starrySkyNumStars; i++) {
        this.starrySkyStars.push({
          originX: Math.random() * this.starrySkyCanvas.width,
          originY: Math.random() * this.starrySkyCanvas.height,
          x: 0, // 实时坐标
          y: 0,
          size: Math.random() * 1.5 + 0.3,
          baseSize: 0, // 记录初始大小
          brightness: Math.random() * 0.6 + 0.2,
          color: '',
          depth: Math.random() * 0.6 + 0.1,
          opacityDirection: Math.random() > 0.5 ? 1 : -1,
          twinkleSpeed: Math.random() * 0.015 + 0.002,
          vx: (Math.random() - 0.5) * 0.1, // 粒子速度
          vy: (Math.random() - 0.5) * 0.1
        });
      }
      // 记录初始大小
      this.starrySkyStars.forEach(star => { star.baseSize = star.size; });
    },
    initGlowParticles() {
      this.glowParticles = [];
      const w = window.innerWidth;
      const h = window.innerHeight;
      for (let i = 0; i < this.glowParticleNum; i++) {
        this.glowParticles.push({
          x: Math.random() * w,
          y: Math.random() * h,
          r: 1.5 + Math.random() * 2.5,
          baseR: 1.5 + Math.random() * 2.5,
          alpha: 0.25 + Math.random() * 0.25,
          speedX: (Math.random() - 0.5) * 0.08,
          speedY: (Math.random() - 0.5) * 0.08,
          phase: Math.random() * Math.PI * 2
        });
      }
    },
    createMeteor() {
      const w = this.starrySkyCanvas.width;
      const h = this.starrySkyCanvas.height;
      // 流星从顶部随机位置斜向划过
      const startX = Math.random() * w * 0.8 + w * 0.1;
      const startY = Math.random() * h * 0.2;
      const angle = Math.PI / 2.5 + (Math.random() - 0.5) * 0.2; // 约70度
      const speed = 8 + Math.random() * 4;
      const length = 180 + Math.random() * 80;
      return {
        x: startX,
        y: startY,
        angle,
        speed,
        length,
        alpha: 1,
        life: 0
      };
    },
    animateStarrySky() {
      if (!this.starrySkyCanvas) return;
      const ctx = this.starrySkyCtx;
      const stars = this.starrySkyStars;
      const mouse = this.starrySkyMouse;
      ctx.clearRect(0, 0, this.starrySkyCanvas.width, this.starrySkyCanvas.height);
      // 微光粒子悬浮
      for (let i = 0; i < this.glowParticles.length; i++) {
        const p = this.glowParticles[i];
        // 缓慢漂浮
        p.x += p.speedX;
        p.y += p.speedY;
        // 边界回弹
        if (p.x < 0 || p.x > this.starrySkyCanvas.width) p.speedX *= -1;
        if (p.y < 0 || p.y > this.starrySkyCanvas.height) p.speedY *= -1;
        // 半径呼吸变化
        p.r = p.baseR + Math.sin(Date.now() / 1200 + p.phase) * 0.7;
        // 绘制发光粒子
        ctx.save();
        ctx.globalAlpha = p.alpha + 0.15 * Math.abs(Math.sin(Date.now() / 1500 + p.phase));
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(180,200,255,0.7)';
        ctx.shadowColor = '#bcdcff';
        ctx.shadowBlur = 16;
        ctx.fill();
        ctx.restore();
      }
      // 拖尾粒子
      for (let i = 0; i < this.trailPoints.length; i++) {
        const p = this.trailPoints[i];
        ctx.save();
        ctx.globalAlpha = (i + 1) / this.trailPoints.length * 0.5;
        ctx.beginPath();
        ctx.arc(p.x, p.y, 8 * (i + 1) / this.trailPoints.length, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.shadowColor = '#fff';
        ctx.shadowBlur = 12;
        ctx.fill();
        ctx.restore();
      }
      // 星空流动整体偏移和旋转
      this.starFieldOffset.x += 0.04 * Math.cos(Date.now() / 8000);
      this.starFieldOffset.y += 0.02 * Math.sin(Date.now() / 12000);
      this.starFieldAngle += 0.00012; // 缓慢旋转
      // 粒子交互：星星受鼠标吸引/高亮
      stars.forEach(star => {
        // 3D视差
        const mouseOffsetX = (mouse.x - window.innerWidth / 2) * star.depth * 0.03;
        const mouseOffsetY = (mouse.y - window.innerHeight / 2) * star.depth * 0.03;
        // 星空流动整体平移和旋转
        let ox = star.originX + this.starFieldOffset.x;
        let oy = star.originY + this.starFieldOffset.y;
        // 以屏幕中心为旋转中心
        const cx = this.starrySkyCanvas.width / 2;
        const cy = this.starrySkyCanvas.height / 2;
        const dx = ox - cx;
        const dy = oy - cy;
        const cosA = Math.cos(this.starFieldAngle);
        const sinA = Math.sin(this.starFieldAngle);
        const rx = dx * cosA - dy * sinA;
        const ry = dx * sinA + dy * cosA;
        ox = cx + rx;
        oy = cy + ry;
        star.x = ox + mouseOffsetX;
        star.y = oy + mouseOffsetY;
        // 粒子交互：靠近鼠标时变大/高亮/受吸引
        const dx2 = star.x - mouse.x;
        const dy2 = star.y - mouse.y;
        const dist = Math.sqrt(dx2 * dx2 + dy2 * dy2);
        if (dist < 80) {
          // 受吸引，向鼠标靠近一点
          star.x -= dx2 * 0.03;
          star.y -= dy2 * 0.03;
          // 变大变亮
          star.size = star.baseSize + 1.2 * (1 - dist / 80);
          star.color = `rgba(255,255,200,${Math.min(1, star.brightness + 0.4)})`;
        } else {
          // 恢复原状
          star.size = star.baseSize;
          star.color = `rgba(220,220,255,${star.brightness})`;
        }
        // twinkle
        star.brightness += star.twinkleSpeed * star.opacityDirection;
        if (star.brightness > 0.8 || star.brightness < 0.1) {
          star.opacityDirection *= -1;
          star.brightness = Math.max(0.1, Math.min(0.8, star.brightness));
        }
        // 绘制星星
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2, false);
        ctx.fillStyle = star.color;
        ctx.shadowColor = star.color;
        ctx.shadowBlur = star.size * 2;
        ctx.fill();
        ctx.shadowBlur = 0;
      });
      this.drawConnections(ctx, stars);
      // 流星掉落
      const now = Date.now();
      if (now - this.lastMeteorTime > 1200 + Math.random() * 1200) {
        this.meteors.push(this.createMeteor());
        this.lastMeteorTime = now;
      }
      for (let i = this.meteors.length - 1; i >= 0; i--) {
        const m = this.meteors[i];
        // 拖尾
        for (let t = 0; t < 8; t++) {
          const tailX = m.x - Math.cos(m.angle) * (m.length * t / 8);
          const tailY = m.y - Math.sin(m.angle) * (m.length * t / 8);
          ctx.save();
          ctx.globalAlpha = m.alpha * (1 - t / 8) * 0.7;
          ctx.beginPath();
          ctx.arc(tailX, tailY, 2.2 + 2 * (1 - t / 8), 0, Math.PI * 2);
          ctx.fillStyle = 'rgba(255,255,255,0.85)';
          ctx.shadowColor = '#fff';
          ctx.shadowBlur = 16;
          ctx.fill();
          ctx.restore();
        }
        // 主体
        ctx.save();
        ctx.globalAlpha = m.alpha;
        ctx.strokeStyle = 'rgba(255,255,255,0.85)';
        ctx.lineWidth = 2.2;
        ctx.beginPath();
        ctx.moveTo(m.x, m.y);
        ctx.lineTo(m.x - Math.cos(m.angle) * m.length, m.y - Math.sin(m.angle) * m.length);
        ctx.stroke();
        ctx.restore();
        // 更新流星位置
        m.x += Math.cos(m.angle) * m.speed;
        m.y += Math.sin(m.angle) * m.speed;
        m.alpha -= 0.012;
        m.life += 1;
        // 超出范围或透明度为0则移除
        if (m.x > this.starrySkyCanvas.width + 100 || m.y > this.starrySkyCanvas.height + 100 || m.alpha <= 0 || m.life > 120) {
          this.meteors.splice(i, 1);
        }
      }
      this.starrySkyAnimId = requestAnimationFrame(this.animateStarrySky);
    },
    drawConnections(ctx, stars) {
      for (let i = 0; i < stars.length; i++) {
        for (let j = i + 1; j < stars.length; j++) {
          const dx = stars[i].x - stars[j].x;
          const dy = stars[i].y - stars[j].y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < this.connectionThreshold) {
            ctx.save();
            ctx.globalAlpha = 0.18 * (1 - dist / this.connectionThreshold);
            ctx.strokeStyle = '#BFD6FF';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(stars[i].x, stars[i].y);
            ctx.lineTo(stars[j].x, stars[j].y);
            ctx.stroke();
            ctx.restore();
          }
        }
      }
    },
    switchBg() {
      this.showSecondBg = true;
      setTimeout(() => {
        this.currentBgIndex = (this.currentBgIndex + 1) % 2;
        this.showSecondBg = false;
      }, 1000);
    },
    switchLoginType(type) {
      this.loginType = type;
    },
    // 切换太阳系动画显示状态
    toggleIframe() {
      this.showIframe = !this.showIframe;
      // 当显示太阳系动画时，确保背景为银河.jpg（索引0）
      if (this.showIframe) {
        this.currentBgIndex = 0;
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.login-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  min-width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0; left: 0;
  z-index: 0;
  background: var(--base-body-background) center center/cover no-repeat;
}
.second-bg {
  position: fixed;
  top: 0; left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  opacity: 1;
  pointer-events: none;
}
.fade-bg-enter-active, .fade-bg-leave-active {
  transition: opacity 1s;
}
.fade-bg-enter, .fade-bg-leave-to {
  opacity: 0;
}
#starry-sky-canvas {
  position: fixed;
  top: 0; left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  pointer-events: none;
}
.planet-iframe-absolute {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 60vw;
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  background: transparent;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
}
.planet-iframe-centered {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 16px;
  background: transparent;
  display: block;
}
.login-flex-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 100vw;
  height: 100vh;
  z-index: 4;
  padding-right: 3vw;
}
.login {
  position: relative;
  z-index: 2;
  width: 90vw;
  max-width: 900px;
  min-width: 320px;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 统一登录容器样式 */
.unified-login-container {
  background: rgba(20, 25, 40, 0.18);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.37);
  border: 1px solid rgba(255,255,255,0.12);
  color: var(--base-color-1);
  width: 100%;
  min-width: 320px;
  padding: 30px;
  margin-top: 30px;
  transition: box-shadow 0.3s cubic-bezier(.25,.8,.25,1), transform 0.3s cubic-bezier(.25,.8,.25,1);
}

.unified-login-container:hover {
  box-shadow: 0 12px 40px 0 rgba(0,0,0,0.45);
  transform: translateY(-2px) scale(1.01);
}

/* 登录标题 */
.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--base-color-1);
  margin: 0 0 8px 0;
  letter-spacing: 2px;
}

.login-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  letter-spacing: 1px;
}

/* 双栏布局 */
.dual-login-layout {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  justify-content: center;
}

/* 扫码登录区域 */
.qrcode-section {
  flex: 1;
  min-width: 240px;
  max-width: 350px;
}

/* 密码登录区域 */
.password-section {
  flex: 1;
  min-width: 260px;
  max-width: 380px;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header i {
  font-size: 20px;
  color: var(--current-color);
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--base-color-1);
  margin: 0;
  letter-spacing: 1px;
}

/* 分隔线 */
.login-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  margin: 60px 0 40px 0;
}

.divider-line {
  width: 1px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.divider-text {
  margin: 15px 0;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
}

/* 内容区域 */
.qrcode-content, .password-content {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .unified-login-container {
    padding: 25px;
    margin: 20px;
  }
  
  .dual-login-layout {
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }
  
  .login-divider {
    flex-direction: row;
    margin: 15px 0;
    min-width: auto;
    width: 100%;
    max-width: 200px;
  }
  
  .divider-line {
    width: 60px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  }
  
  .qrcode-section, .password-section {
    min-width: auto;
    max-width: none;
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .unified-login-container {
    padding: 20px;
    margin: 15px;
  }
  
  .login-header h2 {
    font-size: 24px;
  }
  
  .login-header p {
    font-size: 14px;
  }
}

/* 主题适配 */
.theme-dark .unified-login-container {
  background: var(--base-menu-background, rgba(20, 25, 40, 0.18));
  color: var(--theme-color, #ffffff);
}

.theme-dark .login-header h2 {
  color: var(--theme-color, #ffffff);
}

.theme-dark .login-header p {
  color: var(--theme-color, rgba(255, 255, 255, 0.7));
}

.theme-dark .section-header h3 {
  color: var(--theme-color, #ffffff);
}

.theme-dark .section-header i {
  color: var(--current-color, #409eff);
}
.login-form {
  width: 100%;
  color: var(--base-color-1);
}
.login-input {
  background: rgba(11, 14, 24, 0.6) !important;
  border: 1px solid rgba(255,255,255,0.1) !important;
  border-radius: 6px !important;
  color: var(--base-color-1) !important;
  input {
    background: transparent !important;
    color: var(--base-color-1) !important;
    font-size: 15px;
    padding: 12px 5px;
  }
  ::placeholder {
    color: #788099 !important;
  }
}
.input-icon {
  font-size: 18px;
  color: var(--icon-color) !important;
  margin-right: 10px;
  width: 18px;
  text-align: center;
}
.captcha-group {
  display: flex;
  align-items: center;
}
.login-code {
  margin-left: 10px;
  display: flex;
  align-items: center;
  .login-code-img {
    height: 38px;
    cursor: pointer;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  }
}
.login-button {
  width: 100%;
  padding: 11px 0;
  background: linear-gradient(90deg, var(--current-color) 0%, var(--base-color-7) 100%);
  border: none;
  border-radius: 6px;
  color: var(--base-color-1);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, transform 0.1s;
  margin-top: 15px;
  box-shadow: 0 4px 12px rgba(0,122,255,0.25);
  &:hover {
    background: linear-gradient(90deg, var(--base-color-7) 0%, var(--current-color) 100%);
    transform: translateY(-1px) scale(1.01);
  }
  &:active {
    background: var(--current-color);
    transform: translateY(1px);
  }
}
.title {
  text-align: center;
  font-size: 22px;
  color: var(--base-color-1);
  letter-spacing: 5px;
  font-weight: 600;
  margin-bottom: 30px;
}
.select-click {
  display: flex;
  justify-content: space-between;
}
.top-logo {
  display: flex;
  position: fixed;
  left: 100px;
  top: 50px;
  font-size: 30px;
  letter-spacing: 1px;
  margin-bottom: 20px;
  padding: 0;
  color: var(--base-color-1);
  z-index: 10;
}
.left-pic {
  padding-top: 70px;
}
.el-login-footer.copyright-center {
  position: fixed;
  left: 0;
  bottom: 30px;
  width: 100vw;
  text-align: center;
  color: var(--base-color-1);
  font-size: 12px;
  letter-spacing: 1px;
  z-index: 100;
  background: transparent;
}
.iframe-toggle-btn.earth-svg-btn {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  box-shadow: none;
  border: none;
  position: fixed;
  top: 24px;
  right: 32px;
  z-index: 1000;
  cursor: pointer;
  transition: box-shadow 0.2s, border 0.2s, background 0.2s;
  overflow: hidden;
}
.earth-svg-icon {
  width: 44px;
  height: 44px;
  display: block;
  border-radius: 50%;
  object-fit: cover;
  background: transparent;
  box-shadow: none;
}

/* 紧凑模式样式 */
.login.compact-mode {
  width: 420px;
  max-width: 420px;
  min-width: 320px;
  position: relative;
  z-index: 10;
}

.compact-login-container {
  background: rgba(20, 25, 40, 0.18);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.37);
  border: 1px solid rgba(255,255,255,0.12);
  color: var(--base-color-1);
  width: 100%;
  padding: 25px;
  margin-top: 30px;
  transition: box-shadow 0.3s cubic-bezier(.25,.8,.25,1), transform 0.3s cubic-bezier(.25,.8,.25,1);
}

.compact-login-container:hover {
  box-shadow: 0 12px 40px 0 rgba(0,0,0,0.45);
  transform: translateY(-2px) scale(1.02);
}

.compact-login-tabs {
  display: flex;
  background: rgba(20, 25, 40, 0.18);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 20px;
  border: 1px solid rgba(255,255,255,0.12);
}

.compact-tab-item {
  flex: 1;
  padding: 12px 20px;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.compact-tab-item:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
}

.compact-tab-item.active {
  background: linear-gradient(90deg, var(--current-color) 0%, var(--base-color-7) 100%);
  color: var(--base-color-1);
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0,122,255,0.25);
}

.compact-tab-item i {
  font-size: 16px;
}

.compact-password-form, .compact-qrcode-form {
  text-align: center;
}

.compact-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--base-color-1);
  margin: 0 0 20px 0;
  letter-spacing: 1px;
}

.compact-form {
  width: 100%;
  color: var(--base-color-1);
}

/* 紧凑模式主题适配 */
.theme-dark .compact-login-container {
  background: var(--base-menu-background, rgba(20, 25, 40, 0.18));
  color: var(--theme-color, #ffffff);
}

.theme-dark .compact-login-tabs {
  background: var(--base-menu-background, rgba(20, 25, 40, 0.18));
}

.theme-dark .compact-tab-item {
  color: var(--theme-color, rgba(255, 255, 255, 0.6));
}

.theme-dark .compact-tab-item.active {
  background: linear-gradient(90deg, var(--theme-color) 0%, var(--current-color) 100%);
  color: var(--base-color-1, #ffffff);
}

.theme-dark .compact-title {
  color: var(--theme-color, #ffffff);
}
</style>
<style scoped>
.default-style>>>.el-input .el-input__inner {
  background-color: #FFFFFF;
  color: #606266;
  border: 1px solid #DCDFE6;
}
.default-style>>>.el-input .el-input__inner:hover {
  border: 1px solid #c0c4cc;
}
.default-style>>>.el-input .el-input__inner:focus {
  border: 1px solid #3671e8;
}
.default-style>>>.el-popper {
  border: 1px solid #e4e7ed;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}
.default-style>>>.el-popper[x-placement^=bottom] .popper__arrow::after,
.default-style>>>.el-popper[x-placement^=top] .popper__arrow::after {
  border-bottom-color: #fff;
  border-top-color: #fff;
}
.default-style>>>.el-popper[x-placement^=bottom] .popper__arrow,
.default-style>>>.el-popper[x-placement^=top] .popper__arrow {
  border-bottom-color: rgba(0,0,0,.1);
  border-top-color: rgba(0,0,0,.1);
}
.default-style>>>.el-select-dropdown__item:not(.is-disabled) {
  color: #606266;
  background-color: #fff;
}
.default-style>>>.el-select-dropdown__item:hover {
  color: #606266;
  background-color: #f5f7fa;
}
.default-style>>>.el-select-dropdown__item.selected {
  color: #3671e8;
  background-color: #f5f7fa;
}
.default-style>>>.el-select-dropdown__item.selected:hover {
  background-color: #f5f7fa;
}
.default-style>>>.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  background-color: #f5f7fa;
}
</style>

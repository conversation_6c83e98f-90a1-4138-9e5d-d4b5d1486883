<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入公告标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人员" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="noticeType">
        <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_notice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >编辑</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-view"
          size="mini"
          @click="testForceNotice"
        >测试强制弹窗</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 切换视图按钮 -->
    <div class="view-switcher">
      <el-radio-group v-model="viewMode" size="mini" @change="handleViewChange">
        <el-radio-button label="table">
          <i class="el-icon-menu"></i> 表格视图
        </el-radio-button>
        <el-radio-button label="card">
          <i class="el-icon-collection"></i> 卡片视图
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 表格视图 -->
    <el-table v-if="viewMode === 'table'" v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="noticeId" width="100" />
      <el-table-column
        label="公告标题"
        align="center"
        prop="noticeTitle"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="公告类型" align="center" prop="noticeType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_notice_type" :value="scope.row.noticeType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_notice_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="强制查看" align="center" prop="forceRead" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.forceRead === '1' ? 'warning' : 'info'" size="mini">
            {{ scope.row.forceRead === '1' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" width="100" />
      <el-table-column label="发布时间" align="center" prop="releaseTime" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handlePreview(scope.row)"
          >预览</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 卡片视图 -->
    <div v-if="viewMode === 'card'" v-loading="loading" class="notice-cards">
      <div v-for="notice in noticeList" :key="notice.noticeId" class="notice-card" @click="handlePreview(notice)">
        <div class="card-header">
          <div class="card-title">
            <svg-icon icon-class="message" class="title-icon" />
            <span>{{ notice.noticeTitle }}</span>
          </div>
          <div class="card-badges">
            <el-tag :type="getNoticeTypeColor(notice.noticeType)" size="mini">
              {{ getNoticeTypeText(notice.noticeType) }}
            </el-tag>
            <el-tag v-if="notice.forceRead === '1'" type="warning" size="mini">
              <i class="el-icon-warning-outline"></i> 强制查看
            </el-tag>
          </div>
        </div>
        <div class="card-content">
          <div class="card-meta">
            <span class="meta-item">
              <i class="el-icon-user"></i>
              {{ notice.createBy }}
            </span>
            <span class="meta-item">
              <i class="el-icon-time"></i>
              {{ notice.releaseTime || parseTime(notice.createTime, '{y}-{m}-{d}') }}
            </span>
          </div>
          <div class="card-excerpt" v-if="notice.header">
            {{ notice.header }}
          </div>
        </div>
        <div class="card-actions">
          <el-button size="mini" type="primary" icon="el-icon-view" @click.stop="handlePreview(notice)">预览</el-button>
          <el-button size="mini" icon="el-icon-edit" @click.stop="handleUpdate(notice)">编辑</el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" @click.stop="handleDelete(notice)">删除</el-button>
        </div>
      </div>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公告类型" prop="noticeType">
              <el-select v-model="form.noticeType" placeholder="请选择公告类型">
                <el-option
                  v-for="dict in dict.type.sys_notice_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_notice_status" :key="dict.value"
                  :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="releaseTime">
              <el-date-picker v-model="form.releaseTime" value-format="yyyy-MM-dd" type="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="强制查看">
              <el-radio-group v-model="form.forceRead">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面">
              <imageUpload :values="form.coverOssId" :limit="1" @input="getCover" v-model="form.cover" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="段落头" prop="header">
              <el-input v-model="form.header" type="textarea" placeholder="请输入段落头" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容">
              <editor v-model="form.noticeContent" :min-height="192"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 预览公告弹窗 - 使用美化的NoticePopup组件 -->
    <NoticePopup 
      :notices="previewNotices" 
      @all-read="handlePreviewClose"
    />
  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice, debugForceNotices } from "@/api/system/notice";
import NoticePopup from "@/components/NoticePopup";

export default {
  name: "Notice",
  dicts: ['sys_notice_status', 'sys_notice_type'],
  components: {
    NoticePopup
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示查询条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 视图模式
      viewMode: 'table',
      // 预览弹窗数据
      previewNotices: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: "公告标题不能为空", trigger: "blur" }
        ],
        noticeType: [
          { required: true, message: "公告类型不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getCover(value) {
      this.form.coverOssId = value;
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0",
        forceRead: "0"
      };
      this.resetForm("form");
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公告";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data;
        // 确保forceRead字段有默认值
        if (!this.form.forceRead) {
          this.form.forceRead = "0";
        }
        this.open = true;
        this.title = "修改公告";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.noticeId != undefined) {
            updateNotice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNotice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      this.$modal.confirm('是否确认删除？').then(function() {
        return delNotice(noticeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 测试强制弹窗功能 */
    async testForceNotice() {
      console.log('=== 开始测试强制弹窗功能 ===');
      
      try {
        // 1. 先调用调试接口，获取详细信息
        const debugResponse = await debugForceNotices();
        console.log('调试接口响应:', debugResponse);
        
        // 2. 触发检查未读通知的事件
        this.$bus.$emit('check-unread-notices');
        
        // 3. 显示调试信息
        const debugData = debugResponse.data || {};
        const message = `
测试完成！请查看控制台详细日志。

调试信息：
- 当前用户ID: ${debugData.currentUserId || '未知'}
- 当前时间: ${debugData.currentTime || '未知'}
- 强制通知总数: ${debugData.allForceNoticesCount || 0}
- 未读通知数: ${debugData.unreadNoticesCount || 0}

${debugData.success ? '✅ 调试接口调用成功' : '❌ 调试接口调用失败: ' + (debugData.error || '未知错误')}
        `.trim();
        
        this.$modal.msgInfo(message);
        
      } catch (error) {
        console.error('测试失败:', error);
        this.$modal.msgError("测试失败，请查看控制台错误信息");
      }
      
      console.log('=== 强制弹窗功能测试结束 ===');
    },
    /** 切换视图模式 */
    handleViewChange(mode) {
      this.viewMode = mode;
    },
    /** 预览公告 */
    async handlePreview(notice) {
      console.log('=== 预览公告 ===');
      console.log('原始通知数据:', notice);
      console.log('数据字段:', Object.keys(notice));
      
      try {
        // 通过API获取完整的通知详情，确保数据格式与强制弹窗一致
        const response = await getNotice(notice.noticeId);
        const fullNotice = response.data;
        
        console.log('API返回的完整通知数据:', fullNotice);
        console.log('完整数据字段:', Object.keys(fullNotice));
        
        // 转换为数组格式以适配NoticePopup组件
        this.previewNotices = [fullNotice];
        
        console.log('传递给NoticePopup的数据:', this.previewNotices);
      } catch (error) {
        console.error('获取通知详情失败:', error);
        // 降级处理：使用列表中的数据
        this.previewNotices = [notice];
        this.$modal.msgError("获取通知详情失败，请重试");
      }
    },
    /** 关闭预览弹窗 */
    handlePreviewClose() {
      this.previewNotices = [];
    },
    /** 获取公告类型文本 */
    getNoticeTypeText(type) {
      const typeMap = {
        '1': '通知',
        '2': '公告'
      };
      return typeMap[type] || '通知';
    },
    /** 获取公告类型颜色 */
    getNoticeTypeColor(type) {
      const colorMap = {
        '1': 'primary',
        '2': 'success'
      };
      return colorMap[type] || 'primary';
    }
  }
};
</script>

<style scoped>
/* 视图切换器 */
.view-switcher {
  margin-bottom: 20px;
  text-align: right;
}

/* 卡片视图样式 */
.notice-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.notice-card {
  background: var(--base-item-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.notice-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: var(--theme-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 10px;
}

.title-icon {
  font-size: 18px;
  color: var(--theme-color);
  margin-right: 8px;
}

.card-title span {
  font-size: 16px;
  font-weight: 600;
  color: var(--base-color-1);
  line-height: 1.4;
  word-break: break-word;
}

.card-badges {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-end;
}

.card-content {
  margin-bottom: 15px;
}

.card-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
}

.meta-item {
  font-size: 13px;
  color: var(--base-color-2);
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-excerpt {
  font-size: 14px;
  color: var(--base-color-2);
  line-height: 1.5;
  background: rgba(var(--theme-color-rgb), 0.05);
  padding: 10px 12px;
  border-radius: 6px;
  border-left: 3px solid var(--theme-color);
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 移除了预览弹窗的旧样式，现在使用NoticePopup组件 */

/* 主题适配 */
.theme-dark .notice-card {
  background: var(--base-item-bg);
  border-color: var(--border-color);
}

.theme-dark .notice-card:hover {
  border-color: var(--theme-color);
}

.theme-starry-sky .notice-card {
  background: rgba(var(--base-item-bg-rgb), 0.8);
  backdrop-filter: blur(10px);
}
</style>
<template>
  <div class="app-container bg-container" v-loading="loading">
    <el-row :gutter="20">
      <!-- 个人信息区域 -->
      <el-col :span="24"> {{ /* 个人信息占据整行 */ }}
        <el-card class="box-card profile-card">
          <div class="overview-title">
            <div class="overview-icon"></div><span>个人信息</span>
          </div>
          <div class="info-content">
            <!-- 左侧头像 -->
            <div style="flex: 1;text-align:center;">
              <userAvatar :user="user" />
            </div>
            <!-- 右侧详细信息 -->
            <div class="info-box">
              <div class="info-body">
                <div><svg-icon icon-class="user" style="margin-right:6px;" />用户名称：{{ user.userName }}</div>
                <div><svg-icon icon-class="nickname" style="margin-right:6px;" />姓名：{{ user.nickName }}</div>
                <div><svg-icon icon-class="sex" style="margin-right:6px;" />性别：{{ handleSex(user.sex) }}</div>
                <div><svg-icon icon-class="phone" style="margin-right:6px;" />手机号码：{{ user.phonenumber || '--' }}</div>
                <div style="height:21px;"><svg-icon icon-class="email" style="margin-right:6px;" />用户邮箱：{{ user.email || '--' }}</div>
              </div>
              <div class="info-body">
                <div><svg-icon icon-class="tree" style="margin-right:6px;" />所属部门：{{ user.dept ? (user.dept.deptName || '--') : '--' }}</div>
                <div><svg-icon icon-class="post" style="margin-right:6px;" />所在岗位：{{ postGroup || '--' }}</div>
                <div><svg-icon icon-class="peoples" style="margin-right:6px;" />所属角色：{{ roleGroup || '--' }}</div>
                <div><svg-icon icon-class="date" style="margin-right:6px;" />创建时间：{{ parseTime(user.createTime, '{y}-{m}-{d}') }}</div>
                <div style="height:21px;">
                  <el-button size="mini" type="primary" @click="openInfoDialog">修改信息</el-button>
                  <el-button size="mini" type="primary" @click="openPwdDialog">重置密码</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;"> {{ /* 我的工单和我的巡检区域 */ }}
      <!-- 我的工单 -->
      <el-col :span="12"> {{ /* 占据一半宽度 */ }}
        <el-card class="box-card task-card">
          <div class="overview-title">
            <div class="overview-icon"></div><span>我的工单</span>
          </div>
          <div class="count-body"> {{ /* 示例结构 */ }}
            <div class="count-item" @click="toRepairOrder">
               <div style="text-align: center;">待处理</div>
               <div class="count-num">{{ repairCount.pending || 0 }}</div>
            </div>
             <div class="count-item" @click="toRepairOrder">
               <div style="text-align: center;">已完成</div>
               <div class="count-num">{{ repairCount.completed || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <!-- 我的巡检 -->
      <el-col :span="12"> {{ /* 占据一半宽度 */ }}
        <el-card class="box-card task-card">
          <div class="overview-title">
            <div class="overview-icon"></div><span>我的巡检</span>
          </div>
           <div class="count-body"> {{ /* 示例结构 */ }}
            <div class="count-item" @click="toInspection">
               <div style="text-align: center;">待处理</div>
               <div class="count-num">{{ inspectionCount.pending || 0 }}</div>
            </div>
             <div class="count-item" @click="toInspection">
               <div style="text-align: center;">已完成</div>
               <div class="count-num">{{ inspectionCount.completed || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;"> {{ /* 月度数量图表区域 */ }}
      <!-- 工单数量 (月度) 图表 -->
      <el-col :span="12"> {{ /* 占据一半宽度 */ }}
         <el-card class="box-card chart-card">
          <div class="overview-title">
            <div class="overview-icon"></div><span>工单数量 (月度)</span>
          </div>
          <!-- 图表占位符 -->
          <div class="chart-container">
             <OrderChart :barData="repairs" :setInfo="{xName: '月份', yName: '数量', legendName: ['已完成', '未完成']}" height="260px" />
          </div>
         </el-card>
      </el-col>
      <!-- 巡检数量 (月度) 图表 -->
      <el-col :span="12"> {{ /* 占据一半宽度 */ }}
        <el-card class="box-card chart-card">
          <div class="overview-title">
            <div class="overview-icon"></div><span>巡检数量 (月度)</span>
          </div>
           <!-- 图表占位符 -->
          <div class="chart-container">
             <OrderChart :barData="inspections" :setInfo="{xName: '月份', yName: '数量', legendName: ['已完成', '未完成']}" height="260px" />
          </div>
        </el-card>
      </el-col>
    </el-row>


    <!-- 弹窗保持不变 -->
    <el-dialog :title="title" :visible.sync="infoDialog" append-to-body destroy-on-close width="500px">
      <user-info :user="user" @closeDialog="closeInfoDialog" />
    </el-dialog>
    <el-dialog :title="title" :visible.sync="pwdDialog" append-to-body destroy-on-close width="500px">
      <resetPwd @closeDialog="closePwdDialog" />
    </el-dialog>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import resetPwd from "./resetPwd";
import { getOrderCountByAssignee,getAssigneeOrderStatsByMonth } from "@/api/jenasi/orders";
// 引入获取用户信息的API
import { getUserProfile, countRepairAndInspection } from "@/api/system/user";
// 可能需要引入工单和巡检相关的API来获取数量和图表数据
// import { getRepairCount, getInspectionCount, getMonthlyRepairCount, getMonthlyInspectionCount } from "@/api/your-task-module"; // 示例API
// 如果有图表组件，也需要引入
import OrderChart from './orderChart.vue';
import UserInfo from './userInfo.vue';

export default {
  name: "Profile",
  components: {
    userAvatar,
    resetPwd,
    // PieChartVue, // 如果需要饼图
    // OrderChartVue, // 如果需要柱状图/折线图
    OrderChart,
    UserInfo
  },
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      // activeTab: "userinfo", // 这个可能不需要了
      infoDialog: false,
      pwdDialog: false,
      title: '修改信息',
      // 新增：用于存放我的工单和我的巡检数量
      repairCount: { pending: 0, completed: 0 },
      inspectionCount: { pending: 0, completed: 0 },
      // 新增：用于存放月度图表数据
      repairs: {
        xData: [],
        currentData: [],
        sameData: []
      },
      inspections: {
        xData: ['2024-06', '2024-12', '2025-01', '2025-03', '2025-04'],
        currentData: [0, 1, 0, 0, 1],
        sameData: [0, 1, 1, 0, 0]
      },
      loading: true,
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      this.loading = true
      getUserProfile().then(response => {
        this.user = response.data.user;
        this.roleGroup = response.data.roleGroup;
        this.postGroup = response.data.postGroup;
        // 获取用户信息后，使用用户的nickName获取工单数据
        if (this.user.nickName) {
          this.getOrderData();
          this.getMonthlyOrderData();
        }
      }).finally(() => {
        this.loading = false
      });
    },
    // 获取工单统计数据
    async getOrderData() {
      try {
        const response = await getOrderCountByAssignee(this.user.nickName);
        const data = response.data;
        // 将API返回的数据映射到页面显示
        this.repairCount = {
          pending: data.newOrNotCompletedOrderCount || 0,
          completed: data.completedOrderCount || 0
        };
      } catch (error) {
        console.error('获取工单统计数据失败:', error);
        this.$message.error('获取工单统计数据失败');
        // 设置默认值
        this.repairCount = { pending: 0, completed: 0 };
      }
    },

    // 获取月度工单图表数据
    async getMonthlyOrderData() {
      try {
        const response = await getAssigneeOrderStatsByMonth(this.user.nickName);
        const monthlyData = response.data;

        // 转换数据格式为OrderChart组件需要的格式
        if (monthlyData && monthlyData.length > 0) {
          const xData = [];
          const currentData = []; // 已完成
          const sameData = []; // 未完成

          monthlyData.forEach(item => {
            xData.push(item.month);
            currentData.push(item.completedOrderCount || 0);
            sameData.push(item.newOrNotCompletedOrderCount || 0);
          });

          this.repairs = {
            xData,
            currentData,
            sameData
          };
        } else {
          // 如果没有数据，设置默认的空数据
          this.repairs = {
            xData: [],
            currentData: [],
            sameData: []
          };
        }
      } catch (error) {
        console.error('获取月度工单数据失败:', error);
        this.$message.error('获取月度工单数据失败');
        // 设置默认值
        this.repairs = {
          xData: [],
          currentData: [],
          sameData: []
        };
      }
    },
    handleSex(sex) {
      if (!sex) return '--'
      switch (sex) {
        case '1':
          return '女'
        case '0':
          return '男'
        case '2':
          return '未知'
        default:
          return '--'; // 添加默认值
      }
    },
    // 打开修改信息弹窗
    openInfoDialog() {
      this.title = '修改信息'
      this.infoDialog = true
    },
    // 重置密码
    openPwdDialog() {
      this.title = '重置密码'
      this.pwdDialog = true
    },
    // 关闭信息修改弹窗
    closeInfoDialog(value) {
      if (value) this.getUser()
      this.infoDialog = false
    },
    // 关闭重置密码弹窗
    closePwdDialog(value) {
      this.pwdDialog = false
    },
    // 跳转我的工单
    toRepairOrder() {
      //this.$router.push({path:'/produce/sheet',query: { orderStatus: "IN_PROGRESS" }})
      this.$router.push({ path: '/myTask' }) // 请确认工单列表页面的实际路由
    },
    // 跳转我的巡检
    toInspection() {
      this.$router.push({ path: '/maintenance/my-inspection' }) // 请确认巡检列表页面的实际路由
    },
  }
};
</script>
<style scoped>
.app-container {
  width: 100%;
  min-height: calc(100vh - 84px);
  background: var(--base-body-background); /* 适配主题 */
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
  background: var(--base-item-bg); /* 适配主题 */
}

.profile-card {
    /* 个人信息卡片的高度 */
   height: 250px; /* 根据内容调整高度 */
}

.task-card {
    /* 我的工单/巡检卡片高度 */
    height: 250px; /* 根据内容调整高度 */
}

.chart-card {
    /* 图表卡片高度 */
    height: 350px; /* 根据内容调整高度 */
}


.box-card>>>.el-card__body {
  height: 100%;
  display: flex; /* 启用 flex 布局 */
  flex-direction: column; /* 子项垂直排列 */
  padding: 20px; /* 根据需要调整内边距 */
}

.overview-title {
  color: var(--base-color-1);
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px; /* 标题字体大小 */
  font-weight: bold; /* 标题加粗 */
}

.overview-icon {
  width: 5px;
  height: 20px;
  margin-right: 10px;
  background: var(--current-color);
  /* 调整圆角 */
  border-radius: 2px;
}

.info-content {
  height: calc(100% - 41px); /* 减去标题高度和 margin */
  display: flex;
  align-items: center;
  padding: 0px; /* 移除原有内边距，由父容器控制 */
}

.info-box {
  flex: 3;
  display: flex;
  height: 100%;
  /* border-right: 1px solid var(--base-color-1); 根据需要是否保留分隔线*/
   padding-left: 20px; /* 详细信息部分左侧间距 */
}

.info-body {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
   /* 调整信息行之间的间距 */
   line-height: 1.8;
   font-size: 14px;
   color: #606266; /* 字体颜色 */
}

.count-body {
  height: calc(100% - 41px); /* 减去标题高度和 margin */
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: space-evenly; /* 垂直方向均匀分布 */
  width: 100%; /* 宽度占满父容器 */
   padding: 0 20px; /* 左右内边距 */
}

.count-item {
  cursor: pointer;
  height: 45%; /* 调整高度比例 */
  display:flex; /* 内部使用flex布局 */
  flex-direction:column; /* 内部子项垂直排列 */
  align-items:center; /* 内部子项水平居中 */
  justify-content:center; /* 内部子项垂直居中 */
  width: 100%; /* 宽度占满 count-body */
  border: 1px solid var(--border-color-1);
  transition: background 1s, color 0.3s;
  /* 添加圆角和阴影 */
  border-radius: 8px;
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
  /* 背景颜色 */
  background-color: var(--base-item-bg); /* 使用主题变量 */
  color: var(--theme-color); /* 使用主题变量 */
}

.count-item:hover {
  background: var(--base-color-8);
  border: 1px solid transparent;
  transition: background 1s, color 0.3s;
  /* 鼠标悬停时的阴影效果 */
  /* box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.2); */
  color: var(--theme-color);
}

.count-num {
  color: var(--theme-color); /* 使用主题色 */
  font-size: 36px;
  font-weight: bold;
  text-align: center;
  height: auto; /* 高度自动 */
  display: block; /* 改为block */
  margin-top: 10px; /* 数量与文字间距 */
}

.chart-container {
    height: calc(100% - 41px); /* 减去标题高度和 margin */
    width: 100%;
    /* 这里是图表实际渲染区域 */
}


@media (max-width: 1200px) {
  .info-box {
    border: none;
    padding-left: 0; /* 窄屏幕下移除左侧间距 */
    flex-direction: column; /* 窄屏幕下垂直排列 */
    align-items: center; /* 垂直排列时水平居中 */
  }
   .info-body {
       align-items: center; /* 垂直排列时文字居中 */
       margin-bottom: 15px; /* 每组信息之间加间距 */
   }
   .info-body > div {
       text-align: center; /* 文字居中 */
   }
   .info-content {
       flex-direction: column; /* 个人信息部分垂直排列 */
   }
   .info-content > div {
       margin-bottom: 15px; /* 头像和信息块之间加间距 */
   }
   .task-card, .chart-card {
       height: auto; /* 窄屏幕下高度自适应 */
   }
   .count-body {
       flex-direction: row; /* 我的工单/巡检卡片内部项目水平排列 */
       justify-content: space-around; /* 水平方向均匀分布 */
   }
    .count-item {
        width: 45%; /* 水平排列时调整宽度 */
    }

}
</style>

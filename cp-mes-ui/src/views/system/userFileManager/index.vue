<template>
  <div class="app-container user-file-manager" :class="themeClass" @contextmenu="handleContainerContextMenu">
    <!-- 顶部工具栏 -->
    <div class="toolbar-wrapper">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-upload"
            size="mini"
            @click="handleUpload"
            v-hasPermi="['system:userFileManager:upload']"
            class="modern-button"
          >上传文件</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-folder-add"
            size="mini"
            @click="handleCreateFolder"
            v-hasPermi="['system:userFileManager:folder']"
            class="modern-button"
          >新建文件夹</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:userFileManager:remove']"
            class="modern-button"
          >删除</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择文件类型" clearable>
          <el-option label="图片" value="image" />
          <el-option label="文档" value="document" />
          <el-option label="视频" value="video" />
          <el-option label="音频" value="audio" />
          <el-option label="压缩包" value="archive" />
          <el-option label="代码" value="code" />
          <el-option label="CAD设计" value="cad" />
          <el-option label="配置" value="config" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="存储策略" prop="storageStrategy">
        <el-select v-model="queryParams.storageStrategy" placeholder="请选择存储策略" clearable>
          <el-option label="私有" value="private" />
          <el-option label="共享" value="shared" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 导航栏 -->
    <div class="nav-bar modern-nav">
      <div class="breadcrumb-section">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item 
            v-for="(item, index) in breadcrumbs" 
            :key="index"
            class="breadcrumb-item"
          >
            <span 
              v-if="index === breadcrumbs.length - 1" 
              class="current-breadcrumb"
            >
              <i class="el-icon-folder" style="margin-right: 4px;"></i>
              {{ item.name }}
            </span>
            <span 
              v-else 
              @click="handleBreadcrumbClick(item, index)" 
              class="breadcrumb-link"
            >
              <i class="el-icon-folder" style="margin-right: 4px;"></i>
              {{ item.name }}
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="nav-actions">
        <el-button-group size="mini" class="view-mode-toggle">
          <el-button 
            :type="viewMode === 'list' ? 'primary' : ''" 
            icon="el-icon-menu" 
            @click="viewMode = 'list'"
            class="toggle-button"
            :class="{ active: viewMode === 'list' }"
          ></el-button>
          <el-button 
            :type="viewMode === 'grid' ? 'primary' : ''" 
            icon="el-icon-s-grid" 
            @click="viewMode = 'grid'"
            class="toggle-button"
            :class="{ active: viewMode === 'grid' }"
          ></el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 左侧文件夹树 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="folder-tree-card modern-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-folder-opened"></i>
            <span>文件夹</span>
          </div>
          <el-tree
            :key="treeKey"
            :data="folderTree"
            :props="treeProps"
            :expand-on-click-node="false"
            :default-expand-all="false"
            :default-expanded-keys="treeExpandedKeys"
            :highlight-current="true"
            node-key="folderId"
            ref="folderTreeRef"
            @node-click="handleFolderClick"
            @node-contextmenu="handleFolderContextMenu"
            @node-expand="handleNodeExpand"
            @node-collapse="handleNodeCollapse"
            class="modern-tree"
          >
            <span class="custom-tree-node modern-tree-node" slot-scope="{ node, data }">
              <span class="node-content" @dblclick.stop="handleFolderDoubleClick(data, node)">
                <i class="node-icon" :class="getTreeNodeIcon(data)"></i>
                <span class="node-label">{{ node.label }}</span>
                <span class="file-count" v-if="data.fileCount > 0">({{ data.fileCount }})</span>
              </span>
              <span class="tree-node-actions">
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="handleEditFolder(data)"
                  v-if="data.folderId !== 0 && data.folderId !== -1"
                  v-hasPermi="['system:userFileManager:folder']"
                  class="action-button"
                >
                  <i class="el-icon-edit"></i>
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="handleDeleteFolder(data)"
                  v-if="data.folderId !== 0 && data.folderId !== -1"
                  v-hasPermi="['system:userFileManager:folder']"
                  class="action-button delete-button"
                >
                  <i class="el-icon-delete"></i>
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 右侧文件列表 -->
      <el-col :span="18" @contextmenu.native="handleMainAreaContextMenu">
        <!-- 列表视图 -->
        <el-table
          v-if="viewMode === 'list'"
          v-loading="loading"
          :data="fileList"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @row-contextmenu="handleRowContextMenu"
          @contextmenu.native="handleTableContextMenu"
          :default-sort="{prop: 'createTime', order: 'descending'}"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="文件名" prop="originalName" :show-overflow-tooltip="true" sortable="custom">
            <template slot-scope="scope">
              <div class="file-item">
                <i :class="getFileIcon(scope.row.originalName)" style="margin-right: 8px;"></i>
                <el-link :underline="false" @click="handlePreview(scope.row)">
                  {{ scope.row.originalName }}
                </el-link>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="文件类型" prop="fileType" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getFileTypeTagType(scope.row.fileType)" size="mini">
                {{ getFileTypeText(scope.row.fileType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="文件大小" prop="fileSize" width="100" align="center" sortable="custom">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column label="存储策略" prop="storageStrategy" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.storageStrategy === 'private' ? 'success' : 'warning'" size="mini">
                {{ scope.row.storageStrategy === 'private' ? '私有' : '共享' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="上传时间" prop="createTime" width="180" align="center" sortable="custom">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handlePreview(scope.row)"
                v-hasPermi="['system:userFileManager:query']"
              >预览</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDownload(scope.row)"
                v-hasPermi="['system:userFileManager:query']"
              >下载</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-link"
                @click="handleCopyDownloadLink(scope.row)"
                v-hasPermi="['system:userFileManager:query']"
              >复制链接</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-share"
                @click="handleShare(scope.row)"
                v-if="scope.row.storageStrategy === 'private'"
                v-hasPermi="['system:userFileManager:share']"
              >分享</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                style="color: #f56c6c;"
                @click="handleDeleteFile(scope.row)"
                v-hasPermi="['system:userFileManager:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态提示 -->
        <div v-if="viewMode === 'list' && fileList.length === 0" class="empty-state modern-empty" @contextmenu="handleEmptyStateContextMenu">
          <div class="empty-content">
            <div class="empty-icon">
              <i class="el-icon-folder-opened"></i>
            </div>
            <h3>暂无文件</h3>
            <p>右键可上传文件或创建文件夹</p>
            <div class="empty-actions">
              <el-button type="primary" @click="handleUpload" v-hasPermi="['system:userFileManager:upload']">
                <i class="el-icon-upload"></i> 上传文件
              </el-button>
              <el-button @click="handleCreateFolder" v-hasPermi="['system:userFileManager:folder']">
                <i class="el-icon-folder-add"></i> 新建文件夹
              </el-button>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div 
          v-else 
          class="grid-view modern-grid" 
          v-loading="loading"
          @contextmenu="handleGridContextMenu"
        >
          <div class="file-grid" v-if="fileList.length > 0">
            <div
              v-for="(file, index) in fileList"
              :key="file.ossId"
              class="file-card modern-file-card"
              :class="{ 'selected': selectedFiles.includes(file.ossId) }"
              @click="handleFileCardClick(file)"
              @dblclick="handleFileCardDoubleClick(file)"
              @contextmenu.stop="handleFileContextMenu(file, $event)"
            >
              <div class="file-card-content">
                <div class="file-icon-wrapper">
                  <div class="file-icon">
                    <img v-if="isImage(file.originalName)" :src="file.url" @error="handleImageError" class="file-image" />
                    <i v-else :class="getFileIcon(file.originalName)" class="file-type-icon"></i>
                  </div>
                  <div class="storage-badge">
                    <el-tag 
                      :type="file.storageStrategy === 'private' ? 'success' : 'warning'" 
                      size="mini"
                      class="storage-tag"
                    >
                      {{ file.storageStrategy === 'private' ? '私有' : '共享' }}
                    </el-tag>
                  </div>
                </div>
                <div class="file-info">
                  <div class="file-name" :title="file.originalName">{{ file.originalName }}</div>
                  <div class="file-meta">
                    <span class="file-type-text">{{ getFileTypeText(file.fileType) }}</span>
                    <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
                  </div>
                  <div class="file-time">{{ parseTime(file.createTime, '{m}-{d} {h}:{i}') }}</div>
                </div>
                <!-- 文件操作提示 -->
                <div class="file-operation-hint">
                  <span class="hint-text">单击预览 · 双击下载 · 右键菜单</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 网格视图空状态 -->
          <div v-if="fileList.length === 0" class="empty-grid-state modern-empty" @contextmenu="handleEmptyStateContextMenu">
            <div class="empty-content">
              <div class="empty-icon">
                <i class="el-icon-folder-opened"></i>
              </div>
              <h3>暂无文件</h3>
              <p>右键可上传文件或创建文件夹</p>
              <div class="empty-actions">
                <el-button type="primary" @click="handleUpload" v-hasPermi="['system:userFileManager:upload']">
                  <i class="el-icon-upload"></i> 上传文件
                </el-button>
                <el-button @click="handleCreateFolder" v-hasPermi="['system:userFileManager:folder']">
                  <i class="el-icon-folder-add"></i> 新建文件夹
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 上传文件对话框 -->
    <el-dialog :title="uploadDialogTitle" :visible.sync="uploadDialog" width="800px" append-to-body @close="handleUploadDialogClose">
      <user-file-upload
        v-model="uploadFiles"
        :storage-strategy="uploadStorageStrategy"
        :default-folder-id="getSafeUploadFolderId()"
        :show-storage-selector="!isDirectUpload"
        :show-folder-selector="!isDirectUpload || targetFolderId === null"
        :show-file-list="false"
        :limit="20"
        :file-size="1024"
        @input="handleUploadChange"
        @folder-not-found="handleFolderNotFound"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialog = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog 
      :visible.sync="previewDialog" 
      width="90%" 
      top="5vh"
      append-to-body
      :show-close="false"
      custom-class="preview-dialog"
      :destroy-on-close="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      :lock-scroll="true"
      @closed="handlePreviewClosed"
    >
      <!-- 自定义标题栏 -->
      <div class="preview-header" slot="title">
        <!-- 左侧功能按钮已移除 -->
        <div class="left-actions">
          <!-- 下载和新窗口打开按钮已移除，可通过右键菜单访问 -->
        </div>

        <!-- 中间标题信息 -->
        <div class="preview-title">
          <i :class="getFileIcon(previewFile.originalName)" class="preview-file-icon"></i>
          <div class="preview-file-info">
            <h3 class="preview-file-name">{{ previewFile.originalName }}</h3>
            <div class="preview-file-meta">
              <span class="preview-file-type">{{ getFileExtension(previewFile.originalName) }}</span>
              <span class="preview-file-size">{{ formatFileSize(previewFile.fileSize) }}</span>
              <span class="preview-file-time">{{ parseTime(previewFile.createTime) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧关闭按钮已移除 -->
        <div class="right-actions">
          <!-- 关闭按钮已移除，用户可以通过点击遮罩或按ESC键关闭 -->
        </div>
      </div>

      <!-- 预览内容 -->
      <div class="file-preview-container">
        <!-- 图片预览 -->
        <div v-if="isImage(previewFile.originalName)" class="image-preview">
          <div class="image-wrapper">
            <img 
              :src="previewFile.url" 
              :alt="previewFile.originalName"
              class="preview-image"
              @load="onImageLoad"
              @error="onImageError"
            />
          </div>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="isPdf(previewFile.originalName)" class="pdf-preview">
          <iframe 
            :src="previewFile.url" 
            class="preview-iframe"
            frameborder="0"
            @load="onIframeLoad"
            @error="onIframeError"
          ></iframe>
        </div>

        <!-- 文本文件预览 -->
        <div v-else-if="isTextFile(previewFile.originalName)" class="text-preview">
          <div class="text-content" v-if="textContent">
            <pre>{{ textContent }}</pre>
          </div>
          <div v-else class="loading-text">
            <i class="el-icon-loading"></i>
            <p>正在加载文件内容...</p>
          </div>
        </div>

        <!-- 代码文件预览 -->
        <div v-else-if="isCodeFile(previewFile.originalName)" class="code-preview">
          <div class="code-content" v-if="codeContent">
            <pre><code v-highlight>{{ codeContent }}</code></pre>
          </div>
          <div v-else class="loading-text">
            <i class="el-icon-loading"></i>
            <p>正在加载代码内容...</p>
          </div>
        </div>

        <!-- 不支持预览的文件 -->
        <div v-else class="no-preview">
          <div class="no-preview-content">
            <i :class="getFileIcon(previewFile.originalName)" class="no-preview-icon"></i>
            <h3>{{ previewFile.originalName }}</h3>
            <p class="no-preview-message">此文件类型暂不支持在线预览</p>
            <div class="no-preview-info">
              <div class="info-item">
                <span class="info-label">文件类型：</span>
                <span class="info-value">{{ getFileTypeDisplay(previewFile.originalName) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">文件大小：</span>
                <span class="info-value">{{ formatFileSize(previewFile.fileSize) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">上传时间：</span>
                <span class="info-value">{{ parseTime(previewFile.createTime) }}</span>
              </div>
            </div>
            <div class="no-preview-actions">
              <el-button 
                type="primary" 
                icon="el-icon-download" 
                @click="handleDownload(previewFile)"
                class="download-btn"
              >
                下载文件
              </el-button>
              <el-button 
                icon="el-icon-link" 
                @click="handleCopyDownloadLink(previewFile)"
                class="copy-link-btn"
              >
                复制下载链接
              </el-button>
              <el-button 
                v-if="canOpenInNewWindow(previewFile.originalName)"
                icon="el-icon-document" 
                @click="openInNewWindow(previewFile.url)"
              >
                在新窗口打开
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 分享文件对话框 -->
    <el-dialog title="分享文件到共享空间" :visible.sync="shareDialog" width="500px" append-to-body>
      <div class="share-file-info">
        <p><strong>文件名：</strong>{{ shareFile.originalName }}</p>
        <p><strong>文件大小：</strong>{{ formatFileSize(shareFile.fileSize) }}</p>
      </div>
      
      <el-form :model="shareForm" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="操作类型">
          <el-radio-group v-model="shareForm.shareType">
            <el-radio label="copy">复制（保留原文件）</el-radio>
            <el-radio label="move">移动（删除原文件）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="目标文件夹">
          <el-cascader
            v-model="shareForm.targetFolderId"
            :options="sharedFolderOptions"
            :props="{ value: 'folderId', label: 'folderName', children: 'children', checkStrictly: true, emitPath: false }"
            placeholder="选择目标文件夹（默认为根目录）"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="shareDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleShareConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 创建/编辑文件夹对话框 -->
    <el-dialog 
      :title="folderDialog.title" 
      :visible.sync="folderDialog.visible" 
      width="500px" 
      append-to-body 
      :destroy-on-close="true"
      @close="handleFolderDialogClose"
    >
      <el-form :model="folderForm" :rules="folderRules" ref="folderForm" label-width="100px">
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input v-model="folderForm.folderName" placeholder="请输入文件夹名称" />
        </el-form-item>
        <el-form-item label="上级文件夹" prop="parentId" v-if="!folderDialog.isDirect">
          <el-cascader
            v-model="folderForm.parentId"
            :options="folderOptions"
            :props="folderProps"
            placeholder="请选择上级文件夹"
            :show-all-levels="false"
            clearable
          />
        </el-form-item>
        <el-form-item label="存储策略" prop="storageStrategy" v-if="!folderDialog.isDirect">
          <el-radio-group v-model="folderForm.storageStrategy" @change="updateFolderOptions">
            <el-radio label="private">我的文件</el-radio>
            <el-radio label="shared">共享空间</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="folderDialog.isDirect" label="存储位置">
          <el-tag :type="folderForm.storageStrategy === 'shared' ? 'warning' : 'success'">
            {{ folderForm.storageStrategy === 'shared' ? '共享空间' : '我的文件' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="排序号" prop="orderNum">
          <el-input-number v-model="folderForm.orderNum" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="folderForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="folderDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleFolderConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 右键菜单 -->
    <div 
      v-show="contextMenu.visible" 
      class="context-menu" 
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      ref="contextMenu"
    >
      <ul class="context-menu-list">
        <!-- 空白区域右键菜单 -->
        <template v-if="contextMenu.type === 'blank'">
          <li @click="handleContextUpload" v-hasPermi="['system:userFileManager:upload']">
            <i class="el-icon-upload"></i>
            上传文件
          </li>
          <li @click="handleContextCreateFolder" v-hasPermi="['system:userFileManager:folder']">
            <i class="el-icon-folder-add"></i>
            新建文件夹
          </li>
          <li @click="refreshFolderTree">
            <i class="el-icon-refresh"></i>
            刷新
          </li>
        </template>
        
        <!-- 文件右键菜单 -->
        <template v-if="contextMenu.type === 'file'">
          <li @click="handleContextPreview" v-hasPermi="['system:userFileManager:query']">
            <i class="el-icon-view"></i>
            预览
          </li>
          <li @click="handleContextDownload" v-hasPermi="['system:userFileManager:query']">
            <i class="el-icon-download"></i>
            下载
          </li>
          <li @click="handleContextCopyDownloadLink" v-hasPermi="['system:userFileManager:query']">
            <i class="el-icon-link"></i>
            复制下载链接
          </li>
          <li @click="handleContextOpenInNewWindow" v-hasPermi="['system:userFileManager:query']" v-if="contextMenu.data && canOpenInNewWindow(contextMenu.data.originalName)">
            <i class="el-icon-document"></i>
            在新窗口打开
          </li>
          <li @click="handleContextShare" v-hasPermi="['system:userFileManager:share']" v-if="contextMenu.data && contextMenu.data.storageStrategy !== 'shared'">
            <i class="el-icon-share"></i>
            分享
          </li>
          <li class="divider"></li>
          <li @click="handleContextDeleteFile" v-hasPermi="['system:userFileManager:remove']" class="danger">
            <i class="el-icon-delete"></i>
            删除
          </li>
        </template>
        
        <!-- 文件夹右键菜单 -->
        <template v-if="contextMenu.type === 'folder'">
          <li @click="handleContextUploadToFolder" v-hasPermi="['system:userFileManager:upload']">
            <i class="el-icon-upload"></i>
            上传文件
          </li>
          <li @click="handleContextCreateFolderInside" v-hasPermi="['system:userFileManager:folder']">
            <i class="el-icon-folder-add"></i>
            新建文件夹
          </li>
          <li @click="handleContextDeleteFolder" v-hasPermi="['system:userFileManager:remove']" v-if="contextMenu.data && contextMenu.data.folderId !== 0 && contextMenu.data.folderId !== -1" class="danger">
            <i class="el-icon-delete"></i>
            删除文件夹
          </li>
          <li @click="handleContextRefresh">
            <i class="el-icon-refresh"></i>
            刷新
          </li>
        </template>
      </ul>
    </div>

    <!-- 右键菜单遮罩 -->
    <div 
      v-show="contextMenu.visible" 
      class="context-menu-overlay" 
      @click="hideContextMenu"
      @contextmenu.prevent="hideContextMenu"
    ></div>
  </div>
</template>

<script>
import { 
  listPrivateFiles, 
  listMySharedFiles, 
  listAllSharedFiles, 
  listFilesByFolder,
  delOss,
  moveToShared,
  copyToShared,
  moveToSharedFolder,
  copyToSharedFolder
} from "@/api/system/oss";
import { 
  getPrivateFolderTree, 
  getSharedFolderTree, 
  addOssFolder, 
  updateOssFolder, 
  delOssFolder 
} from "@/api/system/ossFolder";
import UserFileUpload from "@/components/UserFileUpload";

export default {
  name: "UserFileManager",
  components: {
    UserFileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectedFiles: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文件表格数据
      fileList: [],
      // 视图模式
      viewMode: 'list',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: null,
        fileType: null,
        storageStrategy: 'private',
        folderId: 0
      },
      // 文件夹树
      folderTree: [],
      currentFolderId: 0,
      currentStorageStrategy: 'private',
      treeExpandedKeys: [], // 文件夹树展开的节点keys
      treeKey: 0, // 用于强制重新渲染tree组件
      treeProps: {
        children: 'children',
        label: 'folderName'
      },
      // 导航栏
      breadcrumbs: [
        { name: '我的文件', folderId: 0 }
      ],
      // 文件夹选项
      folderOptions: [],
      folderProps: {
        value: 'folderId',
        label: 'folderName',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      // 上传对话框
      uploadDialog: false,
      uploadFiles: "",
      // 是否为直接上传（右键触发）
      isDirectUpload: false,
      // 目标文件夹信息（用于右键操作）
      targetFolderId: null,
      targetFolderName: '',
      targetStorageStrategy: '',
      // 预览对话框
      previewDialog: false,
      previewFile: {},
      textContent: null,
      codeContent: null,
      // 分享文件对话框
      shareDialog: false,
      shareFile: {},
      shareForm: {
        targetFolderId: 0,
        shareType: 'copy' // copy: 复制, move: 移动
      },
      // 共享文件夹选项
      sharedFolderOptions: [],
      // 文件夹对话框
      folderDialog: {
        visible: false,
        title: "",
        isAdd: true,
        isDirect: false,  // 是否为直接创建（右键触发）
        key: 0  // 用于强制重新渲染组件
      },
      folderForm: {
        folderId: null,
        folderName: '',
        parentId: 0,
        storageStrategy: 'private',
        orderNum: 0,
        remark: ''
      },
      folderRules: {
        folderName: [
          { required: true, message: "文件夹名称不能为空", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
        ]
      },
      // 右键菜单
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        type: '', // 'blank', 'file', 'folder'
        data: null
      },
      
      // 防抖控制
      createFolderLock: false,
      
      // 主题监听器
      themeWatcher: 0,
      
      // 点击计时器（用于区分单击双击）
      clickTimer: null
    };
  },
  computed: {
    // 上传对话框标题
    uploadDialogTitle() {
      if (this.isDirectUpload) {
        if (this.targetFolderName) {
          return `上传文件到"${this.targetFolderName}"文件夹`;
        }
        return this.currentStorageStrategy === 'shared' ? '上传文件到共享空间' : '上传文件到我的文件';
      }
      return '上传文件';
    },
    
    // 上传存储策略
    uploadStorageStrategy() {
      if (this.isDirectUpload) {
        if (this.targetStorageStrategy) {
          return this.targetStorageStrategy;
        }
        return this.currentStorageStrategy === 'shared' ? 'shared' : 'private';
      }
      return this.currentStorageStrategy;
    },
    
    // 主题class - 响应式监听主题变化
    themeClass() {
      // 强制触发响应式更新
      this.themeWatcher;
      
      const body = document.body;
      if (body.classList.contains('theme-dark')) {
        return 'theme-dark';
      } else if (body.classList.contains('theme-starry-sky')) {
        return 'theme-starry-sky';
      }
      return '';
    }
  },
  created() {
    this.getList();
    this.loadFolderTree();
  },
  mounted() {
    // 添加全局点击事件监听器
    document.addEventListener('click', this.hideContextMenu);
    document.addEventListener('scroll', this.hideContextMenu, true);
    
    // 监听主题变化
    this.setupThemeObserver();
  },
  beforeDestroy() {
    // 移除全局事件监听器
    document.removeEventListener('click', this.hideContextMenu);
    document.removeEventListener('scroll', this.hideContextMenu, true);
    
    // 清理定时器
    if (this.clickTimer) {
      clearTimeout(this.clickTimer);
      this.clickTimer = null;
    }
    
    // 移除主题观察器
    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }
    if (this.htmlThemeObserver) {
      this.htmlThemeObserver.disconnect();
    }
  },
  methods: {
    /** 查询文件列表 */
    getList() {
      this.loading = true;
      let apiCall;
      
      if (this.queryParams.storageStrategy === 'shared' && this.queryParams.folderId === -1) {
        // 查看所有共享文件
        apiCall = listAllSharedFiles(this.queryParams);
      } else if (this.queryParams.folderId > 0) {
        // 查询文件夹下的文件
        apiCall = listFilesByFolder(this.queryParams.folderId, this.queryParams);
      } else if (this.queryParams.storageStrategy === 'shared') {
        // 我的共享文件
        apiCall = listMySharedFiles(this.queryParams);
      } else {
        // 我的私有文件
        apiCall = listPrivateFiles(this.queryParams);
      }
      
      apiCall.then(response => {
        this.fileList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 加载文件夹树 */
    async loadFolderTree(preserveExpandedState = false) {
      try {
        let savedExpandedKeys = [];
        
        // 如果需要保持展开状态，先保存当前的展开状态
        if (preserveExpandedState) {
          savedExpandedKeys = [...this.treeExpandedKeys];
          console.log('保存的展开状态:', savedExpandedKeys);
        }
        
        // 同时获取私有和共享文件夹
        const [privateResponse, sharedResponse] = await Promise.all([
          getPrivateFolderTree(),
          getSharedFolderTree()
        ]);
        
        // 构建层级化的文件夹树
        const privateFolders = this.buildFolderTree(privateResponse.data || [], 0);
        const sharedFolders = this.buildFolderTree(sharedResponse.data || [], 0);
        
        // 构建文件夹树
        this.folderTree = [
          {
            folderId: 0,
            folderName: '我的文件',
            storageStrategy: 'private',
            children: privateFolders
          },
          {
            folderId: -1,
            folderName: '共享空间',
            storageStrategy: 'shared',
            children: sharedFolders
          }
        ];
        
        // 根据当前策略构建选项
        let currentFolders = [];
        if (this.currentStorageStrategy === 'private') {
          currentFolders = privateResponse.data || [];
        } else {
          currentFolders = sharedResponse.data || [];
        }
        
        this.folderOptions = this.buildTreeOptions(currentFolders);
        
        // 如果需要保持展开状态，恢复展开状态
        if (preserveExpandedState && savedExpandedKeys.length > 0) {
          // 设置展开的keys，这会触发default-expanded-keys更新
          this.treeExpandedKeys = [...savedExpandedKeys];
          // 强制重新渲染tree组件
          this.treeKey += 1;
          console.log('恢复展开状态:', this.treeExpandedKeys, '树组件key:', this.treeKey);
        } else if (!preserveExpandedState) {
          // 如果不需要保持展开状态，清空展开keys
          this.treeExpandedKeys = [];
          this.treeKey += 1;
        }
      } catch (error) {
        console.error('加载文件夹树失败:', error);
      }
    },
    
    /** 构建树形选项 */
    buildTreeOptions(folders) {
      const options = [];
      options.push({
        folderId: 0,
        folderName: '根目录',
        children: this.buildFolderTree(folders, 0)
      });
      return options;
    },
    
    /** 构建文件夹树 */
    buildFolderTree(folders, parentId) {
      if (!folders || !Array.isArray(folders)) {
        return [];
      }
      
      return folders
        .filter(folder => folder.parentId === parentId)
        .sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0)) // 按排序号排序
        .map(folder => ({
          ...folder,
          children: this.buildFolderTree(folders, folder.folderId)
        }));
    },

    /** 判断文件夹是否在共享文件夹树中 */
    isInSharedFolderTree(folderId) {
      const sharedTree = this.folderTree.find(tree => tree.storageStrategy === 'shared');
      if (!sharedTree || !sharedTree.children) return false;
      
      const findInTree = (folders, id) => {
        for (const folder of folders) {
          if (folder.folderId === id) return true;
          if (folder.children && findInTree(folder.children, id)) return true;
        }
        return false;
      };
      
      return findInTree(sharedTree.children, folderId);
    },
    
    /** 更新文件夹选项 */
    updateFolderOptions(value) {
      if (value) {
        this.folderForm.storageStrategy = value;
      }
      
      // 根据存储策略构建文件夹选项
      const currentStrategy = value || this.folderForm.storageStrategy;
      const treeRoot = this.folderTree.find(tree => tree.storageStrategy === currentStrategy);
      
      if (treeRoot && treeRoot.children) {
        this.folderOptions = this.buildTreeOptions(treeRoot.children);
      } else {
        this.folderOptions = this.buildTreeOptions([]);
      }
      
      // 如果切换了存储策略，重置父文件夹选择
      if (value && value !== this.currentStorageStrategy) {
        this.folderForm.parentId = 0;
      }
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ossId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    
    /** 排序触发事件 */
    handleSortChange(column) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order === "ascending" ? "asc" : "desc";
      this.getList();
    },
    
    /** 文件夹点击事件 */
    handleFolderClick(data) {
      this.currentFolderId = data.folderId;
      this.queryParams.folderId = data.folderId;
      
      if (data.folderId === -1) {
        // 点击共享空间
        this.queryParams.storageStrategy = 'shared';
        this.currentStorageStrategy = 'shared';
        this.updateBreadcrumbs([{ name: '共享空间', folderId: -1 }]);
      } else if (data.folderId === 0) {
        // 点击我的文件根目录
        this.queryParams.storageStrategy = 'private';
        this.currentStorageStrategy = 'private';
        this.updateBreadcrumbs([{ name: '我的文件', folderId: 0 }]);
      } else {
        // 点击普通文件夹，需要判断它属于哪个存储策略
        // 如果文件夹有明确的存储策略，使用它
        if (data.storageStrategy) {
          this.queryParams.storageStrategy = data.storageStrategy;
          this.currentStorageStrategy = data.storageStrategy;
        } else {
          // 否则根据当前上下文判断
          // 如果是从共享空间树中点击的，保持shared策略
          const isFromSharedTree = this.isInSharedFolderTree(data.folderId);
          if (isFromSharedTree) {
            this.queryParams.storageStrategy = 'shared';
            this.currentStorageStrategy = 'shared';
          } else {
            this.queryParams.storageStrategy = 'private';
            this.currentStorageStrategy = 'private';
          }
        }
        this.updateBreadcrumbsForFolder(data);
      }
      
      this.getList();
    },
    
    /** 更新面包屑导航 */
    updateBreadcrumbs(crumbs) {
      this.breadcrumbs = crumbs;
    },
    
    /** 为文件夹更新面包屑导航 */
    updateBreadcrumbsForFolder(folder) {
      // 构建面包屑路径
      const buildPath = (targetId, storageStrategy = 'private') => {
        if (targetId === 0) {
          return storageStrategy === 'shared' 
            ? [{ name: '共享空间', folderId: -1 }]
            : [{ name: '我的文件', folderId: 0 }];
        }
        
        // 根据存储策略选择正确的文件夹树
        const treeRoot = this.folderTree.find(tree => tree.storageStrategy === storageStrategy);
        if (!treeRoot) {
          return storageStrategy === 'shared' 
            ? [{ name: '共享空间', folderId: -1 }]
            : [{ name: '我的文件', folderId: 0 }];
        }
        
        const findFolder = (folderList, id) => {
          for (const f of folderList) {
            if (f.folderId === id) return f;
            if (f.children) {
              const found = findFolder(f.children, id);
              if (found) return found;
            }
          }
          return null;
        };
        
        const found = findFolder(treeRoot.children, targetId);
        if (found) {
          if (found.parentId === 0) {
            const rootName = storageStrategy === 'shared' ? '共享空间' : '我的文件';
            const rootId = storageStrategy === 'shared' ? -1 : 0;
            return [
              { name: rootName, folderId: rootId },
              { name: found.folderName, folderId: found.folderId }
            ];
          } else {
            const parentPath = buildPath(found.parentId, storageStrategy);
            return [...parentPath, { name: found.folderName, folderId: found.folderId }];
          }
        }
        
        const rootName = storageStrategy === 'shared' ? '共享空间' : '我的文件';
        const rootId = storageStrategy === 'shared' ? -1 : 0;
        return [{ name: rootName, folderId: rootId }];
      };
      
      this.breadcrumbs = buildPath(folder.folderId, this.currentStorageStrategy);
    },
    
    /** 面包屑点击事件 */
    handleBreadcrumbClick(item, index) {
      this.breadcrumbs = this.breadcrumbs.slice(0, index + 1);
      this.currentFolderId = item.folderId;
      this.queryParams.folderId = item.folderId;
      
      // 根据点击的文件夹ID设置存储策略
      if (item.folderId === -1) {
        this.queryParams.storageStrategy = 'shared';
        this.currentStorageStrategy = 'shared';
      } else if (item.folderId === 0) {
        this.queryParams.storageStrategy = 'private';
        this.currentStorageStrategy = 'private';
      } else {
        // 保持当前存储策略，但也需要验证一致性
        this.queryParams.storageStrategy = this.currentStorageStrategy;
      }
      
      this.getList();
    },
    
    /** 文件选择（网格视图） */
    handleFileSelect(file) {
      const index = this.selectedFiles.indexOf(file.ossId);
      if (index > -1) {
        this.selectedFiles.splice(index, 1);
      } else {
        this.selectedFiles.push(file.ossId);
      }
      this.ids = this.selectedFiles;
      this.single = this.selectedFiles.length !== 1;
      this.multiple = this.selectedFiles.length === 0;
    },

    /** 文件卡片单击 - 预览文件 */
    handleFileCardClick(file) {
      // 使用定时器来区分单击和双击
      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
        return; // 这是双击的第二次点击，不执行单击逻辑
      }
      
      this.clickTimer = setTimeout(() => {
        // 单击预览文件
        this.handlePreview(file);
        this.clickTimer = null;
      }, 200); // 200ms内如果有第二次点击，则为双击
    },

    /** 文件卡片双击 - 下载文件 */
    handleFileCardDoubleClick(file) {
      // 清除单击定时器
      if (this.clickTimer) {
        clearTimeout(this.clickTimer);
        this.clickTimer = null;
      }
      
      // 双击下载文件
      this.handleDownload(file);
    },
    
    /** 上传文件 */
    handleUpload() {
      // 重置目标文件夹信息
      this.targetFolderId = null;
      this.targetFolderName = '';
      this.targetStorageStrategy = '';
      this.isDirectUpload = false;
      this.uploadDialog = true;
    },

    /** 直接上传文件（根据当前位置确定策略） */
    handleDirectUpload() {
      // 重置目标文件夹信息，使用当前文件夹
      this.targetFolderId = null;
      this.targetFolderName = '';
      this.targetStorageStrategy = '';
      this.isDirectUpload = true;
      this.uploadDialog = true;
    },
    
    /** 上传文件变化 */
    handleUploadChange(value) {
      if (value === "success" || value) {
        // 如果是直接上传到指定文件夹，且不是当前文件夹，显示成功消息
        if (this.isDirectUpload && this.targetFolderId !== null && this.targetFolderId !== this.currentFolderId) {
          // 显示成功消息
          this.$modal.msgSuccess(`文件已成功上传到"${this.targetFolderName}"文件夹`);
        } else if (this.isDirectUpload) {
          // 上传到当前文件夹的成功消息
          this.$modal.msgSuccess("文件上传成功");
        }
        
        // 刷新文件列表和文件夹树（保持展开状态）
        this.getList();
        this.loadFolderTree(true);
        
        // 关闭上传对话框
        if (value === "success") {
          setTimeout(() => {
            this.uploadDialog = false;
          }, 1000);
        }
      } else if (value === "error") {
        // 处理上传失败情况
        this.$modal.msgError("文件上传失败");
      }
    },
    
    /** 创建文件夹 */
    handleCreateFolder() {
      if (this.createFolderLock) {
        console.log('新建文件夹操作被锁定，请稍后再试');
        return;
      }
      
      this.createFolderLock = true;
      setTimeout(() => {
        this.createFolderLock = false;
      }, 1000);
      
      this.reset();
      this.folderDialog = {
        visible: true,
        title: "创建文件夹",
        isAdd: true,
        isDirect: false,
        key: this.folderDialog.key + 1
      };
      
      // 根据当前位置和存储策略设置
      this.folderForm.parentId = this.currentFolderId === -1 ? 0 : this.currentFolderId;
      this.folderForm.storageStrategy = this.currentStorageStrategy;
      this.updateFolderOptions(this.currentStorageStrategy);
    },

    /** 直接创建文件夹（根据当前位置确定策略） */
    handleDirectCreateFolder() {
      if (this.createFolderLock) {
        console.log('新建文件夹操作被锁定，请稍后再试');
        return;
      }
      
      this.createFolderLock = true;
      setTimeout(() => {
        this.createFolderLock = false;
      }, 1000);
      
      this.reset();
      const folderTitle = this.currentStorageStrategy === 'shared' ? '在共享空间创建文件夹' : '在我的文件创建文件夹';
      this.folderDialog = {
        visible: true,
        title: folderTitle,
        isAdd: true,
        isDirect: true,
        key: this.folderDialog.key + 1
      };
      
      // 根据当前位置和存储策略自动设置
      this.folderForm.parentId = this.currentFolderId === -1 ? 0 : this.currentFolderId;
      this.folderForm.storageStrategy = this.currentStorageStrategy;
      this.updateFolderOptions(this.currentStorageStrategy);
    },
    
    /** 编辑文件夹 */
    handleEditFolder(data) {
      this.reset();
      this.folderDialog = {
        visible: true,
        title: "编辑文件夹",
        isAdd: false,
        key: this.folderDialog.key + 1
      };
      this.folderForm = { ...data };
    },
    
    /** 确认文件夹操作 */
    async handleFolderConfirm() {
      if (!this.$refs.folderForm) {
        this.$modal.msgError("表单未正确加载，请重新打开对话框");
        this.createFolderLock = false;
        return;
      }
      
      this.$refs.folderForm.validate(async (valid) => {
        if (valid) {
          try {
            if (this.folderDialog.isAdd) {
              const result = await addOssFolder(this.folderForm);
              this.$modal.msgSuccess("创建文件夹成功");
              
              // 创建成功后，延迟刷新文件夹树以确保后端数据同步（保持展开状态）
              setTimeout(async () => {
                await this.loadFolderTree(true);
                // 如果树组件存在，展开新创建文件夹的父级
                if (this.$refs.folderTreeRef && this.folderForm.parentId > 0) {
                  this.$refs.folderTreeRef.setCurrentKey(this.folderForm.parentId);
                  // 展开父节点
                  const parentNode = this.$refs.folderTreeRef.getNode(this.folderForm.parentId);
                  if (parentNode && !parentNode.expanded) {
                    parentNode.expand();
                  }
                }
              }, 500);
            } else {
              await updateOssFolder(this.folderForm);
              this.$modal.msgSuccess("修改文件夹成功");
              // 修改操作后立即刷新（保持展开状态）
              await this.loadFolderTree(true);
            }
            
            this.folderDialog.visible = false;
            // 刷新当前文件列表
            this.getList();
          } catch (error) {
            this.$modal.msgError("操作失败: " + (error.message || error));
          } finally {
            this.createFolderLock = false;
          }
        } else {
          this.createFolderLock = false;
        }
      });
    },
    
    /** 删除文件夹 */
    handleDeleteFolder(data) {
      this.$confirm('是否确认删除文件夹"' + data.folderName + '"？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        await delOssFolder(data.folderId);
        this.$modal.msgSuccess("删除成功");
        this.loadFolderTree(true);
        if (this.currentFolderId === data.folderId) {
          // 如果删除的是当前所在文件夹，返回到根目录
          this.currentFolderId = 0;
          this.queryParams.folderId = 0;
          this.queryParams.storageStrategy = 'private';
          this.currentStorageStrategy = 'private';
          this.updateBreadcrumbs([{ name: '我的文件', folderId: 0 }]);
        }
        // 刷新文件列表
        this.getList();
      });
    },
    
    /** 查看共享空间 */
    handleViewShared() {
      this.currentFolderId = -1;
      this.queryParams.folderId = -1;
      this.queryParams.storageStrategy = 'shared';
      this.currentStorageStrategy = 'shared';
      this.updateBreadcrumbs([{ name: '共享空间', folderId: -1 }]);
      this.getList();
    },
    
    /** 预览文件 */
    async handlePreview(row) {
      this.previewFile = row;
      this.textContent = null;
      this.codeContent = null;
      
      // 如果是文本或代码文件，尝试加载内容
      if (this.isTextFile(row.originalName) || this.isCodeFile(row.originalName)) {
        try {
          const response = await fetch(row.url, {
            headers: {
              'Content-Type': 'text/plain; charset=utf-8'
            }
          });
          
          if (response.ok) {
            // 获取文件内容的ArrayBuffer
            const arrayBuffer = await response.arrayBuffer();
            
            // 尝试多种编码方式解码，优先使用UTF-8
            let content = '';
            try {
              // 首先尝试UTF-8解码
              const utf8Decoder = new TextDecoder('utf-16le', { fatal: true });
              content = utf8Decoder.decode(arrayBuffer);
            } catch (utf8Error) {
              try {
                // 如果UTF-8失败，尝试GBK编码（中文常用）
                const gbkDecoder = new TextDecoder('gbk', { fatal: true });
                content = gbkDecoder.decode(arrayBuffer);
                console.log('使用GBK编码解码文件:', row.originalName);
              } catch (gbkError) {
                try {
                  // 如果GBK也失败，尝试GB2312编码
                  const gb2312Decoder = new TextDecoder('gb2312', { fatal: true });
                  content = gb2312Decoder.decode(arrayBuffer);
                  console.log('使用GB2312编码解码文件:', row.originalName);
                } catch (gb2312Error) {
                  // 最后使用UTF-8非严格模式（会替换无效字符）
                  const fallbackDecoder = new TextDecoder('utf-8', { fatal: false });
                  content = fallbackDecoder.decode(arrayBuffer);
                  console.warn('使用UTF-8非严格模式解码文件:', row.originalName);
                }
              }
            }
            
            if (this.isTextFile(row.originalName)) {
              this.textContent = content;
            } else if (this.isCodeFile(row.originalName)) {
              this.codeContent = content;
            }
          } else {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
        } catch (error) {
          console.warn('无法加载文件内容:', error);
          if (this.isTextFile(row.originalName)) {
            this.textContent = '加载文件内容失败，请检查文件是否存在或网络连接。';
          } else if (this.isCodeFile(row.originalName)) {
            this.codeContent = '加载代码内容失败，请检查文件是否存在或网络连接。';
          }
        }
      }
      
      this.previewDialog = true;
    },
    
    /** 下载文件 */
    handleDownload(row) {
      try {
        if (!row.url) {
          this.$message.error('文件下载链接无效');
          return;
        }
        
        // 显示下载提示
        this.$message.info('正在准备下载文件：' + (row.originalName || '未知文件'));
        
        // 使用 fetch 获取文件，然后创建blob下载
        // 这样可以确保所有类型的文件都能正确下载而不是在浏览器中打开
        fetch(row.url)
          .then(response => {
            if (!response.ok) {
              throw new Error('网络响应错误');
            }
            return response.blob();
          })
          .then(blob => {
            // 创建对象URL
            const url = window.URL.createObjectURL(blob);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = row.originalName || '下载文件';
            link.style.display = 'none';
            
            // 添加到DOM并点击
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 释放对象URL
            window.URL.revokeObjectURL(url);
            
            this.$message.success('文件下载成功：' + (row.originalName || '未知文件'));
          })
          .catch(error => {
            console.error('下载文件失败:', error);
            // 如果fetch失败，尝试使用传统方式（可能会在新窗口打开）
            this.$message.warning('正在尝试备用下载方式...');
            
            const link = document.createElement('a');
            link.href = row.url;
            link.download = row.originalName || '下载文件';
            link.target = '_blank';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.$message.info('已触发下载，如果未开始下载请检查浏览器设置');
          });
      } catch (error) {
        console.error('下载文件失败:', error);
        this.$message.error('文件下载失败，请稍后重试');
      }
    },
    
    /** 复制下载链接 */
    handleCopyDownloadLink(row) {
      try {
        if (!row.url) {
          this.$message.error('文件下载链接无效');
          return;
        }
        
        // 构建完整的下载链接（包含文件名参数）
        const downloadUrl = `${row.url}?download=true&filename=${encodeURIComponent(row.originalName)}`;
        
        // 使用现代浏览器的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(downloadUrl).then(() => {
            this.$message.success('下载链接已复制到剪贴板');
          }).catch(err => {
            console.error('复制失败:', err);
            this.fallbackCopyText(downloadUrl);
          });
        } else {
          // 备用方案：使用传统方法
          this.fallbackCopyText(downloadUrl);
        }
      } catch (error) {
        console.error('复制下载链接失败:', error);
        this.$message.error('复制失败，请手动复制文件链接');
      }
    },

    /** 备用复制方法（兼容旧浏览器） */
    fallbackCopyText(text) {
      try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 执行复制命令
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          this.$message.success('下载链接已复制到剪贴板');
        } else {
          throw new Error('复制命令执行失败');
        }
      } catch (err) {
        console.error('备用复制方法失败:', err);
        // 显示链接让用户手动复制
        this.$alert(text, '请手动复制下载链接', {
          confirmButtonText: '确定',
          type: 'info'
        });
      }
    },

    /** 分享文件 */
    handleShare(row) {
      // 显示分享文件对话框
      this.shareFile = row;
      this.shareDialog = true;
      // 加载共享文件夹选项
      this.loadSharedFolderOptions();
    },

    /** 加载共享文件夹选项 */
    async loadSharedFolderOptions() {
      try {
        const response = await getSharedFolderTree();
        this.sharedFolderOptions = this.buildShareFolderOptions(response.data || []);
      } catch (error) {
        console.error('加载共享文件夹失败:', error);
      }
    },

    /** 构建分享文件夹选项 */
    buildShareFolderOptions(folders) {
      const options = [{
        folderId: 0,
        folderName: '共享空间根目录',
        children: this.buildShareFolderTree(folders, 0)
      }];
      return options;
    },

    /** 构建分享文件夹树 */
    buildShareFolderTree(folders, parentId) {
      return folders
        .filter(folder => folder.parentId === parentId)
        .map(folder => ({
          folderId: folder.folderId,
          folderName: folder.folderName,
          children: this.buildShareFolderTree(folders, folder.folderId)
        }));
    },

    /** 确认分享文件 */
    async handleShareConfirm() {
      try {
        if (this.shareForm.shareType === 'move') {
          await moveToSharedFolder(this.shareFile.ossId, this.shareForm.targetFolderId);
          this.$modal.msgSuccess("移动到共享空间成功");
        } else {
          await copyToSharedFolder(this.shareFile.ossId, this.shareForm.targetFolderId);
          this.$modal.msgSuccess("复制到共享空间成功");
        }
        this.shareDialog = false;
        this.getList();
      } catch (error) {
        this.$modal.msgError("操作失败: " + error.message);
      }
    },
    
    /** 删除文件 */
    handleDeleteFile(row) {
      this.$confirm('是否确认删除文件"' + row.originalName + '"？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        await delOss(row.ossId);
        this.$modal.msgSuccess("删除成功");
        this.getList();
      });
    },
    
    /** 批量删除 */
    handleDelete() {
      const ossIds = this.ids;
      this.$confirm('是否确认删除选中的文件？', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        for (const ossId of ossIds) {
          await delOss(ossId);
        }
        this.$modal.msgSuccess("删除成功");
        this.getList();
      });
    },
    
    /** 刷新文件夹树 */
    refreshFolderTree() {
      this.loadFolderTree(true);
    },
    
    /** 表单重置 */
    reset() {
      this.folderForm = {
        folderId: null,
        folderName: '',
        parentId: 0,
        storageStrategy: this.currentStorageStrategy,
        orderNum: 0,
        remark: ''
      };
    },
    
    /** 获取文件图标 */
    getFileIcon(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return 'el-icon-document';
      }
      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        // 文档类型
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        rtf: 'el-icon-document',
        odt: 'el-icon-document',
        txt: 'el-icon-document',
        md: 'el-icon-document',
        
        // 表格类型
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        csv: 'el-icon-s-grid',
        ods: 'el-icon-s-grid',
        
        // 演示文稿
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        odp: 'el-icon-present',
        
        // 图片类型
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        bmp: 'el-icon-picture',
        webp: 'el-icon-picture',
        svg: 'el-icon-picture',
        tiff: 'el-icon-picture',
        ico: 'el-icon-picture',
        
        // 视频类型
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mov: 'el-icon-video-camera',
        wmv: 'el-icon-video-camera',
        flv: 'el-icon-video-camera',
        webm: 'el-icon-video-camera',
        mkv: 'el-icon-video-camera',
        '3gp': 'el-icon-video-camera',
        m4v: 'el-icon-video-camera',
        
        // 音频类型
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        flac: 'el-icon-headset',
        aac: 'el-icon-headset',
        ogg: 'el-icon-headset',
        wma: 'el-icon-headset',
        m4a: 'el-icon-headset',
        
        // 压缩文件
        zip: 'el-icon-folder-opened',
        rar: 'el-icon-folder-opened',
        '7z': 'el-icon-folder-opened',
        tar: 'el-icon-folder-opened',
        gz: 'el-icon-folder-opened',
        bz2: 'el-icon-folder-opened',
        xz: 'el-icon-folder-opened',
        
        // 代码文件
        js: 'el-icon-document',
        css: 'el-icon-document',
        html: 'el-icon-document',
        htm: 'el-icon-document',
        json: 'el-icon-document',
        xml: 'el-icon-document',
        java: 'el-icon-document',
        py: 'el-icon-document',
        php: 'el-icon-document',
        cpp: 'el-icon-document',
        c: 'el-icon-document',
        h: 'el-icon-document',
        vue: 'el-icon-document',
        jsx: 'el-icon-document',
        ts: 'el-icon-document',
        
        // CAD和设计文件
        dwg: 'el-icon-edit',
        dxf: 'el-icon-edit',
        step: 'el-icon-edit',
        stp: 'el-icon-edit',
        iges: 'el-icon-edit',
        igs: 'el-icon-edit',
        stl: 'el-icon-edit',
        obj: 'el-icon-edit',
        fbx: 'el-icon-edit',
        
        // 配置文件
        ini: 'el-icon-setting',
        cfg: 'el-icon-setting',
        conf: 'el-icon-setting',
        yaml: 'el-icon-setting',
        yml: 'el-icon-setting',
        
        // 日志文件
        log: 'el-icon-warning',
        
        // 应用程序文件
        apk: 'el-icon-mobile-phone',
        exe: 'el-icon-monitor',
        msi: 'el-icon-monitor',
        dmg: 'el-icon-monitor',
        deb: 'el-icon-monitor',
        rpm: 'el-icon-monitor',
        pkg: 'el-icon-monitor',
        ipa: 'el-icon-mobile-phone'
      };
      return iconMap[ext] || 'el-icon-document';
    },
    
    /** 获取文件类型文本 */
    getFileTypeText(fileType) {
      const typeMap = {
        image: '图片',
        document: '文档',
        video: '视频',
        audio: '音频',
        archive: '压缩包',
        code: '代码',
        cad: 'CAD设计',
        config: '配置',
        app: '应用程序',
        other: '其他'
      };
      return typeMap[fileType] || '未知';
    },
    
    /** 获取文件类型标签类型 */
    getFileTypeTagType(fileType) {
      const typeMap = {
        image: 'success',
        document: 'primary',
        video: 'warning',
        audio: 'info',
        archive: 'danger',
        code: 'success',
        cad: 'warning',
        config: 'info',
        app: 'danger',
        other: ''
      };
      return typeMap[fileType] || '';
    },
    
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size || size === -1) return '未知';
      const units = ['B', 'KB', 'MB', 'GB'];
      let unitIndex = 0;
      let fileSize = parseInt(size);
      
      while (fileSize >= 1024 && unitIndex < units.length - 1) {
        fileSize /= 1024;
        unitIndex++;
      }
      
      return Math.round(fileSize * 100) / 100 + ' ' + units[unitIndex];
    },
    
    /** 判断是否为图片 */
    isImage(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'ico'].includes(ext);
    },
    
    /** 判断是否为PDF */
    isPdf(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ext === 'pdf';
    },

    /** 判断是否为文本文件 */
    isTextFile(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['txt', 'md', 'xml', 'json', 'yml', 'yaml', 'log', 'ini', 'conf', 'config'].includes(ext);
    },

    /** 判断是否为代码文件 */
    isCodeFile(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'less', 'java', 'py', 'cpp', 'c', 'h', 'php', 'go', 'rs', 'rb', 'sql', 'sh', 'bat'].includes(ext);
    },

    /** 获取文件扩展名 */
    getFileExtension(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return '';
      }
      const ext = fileName.split('.').pop();
      return ext ? ext.toUpperCase() : '';
    },

    /** 获取文件类型显示名称 */
    getFileTypeDisplay(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return '未知文件';
      }
      const ext = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        // 图片
        'jpg': 'JPEG图片', 'jpeg': 'JPEG图片', 'png': 'PNG图片', 'gif': 'GIF动图',
        'bmp': 'BMP图片', 'webp': 'WebP图片', 'svg': 'SVG矢量图', 'ico': '图标文件',
        // 文档
        'pdf': 'PDF文档', 'doc': 'Word文档', 'docx': 'Word文档', 
        'xls': 'Excel表格', 'xlsx': 'Excel表格', 'ppt': 'PowerPoint演示', 'pptx': 'PowerPoint演示',
        'txt': '文本文件', 'md': 'Markdown文档', 'rtf': '富文本文档',
        // 代码
        'js': 'JavaScript', 'ts': 'TypeScript', 'jsx': 'React JSX', 'tsx': 'React TSX',
        'vue': 'Vue组件', 'html': 'HTML文档', 'css': '样式表', 'scss': 'Sass样式', 'less': 'Less样式',
        'java': 'Java源码', 'py': 'Python脚本', 'cpp': 'C++源码', 'c': 'C源码', 'h': '头文件',
        'php': 'PHP脚本', 'go': 'Go源码', 'rs': 'Rust源码', 'rb': 'Ruby脚本',
        'sql': 'SQL脚本', 'sh': 'Shell脚本', 'bat': '批处理文件',
        // 数据
        'json': 'JSON数据', 'xml': 'XML文档', 'yml': 'YAML配置', 'yaml': 'YAML配置',
        'csv': 'CSV表格', 'ini': '配置文件', 'conf': '配置文件', 'config': '配置文件',
        // 压缩
        'zip': 'ZIP压缩包', 'rar': 'RAR压缩包', '7z': '7Z压缩包', 'tar': 'TAR归档', 'gz': 'GZ压缩包',
        // 媒体
        'mp4': 'MP4视频', 'avi': 'AVI视频', 'mov': 'QuickTime视频', 'wmv': 'WMV视频',
        'mp3': 'MP3音频', 'wav': 'WAV音频', 'flac': 'FLAC音频', 'aac': 'AAC音频',
        // 应用程序
        'apk': 'Android应用', 'exe': 'Windows可执行文件', 'msi': 'Windows安装包', 
        'dmg': 'macOS磁盘镜像', 'deb': 'Debian安装包', 'rpm': 'RedHat安装包',
        'pkg': 'macOS安装包', 'ipa': 'iOS应用'
      };
      return typeMap[ext] || (ext ? ext.toUpperCase() + '文件' : '未知文件');
    },

    /** 判断是否可以在新窗口打开 */
    canOpenInNewWindow(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      // 支持所有可预览的文件类型在新窗口打开
      return this.isImage(fileName) || this.isPdf(fileName) || this.isTextFile(fileName) || this.isCodeFile(fileName);
    },

    /** 在新窗口打开文件 */
    openInNewWindow(url) {
      try {
        if (!url) {
          this.$message.error('文件链接无效');
          return;
        }
        const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
        if (!newWindow) {
          this.$message.warning('无法打开新窗口，请检查浏览器弹窗拦截设置');
        } else {
          this.$message.success('已在新窗口打开文件');
        }
      } catch (error) {
        console.error('打开新窗口失败:', error);
        this.$message.error('打开新窗口失败，请稍后重试');
      }
    },

    /** 图片加载成功 */
    onImageLoad() {
      // 可以在这里添加加载成功的处理逻辑
    },

    /** 图片加载错误 */
    onImageError() {
      this.$message.error('图片加载失败');
    },

    /** iframe加载成功 */
    onIframeLoad() {
      // 可以在这里添加加载成功的处理逻辑
    },

    /** iframe加载错误 */
    onIframeError() {
      this.$message.error('文件加载失败');
    },

    /** 预览弹窗关闭完成后处理 */
    handlePreviewClosed() {
      // 只有在弹窗完全关闭后才清空数据，避免按钮失效
      this.textContent = null;
      this.codeContent = null;
      this.previewFile = {};
    },

    /** 关闭预览弹窗 */
    closePreview() {
      try {
        // 直接设置关闭状态并清理数据
        this.previewDialog = false;
        this.textContent = null;
        this.codeContent = null;
        this.previewFile = {};
      } catch (error) {
        console.error('关闭预览弹窗失败:', error);
        // 强制关闭
        this.previewDialog = false;
        this.textContent = null;
        this.codeContent = null;
        this.previewFile = {};
      }
    },

    /** 处理关闭预览弹窗 */
    handleClosePreview() {
      this.closePreview();
    },

    /** 预览弹窗中的下载按钮 */
    handlePreviewDownload() {
      if (this.previewFile && this.previewFile.url) {
        this.handleDownload(this.previewFile);
      } else {
        this.$message.error('文件信息无效，无法下载');
      }
    },

    /** 预览弹窗中的新窗口打开按钮 */
    handlePreviewOpenInNewWindow() {
      if (this.previewFile && this.previewFile.url) {
        this.openInNewWindow(this.previewFile.url);
      } else {
        this.$message.error('文件信息无效，无法打开');
      }
    },
    
    /** 图片加载错误处理 */
    handleImageError(event) {
      event.target.style.display = 'none';
      const icon = document.createElement('i');
      icon.className = 'el-icon-picture';
      icon.style.fontSize = '48px';
      icon.style.color = '#ccc';
      event.target.parentNode.appendChild(icon);
    },

    /** 右键菜单相关方法 */
    
    // 显示右键菜单
    showContextMenu(x, y, type, data = null) {
      this.contextMenu = {
        visible: true,
        x,
        y,
        type,
        data
      };
      // 确保菜单在视窗内
      this.$nextTick(() => {
        if (this.$refs.contextMenu) {
          const menuRect = this.$refs.contextMenu.getBoundingClientRect();
          const windowWidth = window.innerWidth;
          const windowHeight = window.innerHeight;
          
          if (x + menuRect.width > windowWidth) {
            this.contextMenu.x = x - menuRect.width;
          }
          if (y + menuRect.height > windowHeight) {
            this.contextMenu.y = y - menuRect.height;
          }
        }
      });
    },
    
    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenu.visible = false;
    },

    // 容器右键
    handleContainerContextMenu(event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'blank');
    },

    // 工具栏右键
    handleToolbarContextMenu(event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'blank');
    },

    // 导航栏右键
    handleNavContextMenu(event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'blank');
    },
    
    // 表格行右键
    handleRowContextMenu(row, column, event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'file', row);
    },
    
    // 表格空白区域右键
    handleTableContextMenu(event) {
      event.preventDefault();
      // 检查是否点击在表格行上
      const target = event.target;
      if (!target.closest('tr') || target.closest('thead')) {
        this.showContextMenu(event.clientX, event.clientY, 'blank');
      }
    },
    
    // 主区域右键
    handleMainAreaContextMenu(event) {
      event.preventDefault();
      const target = event.target;
      // 如果不是在表格行或文件卡片上，显示空白菜单
      if (!target.closest('tr') && !target.closest('.file-card')) {
        this.showContextMenu(event.clientX, event.clientY, 'blank');
      }
    },
    
    // 网格视图文件右键
    handleFileContextMenu(file, event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'file', file);
    },
    
    // 网格视图空白区域右键
    handleGridContextMenu(event) {
      event.preventDefault();
      // 检查是否点击在文件卡片上
      const target = event.target;
      if (!target.closest('.file-card')) {
        this.showContextMenu(event.clientX, event.clientY, 'blank');
      }
    },
    
    // 文件夹树右键
    handleFolderContextMenu(event, data, node, element) {
      event.preventDefault();
      
      // 如果是根节点（我的文件或共享空间），显示空白区域菜单
      if (data.folderId === 0 || data.folderId === -1) {
        // 先切换到对应的存储策略和文件夹
        this.handleFolderClick(data);
        // 然后显示右键菜单
        this.showContextMenu(event.clientX, event.clientY, 'blank');
      } else {
        // 普通文件夹，显示文件夹右键菜单
        this.showContextMenu(event.clientX, event.clientY, 'folder', data);
      }
    },

    // 空状态区域右键
    handleEmptyStateContextMenu(event) {
      event.preventDefault();
      this.showContextMenu(event.clientX, event.clientY, 'blank');
    },
    
    /** 右键菜单操作方法 */
    
    // 右键上传文件
    handleContextUpload() {
      this.hideContextMenu();
      // 根据当前位置确定存储策略，直接上传
      this.handleDirectUpload();
    },
    
    // 右键创建文件夹
    handleContextCreateFolder() {
      this.hideContextMenu();
      // 添加小延迟确保菜单完全隐藏
      setTimeout(() => {
        this.handleDirectCreateFolder();
      }, 50);
    },
    
    // 右键预览文件
    handleContextPreview() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handlePreview(this.contextMenu.data);
      }
    },
    
    // 右键下载文件
    handleContextDownload() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleDownload(this.contextMenu.data);
      }
    },

    // 右键复制下载链接
    handleContextCopyDownloadLink() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleCopyDownloadLink(this.contextMenu.data);
      }
    },

    // 右键在新窗口打开文件
    handleContextOpenInNewWindow() {
      this.hideContextMenu();
      if (this.contextMenu.data && this.contextMenu.data.url) {
        this.openInNewWindow(this.contextMenu.data.url);
      } else {
        this.$message.error('文件信息无效，无法打开');
      }
    },
    
    // 右键分享文件
    handleContextShare() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleShare(this.contextMenu.data);
      }
    },
    
    // 右键删除文件
    handleContextDeleteFile() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleDeleteFile(this.contextMenu.data);
      }
    },
    
    // 右键打开文件夹
    handleContextOpenFolder() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleFolderClick(this.contextMenu.data);
      }
    },
    
    // 右键编辑文件夹
    handleContextEditFolder() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleEditFolder(this.contextMenu.data);
      }
    },
    
    // 右键删除文件夹
    handleContextDeleteFolder() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        this.handleDeleteFolder(this.contextMenu.data);
      }
    },

    // 右键在文件夹内上传文件
    async handleContextUploadToFolder() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        const folderData = this.contextMenu.data;
        
        // 验证文件夹是否存在（除了根节点）
        if (folderData.folderId > 0) {
          try {
            // 记录大整数ID信息，但不阻止操作
            if (typeof folderData.folderId === 'number' && folderData.folderId > Number.MAX_SAFE_INTEGER) {
              console.warn("文件夹ID超出JavaScript安全整数范围，将以字符串形式处理:", folderData.folderId);
            }
            
            // 验证文件夹是否存在
            const folderExists = this.validateFolderExists(folderData.folderId);
            if (!folderExists) {
              this.$modal.msgError("文件夹不存在，请刷新页面后重试");
              this.loadFolderTree(true); // 刷新文件夹树（保持展开状态）
              return;
            }
          } catch (error) {
            console.error("验证文件夹存在性失败:", error);
            this.$modal.msgError("无法验证文件夹状态，请刷新页面后重试");
            return;
          }
        }
        
        // 设置上传目标文件夹信息
        // 如果是根节点（我的文件或共享空间），目标文件夹ID为0
        if (folderData.folderId === -1 || folderData.folderId === 0) {
          this.targetFolderId = 0;
        } else {
          // 保持原始ID形式（字符串或数字）
          this.targetFolderId = folderData.folderId;
        }
        
        this.targetFolderName = folderData.folderName;
        this.targetStorageStrategy = folderData.storageStrategy || 
                                   (folderData.folderId === -1 ? 'shared' : 'private');
        
        console.log('设置上传目标文件夹:', {
          原始文件夹数据: folderData,
          目标文件夹ID: this.targetFolderId,
          目标文件夹名称: this.targetFolderName,
          目标存储策略: this.targetStorageStrategy
        });
        
        // 显示加载状态
        this.loading = true;
        
        // 短暂延迟后打开对话框，确保状态正确更新
        setTimeout(() => {
          this.loading = false;
          // 直接打开上传对话框，不切换当前文件夹
          this.isDirectUpload = true;
          this.uploadDialog = true;
        }, 200);
      }
    },

    // 右键在文件夹内创建新文件夹
    async handleContextCreateFolderInside() {
      this.hideContextMenu();
      if (this.contextMenu.data) {
        const targetFolder = this.contextMenu.data;
        
        // 验证文件夹是否存在（除了根节点）
        if (targetFolder.folderId > 0) {
          try {
            // 记录大整数ID信息，但不阻止操作
            if (typeof targetFolder.folderId === 'number' && targetFolder.folderId > Number.MAX_SAFE_INTEGER) {
              console.warn("文件夹ID超出JavaScript安全整数范围，将以字符串形式处理:", targetFolder.folderId);
            }
            
            const folderExists = this.validateFolderExists(targetFolder.folderId);
            if (!folderExists) {
              this.$modal.msgError("文件夹不存在，请刷新页面后重试");
              this.loadFolderTree(true); // 刷新文件夹树（保持展开状态）
              return;
            }
          } catch (error) {
            console.error("验证文件夹存在性失败:", error);
            this.$modal.msgError("无法验证文件夹状态，请刷新页面后重试");
            return;
          }
        }
        
        // 直接在选中的文件夹内创建，不切换当前文件夹
        this.reset();
        const targetFolderId = targetFolder.folderId === -1 ? 0 : targetFolder.folderId;
        const targetStorageStrategy = targetFolder.storageStrategy || 
                                     (targetFolder.folderId === -1 ? 'shared' : 'private');
        
        const folderTitle = `在"${targetFolder.folderName}"中创建文件夹`;
        this.folderDialog = {
          visible: true,
          title: folderTitle,
          isAdd: true,
          isDirect: true,
          key: this.folderDialog.key + 1
        };
        
        // 设置表单数据：父文件夹为选中的文件夹
        this.folderForm.parentId = targetFolderId;
        this.folderForm.storageStrategy = targetStorageStrategy;
        this.updateFolderOptions(targetStorageStrategy);
      }
    },

    // 右键刷新
    handleContextRefresh() {
      this.hideContextMenu();
      this.getList();
      this.loadFolderTree(true);
    },

    // 文件夹对话框关闭处理
    handleFolderDialogClose() {
      this.createFolderLock = false;
    },

    // 上传对话框关闭处理
    handleUploadDialogClose() {
      // 重置目标文件夹信息
      this.targetFolderId = null;
      this.targetFolderName = '';
      this.targetStorageStrategy = '';
      this.isDirectUpload = false;
    },

    /** 处理文件夹不存在事件 */
    handleFolderNotFound() {
      // 关闭上传对话框
      this.uploadDialog = false;
      // 刷新文件夹树（保持展开状态）
      this.loadFolderTree(true);
      // 显示提示信息
      this.$modal.msgWarning("文件夹已不存在，已刷新文件夹列表");
    },

    /** 获取树节点图标 */
    getTreeNodeIcon(data) {
      if (data.folderId === 0) {
        return 'el-icon-user'; // 我的文件
      } else if (data.folderId === -1) {
        return 'el-icon-share'; // 共享空间
      } else {
        return 'el-icon-folder'; // 普通文件夹
      }
    },

    /** 文件夹双击事件 - 自动展开/收起 */
    handleFolderDoubleClick(data, node) {
      // 直接使用节点对象来切换展开状态
      if (node) {
        if (node.expanded) {
          // 收起节点
          node.collapse();
        } else {
          // 展开节点
          node.expand();
        }
      }
    },

    /** 验证文件夹是否存在 */
    validateFolderExists(folderId) {
      if (!folderId || folderId <= 0) {
        return true; // 根文件夹总是存在
      }
      
      // 转换为字符串进行比较，避免精度问题
      const folderIdStr = String(folderId);
      
      // 在文件夹树中搜索文件夹
      const findInTree = (folders, idStr) => {
        for (const folder of folders) {
          // 转换为字符串比较，避免精度问题
          if (String(folder.folderId) === idStr) {
            return true;
          }
          if (folder.children && findInTree(folder.children, idStr)) {
            return true;
          }
        }
        return false;
      };
      
      // 在所有文件夹树中搜索
      for (const tree of this.folderTree) {
        if (String(tree.folderId) === folderIdStr) {
          return true;
        }
        if (tree.children && findInTree(tree.children, folderIdStr)) {
          return true;
        }
      }
      
      console.log('文件夹验证失败 - 在树中未找到文件夹ID:', folderIdStr);
      console.log('当前文件夹树:', this.folderTree);
      return false;
    },

    /** 检查ID是否为安全整数 */
    isSafeInteger(id) {
      if (typeof id === 'string') {
        const numId = parseInt(id);
        return Number.isSafeInteger(numId);
      }
      return Number.isSafeInteger(id);
    },

    /** 安全地获取文件夹ID */
    getSafeFolderId(folderId) {
      if (!this.isSafeInteger(folderId)) {
        console.warn('不安全的文件夹ID，已重置为根目录:', folderId);
        return 0;
      }
      return typeof folderId === 'string' ? parseInt(folderId) : folderId;
    },

    /** 获取安全的上传文件夹ID（保持字符串形式处理大整数） */
    getSafeUploadFolderId() {
      // 优先使用目标文件夹ID
      let folderId = this.targetFolderId !== null ? this.targetFolderId : this.currentFolderId;
      
      // 处理根目录的特殊情况
      if (folderId === -1) {
        return 0; // 共享空间根目录
      }
      
      // 如果是大整数，保持字符串形式
      if (typeof folderId === 'number' && folderId > Number.MAX_SAFE_INTEGER) {
        console.log('保持大整数文件夹ID为字符串形式:', folderId);
        return String(folderId);
      }
      
      // 如果已经是字符串且是大整数，保持字符串
      if (typeof folderId === 'string') {
        const numId = parseInt(folderId);
        if (numId > Number.MAX_SAFE_INTEGER) {
          console.log('保持大整数文件夹ID为字符串形式:', folderId);
          return folderId; // 保持字符串
        }
        return numId; // 小整数转为数字
      }
      
      return folderId || 0;
    },

    /** 节点展开事件 */
    handleNodeExpand(data, node) {
      const key = data.folderId;
      if (!this.treeExpandedKeys.includes(key)) {
        this.treeExpandedKeys.push(key);
      }
    },

    /** 节点收起事件 */
    handleNodeCollapse(data, node) {
      const key = data.folderId;
      const index = this.treeExpandedKeys.indexOf(key);
      if (index > -1) {
        this.treeExpandedKeys.splice(index, 1);
      }
    },

    /** 设置主题变化观察器 */
    setupThemeObserver() {
      // 使用 MutationObserver 监听 body 的 class 变化
      this.themeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            // 主题发生变化，触发计算属性重新计算
            this.themeWatcher++;
            
            // 强制更新组件，确保主题立即生效
            this.$nextTick(() => {
              this.$forceUpdate();
              console.log('主题已切换并强制更新:', this.themeClass);
            });
          }
        });
      });

      // 开始观察 body 元素的 class 属性变化
      this.themeObserver.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
      });
      
      // 也监听 html 元素，以防主题类添加在 html 上
      this.htmlThemeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            this.themeWatcher++;
            this.$nextTick(() => {
              this.$forceUpdate();
              console.log('HTML主题已切换并强制更新:', this.themeClass);
            });
          }
        });
      });
      
      this.htmlThemeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
    },
  }
};
</script>

<style scoped lang="scss">
.user-file-manager {
  // 默认样式（浅色主题）
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  padding: 20px;
  min-height: calc(100vh - 84px);

  // 工具栏样式
  .toolbar-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

    .modern-button {
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
      border: 2px solid rgba(255, 255, 255, 0.3);
      background: rgba(255, 255, 255, 0.1);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.2);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // 导航栏样式
  .modern-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;

    .breadcrumb-section {
      flex: 1;

      .breadcrumb-item {
        .current-breadcrumb {
          color: #409eff;
          font-weight: 600;
          font-size: 16px;
        }

        .breadcrumb-link {
          cursor: pointer;
          color: #606266;
          transition: color 0.3s ease;
          font-weight: 500;

          &:hover {
            color: #409eff;
            text-decoration: none;
          }
        }
      }
    }

    .nav-actions {
      .view-mode-toggle {
        .toggle-button {
          border-radius: 6px;
          transition: all 0.3s ease;

          &.active {
            background: #409eff;
            color: white;
            transform: scale(1.05);
          }

          &:hover:not(.active) {
            background: #ecf5ff;
            color: #409eff;
          }
        }
      }
    }
  }

  // 卡片样式
  .modern-card {
    border-radius: 12px;
    border: 1px solid #ebeef5;
    background: white;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      transform: translateY(-1px);
    }

    .card-header {
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #303133;
      padding: 15px 0;

      i {
        margin-right: 8px;
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  // 文件夹树样式
  .folder-tree-card {
    height: 600px;
    overflow: hidden;

    .modern-tree {
      height: 100%;
      overflow-y: auto;
      
      // 隐藏滚动条（浅色主题）
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      .modern-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding: 8px 0;
        transition: all 0.3s ease;

        .node-content {
          display: flex;
          align-items: center;
          flex: 1;

          .node-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #409eff;
          }

          .node-label {
            font-weight: 500;
            color: #303133;
          }

          .file-count {
            color: #909399;
            font-size: 12px;
            margin-left: 8px;
            background: #f0f2f5;
            padding: 2px 6px;
            border-radius: 10px;
          }
        }

        .tree-node-actions {
          display: none;
          
          .action-button {
            margin-left: 4px;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background: #f0f2f5;
            }

            &.delete-button:hover {
              background: #fef0f0;
              color: #f56c6c;
            }
          }
        }

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 6px;

          .tree-node-actions {
            display: block;
          }
        }
      }
    }
  }

  // 网格视图样式
  .modern-grid {
    min-height: 400px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    // 隐藏滚动条（浅色主题）
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .file-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 20px;
      padding: 10px 0;

      .modern-file-card {
        border: 2px solid transparent;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:hover {
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          transform: translateY(-4px);
          border-color: #409eff;

          .file-operation-hint {
            opacity: 1;
          }
        }

        &.selected {
          border-color: #409eff;
          background: linear-gradient(135deg, #ecf5ff 0%, #f0f9ff 100%);
        }

        .file-card-content {
          text-align: center;
          position: relative;

          .file-icon-wrapper {
            position: relative;
            margin-bottom: 15px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;

            .file-icon {
              .file-image {
                max-width: 100%;
                max-height: 80px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }

              .file-type-icon {
                font-size: 48px;
                color: #409eff;
                opacity: 0.8;
              }
            }

            .storage-badge {
              position: absolute;
              top: -5px;
              right: -5px;

              .storage-tag {
                border-radius: 8px;
                font-weight: 500;
                border: none;
              }
            }
          }

          .file-info {
            .file-name {
              font-weight: 600;
              color: #303133;
              margin-bottom: 8px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 14px;
            }

            .file-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 5px;

              .file-type-text {
                display: inline-block;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 500;
              }

              .file-size {
                color: #909399;
                font-size: 12px;
                font-weight: 500;
              }
            }

            .file-time {
              color: #c0c4cc;
              font-size: 11px;
            }
          }

          /* 悬浮操作按钮已移除 */
          
          .file-operation-hint {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: all 0.3s ease;
            
            .hint-text {
              background: rgba(64, 158, 255, 0.9);
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 10px;
              white-space: nowrap;
              font-weight: 500;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            }
          }
        }
      }
    }
  }

  // 全局滚动条隐藏（浅色主题）
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  // 空状态样式
  .modern-empty {
    background: white;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-content {
      .empty-icon {
        margin-bottom: 20px;

        i {
          font-size: 64px;
          color: #d3d4d6;
          opacity: 0.6;
        }
      }

      h3 {
        color: #606266;
        margin-bottom: 10px;
        font-weight: 600;
      }

      p {
        color: #909399;
        margin-bottom: 30px;
        font-size: 14px;
      }

      .empty-actions {
        .el-button {
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  // 右键菜单样式
  .context-menu {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #ebeef5;
    padding: 8px 0;
    z-index: 10000 !important; // 提高优先级，确保在所有主题下都能显示
    position: fixed;

    .context-menu-list {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 10px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #606266;

        i {
          margin-right: 8px;
          width: 14px;
          text-align: center;
        }

        &:hover {
          background: #f5f7fa;
          color: #409eff;
        }

        &.danger:hover {
          background: #fef0f0;
          color: #f56c6c;
        }

        &.divider {
          height: 1px;
          background: #e4e7ed;
          margin: 4px 0;
          padding: 0;
          cursor: default;

          &:hover {
            background: #e4e7ed;
          }
        }
      }
    }
  }

  .context-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999 !important; // 确保在所有主题下都能正确工作
  }

  // 文件项样式（用于列表视图）
  .file-item {
    display: flex;
    align-items: center;

    i {
      color: #409eff;
    }
  }

  // 分享对话框样式
  .share-file-info {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #e4e7ed;

    p {
      margin: 5px 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
      }
    }
  }

  // 预览弹窗样式
  ::v-deep .preview-dialog {
    .el-dialog {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: dialogSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      max-width: 95vw !important;
      max-height: 90vh !important;
    }

    .el-dialog__wrapper {
      overflow: hidden;
    }

    .el-dialog__header {
      padding: 0 !important;
      border-bottom: none !important;
      flex-shrink: 0;
    }

    .el-dialog__title {
      display: block !important;
      width: 100% !important;
    }

    .el-dialog__body {
      padding: 0;
      background: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%);
      overflow: hidden;
      height: calc(90vh - 120px);
      max-height: calc(90vh - 120px);
    }
  }

  // 动画定义
  @keyframes dialogSlideIn {
    0% {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px;
    border-bottom: 1px solid rgba(228, 231, 237, 0.8);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    width: 100%;
    box-sizing: border-box;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #409eff 0%, #67c23a 50%, #e6a23c 100%);
    }

    // 左侧功能按钮组
    .left-actions {
      display: flex;
      gap: 8px;
      flex-shrink: 0;
    }

    // 中间标题信息
    .preview-title {
      display: flex;
      align-items: center;
      gap: 20px;
      flex: 1;
      justify-content: center;
      min-width: 0;
      padding: 8px 16px;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(8px);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

      .preview-file-icon {
        font-size: 36px;
        color: #409eff;
        flex-shrink: 0;
        filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
        animation: iconPulse 2s ease-in-out infinite;
      }

      .preview-file-info {
        text-align: center;
        min-width: 0;

        .preview-file-name {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.4;
          word-break: break-all;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .preview-file-meta {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 12px;
          flex-wrap: wrap;

          .preview-file-type {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }

          .preview-file-size,
          .preview-file-time {
            color: #6b7280;
            font-weight: 500;
            padding: 2px 8px;
            background: rgba(243, 244, 246, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(229, 231, 235, 0.6);
          }
        }
      }
    }

    // 右侧关闭按钮
    .right-actions {
      flex-shrink: 0;
    }

    .preview-action-btn {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 8px;
      font-weight: 600;
      font-size: 13px;
      letter-spacing: 0.5px;
      border: none !important;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transition: all 0.6s ease;
        transform: translate(-50%, -50%);
      }

      &:hover::before {
        width: 300px;
        height: 300px;
      }

      &.download-action {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
          background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
        }
      }

      &.open-action {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        box-shadow: 0 4px 15px rgba(103, 194, 58, 0.3);

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow: 0 8px 25px rgba(103, 194, 58, 0.4);
          background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
        }
      }

      &.close-action {
        background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
        box-shadow: 0 4px 15px rgba(245, 108, 108, 0.3);

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow: 0 8px 25px rgba(245, 108, 108, 0.4);
          background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
        }
      }

      &:active {
        transform: translateY(0) scale(0.98);
      }
    }
  }

  .file-preview-container {
    width: 100%;
    height: 70vh;
    max-height: 70vh;
    overflow: hidden;
    background: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%);
    position: relative;
    box-sizing: border-box;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(64, 158, 255, 0.3) 50%, transparent 100%);
      z-index: 1;
    }

    // 图片预览样式
    .image-preview {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      background: #f8f9fa;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;

      .image-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: calc(100% - 40px);
        max-height: calc(70vh - 160px); // 减去头部高度
        width: 100%;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        position: relative;

        .preview-image {
          max-width: 100%;
          max-height: 100%;
          width: auto;
          height: auto;
          object-fit: contain;
          transition: transform 0.3s ease;
          cursor: zoom-in;

          &:hover {
            transform: scale(1.02);
          }
        }
      }
    }

    // PDF预览样式
    .pdf-preview {
      width: 100%;
      height: calc(70vh - 120px); // 减去头部高度
      overflow: hidden;
      padding: 0;

      .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: white;
        display: block;
      }
    }

    // 文本预览样式
    .text-preview {
      width: 100%;
      height: calc(70vh - 120px);
      overflow: hidden;

      .text-content {
        width: 100%;
        height: 100%;
        overflow: auto;

        pre {
          margin: 0;
          padding: 24px;
          background: #f8f9fa;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.6;
          color: #2c3e50;
          white-space: pre-wrap;
          word-wrap: break-word;
          word-break: break-all;
          overflow-wrap: break-word;
          min-height: calc(100% - 48px);
          box-sizing: border-box;
        }
      }
    }

    // 代码预览样式
    .code-preview {
      width: 100%;
      height: calc(70vh - 120px);
      overflow: hidden;

      .code-content {
        width: 100%;
        height: 100%;
        overflow: auto;

        pre {
          margin: 0;
          background: #2d3748;
          min-height: 100%;
          box-sizing: border-box;

          code {
            display: block;
            padding: 24px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #e2e8f0;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
            min-height: calc(100% - 48px);
            box-sizing: border-box;
          }
        }
      }
    }

    // 加载状态样式
    .loading-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 20px;
      color: #6b7280;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #409eff 0%, #67c23a 50%, #e6a23c 100%);
        border-radius: 2px;
        animation: loadingBar 2s ease-in-out infinite;
      }

      i {
        font-size: 40px;
        margin-bottom: 20px;
        color: #409eff;
        animation: loadingSpin 1.5s ease-in-out infinite;
        filter: drop-shadow(0 2px 8px rgba(64, 158, 255, 0.3));
      }

      p {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        opacity: 0.8;
      }
    }

    @keyframes loadingSpin {
      0% { transform: rotate(0deg) scale(1); }
      50% { transform: rotate(180deg) scale(1.1); }
      100% { transform: rotate(360deg) scale(1); }
    }

    @keyframes loadingBar {
      0%, 100% { width: 60px; opacity: 1; }
      50% { width: 100px; opacity: 0.7; }
    }

    // 不支持预览样式
    .no-preview {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      width: 100%;
      height: calc(70vh - 120px);
      box-sizing: border-box;
      overflow: auto;

      .no-preview-content {
        text-align: center;
        max-width: 500px;
        width: 100%;

        .no-preview-icon {
          font-size: 64px;
          color: #c0c4cc;
          margin-bottom: 24px;
        }

        h3 {
          margin: 0 0 16px 0;
          font-size: 20px;
          font-weight: 600;
          color: #303133;
          word-break: break-all;
        }

        .no-preview-message {
          margin: 0 0 32px 0;
          font-size: 16px;
          color: #909399;
        }

        .no-preview-info {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 32px;
          text-align: left;

          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e4e7ed;

            &:last-child {
              border-bottom: none;
            }

            .info-label {
              font-weight: 500;
              color: #606266;
            }

            .info-value {
              color: #303133;
              font-weight: 600;
            }
          }
        }

        .no-preview-actions {
          display: flex;
          gap: 12px;
          justify-content: center;

          .download-btn {
            background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
            border: none;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
            }
          }
        }
      }
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // 深色主题适配
  &.theme-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    .toolbar-wrapper {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      box-shadow: 0 4px 12px rgba(52, 73, 94, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .modern-button {
        backdrop-filter: blur(8px);
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .modern-nav {
      background: var(--base-menu-background);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);

      .breadcrumb-item {
        .current-breadcrumb {
          color: var(--theme-color);
        }

        .breadcrumb-link {
          color: var(--base-text-color-secondary);

          &:hover {
            color: var(--theme-color);
          }
        }
      }

      .nav-actions .view-mode-toggle .toggle-button {
        background: var(--base-button-background);
        border-color: var(--base-border-color);
        color: var(--base-text-color);

        &.active {
          background: var(--theme-color);
          border-color: var(--theme-color);
        }

        &:hover:not(.active) {
          background: var(--theme-color-light);
          color: var(--theme-color);
        }
      }
    }

    .modern-card {
      background: var(--base-menu-background);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
        border-color: var(--theme-color);
        transform: translateY(-1px);
      }

      .card-header {
        color: var(--base-text-color);

        i {
          color: var(--theme-color);
        }
      }
    }

    .folder-tree-card .modern-tree .modern-tree-node {
      .node-content {
        .node-icon {
          color: var(--theme-color);
        }

        .node-label {
          color: var(--base-text-color);
        }

        .file-count {
          color: var(--base-text-color-secondary);
          background: var(--base-fill-color);
        }
      }

      .tree-node-actions .action-button {
        color: var(--base-text-color-secondary);

        &:hover {
          background: var(--base-fill-color);
          color: var(--theme-color);
        }

        &.delete-button:hover {
          background: var(--base-danger-light);
          color: var(--base-danger-color);
        }
      }

      &:hover {
        background: var(--theme-color-light-9);
      }
    }

    .modern-grid,
    .modern-empty {
      background: var(--base-menu-background);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .modern-file-card {
      background: var(--base-card-background);
      border: 2px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(8px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        border-color: var(--theme-color);
        transform: translateY(-2px) scale(1.01);
      }

      &.selected {
        border-color: var(--theme-color);
        background: var(--theme-color-light-9);
        box-shadow: 0 0 15px rgba(var(--theme-color-rgb, 64, 158, 255), 0.3);
      }

      .file-card-content {
        .file-icon-wrapper .file-icon .file-type-icon {
          color: var(--theme-color);
        }

        .file-info {
          .file-name {
            color: var(--base-text-color);
          }

          .file-meta {
            .file-type-text {
              background: var(--theme-color);
            }

            .file-size {
              color: var(--base-text-color-secondary);
            }
          }

          .file-time {
            color: var(--base-text-color-placeholder);
          }
        }
      }
    }

    .modern-empty .empty-content {
      .empty-icon i {
        color: var(--base-text-color-placeholder);
      }

      h3 {
        color: var(--base-text-color-secondary);
      }

      p {
        color: var(--base-text-color-secondary);
      }
    }

    .context-menu {
      background: var(--base-menu-background);
      border-color: var(--base-border-color);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
      z-index: 10000 !important; // 确保深色主题下也能正常显示

      .context-menu-list li {
        color: var(--base-text-color-secondary);

        &:hover {
          background: var(--base-fill-color);
          color: var(--theme-color);
        }

        &.danger:hover {
          background: var(--base-danger-light);
          color: var(--base-danger-color);
        }

        &.divider {
          background: var(--base-border-color);
        }
      }
    }

    .file-item i {
      color: var(--theme-color);
    }

    .share-file-info {
      background: var(--base-fill-color);
      border-color: var(--base-border-color);

      p {
        color: var(--base-text-color-secondary);

        strong {
          color: var(--base-text-color);
        }
      }
    }

    // 预览弹窗主题适配
    ::v-deep .preview-dialog .el-dialog {
      background: var(--base-menu-background);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .preview-header {
      background: linear-gradient(135deg, var(--base-menu-background) 0%, var(--base-card-background) 100%);
      border-bottom-color: var(--base-border-color);

      .preview-title {
        .preview-file-icon {
          color: var(--theme-color);
        }

        .preview-file-info {
          .preview-file-name {
            color: var(--base-text-color);
          }

          .preview-file-meta {
            .preview-file-type {
              background: var(--theme-color);
            }

            .preview-file-size,
            .preview-file-time {
              color: var(--base-text-color-secondary);
            }
          }
        }
      }

      .preview-actions .preview-action-btn {
        &.download-action {
          background: var(--theme-color);
          border-color: var(--theme-color);
          color: white;

          &:hover {
            background: var(--theme-color-light);
            border-color: var(--theme-color-light);
            box-shadow: 0 4px 12px rgba(var(--theme-color-rgb, 64, 158, 255), 0.4);
          }
        }

        &.open-action {
          background: var(--base-success-color);
          border-color: var(--base-success-color);
          color: white;

          &:hover {
            background: var(--base-success-color-light);
            border-color: var(--base-success-color-light);
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
          }
        }

        &.close-action {
          background: var(--base-danger-color);
          border-color: var(--base-danger-color);
          color: white;

          &:hover {
            background: var(--base-danger-color-light);
            border-color: var(--base-danger-color-light);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
          }
        }
      }
    }

    .file-preview-container {
      .image-preview {
        background: var(--base-card-background);
      }

      .text-preview .text-content pre {
        background: var(--base-card-background);
        color: var(--base-text-color);
      }

      .code-preview .code-content pre {
        background: #1a202c;
      }

      .loading-text {
        color: var(--base-text-color-secondary);
      }

      .no-preview .no-preview-content {
        .no-preview-icon {
          color: var(--base-text-color-placeholder);
        }

        h3 {
          color: var(--base-text-color);
        }

        .no-preview-message {
          color: var(--base-text-color-secondary);
        }

        .no-preview-info {
          background: var(--base-card-background);

          .info-item {
            border-bottom-color: var(--base-border-color);

            .info-label {
              color: var(--base-text-color-secondary);
            }

            .info-value {
              color: var(--base-text-color);
            }
          }
        }

        .no-preview-actions .download-btn {
          background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color-light) 100%);
          box-shadow: 0 4px 12px rgba(var(--theme-color-rgb, 64, 158, 255), 0.3);

          &:hover {
            box-shadow: 0 6px 16px rgba(var(--theme-color-rgb, 64, 158, 255), 0.4);
          }
        }
      }
    }

    .file-preview .no-preview {
      i {
        color: var(--base-text-color-placeholder);
      }

      p {
        color: var(--base-text-color-secondary);
      }
    }
  }

  // 星空主题特殊适配
  &.theme-starry-sky {
    background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 100%);

    .toolbar-wrapper {
      background: linear-gradient(135deg, #1e3a8a 0%, #0b0d1a 100%);
      box-shadow: 0 4px 16px rgba(30, 58, 138, 0.6);
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      .modern-button {
        backdrop-filter: blur(10px);
        border-color: rgba(255, 255, 255, 0.3);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }

    .modern-nav {
      background: rgba(26, 31, 60, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(15px);

      .breadcrumb-item {
        .current-breadcrumb {
          color: #ffffff;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .breadcrumb-link {
          color: #c0c0c0;
          transition: all 0.3s ease;

          &:hover {
            color: #ffffff;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
          }
        }
      }

      .nav-actions .view-mode-toggle .toggle-button {
        background: rgba(24, 30, 42, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #ffffff;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
          border-color: #1e3a8a;
          box-shadow: 0 0 15px rgba(30, 58, 138, 0.6);
        }

        &:hover:not(.active) {
          background: rgba(37, 52, 90, 0.8);
          color: #ffffff;
          transform: scale(1.05);
        }
      }
    }

    .modern-card {
      background: rgba(26, 31, 60, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(15px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 24px rgba(30, 58, 138, 0.6);
        border-color: #1e3a8a;
        transform: translateY(-2px);
      }

      .card-header {
        color: #ffffff;

        i {
          color: #ffffff;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
        }
      }
    }

    .folder-tree-card .modern-tree .modern-tree-node {
      .node-content {
        .node-icon {
          color: #ffffff;
        }

        .node-label {
          color: #ffffff;
        }

        .file-count {
          color: #c0c0c0;
          background: #181e2a;
        }
      }

      .tree-node-actions .action-button {
        color: #c0c0c0;

        &:hover {
          background: #25345a;
          color: #ffffff;
        }

        &.delete-button:hover {
          background: #3d1a1a;
          color: #ff6b6b;
        }
      }

      &:hover {
        background: #25345a;
      }
    }

    .modern-grid,
    .modern-empty {
      background: #1a1f3c;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
    }

    .modern-file-card {
      background: rgba(24, 30, 42, 0.8);
      border: 2px solid rgba(255, 255, 255, 0.15);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(12px);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 12px 30px rgba(30, 58, 138, 0.6);
        border-color: #1e3a8a;
        transform: translateY(-4px) scale(1.02);
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.1) 0%, transparent 100%);
          border-radius: inherit;
          pointer-events: none;
        }
      }

      &.selected {
        background: rgba(37, 52, 90, 0.9);
        border-color: #1e3a8a;
        box-shadow: 0 0 20px rgba(30, 58, 138, 0.5);
      }

      .file-card-content {
        .file-icon-wrapper .file-icon .file-type-icon {
          color: #ffffff;
        }

        .file-info {
          .file-name {
            color: #ffffff;
          }

          .file-meta {
            .file-type-text {
              background: linear-gradient(135deg, #1e3a8a 0%, #0b0d1a 100%);
            }

            .file-size {
              color: #c0c0c0;
            }
          }

          .file-time {
            color: #a0a0a0;
          }
        }
      }
    }

    .modern-empty .empty-content {
      .empty-icon i {
        color: #808080;
      }

      h3 {
        color: #c0c0c0;
      }

      p {
        color: #a0a0a0;
      }
    }

    .context-menu {
      background: rgba(26, 31, 60, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.9);
      backdrop-filter: blur(15px);
      z-index: 10000 !important; // 确保星空主题下也能正常显示

      .context-menu-list li {
        color: #c0c0c0;

        &:hover {
          background: #25345a;
          color: #ffffff;
        }

        &.danger:hover {
          background: #3d1a1a;
          color: #ff6b6b;
        }

        &.divider {
          background: #233055;
        }
      }
    }

    .file-item i {
      color: #ffffff;
    }

    .share-file-info {
      background: #181e2a;
      border-color: #233055;

      p {
        color: #c0c0c0;

        strong {
          color: #ffffff;
        }
      }
    }

    // 预览弹窗星空主题适配
    ::v-deep .preview-dialog .el-dialog {
      background: rgba(26, 31, 60, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(15px);
    }

    .preview-header {
      background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(26, 31, 60, 0.9) 100%);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);

      .preview-title {
        .preview-file-icon {
          color: #ffffff;
          filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
        }

        .preview-file-info {
          .preview-file-name {
            color: #ffffff;
            text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
          }

          .preview-file-meta {
            .preview-file-type {
              background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
              box-shadow: 0 0 10px rgba(30, 58, 138, 0.5);
            }

            .preview-file-size,
            .preview-file-time {
              color: #c0c0c0;
            }
          }
        }
      }

      .preview-actions .preview-action-btn {
        backdrop-filter: blur(10px);

        &.download-action {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
          border: none;
          color: #ffffff;

          &:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
            box-shadow: 0 0 15px rgba(30, 58, 138, 0.6);
          }
        }

        &.open-action {
          background: linear-gradient(135deg, #059669 0%, #10b981 100%);
          border: none;
          color: #ffffff;

          &:hover {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            box-shadow: 0 0 15px rgba(5, 150, 105, 0.6);
          }
        }

        &.close-action {
          background: linear-gradient(135deg, #7f1d1d 0%, #ef4444 100%);
          border: none;
          color: #ffffff;

          &:hover {
            background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
            box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
          }
        }
      }
    }

    .file-preview-container {
      background: rgba(26, 31, 60, 0.3);

      .image-preview {
        background: rgba(24, 30, 42, 0.8);

        .image-wrapper {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }

      .pdf-preview .preview-iframe {
        background: rgba(26, 31, 60, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .text-preview .text-content pre {
        background: rgba(24, 30, 42, 0.9);
        color: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .code-preview .code-content pre {
        background: #0f1419;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .loading-text {
        color: #c0c0c0;

        i {
          color: #ffffff;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
        }
      }

      .no-preview .no-preview-content {
        .no-preview-icon {
          color: #808080;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
        }

        h3 {
          color: #ffffff;
          text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
        }

        .no-preview-message {
          color: #c0c0c0;
        }

        .no-preview-info {
          background: rgba(24, 30, 42, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);

          .info-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            .info-label {
              color: #c0c0c0;
            }

            .info-value {
              color: #ffffff;
            }
          }
        }

        .no-preview-actions .download-btn {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.6);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(30, 58, 138, 0.8);
          }
        }
      }
    }

    .file-preview .no-preview {
      i {
        color: #808080;
      }

      p {
        color: #a0a0a0;
      }
    }

         // 滚动条适配
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #181e2a;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: #233055;
      border-radius: 4px;

      &:hover {
        background: #1e3a8a;
      }
    }
  }
}

// 星空主题全局动效
.theme-starry-sky .user-file-manager {
  // 为星空主题添加特殊动画效果
  position: relative;
  // 移除 overflow: hidden，避免影响右键菜单显示

  &::before {
    content: '';
    position: fixed; // 改为fixed定位，避免影响内容布局
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.9), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none; // 确保不干扰鼠标事件
    opacity: 0.3;
    z-index: -1; // 设为负值，确保在所有内容之下
  }

  // 移除子元素的z-index设置，使用自然层级
  
  // 确保右键菜单在最上层
  .context-menu {
    z-index: 10000 !important;
  }
  
  .context-menu-overlay {
    z-index: 9999 !important;
  }

  @keyframes sparkle {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(200px);
    }
  }
}
</style> 
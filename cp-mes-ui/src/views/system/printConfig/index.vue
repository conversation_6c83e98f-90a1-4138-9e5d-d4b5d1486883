<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="标签类型" prop="labelType">
        <el-select v-model="queryParams.labelType" placeholder="请选择标签类型" clearable size="small">
          <el-option label="采购入库" value="purchase"/>
          <el-option label="生产入库" value="production"/>
          <el-option label="混合出库" value="mixed_outbound"/>
          <el-option label="仓库区域" value="warehouse_zone"/>
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option label="启用" value="1"/>
          <el-option label="禁用" value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:printConfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:printConfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:printConfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:printConfig:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-connection"
          size="mini"
          @click="handleBatchTestConnection"
          v-hasPermi="['system:printConfig:query']"
          :loading="batchTesting"
          :disabled="batchTesting || printConfigList.length === 0"
        >{{ batchTesting ? '批量测试中' : '批量测试连接' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="printConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="配置ID" align="center" prop="configId" />
      <el-table-column label="标签类型" align="center" prop="labelType">
        <template slot-scope="scope">
          {{ getLabelTypeName(scope.row.labelType) }}
        </template>
      </el-table-column>
      <el-table-column label="打印机名称" align="center" prop="printerName" />
      <el-table-column label="打印机IP" align="center" prop="printerIp" />
      <el-table-column label="端口" align="center" prop="printerPort" />
      <el-table-column label="完整URL" align="center" prop="fullUrl" show-overflow-tooltip />
      <el-table-column label="超时时间" align="center" prop="timeoutSeconds">
        <template slot-scope="scope">
          {{ scope.row.timeoutSeconds }}秒
        </template>
      </el-table-column>
      <el-table-column label="重试次数" align="center" prop="retryCount">
        <template slot-scope="scope">
          {{ scope.row.retryCount }}次
        </template>
      </el-table-column>
      <el-table-column label="是否默认" align="center" prop="isDefault">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isDefault === true ? 'success' : 'info'">
            {{ scope.row.isDefault === true ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:printConfig:edit']"
              plain
            >修改</el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-connection"
              @click="handleTestConnection(scope.row)"
              v-hasPermi="['system:printConfig:query']"
              :loading="testingConnections.has(scope.row.configId)"
              :disabled="testingConnections.has(scope.row.configId)"
              plain
            >{{ testingConnections.has(scope.row.configId) ? '测试中' : '测试' }}</el-button>
            <el-dropdown 
              v-if="scope.row.isDefault !== true || true"
              @command="(command) => handleMoreAction(command, scope.row)"
              trigger="click"
            >
              <el-button size="mini" type="info" plain>
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item 
                  command="setDefault"
                  v-if="scope.row.isDefault !== true"
                  icon="el-icon-setting"
                >设为默认</el-dropdown-item>
                <el-dropdown-item 
                  command="delete"
                  icon="el-icon-delete"
                  style="color: #f56c6c;"
                >删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改打印接口配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        
        <el-form-item label="标签类型" prop="labelType">
          <el-select v-model="form.labelType" placeholder="请选择标签类型">
            <el-option label="采购入库" value="purchase"/>
            <el-option label="生产入库" value="production"/>
            <el-option label="混合出库" value="mixed_outbound"/>
            <el-option label="仓库区域" value="warehouse_zone"/>
          </el-select>
        </el-form-item>
                 <el-form-item label="打印机名称" prop="printerName">
           <el-input v-model="form.printerName" placeholder="请输入打印机名称" />
         </el-form-item>
         <el-form-item label="打印机IP" prop="printerIp">
           <el-input v-model="form.printerIp" placeholder="请输入打印机IP地址" />
         </el-form-item>
         <el-form-item label="端口" prop="printerPort">
           <el-input-number v-model="form.printerPort" :min="1" :max="65535" placeholder="请输入端口号" />
         </el-form-item>
         <el-form-item label="API端点" prop="apiEndpoint">
           <el-input v-model="form.apiEndpoint" placeholder="请输入API端点，如：/api/data" />
         </el-form-item>
         <el-form-item label="优先级" prop="priority">
           <el-input-number v-model="form.priority" :min="1" :max="100" placeholder="优先级，数字越小优先级越高" />
         </el-form-item>
        <el-form-item label="超时时间" prop="timeoutSeconds">
          <el-input-number v-model="form.timeoutSeconds" :min="1" :max="300" placeholder="请输入超时时间" />
          <span style="margin-left: 8px; color: var(--base-text-color-secondary);">秒</span>
        </el-form-item>
        <el-form-item label="重试次数" prop="retryCount">
          <el-input-number v-model="form.retryCount" :min="0" :max="10" placeholder="请输入重试次数" />
          <span style="margin-left: 8px; color: var(--base-text-color-secondary);">次</span>
        </el-form-item>
                 <el-form-item label="是否默认" prop="isDefault">
           <el-radio-group v-model="form.isDefault">
             <el-radio :label="true">是</el-radio>
             <el-radio :label="false">否</el-radio>
           </el-radio-group>
         </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
                 <el-form-item label="描述" prop="description">
           <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
         </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPrintConfig, getPrintConfig, delPrintConfig, addPrintConfig, updatePrintConfig, testConnection, setDefaultPrinter } from "@/api/system/printConfig";

export default {
  name: "PrintConfig",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 正在测试连接的配置ID集合
      testingConnections: new Set(),
      // 是否正在批量测试
      batchTesting: false,
      // 总条数
      total: 0,
      // 打印接口配置表格数据
      printConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
             // 查询参数
       queryParams: {
         pageNum: 1,
         pageSize: 10,
         labelType: null,
         status: null,
       },
      // 表单参数
      form: {},
             // 表单校验
       rules: {
         labelType: [
           { required: true, message: "标签类型不能为空", trigger: "change" }
         ],
                 printerName: [
           { required: true, message: "打印机名称不能为空", trigger: "blur" }
         ],
         printerIp: [
           { validator: this.validateIp, trigger: "blur" }
         ],
         printerPort: [
           { required: true, message: "端口不能为空", trigger: "blur" },
           { 
             type: "number", 
             min: 1, 
             max: 65535, 
             message: "端口号必须在1-65535之间", 
             trigger: "blur" 
           }
         ],
         apiEndpoint: [
           { required: true, message: "API端点不能为空", trigger: "blur" },
           { 
             pattern: /^\/[a-zA-Z0-9/_-]*$/, 
             message: "API端点必须以'/'开头，只能包含字母、数字、下划线和连字符", 
             trigger: "blur" 
           }
         ]
      },
                   // 标签类型映射（基于二维码打印文档）
      labelTypeMap: {
        'purchase': '采购入库标签',
        'production': '生产入库标签',
        'mixed_outbound': '混合出库标签', 
        'warehouse_zone': '仓库区域标签',
        'storage': '仓储标签',
        'quality': '质检标签',
        'shipping': '出货标签'
      }
    };
  },
  created() {
           this.getList();
     },
     methods: {
       /** 获取标签类型中文名称 */
       getLabelTypeName(labelType) {
         return this.labelTypeMap[labelType] || labelType;
       },
       /** 校验IP地址格式 */
       validateIp(rule, value, callback) {
         if (!value) {
           callback(new Error('打印机IP不能为空'));
         } else {
           const ipPattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
           if (!ipPattern.test(value)) {
             callback(new Error('请输入正确的IP地址格式（如：*************）'));
           } else {
             callback();
           }
         }
       },
    /** 查询打印接口配置列表 */
    getList() {
      this.loading = true;
      listPrintConfig(this.queryParams).then(response => {
        this.printConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
           // 表单重置
       reset() {
         this.form = {
           configId: null,
           labelType: null,
           printerName: null,
           printerIp: null,
           printerPort: 8081,
           apiEndpoint: '/api/data',
           priority: 1,
           timeoutSeconds: 30,
           retryCount: 3,
           isDefault: false,
           status: "1",
           description: null
         };
         this.resetForm("form");
       },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加打印接口配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids
      getPrintConfig(configId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改打印接口配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != null) {
            updatePrintConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPrintConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm('是否确认删除打印接口配置编号为"' + configIds + '"的数据项？').then(function() {
        return delPrintConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/printConfig/export', {
        ...this.queryParams
      }, `打印接口配置_${new Date().getTime()}.xlsx`)
    },
         /** 状态修改 */
     handleStatusChange(row) {
       let text = row.status === "1" ? "启用" : "停用";
       this.$modal.confirm('确认要"' + text + '""' + row.printerName + '"配置吗？').then(function() {
         return updatePrintConfig(row);
       }).then(() => {
         this.$modal.msgSuccess(text + "成功");
       }).catch(function() {
         row.status = row.status === "1" ? "0" : "1";
       });
     },
         /** 测试连接 */
     handleTestConnection(row) {
       // 防止重复点击
       if (this.testingConnections.has(row.configId)) {
         this.$modal.msgWarning('正在测试连接中，请稍候...');
         return;
       }
       
       this.$modal.confirm('是否要测试"' + row.printerName + '"打印机连接？').then(() => {
         // 标记为测试中状态
         this.testingConnections.add(row.configId);
         
         // 显示加载状态
         const loading = this.$loading({
           lock: true,
           text: '正在测试连接...',
           spinner: 'el-icon-loading',
           background: 'rgba(0, 0, 0, 0.7)'
         });
         
         return testConnection(row.configId).finally(() => {
           loading.close();
           // 清除测试中状态
           this.testingConnections.delete(row.configId);
         });
       }).then((response) => {
         if (response.data === true) {
           this.$modal.msgSuccess("连接测试成功！打印机网络连接正常，可以正常发送打印任务。");
         } else {
           this.$modal.msgError("连接测试失败！请检查网络连接、IP地址、端口号等配置。");
         }
               }).catch((error) => {
         console.error('连接测试异常:', error);
         
         // 确保在异常情况下也清除测试中状态
         this.testingConnections.delete(row.configId);
         
         // 根据不同的错误类型提供具体的错误提示
         let errorMessage = "连接测试失败！";
         
         if (error.response) {
           // 服务器返回了错误响应
           const status = error.response.status;
           switch (status) {
             case 404:
               errorMessage = "连接测试失败！找不到指定的配置或接口，请检查配置是否正确。";
               break;
             case 500:
               errorMessage = "连接测试失败！服务器内部错误，请稍后重试或联系管理员。";
               break;
             case 403:
               errorMessage = "连接测试失败！权限不足，请检查您的操作权限。";
               break;
             default:
               errorMessage = `连接测试失败！服务器返回错误（状态码：${status}），请检查配置或稍后重试。`;
           }
         } else if (error.request) {
           // 请求发送失败（网络问题）
           errorMessage = "连接测试失败！网络请求失败，请检查网络连接后重试。";
         } else {
           // 其他错误
           errorMessage = "连接测试失败！发生未知错误，请稍后重试。";
         }
         
         this.$modal.msgError(errorMessage);
       });
     },
         /** 设为默认打印机 */
     handleSetDefault(row) {
       this.$modal.confirm('确认要将"' + row.printerName + '"设为默认打印机吗？').then(function() {
         return setDefaultPrinter(row.configId);
       }).then(() => {
         this.getList();
         this.$modal.msgSuccess("设置成功");
       }).catch(() => {});
     },
     /** 处理更多操作 */
     handleMoreAction(command, row) {
       switch (command) {
         case 'setDefault':
           if (this.$auth.hasPermi('system:printConfig:edit')) {
             this.handleSetDefault(row);
           }
           break;
         case 'delete':
           if (this.$auth.hasPermi('system:printConfig:remove')) {
             this.handleDelete(row);
           }
           break;
       }
     },
     /** 批量测试连接 */
     async handleBatchTestConnection() {
       // 过滤出启用状态的打印机配置
       const enabledConfigs = this.printConfigList.filter(config => config.status === '1');
       
       if (enabledConfigs.length === 0) {
         this.$modal.msgWarning('没有找到启用状态的打印机配置！');
         return;
       }
       
       const confirmText = `确认要测试 ${enabledConfigs.length} 台打印机的连接吗？`;
       
       try {
         await this.$modal.confirm(confirmText);
         
         this.batchTesting = true;
         const results = [];
         let successCount = 0;
         let failureCount = 0;
         
         // 显示进度加载
         const loading = this.$loading({
           lock: true,
           text: `正在批量测试连接 (0/${enabledConfigs.length})...`,
           spinner: 'el-icon-loading',
           background: 'rgba(0, 0, 0, 0.7)'
         });
         
         // 逐个测试打印机连接
         for (let i = 0; i < enabledConfigs.length; i++) {
           const config = enabledConfigs[i];
           
           // 更新进度提示
           loading.setText(`正在批量测试连接 (${i + 1}/${enabledConfigs.length})...`);
           
           try {
             this.testingConnections.add(config.configId);
             const response = await testConnection(config.configId);
             
             const result = {
               printerName: config.printerName,
               printerIp: config.printerIp,
               success: response.data === true,
               message: response.data === true ? '连接成功' : '连接失败'
             };
             
             results.push(result);
             
             if (result.success) {
               successCount++;
             } else {
               failureCount++;
             }
             
           } catch (error) {
             console.error(`测试 ${config.printerName} 连接失败:`, error);
             
             results.push({
               printerName: config.printerName,
               printerIp: config.printerIp,
               success: false,
               message: '连接异常'
             });
             
             failureCount++;
           } finally {
             this.testingConnections.delete(config.configId);
           }
         }
         
         loading.close();
         this.batchTesting = false;
         
         // 显示测试结果
         this.showBatchTestResults(results, successCount, failureCount);
         
       } catch (error) {
         console.error('批量测试异常:', error);
         this.batchTesting = false;
       }
     },
     /** 显示批量测试结果 */
     showBatchTestResults(results, successCount, failureCount) {
       const totalCount = results.length;
       
       // 构建结果HTML
       let resultHtml = `
         <div style="text-align: left;">
           <h4 style="margin-bottom: 15px; color: var(--base-text-color);">
             测试完成：共 ${totalCount} 台，成功 ${successCount} 台，失败 ${failureCount} 台
           </h4>
           <div style="max-height: 300px; overflow-y: auto;">
       `;
       
       results.forEach(result => {
         const statusColor = result.success ? '#67C23A' : '#F56C6C';
         const statusIcon = result.success ? '✓' : '✗';
         
         resultHtml += `
           <div style="padding: 8px 0; border-bottom: 1px solid var(--base-border-color);">
             <div style="display: flex; align-items: center; margin-bottom: 4px;">
               <span style="color: ${statusColor}; font-weight: bold; margin-right: 8px; font-size: 16px;">
                 ${statusIcon}
               </span>
               <span style="font-weight: 500; color: var(--base-text-color);">
                 ${result.printerName}
               </span>
             </div>
             <div style="margin-left: 24px; color: var(--base-text-color-secondary); font-size: 12px;">
               IP: ${result.printerIp} | 状态: ${result.message}
             </div>
           </div>
         `;
       });
       
       resultHtml += `
           </div>
         </div>
       `;
       
       // 使用MessageBox显示详细结果
       this.$msgbox({
         title: '批量连接测试结果',
         dangerouslyUseHTMLString: true,
         message: resultHtml,
         showCancelButton: false,
         confirmButtonText: '确定',
         type: successCount === totalCount ? 'success' : (failureCount === totalCount ? 'error' : 'warning')
       });
       
       // 同时显示简要提示
       if (successCount === totalCount) {
         this.$modal.msgSuccess(`批量测试完成！所有 ${totalCount} 台打印机连接正常。`);
       } else if (failureCount === totalCount) {
         this.$modal.msgError(`批量测试完成！所有 ${totalCount} 台打印机连接失败。`);
       } else {
         this.$modal.msgWarning(`批量测试完成！${successCount} 台连接成功，${failureCount} 台连接失败。`);
       }
     }
  }
};
</script>

<style scoped>
/* 主题适配样式 */
.app-container {
  background: transparent;
  color: var(--base-text-color);
  padding: 20px;
}

/* 表格样式优化 */
.el-table {
  background-color: var(--base-menu-background);
  color: var(--base-text-color);
  border: 1px solid var(--base-border-color);
  border-radius: 6px;
}

/* 表格头部样式 */
.el-table :deep(.el-table__header-wrapper) {
  background-color: var(--base-sidebar-background);
}

.el-table :deep(.el-table__header th) {
  background-color: var(--base-sidebar-background);
  color: var(--base-text-color);
  font-weight: 500;
  border-bottom: 1px solid var(--base-border-color);
}

/* 表格行样式 */
.el-table :deep(.el-table__row > td) {
  border-bottom: 1px solid var(--base-border-color);
}

.el-table :deep(.el-table__row:hover > td) {
  background-color: var(--theme-color-light) !important;
}

/* 表单样式 */
.el-form {
  background: transparent;
  color: var(--base-text-color);
}

/* 弹窗样式 */
.el-dialog {
  background-color: var(--base-menu-background);
  color: var(--base-text-color);
  border: 1px solid var(--base-border-color);
  border-radius: 6px;
}

/* 操作按钮容器样式 */
.operation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.operation-buttons .el-button {
  margin: 0;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.operation-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 下拉菜单样式 */
.el-dropdown {
  margin-left: 4px;
}

.el-dropdown-menu {
  background-color: var(--base-menu-background);
  border: 1px solid var(--base-border-color);
  border-radius: 6px;
}

.el-dropdown-menu :deep(.el-dropdown-menu__item) {
  color: var(--base-text-color);
  transition: all 0.3s ease;
}

.el-dropdown-menu :deep(.el-dropdown-menu__item:hover) {
  background-color: var(--theme-color-light);
  color: var(--theme-color);
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-bottom: 16px;
}

.el-form-item :deep(.el-form-item__label) {
  color: var(--base-text-color);
  font-weight: 500;
}

/* 工具栏样式 */
.mb8 {
  margin-bottom: 16px;
}

.mb8 .el-button {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分页器样式 */
.el-pagination {
  margin-top: 20px;
  text-align: center;
}

.el-pagination :deep(.el-pagination__total),
.el-pagination :deep(.el-pagination__jump) {
  color: var(--base-text-color);
}

/* 状态开关样式 */
.el-switch {
  --el-switch-on-color: var(--theme-color);
  --el-switch-off-color: var(--base-border-color);
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 输入框样式 */
.el-input :deep(.el-input__inner),
.el-select :deep(.el-input__inner),
.el-input-number :deep(.el-input__inner) {
  background-color: var(--base-menu-background);
  border: 1px solid var(--base-border-color);
  color: var(--base-text-color);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.el-input :deep(.el-input__inner):focus,
.el-select :deep(.el-input__inner):focus,
.el-input-number :deep(.el-input__inner):focus {
  border-color: var(--theme-color);
  box-shadow: 0 0 0 2px var(--theme-color-light);
}

/* 深色主题特定样式 */
.theme-dark .app-container {
  background-color: var(--base-menu-background);
}

.theme-dark .el-table {
  background-color: var(--base-menu-background);
}

.theme-dark .el-table :deep(.el-table__header th) {
  background-color: var(--base-sidebar-background);
  border-color: var(--base-border-color);
}

.theme-dark .el-table :deep(.el-table__row:hover > td) {
  background-color: rgba(64, 158, 255, 0.1) !important;
}
</style> 
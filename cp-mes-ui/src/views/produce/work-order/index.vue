<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <div class="search-container">
      <el-card shadow="hover" class="search-card">
        <div slot="header" class="search-header">
          <i class="el-icon-search"></i>
          <span>工单查询</span>
        </div>
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px" class="search-form">
          <el-form-item label="工单编码" prop="orderCode">
            <el-input
              v-model="queryParams.orderCode"
              placeholder="请输入工单编码"
              clearable
              prefix-icon="el-icon-document"
              style="width: 150px;"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="工单类型" prop="orderType">
            <el-select v-model="queryParams.orderType" placeholder="请选择工单类型" clearable style="width: 160px;">
              <el-option label="普通工单" value="NORMAL" />
              <el-option label="加急工单" value="URGENT" />
            </el-select>
          </el-form-item>
          <el-form-item label="工单状态" prop="orderStatus">
            <el-select v-model="queryParams.orderStatus" placeholder="请选择工单状态" clearable style="width: 160px;">
              <el-option label="未开始" value="NEW" />
              <el-option label="执行中" value="IN_PROGRESS" />
              <el-option label="暂停中" value="PAUSED" />
              <el-option label="已完成" value="COMPLETED" />
            </el-select>
          </el-form-item>
          <el-form-item label="产品名称" prop="productName">
            <el-input
              v-model="queryParams.productName"
              placeholder="请输入名称"
              clearable
              prefix-icon="el-icon-goods"
              style="width: 140px;"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="下发状态" prop="orderDispatchStatus">
            <el-select v-model="queryParams.orderDispatchStatus" placeholder="请选择下发状态" clearable style="width: 160px;">
              <el-option label="未下发" value="NOT_DISPATCHED" />
              <el-option label="已下发" value="DISPATCHED" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间状态" prop="dueType">
            <el-select v-model="queryParams.dueType" placeholder="请选择时间状态" clearable style="width: 160px;">
              <el-option label="临期" value="1" />
              <el-option label="逾期" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计面板 -->
    <div class="stats-container">
      <el-row :gutter="16">
        <el-col :span="4">
          <el-card class="stats-card stats-total">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ total }}</div>
                <div class="stats-label">总工单数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card stats-new">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ getStatusCount('NEW') }}</div>
                <div class="stats-label">未开始</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card stats-progress">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-loading"></i>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ getStatusCount('IN_PROGRESS') }}</div>
                <div class="stats-label">执行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card stats-paused">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-video-pause"></i>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ getStatusCount('PAUSED') }}</div>
                <div class="stats-label">暂停中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card stats-completed">
            <div class="stats-content">
              <div class="stats-icon">
                <i class="el-icon-check"></i>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ getStatusCount('COMPLETED') }}</div>
                <div class="stats-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar-container">
      <el-card shadow="never" class="toolbar-card">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >创建工单</el-button>
          </div>
          <div class="toolbar-right">
            <el-button icon="el-icon-refresh" circle @click="handleQuery" title="刷新"></el-button>
            <el-button icon="el-icon-setting" circle title="设置"></el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 工单列表表格 -->
    <div class="table-container">
      <el-card shadow="never" class="table-card">
        <div slot="header" class="table-header">
          <div class="table-title">
            <i class="el-icon-tickets"></i>
            <span>工单列表</span>
          </div>
          <div class="table-stats">
            <span>共 {{ total }} 条记录</span>
          </div>
        </div>
        <el-table
          v-loading="loading"
          :data="workOrderList"
          :height="tableHeight"
          stripe
          border
          class="work-order-table"
        >

      <el-table-column label="工单ID" align="center" prop="orderId" width="100" fixed="left" sortable />
      <el-table-column label="工单编码" align="center" prop="orderCode" width="250" fixed="left" show-overflow-tooltip />
      <el-table-column label="工单类型" align="center" prop="orderType" width="150" sortable>
        <template slot-scope="scope">
          <el-tag :type="scope.row.orderType === 'URGENT' ? 'danger' : 'primary'" size="small">
            {{ scope.row.orderType === 'URGENT' ? '加急' : '普通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工单状态" align="center" prop="orderStatus" width="150" sortable>
        <template slot-scope="scope">
          <el-tag :type="getStatusColor(scope.row.orderStatus)" size="small">
            {{ getStatusName(scope.row.orderStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="下发状态" align="center" prop="orderDispatchStatus" width="120" sortable>
        <template slot-scope="scope">
          <el-tag :type="getDispatchStatusColor(scope.row.orderDispatchStatus)" size="small">
            {{ getDispatchStatusName(scope.row.orderDispatchStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="产品信息" align="left" width="auto" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="product-info-inline">
            <span v-for="(product, index) in scope.row.products" :key="index" class="product-tag">
              {{ product.productName }}--{{ product.styleName }}
              <span v-if="index < scope.row.products.length - 1" class="product-separator">、</span>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品数量" align="center" width="130">
        <template slot-scope="scope">
          <span>{{ getProductCount(scope.row.products) }}</span>
        </template>
      </el-table-column>


      <el-table-column label="期望完成时间" align="center" prop="orderExpectedTime" width="190" sortable>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.orderExpectedTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="320" fixed="right">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
              class="operation-btn"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-if="canModifyOrder(scope.row.orderStatus)"
              class="operation-btn"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleDispatchOrder(scope.row)"
              v-if="canDispatchOrder(scope.row)"
              class="operation-btn operation-btn-primary"
            >下发</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-video-pause"
              @click="handlePauseOrder(scope.row)"
              v-if="canPauseOrder(scope.row.orderStatus)"
              class="operation-btn operation-btn-warning"
            >暂停</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-video-play"
              @click="handleResumeOrder(scope.row)"
              v-if="canResumeOrder(scope.row.orderStatus)"
              class="operation-btn operation-btn-success"
            >开始</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-if="canDeleteOrder(scope.row.orderStatus)"
              class="operation-btn operation-btn-danger"
            >删除</el-button>
          </div>
        </template>
      </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 创建工单对话框 -->
    <el-dialog
      title="创建新工单"
      :visible.sync="createDialogVisible"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <create-work-order-form
        ref="createWorkOrderForm"
        @close="createDialogVisible = false"
        @success="handleCreateSuccess"
      />
    </el-dialog>

    <!-- 工单详情对话框 -->
    <el-dialog title="工单详情" :visible.sync="detailDialogVisible" width="1400px" append-to-body>
      <div v-loading="detailLoading" element-loading-text="加载中...">
        <div v-if="currentOrder && !detailLoading">
          <!-- 工单基本信息 -->
          <el-descriptions title="工单基本信息" :column="3" border class="detail-section">
            <el-descriptions-item label="工单ID">{{ currentOrder.orderId }}</el-descriptions-item>
            <el-descriptions-item label="工单编码">{{ currentOrder.orderCode }}</el-descriptions-item>
            <el-descriptions-item label="工单类型">
              <el-tag :type="currentOrder.orderType === 'URGENT' ? 'danger' : 'primary'" size="small">
                {{ currentOrder.orderType === 'URGENT' ? '加急工单' : '普通工单' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="工单状态">
              <el-tag :type="getStatusColor(currentOrder.orderStatus)" size="small">
                {{ getStatusName(currentOrder.orderStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(currentOrder.orderCreatedTime) }}</el-descriptions-item>
            <el-descriptions-item label="产品数量">{{ getProductCount(currentOrder.products) }}</el-descriptions-item>
          </el-descriptions>

          <!-- 产品信息列表 -->
          <div class="detail-section">
            <h3 class="section-title">产品信息列表</h3>
            <el-table :data="currentOrder.products || []" border stripe class="product-table">
              <el-table-column prop="productId" label="产品ID" width="200" align="center" />
              <el-table-column label="产品信息" width="220" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div class="product-info-detail">
                    <div class="product-name">{{ scope.row.productName }}</div>
                    <div class="product-style" v-if="scope.row.styleName">{{ scope.row.styleName }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="orderItemQuantity" label="生产数量" width="150" align="center" />
              <el-table-column prop="inventory" label="库存数量" width="100" align="center" />
              <el-table-column label="工序进度" width="200" align="center">
                <template slot-scope="scope">
                  <el-progress
                    :percentage="getStepProgress(scope.row.stepTasks)"
                    :color="getProgressColor(getStepProgress(scope.row.stepTasks))"
                    :stroke-width="6"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleProductDetail(scope.row)"
                    icon="el-icon-view"
                  >查看工序</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 操作按钮 -->
          <div class="detail-actions">
            <el-button
              :type="getReportWorkButtonType(currentOrder)"
              :icon="getReportWorkButtonIcon(currentOrder)"
              @click="handleReportWork(currentOrder)"
              :loading="reportWorkLoading"
              :disabled="!canReportWork(currentOrder)"
            >{{ getReportWorkButtonText(currentOrder) }}</el-button>
            <el-button @click="detailDialogVisible = false">关闭</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 修改状态对话框 -->
    <el-dialog
      title="修改工单状态"
      :visible.sync="statusDialogVisible"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="statusForm"
        :model="statusForm"
        label-width="100px"
        v-loading="editLoading"
        element-loading-text="保存中..."
      >
        <el-form-item label="工单编码">
          <el-input v-model="statusForm.orderCode" disabled />
        </el-form-item>
        <el-form-item label="当前状态">
          <el-tag :type="getStatusColor(statusForm.currentStatus)" size="medium">
            {{ getStatusName(statusForm.currentStatus) }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态" prop="newStatus">
          <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%;" required>
            <el-option label="未开始" value="NEW" />
            <el-option label="执行中" value="IN_PROGRESS" />
            <el-option label="暂停中" value="PAUSED" />
            <el-option label="已完成" value="COMPLETED" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleStatusSubmit"
          :loading="editLoading"
        >保存</el-button>
      </div>
    </el-dialog>

    <!-- 编辑工单对话框 -->
    <el-dialog
      title="编辑工单"
      :visible.sync="editDialogVisible"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
        v-loading="editLoading"
        element-loading-text="保存中..."
      >
        <el-form-item label="工单编码">
          <el-input v-model="editForm.orderCode" disabled />
        </el-form-item>
        <el-form-item label="工单类型" prop="orderType">
          <el-select v-model="editForm.orderType" placeholder="请选择工单类型" style="width: 100%;">
            <el-option label="普通工单" value="NORMAL" />
            <el-option label="加急工单" value="URGENT" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleEditSubmit"
          :loading="editLoading"
        >保存</el-button>
      </div>
    </el-dialog>

    <!-- 产品工序详情对话框 -->
    <el-dialog
      title="产品工序详情"
      :visible.sync="productDetailDialogVisible"
      width="1300px"
      append-to-body
    >
      <div v-if="currentProduct">
        <!-- 产品基本信息 -->
        <el-descriptions title="产品基本信息" :column="3" border class="detail-section">
          <el-descriptions-item label="产品ID">{{ currentProduct.productId }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ currentProduct.productName }}--{{currentProduct.styleName}}</el-descriptions-item>
          <el-descriptions-item label="生产数量">{{ currentProduct.orderItemQuantity }}</el-descriptions-item>
                                <el-descriptions-item label="库存数量">{{ currentProduct.inventory }}</el-descriptions-item>
           <el-descriptions-item label="任务级别">
            <el-tag :type="currentProduct.taskLevel === 'URGENT' ? 'danger' : 'primary'" size="small">
              {{ currentProduct.taskLevel === 'URGENT' ? '加急' : '普通' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ currentProduct.createdName }}</el-descriptions-item>
        </el-descriptions>

        <!-- 工序任务列表 -->
        <div class="detail-section">
          <h3 class="section-title">工序任务列表</h3>
          <el-table :data="currentProduct.stepTasks || []" border stripe class="step-task-table">
            <el-table-column prop="stepNumber" label="工序编号" width="120" align="center" />
            <el-table-column prop="stepName" label="工序名称" width="150" show-overflow-tooltip />
            <el-table-column prop="assignee" label="负责人" width="120" align="center" />
            <el-table-column label="完成状态" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="getStepStatusColor(scope.row.isCompleted)" size="small">
                  {{ getStepStatusName(scope.row.isCompleted) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="缺陷信息" width="200" align="center">
              <template slot-scope="scope">
                <div v-if="hasDefectInfo(scope.row.defectInfo)" class="defect-info">
                  <el-tag
                    v-for="(value, key) in getFilteredDefectInfo(scope.row.defectInfo)"
                    :key="key"
                    type="danger"
                    size="mini"
                    class="defect-tag"
                  >
                    {{ key }}: {{ value }}
                  </el-tag>
                </div>
                <span v-else class="no-defect">无缺陷</span>
              </template>
            </el-table-column>
            <el-table-column prop="stepTaskCreateTime" label="开始时间" width="180" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.stepTaskCreateTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="expectedAt" label="期望完成时间" width="180" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.expectedAt) }}
              </template>
            </el-table-column>
            <el-table-column prop="completedAt" label="实际完成时间" width="180" align="center">
              <template slot-scope="scope">
                {{ parseTime(scope.row.completedAt) || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="productDetailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  updateOrderStatus,
  getOrderDetailPage,
  updateOrderType
} from '@/api/jenasi/orders';
import {
  deleteOrder,
  batchDeleteOrderItems,
  deleteOrderTask,
  deleteStepTasks,
  getOrderDetailById,
  issueOrder
} from '@/api/jenasi/workOrder';
import CreateWorkOrderForm from './create-form.vue';

export default {
  name: "WorkOrderIndex",
  components: {
    CreateWorkOrderForm
  },
  data() {
    return {
      // 表格数据加载状态
      loading: true,
      // 总条数
      total: 0,
      // 工单表格数据
      workOrderList: [],
      // 表格高度
      tableHeight: window.innerHeight - 280,
      // 创建工单对话框显示状态
      createDialogVisible: false,
      // 详情对话框显示状态
      detailDialogVisible: false,
      // 产品详情对话框显示状态
      productDetailDialogVisible: false,
      // 编辑对话框显示状态
      editDialogVisible: false,
      // 详情加载状态
      detailLoading: false,
      // 报工加载状态
      reportWorkLoading: false,
      // 编辑加载状态
      editLoading: false,
      // 删除加载状态
      deleteLoading: false,
      // 下发加载状态
      dispatchLoading: false,
      // 当前查看的工单
      currentOrder: null,
      // 当前查看的产品
      currentProduct: null,
      // 当前编辑的工单
      editForm: {
        orderId: null,
        orderCode: '',
        orderType: ''
      },

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        orderType: null,
        orderStatus: null,
        productName: null,
        orderDispatchStatus: null,
        dueType: null
      },

      // 编辑表单验证规则
      editRules: {
        orderType: [
          { required: true, message: '请选择工单类型', trigger: 'change' }
        ]
      },
      // 状态对话框显示状态
      statusDialogVisible: false,
      // 状态修改表单
      statusForm: {
        orderId: null,
        orderCode: '',
        currentStatus: '',
        newStatus: ''
      }
    };
  },
  created() {
    this.handleRouteParams(); //自动设置查询参数(全局导航生产中)
    this.getList();
    this.handleResize();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    // 监听路由变化，支持动态查询参数设置
    '$route'(to, from) {
      if (to.path === '/produce/sheet' && to.query !== from.query) {
        this.handleRouteParams();
        this.getList();
      }
    }
  },
  methods: {
    /** 全局导航（查看生产中）自动设置查询参数 */
    handleRouteParams() {
      const query = this.$route.query;
      // 如果路由中包含orderStatus参数，自动设置查询条件
      if (query.orderStatus) {
        this.queryParams.orderStatus = query.orderStatus;
       // this.$message.success(`已自动筛选${this.getStatusName(query.orderStatus)}状态的工单`);
      }
      if (query.dueType) {
        this.queryParams.dueType = query.dueType;
        // this.$message.success(`已自动筛选${this.getStatusName(query.orderStatus)}状态的工单`);
      }
      // 如果路由中包含其他查询参数，也可以在这里处理
    },
    /** 查询工单列表 */
    getList() {
      this.loading = true;
      const query = { ...this.queryParams };

      // 调用工单详细信息分页接口
      getOrderDetailPage(query).then(response => {
        this.workOrderList = response.data.records || [];
        this.total = response.data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$modal.msgError('获取工单列表失败');
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      // 重置查询参数到初始状态
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        orderType: null,
        orderStatus: null,
        productName: null,
        orderDispatchStatus: null
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },



    /** 新增按钮操作 */
    handleAdd() {
      this.createDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.createWorkOrderForm) {
          this.$refs.createWorkOrderForm.reset();
        }
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.editForm = {
        orderId: row.orderId,
        orderCode: row.orderCode,
        orderType: row.orderType
      };
      this.editDialogVisible = true;
    },

    /** 查看详情 */
    handleDetail(row) {
      this.currentOrder = row;
      this.detailDialogVisible = true;
    },

    /** 查看产品详情 */
    handleProductDetail(product) {
      this.currentProduct = product;
      this.productDetailDialogVisible = true;
    },





    /** 创建工单成功回调 */
    handleCreateSuccess() {
      this.createDialogVisible = false;
      this.getList();
    },

    /** 获取产品数量 */
    getProductCount(products) {
      return products ? products.length : 0;
    },

        /** 获取整体工序进度 */
    getOverallProgress(products) {
      if (!products || products.length === 0) return 0;

      let totalSteps = 0;
      let completedSteps = 0;

      products.forEach(product => {
        if (product.stepTasks && product.stepTasks.length > 0) {
          totalSteps += product.stepTasks.length;
          completedSteps += product.stepTasks.filter(step => step.isCompleted === 2).length;
        }
      });

      return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
    },

    /** 获取单个产品的工序进度 */
    getStepProgress(stepTasks) {
      if (!stepTasks || stepTasks.length === 0) return 0;
      const completedCount = stepTasks.filter(task => task.isCompleted === 2).length;
      return Math.round((completedCount / stepTasks.length) * 100);
    },

    /** 获取进度条颜色 */
    getProgressColor(percentage) {
      if (percentage === 100) return '#67c23a';
      if (percentage >= 80) return '#e6a23c';
      if (percentage >= 50) return '#409eff';
      return '#f56c6c';
    },

    /** 获取状态颜色 */
    getStatusColor(status) {
      const colorMap = {
        'NEW': 'info',
        'IN_PROGRESS': 'warning',
        'COMPLETED': 'success',
        'PAUSED': 'warning'
      };
      return colorMap[status] || 'info';
    },

    /** 获取状态名称 */
    getStatusName(status) {
      const nameMap = {
        'NEW': '未开始',
        'IN_PROGRESS': '执行中',
        'COMPLETED': '已完成',
        'PAUSED': '暂停中'
      };
      return nameMap[status] || status;
    },

    /** 获取下发状态颜色 */
    getDispatchStatusColor(status) {
      const colorMap = {
        'NOT_DISPATCHED': 'warning',
        'DISPATCHED': 'success'
      };
      return colorMap[status] || 'info';
    },

    /** 获取下发状态名称 */
    getDispatchStatusName(status) {
      const nameMap = {
        'NOT_DISPATCHED': '未下发',
        'DISPATCHED': '已下发'
      };
      return nameMap[status] || status;
    },



    /** 获取工序状态颜色 */
    getStepStatusColor(isCompleted) {
      const colorMap = {
        0: 'info',      // 未开始
        1: 'warning',   // 执行中
        2: 'success'    // 已完成
      };
      return colorMap[isCompleted] || 'info';
    },

    /** 获取工序状态名称 */
    getStepStatusName(isCompleted) {
      const nameMap = {
        0: '未开始',
        1: '执行中',
        2: '已完成'
      };
      return nameMap[isCompleted] || '未开始';
    },

    /** 检查是否可以修改工单 */
    canModifyOrder(status) {
      // 只有已完成状态不能修改工单，其他状态都可以修改
      return status !== 'COMPLETED';
    },

    /** 检查是否可以删除工单 */
    canDeleteOrder(status) {
      // 只有未开始的工单可以删除
      return status === 'NEW';
    },

    /** 检查是否可以修改状态 */
    canModifyStatus(status) {
      // 已完成的工单不能修改状态
      return status !== 'COMPLETED';
    },

    /** 检查是否可以暂停工单 */
    canPauseOrder(status) {
      // 只有执行中的工单可以暂停
      return status === 'IN_PROGRESS';
    },

    /** 检查是否可以恢复工单 */
    canResumeOrder(status) {
      // 只有暂停中的工单可以恢复
      return status === 'PAUSED';
    },

    /** 检查是否可以下发工单 */
    canDispatchOrder(order) {
      // 只有未下发的工单可以下发
      return order.orderDispatchStatus === 'NOT_DISPATCHED';
    },



    /** 处理窗口大小变化 */
    handleResize() {
      this.tableHeight = window.innerHeight - 280;
    },

    /** 获取指定状态的工单数量 */
    getStatusCount(status) {
      return this.workOrderList.filter(order => order.orderStatus === status).length;
    },

    /** 检查是否可以报工 */
    canReportWork(order) {
      if (!order || !order.products || order.products.length === 0) return false;

      // 工单状态为已完成时不能报工
      if (order.orderStatus === 'COMPLETED') return false;

      // 检查所有产品的工序进度是否都达到100%
      return order.products.every(product => {
        if (!product.stepTasks || product.stepTasks.length === 0) return false;
        return this.getStepProgress(product.stepTasks) === 100;
      });
    },

    /** 获取报工按钮文本 */
    getReportWorkButtonText(order) {
      if (!order) return '报工';
      if (order.orderStatus === 'COMPLETED') return '已报工';
      return '报工';
    },

    /** 获取报工按钮类型 */
    getReportWorkButtonType(order) {
      if (!order) return 'success';
      if (order.orderStatus === 'COMPLETED') return 'info';
      return 'success';
    },

    /** 获取报工按钮图标 */
    getReportWorkButtonIcon(order) {
      if (!order) return 'el-icon-check';
      if (order.orderStatus === 'COMPLETED') return 'el-icon-circle-check';
      return 'el-icon-check';
    },

    /** 报工操作 */
    handleReportWork(order) {
      // 已完成的工单不能再报工
      if (order.orderStatus === 'COMPLETED') {
        return;
      }

      // 检查工单状态
      if (order.orderStatus !== 'IN_PROGRESS') {
        this.$modal.msgWarning('只有执行中的工单才能进行报工操作');
        return;
      }

      // 检查工序进度
      if (!this.canReportWork(order)) {
        this.$modal.msgWarning('所有产品的工序进度必须达到100%才能报工');
        return;
      }

      this.$modal.confirm('确认对该工单进行报工操作？报工后工单状态将变更为已完成。').then(() => {
        this.reportWorkLoading = true;
        return updateOrderStatus(order.orderId, 'COMPLETED');
      }).then(() => {
        this.$modal.msgSuccess("报工成功，工单状态已更新为已完成");
        this.reportWorkLoading = false;
        this.detailDialogVisible = false;
        this.getList(); // 刷新列表
      }).catch(() => {
        this.reportWorkLoading = false;
      });
    },

    /** 编辑表单提交 */
    handleEditSubmit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          this.editLoading = true;
          updateOrderType(this.editForm.orderId, this.editForm.orderType)
            .then(() => {
              this.$modal.msgSuccess('工单类型修改成功');
              this.editDialogVisible = false;
              this.getList(); // 刷新列表
            })
            .catch(() => {
              this.$modal.msgError('工单类型修改失败');
            })
            .finally(() => {
              this.editLoading = false;
            });
        }
      });
    },

    /** 删除工单操作 */
    handleDelete(row) {
      // 检查工单状态
      if (row.orderStatus !== 'NEW') {
        this.$modal.msgWarning('只有未开始的工单才能删除');
        return;
      }

      this.$modal.confirm(`确认删除工单"${row.orderCode}"？删除后将无法恢复。`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.deleteLoading = true;
        return this.deleteWorkOrder(row.orderId);
      }).then(() => {
        this.$modal.msgSuccess('工单删除成功');
        this.getList(); // 刷新列表
      }).catch((error) => {
        if (error !== 'cancel') {
          console.error('删除工单失败:', error);
          this.$modal.msgError('删除工单失败，请稍后重试');
        }
      }).finally(() => {
        this.deleteLoading = false;
      });
    },

    /** 删除工单及其关联数据 */
    async deleteWorkOrder(orderId) {
      try {
        // 1. 首先获取工单详细信息
        const orderDetailResponse = await getOrderDetailById(orderId);
        const orderDetail = orderDetailResponse.data;

        if (!orderDetail) {
          throw new Error('无法获取工单详细信息');
        }

        // 2. 提取需要删除的数据ID
        const orderItemIds = [];
        const orderTaskIds = [];
        const stepTaskIds = [];

        if (orderDetail.products && orderDetail.products.length > 0) {
          orderDetail.products.forEach(product => {
            // 收集产品ID（orderItemId）
            if (product.orderItemId) {
              orderItemIds.push(product.orderItemId);
            }

            // 收集任务ID和工序ID
            if (product.stepTasks && product.stepTasks.length > 0) {
              product.stepTasks.forEach(stepTask => {
                if (stepTask.stepTaskId) {
                  stepTaskIds.push(stepTask.stepTaskId);
                }
                if (stepTask.orderTaskId) {
                  orderTaskIds.push(stepTask.orderTaskId);
                }
              });
            }
          });
        }

        // 3. 按顺序删除关联数据（先删除子级数据，再删除父级数据）
        const deletePromises = [];

        // 删除工序任务
        if (stepTaskIds.length > 0) {
          deletePromises.push(deleteStepTasks(stepTaskIds));
        }

        // 删除订单任务
        if (orderTaskIds.length > 0) {
          deletePromises.push(deleteOrderTask([...new Set(orderTaskIds)])); // 去重
        }

        // 删除订单项目
        if (orderItemIds.length > 0) {
          deletePromises.push(batchDeleteOrderItems(orderItemIds));
        }

        // 等待所有关联数据删除完成
        if (deletePromises.length > 0) {
          await Promise.all(deletePromises);
        }

        // 4. 最后删除工单本身
        await deleteOrder(orderId);

        return Promise.resolve();
      } catch (error) {
        console.error('删除工单过程中发生错误:', error);
        throw error;
      }
    },

    /** 暂停工单操作 */
    handlePauseOrder(row) {
      // 检查工单状态是否为执行中
      if (row.orderStatus !== 'IN_PROGRESS') {
        this.$modal.msgWarning('只有执行中的工单才能暂停');
        return;
      }

      this.$modal.confirm(`确认暂停工单"${row.orderCode}"？`, '暂停确认', {
        confirmButtonText: '确定暂停',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.editLoading = true;
        return updateOrderStatus(row.orderId, 'PAUSED');
      }).then(() => {
        this.$modal.msgSuccess('工单已暂停');
        this.getList(); // 刷新列表
      }).catch((error) => {
        if (error !== 'cancel') {
          this.$modal.msgError('暂停工单失败');
        }
      }).finally(() => {
        this.editLoading = false;
      });
    },

    /** 判断工单应该恢复到的状态 */
    determineResumeStatus(workOrder) {
      // 检查所有产品的所有工序状态
      if (!workOrder.products || workOrder.products.length === 0) {
        return 'NEW'; // 没有产品时默认为未开始
      }

      let hasStartedSteps = false;

      for (let product of workOrder.products) {
        if (product.stepTasks && product.stepTasks.length > 0) {
          for (let stepTask of product.stepTasks) {
            // 如果有任何工序的 is_completed 不是 0，说明已经有工序开始了
            if (stepTask.isCompleted !== 0) {
              hasStartedSteps = true;
              break;
            }
          }
        }
        if (hasStartedSteps) break;
      }

      // 如果有工序已经开始，返回执行中；否则返回未开始
      return hasStartedSteps ? 'IN_PROGRESS' : 'NEW';
    },

    /** 恢复工单操作 */
    handleResumeOrder(row) {
      // 检查工单状态是否为暂停中
      if (row.orderStatus !== 'PAUSED') {
        this.$modal.msgWarning('只有暂停中的工单才能恢复');
        return;
      }

      // 智能判断应该恢复到的状态
      const targetStatus = this.determineResumeStatus(row);
      const statusText = targetStatus === 'NEW' ? '未开始' : '执行中';

      this.$modal.confirm(`确认开始工单"${row.orderCode}"？工单状态将恢复为${statusText}。`, '开始确认', {
        confirmButtonText: '确定开始',
        cancelButtonText: '取消',
        type: 'success'
      }).then(() => {
        this.editLoading = true;
        return updateOrderStatus(row.orderId, targetStatus);
      }).then(() => {
        this.$modal.msgSuccess(`工单状态已恢复为${statusText}`);
        this.getList(); // 刷新列表
      }).catch((error) => {
        if (error !== 'cancel') {
          this.$modal.msgError('恢复工单状态失败');
        }
      }).finally(() => {
        this.editLoading = false;
      });
    },

    /** 修改工单状态操作 */
    handleStatusUpdate(row) {
      // 检查是否为已完成状态
      if (row.orderStatus === 'COMPLETED') {
        this.$modal.msgWarning('已完成状态的工单不能修改状态');
        return;
      }

      this.statusForm = {
        orderId: row.orderId,
        orderCode: row.orderCode,
        currentStatus: row.orderStatus,
        newStatus: ''
      };
      this.statusDialogVisible = true;
    },

    /** 状态修改表单提交 */
    handleStatusSubmit() {
      // 检查是否选择了新状态
      if (!this.statusForm.newStatus) {
        this.$modal.msgWarning('请选择新状态');
        return;
      }

      this.editLoading = true;
      updateOrderStatus(this.statusForm.orderId, this.statusForm.newStatus)
        .then(() => {
          this.$modal.msgSuccess('工单状态修改成功');
          this.statusDialogVisible = false;
          this.getList(); // 刷新列表
        })
        .catch(() => {
          this.$modal.msgError('工单状态修改失败');
        })
        .finally(() => {
          this.editLoading = false;
        });
    },

    /** 下发工单操作 */
    handleDispatchOrder(row) {
      // 检查下发状态
      if (row.orderDispatchStatus !== 'NOT_DISPATCHED') {
        this.$modal.msgWarning('只有未下发的工单才能执行下发操作');
        return;
      }

      this.$modal.confirm(`确认下发工单"${row.orderCode}"？下发后员工将看到任务。`, '下发确认', {
        confirmButtonText: '确定下发',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dispatchLoading = true;
        return issueOrder(row.orderId);
      }).then(() => {
        this.$modal.msgSuccess('工单下发成功');
        this.getList(); // 刷新列表
      }).catch((error) => {
        if (error !== 'cancel') {
          console.error('下发工单失败:', error);
          this.$modal.msgError('下发工单失败，请稍后重试');
        }
      }).finally(() => {
        this.dispatchLoading = false;
      });
    },

    /** 检查是否有缺陷信息 */
    hasDefectInfo(defectInfo) {
      if (!defectInfo || typeof defectInfo !== 'object') return false;
      return Object.values(defectInfo).some(value => value > 0);
    },

    /** 获取过滤后的缺陷信息（只包含数量大于0的项目） */
    getFilteredDefectInfo(defectInfo) {
      if (!defectInfo || typeof defectInfo !== 'object') return {};

      const filtered = {};
      Object.keys(defectInfo).forEach(key => {
        if (defectInfo[key] > 0) {
          filtered[key] = defectInfo[key];
        }
      });
      return filtered;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
}

/* 搜索容器样式 */
.search-container {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--el-fill-color-blank);
}

.search-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.search-header i {
  margin-right: 8px;
  color: var(--el-color-primary);
}

.search-form {
  padding: 10px 0;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 0;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-button {
  margin-right: 8px;
  padding: 10px 20px;
  border-radius: 6px;
}

.search-form .el-button:last-child {
  margin-right: 0;
}

/* 工具栏样式 */
.toolbar-container {
  margin-bottom: 20px;
}

.toolbar-card {
  border-radius: 8px;
  background-color: var(--el-fill-color-blank);
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.toolbar-left .el-button-group .el-button {
  padding: 10px 16px;
  border-radius: 6px;
}

.toolbar-left .el-button-group .el-button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.toolbar-left .el-button-group .el-button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.toolbar-right .el-button {
  margin-left: 8px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 表格容器样式 */
.table-container {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--el-fill-color-blank);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.table-title {
  display: flex;
  align-items: center;
}

.table-title i {
  margin-right: 8px;
  color: var(--el-color-primary);
  font-size: 18px;
}

.table-stats {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 统计面板样式 */
.stats-container {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

/* 不同状态的统计卡片颜色 */
.stats-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-total .stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-total .stats-number,
.stats-total .stats-label {
  color: white;
}

.stats-new {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stats-new .stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-new .stats-number,
.stats-new .stats-label {
  color: white;
}

.stats-progress {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stats-progress .stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-progress .stats-number,
.stats-progress .stats-label {
  color: white;
}

.stats-paused {
  background: linear-gradient(135deg, #ff9a56 0%, #ffd93d 100%);
  color: white;
}

.stats-paused .stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-paused .stats-number,
.stats-paused .stats-label {
  color: white;
}

.stats-completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stats-completed .stats-icon {
  background: rgba(255, 255, 255, 0.2);
}

.stats-completed .stats-number,
.stats-completed .stats-label {
  color: white;
}

.work-order-table {
  margin-top: 10px;
}

.work-order-table .el-table__header {
  background-color: var(--base-menu-background);
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.operation-btn {
  padding: 5px 8px;
  color: var(--theme-color);
}

.operation-btn:hover {
  color: var(--el-color-primary-light-3);
}

.operation-btn-danger {
  color: var(--el-color-danger);
}

.operation-btn-danger:hover {
  color: var(--el-color-danger-light-3);
}

.operation-btn-primary {
  color: var(--el-color-primary);
}

.operation-btn-primary:hover {
  color: var(--el-color-primary-light-3);
}

.operation-btn-warning {
  color: var(--el-color-warning);
}

.operation-btn-warning:hover {
  color: var(--el-color-warning-light-3);
}

.operation-btn-success {
  color: var(--el-color-success);
}

.operation-btn-success:hover {
  color: var(--el-color-success-light-3);
}

.operation-btn-disabled {
  color: var(--el-text-color-disabled);
  cursor: not-allowed;
}

.operation-btn-disabled:hover {
  color: var(--el-text-color-disabled);
  transform: none;
}

/* 产品信息显示样式 */
.product-info-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  line-height: 1.4;
}

/* 产品详情信息样式 */
.product-info-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
  line-height: 1.3;
}

.product-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.product-style {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.product-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.product-separator {
  color: var(--el-text-color-secondary);
  margin: 0 2px;
  font-weight: normal;
}

/* 深色主题适配 */
.theme-dark .app-container {
  background-color: var(--el-bg-color-page);
}

.theme-dark .search-card,
.theme-dark .toolbar-card,
.theme-dark .table-card {
  background-color: var(--el-fill-color-blank);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.theme-dark .search-header,
.theme-dark .table-header {
  color: var(--el-text-color-primary);
}

.theme-dark .search-header i,
.theme-dark .table-title i {
  color: var(--el-color-primary);
}

.theme-dark .table-stats {
  color: var(--el-text-color-secondary);
}

.theme-dark .stats-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.theme-dark .stats-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.theme-dark .work-order-table .el-table__header {
  background-color: var(--el-bg-color-overlay);
}

.theme-dark .operation-btn {
  color: var(--el-color-primary-light-3);
}

.theme-dark .operation-btn:hover {
  color: var(--el-color-primary);
}

.theme-dark .operation-btn-danger {
  color: var(--el-color-danger-light-3);
}

.theme-dark .operation-btn-danger:hover {
  color: var(--el-color-danger);
}

.theme-dark .operation-btn-primary {
  color: var(--el-color-primary-light-3);
}

.theme-dark .operation-btn-primary:hover {
  color: var(--el-color-primary);
}

.theme-dark .operation-btn-warning {
  color: var(--el-color-warning-light-3);
}

.theme-dark .operation-btn-warning:hover {
  color: var(--el-color-warning);
}

.theme-dark .operation-btn-success {
  color: var(--el-color-success-light-3);
}

.theme-dark .operation-btn-success:hover {
  color: var(--el-color-success);
}

.theme-dark .operation-btn-disabled {
  color: var(--el-text-color-disabled);
  cursor: not-allowed;
}

.theme-dark .operation-btn-disabled:hover {
  color: var(--el-text-color-disabled);
  transform: none;
}

.theme-dark .product-tag {
  background-color: var(--el-color-primary-dark-2);
  color: var(--el-color-primary-light-3);
}

.theme-dark .product-separator {
  color: var(--el-text-color-secondary);
}

/* 深色主题产品详情样式 */
.theme-dark .product-name {
  color: var(--el-text-color-primary);
}

.theme-dark .product-style {
  color: var(--el-text-color-secondary);
}

/* 搜索表单样式适配 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

.el-form--inline .el-form-item__label {
  color: var(--el-text-color-primary);
}

.el-form--inline .el-input__inner,
.el-form--inline .el-select .el-input__inner {
  border-color: var(--el-border-color);
  background-color: var(--el-fill-color-blank);
  color: var(--el-text-color-primary);
}

/* 表格行悬停效果 */
.work-order-table .el-table__row:hover {
  background-color: var(--el-table-row-hover-bg-color);
}

/* 分页器样式 */
.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.detail-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--theme-color);
}

.detail-actions {
  margin-top: 20px;
  text-align: right;
}

.detail-actions .el-button {
  margin-left: 10px;
  padding: 10px 20px;
  border-radius: 6px;
}

.detail-actions .el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.detail-actions .el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.detail-actions .el-button--success:disabled {
  background: var(--el-color-info-light-7) !important;
  border-color: var(--el-color-info-light-7) !important;
  color: var(--el-text-color-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.detail-actions .el-button--info:disabled {
  background: var(--el-color-info-light-5) !important;
  border-color: var(--el-color-info-light-5) !important;
  color: var(--el-text-color-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 工单详情样式适配 */
.detail-section .el-descriptions {
  background-color: var(--el-fill-color-blank);
}

.detail-section .el-descriptions__header {
  background-color: var(--base-menu-background);
  color: var(--el-text-color-primary);
}

.product-table .el-table__header,
.step-task-table .el-table__header {
  background-color: var(--base-menu-background);
}

.product-table .el-table__row:hover,
.step-task-table .el-table__row:hover {
  background-color: var(--el-table-row-hover-bg-color);
}



/* 深色主题适配 */
.theme-dark .detail-section .el-descriptions {
  background-color: var(--el-bg-color-overlay);
}

.theme-dark .detail-section .el-descriptions__header {
  background-color: var(--el-bg-color-overlay);
}

.theme-dark .product-table .el-table__header,
.theme-dark .step-task-table .el-table__header {
  background-color: var(--el-bg-color-overlay);
}

.theme-dark .detail-actions .el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
}

.theme-dark .detail-actions .el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.theme-dark .detail-actions .el-button--success:disabled {
  background: var(--el-color-info-dark-2) !important;
  border-color: var(--el-color-info-dark-2) !important;
  color: var(--el-text-color-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.theme-dark .detail-actions .el-button--info:disabled {
  background: var(--el-color-info-dark-2) !important;
  border-color: var(--el-color-info-dark-2) !important;
  color: var(--el-text-color-disabled) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 进度条样式 */
.el-progress {
  width: 100%;
}

.el-progress__text {
  font-size: 12px !important;
}

/* 编辑对话框样式 */
.el-dialog__wrapper .el-dialog {
  border-radius: 8px;
  background-color: var(--el-fill-color-blank);
}

.el-dialog__header {
  background-color: var(--base-menu-background);
  color: var(--el-text-color-primary);
  border-radius: 8px 8px 0 0;
  padding: 20px;
}

.el-dialog__title {
  font-weight: 600;
  font-size: 18px;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
  padding: 10px 20px;
  border-radius: 6px;
}

/* 编辑表单样式 */
.el-form-item__label {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.el-form-item__content .el-input__inner,
.el-form-item__content .el-select .el-input__inner {
  border-color: var(--el-border-color);
  background-color: var(--el-fill-color-blank);
  color: var(--el-text-color-primary);
}

.el-form-item__content .el-input__inner:disabled {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
}

/* 深色主题适配 */
.theme-dark .el-dialog__wrapper .el-dialog {
  background-color: var(--el-bg-color-overlay);
}

.theme-dark .el-dialog__header {
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid var(--el-border-color-darker);
}

.theme-dark .el-dialog__footer {
  border-top: 1px solid var(--el-border-color-darker);
}

.theme-dark .el-form-item__content .el-input__inner,
.theme-dark .el-form-item__content .el-select .el-input__inner {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-darker);
}

.theme-dark .el-form-item__content .el-input__inner:disabled {
  background-color: var(--el-fill-color-darker);
  color: var(--el-text-color-disabled);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .search-form .el-form-item {
    margin-bottom: 10px;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 15px;
  }

  .toolbar-left .el-button-group {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .search-form .el-form-item .el-input,
  .search-form .el-form-item .el-select {
    width: 100% !important;
  }

  .stats-container .el-col {
    margin-bottom: 15px;
  }

  .stats-content {
    padding: 15px;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 15px;
  }

  .stats-number {
    font-size: 24px;
  }

  .stats-label {
    font-size: 13px;
  }

  .product-info-inline {
    gap: 2px;
  }

  .product-tag {
    padding: 1px 6px;
    font-size: 11px;
    border-radius: 10px;
  }

  .product-separator {
    margin: 0 1px;
  }

  /* 编辑对话框移动端适配 */
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto;
  }

  .el-dialog__header {
    padding: 15px;
  }

  .el-dialog__body {
    padding: 15px;
  }

  .el-dialog__footer {
    padding: 15px;
  }

  .dialog-footer .el-button {
    margin-left: 8px;
    padding: 8px 16px;
  }
}

/* 动画效果 */
.search-card,
.toolbar-card,
.table-card {
  transition: all 0.3s ease;
}

.search-card:hover,
.toolbar-card:hover,
.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-item {
  transition: all 0.2s ease;
}

.product-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.operation-btn {
  transition: all 0.2s ease;
}

.operation-btn:hover {
  transform: translateY(-1px);
}

.toolbar-right .el-button {
  transition: all 0.2s ease;
}

.toolbar-right .el-button:hover {
  transform: scale(1.1);
}

/* 加载状态优化 */
.el-table__empty-text {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.theme-dark .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 滚动条样式 */
.product-info::-webkit-scrollbar {
  width: 4px;
}

.product-info::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 2px;
}

.product-info::-webkit-scrollbar-thumb {
  background: var(--el-color-primary-light-5);
  border-radius: 2px;
}

.product-info::-webkit-scrollbar-thumb:hover {
  background: var(--el-color-primary);
}

/* 缺陷信息样式 */
.defect-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.defect-tag {
  margin: 2px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
  border: 1px solid var(--el-color-danger-light-7);
}

.no-defect {
  color: var(--el-color-success);
  font-size: 12px;
  font-weight: 500;
}

/* 深色主题适配 */
.theme-dark .defect-tag {
  background-color: var(--el-color-danger-dark-2);
  color: var(--el-color-danger-light-3);
  border-color: var(--el-color-danger-dark-1);
}

.theme-dark .no-defect {
  color: var(--el-color-success-light-3);
}
</style>

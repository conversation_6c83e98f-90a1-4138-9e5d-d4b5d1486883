<template>
  <div class="create-work-order-form">
    <el-form ref="orderForm" :model="orderData" :rules="orderRules" label-width="120px">
      <!-- Work Order Details -->
      <h3>1. 工单基本信息</h3>
      <el-row :gutter="20" style="width: 1000px; margin: 20px auto;">
        <el-col :span="12">
          <el-form-item label="工单编码" prop="orderCode">
            <el-input
              v-model="orderData.orderCode"
              placeholder="请输入工单编码（不能包含空格）"
              @input="handleOrderCodeInput">
              <el-button slot="append" icon="el-icon-refresh" @click="generateOrderCode">自动生成</el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单类型" prop="orderType">
            <el-select v-model="orderData.orderType" placeholder="请选择工单类型" @change="handleOrderTypeChange">
              <el-option label="普通" value="NORMAL"></el-option>
              <el-option label="加急" value="URGENT"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- Products and Tasks -->
      <el-divider></el-divider>
      <h3>2. 添加产品及任务</h3>
      <el-button type="primary" icon="el-icon-plus" size="mini" @click="openProductDialog" style="margin-bottom: 10px;">添加产品</el-button>

      <div v-if="orderData.items.length === 0" style="text-align: center; color: #909399; margin-top: 20px;">
          请添加产品
      </div>

      <div v-for="(item, itemIndex) in orderData.items" :key="item.materialId" class="item-task-card">
        <el-card shadow="hover" style="margin-bottom: 15px;">
          <div class="product-layout">
            <!-- 左侧：产品信息 -->
            <div class="product-info-section">
              <div class="product-title">{{ item.productName }}--{{item.bardType}}型</div>
              <div class="product-details">
                <div class="product-code">编号: {{ item.productId }}</div>
                <div class="product-style" v-if="item.styleName">
                  <span>款式: </span>
                  <el-tag type="info" size="mini">{{ item.styleName }}</el-tag>
                </div>
              </div>
            </div>

            <!-- 中间：仓库信息 -->
            <div class="warehouse-info-section">
              <div class="warehouse-row">
                <span class="warehouse-label">贴片仓：</span>
                <div class="warehouse-tags">
                  <el-tag type="success" size="mini">上板: {{ item.smdStock.upperBoard }}</el-tag>
                  <el-tag type="success" size="mini">下板: {{ item.smdStock.lowerBoard }}</el-tag>
                </div>
              </div>
              <div class="warehouse-row">
                <span class="warehouse-label">线边仓：</span>
                <div class="warehouse-tags">
                  <el-tag type="warning" size="mini">上板: {{ item.lineStock.upperBoard }}</el-tag>
                  <el-tag type="warning" size="mini">下板: {{ item.lineStock.lowerBoard }}</el-tag>
                </div>
              </div>
              <div class="warehouse-row">
                <span class="warehouse-label">原料仓：</span>
                <div class="warehouse-tags">
                  <el-tag type="info" size="mini">上板: {{ item.rawStock.upperBoard }}</el-tag>
                  <el-tag type="info" size="mini">下板: {{ item.rawStock.lowerBoard }}</el-tag>
                </div>
              </div>
            </div>

            <!-- 右侧：数量和操作 -->
            <div class="actions-section">
              <div class="quantity-control">
                <el-button type="text" size="mini" style="margin-top: -10px;" @click="viewBomList(item.productName,item.styleName)">查看BOM清单</el-button>
                <label>数量:</label>
                <div class="quantity-row">
                  <el-input-number v-model="item.quantity" :min="1" size="small"></el-input-number>
                  <el-button type="danger" icon="el-icon-delete" size="mini" @click="removeItem(itemIndex)"></el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- Process Routes Collapse -->
          <el-collapse v-if="processRoutes.length > 0" v-model="item.expandedRoutes" @change="handleRouteCollapseChange($event, item)">
            <el-collapse-item v-for="route in processRoutes" :key="route.processRouteId" :name="route.processRouteNumber">
              <template slot="title">
                <div @click.stop class="route-title-container">
                  <el-checkbox
                    v-model="item.selectedRoutes"
                    :label="route.processRouteNumber"
                    @change="handleRouteSelectionChange($event, route.processRouteNumber, item)"
                    class="route-checkbox">
                    {{ route.processRouteName }}
                  </el-checkbox>
                </div>
              </template>

              <div v-if="item.routeDetails[route.processRouteNumber] && item.routeDetails[route.processRouteNumber].stepsLoading" v-loading="true" element-loading-text="加载工序..." style="height: 80px;"></div>

              <el-table
                v-if="item.routeDetails[route.processRouteNumber] && item.routeDetails[route.processRouteNumber].steps"
                :data="item.routeDetails[route.processRouteNumber].steps"
                border
                size="mini"
                style="width: 100%;"
                @selection-change="handleStepSelectionChange($event, route, item)">
                <el-table-column type="selection" width="55" />
                <el-table-column type="index" label="序号" width="55"></el-table-column>
                <el-table-column prop="procedureName" label="工序名称" width="180"></el-table-column>
                <el-table-column prop="procedureNumber" label="工序编码" width="180"></el-table-column>
                <el-table-column label="汇报人" width="180">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.selectedAssignee" placeholder="请选择" size="mini" style="width: 100%;">
                      <el-option
                        v-for="reporter in scope.row.reportersList"
                        :key="reporter.id"
                        :label="reporter.name"
                        :value="reporter.id">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
              <el-tag v-if="item.routeDetails[route.processRouteNumber] && !item.routeDetails[route.processRouteNumber].steps" type="info" style="margin-top: 10px;">此工艺路线未配置工序</el-tag>
            </el-collapse-item>
          </el-collapse>
          <el-empty v-else description="暂无可用工艺路线" :image-size="80"></el-empty>

        </el-card>
      </div>

      <!-- Final Details -->
      <el-divider></el-divider>
      <h3>3. 最终信息</h3>
      <el-row :gutter="20" style="width: 1000px; margin: 20px auto;">
        <el-col :span="12">
          <el-form-item label="期望完成时间" prop="expectedTime" required>
            <el-date-picker
              v-model="orderData.expectedTime"
              type="date"
              placeholder="选择工单完成日期"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="工单备注" prop="remark">
            <el-input v-model="orderData.remark" type="textarea" :rows="2" placeholder="请输入工单整体备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- Global Actions -->
    <div style="margin-top: 20px; text-align: center;">
      <el-divider></el-divider>
      <el-button type="success" @click="handleSubmit" :loading="loading">提交创建</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>

    <!-- Product Selection Dialog -->
    <el-dialog
      title="选择产品"
      :visible.sync="productDialogVisible"
      width="70%"
      append-to-body
      @opened="onProductDialogOpened"
      @close="onProductDialogClose">
        <!-- Search Bar -->
        <div style="margin-bottom: 15px;">
          <el-row :gutter="10">
            <el-col :span="18">
              <el-input
                v-model="productSearchName"
                placeholder="请输入产品名称进行搜索"
                clearable
                @clear="handleProductSearch"
                @input="handleProductSearchInput"
                @keyup.enter="handleProductSearch">
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleProductSearch" :loading="productLoading">搜索</el-button>
            </el-col>
          </el-row>
        </div>

        <el-table
          :data="products"
          @selection-change="handleProductSelection"
          v-loading="productLoading"
          height="400px"
          ref="productTable"
          :default-sort="{prop: 'productNumber', order: 'ascending'}"
          row-key="uniqueId">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column property="productNumber" label="产品编号" width="160"></el-table-column>
            <el-table-column label="产品名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="!productSearchName">{{ scope.row.productName }}</span>
                <span v-else v-html="highlightSearchTerm(scope.row.productName, productSearchName)"></span>
              </template>
            </el-table-column>
          <el-table-column property="materialType" label="产品类型" width="150"></el-table-column>
            <el-table-column property="boardType" label="所属类型" width="150"></el-table-column>
            <el-table-column label="产品款式" width="180">
              <template slot-scope="scope">
                <div v-if="scope.row.styleName">
                  <el-tag type="info" size="mini">{{ scope.row.styleName }}</el-tag>
                  <div style="font-size: 10px; color: var(--el-text-color-placeholder); margin-top: 2px;">
                    ID: {{ scope.row.styleId || 'N/A' }}
                  </div>
                </div>
                <span v-else style="color: var(--el-text-color-placeholder);">无款式</span>
              </template>
            </el-table-column>
            <el-table-column label="库存" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.stockQuantity > 0 ? 'success' : 'danger'" size="small">
                  {{ scope.row.stockQuantity || 0 }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  class="product-action-btn"
                  @click="addToSelectedList(scope.row)"
                  :disabled="selectedProductsList.some(item => item.productNumber === scope.row.productNumber && (item.styleId || '') === (scope.row.styleId || ''))">
                  {{ selectedProductsList.some(item => item.productNumber === scope.row.productNumber && (item.styleId || '') === (scope.row.styleId || '')) ? '已添加' : '添加' }}
                </el-button>
              </template>
            </el-table-column>
            <template slot="empty">
              <div style="padding: 20px;">
                <i class="el-icon-search" style="font-size: 48px; color: #c0c4cc;"></i>
                <p style="color: #909399; margin-top: 10px;">
                  {{ productSearchName ? `未找到包含"${productSearchName}"的产品` : '暂无产品数据' }}
                </p>
              </div>
            </template>
        </el-table>

        <div style="text-align: center; margin: 10px; color: #909399;">
            共找到 {{ productTotal }} 个产品
            <span v-if="productSearchName">（搜索：{{ productSearchName }}）</span>
            <div v-if="selectedProducts.length > 0" style="margin-top: 8px;">
              <el-button type="primary" size="mini" @click="addSelectedProductsBatch">
                批量添加选中项 ({{ selectedProducts.length }})
              </el-button>
            </div>
        </div>

        <!-- 分页组件 -->
        <div style="text-align: center; margin: 15px 0;">
          <el-pagination
            @current-change="handleProductPageChange"
            :current-page="productCurrentPage"
            :page-size="productPageSize"
            :total="productTotal"
            layout="prev, pager, next, jumper"
            :hide-on-single-page="false"
            small>
          </el-pagination>
        </div>

        <!-- 已选择产品列表 -->
        <div style="margin-top: 20px; border-top: 1px solid var(--el-border-color-light); padding-top: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h4 style="margin: 0; color: var(--el-text-color-primary);">
              <i class="el-icon-shopping-cart-2" style="margin-right: 5px; color: var(--theme-color);"></i>
              已选择的产品 ({{ selectedProductsList.length }})
            </h4>
            <el-button v-if="selectedProductsList.length > 0" type="text" @click="clearSelectedList" style="color: var(--el-text-color-regular);">
              <i class="el-icon-delete"></i> 清空列表
            </el-button>
          </div>

          <div v-if="selectedProductsList.length === 0" style="text-align: center; padding: 30px; color: var(--el-text-color-placeholder);">
            <i class="el-icon-shopping-cart-full" style="font-size: 48px; margin-bottom: 10px;"></i>
            <p style="margin: 0;">暂无选择的产品</p>
            <p style="margin: 5px 0 0 0; font-size: 12px;">点击上方表格中的"添加"按钮或使用批量添加功能</p>
          </div>

                    <div v-else class="selected-products-list" style="max-height: 150px; overflow-y: auto;">
            <div
              v-for="(product, index) in selectedProductsList"
              :key="product.productNumber"
              class="selected-product-item"
              style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px;">

              <div style="flex: 1;">
                <div style="font-weight: 500; color: var(--el-text-color-primary);">
                  {{ product.productName }}
                  <el-tag v-if="product.styleName" type="info" size="mini" style="margin-left: 8px;">
                    {{ product.styleName }}-{{ product.boardType }}型
                  </el-tag>
                </div>
                <div style="font-size: 12px; color: var(--el-text-color-regular); margin-top: 2px;">
                  编号: {{ product.productNumber }}
                  <span v-if="product.styleId" style="margin-left: 10px;">款式ID: {{ product.styleId }}</span>
                  <span style="margin-left: 10px;">添加时间: {{ product.addedTime }}</span>
                </div>
              </div>

              <div style="display: flex; align-items: center; gap: 10px;">
                <el-tag :type="product.stockQuantity > 0 ? 'success' : 'danger'" size="mini">
                  库存: {{ product.stockQuantity || 0 }}
                </el-tag>
                <el-button type="text" @click="removeFromSelectedList(index)" style="color: var(--el-color-danger);">
                  <i class="el-icon-close"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="productDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="addSelectedProducts" :disabled="selectedProductsList.length === 0">
              确定添加（{{ selectedProductsList.length }}）
            </el-button>
        </span>
    </el-dialog>

    <!-- BOM清单对话框 -->
    <el-dialog
      :title="`BOM清单 - ${currentProductName}`"
      :visible.sync="bomDialogVisible"
      width="55%"
      append-to-body
      @close="bomData = []">

      <el-table
        :data="bomData"
        v-loading="bomLoading"
        element-loading-text="加载BOM清单中..."
        border
        stripe
        height="500px"
        style="width: 100%;">

        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column prop="model" label="型号" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="module" label="功能模块" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="boardType" label="板型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.boardType === '上板' ? 'success' : 'warning'" size="mini">
              {{ scope.row.boardType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="material" label="物料名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="quantity" label="数量" width="80" align="center"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"></el-table-column>
        <el-table-column prop="currentStock" label="库存数量" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="minStockQuantity" label="安全库存" width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reservedField3" label="物料编码" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.reservedField3">{{ scope.row.reservedField3 }}</span>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="padding: 40px;">
            <i class="el-icon-document" style="font-size: 48px; color: #c0c4cc;"></i>
            <p style="color: #909399; margin-top: 10px;">
              {{ bomLoading ? '正在加载...' : `产品 ${currentProductName} 暂无BOM清单数据` }}
            </p>
          </div>
        </template>
      </el-table>

      <div style="text-align: center; margin-top: 15px; color: #909399;">
        共找到 {{ bomData.length }} 条BOM记录
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="bomDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  createOrder,
  createOrderItems,
  createOrderTask,
  getProcessRoute,
  createStepTasksBatch,
  getProcessRouteInfo,
  getProductWarehousePage,
  findByNameAndStyleId,
  findByNameAndStyleIdTwo,
  findByName
} from '@/api/jenasi/workOrder';
import { getBomByModelAndStyle } from '@/api/basicData/productMaterialsBom';

export default {
  name: "CreateWorkOrderForm",
  data() {
    return {
      loading: false,
      productLoading: false,

      orderData: {
        orderCode: '',
        orderType: 'NORMAL',
        remark: '',
        expectedTime: null,
        items: []
      },
      orderRules: {
        orderCode: [
          { required: true, message: '请输入工单编码', trigger: 'blur' },
          { validator: this.validateOrderCode, trigger: 'blur' }
        ],
        orderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
        expectedTime: [{ required: true, message: '请选择期望完成时间', trigger: 'change' }]
      },

      productDialogVisible: false,
      products: [],
      selectedProducts: [],
      // 记录选中的产品列表，用于最终添加
      selectedProductsList: [],
      processRoutes: [],
      productSearchName: '', // 产品搜索关键词
      searchTimer: null, // 搜索防抖定时器
      uniqueIdCounter: 0, // 添加一个计数器用于生成唯一ID

      // Product list info
      productTotal: 0,
      productCurrentPage: 1,
      productPageSize: 20, // 修改为20条每页

      // 日期选择器配置
      pickerOptions: {
        disabledDate(time) {
          // 禁用当天之前的日期
          return time.getTime() < Date.now() - 8.64e7; // 8.64e7 = 24 * 60 * 60 * 1000 (一天的毫秒数)
        }
      },

      // BOM清单相关数据
      bomDialogVisible: false,
      bomData: [],
      bomLoading: false,
      currentProductName: ''
    };
  },
  computed: {
    // 过滤后的产品列表
    filteredProducts() {
      return this.products.map((item, index) => ({
        ...item,
        id: item.productNumber || `temp_${index}` // 确保每行都有唯一的id
      }));
    }
  },
  methods: {
    handleOrderTypeChange(newType) {
      if (this.orderData.items && this.orderData.items.length > 0) {
        this.orderData.items.forEach(item => {
          if (item.tasks && item.tasks.length > 0) {
            item.tasks.forEach(task => {
              task.taskLevel = newType;
            });
          }
        });
      }
    },

    handleRouteCollapseChange(expandedRouteCodes, item) {
      if (!expandedRouteCodes) return; // All collapsed

      expandedRouteCodes.forEach(activeRouteCode => {
        // If details are not loaded, fetch them
        if (!item.routeDetails[activeRouteCode]) {
          this.$set(item.routeDetails, activeRouteCode, {
            steps: null,
            stepsLoading: true,
            selectedSteps: []
          });

          const selectedRoute = this.processRoutes.find(r => r.processRouteNumber === activeRouteCode);
          if (selectedRoute) {
            const processRouteId = selectedRoute.processRouteId;
            getProcessRouteInfo(processRouteId).then(response => {
              const procedures = response.data.procedureList || [];
              const routeDetail = item.routeDetails[activeRouteCode];
              routeDetail.steps = procedures.map(p => {
                const reporterIds = p.reportingAuthority ? p.reportingAuthority.split(',') : [];
                const reporterNames = p.reportingName ? p.reportingName.split(',') : [];
                const reportersList = reporterIds.map((id, index) => ({ id: id, name: reporterNames[index] || '' }));
                return {
                  ...p,
                  reportersList: reportersList,
                  selectedAssignee: reportersList.length > 0 ? reportersList[0].id : null
                };
              });
            }).catch(() => {
              this.$modal.msgError(`获取工艺路线详细信息失败`);
              item.routeDetails[activeRouteCode].steps = []; // Set to empty on error
            }).finally(() => {
              item.routeDetails[activeRouteCode].stepsLoading = false;
            });
          }
        }
      });
    },

    handleRouteSelectionChange(isSelected, routeCode, item) {
      // When a route is selected, automatically expand it to show the steps
      if (isSelected && !item.expandedRoutes.includes(routeCode)) {
        item.expandedRoutes.push(routeCode);
      }
    },

    // Product Handling
    openProductDialog() {
      console.log('打开产品选择对话框');
      this.productDialogVisible = true;
      this.productSearchName = '';
      this.selectedProducts = [];
      this.selectedProductsList = []; // 清空已选择产品列表
      this.products = []; // 清空之前的数据
      this.productCurrentPage = 1; // 重置页码
      this.uniqueIdCounter = 0; // 重置计数器

      // 等待对话框渲染完成后再获取数据
      this.$nextTick(() => {
        this.fetchProducts('', 1);
        // 确保表格选择功能可用
        if (this.$refs.productTable) {
          this.$refs.productTable.clearSelection();
        }
      });
    },
    fetchProducts(searchName = '', pageNum = 1) {
      console.log('开始获取产品列表，搜索条件:', searchName, '页码:', pageNum);
      this.productLoading = true;

      const params = {
        pageNum: pageNum,
        pageSize: this.productPageSize,
        productName: searchName || undefined,
        sortField: 'productNumber',
        sortOrder: 'asc'
      };

      getProductWarehousePage(params).then(response => {
        console.log('获取产品列表成功:', response);
        const pageData = response.data || {};
        // 适配新接口返回的数据结构
        this.products = (pageData.records || []).map(item => ({
          productId: item.productId,
          materialId: item.productId, // 使用productId作为materialId
          productName: item.productName,
          materialType: item.materialType,
          stockQuantity: item.stockQuantity || 0,
          uniqueId: `product_${this.uniqueIdCounter++}`, // 添加唯一ID
          // 保持与原接口兼容的字段
          ...item
        }));
        this.productTotal = pageData.total || 0;
        this.productCurrentPage = pageData.current || 1;
        this.productLoading = false;
      }).catch((error) => {
        console.error('获取产品列表失败:', error);
        this.productLoading = false;
        this.$modal.msgError('获取产品列表失败');
        // 确保即使失败也有空数组
        this.products = [];
        this.productTotal = 0;
      });
    },
    // 搜索产品
    handleProductSearch() {
      this.productCurrentPage = 1; // 重置到第一页
      if (this.productSearchName && this.productSearchName.trim() !== '') {
        // 有搜索关键词时，向后端请求
        this.fetchProducts(this.productSearchName, 1);
      } else {
        // 无搜索关键词时，获取所有产品
        this.fetchProducts('', 1);
      }
    },
    // 输入搜索关键词时的处理
    handleProductSearchInput() {
      // 如果搜索框为空，则重新获取所有产品
      if (this.productSearchName.trim() === '') {
        this.productCurrentPage = 1;
        this.fetchProducts('', 1);
      }
    },
    // 处理分页变化
    handleProductPageChange(page) {
      this.productCurrentPage = page;
      const searchName = this.productSearchName && this.productSearchName.trim() !== '' ? this.productSearchName : '';
      this.fetchProducts(searchName, page);
    },
    handleProductSelection(selection) {
      console.log('产品选择变化:', selection);
      this.selectedProducts = selection;
    },

    // 生成产品唯一标识（用于调试和日志）
    getProductUniqueKey(product) {
      return `${product.productNumber}_${product.styleId || 'nostyle'}`;
    },

    // 添加产品到已选择列表
    addToSelectedList(product) {
      // 检查是否已存在（基于产品编号和款式ID的组合）
      const exists = this.selectedProductsList.some(item =>
        item.productNumber === product.productNumber &&
        (item.styleId || '') === (product.styleId || '')
      );
      if (!exists) {
        this.selectedProductsList.push({
          ...product,
          addedTime: new Date().toLocaleTimeString() // 记录添加时间
        });
        const styleInfo = product.styleName ? ` (款式: ${product.styleName})` : '';
        this.$message.success(`已添加产品：${product.productName}${styleInfo}`);
        console.log('已添加产品:', this.getProductUniqueKey(product), '当前列表:', this.selectedProductsList);
      } else {
        const styleInfo = product.styleName ? ` (款式: ${product.styleName})` : '';
        this.$message.warning(`产品 ${product.productName}${styleInfo} 已在选择列表中`);
      }
    },

    // 从已选择列表中移除产品
    removeFromSelectedList(index) {
      const product = this.selectedProductsList[index];
      this.selectedProductsList.splice(index, 1);
      this.$message.success(`已移除产品：${product.productName}`);
      console.log('已选择产品列表:', this.selectedProductsList);
    },

    // 清空已选择列表
    clearSelectedList() {
      this.selectedProductsList = [];
      this.$message.success('已清空选择列表');
    },

    // 批量添加选中的产品
    addSelectedProductsBatch() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请先选择要添加的产品');
        return;
      }

      let addedCount = 0;
      this.selectedProducts.forEach(product => {
        // 检查是否已存在（基于产品编号和款式ID的组合）
        const exists = this.selectedProductsList.some(item =>
          item.productNumber === product.productNumber &&
          (item.styleId || '') === (product.styleId || '')
        );
        if (!exists) {
          this.selectedProductsList.push({
            ...product,
            addedTime: new Date().toLocaleTimeString()
          });
          addedCount++;
        }
      });

      if (addedCount > 0) {
        this.$message.success(`成功添加 ${addedCount} 个产品到选择列表`);
        // 清空表格选择
        if (this.$refs.productTable) {
          this.$refs.productTable.clearSelection();
        }
      } else {
        this.$message.warning('选中的产品已在列表中');
      }

      console.log('已选择产品列表:', this.selectedProductsList);
    },
    // 产品选择对话框打开后的处理
    onProductDialogOpened() {
      console.log('产品选择对话框已打开');
      // 确保表格可以正常工作
      this.$nextTick(() => {
        if (this.$refs.productTable) {
          this.$refs.productTable.doLayout();
          console.log('表格布局已刷新');
        }
      });
    },
    // 产品选择对话框关闭时的处理
    onProductDialogClose() {
      console.log('产品选择对话框已关闭');
      this.selectedProducts = [];
      this.productSearchName = '';
    },
    async addSelectedProducts() {
      if (this.selectedProductsList.length === 0) {
        this.$modal.msgWarning('请先选择产品');
        return;
      }

      let addedCount = 0;
      for (const product of this.selectedProductsList) {
        // 检查是否已存在（基于产品编号和款式ID的组合）
        if (!this.orderData.items.some(item =>
          item.materialId === product.productNumber &&
          (item.styleId || '') === (product.styleId || '')
        )) {
          // 获取贴片仓、线边仓和原料仓的库存信息
          try {
            const [smdResponse, lineResponse, rawResponse] = await Promise.all([
              findByNameAndStyleId(product.productName, product.styleId),
              findByNameAndStyleIdTwo(product.productName, product.styleId),
              findByName(product.productName)
            ]);

            // 获取贴片仓数据
            const smdStockData = smdResponse.data[product.productName] || [];
            const smdStock = {
              upperBoard: smdStockData.find(item => item.boardType === '上板')?.currentStock || 0,
              lowerBoard: smdStockData.find(item => item.boardType === '下板')?.currentStock || 0
            };

            // 获取线边仓数据
            const lineStockData = lineResponse.data[product.productName] || [];
            const lineStock = {
              upperBoard: lineStockData.find(item => item.boardType === '上板')?.currentStock || 0,
              lowerBoard: lineStockData.find(item => item.boardType === '下板')?.currentStock || 0
            };

            // 获取原料仓数据
            const rawStockData = rawResponse.data[product.productName] || [];
            const rawStock = {
              upperBoard: rawStockData.find(item => item.boardType === '上板')?.currentStock || 0,
              lowerBoard: rawStockData.find(item => item.boardType === '下板')?.currentStock || 0
            };

            this.orderData.items.push({
              productId: product.productNumber,
              materialId: product.productNumber,
              productName: product.productName,
              productCategory: product.materialType || 'default-type',
              styleId: product.styleId || 0,
              styleName: product.styleName || '未知款式',
              boardType: product.boardType || '未知板型',
              quantity: 1,
              inventory: product.stockQuantity || 0,
              expandedRoutes: [],
              selectedRoutes: [],
              routeDetails: {},
              smdStock,
              lineStock,
              rawStock
            });
            addedCount++;
          } catch (error) {
            console.error('获取仓库库存信息失败:', error);
            this.$modal.msgError(`获取产品 ${product.productName} 的仓库库存信息失败`);
          }
        }
      }

      if (addedCount > 0) {
        this.$modal.msgSuccess(`成功添加 ${addedCount} 个产品`);
        try {
          // 在成功添加产品后获取工艺路线数据 (if not already loaded)
          if (this.processRoutes.length === 0) {
            await this.fetchProcessRoutes();
          }

          // 设置默认工艺路线
          if (this.processRoutes.length > 0) {
            const firstRouteNumber = this.processRoutes[0].processRouteNumber;
            this.orderData.items.forEach(item => {
              // Only for newly added items
              if (item.selectedRoutes && item.selectedRoutes.length === 0) {
                item.selectedRoutes.push(firstRouteNumber);
                item.expandedRoutes.push(firstRouteNumber);
                this.handleRouteCollapseChange(item.expandedRoutes, item);
              }
            });
          }
        } catch (error) {
          this.$modal.msgError("获取工艺路线失败，无法自动选择。");
        }
      } else {
        this.$modal.msgWarning('选择的产品已存在');
      }

      this.productDialogVisible = false;
    },
    // 高亮搜索关键词
    highlightSearchTerm(text, searchTerm) {
      if (!text || !searchTerm) return text;
      const regex = new RegExp(`(${searchTerm})`, 'gi');
      return text.replace(regex, '<mark>$1</mark>');
    },
    removeItem(itemIndex) {
      this.orderData.items.splice(itemIndex, 1);
    },

    fetchProcessRoutes() {
        console.log('开始获取工艺路线数据');
        return getProcessRoute().then(response => {
            console.log('获取工艺路线成功:', response);
            this.processRoutes = response.data || [];
        }).catch(error => {
            console.error('获取工艺路线失败:', error);
            this.processRoutes = [];
            throw error; // Propagate the error
        });
    },

    // 验证工单编码不能包含空格
    validateOrderCode(rule, value, callback) {
      if (value && value.includes(' ')) {
        callback(new Error('工单编码不能包含空格'));
      } else {
        callback();
      }
    },

    // 处理工单编码输入，实时提示空格问题
    handleOrderCodeInput(value) {
      if (value && value.includes(' ')) {
        this.$message.warning('工单编码不能包含空格');
        // 移除空格
        this.orderData.orderCode = value.replace(/\s/g, '');
      }
    },

    generateOrderCode() {
      this.orderData.orderCode = 'WO-' + new Date().getTime();
    },

    // Submission
    async handleSubmit() {
      this.$refs.orderForm.validate(async (valid) => {
        if (valid) {
          if (this.orderData.items.length === 0) {
            this.$modal.msgError("请至少添加一个产品");
            return;
          }

          // Validation: Ensure at least one process route is selected for each item
          for (let item of this.orderData.items) {
            if (!item.selectedRoutes || item.selectedRoutes.length === 0) {
              this.$modal.msgError(`请为产品【${item.productName}】选择至少一个工艺路线`);
              return;
            }
          }

          this.loading = true;

          try {
            // Step 1: Create the Work Order
            const orderPayload = {
              orderCode: this.orderData.orderCode,
              orderType: this.orderData.orderType,
              remark: this.orderData.remark,
              expectedTime: this.orderData.expectedTime
            };
            const orderResponse = await createOrder(orderPayload);
            const orderId = orderResponse.data.id;

            // Step 2: Create the Order Items
            const itemsPayload = {
              orderId: orderId,
              items: this.orderData.items.map(item => ({
                productId: item.productId, // 使用productNumber作为productId
                productName: item.productName,
                quantity: item.quantity,
                inventory: item.inventory,
                styleId: item.styleId, // 添加款式ID
                styleName: item.styleName, // 添加款式名称
                boardType: item.boardType // 添加板型
              }))
            };
            await createOrderItems(itemsPayload);

            // Step 3 & 4: Create Tasks and Step Tasks
            for (let item of this.orderData.items) {
              for (const routeCode of item.selectedRoutes) {
                if (routeCode) {
                  const taskCreateRequest = {
                    orderId: orderId,
                    itemId: item.productId, // 使用productNumber作为itemId
                    itemName: item.productName,
                    quantity: parseInt(item.quantity),
                    tasks: [{ // Create one task per item based on the active route
                      taskLevel: this.orderData.orderType,
                      routeCode: routeCode,
                      remark: '',
                      createName: this.$store.state.user.name || 'System',
                      updateName: this.$store.state.user.name || 'System',
                      expectedTime: null
                    }]
                  };

                  const taskResponse = await createOrderTask(taskCreateRequest);
                  const createdTasks = taskResponse.data;

                  if (createdTasks && createdTasks.length > 0) {
                    const mainTask = createdTasks[0]; // Assuming one task is created
                    const routeDetail = item.routeDetails[routeCode];
                    if (routeDetail && routeDetail.selectedSteps && routeDetail.selectedSteps.length > 0) {
                      const payload = {
                        taskId: mainTask.id,
                        stepInfos: routeDetail.selectedSteps.map(step => {
                          const selectedReporter = step.reportersList.find(r => r.id === step.selectedAssignee);
                          return {
                            stepId: step.procedureId,
                            stepName: step.procedureName,
                            stepNumber: step.procedureNumber,
                            assignee: selectedReporter ? selectedReporter.name : "",
                            expectedAt: this.orderData.expectedTime || ""
                          }
                        })
                      };
                      await createStepTasksBatch(payload);
                    }
                  }
                }
              }
            }

            this.$modal.msgSuccess("工单、任务及工序已成功创建！");
            this.$emit('success');

          } catch (error) {
            console.error("创建工单过程中发生错误:", error);
            let errorMessage = "创建工单失败";
            if (error.response && error.response.data && error.response.data.msg) {
              errorMessage = error.response.data.msg;
            } else if (error.message) {
              errorMessage = error.message;
            }
            this.$modal.msgError(errorMessage);
          } finally {
            this.loading = false;
          }
        }
      });
    },

    handleCancel() {
      this.$emit('close');
    },

    reset() {
        this.orderData = {
            orderCode: '',
            orderType: 'NORMAL',
            remark: '',
            expectedTime: null,
            items: []
        };
        this.generateOrderCode();
    },

    handleStepSelectionChange(selection, route, item) {
      const routeCode = route.processRouteNumber;
      if (item.routeDetails[routeCode]) {
        item.routeDetails[routeCode].selectedSteps = selection;

        // 当选择了工序时，自动选中上级工艺路线
        if (selection && selection.length > 0) {
          // 如果该工艺路线还未被选中，则自动选中
          if (!item.selectedRoutes.includes(routeCode)) {
            item.selectedRoutes.push(routeCode);
          }
        } else {
          // 当取消选择所有工序时，自动取消选中该工艺路线
          const index = item.selectedRoutes.indexOf(routeCode);
          if (index > -1) {
            item.selectedRoutes.splice(index, 1);
          }
        }
      }
    },

    // 查看BOM清单
    async viewBomList(productName,styleName) {
      if (!productName) {
        this.$message.warning('产品名称不能为空');
        return;
      }

      this.currentProductName = productName;
      this.currentStyleName = styleName;
      this.bomDialogVisible = true;
      this.bomLoading = true;
      this.bomData = [];

      try {
        const response = await getBomByModelAndStyle(productName,styleName);
        if (response.code === 200) {
          this.bomData = response.data || [];
          if (this.bomData.length === 0) {
            this.$message.info(`产品 ${productName} 暂无BOM清单数据`);
          }
        } else {
          this.$message.error(response.msg || '获取BOM清单失败');
        }
      } catch (error) {
        console.error('获取BOM清单失败:', error);
        this.$message.error('获取BOM清单失败，请稍后重试');
      } finally {
        this.bomLoading = false;
      }
    },
  },
  created() {
    this.generateOrderCode();
    // 移除组件创建时的工艺路线获取，改为在添加产品时获取
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>

<style scoped>
.create-work-order-form {
  padding: 20px;
}

.route-title-container {
  width: 100%;
  padding-left: 5px;
}

.route-checkbox {
  font-weight: 500;
}

:deep(.route-checkbox .el-checkbox__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.route-checkbox.is-checked .el-checkbox__label) {
  color: var(--theme-color);
}

.item-task-card .el-card__header {
  background-color: var(--base-menu-background);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name {
    font-weight: bold;
    color: var(--theme-color);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.warehouse-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.warehouse-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.warehouse-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.item-content {
  padding: 20px;
}

.warehouse-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
}

.warehouse-values {
  display: flex;
  gap: 15px;
  flex: 1;
  justify-content: flex-start;
}

.actions-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px solid var(--el-border-color-light);
}

.quantity-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  label {
    color: var(--el-text-color-primary);
    font-size: 14px;
  }
}

.quantity-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 标签样式优化 */
:deep(.warehouse-values .el-tag) {
  min-width: 80px;
  text-align: center;
  padding: 0 8px;
  margin: 0;

  &.el-tag--success {
    background-color: var(--el-color-success-light-9);
  }

  &.el-tag--warning {
    background-color: var(--el-color-warning-light-9);
  }

  &.el-tag--info {
    background-color: var(--el-color-info-light-9);
  }
}

/* 深色主题适配 */
.theme-dark :deep(.warehouse-values .el-tag) {
  &.el-tag--success {
    background-color: var(--el-color-success-dark-9);
  }

  &.el-tag--warning {
    background-color: var(--el-color-warning-dark-9);
  }

  &.el-tag--info {
    background-color: var(--el-color-info-dark-9);
  }
}

/* 确保表格选择功能正常工作 */
:deep(.el-table) {
  .el-checkbox__input {
    pointer-events: auto !important;
  }

  .el-table-column--selection {
    pointer-events: auto !important;
  }

  .el-checkbox {
    pointer-events: auto !important;
  }

  .el-checkbox__inner {
    pointer-events: auto !important;
  }

  /* 确保复选框可见并可点击 */
  .el-table__column .cell {
    pointer-events: auto !important;
  }
}

/* 分页组件主题适配 */
:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__jump {
    color: var(--el-text-color-primary);
  }

  .el-pager li {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
  }

  .el-pager li.active {
    background-color: var(--theme-color);
    color: #fff;
  }

  .btn-prev,
  .btn-next {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
  }
}

/* 款式标签主题适配 */
:deep(.el-tag) {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border-color: var(--el-color-info-light-7);
}

.theme-dark :deep(.el-tag) {
  background-color: var(--el-color-info-dark-2);
  color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-dark-1);
}

/* 搜索高亮样式 */
:deep(mark) {
  background-color: var(--el-color-warning-light-7);
  color: var(--el-color-warning-dark-2);
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
}

/* 深色主题下的搜索高亮 */
.theme-dark :deep(mark) {
  background-color: var(--el-color-warning-dark-2);
  color: var(--el-color-warning-light-9);
}

/* 已选择产品列表样式 */
.selected-products-list {
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
}

.selected-product-item {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.2s;
}

.selected-product-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.selected-product-item:last-child {
  border-bottom: none;
}

/* 深色主题下的已选择产品列表 */
.theme-dark .selected-product-item:hover {
  background-color: var(--el-color-primary-dark-2);
}

/* 操作按钮样式优化 */
.product-action-btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
}

.product-action-btn:disabled {
  background-color: var(--el-color-info-light-9);
  color: var(--el-text-color-placeholder);
  cursor: not-allowed;
}

/* 款式标签优化 */
:deep(.el-tag--info) {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
  border-color: var(--el-color-info-light-7);
}

.theme-dark :deep(.el-tag--info) {
  background-color: var(--el-color-info-dark-2);
  color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-dark-1);
}



/* 三列布局样式 */
.product-layout {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  min-height: 120px;
}

/* 左侧产品信息 */
.product-info-section {
  flex: 0 0 250px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color);
  line-height: 1.4;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-code {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.product-style {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 中间仓库信息 */
.warehouse-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.warehouse-row {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
  max-width: 400px;
}

.warehouse-label {
  width: 70px;
  text-align: right;
  color: var(--el-text-color-primary);
  font-weight: 500;
  font-size: 14px;
}

.warehouse-tags {
  display: flex;
  gap: 10px;
  flex: 1;
}

/* 右侧操作区域 */
.actions-section {
  flex: 0 0 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.quantity-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  label {
    color: var(--el-text-color-primary);
    font-size: 14px;
  }
}

/* 标签样式优化 */
:deep(.warehouse-tags .el-tag) {
  min-width: 75px;
  text-align: center;

  &.el-tag--success {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
    border-color: var(--el-color-success-light-5);
  }

  &.el-tag--warning {
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
    border-color: var(--el-color-warning-light-5);
  }

  &.el-tag--info {
    background-color: var(--el-color-info-light-9);
    color: var(--el-color-info);
    border-color: var(--el-color-info-light-5);
  }
}

/* 深色主题适配 */
.theme-dark {
  :deep(.warehouse-tags .el-tag) {
    &.el-tag--success {
      background-color: var(--el-color-success-dark-9);
      color: var(--el-color-success-light-8);
      border-color: var(--el-color-success-dark-2);
    }

    &.el-tag--warning {
      background-color: var(--el-color-warning-dark-9);
      color: var(--el-color-warning-light-8);
      border-color: var(--el-color-warning-dark-2);
    }

    &.el-tag--info {
      background-color: var(--el-color-info-dark-9);
      color: var(--el-color-info-light-8);
      border-color: var(--el-color-info-dark-2);
    }
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .product-layout {
    flex-direction: column;
    gap: 15px;
  }

  .product-info-section,
  .actions-section {
    flex: none;
  }

  .warehouse-info-section {
    padding: 0;
  }
}
</style>

<template>
    <div class="app-container">
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="订单类别" prop="category">
          <el-select v-model="queryParams.category" placeholder="请选择订单类别" clearable style="width: 150px;">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable style="width: 150px;">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="客户姓名" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户姓名"
            clearable
            style="width: 150px;"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户电话" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入客户电话"
            clearable
            style="width: 150px;"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="快递单号" prop="expressNo">
          <el-input
            v-model="queryParams.expressNo"
            placeholder="请输入快递单号"
            clearable
            style="width: 150px;"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 300px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增订单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >修改订单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleBatchUpdate"
          >批量状态更新</el-button>
        </el-col>

        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
  
      <!-- 订单列表 -->
      <el-table 
        v-loading="loading" 
        :data="orderList" 
        @selection-change="handleSelectionChange"
        :height="tableHeight"
        stripe
        border
        class="order-table"
      >
        <el-table-column type="selection" width="50" align="center" fixed="left" />
        <el-table-column label="订单ID" align="center" prop="id" width="200" fixed="left" sortable />
        <el-table-column label="订单类别" align="center" prop="category" width="120" sortable>
          <template slot-scope="scope">
            <el-tag :type="scope.row.category === 'EXTERNAL' ? 'primary' : 'success'" size="small">
              {{ getOrderCategoryName(scope.row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="status" width="150" sortable>
          <template slot-scope="scope">
            <el-tag :type="getStatusColor(scope.row.status)" size="small">
              {{ getOrderStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="客户姓名" align="center" prop="customerName" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.customerName || '内部订单' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户电话" align="center" prop="phone" width="130">
          <template slot-scope="scope">
            <span>{{ scope.row.phone || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收货地址" align="left" prop="address" min-width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="address-text">{{ scope.row.address || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" align="center" prop="totalAmount" width="150" sortable>
          <template slot-scope="scope">
            <span class="amount-text">¥{{ scope.row.totalAmount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品数量" align="center" prop="itemCount" width="130" sortable>
          <template slot-scope="scope">
            <el-tag type="info" size="mini">{{ scope.row.itemCount || 0 }}件</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="快递公司" align="center" prop="expressCompany" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.expressCompany || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="快递单号" align="center" prop="expressNo" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="express-no-text">{{ scope.row.expressNo || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="库存快照" align="center" width="180">
          <template slot-scope="scope">
            <div class="stock-status">
              <el-tag :type="getStockStatusType(scope.row)" size="small">
                {{ getStockStatusText(scope.row) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleDetail(scope.row)"
                class="operation-btn"
              >详情</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-if="canModifyOrder(scope.row.status)"
                class="operation-btn"
              >修改</el-button>
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" class="operation-dropdown">
                <el-button size="mini" type="text" class="operation-btn">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item 
                    command="updateStatus" 
                    icon="el-icon-refresh"
                    v-if="canUpdateStatus(scope.row.status)"
                  >更新状态</el-dropdown-item>
                  <el-dropdown-item 
                    command="setExpress" 
                    icon="el-icon-truck" 
                    v-if="canSetExpressInfo(scope.row)"
                  >设置快递</el-dropdown-item>
                  <el-dropdown-item 
                    command="complete" 
                    icon="el-icon-check" 
                    v-if="canCompleteOrder(scope.row)"
                  >完成订单</el-dropdown-item>
                  <el-dropdown-item 
                    command="cancel" 
                    icon="el-icon-close" 
                    v-if="canCancelOrder(scope.row.status)"
                  >取消订单</el-dropdown-item>
                  <el-dropdown-item 
                    command="setTask" 
                    icon="el-icon-setting" 
                    v-if="canSetTask(scope.row.status)"
                  >设置任务</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
  
      <!-- 新增/修改订单对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="1400px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="订单类别" prop="category">
                <el-select v-model="form.category" placeholder="请选择订单类别" @change="categoryChange" style="width: 100%;">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="当前状态" v-if="title === '修改订单'">
                <el-input :value="getOrderStatusName(form.status)" disabled style="width: 100%;" />
              </el-form-item>
            </el-col>
          </el-row>
  
          <!-- 客户信息（外部订单必填） -->
          <div v-if="form.category === 'EXTERNAL'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户姓名" prop="customerName">
                  <el-input v-model="form.customerName" placeholder="请输入客户姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入客户电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="收货地址" prop="address">
                  <el-input v-model="form.address" placeholder="请输入收货地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="快递公司">
                  <el-input v-model="form.expressCompany" placeholder="请输入快递公司" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="title === '修改订单' && form.expressNo">
              <el-col :span="12">
                <el-form-item label="快递单号">
                  <el-input v-model="form.expressNo" disabled placeholder="快递单号（只读）" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
  
          <!-- 订单明细 -->
          <el-form-item label="订单明细" prop="items">
            <div style="margin-bottom: 10px;">
              <el-button type="primary" size="mini" @click="addItem">添加商品</el-button>
              <span style="margin-left: 20px; color: var(--text-color-secondary, #909399);">
                总金额: <strong style="color: var(--theme-color, #E6A23C);">¥{{ calculateTotalAmount() }}</strong>
              </span>
            </div>
            <el-table :data="form.items" style="width: 100%" border>
              <el-table-column label="商品选择" width="180">
                <template slot-scope="scope">
                  <el-button 
                    size="mini" 
                    type="primary" 
                    @click="selectProduct(scope.$index)"
                    style="width: 100%;"
                  >
                    {{ scope.row.productName || '选择商品' }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="商品ID" width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productId" disabled size="small" style="width: 100%;" />
                </template>
              </el-table-column>
              <el-table-column label="商品类别" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productCategory" disabled size="small" />
                </template>
              </el-table-column>
              <el-table-column label="商品名称" width="200">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productName" disabled size="small" />
                </template>
              </el-table-column>
              <el-table-column label="数量" width="130">
                <template slot-scope="scope">
                  <div>
                    <el-input-number 
                      v-model="scope.row.quantity" 
                      :min="1" 
                      size="small" 
                      style="width: 100%;" 
                      controls-position="right"
                      @change="calculateItemTotal(scope.row)" 
                    />
                    <div v-if="scope.row.inventory !== undefined && scope.row.quantity > scope.row.inventory" 
                         style="font-size: 11px; color: #f56c6c; margin-top: 2px;">
                      库存不足（当前：{{ scope.row.inventory }}）
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="当前库存" width="100">
                <template slot-scope="scope">
                  <div style="text-align: center;">
                    <span v-if="scope.row.inventory !== undefined" 
                          :style="{ 
                            color: scope.row.inventory > 0 ? 'var(--theme-color, #67c23a)' : '#f56c6c',
                            fontWeight: 'bold' 
                          }">
                      {{ scope.row.inventory }}
                    </span>
                    <span v-else style="color: var(--text-color-secondary, #909399);">-</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="单价" width="140">
                <template slot-scope="scope">
                  <el-input-number 
                    v-model="scope.row.unitPrice" 
                    :min="0" 
                    :precision="2" 
                    size="small" 
                    style="width: 100%;" 
                    controls-position="right"
                    @change="calculateItemTotal(scope.row)" 
                  />
                </template>
              </el-table-column>
              <el-table-column label="小计" width="120">
                <template slot-scope="scope">
                  <span class="amount-text">¥{{ scope.row.totalPrice || 0 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="90">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="removeItem(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
  
      <!-- 订单详情对话框 -->
      <el-dialog title="订单详情" :visible.sync="openDetail" width="800px" append-to-body>
        <div v-if="detailData">
          <el-descriptions title="订单信息" :column="2" border>
            <el-descriptions-item label="订单ID">{{ detailData.id }}</el-descriptions-item>
            <el-descriptions-item label="订单类别">{{ getOrderCategoryName(detailData.category) }}</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusColor(detailData.status)">{{ getOrderStatusName(detailData.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="订单金额">
              <span style="color: var(--theme-color, #E6A23C); font-weight: bold;">¥{{ detailData.totalAmount || calculateTotalAmount() }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="客户姓名">{{ detailData.customerName || '无' }}</el-descriptions-item>
            <el-descriptions-item label="客户电话">{{ detailData.phone || '无' }}</el-descriptions-item>
            <el-descriptions-item label="收货地址" :span="2">{{ detailData.address || '无' }}</el-descriptions-item>
            <el-descriptions-item label="快递公司">{{ detailData.expressCompany || '无' }}</el-descriptions-item>
            <el-descriptions-item label="快递单号">{{ detailData.expressNo || '无' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ parseTime(detailData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
            <el-descriptions-item label="库存快照">
              <div class="inventory-snapshot-detail">
                <div class="inventory-items-list">
                  <div v-for="(item, index) in detailData.items" :key="index" class="inventory-item-row">
                    <span class="item-name">{{ item.productName }}</span>：
                    <span class="demand-info">需求{{ item.quantity || 0 }}</span>
                    <span class="divider">/</span>
                    <span class="stock-info">库存{{ item.inventory || 0 }}</span>
                    <span class="status-info" :class="{
                      'sufficient': (item.inventory || 0) >= (item.quantity || 0),
                      'insufficient': (item.inventory || 0) < (item.quantity || 0)
                    }">
                      {{ getItemStatusText(item) }}
                    </span>
                    <span class="status-icon" :class="{
                      'success-icon': (item.inventory || 0) >= (item.quantity || 0),
                      'warning-icon': (item.inventory || 0) < (item.quantity || 0)
                    }">
                      {{ getItemStatusIcon(item) }}
                    </span>
                  </div>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
  
          <div style="margin-top: 20px;">
            <h4>订单明细</h4>
            <el-table :data="detailData.items" style="width: 100%">
              <el-table-column label="商品ID" align="center" prop="productId" />
              <el-table-column label="商品类别" align="center" prop="productCategory" />
              <el-table-column label="商品名称" align="center" prop="productName" />
              <el-table-column label="数量" align="center" width="100">
                <template slot-scope="scope">
                  <div>
                    <span>{{ scope.row.quantity }}</span>
                    <div v-if="scope.row.inventory !== undefined && scope.row.quantity > scope.row.inventory" 
                         style="font-size: 10px; color: #f56c6c; margin-top: 2px;">
                      超出库存
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="当前库存" align="center" width="100">
                <template slot-scope="scope">
                  <span :style="{ color: scope.row.inventory > 0 ? 'var(--theme-color, #67c23a)' : '#f56c6c' }">
                    {{ scope.row.inventory !== undefined ? scope.row.inventory : '-' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="单价" align="center" prop="unitPrice">
                <template slot-scope="scope">
                  <span>¥{{ scope.row.unitPrice }}</span>
                </template>
              </el-table-column>
              <el-table-column label="小计" align="center" prop="totalPrice">
                <template slot-scope="scope">
                  <span style="color: var(--theme-color, #E6A23C); font-weight: bold;">¥{{ scope.row.totalPrice }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-dialog>
  
      <!-- 批量状态更新对话框 -->
      <el-dialog title="批量更新状态" :visible.sync="openBatchUpdate" width="400px" append-to-body>
        <el-form :model="batchForm" label-width="80px">
          <el-form-item label="新状态">
            <el-select v-model="batchForm.status" placeholder="请选择新状态" style="width: 100%;">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div style="color: var(--text-color-secondary, #909399); font-size: 12px;">
              已选择 {{ multipleSelection.length }} 个订单
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="openBatchUpdate = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchUpdate">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 设置任务对话框 -->
      <el-dialog title="设置订单任务" :visible.sync="openTaskSetting" width="1000px" append-to-body>
        <div v-if="currentOrder">
          <el-descriptions title="订单信息" :column="2" border size="small" style="margin-bottom: 20px;">
            <el-descriptions-item label="订单ID">{{ currentOrder.id }}</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusColor(currentOrder.status)">{{ getOrderStatusName(currentOrder.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="客户姓名">{{ currentOrder.customerName || '内部订单' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(currentOrder.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          </el-descriptions>

          <h4 style="margin: 20px 0 10px 0;">为订单物品设置任务</h4>
          <div v-if="currentOrder.items && currentOrder.items.length > 0">
            <div v-for="(item, index) in currentOrder.items" :key="index" class="item-task-card">
              <el-card shadow="hover" style="margin-bottom: 15px;">
                <div slot="header" class="item-header">
                  <div class="item-info-left">
                    <span class="item-name">{{ item.productName }}</span>
                    <span class="item-info">{{ item.productCategory }}</span>
                  </div>
                  <div class="item-quantity-info">
                    <div class="quantity-comparison">
                      <span class="quantity-label">需求数量:</span>
                      <span class="quantity-value demand">{{ item.quantity }}</span>
                      <span class="quantity-divider">|</span>
                      <span class="quantity-label">库存数量:</span>
                      <span class="quantity-value" :class="{
                        'sufficient': item.inventory >= item.quantity,
                        'insufficient': item.inventory < item.quantity,
                        'out-of-stock': item.inventory === 0
                      }">{{ item.inventory || 0 }}</span>
                    </div>
                    <div class="stock-status-indicator">
                      <el-tag :type="getItemStockStatusType(item)" size="mini">
                        {{ getItemStockStatusText(item) }}
                      </el-tag>
                    </div>
                  </div>
                  <el-button type="primary" size="mini" @click="addTaskForItem(item)" v-if="item.tasks.length < 3">
                    添加任务
                  </el-button>
                </div>
                
                <div v-if="item.tasks && item.tasks.length > 0">
                  <div v-for="(task, taskIndex) in item.tasks" :key="taskIndex" class="task-form-item">
                    <div class="task-header">
                      <span class="task-title">任务 {{ taskIndex + 1 }}</span>
                      <el-button type="text" size="mini" @click="removeTaskFromItem(item, taskIndex)" v-if="item.tasks.length > 1">
                        <i class="el-icon-delete"></i> 删除
                      </el-button>
                    </div>
                    
                    <!-- 库存警告提示 -->
                    <div v-if="item.inventory !== undefined && item.quantity > item.inventory" 
                         class="inventory-warning-alert">
                      <el-alert
                        title="库存警告"
                        :description="`当前商品库存不足！需求数量：${item.quantity}，可用库存：${item.inventory}，缺少：${item.quantity - item.inventory}`"
                        type="warning"
                        :closable="false"
                        show-icon
                        style="margin-bottom: 15px;">
                      </el-alert>
                    </div>
                    
                    <el-form :model="task" :rules="taskRules" size="small">
                      <el-row :gutter="20">
                        <el-col :span="8">
                          <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="task.taskType" placeholder="请选择任务类型" style="width: 100%;">
                              <el-option label="生产任务" value="PRODUCE"></el-option>
                              <el-option label="发货任务" value="DELIVERY"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="任务级别" prop="taskLevel">
                            <el-select v-model="task.taskLevel" placeholder="请选择任务级别" style="width: 100%;">
                              <el-option label="普通" :value="0"></el-option>
                              <el-option label="重要" :value="1"></el-option>
                              <el-option label="加急" :value="2"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="工艺路线" prop="routeCode" v-if="task.taskType === 'PRODUCE'">
                            <el-select v-model="task.routeCode" placeholder="选择工艺路线" style="width: 100%;">
                              <el-option 
                                v-for="route in processRoutes" 
                                :key="route.processRouteId" 
                                :label="route.processRouteName" 
                                :value="route.processRouteNumber"
                                :disabled="isRouteUsedByItem(item, route.processRouteNumber, taskIndex)">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="20">

                        <el-col :span="12">
                          <el-form-item label="备注">
                            <el-input v-model="task.remark" placeholder="请输入备注信息" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="20">
                        <el-col :span="12">
                          <el-form-item label="预期完成时间" prop="expectedTime" required>
                            <el-date-picker
                              v-model="task.expectedTime"
                              type="datetime"
                              placeholder="请选择预期完成时间"
                              format="yyyy-MM-dd HH:mm:ss"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              style="width: 100%;"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>
                  </div>
                </div>
                <div v-else>
                  <el-empty description="暂无任务，请点击添加任务" :image-size="100" />
                </div>
              </el-card>
            </div>
          </div>
          <div v-else>
            <el-empty description="该订单暂无物品信息" />
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelTaskSetting">取 消</el-button>
          <el-button type="primary" @click="submitTaskSetting" :loading="taskSettingLoading">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 商品选择对话框 -->
      <el-dialog title="选择商品" :visible.sync="openProductSelect" width="800px" append-to-body>
        <div style="margin-bottom: 15px;">
          <el-form :inline="true" size="small">
            <el-form-item label="商品类别">
              <el-input v-model="productSearchForm.materialType" placeholder="请输入商品类别" clearable style="width: 150px;" />
            </el-form-item>
            <el-form-item label="商品名称">
              <el-input v-model="productSearchForm.materialName" placeholder="请输入商品名称" clearable style="width: 150px;" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="searchProducts">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetProductSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table 
          :data="productList" 
          v-loading="productLoading"
          @row-click="selectProductRow"
          highlight-current-row
          max-height="400"
          style="width: 100%;"
        >
          <el-table-column label="商品ID" prop="materialId" width="100" />
          <el-table-column label="商品类别" prop="materialType" width="120" />
          <el-table-column label="商品名称" prop="materialName" min-width="200" show-overflow-tooltip />
          <el-table-column label="当前库存" prop="stockQuantity" width="100" align="center">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.stockQuantity > 0 ? 'var(--theme-color, #67c23a)' : '#f56c6c' }">
                {{ scope.row.stockQuantity || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="单位" prop="unit" width="80" align="center" />
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="primary" 
                @click="confirmSelectProduct(scope.row)"
              >
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div slot="footer" class="dialog-footer">
          <el-button @click="openProductSelect = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { 
    queryOrders, 
    addOrder, 
    updateOrder, 
    updateOrderStatus, 
    setExpressInfo, 
    cancelOrder, 
    completeOrder,
    getOrderDetail,
    listInventory
  } from "@/api/jenasi/orders";
  import { 
    createOrderTask, 
    getProcessRoute 
  } from "@/api/jenasi/orderTask";
  import {
    ORDER_CATEGORY_OPTIONS,
    ORDER_STATUS_OPTIONS,
    EXTERNAL_ORDER_STATUS_OPTIONS,
    INTERNAL_ORDER_STATUS_OPTIONS,
    getOrderStatusColor,
    getOrderStatusName,
    getOrderCategoryName,
    isOrderModifiable,
    isOrderCancellable,
    isExpressSettable,
    isOrderCompletable
  } from "@/api/jenasi/orderConstants";
  
  export default {
    name: "Orders",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 多选
        multipleSelection: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 订单表格数据
        orderList: [],
        // 表格高度
        tableHeight: 'calc(100vh - 350px)',
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        openDetail: false,
        openBatchUpdate: false,
        // 详情数据
        detailData: null,
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          category: null,
          status: null,
          customerName: null,
          phone: null,
          expressNo: null
        },
        // 表单参数
        form: {
          items: []
        },
        // 批量更新表单
        batchForm: {
          status: null
        },
        // 选择的新状态
        selectedNewStatus: '',
        // 选项数据
        categoryOptions: ORDER_CATEGORY_OPTIONS,
        statusOptions: ORDER_STATUS_OPTIONS,
        // 表单校验
        rules: {
          category: [
            { required: true, message: "订单类别不能为空", trigger: "change" }
          ],
          customerName: [
            { required: true, message: "客户姓名不能为空", trigger: "blur" }
          ],
          phone: [
            { required: true, message: "客户电话不能为空", trigger: "blur" }
          ],
          address: [
            { required: true, message: "收货地址不能为空", trigger: "blur" }
          ],
          items: [
            { required: true, message: "订单明细不能为空", trigger: "change" }
          ]
        },
        // 商品选择相关
        openProductSelect: false,
        currentItemIndex: -1, // 当前选择商品的明细行索引
        productSearchForm: {
          materialType: '',
          materialName: ''
        },
        productList: [],
        productLoading: false,
        // 任务设置相关
        openTaskSetting: false,
        currentOrder: null,
        taskSettingLoading: false,
        processRoutes: [], // 工艺路线列表
        taskRules: {
          taskType: [
            { required: true, message: "请选择任务类型", trigger: "change" }
          ],
          taskLevel: [
            { required: true, message: "请选择任务级别", trigger: "change" }
          ],

        }
      };
    },
    created() {
      this.getList();
    },
    mounted() {
      this.setTableHeight();
      window.addEventListener('resize', this.setTableHeight);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
      /** 查询订单列表 */
      getList() {
        this.loading = true;
        const params = {
          ...this.queryParams
        };
        if (this.dateRange && this.dateRange.length === 2) {
          params.createTimeStart = this.dateRange[0];
          params.createTimeEnd = this.dateRange[1];
        }
        
        queryOrders(params).then(response => {
          // 确保响应数据结构正确
          if (response.data && response.data.records) {
            this.orderList = response.data.records;
            this.total = response.data.total;
            
            console.log('获取到的订单列表:', this.orderList);
            console.log('第一个订单的结构:', this.orderList[0]);
          } else {
            this.orderList = [];
            this.total = 0;
          }
          this.loading = false;
        }).catch(error => {
          console.error('获取订单列表失败:', error);
          this.orderList = [];
          this.total = 0;
          this.loading = false;
        });
      },


      
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },
      
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "新增订单";
      },
      
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        
        // 如果没有传递row参数（顶部按钮点击），则从选中的数据中获取
        if (!row || typeof row !== 'object' || !row.id) {
          if (this.multipleSelection.length !== 1) {
            this.$modal.msgError("请选择一个订单进行修改");
            return;
          }
          row = this.multipleSelection[0];
        }
        
        const orderId = row.id;
        
        // 根据后端逻辑，只有PENDING、PROCESSING、PACKAGED状态可以修改
        const modifiableStatuses = ['PENDING', 'PROCESSING', 'PACKAGED'];
        if (!modifiableStatuses.includes(row.status)) {
          const statusName = this.getOrderStatusName(row.status) || row.status || '未知状态';
          this.$modal.msgError(`当前订单状态"${statusName}"不允许修改订单内容。只有"待处理"、"处理中"、"已打包"状态的订单可以修改基本信息。`);
          return;
        }
        
        this.getOrderDetails(orderId).then(() => {
          // 再次检查从后端获取的最新状态
          if (!modifiableStatuses.includes(this.form.status)) {
            const statusName = this.getOrderStatusName(this.form.status) || this.form.status || '未知状态';
            this.$modal.msgError(`订单状态已更新为"${statusName}"，当前状态不允许修改订单内容。`);
            return;
          }
          this.open = true;
          this.title = "修改订单";
        }).catch(error => {
          console.error('获取订单详情失败:', error);
          this.$modal.msgError("获取订单详情失败，请重试");
        });
      },
      
      /** 详情按钮操作 */
      handleDetail(row) {
        this.getOrderDetails(row.id).then(() => {
          this.detailData = { ...this.form };
          this.openDetail = true;
        });
      },
      
      /** 获取订单详情 */
      getOrderDetails(orderId) {
        return new Promise((resolve, reject) => {
          // 使用查询接口获取包含明细的完整订单数据
          const queryParams = {
            pageNum: 1,
            pageSize: 1,
            id: orderId
          };
          
          queryOrders(queryParams).then(response => {
            if (response.data && response.data.records && response.data.records.length > 0) {
              const orderData = response.data.records[0];
              
              // 添加调试日志
              console.log('获取订单详情成功:', {
                orderId: orderData.id,
                status: orderData.status,
                statusName: this.getOrderStatusName(orderData.status),
                isModifiable: this.isModifiable(orderData.status),
                itemsCount: orderData.items ? orderData.items.length : 0
              });
              
              this.form = {
                id: orderData.id,
                category: orderData.category,
                status: orderData.status,
                customerName: orderData.customerName,
                phone: orderData.phone,
                address: orderData.address,
                expressCompany: orderData.expressCompany,
                expressNo: orderData.expressNo,
                createTime: orderData.createTime, // 添加创建时间
                items: orderData.items || []
              };
              
              resolve();
            } else {
              console.error('未找到订单数据');
              reject(new Error('未找到订单数据'));
            }
          }).catch(error => {
            console.error('获取订单详情失败:', error);
            reject(error);
          });
        });
      },
      
      /** 提交按钮 */
      submitForm() {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              // 修改订单时，先验证状态是否允许修改
              if (!this.isModifiable(this.form.status)) {
                this.$modal.msgError("当前订单状态不允许修改，请刷新页面获取最新状态");
                return;
              }
              
              // 创建提交数据副本，移除status字段避免状态冲突
              const updateData = { ...this.form };
              delete updateData.status;
              
              updateOrder(updateData).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('修改订单失败:', error);
                if (error.response && error.response.data && error.response.data.msg) {
                  this.$modal.msgError(error.response.data.msg);
                } else {
                  this.$modal.msgError("修改订单失败");
                }
              });
            } else {
              addOrder(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.error('新增订单失败:', error);
                if (error.response && error.response.data && error.response.data.msg) {
                  this.$modal.msgError(error.response.data.msg);
                } else {
                  this.$modal.msgError("新增订单失败");
                }
              });
            }
          }
        });
      },
      
      /** 取消按钮 */
      cancel() {
        this.open = false;
        this.reset();
      },
      
      /** 表单重置 */
      reset() {
        this.form = {
          id: null,
          category: null,
          status: null,
          customerName: null,
          phone: null,
          address: null,
          expressCompany: null,
          expressNo: null,
          createTime: null,
          items: []
        };
        this.resetForm("form");
      },
      
      /** 多选框选中数据 */
      handleSelectionChange(selection) {
        this.multipleSelection = selection;
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      
      /** 订单类别变化 */
      categoryChange(value) {
        // 清空客户信息
        if (value === 'INTERNAL') {
          this.form.customerName = null;
          this.form.phone = null;
          this.form.address = null;
          this.form.expressCompany = null;
          this.form.expressNo = null;
        }
        // 重新设置校验规则
        this.updateValidationRules();
      },
      
      /** 更新校验规则 */
      updateValidationRules() {
        if (this.form.category === 'EXTERNAL') {
          this.rules.customerName[0].required = true;
          this.rules.phone[0].required = true;
          this.rules.address[0].required = true;
        } else {
          this.rules.customerName[0].required = false;
          this.rules.phone[0].required = false;
          this.rules.address[0].required = false;
        }
      },
      
      /** 添加商品明细 */
      addItem() {
        this.form.items.push({
          productId: null,
          productCategory: '',
          productName: '',
          inventory: undefined, // 库存信息（从订单接口获取）
          quantity: 1,
          unitPrice: 0,
          totalPrice: 0
        });
      },
      
      /** 删除商品明细 */
      removeItem(index) {
        this.form.items.splice(index, 1);
      },
      
      /** 计算明细小计 */
      calculateItemTotal(item) {
        item.totalPrice = (item.quantity || 0) * (item.unitPrice || 0);
      },
      
      /** 计算订单总金额 */
      calculateTotalAmount() {
        return this.form.items.reduce((total, item) => {
          return total + ((item.quantity || 0) * (item.unitPrice || 0));
        }, 0).toFixed(2);
      },
      
      /** 更多操作下拉菜单 */
      handleCommand(command, row) {
        switch (command) {
          case 'updateStatus':
            this.handleUpdateStatus(row);
            break;
          case 'setExpress':
            this.handleSetExpress(row);
            break;
          case 'complete':
            this.handleComplete(row);
            break;
          case 'cancel':
            this.handleCancel(row);
            break;
          case 'setTask':
            this.handleSetTask(row);
            break;
        }
      },
      
      /** 更新状态 */
      handleUpdateStatus(row) {
        // 根据后端逻辑检查状态是否可以更新
        const finalStatuses = ['CANCELLED', 'COMPLETED', 'RETURNED'];
        if (finalStatuses.includes(row.status)) {
          this.$modal.msgWarning(`当前订单状态"${this.getOrderStatusName(row.status)}"是终结状态，不允许变更`);
          return;
        }
        
        // 根据订单类别获取可用状态选项
        const statusOptions = this.getStatusOptionsByCategory(row.category);
        
        // 过滤掉当前状态和不合理的状态流转
        const availableOptions = statusOptions.filter(item => {
          if (item.value === row.status) return false;
          
          // 根据后端逻辑过滤不允许的状态流转
          if (row.status === 'DELIVERED' && item.value === 'SHIPPED') return false;
          if (row.status === 'SHIPPED' && ['PENDING', 'PROCESSING', 'PACKAGED'].includes(item.value)) return false;
          
          return true;
        });
        
        if (availableOptions.length === 0) {
          this.$modal.msgWarning("当前状态无法更新到其他状态");
          return;
        }
        
        // 创建选择器HTML字符串
        const selectHtml = `
          <div>
            <p style="margin-bottom: 10px; color: var(--text-color-regular, #606266);">
              当前状态：<strong>${this.getOrderStatusName(row.status)}</strong>
            </p>
            <p style="margin-bottom: 10px; font-size: 12px; color: var(--text-color-secondary, #909399);">
              请选择要更新的新状态：
            </p>
            <select id="statusSelect" style="width: 100%; padding: 8px; border: 1px solid var(--border-color-base, #dcdfe6); border-radius: 4px; font-size: 14px; background: var(--bg-color, #ffffff); color: var(--text-color-primary, #303133);">
              <option value="">请选择新状态</option>
              ${availableOptions.map(option => 
                `<option value="${option.value}">${option.label}</option>`
              ).join('')}
            </select>
          </div>
        `;
        
        this.$msgbox({
          title: '状态更新',
          dangerouslyUseHTMLString: true,
          message: selectHtml,
          showCancelButton: true,
          confirmButtonText: '确定更新',
          cancelButtonText: '取消',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              const selectElement = document.getElementById('statusSelect');
              const selectedValue = selectElement ? selectElement.value : '';
              
              if (!selectedValue) {
                this.$message.warning('请选择新状态');
                return;
              }
              
              console.log('准备更新状态:', {
                orderId: row.id,
                fromStatus: row.status,
                toStatus: selectedValue,
                category: row.category
              });
              
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '更新中...';
              
              updateOrderStatus(row.id, selectedValue).then((response) => {
                console.log('状态更新成功响应:', response);
                this.$modal.msgSuccess("状态更新成功");
                // 刷新列表数据
                this.getList();
                done();
              }).catch(error => {
                console.error('状态更新失败详情:', error);
                
                // 更友好的错误处理
                let errorMsg = "状态更新失败";
                if (error.response && error.response.data) {
                  if (error.response.data.msg) {
                    errorMsg = error.response.data.msg;
                  } else if (error.response.data.message) {
                    errorMsg = error.response.data.message;
                  }
                }
                
                this.$modal.msgError(errorMsg);
                instance.confirmButtonLoading = false;
                instance.confirmButtonText = '确定更新';
              }).finally(() => {
                // 确保loading状态总是被重置
                if (instance.confirmButtonLoading) {
                  instance.confirmButtonLoading = false;
                  instance.confirmButtonText = '确定更新';
                }
              });
            } else {
              done();
            }
          }
        });
      },
      
      /** 设置快递信息 */
      handleSetExpress(row) {
        // 创建表单HTML
        const formHtml = `
          <div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; color: var(--text-color-primary, #303133);">快递公司：</label>
              <input id="expressCompany" 
                     type="text" 
                     placeholder="请输入快递公司" 
                     value="${row.expressCompany || ''}"
                     style="width: 100%; padding: 8px; border: 1px solid var(--border-color-base, #dcdfe6); border-radius: 4px; background: var(--bg-color, #ffffff); color: var(--text-color-primary, #303133);" />
            </div>
            <div style="margin-bottom: 10px;">
              <label style="display: block; margin-bottom: 5px; color: var(--text-color-primary, #303133);">快递单号：</label>
              <input id="expressNo" 
                     type="text" 
                     placeholder="请输入快递单号" 
                     value="${row.expressNo || ''}"
                     style="width: 100%; padding: 8px; border: 1px solid var(--border-color-base, #dcdfe6); border-radius: 4px; background: var(--bg-color, #ffffff); color: var(--text-color-primary, #303133);" />
            </div>
          </div>
        `;
        
        this.$msgbox({
          title: '设置快递信息',
          dangerouslyUseHTMLString: true,
          message: formHtml,
          showCancelButton: true,
          confirmButtonText: '确定设置',
          cancelButtonText: '取消',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              const companyInput = document.getElementById('expressCompany');
              const noInput = document.getElementById('expressNo');
              
              const expressCompany = companyInput ? companyInput.value.trim() : '';
              const expressNo = noInput ? noInput.value.trim() : '';
              
              if (!expressCompany && !expressNo) {
                this.$message.warning('请至少输入快递公司或快递单号');
                return;
              }
              
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '设置中...';
              
              setExpressInfo(row.id, expressCompany, expressNo).then(() => {
                this.$modal.msgSuccess("快递信息设置成功");
                this.getList();
                done();
              }).catch(error => {
                console.error('快递信息设置失败:', error);
                
                let errorMsg = "快递信息设置失败";
                if (error.response && error.response.data && error.response.data.msg) {
                  errorMsg = error.response.data.msg;
                }
                
                this.$modal.msgError(errorMsg);
                instance.confirmButtonLoading = false;
                instance.confirmButtonText = '确定设置';
              }).finally(() => {
                if (instance.confirmButtonLoading) {
                  instance.confirmButtonLoading = false;
                  instance.confirmButtonText = '确定设置';
                }
              });
            } else {
              done();
            }
          }
        });
      },
      
      /** 完成订单 */
      handleComplete(row) {
        this.$modal.confirm(`是否确认完成订单"${row.id}"？`).then(() => {
          return completeOrder(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("订单完成成功");
        });
      },
      
      /** 取消订单 */
      handleCancel(row) {
        this.$modal.confirm(`是否确认取消订单"${row.id}"？`).then(() => {
          return cancelOrder(row.id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("订单取消成功");
        });
      },
      
      /** 批量更新状态 */
      handleBatchUpdate() {
        if (this.multipleSelection.length === 0) {
          this.$modal.msgError("请选择要更新的订单");
          return;
        }
        
        // 根据后端逻辑检查选中的订单是否都可以修改状态
        const finalStatuses = ['CANCELLED', 'COMPLETED', 'RETURNED'];
        const cannotUpdateOrders = this.multipleSelection.filter(order => {
          return finalStatuses.includes(order.status);
        });
        
        if (cannotUpdateOrders.length > 0) {
          const statusNames = cannotUpdateOrders.map(order => 
            `${order.id}(${this.getOrderStatusName(order.status)})`
          ).join('、');
          this.$modal.msgError(`以下订单处于终结状态，不允许批量修改：${statusNames}`);
          return;
        }
        
        this.openBatchUpdate = true;
      },
      
      /** 提交批量更新 */
      submitBatchUpdate() {
        if (!this.batchForm.status) {
          this.$modal.msgError("请选择新状态");
          return;
        }
        
        // 检查状态流转的合理性
        const targetStatus = this.batchForm.status;
        const invalidOrders = this.multipleSelection.filter(order => {
          // 不允许的状态流转
          if (order.status === 'DELIVERED' && targetStatus === 'SHIPPED') return true;
          if (order.status === 'SHIPPED' && ['PENDING', 'PROCESSING', 'PACKAGED'].includes(targetStatus)) return true;
          return false;
        });
        
        if (invalidOrders.length > 0) {
          const orderInfo = invalidOrders.map(order => 
            `${order.id}(${this.getOrderStatusName(order.status)})`
          ).join('、');
          this.$modal.msgError(`以下订单不允许更新到目标状态：${orderInfo}`);
          return;
        }
        
        // 显示确认信息
        const orderCount = this.multipleSelection.length;
        const targetStatusName = this.getOrderStatusName(targetStatus);
        
        this.$modal.confirm(`确定要将选中的 ${orderCount} 个订单状态更新为"${targetStatusName}"吗？`, '批量状态更新确认', {
          confirmButtonText: '确定更新',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 执行批量更新
          const promises = this.multipleSelection.map(order => 
            updateOrderStatus(order.id, targetStatus)
          );
          
          Promise.all(promises).then(() => {
            this.$modal.msgSuccess(`成功更新 ${orderCount} 个订单状态`);
            this.openBatchUpdate = false;
            this.batchForm.status = null;
            this.getList();
          }).catch(error => {
            console.error('批量更新失败:', error);
            
            let errorMsg = "批量更新失败";
            if (error.response && error.response.data && error.response.data.msg) {
              errorMsg = error.response.data.msg;
            }
            
            this.$modal.msgError(errorMsg + "，请检查订单状态并重试");
          });
        });
      },
      
      // 工具方法
      getStatusColor(status) {
        return getOrderStatusColor(status);
      },
      
      getOrderStatusName(status) {
        if (!status) return '未知状态';
        return getOrderStatusName(status) || status;
      },
      
      getOrderCategoryName(category) {
        return getOrderCategoryName(category);
      },
      
      // 根据后端逻辑重新实现的操作可用性判断
      canModifyOrder(status) {
        // 只有PENDING、PROCESSING、PACKAGED状态可以修改订单基本信息
        return ['PENDING', 'PROCESSING', 'PACKAGED'].includes(status);
      },
      
      canUpdateStatus(status) {
        // 终结状态不允许状态更新
        const finalStatuses = ['CANCELLED', 'COMPLETED', 'RETURNED'];
        return !finalStatuses.includes(status);
      },
      
      canCancelOrder(status) {
        // 根据后端逻辑，已发货、已签收、已完成的订单不可取消
        const nonCancellableStatuses = ['SHIPPED', 'DELIVERED', 'COMPLETED', 'CANCELLED'];
        return !nonCancellableStatuses.includes(status);
      },
      
      canSetExpressInfo(row) {
        // 只有外部订单的特定状态可以设置快递信息
        if (row.category !== 'EXTERNAL') return false;
        const allowedStatuses = ['PENDING', 'PROCESSING', 'PACKAGED', 'SHIPPED'];
        return allowedStatuses.includes(row.status);
      },
      
      canCompleteOrder(row) {
        // 根据订单类型检查是否可以完成
        if (row.category === 'INTERNAL') {
          // 内部订单：处理中状态可以直接完成
          return row.status === 'PROCESSING';
        } else {
          // 外部订单：只有已签收状态才能完成
          return row.status === 'DELIVERED';
        }
      },
      
      // 保持向后兼容的旧方法
      isModifiable(status) {
        return this.canModifyOrder(status);
      },
      
      canCancel(status) {
        return this.canCancelOrder(status);
      },
      
      canSetExpress(row) {
        return this.canSetExpressInfo(row);
      },
      
      canComplete(row) {
        return this.canCompleteOrder(row);
      },
      
      getStatusOptionsByCategory(category) {
        if (category === 'EXTERNAL') {
          return EXTERNAL_ORDER_STATUS_OPTIONS;
        } else if (category === 'INTERNAL') {
          return INTERNAL_ORDER_STATUS_OPTIONS;
        }
        return ORDER_STATUS_OPTIONS;
      },
      
      /** 设置表格高度 */
      setTableHeight() {
        this.$nextTick(() => {
          const windowHeight = window.innerHeight;
          const headerHeight = 60; // 顶部导航高度
          const formHeight = this.showSearch ? 120 : 60; // 搜索表单高度
          const buttonHeight = 60; // 操作按钮高度
          const paginationHeight = 60; // 分页高度
          const marginHeight = 40; // 其他边距

          const calculatedHeight = windowHeight - headerHeight - formHeight - buttonHeight - paginationHeight - marginHeight;
          this.tableHeight = Math.max(300, calculatedHeight) + 'px';
        });
      },

      /** 商品选择相关方法 */
      // 选择商品
      selectProduct(index) {
        this.currentItemIndex = index;
        this.openProductSelect = true;
        this.searchProducts(); // 默认加载所有商品
      },

      // 搜索商品
      searchProducts() {
        this.productLoading = true;
        listInventory(this.productSearchForm.materialType, this.productSearchForm.materialName)
          .then(response => {
            if (response.data) {
              this.productList = response.data;
            } else {
              this.productList = [];
            }
          })
          .catch(error => {
            console.error('搜索商品失败:', error);
            this.$modal.msgError('搜索商品失败');
            this.productList = [];
          })
          .finally(() => {
            this.productLoading = false;
          });
      },

      // 重置商品搜索
      resetProductSearch() {
        this.productSearchForm = {
          materialType: '',
          materialName: ''
        };
        this.searchProducts();
      },

      // 选择商品行
      selectProductRow(row) {
        // 可以在这里添加行选中的逻辑
      },

        // 确认选择商品
        confirmSelectProduct(product) {
          if (this.currentItemIndex >= 0 && this.currentItemIndex < this.form.items.length) {
            // 更新选中行的商品信息
            this.$set(this.form.items, this.currentItemIndex, {
              ...this.form.items[this.currentItemIndex],
              productId: product.materialId,
              productCategory: product.materialType,
              productName: product.materialName,
              inventory: product.stockQuantity, // 使用库存接口返回的库存信息
              quantity: this.form.items[this.currentItemIndex].quantity || 1,
              unitPrice: this.form.items[this.currentItemIndex].unitPrice || 0,
              totalPrice: (this.form.items[this.currentItemIndex].quantity || 1) * (this.form.items[this.currentItemIndex].unitPrice || 0)
            });

            // 重新计算小计
            this.calculateItemTotal(this.form.items[this.currentItemIndex]);
          }

          // 关闭对话框
          this.openProductSelect = false;
          this.currentItemIndex = -1;
          
          this.$modal.msgSuccess('商品选择成功');
        },

        // 设置任务
        handleSetTask(row) {
          const orderId = row.id;
          
          // 二次确认是否可以设置任务
          if (!this.canSetTask(row.status)) {
            const statusName = this.getOrderStatusName(row.status);
            this.$modal.msgWarning(`当前订单状态为"${statusName}"，无法设置任务。只有"待处理"状态的订单才能设置任务。`);
            return;
          }
          
          // 获取订单详情
          this.getOrderDetails(orderId).then(() => {
            this.currentOrder = { ...this.form };
            // 为每个物品初始化任务数组
            if (this.currentOrder.items && this.currentOrder.items.length > 0) {
              this.currentOrder.items.forEach(item => {
                this.$set(item, 'tasks', [{
                  taskType: 'PRODUCE',
                  taskLevel: 0,
                  routeCode: '',

                  remark: '',
                  createName: this.$store.state.user.name || '',
                  updateName: this.$store.state.user.name || '',
                  expectedTime: ''
                }]);
              });
            }
            // 获取工艺路线列表
            this.getProcessRouteList();
            this.openTaskSetting = true;
          }).catch(error => {
            console.error('获取订单详情失败:', error);
            this.$modal.msgError("获取订单详情失败，请重试");
          });
        },

        // 获取工艺路线列表
        getProcessRouteList() {
          getProcessRoute().then(response => {
            if (response.code === 0 && response.data) {
              this.processRoutes = response.data;
            } else {
              this.processRoutes = [];
            }
          }).catch(error => {
            console.error('获取工艺路线失败:', error);
            this.processRoutes = [];
          });
        },

        // 判断是否可以设置任务
        canSetTask(status) {
          // 只有"待处理"状态的订单才能设置任务
          // 一旦设置任务后状态会变为"处理中"，就不能再次设置任务
          return status === 'PENDING';
        },

        // 为物品添加任务
        addTaskForItem(item) {
          if (item.tasks.length >= 3) {
            this.$modal.msgWarning('每个物品最多只能添加3个任务');
            return;
          }
          
          const newTask = {
            taskType: 'PRODUCE',
            taskLevel: 0,
            routeCode: '',

            remark: '',
            createName: this.$store.state.user.name || '',
            updateName: this.$store.state.user.name || '',
            expectedTime: ''
          };
          
          item.tasks.push(newTask);
        },

        // 从物品中删除任务
        removeTaskFromItem(item, taskIndex) {
          if (item.tasks.length <= 1) {
            this.$modal.msgWarning('至少需要保留一个任务');
            return;
          }
          
          item.tasks.splice(taskIndex, 1);
        },

        // 检查工艺路线是否已被该物品的其他任务使用
        isRouteUsedByItem(item, routeCode, currentTaskIndex) {
          if (!routeCode) return false;
          
          return item.tasks.some((task, index) => {
            return index !== currentTaskIndex && task.routeCode === routeCode;
          });
        },

        // 取消任务设置
        cancelTaskSetting() {
          this.openTaskSetting = false;
          this.currentOrder = null;
          this.taskSettingLoading = false;
        },

        // 提交任务设置
        submitTaskSetting() {
          if (!this.currentOrder || !this.currentOrder.items || this.currentOrder.items.length === 0) {
            this.$modal.msgError("该订单无物品信息，无法设置任务");
            return;
          }

          // 验证表单
          let hasError = false;
          for (let i = 0; i < this.currentOrder.items.length; i++) {
            const item = this.currentOrder.items[i];
            
            if (!item.tasks || item.tasks.length === 0) {
              this.$modal.msgError(`物品"${item.productName}"至少需要设置一个任务`);
              hasError = true;
              break;
            }

            for (let j = 0; j < item.tasks.length; j++) {
              const task = item.tasks[j];
              if (!task.taskType) {
                this.$modal.msgError(`请为物品"${item.productName}"的任务${j + 1}选择任务类型`);
                hasError = true;
                break;
              }
              if (task.taskLevel === null || task.taskLevel === undefined) {
                this.$modal.msgError(`请为物品"${item.productName}"的任务${j + 1}选择任务级别`);
                hasError = true;
                break;
              }

              if (task.taskType === 'PRODUCE' && !task.routeCode) {
                this.$modal.msgError(`请为物品"${item.productName}"的任务${j + 1}选择工艺路线`);
                hasError = true;
                break;
              }
              if (!task.expectedTime) {
                this.$modal.msgError(`请为物品"${item.productName}"的任务${j + 1}设置预期完成时间`);
                hasError = true;
                break;
              }
            }
            
            if (hasError) break;
          }

          if (hasError) {
            return;
          }

          this.taskSettingLoading = true;

          // 为每个物品创建任务
          const taskPromises = this.currentOrder.items.map(item => {
            const taskCreateRequest = {
              orderId: this.currentOrder.id,
              itemId: item.productId,
              itemName: item.productName,
              itemType: item.productCategory,
              quantity: item.quantity || 1,
              tasks: item.tasks.map(task => ({
                taskType: task.taskType,
                taskLevel: task.taskLevel,
                routeCode: task.routeCode || '',

                remark: task.remark || '',
                createName: task.createName,
                updateName: task.updateName,
                expectedTime: task.expectedTime || ''
              }))
            };

            return createOrderTask(taskCreateRequest);
          });

          Promise.all(taskPromises).then(responses => {
            let totalTasks = 0;
            responses.forEach(response => {
              if (response.code === 0 && response.data) {
                totalTasks += response.data.length;
              }
            });
            
            // 任务创建成功后，自动将订单状态更新为"处理中"
            return updateOrderStatus(this.currentOrder.id, 'PROCESSING').then(() => {
              this.taskSettingLoading = false;
              this.openTaskSetting = false;
              this.currentOrder = null;
              this.$modal.msgSuccess(`成功创建 ${totalTasks} 个任务，订单状态已更新为"处理中"`);
              // 刷新订单列表以显示最新状态
              this.getList();
            });
          }).catch(error => {
            console.error('创建任务或更新状态失败:', error);
            this.taskSettingLoading = false;
            
            // 更详细的错误处理
            let errorMsg = "操作失败";
            if (error.response && error.response.data) {
              if (error.response.data.msg) {
                errorMsg = error.response.data.msg;
              } else if (error.response.data.message) {
                errorMsg = error.response.data.message;
              }
            } else if (error.message) {
              errorMsg = error.message;
            }
            
            this.$modal.msgError(errorMsg + "，请重试");
          });
        },

        // 库存状态相关方法（基于订单接口返回的库存数据）
        getStockStatusType(order) {
          if (!order.items || order.items.length === 0) {
            return 'info';
          }
          
          const hasInsufficientStock = order.items.some(item => {
            // 使用 inventory 字段（从订单接口返回）
            return item.inventory !== undefined && item.quantity > item.inventory;
          });
          
          if (hasInsufficientStock) {
            return 'danger';
          }
          
          const hasLowStock = order.items.some(item => {
            return item.inventory !== undefined && item.inventory === 0;
          });
          
          if (hasLowStock) {
            return 'warning';
          }
          
          return 'success';
        },

        getStockStatusText(order) {
          if (!order.items || order.items.length === 0) {
            return '无商品';
          }
          
          const hasInsufficientStock = order.items.some(item => {
            // 使用 inventory 字段（从订单接口返回）
            return item.inventory !== undefined && item.quantity > item.inventory;
          });
          
          if (hasInsufficientStock) {
            return '库存不足';
          }
          
          const hasLowStock = order.items.some(item => {
            return item.inventory !== undefined && item.inventory === 0;
          });
          
          if (hasLowStock) {
            return '库存偏低';
          }
          
          return '库存充足';
        },

        getStockSummary(order) {
          if (!order.items || order.items.length === 0) {
            return '';
          }
          
          // 计算总订单数量和总库存数量
          let totalOrderQuantity = 0;
          let totalStockQuantity = 0;
          
          order.items.forEach(item => {
            totalOrderQuantity += item.quantity || 0;
            totalStockQuantity += item.inventory || 0;
          });
          
          // 显示具体的数量信息
          return `需求:${totalOrderQuantity} 库存:${totalStockQuantity}`;
        },

        // 获取详细的库存对比信息（用于详情页面）
        getDetailedInventoryComparison(order) {
          if (!order.items || order.items.length === 0) {
            return '暂无商品信息';
          }

          const comparisonDetails = order.items.map((item, index) => {
            const orderQty = item.quantity || 0;
            const stockQty = item.inventory || 0;
            const status = orderQty > stockQty ? '⚠️库存不足' : stockQty === 0 ? '❌无库存' : '✅库存充足';
            const difference = stockQty - orderQty;
            const diffText = difference >= 0 ? `(余${difference})` : `(缺${Math.abs(difference)})`;
            
            return `${index + 1}. ${item.productName}: 需求${orderQty} / 库存${stockQty} ${diffText} ${status}`;
          });

          // 计算总计
          const totalOrderQuantity = order.items.reduce((sum, item) => sum + (item.quantity || 0), 0);
          const totalStockQuantity = order.items.reduce((sum, item) => sum + (item.inventory || 0), 0);
          const totalDifference = totalStockQuantity - totalOrderQuantity;
          const totalStatus = totalDifference >= 0 ? 
            (totalDifference > 0 ? `✅总体库存充足(余${totalDifference})` : '✅总体库存刚好满足') : 
            `⚠️总体库存不足(缺${Math.abs(totalDifference)})`;
          
          const summary = `【总计】需求${totalOrderQuantity} / 库存${totalStockQuantity} - ${totalStatus}`;
          
          return [...comparisonDetails, '', summary].join('\n');
        },

        // 获取单个物品的库存状态类型（用于任务设置页面）
        getItemStockStatusType(item) {
          if (!item.inventory && item.inventory !== 0) {
            return 'info';
          }
          
          if (item.inventory === 0) {
            return 'danger';
          }
          
          if (item.quantity > item.inventory) {
            return 'warning';
          }
          
          return 'success';
        },

        // 获取单个物品的库存状态文本（用于任务设置页面）
        getItemStockStatusText(item) {
          if (!item.inventory && item.inventory !== 0) {
            return '库存未知';
          }
          
          if (item.inventory === 0) {
            return '无库存';
          }
          
          if (item.quantity > item.inventory) {
            const shortage = item.quantity - item.inventory;
            return `库存不足(缺${shortage})`;
          }
          
          const surplus = item.inventory - item.quantity;
          if (surplus > 0) {
            return `库存充足(余${surplus})`;
          } else {
            return '库存刚好';
          }
        },

        // 获取商品库存状态文本（用于详情页面库存快照）
        getItemStatusText(item) {
          const orderQty = item.quantity || 0;
          const stockQty = item.inventory || 0;
          const difference = stockQty - orderQty;
          
          if (difference > 0) {
            return `（富余）`;
          } else if (difference < 0) {
            return `（短缺）`;
          } else {
            return `（刚好）`;
          }
        },

        // 获取商品库存状态图标（用于详情页面库存快照）
        getItemStatusIcon(item) {
          const orderQty = item.quantity || 0;
          const stockQty = item.inventory || 0;
          
          if (stockQty >= orderQty) {
            return '✓库存充足';
          } else {
            return '⚠库存不足';
          }
        }
    }
  };
  </script>
  
  <style scoped>
.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
.order-table {
  border-radius: 4px;
  overflow: hidden;
}

/* 快递单号样式 */
.express-no-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-regular, #606266);
}

/* 地址样式 */
.address-text {
  color: var(--text-color-regular, #606266);
  line-height: 1.4;
}

/* 金额样式 */
.amount-text {
  color: var(--theme-color, #E6A23C);
  font-weight: bold;
  font-size: 14px;
}

/* 时间信息样式 */
.time-info {
  line-height: 1.3;
}

.time-info > div:first-child {
  color: var(--text-color-primary, #303133);
  font-size: 13px;
}

.time-detail {
  color: var(--text-color-secondary, #909399);
  font-size: 11px;
  margin-top: 2px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.operation-btn {
  padding: 4px 6px;
  font-size: 12px;
  border-radius: 2px;
}

.operation-dropdown {
  margin-left: 0;
}

/* 主题适配样式 */
::v-deep .el-descriptions__label {
  font-weight: bold;
  color: var(--text-color-primary, #303133);
}

::v-deep .el-descriptions__content {
  color: var(--text-color-regular, #606266);
}

::v-deep .el-table {
  background: var(--bg-color, #ffffff);
  color: var(--text-color-primary, #303133);
  border: 1px solid var(--border-color-lighter, #f2f6fc);
}

::v-deep .el-table th {
  background: var(--bg-color-page, #f5f7fa);
  color: var(--text-color-primary, #303133);
  border-bottom: 1px solid var(--border-color-light, #ebeef5);
}

::v-deep .el-table td {
  border-bottom: 1px solid var(--border-color-light, #ebeef5);
}

::v-deep .el-table--border {
  border: 1px solid var(--border-color-lighter, #f2f6fc);
}

::v-deep .el-table--border td, 
::v-deep .el-table--border th {
  border-right: 1px solid var(--border-color-lighter, #f2f6fc);
}

::v-deep .el-table__empty-text {
  color: var(--text-color-placeholder, #c0c4cc);
}

::v-deep .el-table__fixed-right {
  border-left: 1px solid var(--border-color-lighter, #f2f6fc);
}

::v-deep .el-table__fixed {
  border-right: 1px solid var(--border-color-lighter, #f2f6fc);
}

::v-deep .el-form-item__label {
  color: var(--text-color-primary, #303133);
}

::v-deep .el-input__inner {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-base, #dcdfe6);
  color: var(--text-color-primary, #303133);
}

::v-deep .el-input__inner:focus {
  border-color: var(--theme-color, #409eff);
}

::v-deep .el-select-dropdown {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-light, #e4e7ed);
  box-shadow: 0 2px 12px 0 var(--box-shadow-color, rgba(0, 0, 0, 0.1));
}

::v-deep .el-select-dropdown__item {
  color: var(--text-color-regular, #606266);
}

::v-deep .el-select-dropdown__item:hover {
  background: var(--bg-color-page, #f5f7fa);
}

::v-deep .el-dialog {
  background: var(--bg-color, #ffffff);
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--box-shadow-color, rgba(0, 0, 0, 0.3));
}

::v-deep .el-dialog__header {
  background: var(--bg-color, #ffffff);
  border-bottom: 1px solid var(--border-color-light, #ebeef5);
  border-radius: 8px 8px 0 0;
}

::v-deep .el-dialog__title {
  color: var(--text-color-primary, #303133);
}

::v-deep .el-button--primary {
  background: var(--theme-color, #409eff);
  border-color: var(--theme-color, #409eff);
}

::v-deep .el-button--primary:hover {
  background: var(--theme-color-light, #66b1ff);
  border-color: var(--theme-color-light, #66b1ff);
}

::v-deep .el-dropdown-menu {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-light, #e4e7ed);
  box-shadow: 0 2px 12px 0 var(--box-shadow-color, rgba(0, 0, 0, 0.1));
}

::v-deep .el-dropdown-menu__item {
  color: var(--text-color-regular, #606266);
}

::v-deep .el-dropdown-menu__item:hover {
  background: var(--bg-color-page, #f5f7fa);
  color: var(--theme-color, #409eff);
}

/* 任务设置对话框样式 */
.item-task-card {
  transition: all 0.3s ease;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.item-info-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.item-name {
  font-weight: bold;
  color: var(--text-color-primary, #303133);
  font-size: 16px;
  margin-bottom: 4px;
}

.item-info {
  color: var(--text-color-secondary, #909399);
  font-size: 13px;
}

.item-quantity-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 0 0 auto;
}

.quantity-comparison {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  padding: 8px 12px;
  background: var(--bg-color-page, #f8f9fa);
  border-radius: 6px;
  border: 1px solid var(--border-color-lighter, #f2f6fc);
}

.quantity-label {
  color: var(--text-color-regular, #606266);
  font-size: 12px;
  font-weight: 500;
}

.quantity-value {
  font-weight: bold;
  font-size: 14px;
  min-width: 30px;
  text-align: center;
}

.quantity-value.demand {
  color: var(--theme-color, #409eff);
}

.quantity-value.sufficient {
  color: var(--theme-color, #67c23a);
}

.quantity-value.insufficient {
  color: var(--warning-color, #e6a23c);
}

.quantity-value.out-of-stock {
  color: var(--danger-color, #f56c6c);
}

.quantity-divider {
  color: var(--border-color-base, #dcdfe6);
  font-weight: bold;
}

.stock-status-indicator {
  display: flex;
  justify-content: center;
}

.inventory-warning-alert {
  margin-top: 15px;
}

.inventory-warning-alert ::v-deep .el-alert {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--warning-color, #e6a23c);
  border-radius: 6px;
}

.inventory-warning-alert ::v-deep .el-alert__title {
  color: var(--warning-color, #e6a23c);
  font-weight: bold;
}

.inventory-warning-alert ::v-deep .el-alert__description {
  color: var(--text-color-regular, #606266);
  font-size: 13px;
  line-height: 1.4;
}

.inventory-warning-alert ::v-deep .el-alert__icon {
  color: var(--warning-color, #e6a23c);
}

.task-form-item {
  border: 1px solid var(--border-color-lighter, #f2f6fc);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background: var(--bg-color-page, #f8f9fa);
  transition: all 0.3s ease;
}

.task-form-item:hover {
  border-color: var(--theme-color-light, #c6e2ff);
  background: var(--bg-color, #ffffff);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color-lighter, #f2f6fc);
}

.task-title {
  font-weight: 600;
  color: var(--text-color-primary, #303133);
  font-size: 14px;
}

::v-deep .el-card {
  border: 1px solid var(--border-color-lighter, #f2f6fc);
  transition: all 0.3s ease;
}

::v-deep .el-card:hover {
  border-color: var(--theme-color, #409eff);
  box-shadow: 0 2px 12px 0 var(--box-shadow-color, rgba(64, 158, 255, 0.1));
}

::v-deep .el-card__header {
  background: var(--bg-color-page, #f5f7fa);
  border-bottom: 1px solid var(--border-color-light, #ebeef5);
  padding: 15px 20px;
}

::v-deep .el-card__body {
  padding: 20px;
}

/* 库存状态样式 */
.stock-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stock-detail {
  text-align: center;
  word-break: break-word;
}

/* 详情页面库存快照样式 */
.inventory-snapshot-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 600px;
}

.inventory-items-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.inventory-item-row {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: var(--bg-color-page, #f8f9fa);
  border: 1px solid var(--border-color-lighter, #f2f6fc);
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.inventory-item-row .item-name {
  font-weight: 600;
  color: var(--text-color-primary, #303133);
  min-width: 120px;
  flex-shrink: 0;
}

.inventory-item-row .demand-info {
  color: var(--theme-color, #409eff);
  font-weight: 500;
}

.inventory-item-row .divider {
  color: var(--text-color-secondary, #909399);
  font-weight: bold;
}

.inventory-item-row .stock-info {
  color: var(--text-color-regular, #606266);
  font-weight: 500;
}

.inventory-item-row .status-info {
  font-size: 12px;
  margin-left: 4px;
}

.inventory-item-row .status-info.sufficient {
  color: var(--success-color, #67c23a);
}

.inventory-item-row .status-info.insufficient {
  color: var(--warning-color, #e6a23c);
}

.inventory-item-row .status-icon {
  font-size: 12px;
  font-weight: 600;
  margin-left: 6px;
}

.inventory-item-row .status-icon.success-icon {
  color: var(--success-color, #67c23a);
}

.inventory-item-row .status-icon.warning-icon {
  color: var(--warning-color, #e6a23c);
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .order-table {
    font-size: 12px;
  }
  
  .express-no-text {
    font-size: 10px;
  }
  
  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }
  
  .stock-detail {
    font-size: 10px !important;
  }
  
  /* 任务设置对话框响应式 */
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .item-quantity-info {
    align-self: stretch;
  }
  
  .quantity-comparison {
    justify-content: center;
    flex-wrap: wrap;
    gap: 6px;
    font-size: 12px;
    padding: 6px 10px;
  }
  
  .quantity-label {
    font-size: 11px;
  }
  
  .quantity-value {
    font-size: 12px;
    min-width: 25px;
  }
}

@media screen and (max-width: 768px) {
  .order-table {
    font-size: 11px;
  }
  
  .operation-btn {
    padding: 2px 4px;
    font-size: 10px;
  }
  
  /* 小屏幕下的任务设置对话框优化 */
  .item-name {
    font-size: 14px;
  }
  
  .item-info {
    font-size: 12px;
  }
  
  .quantity-comparison {
    flex-direction: column;
    gap: 4px;
    padding: 8px;
    text-align: center;
  }
  
  .quantity-label {
    font-size: 10px;
  }
  
  .quantity-value {
    font-size: 11px;
  }
  
  .quantity-divider {
    display: none;
  }
}

/* 商品选择对话框主题适配 */
::v-deep .el-dialog__header {
  border-bottom: 1px solid var(--border-color-light, #ebeef5);
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--table-row-hover-bg, #f5f7fa);
}

::v-deep .el-table__body tr.current-row > td {
  background-color: var(--theme-color-light, #ecf5ff);
}

::v-deep .el-button--mini.is-disabled {
  background-color: var(--bg-color-page, #f5f7fa);
  border-color: var(--border-color-lighter, #f2f6fc);
  color: var(--text-color-placeholder, #c0c4cc);
}
</style> 
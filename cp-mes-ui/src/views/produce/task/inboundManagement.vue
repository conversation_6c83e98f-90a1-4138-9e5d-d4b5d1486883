<template>
  <div class="inbound-management-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购单号" prop="purchaseOrderNo">
        <el-input
          v-model="queryParams.purchaseOrderNo"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域代码" prop="zoneCode">
        <el-input
          v-model="queryParams.zoneCode"
          placeholder="请输入区域代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人员" prop="operator">
        <el-input
          v-model="queryParams.operator"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- 修改按钮已移除 -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getInboundList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inboundList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="采购单号" align="center" prop="purchaseOrderNo" width="180" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag type="success">已入库</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="区域代码" align="center" prop="zoneCode" />
      <el-table-column label="操作人员" align="center" prop="operator" />
      <el-table-column label="物料类型" align="center" prop="materialType" />
      <!-- 🔧 新增：板类型列 -->
      <el-table-column label="板类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.boardType" :type="getBoardTypeTagType(scope.row.boardType)" size="mini">
            {{ getBoardTypeText(scope.row.boardType) }}
          </el-tag>
          <span v-else class="text-muted">--</span>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="二维码" align="center" prop="qrCode" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.qrCode" class="qr-code-text">{{ scope.row.qrCode }}</span>
          <span v-else style="color: #ccc;">--</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160" fixed="right">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              class="action-btn view-btn">
              查看
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              class="action-btn delete-btn">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getInboundList" />

    <!-- 查看详情对话框 -->
    <el-dialog title="已入库详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="采购单号">{{ viewData.purchaseOrderNo }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag type="success">已入库</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="区域代码">{{ viewData.zoneCode }}</el-descriptions-item>
        <el-descriptions-item label="操作人员">{{ viewData.operator }}</el-descriptions-item>
        <el-descriptions-item label="物料类型">{{ viewData.materialType }}</el-descriptions-item>
        <el-descriptions-item label="物料名称">{{ viewData.materialName }}</el-descriptions-item>
        <el-descriptions-item label="二维码">{{ viewData.qrCode || '--' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewData.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark || '--' }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 修改已入库单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="采购单号" prop="purchaseOrderNo">
              <el-input v-model="form.purchaseOrderNo" placeholder="请输入采购单号" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="区域代码" prop="zoneCode">
              <el-input v-model="form.zoneCode" placeholder="请输入区域代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作人员" prop="operator">
              <el-input v-model="form.operator" placeholder="请输入操作人员" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="物料类型" prop="materialType">
              <el-input v-model="form.materialType" placeholder="请输入物料类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialName">
              <el-input v-model="form.materialName" placeholder="请输入物料名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="二维码" prop="qrCode">
              <el-input v-model="form.qrCode" placeholder="请输入二维码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addOrUpdatePurchaseInBound,
  deletePurchaseInBound,
  getPurchaseInboundList
} from "@/api/jenasi/purchaseInbound";

export default {
  name: "InboundManagement",
  data() {
    return {
      // 状态映射
      statusMap: {
        0: '待入库',
        1: '已入库',
        2: '已取消'
      },
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 已入库单表格数据
      inboundList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情对话框
      viewOpen: false,
      // 查看详情数据
      viewData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseOrderNo: undefined,
        status: 1, // 固定为已入库状态
        zoneCode: undefined,
        operator: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseOrderNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" }
        ],
      }
    };
  },

  created() {
    this.getInboundList();
  },
  
  methods: {
    /** 查询已入库单列表 */
    getInboundList() {
      this.loading = true;
      getPurchaseInboundList(this.queryParams).then(response => {
        // 根据实际接口响应结构处理数据
        if (response.data && response.data.records) {
          this.inboundList = response.data.records;
          this.total = response.data.total || 0;
        } else {
          this.inboundList = response.rows || [];
          this.total = response.total || 0;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 向后兼容，保持原有方法名
    getList() {
      this.getInboundList();
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        purchaseOrderNo: undefined,
        status: 1, // 固定为已入库状态
        qrCode: undefined,
        zoneCode: undefined,
        operator: undefined,
        materialName: undefined,
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getInboundList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewData = Object.assign({}, row);
      this.viewOpen = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      // 这里可以调用获取详情的接口，目前直接使用行数据
      this.form = { ...row };
      this.open = true;
      this.title = "修改已入库单";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          addOrUpdatePurchaseInBound(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getInboundList();
          }).catch(() => {
            this.$modal.msgError("操作失败");
          }).finally(() => {
            this.buttonLoading = false;
          });
        }
      });
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除选中的已入库单？').then(() => {
        this.loading = true;
        const deletePromises = ids.map(id => deletePurchaseInBound(id));
        return Promise.all(deletePromises);
      }).then(() => {
        this.loading = false;
        this.getInboundList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 获取状态文本 */
    getStatusText(status) {
      return this.statusMap[status] || '未知状态';
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('wms/purchaseInBound/export', {
        ...this.queryParams
      }, `inbound_management_${new Date().getTime()}.xlsx`)
    },

    // 🔧 获取板类型文本
    getBoardTypeText(boardType) {
      const boardTypeMap = {
        '上板': '上板',
        '下板': '下板',
        '单板': '单板'
      };
      return boardTypeMap[boardType] || boardType || '--';
    },

    // 🔧 获取板类型标签样式
    getBoardTypeTagType(boardType) {
      const typeMap = {
        '上板': 'primary',
        '下板': 'success',
        '单板': 'info'
      };
      return typeMap[boardType] || 'info';
    }
  }
};
</script>

<style lang="scss" scoped>
.inbound-management-container {
  background: var(--base-body-background);
  padding: 20px;
  transition: all 0.3s ease;
  color: var(--theme-color);
}

/* 继承主页面的样式 */
:deep(.el-form) {
  background: var(--base-main-bg);
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
}

:deep(.el-table) {
  background: var(--base-main-bg);
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
}

:deep(.el-table__header-wrapper th) {
  background: var(--current-color) !important;
  color: white !important;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 0;
  border: none;
}

:deep(.el-table__row) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background: var(--table-row-hover-bg) !important;
}

:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px;
  margin: 0;
  border-radius: 12px 12px 0 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

/* 操作按钮美化样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.action-btn {
  padding: 6px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
  min-width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-btn i {
  margin-right: 4px;
  font-size: 12px;
}

.view-btn {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.2) !important;
}

.view-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409EFF !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-btn {
  color: #F56C6C !important;
  background: rgba(245, 108, 108, 0.1) !important;
  border-color: rgba(245, 108, 108, 0.2) !important;
}

.delete-btn:hover {
  background: rgba(245, 108, 108, 0.2) !important;
  border-color: #F56C6C !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

/* 二维码文本样式 */
.qr-code-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #606266;
  background: rgba(64, 158, 255, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
    flex-wrap: wrap;
  }

  .action-btn {
    width: 100%;
    min-width: auto;
  }
}
</style>

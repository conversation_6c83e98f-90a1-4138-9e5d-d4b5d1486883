<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-s-order"></i>
        采购入库管理系统
      </h2>
      <p class="page-description">统一管理采购入库的全流程操作，包括待入库和已入库数据的管理</p>
    </div>

    <!-- 任务类型标签页导航 -->
    <el-card class="nav-card" shadow="never">
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="采购入库" name="purchase">
          <template slot="label">
            <i class="el-icon-shopping-cart-2"></i>
            采购入库
          </template>
        </el-tab-pane>
        <el-tab-pane label="已入库管理" name="inbound">
          <template slot="label">
            <i class="el-icon-finished"></i>
            已入库管理
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 采购入库内容 -->
      <div v-show="activeTab === 'purchase'" class="tab-content">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="采购单号" prop="purchaseOrderNo">
            <el-input
              v-model="queryParams.purchaseOrderNo"
              placeholder="请输入采购单号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="区域代码" prop="zoneCode">
            <el-input
              v-model="queryParams.zoneCode"
              placeholder="请输入区域代码"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="操作人员" prop="operator">
            <el-input
              v-model="queryParams.operator"
              placeholder="请输入操作人员"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable
                       @keyup.enter.native="handleQuery">
              <el-option label="待入库" :value="0"></el-option>
              <el-option label="已取消" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getPurchaseList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="inboundList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" width="80" />
          <el-table-column label="采购单号" align="center" prop="purchaseOrderNo" width="180" />
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 0" type="warning">待入库</el-tag>
              <el-tag v-else-if="scope.row.status == 1" type="success">已入库</el-tag>
              <el-tag v-else-if="scope.row.status == 2" type="danger">已取消</el-tag>
              <el-tag v-else type="info">未知</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="区域代码" align="center" prop="zoneCode" />
          <el-table-column label="操作人员" align="center" prop="operator" />
          <el-table-column label="物料类型" align="center" prop="materialType" />
          <!-- 🔧 新增：板类型列 -->
          <el-table-column label="板类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.boardType" :type="getBoardTypeTagType(scope.row.boardType)" size="mini">
                {{ getBoardTypeText(scope.row.boardType) }}
              </el-tag>
              <span v-else class="text-muted">--</span>
            </template>
          </el-table-column>
          <el-table-column label="物料名称" align="center" prop="materialName" />
          <el-table-column label="二维码" align="center" prop="qrCode" width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.qrCode" class="qr-code-text">{{ scope.row.qrCode }}</span>
              <span v-else style="color: #ccc;">--</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
          <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
          <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="320" fixed="right">
            <template slot-scope="scope">
              <div class="purchase-action-buttons">
                <!-- 主要操作按钮 - 直接显示 -->
                <el-button
                  v-if="scope.row.status === 0"
                  size="mini"
                  type="text"
                  icon="el-icon-printer"
                  @click="handlePrintQrCode(scope.row)"
                  class="purchase-action-btn print-btn">
                  打印二维码
                </el-button>
                <!-- 确认入库按钮已隐藏 -->
                <el-button
                  v-if="scope.row.status === 0 && scope.row.qrCode && scope.row.qrCode.trim() !== ''"
                  size="mini"
                  type="text"
                  icon="el-icon-upload2"
                  @click="handlePurchaseInbound(scope.row)"
                  class="purchase-action-btn inbound-btn">
                  采购入库
                </el-button>

                <!-- 更多操作下拉菜单 -->
                <el-dropdown
                  size="mini"
                  @command="handleDropdownCommand($event, scope.row)"
                  class="purchase-dropdown">
                  <el-button
                    size="mini"
                    type="text"
                    class="purchase-action-btn more-btn">
                    更多
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit">
                      <i class="el-icon-edit"></i>
                      修改
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <i class="el-icon-delete"></i>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="getPurchaseList" />

        <!-- 添加或修改采购入库单对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="采购单号" prop="purchaseOrderNo">
                  <el-input v-model="form.purchaseOrderNo" placeholder="请输入采购单号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
                    <el-option label="待入库" :value="0"></el-option>
                    <el-option label="已取消" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="区域代码" prop="zoneCode">
                  <el-input v-model="form.zoneCode" placeholder="请输入区域代码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="操作人员" prop="operator">
                  <el-input v-model="form.operator" placeholder="请输入操作人员" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="二维码" prop="qrCode">
                  <el-input v-model="form.qrCode" placeholder="请输入二维码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="物料类型" prop="materialType">
                  <el-input v-model="form.materialType" placeholder="请输入物料类型" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物料名称" prop="materialName">
                  <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </div>

      <!-- 已入库管理内容 - 使用组件 -->
      <div v-show="activeTab === 'inbound'" class="tab-content">
        <InboundManagement ref="inboundManagementRef" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  addOrUpdatePurchaseInBound,
  deletePurchaseInBound,
  getPurchaseInboundList,
  updatePurchaseInboundQrCode,
  purchaseInboundWithInventory
} from "@/api/jenasi/purchaseInbound";
import {
  getPurchaseOrderDetail,
  printPurchaseInboundQrCode
} from "@/api/jenasi/purchaseOrder";
import InboundManagement from './inboundManagement.vue';

export default {
  name: "TaskManager",
  components: {
    InboundManagement
  },
  data() {
    return {
      // 状态映射
      statusMap: {
        0: '待入库',
        1: '已入库',
        2: '已取消'
      },
      // 当前选中的标签页
      activeTab: 'purchase',
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购入库单表格数据
      inboundList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseOrderNo: undefined,
        status: 0, // 默认只显示待入库状态
        zoneCode: undefined,
        operator: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseOrderNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },

  created() {
    // 初始化时根据路由参数设置标签页
    const tabFromQuery = this.$route.query.tab;
    if (tabFromQuery && ['purchase', 'inbound'].includes(tabFromQuery)) {
      this.activeTab = tabFromQuery;
    }

    if (this.activeTab === 'purchase') {
      this.getPurchaseList();
    }
  },

  methods: {
    // 标签页切换处理
    handleTabClick(tab) {
      this.activeTab = tab.name;

      // 更新路由参数
      const query = { ...this.$route.query, tab: tab.name };
      this.$router.replace({ query }).catch(() => {});

      // 刷新当前tab的数据
      this.refreshCurrentTabData();
    },

    refreshCurrentTabData() {
      this.$nextTick(() => {
        if (this.activeTab === 'inbound') {
          // 刷新已入库管理组件数据
          if (this.$refs.inboundManagementRef && this.$refs.inboundManagementRef.getList) {
            this.$refs.inboundManagementRef.getList();
          }
        } else {
          // 刷新采购入库数据
          this.getPurchaseList();
        }
      });
    },

    /** 查询采购入库单列表 */
    getPurchaseList() {
      this.loading = true;
      getPurchaseInboundList(this.queryParams).then(response => {
        // 根据实际接口响应结构处理数据
        if (response.data && response.data.records) {
          this.inboundList = response.data.records;
          this.total = response.data.total || 0;
        } else {
          this.inboundList = response.rows || [];
          this.total = response.total || 0;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    // 向后兼容，保持原有方法名
    getList() {
      if (this.activeTab === 'purchase') {
        this.getPurchaseList();
      } else {
        this.refreshCurrentTabData();
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        purchaseOrderNo: undefined,
        status: undefined,
        qrCode: undefined,
        zoneCode: undefined,
        operator: undefined,
        remark: undefined,
        materialType: undefined,
        materialName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getPurchaseList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购入库单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      // 这里可以调用获取详情的接口，目前直接使用行数据
      this.form = { ...row };
      this.open = true;
      this.title = "修改采购入库单";
    },

    /** 处理下拉菜单命令 */
    handleDropdownCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
        default:
          console.warn('未知的下拉菜单命令:', command);
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          addOrUpdatePurchaseInBound(this.form).then(response => {
            this.$modal.msgSuccess(this.form.id ? "修改成功" : "新增成功");
            this.open = false;
            this.getPurchaseList();
          }).catch(() => {
            this.$modal.msgError("操作失败");
          }).finally(() => {
            this.buttonLoading = false;
          });
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$modal.confirm('是否确认删除选中的采购入库单？').then(() => {
        this.loading = true;
        const deletePromises = ids.map(id => deletePurchaseInBound(id));
        return Promise.all(deletePromises);
      }).then(() => {
        this.loading = false;
        this.getPurchaseList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('wms/purchaseInBound/export', {
        ...this.queryParams
      }, `purchase_inbound_${new Date().getTime()}.xlsx`)
    },

    /** 打印二维码按钮操作 */
    async handlePrintQrCode(row) {
      try {
        this.$message.info('正在准备打印数据...');

        // 1. 根据采购单号查询采购订单详情
        const orderResponse = await getPurchaseOrderDetail({
          purchaseNo: row.purchaseOrderNo,
          pageNum: 1,
          pageSize: 1
        });

        if (!orderResponse.data || !orderResponse.data.records || orderResponse.data.records.length === 0) {
          this.$message.error('未找到该采购单的详细信息');
          return;
        }

        const orderInfo = orderResponse.data.records[0];

        // 2. 从采购入库记录获取批次号和板型
        let batchNo = '';
        let boardType = '';
        try {
          const { getPurchaseInboundByOrderNo } = await import('@/api/jenasi/purchaseInbound');
          const inboundResponse = await getPurchaseInboundByOrderNo(row.purchaseOrderNo);
          if (inboundResponse.code === 0 && inboundResponse.data) {
            batchNo = inboundResponse.data.batchNo || '';
            boardType = inboundResponse.data.boardType || '';
            console.log('从采购入库记录获取批次号和板型:', { batchNo, boardType });
          } else {
            this.$message.warning('未找到该采购单的入库记录，请先确认收货');
            return;
          }
        } catch (error) {
          console.error('获取采购入库记录失败:', error);
          this.$message.error('获取批次号失败，请先确认收货');
          return;
        }

        // 3. 构建打印数据（使用从采购入库记录获取的批次号和板型）
        const printData = {
          label_type: "purchase",
          purchase_no: orderInfo.purchaseNo,
          item_name: orderInfo.itemName || '物料名称',
          quantity: parseFloat(orderInfo.quantity) || 0,
          batch_no: batchNo, // 使用从采购入库记录获取的批次号
          board_type: this.getBoardTypeDisplay(boardType) // 使用提取的板型变量
        };

        console.log('准备打印的数据:', printData);
        this.$message.info('正在发送打印任务...');

        // 3. 调用打印接口
        const printResponse = await printPurchaseInboundQrCode(printData);

        if (printResponse.code === 0) {
          this.$message.success('打印任务已发送到打印机');
          // 重要：打印成功后立即保存二维码内容到数据库
          await this.updateQrCodeInfo(row, printData);
        } else {
          this.$message.error(printResponse.msg || '打印任务发送失败');
        }

      } catch (error) {
        console.error('打印二维码失败:', error);
        let errorMsg = '打印失败，请检查网络连接和打印机状态';

        // 根据错误信息提供更具体的提示
        if (error.response && error.response.data && error.response.data.msg) {
          errorMsg = error.response.data.msg;

          // 如果是配置相关错误，提供具体的解决建议
          if (errorMsg.includes('未找到可用的采购标签打印机配置')) {
            errorMsg += '\n请联系管理员在系统管理->打印配置中添加采购标签打印机';
          } else if (errorMsg.includes('打印机配置不完整')) {
            errorMsg += '\n请检查打印机IP、端口和API端点配置';
          } else if (errorMsg.includes('无法连接到打印机服务')) {
            errorMsg += '\n请检查打印机服务是否启动（端口8081）';
          }
        }

        this.$message.error(errorMsg);
      }
    },

    /** 更新二维码信息 */
    async updateQrCodeInfo(row, printData) {
      try {
        // 构建二维码内容（与打印机生成的格式保持一致，移除批次号字段）
        let qrContent = `label_type:${printData.label_type}|purchase_no:${printData.purchase_no}`;

        console.log('准备保存二维码内容到数据库:', {
          purchaseOrderNo: printData.purchase_no,
          qrContent: qrContent,
          operator: this.$store.state.user.name || '系统'
        });

        // 调用后端接口更新数据库中的二维码信息
        const response = await updatePurchaseInboundQrCode(
          printData.purchase_no,
          qrContent,
          this.$store.state.user.name || '系统'
        );

        if (response.code === 0) {
          // 更新当前行的二维码信息（用于页面显示）
          this.$set(row, 'qrCode', qrContent);
          console.log('二维码内容已成功保存到数据库');
          this.$message.success('二维码内容已保存');
        } else {
          console.error('保存二维码内容失败:', response.msg);
          this.$message.warning('二维码打印成功，但保存到数据库失败: ' + (response.msg || '未知错误'));
        }
      } catch (error) {
        console.error('保存二维码内容异常:', error);
        this.$message.warning('二维码打印成功，但保存到数据库时发生异常');
      }
    },

    /** 获取状态文本 */
    getStatusText(status) {
      return this.statusMap[status] || '未知状态';
    },

    /** 确认入库操作 */
    handleConfirmInbound(row) {
      this.$modal.confirm('确认将该采购单标记为已入库状态吗？').then(() => {
        // 确保所有必需字段都有值
        const updateData = {
          id: row.id,
          purchaseOrderNo: row.purchaseOrderNo,
          status: 1, // 更新为已入库状态
          qrCode: row.qrCode || '',
          zoneCode: row.zoneCode || '',
          operator: row.operator || '',
          remark: row.remark || '',
          materialType: row.materialType || '',
          materialName: row.materialName || ''
        };

        // 添加调试信息
        console.log('确认入库请求数据:', updateData);

        this.buttonLoading = true;
        addOrUpdatePurchaseInBound(updateData).then(response => {
          this.$modal.msgSuccess("确认入库成功");
          this.getPurchaseList();
        }).catch(error => {
          console.error('确认入库失败:', error);
          this.$modal.msgError("确认入库失败");
        }).finally(() => {
          this.buttonLoading = false;
        });
      }).catch(() => {
        // 用户取消操作
      });
    },

    /** 采购入库操作 */
    async handlePurchaseInbound(row) {
      try {
        // 参数验证
        if (!row.purchaseOrderNo) {
          this.$message.error('采购单号不能为空');
          return;
        }

        // 弹出区域选择对话框
        const zoneCode = await this.selectZoneCode();
        if (!zoneCode) {
          return; // 用户取消选择
        }

        // 确认操作
        const confirmResult = await this.$confirm(
          `确定要将采购单号 "${row.purchaseOrderNo}" 入库到区域 "${zoneCode}" 吗？\n此操作将同时完成区域绑定和库存入库。`,
          '采购入库确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        if (!confirmResult) {
          return;
        }

        this.$message.info('正在执行采购入库操作...');
        this.buttonLoading = true;

        // 调用集成接口
        const response = await purchaseInboundWithInventory(
          row.purchaseOrderNo,
          zoneCode,
          this.$store.state.user.name || '系统'
        );

        if (response.code === 0) {
          this.$message.success('采购入库操作成功');

          // 显示操作结果详情
          const result = response.data;
          const purchaseInbound = result.purchaseInbound;
          const inventoryResult = result.inventoryResult;

          this.$notify({
            title: '采购入库完成',
            message: `
              采购单号：${purchaseInbound.purchaseOrderNo}
              绑定区域：${purchaseInbound.zoneCode}
              库存明细ID：${inventoryResult.detailId}
              入库数量：${inventoryResult.quantity || '未知'}
            `,
            type: 'success',
            duration: 5000
          });

          // 刷新列表
          this.getPurchaseList();
        } else {
          this.$message.error(response.msg || '采购入库操作失败');
        }

      } catch (error) {
        console.error('采购入库操作失败:', error);

        let errorMsg = '采购入库操作失败';
        if (error.response && error.response.data && error.response.data.msg) {
          errorMsg = error.response.data.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }

        this.$message.error(errorMsg);
      } finally {
        this.buttonLoading = false;
      }
    },

    /** 选择区域编码 */
    async selectZoneCode() {
      return new Promise((resolve) => {
        this.$prompt('请输入区域编码', '选择区域', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.+$/,
          inputErrorMessage: '区域编码不能为空'
        }).then(({ value }) => {
          resolve(value);
        }).catch(() => {
          resolve(null);
        });
      });
    },

    // 🔧 获取板类型文本
    getBoardTypeText(boardType) {
      const boardTypeMap = {
        '上板': '上板',
        '下板': '下板',
        '单板': '单板'
      };
      return boardTypeMap[boardType] || boardType || '--';
    },

    // 🔧 获取板类型标签样式
    getBoardTypeTagType(boardType) {
      const typeMap = {
        '上板': 'primary',
        '下板': 'success',
        '单板': 'info'
      };
      return typeMap[boardType] || 'info';
    },

    /** 获取板型显示文本 */
    getBoardTypeDisplay(boardType) {
      if (!boardType) return '单板'

      // 标准化板型显示
      const boardTypeMap = {
        '上板': '上板',
        '下板': '下板',
        '单板': '单板',
        'upper': '上板',
        'lower': '下板',
        'single': '单板'
      }

      return boardTypeMap[boardType] || boardType || '单板'
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: var(--base-body-background);
  min-height: calc(100vh - 84px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color-1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }

  &:hover::before {
    left: 100%;
  }

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--current-color);
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;

    i {
      margin-right: 10px;
      animation: bounce 2s infinite;
    }
  }

  .page-description {
    color: var(--base-color-3);
    margin: 0;
    font-size: 16px;
    position: relative;
    z-index: 1;
  }
}

.nav-card {
  margin-bottom: 20px;
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);

  :deep(.el-card__body) {
    padding: 20px;
    background: var(--base-main-bg);
  }

  :deep(.el-tabs__header) {
    margin: 0;
    background: var(--base-color-9);
    border-radius: 8px 8px 0 0;
    position: relative;
  }

  :deep(.el-tabs__nav) {
    display: flex;
    width: 100%;
  }

  :deep(.el-tabs__item) {
    flex: 1;
    text-align: center;
    color: var(--base-color-1);
    font-weight: 500;
    transition: all 0.3s ease;
    background: transparent;
    border-radius: 6px;
    margin: 4px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--current-color);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &.is-active {
      color: var(--theme-color);
      font-weight: 600;
      background: var(--current-color);
      box-shadow: 0 2px 8px var(--tag-shadow-color-2);
      transform: translateY(-2px);

      &::before {
        opacity: 0.1;
      }
    }

    &:hover {
      color: var(--current-color);
      background: var(--base-main-bg);
      transform: translateY(-1px);

      &::before {
        opacity: 0.05;
      }
    }
  }

  :deep(.el-tabs__active-bar) {
    background: var(--current-color);
    height: 3px;
    border-radius: 2px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
    margin: 0;
    background: var(--base-main-bg);
  }
}

.content-wrapper {
  background: var(--base-main-bg);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--tag-shadow-color-1);
  border: 1px solid var(--border-color-1);
  min-height: 600px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--current-color);
    opacity: 0.8;
  }
}

.tab-content {
  padding: 20px;
  min-height: 600px;
  background: var(--base-main-bg);
  transition: all 0.3s ease;

  // 确保表格组件样式继承主题
  :deep(.el-form) {
    .el-form-item__label {
      color: var(--theme-color);
      font-weight: 500;
    }

    .el-input__inner {
      background: var(--base-main-bg);
      border-color: var(--border-color-1);
      color: var(--theme-color);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--current-color);
        box-shadow: 0 0 8px rgba(54, 113, 232, 0.2);
      }

      &:hover {
        border-color: var(--current-color);
      }
    }

    .el-select .el-input__inner {
      background: var(--base-main-bg);
    }

    .el-button {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &.el-button--primary {
        background: var(--current-color);
        border-color: var(--current-color);
        color: var(--theme-color);
        box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);

        &:hover {
          background: var(--color-2);
          border-color: var(--color-2);
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(54, 113, 232, 0.4);
        }
      }
    }
  }

  :deep(.el-table) {
    background: var(--base-main-bg);
    border: 1px solid var(--border-color-1);
    border-radius: 8px;
    overflow: hidden;

    th {
      background: var(--base-color-9);
      color: var(--theme-color);
      border-bottom: 1px solid var(--border-color-1);
      font-weight: 600;
    }

    td {
      border-bottom: 1px solid var(--border-color-1);
      color: var(--theme-color);
      background: var(--base-main-bg);
      transition: all 0.3s ease;
    }

    tr:hover td {
      background: var(--table-row-hover-bg);
      transform: scale(1.005);
    }

    .el-button--text {
      color: var(--current-color);

      &:hover {
        color: var(--color-2);
      }
    }
  }

  :deep(.el-tag) {
    border: none;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.el-tag--success {
      background: linear-gradient(135deg, #67c23a, #85ce61);
      color: white;
      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
    }

    &.el-tag--warning {
      background: linear-gradient(135deg, #e6a23c, #ebb563);
      color: white;
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
    }

    &.el-tag--danger {
      background: linear-gradient(135deg, #f56c6c, #f78989);
      color: white;
      box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
    }

    &.el-tag--info {
      background: linear-gradient(135deg, #909399, #a6a9ad);
      color: white;
      box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
    }

    &.el-tag--primary {
      background: var(--current-color);
      color: white;
      box-shadow: 0 2px 8px rgba(54, 113, 232, 0.3);
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    text-align: center;

    .el-pager li {
      background: var(--base-main-bg);
      color: var(--theme-color);
      border: 1px solid var(--border-color-1);
      border-radius: 6px;
      margin: 0 2px;
      transition: all 0.3s ease;

      &.active {
        background: var(--current-color);
        border-color: var(--current-color);
        color: white;
        box-shadow:
          0 0 16px rgba(30, 58, 138, 0.8),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      &:hover {
        color: var(--current-color);
        border-color: var(--current-color);
        transform: translateY(-1px);
      }
    }

    .btn-prev, .btn-next {
      background: var(--base-main-bg);
      color: var(--theme-color);
      border: 1px solid var(--border-color-1);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        color: var(--current-color);
        border-color: var(--current-color);
        transform: translateY(-1px);
      }
    }

    .el-pagination__editor .el-input__inner {
      background: var(--base-main-bg);
      border-color: var(--border-color-1);
      color: var(--theme-color);
      border-radius: 6px;
    }
  }
}

/* 星空主题特殊适配 */
.theme-starry-sky .app-container {
  background: linear-gradient(135deg, #0b0d1a 0%, #1a1f3c 25%, #1e3a8a 50%, #0b0d1a 75%, #1a1f3c 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.9), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(30, 58, 138, 0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.7), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(30, 58, 138, 0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.8), transparent),
      radial-gradient(1px 1px at 200px 90px, rgba(30, 58, 138, 0.5), transparent);
    background-repeat: repeat;
    background-size: 250px 150px;
    pointer-events: none;
    animation: starAnimation 25s linear infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(ellipse at top, rgba(30, 58, 138, 0.1) 0%, transparent 70%),
      radial-gradient(ellipse at bottom, rgba(26, 31, 60, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.theme-starry-sky {
  .page-header {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 32px rgba(30, 58, 138, 0.2);

    &::before {
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.3), transparent);
    }

    .page-title {
      color: var(--current-color);
      text-shadow:
        0 0 10px rgba(30, 58, 138, 0.8),
        0 0 20px rgba(30, 58, 138, 0.5),
        0 0 30px rgba(30, 58, 138, 0.3);

      i {
        animation: bounce 2s infinite, glow 3s ease-in-out infinite alternate;
      }
    }

    .page-description {
      color: var(--base-color-2);
      text-shadow: 0 0 10px rgba(30, 58, 138, 0.3);
    }
  }

  .nav-card {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 16px rgba(30, 58, 138, 0.2);

    :deep(.el-card__body) {
      background: transparent;
    }

    :deep(.el-tabs__header) {
      background: rgba(var(--base-color-9), 0.5);
    }

    :deep(.el-tabs__item) {
      color: var(--base-color-2);

      &.is-active {
        color: var(--theme-color);
        background: var(--current-color);
        text-shadow:
          0 0 8px rgba(30, 58, 138, 0.8),
          0 0 16px rgba(30, 58, 138, 0.5);
        box-shadow:
          0 4px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      &:hover {
        color: var(--current-color);
        background: rgba(var(--current-color), 0.1);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.6);
      }
    }

    :deep(.el-tabs__active-bar) {
      background: var(--current-color);
      box-shadow: 0 0 16px rgba(30, 58, 138, 0.8);
    }

    :deep(.el-tabs__content) {
      background: transparent;
    }
  }

  .content-wrapper {
    background: rgba(var(--base-item-bg), 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color-1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 16px rgba(30, 58, 138, 0.2);

    &::before {
      background: var(--current-color);
      box-shadow: 0 0 16px rgba(30, 58, 138, 0.8);
    }
  }

  .tab-content {
    background: transparent;

    :deep(.el-form) {
      .el-form-item__label {
        color: var(--theme-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.3);
      }

      .el-input__inner {
        background: var(--input-bg);
        border-color: var(--input-border);
        color: var(--input-color);
        backdrop-filter: blur(10px);

        &:focus {
          border-color: var(--current-color);
          box-shadow:
            0 0 16px rgba(30, 58, 138, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        &::placeholder {
          color: var(--base-color-3);
        }
      }

      .el-button {
        &.el-button--primary {
          background: var(--current-color);
          border: none;
          box-shadow:
            0 4px 16px rgba(30, 58, 138, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);

          &:hover {
            background: var(--color-2);
            box-shadow:
              0 6px 20px rgba(30, 58, 138, 0.6),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
          }
        }
      }
    }

    :deep(.el-table) {
      background: var(--table-row-bg);
      border-color: var(--table-border-color);
      backdrop-filter: blur(10px);

      th {
        background: var(--table-header-bg);
        color: var(--table-header-color);
        border-bottom-color: var(--table-border-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.3);
      }

      td {
        border-bottom-color: var(--table-border-color);
        color: var(--theme-color);
        background: transparent;
      }

      tr:hover td {
        background: var(--table-row-hover-bg);
        box-shadow: 0 0 16px rgba(30, 58, 138, 0.2);
      }

      .el-button--text {
        color: var(--current-color);
        text-shadow: 0 0 8px rgba(30, 58, 138, 0.5);

        &:hover {
          color: var(--color-2);
          text-shadow: 0 0 12px rgba(30, 58, 138, 0.8);
        }
      }
    }

    :deep(.el-pagination) {
      .el-pager li {
        background: var(--input-bg);
        color: var(--input-color);
        border-color: var(--input-border);
        backdrop-filter: blur(10px);

        &.active {
          background: var(--current-color);
          border-color: var(--current-color);
          color: white;
          box-shadow:
            0 0 16px rgba(30, 58, 138, 0.8),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        &:hover {
          color: var(--current-color);
          border-color: var(--current-color);
          box-shadow: 0 0 12px rgba(30, 58, 138, 0.5);
        }
      }

      .btn-prev, .btn-next {
        background: var(--input-bg);
        color: var(--input-color);
        border-color: var(--input-border);
        backdrop-filter: blur(10px);

        &:hover {
          color: var(--current-color);
          border-color: var(--current-color);
          box-shadow: 0 0 12px rgba(30, 58, 138, 0.5);
        }
      }
    }
  }
}

/* 动画效果 */
@keyframes starAnimation {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-6px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes glow {
  0% {
    text-shadow:
      0 0 5px rgba(30, 58, 138, 0.5),
      0 0 10px rgba(30, 58, 138, 0.3);
  }
  100% {
    text-shadow:
      0 0 10px rgba(30, 58, 138, 0.8),
      0 0 20px rgba(30, 58, 138, 0.5),
      0 0 30px rgba(30, 58, 138, 0.3);
  }
}

/* Element UI 表格样式适配 */
:deep(.el-table) {
  background: var(--base-main-bg);
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  font-size: 14px;
}

:deep(.el-table__header-wrapper) {
  background: var(--current-color);
}

:deep(.el-table__header-wrapper th) {
  background: var(--current-color) !important;
  color: white !important;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 0;
  border: none;
}

:deep(.el-table__body-wrapper) {
  background: var(--base-main-bg);
}

:deep(.el-table__row) {
  background: var(--base-main-bg);
  color: var(--theme-color);
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background: var(--table-row-hover-bg) !important;
}

:deep(.el-table__row td) {
  border-color: var(--border-color-1);
  padding: 12px 0;
  color: var(--base-color-1);
}

/* 对话框样式适配 */
:deep(.el-dialog) {
  background: var(--base-main-bg);
  border: 1px solid var(--border-color-1);
  border-radius: 12px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  background: var(--current-color);
  color: white;
  padding: 20px;
  margin: 0;
  border-radius: 12px 12px 0 0;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
  padding: 30px;
}

:deep(.el-dialog__footer) {
  background: var(--base-main-bg);
  border-top: 1px solid var(--border-color-1);
  padding: 20px 30px;
  border-radius: 0 0 12px 12px;
}

/* 对话框表单样式 */
.el-dialog :deep(.el-form-item__label) {
  color: var(--base-color-1);
  font-weight: 500;
}

.el-dialog :deep(.el-input__inner) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
}

.el-dialog :deep(.el-input__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

.el-dialog :deep(.el-select .el-input__inner) {
  background: var(--base-main-bg);
  color: var(--base-color-1);
}

.el-dialog :deep(.el-textarea__inner) {
  background: var(--base-main-bg);
  border-color: var(--border-color-1);
  color: var(--base-color-1);
}

.el-dialog :deep(.el-textarea__inner:focus) {
  border-color: var(--current-color);
  box-shadow: 0 0 0 2px rgba(54, 113, 232, 0.2);
}

/* 打印按钮样式适配 */
.el-button.el-button--text {
  color: var(--base-color-1);
  transition: all 0.3s ease;
}

.el-button.el-button--text:hover {
  color: var(--current-color);
  background: rgba(64, 158, 255, 0.1);
}

.el-button.el-button--text[style*="color: #409EFF"] {
  color: var(--current-color) !important;
}

.el-button.el-button--text[style*="color: #409EFF"]:hover {
  color: var(--current-color) !important;
  background: rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

/* 按钮样式 */
:deep(.el-button--primary) {
  background: var(--current-color);
  border-color: var(--current-color);
  color: white;
}

:deep(.el-button--primary:hover) {
  background: var(--color-2, #70afce);
  border-color: var(--color-2, #70afce);
}

:deep(.el-button--success) {
  background: #67c23a;
  border-color: #67c23a;
  color: white;
}

:deep(.el-button--success:hover) {
  background: #85ce61;
  border-color: #85ce61;
}

:deep(.el-button--warning) {
  background: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

:deep(.el-button--warning:hover) {
  background: #ebb563;
  border-color: #ebb563;
}

:deep(.el-button--danger) {
  background: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

:deep(.el-button--danger:hover) {
  background: #f78989;
  border-color: #f78989;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
}

:deep(.el-tag--success) {
  background: rgba(103, 194, 58, 0.1);
  border-color: #67c23a;
  color: #67c23a;
}

:deep(.el-tag--warning) {
  background: rgba(230, 162, 60, 0.1);
  border-color: #e6a23c;
  color: #e6a23c;
}

:deep(.el-tag--danger) {
  background: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
  color: #f56c6c;
}

:deep(.el-tag--info) {
  background: rgba(144, 147, 153, 0.1);
  border-color: #909399;
  color: var(--base-color-3);
}

/* 分页器样式 */
:deep(.pagination-container) {
  background: var(--base-main-bg);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border-color-1);
  margin-top: 20px;
}

/* 深色主题适配 */
.theme-dark :deep(.el-table__row),
.theme-dark :deep(.el-table__body tr) {
  background-color: var(--base-main-bg) !important;
}

.theme-dark :deep(.el-table__row--striped) {
  background-color: var(--base-item-bg) !important;
}

.theme-dark :deep(.el-table__row:hover),
.theme-dark :deep(.el-table__body tr:hover) {
  background-color: var(--table-row-hover-bg) !important;
}

.theme-dark :deep(.el-tag--success) {
  background: rgba(165, 222, 241, 0.2);
  border-color: var(--color-3);
  color: var(--color-3);
}

.theme-dark :deep(.el-tag--warning) {
  background: rgba(223, 221, 221, 0.2);
  border-color: var(--base-color-5);
  color: var(--base-color-5);
}

.theme-dark :deep(.el-tag--danger) {
  background: rgba(223, 221, 221, 0.2);
  border-color: var(--base-color-5);
  color: var(--base-color-5);
}

.theme-dark :deep(.el-tag--info) {
  background: rgba(133, 133, 133, 0.2);
  border-color: var(--base-color-6);
  color: var(--base-color-6);
}

/* 星空主题适配 */
.theme-starry-sky :deep(.el-table__row),
.theme-starry-sky :deep(.el-table__body tr) {
  background-color: var(--base-main-bg) !important;
}

.theme-starry-sky :deep(.el-table__row--striped) {
  background-color: var(--base-item-bg) !important;
}

.theme-starry-sky :deep(.el-table__row:hover),
.theme-starry-sky :deep(.el-table__body tr:hover) {
  background-color: var(--table-row-hover-bg) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    padding: 15px;
    margin-bottom: 15px;

    .page-title {
      font-size: 22px;

      i {
        margin-right: 8px;
      }
    }

    .page-description {
      font-size: 14px;
    }
  }

  .nav-card {
    margin-bottom: 15px;

    :deep(.el-card__body) {
      padding: 15px;
    }

    :deep(.el-tabs__item) {
      font-size: 12px;
      padding: 0 8px;
    }
  }

  .tab-content {
    padding: 15px;
  }

  :deep(.el-button) {
    padding: 6px 12px;
    font-size: 12px;
    margin: 2px;
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  :deep(.el-dialog__body) {
    padding: 20px 15px;
  }

  :deep(.el-table .el-button--mini) {
    padding: 4px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 5px;
  }

  .page-header {
    padding: 10px;

    .page-title {
      font-size: 18px;
      flex-direction: column;

      i {
        margin-right: 0;
        margin-bottom: 5px;
      }
    }
  }

  .nav-card {
    :deep(.el-tabs__item) {
      font-size: 10px;
      padding: 0 4px;
    }
  }

  .tab-content {
    padding: 10px;
  }
}

/* 采购入库页面操作按钮美化样式 */
.purchase-action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.purchase-action-btn {
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
  min-width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.purchase-action-btn i {
  margin-right: 3px;
  font-size: 11px;
}

.edit-btn {
  color: #67C23A !important;
  background: rgba(103, 194, 58, 0.1) !important;
  border-color: rgba(103, 194, 58, 0.2) !important;
}

.edit-btn:hover {
  background: rgba(103, 194, 58, 0.2) !important;
  border-color: #67C23A !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.delete-btn {
  color: #F56C6C !important;
  background: rgba(245, 108, 108, 0.1) !important;
  border-color: rgba(245, 108, 108, 0.2) !important;
}

.delete-btn:hover {
  background: rgba(245, 108, 108, 0.2) !important;
  border-color: #F56C6C !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);
}

.print-btn {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border-color: rgba(64, 158, 255, 0.2) !important;
}

.print-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409EFF !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.inbound-btn {
  color: #E6A23C !important;
  background: rgba(230, 162, 60, 0.1) !important;
  border-color: rgba(230, 162, 60, 0.2) !important;
}

.inbound-btn:hover {
  background: rgba(230, 162, 60, 0.2) !important;
  border-color: #E6A23C !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(230, 162, 60, 0.3);
}

/* 下拉菜单样式 */
.purchase-dropdown {
  margin-left: 2px;
  flex-shrink: 0;
}

.more-btn {
  color: #606266 !important;
  background: rgba(96, 98, 102, 0.1) !important;
  border-color: rgba(96, 98, 102, 0.2) !important;
}

.more-btn:hover {
  background: rgba(96, 98, 102, 0.2) !important;
  border-color: #606266 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(96, 98, 102, 0.3);
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 12px;
  transition: all 0.3s ease;
}

:deep(.el-dropdown-menu__item i) {
  margin-right: 6px;
  font-size: 12px;
}

:deep(.el-dropdown-menu__item.danger-item) {
  color: #F56C6C;
}

:deep(.el-dropdown-menu__item.danger-item:hover) {
  background: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

/* 二维码文本样式 */
.qr-code-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #606266;
  background: rgba(64, 158, 255, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

/* 采购入库页面响应式设计 */
@media (max-width: 1200px) {
  .purchase-action-buttons {
    gap: 2px;
  }

  .purchase-action-btn {
    min-width: 50px;
    padding: 3px 6px !important;
    font-size: 9px !important;
  }
}

@media (max-width: 768px) {
  .purchase-action-buttons {
    flex-direction: column;
    gap: 3px;
    flex-wrap: wrap;
  }

  .purchase-action-btn {
    width: 100%;
    min-width: auto;
    font-size: 10px !important;
    padding: 4px 8px !important;
  }

  .purchase-dropdown {
    margin-left: 0;
    width: 100%;
  }
}
</style>

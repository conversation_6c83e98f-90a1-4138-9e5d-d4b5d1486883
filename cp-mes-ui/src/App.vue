<template>
  <div id="app">
    <router-view />
    <theme-picker />
    <StarrySkyBg v-if="isStarrySkyTheme" />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import StarrySkyBg from "@/components/StarrySkyBg.vue";

export default {
  name: "App",
  components: { ThemePicker, StarrySkyBg },
  data() {
    return {
      theme: document.body.className || localStorage.getItem('theme') || 'theme-light',
    }
  },
  computed: {
    isStarrySkyTheme() {
      return this.theme === 'theme-starry-sky';
    }
  },
  // metaInfo() {
  //   return {
  //     title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
  //     titleTemplate: title => {
  //       return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
  //     }
  //   }
  // }
  mounted() {
    this.updateTheme();
    window.addEventListener('storage', this.handleThemeChange);
    window.addEventListener('DOMContentLoaded', this.updateTheme);
    window.addEventListener('click', this.updateTheme, true); // 兼容ThemeSwitch
  },
  beforeDestroy() {
    window.removeEventListener('storage', this.handleThemeChange);
    window.removeEventListener('DOMContentLoaded', this.updateTheme);
    window.removeEventListener('click', this.updateTheme, true);
  },
  methods: {
    handleThemeChange(e) {
      if (e.key === 'theme') {
        this.theme = e.newValue;
      }
    },
    updateTheme() {
      // 优先取body的className（ThemeSwitch会直接改body.className）
      this.theme = document.body.className || localStorage.getItem('theme') || 'theme-light';
    }
  },
  watch: {
    // 兼容ThemeSwitch组件切换
    theme(val) {
      this.$forceUpdate();
    }
  }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
<style>
/* 去除百度地图标志 */
.BMap_cpyCtrl {
  display: none !important;
}

.anchorBL {
  display: none !important;
}
/* 树结构下拉框 */
.vue-treeselect--has-value .vue-treeselect__input {
  vertical-align: middle !important;
}
</style>

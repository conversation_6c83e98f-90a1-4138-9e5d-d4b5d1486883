<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}" class="fixed-header">
        <navbar ref="navbar"/>
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel>
        <settings />
      </right-panel>
    </div>
    
    <!-- 强制通知弹窗 -->
    <notice-popup :notices="unreadNotices" @all-read="handleAllNoticesRead" />
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import NoticePopup from '@/components/NoticePopup'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import variables from '@/assets/styles/variables.scss'
import { getUnreadForceNotices } from '@/api/system/notice'

export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    NoticePopup,
    RightPanel,
    Settings,
    Sidebar,
    TagsView
  },
  data() {
    return {
      unreadNotices: []
    }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    }
  },
  created() {
    this.$nextTick(() => {
      this.$refs.navbar.getTenantList();
    });
    
    // 监听全局事件，检查未读通知
    this.$bus.$on('check-unread-notices', this.checkUnreadNotices);
    
    // 组件创建时立即检查未读通知
    this.$nextTick(() => {
      setTimeout(() => {
        this.checkUnreadNotices();
      }, 1000);
    });
  },
  
  beforeDestroy() {
    // 移除事件监听
    this.$bus.$off('check-unread-notices', this.checkUnreadNotices);
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    
    async checkUnreadNotices() {
      console.log('开始检查未读强制通知...');
      try {
        const response = await getUnreadForceNotices();
        console.log('未读通知API响应:', response);
        if (response.code === 200 && response.data && response.data.length > 0) {
          console.log('发现未读强制通知:', response.data.length, '条');
          this.unreadNotices = response.data;
        } else {
          console.log('没有未读的强制通知');
          this.unreadNotices = [];
        }
      } catch (error) {
        console.error('获取未读通知失败:', error);
      }
    },
    
    handleAllNoticesRead() {
      this.unreadNotices = [];
      // 可以在这里添加其他处理逻辑，比如刷新页面数据等
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mixin.scss";
  @import "~@/assets/styles/variables.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }

  // .fixed-header {
  //   position: fixed;
  //   top: 0;
  //   right: 0;
  //   z-index: 9;
  //   width: calc(100% - #{$base-sidebar-width});
  // }
</style>

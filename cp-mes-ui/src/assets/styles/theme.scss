// 浅色主题
.theme-light {
  --primary-color: #fff;
  --current-color: #3671e8;
  --current-color-rgb: 54, 113, 232;
  --theme-color: #333333;
  --loading-bg: #3671e8;
  // 侧边栏sidebar
  --base-menu-background: #10469c;
  --base-menu-background-active: #0e3f8c;
  --base-menu-color: #fff;
  --base-menu-color-active: #fff;
  --base-logo-title-color: #fff;
  --base-sub-menu-background: #0354a4;
  --base-sub-menu-hover: #1670b6;
  // 顶部navbar
  --base-nav-background: #fff;
  --icon-color: #5a5e66;
  --nav-box-shadow-color: rgba(0,21,41,.08);
  // 顶部tagView
  --base-tag-background: #fff;
  --tag-border-color: #d8dce5;
  --tag-shadow-color-1:rgba(0, 0, 0, 0.12);
  --tag-shadow-color-2:rgba(0, 0, 0, 0.04);
  --tag-item-border: #d8dce5;
  --tag-item-color: #495060;
  --tag-item-background: #fff;
  --tag-item-border-active: #3671e8;
  --tag-item-color-active: #fff;
  --tag-item-background-active: #3671e8;
  --tag-item-icon: #fff;
  --context-menu-background: #fff;
  --context-menu-color: #333;
  --context-menu-background-active: #eee;
  --context-menu-color-active: #333;
  --context-menu-shadow: rgba(0, 0, 0, .3);
  // body
  --base-body-background: #f6f6f6;
  --base-main-bg: #fff;
  --base-item-bg: #fff;
  --base-color-1: #333333;
  --base-color-2: #595959;
  --base-color-3: #7f7f7f;
  --base-color-4: #59597e;
  --base-color-5: #888888;
  --base-color-6: #cccccc;
  --base-color-7: #3671e8;
  --base-color-8: #f6f8fc;
  --base-color-9: #f5f7fa;
  --border-color-1: #d9d9d9;
  --border-color-2: #030a1c;
  --border-color-3: #ebe6f5;
  // 操作按钮颜色变量
  --color-2: #5a9fd4;
  --color-3: #7f7f7f;
  // el-tree
  --tree-border-color: #e5e6e7;
  --tree-border-bg: #fff; // class="tree-border"
  --tree-bg: #fff;
  // link、a
  --link-default-color: #337ab7;
  --link-hover-color: rgb(32, 160, 255);
  --table-row-hover-bg: #e8eaee;

  .el-table__fixed::before, .el-table__fixed-right::before {
    background-color: rgba($color: #fff, $alpha: 0);
  }

  // 浅色主题下的success按钮样式
  .el-button--success {
    background-color: #67c23a !important;
    border-color: #67c23a !important;
    color: #fff !important;
    &:hover {
      background-color: #85ce61 !important;
      border-color: #85ce61 !important;
      color: #fff !important;
    }
    &.is-disabled {
      background-color: #b3d999 !important;
      color: #fff !important;
      border-color: #b3d999 !important;
    }
  }
}

// 深色主题
.theme-dark {
  --primary-color: #2a3950;
  --current-color: #3a7b99;
  --current-color-rgb: 58, 123, 153;
  --theme-color: #fff;
  --loading-bg: #46576e;
  --font-disable-color: #c0bebe;
  --color-1: #3a7b99;
  --color-2: #70afce;
  --color-3: #a5def1;
  --color-4: #4189aa;
  --color-5: #3b697e;
  --input-border-color: #7c7c7c;
  --border-disbale-color: #686868;
  --border-hover-color: #adb1b8;
  --time-picker-disable-bg: #414b5a;
  // 侧边栏sidebar
  --base-menu-background: #2a3950;
  --base-menu-background-active: #222e41;
  --base-menu-color: #fff;
  --base-menu-color-active: #fff;
  --base-logo-title-color: #fff;
  --base-sub-menu-background: #364966;
  --base-sub-menu-hover: #397a97;
  // 顶部navbar
  --base-nav-background: #1c2e47;
  --icon-color: #ffffff;
  --nav-box-shadow-color: rgba(255,255,255,0.2);
  // 顶部tagView
  --base-tag-background: #1c2e47;
  --tag-border-color: #213653;
  --tag-shadow-color-1:rgba(255, 255, 255, 0.3);
  --tag-shadow-color-2:rgba(255, 255, 255, 0.1);
  --tag-item-border: #88a7b1;
  --tag-item-color: #88a7b1;
  --tag-item-background: transparent;
  --tag-item-border-active: #3a7b99;
  --tag-item-color-active: #c0edfc;
  --tag-item-background-active: #3a7b99;
  --tag-item-icon: #34c3f3;
  --context-menu-background: #2a3950;
  --context-menu-color: #fff;
  --context-menu-background-active: #222e41;
  --context-menu-color-active: #a5def1;
  --context-menu-shadow: rgba(255, 255, 255, .3);
  // body
  --base-body-background: #46576e;
  --base-main-bg: #46576e;
  --base-item-bg: #2c3d55;
  --base-color-1: #fff;
  --base-color-2: #fff;
  --base-color-3: #c4c2c2;
  --base-color-4: #fff;
  --base-color-5: #dfdddd;
  --base-color-6: #858585;
  --base-color-7: #a5def1;
  --base-color-8: #4d5d74;
  --base-color-9: #222e41;
  --border-color-1: #8b8b8b;
  --border-color-2: #fff;
  --border-color-3: #28374d;
  // 操作按钮颜色变量
  --color-2: #70afce;
  --color-3: #a5def1;
  // el-dialog
  --dialog-background: #415063;
  // el-table
  --table-fixed-shadow: rgba(255, 255, 255, 0.12);
  --table-row-bg: #323e52;
  --table-row-hover-bg: #2c3544;
  --table-border-color: #5f6e8a;
  // el-loading
  --loading-mask-bg: rgba(50, 62, 82, .9);
  // el-image
  --image-view-shadow: #666464;
  // el-switch
  --switch-default-bg: #4e5970;
  --switch-default-border-color: #4a5e85;
  --switch-default-after-bg: #23355a;
  // el-tree
  --tree-border-color: #7c7c7c;
  --tree-border-bg: #2a3950; // class="tree-border"
  --tree-bg: transparent;
  // link、a
  --link-default-color: #70afce;
  --link-hover-color: #a5def1;

  // el-form
  .el-form {
    .el-form-item__label {
      color: var(--theme-color);
    }
  }
  .el-input:not(.unchanged), .el-textarea:not(.unchanged) {
    .el-input__inner, .el-textarea__inner {
      background-color: var(--primary-color);
      color: var(--theme-color);
      border-color: var(--input-border-color);
      &:hover {
        border-color: var(--border-hover-color);
      }
      &:focus {
        border-color: var(--current-color);
      }
    }
  }

  .el-input-number {
    .el-input-number__decrease, .el-input-number__increase {
      background: var(--current-color);
      color: var(--theme-color);
      border-color: var(--current-color);
    }
    .el-input-number__increase {
      border-bottom: 1px solid var(--color-2);
    }
  }
  .el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled)
  ,.el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
    border-color: var(--current-color);
  }
  .el-select .el-input.is-focus .el-input__inner {
    border-color: var(--current-color);
  }
  .el-input.is-disabled .el-input__inner,
  .el-textarea.is-disabled .el-textarea__inner {
    background-color: var(--base-menu-background-active);
    color: var(--font-disable-color);
    border-color: var(--border-disbale-color);
    &:hover {
      border-color: var(--border-hover-color);
    }
  }

  // el-radio
  .el-radio {
    color: var(--theme-color);
    .el-radio__input:hover {
      border-color: var(--current-color);
    }
    .el-radio__input.is-checked .el-radio__inner {
      border-color: var(--current-color);
      background: var(--current-color);
    }
    .el-radio__input.is-checked+ .el-radio__label {
      color: var(--color-2);
    }
  }
  .el-radio-group {
    .el-radio-button__inner {
      border-color: var(--current-color);
      color: var(--theme-color);
      background-color: transparent;
      &:hover {
        color: var(--color-2);
      }
    }
    .el-radio-button__orig-radio:checked+ .el-radio-button__inner {
      background-color: var(--current-color);
      border-color: var(--current-color);
    }
  }
  // el-checkbox
  .el-checkbox:not(.unchanged) {
    color: var(--theme-color);
    .el-checkbox__input {
      &.is-focus .el-checkbox__inner {
        border-color: var(--current-color);
      }
      &.is-checked .el-checkbox__inner {
        background-color: var(--current-color);
        border-color: var(--current-color);
      }
      &.is-indeterminate .el-checkbox__inner {
        background-color: var(--current-color);
        border-color: var(--current-color);
      }
      &.is-checked+ .el-checkbox__label {
        color: var(--color-2);
      }
    }
    .el-checkbox__inner {
      &:hover {
        border-color: var(--color-3);
      }
    }
  }
  // el-switch
  .el-switch {
    .el-switch__core {
      background: var(--switch-default-bg);
      border-color: var(--switch-default-border-color);
      &::after {
        background-color: var(--switch-default-after-bg);
      }
    }
    &.is-checked .el-switch__core {
      background: var(--current-color);
      border-color: var(--current-color);
      &::after {
        background-color: var(--color-3);
      }
    }
  }

  // el-dropdown
  .el-dropdown-menu {
    a {
      display: block
    }
    background-color: var(--primary-color);
    border-color: var(--input-border-color);
  }
  .el-dropdown-menu__item {
    color: var(--theme-color);
    &:hover {
      background-color: var(--base-menu-background-active);
      color: var(--color-3);
    }
  }
  .el-dropdown-menu__item--divided {
    border-top-color: var(--color-1);
  }
  .el-dropdown-menu__item--divided:before {
    background-color: var(--primary-color);
  }
  .el-popper {
    background: var(--primary-color);
    color: var(--theme-color);
    border-color: var(--input-border-color);

    &[x-placement^=bottom] .popper__arrow::after,
    &[x-placement^=top] .popper__arrow::after {
      border-bottom-color: var(--primary-color);
      border-top-color: var(--primary-color);
    }
    &[x-placement^=bottom] .popper__arrow,
    &[x-placement^=top] .popper__arrow {
      border-bottom-color: var(--input-border-color);
      border-top-color: var(--input-border-color);
    }
  }
  .el-popover__title {
    color: var(--base-color-1);
  }
  .el-select-dropdown {
    .el-select-dropdown__item:not(.is-disabled) {
      color: var(--theme-color);
      background-color: var(--primary-color);
    }
    .el-select-dropdown__item:hover {
      color: var(--color-2);
      background-color: var(--base-menu-background-active);
    }
    .el-select-dropdown__item.selected {
      color: var(--color-2);
      background-color: var(--primary-color);
      &:hover {
        background-color: var(--base-menu-background-active);
      }
    }
    &.is-multiple .el-select-dropdown__item.selected.hover {
      background-color: var(--base-menu-background-active);
    }
  }
  // el-select el-tag
  .el-select {
    .el-tag.el-tag--info {
      background-color: var(--current-color);
      color: var(--theme-color);
      border-color: var(--current-color);
    }
  }
  // el-scrollbar
  .el-scrollbar {
    .time-select-item {
      &:hover {
        background-color: var(--base-menu-background-active);
      }
      &.selected:not(.disabled) {
        color: var(--color-2);
      }
    }
  }
  // 时间选择器
  .el-date-editor {
    .el-range-input {
      background: var(--primary-color);
      color: var(--theme-color);
    }
    .el-range-separator {
      color: var(--theme-color);
    }
  }
  .el-picker-panel {
    .el-picker-panel__body-wrapper .el-picker-panel__body {
      .el-date-picker__header, .el-date-range-picker__header {
        .el-picker-panel__icon-btn {
          color: var(--theme-color);
          &:hover {
            color: var(--color-2);
          }
        }
        .el-date-picker__header-label {
          color: var(--theme-color);
          &:hover {
            color: var(--color-2);
          }
        }
      }
      .el-picker-panel__content {
        .el-date-table {
          th {
            color: var(--theme-color);
          }
          td.available:hover {
            color: var(--color-2);
          }
          td.today span {
            color: var(--color-2);
          }
          td.current:not(.disabled) span {
            background-color: var(--current-color);
            color: var(--theme-color);
          }
          td.start-date span, td.end-date span {
            background-color: var(--current-color);
            color: var(--theme-color);
          }
          td.in-range div {
            background-color: var(--base-menu-background-active);
          }
          td.disabled div {
            background-color: var(--time-picker-disable-bg);
            // color: #c0c4cc;
          }
          &.is-week-mode .el-date-table__row {
            &:hover div {
              background-color: var(--base-menu-background-active);
            }
            &.current div {
              background-color: var(--base-menu-background-active);
            }
          }
        }
        .el-year-table, .el-month-table {
          td .cell {
            color: var(--theme-color);
          }
          td.current:not(.disabled) .cell, td.today .cell, td:not(.disabled):hover .cell {
            color: var(--color-2);
          }
          td.disabled .cell {
            background-color: var(--time-picker-disable-bg);
          }
        }
      }
      .el-date-picker__time-header {
        .el-time-spinner__item:not(.disabled) {
          color: var(--theme-color);
          &.active {
            color: var(--color-2);
          }
          &:not(.active):hover {
            background: var(--base-menu-background-active);
          }
        }
        .el-time-panel__btn {
          color: var(--theme-color);
          &.confirm {
            color: var(--color-2);
          }
        }
      }
    }
    .el-picker-panel__footer {
      background-color: var(--primary-color);
      .el-button--text {
        color: var(--color-2);
      }
    }
  }
  .el-range-editor {
    border-color: var(--input-border-color);
    background-color: var(--primary-color);
    &:hover {
      border-color: var(--border-hover-color);
    }
    &.is-active {
      border-color: var(--current-color);
    }
  }

  // el-button
  .el-button {
    &.is-plain:hover {
      border-color: var(--color-3);
      color: var(--color-3);
    }
  }
  .el-button--default {
    background-color: var(--tag-item-background);
    border-color: var(--color-3);
    color: var(--color-3);
    &:hover {
      background-color: var(--color-5);
      border-color: var(--color-3);
    }
    &.is-disabled {
      background-color: var(--tag-item-background);
      color: #6a8f9b;
      border-color: #6a8f9b;
    }
  }
  .el-button--primary {
    background-color: var(--color-1);
    border-color: var(--color-1);
    &:hover {
      background-color: var(--color-4);
      border-color: var(--color-1);
    }
    &.is-disabled {
      background-color: #2d627a;
      color: #d3deeb;
    }
  }
  .el-button--text {
    color: var(--color-2);
    &:hover {
      color: var(--color-3);
    }
  }
  .el-button--success {
    background-color: var(--color-2) !important;
    border-color: var(--color-2) !important;
    color: var(--primary-color) !important;
    &:hover {
      background-color: var(--color-3) !important;
      border-color: var(--color-3) !important;
      color: var(--primary-color) !important;
    }
    &.is-disabled {
      background-color: #2d627a !important;
      color: #d3deeb !important;
      border-color: #2d627a !important;
    }
  }

  // el-dialog
  .el-dialog {
    background: var(--dialog-background);
    color: var(--theme-color);
    .el-dialog__title, .el-dialog__headerbtn .el-dialog__close, .el-dialog__body {
      color: var(--theme-color);
    }
  }
  // el-drawer
  .el-drawer {
    color: var(--theme-color);
    background-color: var(--dialog-background);
    .el-drawer__header {
      color: var(--theme-color);
    }
  }
  // el-message
  .el-message-box {
    background-color: var(--dialog-background);
    border-color: var(--dialog-background);
    .el-message-box__title,
    .el-message-box__headerbtn .el-message-box__close,
    .el-message-box__content {
      color: var(--theme-color);
    }
  }

  // el-tabs
  .el-tabs {
    .el-tabs__item {
      color: var(--theme-color);
      &.is-active {
        color: var(--color-2);
      }
    }
    .el-tabs__active-bar {
      background-color: var(--color-2);
    }
  }

  // vue-treeselect
  .vue-treeselect {
    .vue-treeselect__control {
      border-color: var(--input-border-color);
      background: var(--primary-color);
      .vue-treeselect__single-value, .vue-treeselect__input {
        color: var(--theme-color);
      }
      .vue-treeselect__control-arrow-container {
        &:hover .vue-treeselect__control-arrow {
          fill: var(--current-color);
        }
      }
    }
    .vue-treeselect__menu-container {
      .vue-treeselect__menu {
        background: var(--primary-color);
        border-color: var(--input-border-color);
        color: var(--theme-color);
        .vue-treeselect__option--highlight {
          background-color: var(--base-menu-background-active);
        }
        .vue-treeselect__option--selected {
          background: var(--base-menu-background-active);
          color: var(--color-2);
        }
        .vue-treeselect__option-arrow-container {
          &:hover .vue-treeselect__option-arrow {
            fill: var(--current-color);
          }
        }
      }
    }
  }
  .vue-treeselect--focused .vue-treeselect__control {
    border-color: var(--current-color) !important;
  }

  // el-pagination
  .el-pagination {
    .el-pagination__total {
      color: var(--theme-color);
    }
    .el-pagination__sizes .el-input .el-input__inner:hover {
      border-color: var(--current-color);
    }
    .el-pagination__jump {
      color: var(--theme-color);
    }
    &.is-background .btn-prev, .btn-prev, & .btn-next, .btn-next {
      background-color: var(--primary-color);
      color: var(--theme-color);
      &:disabled {
        color: var(--font-disable-color);
      }
    }
    &.is-background .el-pager li, & .el-pager li {
      color: var(--theme-color);
      background-color: var(--primary-color);
      &:not(.disabled).active {
        background-color: var(--current-color);
      }
      &:not(.disabled):hover {
        color: var(--color-2);
      }
    }
  }

  // el-table
  .el-table:not(.unchanged) {
    background-color: transparent;
    color: var(--theme-color);
    .el-table__header-wrapper th, .el-table__fixed-header-wrapper th {
      color: var(--theme-color);
      background-color: var(--current-color);
    }
    .el-table__row, tr {
      background-color: var(--table-row-bg);
      &.hover-row > td.el-table__cell {
        background-color: var(--table-row-hover-bg);
      }
      &.current-row > td.el-table__cell {
        background-color: var(--table-row-hover-bg);
      }
    }
    th.el-table__cell.is-leaf, td.el-table__cell {
      border-color: var(--table-border-color);
    }
    .el-table__fixed, .el-table__fixed-right {
      &::before {
        background-color: var(--table-border-color);
        height: 0;
      }
    }
    .el-table__expand-icon {
      color: var(--theme-color);
    }
    // table中的checkbox样式
    .el-checkbox__input {
      &.is-checked .el-checkbox__inner, &.is-indeterminate .el-checkbox__inner {
        border-color: var(--color-3);
      }
    }
    &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell,
    &.el-table__body .el-table__row.hover-row > td.el-table__cell,
    &.el-table__body tr.current-row > td.el-table__cell {
      background-color: var(--table-row-hover-bg);
    }
    &.el-table--scrollable-x .el-table__fixed, &.el-table--scrollable-x .el-table__fixed-right {
      box-shadow: 0 0 10px var(--table-fixed-shadow);
    }
    &::before, .el-table--group::after, .el-table--border::after {
      background-color: var(--table-border-color);
    }
    &.el-table--border, .el-table--group {
      border-color: var(--table-border-color);
      &::after {
        background-color: var(--table-border-color);
      }
    }
    &.el-table--border th.el-table__cell,
    &.el-table--border th.el-table__cell.gutter:last-of-type {
      border-color: var(--table-border-color);
    }
    .el-table__footer-wrapper tbody td.el-table__cell,
    .el-table__fixed-footer-wrapper tbody td.el-table__cell {
      background-color: #364c72;
      color: var(--theme-color);
    }
    .el-table__body-wrapper.is-scrolling-left ~ .el-table__fixed,
    .el-table__body-wrapper.is-scrolling-right ~ .el-table__fixed-right {
      box-shadow: none;
    }
  }

  // el-loading
  .el-loading-mask {
    background-color: var(--loading-mask-bg);
    .el-loading-spinner .path {
      stroke: var(--current-color);
    }
  }

  // el-upload
  .el-upload__tip {
    color: var(--font-disable-color);
  }
  .el-upload--picture-card {
    background-color: var(--primary-color);
    border-color: var(--input-border-color);
    &:hover {
      border-color: var(--border-hover-color);
    }
    &:focus {
      border-color: var(--current-color);
    }
  }
  .el-upload-list--picture-card .el-upload-list__item-status-label {
    background: var(--current-color);
  }
  .el-upload-list--picture-card .el-upload-list__item {
    background-color: var(--primary-color);
  }
  .upload-file-list .el-upload-list__item {
    border-color: var(--input-border-color) !important;
  }
  .el-link.el-link--default  {
    color: var(--theme-color);
    &:hover {
      color: var(--color-2);
    }
  }
  .el-upload-list__item:hover {
    background-color: var(--primary-color);
  }
  // el-image
  .el-image {
    background-color: var(--primary-color) !important;
    box-shadow: 0 0 5px 1px var(--image-view-shadow) !important;
  }

  // el-tree
  .el-tree {
    color: var(--theme-color);
    background-color: var(--tree-bg);
    .el-tree-node {
      .el-tree-node__content:hover {
        background-color: var(--base-menu-background-active);
      }
      &:focus > .el-tree-node__content {
        background-color: var(--base-menu-background-active);
      }
      &.is-current > .el-tree-node__content {
        background-color: var(--base-menu-background-active);
      }
    }
  }

  // el-tag
  .el-tag {
    background-color: #c6e9fa;
    color: #3a7b99;
    border-color: #3a7b99;
    &.el-tag--warning {
      background-color: #f5eedf;
      color: #ff8800;
      border-color: #fff1cc;
    }
    &.el-tag--success {
      background-color: #dbece3;
      color: #03ce5e;
      border-color: #d0f5e0;
    }
    &.el-tag--danger {
      background-color: #ebdada;
      color: #e43a3a;
      border-color: #ffdbdb;
    }
    &.el-tag--info {
      background-color: #dededf;
      color: #7d7f85;
      border-color: #e9e9eb;
    }
  }

  // editor-富文本编辑器
  .ql-toolbar.ql-snow, .ql-container.ql-snow {
    border-color: var(--input-border-color);
    color: var(--theme-color);
    background-color: var(--primary-color);
  }
  .ql-snow {
    .ql-stroke, .ql-fill {
      stroke: var(--theme-color);
    }
    .ql-picker {
      color: var(--theme-color);
    }
    &.ql-toolbar .ql-picker-label:hover .ql-stroke,
    &.ql-toolbar .ql-picker-label.ql-active .ql-stroke {
      stroke: var(--color-2);
    }
    &.ql-toolbar button:hover,
    & .ql-toolbar button:hover,
    &.ql-toolbar button:focus,
    & .ql-toolbar button:focus,
    &.ql-toolbar button.ql-active,
    & .ql-toolbar button.ql-active,
    &.ql-toolbar .ql-picker-label:hover,
    & .ql-toolbar .ql-picker-label:hover,
    &.ql-toolbar .ql-picker-label.ql-active,
    & .ql-toolbar .ql-picker-label.ql-active,
    &.ql-toolbar .ql-picker-item:hover,
    & .ql-toolbar .ql-picker-item:hover,
    &.ql-toolbar .ql-picker-item.ql-selected,
    & .ql-toolbar .ql-picker-item.ql-selected {
      color: var(--color-2);
      .ql-stroke {
        stroke: var(--color-2);
      }
    }
    &.ql-toolbar button:hover,
    & .ql-toolbar button:hover,
    &.ql-toolbar button:focus,
    & .ql-toolbar button:focus,
    &.ql-toolbar button.ql-active {
      .ql-stroke, .ql-fill {
        stroke: var(--color-2);
      }
    }
    .ql-picker-options {
      background-color: var(--primary-color);
    }
    .ql-tooltip {
      background-color: var(--dialog-background);
      border-color: var(--input-border-color);
      color: var(--theme-color);
      input[type=text] {
        background-color: var(--primary-color);
        border-color: var(--input-border-color);
        color: var(--theme-color);
        &:focus {
          border-color: var(--current-color);
        }
      }
    }
    a {
      color: var(--color-2);
    }
  }
  .ql-editor.ql-blank::before {
    color: var(--font-disable-color);
  }
  .ql-toolbar.ql-snow .ql-picker.ql-expanded {
    .ql-picker-options {
      border-color: var(--input-border-color);
    }
    .ql-picker-label {
      border-color: var(--current-color);
    }
  }

  // el-transfer
  .el-transfer {
    .el-transfer-panel {
      background: var(--primary-color);
      border-color: var(--input-border-color);
      .el-transfer-panel__header {
        background: #2a3950;
        .el-checkbox .el-checkbox__label {
          color: var(--theme-color);
        }
      }
    }
  }

  // el-card
  .el-card {
    background-color: var(--base-item-bg);
    color: var(--theme-color);
    border-color: var(--base-item-bg);
  }

  // el-descriptions
  .el-descriptions {
    .el-descriptions__body {
      background-color: var(--base-item-bg);
      color: var(--theme-color);
    }
  }

  // el-progress
  .el-progress {
    .el-progress-bar__outer {
      background-color: #acabab !important;
    }
    .el-progress-bar__inner {
      background-color: var(--current-color);
    }
    &.is-success .el-progress-bar__inner {
      background-color: var(--color-2);
    }
  }

  // el-breadcrumb
  .el-breadcrumb {
    .el-breadcrumb__inner {
      color: var(--color-2);
    }
    .el-breadcrumb__inner a {
      color: var(--color-2);
      &:hover {
        color: var(--color-3);
      }
    }
  }

  // el-steps
  .el-steps {
    .el-step__head {
      .el-step__line {
        background-color: #c0c4cc;
      }
      .el-step__icon {
        background: #415063;
      }
      &.is-process {
        color: var(--color-3);
        border-color: var(--color-3);
      }
      &.is-finish {
        color: var(--color-2);
        border-color: var(--color-2);
      }
    }
    .el-step__description, .el-step__title {
      &.is-process {
        color: var(--color-3);
      }
      &.is-finish {
        color: var(--color-2);
      }
    }
  }
}

// el-button plain 样式修复
.el-button--primary.is-plain,
.el-button--success.is-plain,
.el-button--warning.is-plain,
.el-button--danger.is-plain {
  color: #fff !important;
  background-color: var(--current-color) !important;
  border: 1px solid var(--current-color) !important;
}

// info类型的plain按钮特殊处理，确保主题适配
.el-button--info.is-plain {
  color: var(--current-color, #409eff) !important;
  background-color: transparent !important;
  border: 1px solid var(--current-color, #409eff) !important;
}

.el-button--info.is-plain:hover {
  color: #fff !important;
  background-color: var(--current-color, #409eff) !important;
  border-color: var(--current-color, #409eff) !important;
}

// 深色主题下的info plain按钮
.theme-dark .el-button--info.is-plain {
  color: var(--current-color, #3a7b99) !important;
  border-color: var(--current-color, #3a7b99) !important;
}

.theme-dark .el-button--info.is-plain:hover {
  color: #fff !important;
  background-color: var(--current-color, #3a7b99) !important;
  border-color: var(--current-color, #3a7b99) !important;
}

// 星空主题下的info plain按钮
.theme-starry-sky .el-button--info.is-plain {
  color: var(--current-color, #1e3a8a) !important;
  border-color: var(--current-color, #1e3a8a) !important;
}

.theme-starry-sky .el-button--info.is-plain:hover {
  color: #fff !important;
  background-color: var(--current-color, #1e3a8a) !important;
  border-color: var(--current-color, #1e3a8a) !important;
}

// 深色主题下的success按钮样式（保持原有逻辑，但移到其他地方处理）
// success按钮的特殊样式会在后续的success按钮样式规则中处理
// 固定button样式
.btn-fixed {
  background-color: #3671e8 !important;
  &:hover {
    background-color: #5e8ded !important;
  }
}

// 星空主题
.theme-starry-sky {
  --primary-color: #0b0d1a;
  --current-color: #1e3a8a;
  --theme-color: #ffffff;
  --loading-bg: #1e3a8a;
  // 侧边栏sidebar
  --base-menu-background: #0b0d1a;
  --base-menu-background-active: #1e3a8a;
  --base-menu-color: #ffffff;
  --base-menu-color-active: #ffffff;
  --base-logo-title-color: #ffffff;
  --base-sub-menu-background: #1a1f3c;
  --base-sub-menu-hover: #1e3a8a;
  // 顶部navbar
  --base-nav-background: #0b0d1a;
  --icon-color: #ffffff;
  --nav-box-shadow-color: rgba(0,0,0,0.5);
  // 顶部tagView
  --base-tag-background: #0b0d1a;
  --tag-border-color: #1e3a8a;
  --tag-shadow-color-1:rgba(0, 0, 0, 0.5);
  --tag-shadow-color-2:rgba(0, 0, 0, 0.2);
  --tag-item-border: #1e3a8a;
  --tag-item-color: #ffffff;
  --tag-item-background: #0b0d1a;
  --tag-item-border-active: #1e3a8a;
  --tag-item-color-active: #ffffff;
  --tag-item-background-active: #1e3a8a;
  --tag-item-icon: #ffffff;
  --context-menu-background: #0b0d1a;
  --context-menu-color: #ffffff;
  --context-menu-background-active: #1e3a8a;
  --context-menu-color-active: #ffffff;
  --context-menu-shadow: rgba(0, 0, 0, 0.5);
  // body
  --base-body-background: #0b0d1a;
  --base-main-bg: #0b0d1a;
  --base-item-bg: #1a1f3c;
  --base-color-1: #ffffff;
  --base-color-2: #c0c0c0;
  --base-color-3: #a0a0a0;
  --base-color-4: #ffffff;
  --base-color-5: #d0d0d0;
  --base-color-6: #808080;
  --base-color-7: #1e3a8a;
  --base-color-8: #0b0d1a;
  --base-color-9: #1a1f3c;
  --border-color-1: #1e3a8a;
  --border-color-2: #ffffff;
  --border-color-3: #1a1f3c;
  --table-row-bg: #181e2a;
  --table-row-hover-bg: #25345a;
  --table-border-color: #233055;
  --table-header-bg: #1e3a8a;
  --table-header-color: #fff;
  --filter-bg: #232b3d;
  --filter-border: #2a3550;
  --input-bg: #181e2a;
  --input-border: #2a3550;
  --input-color: #fff;

  .el-table:not(.unchanged) {
    background-color: transparent;
    color: var(--theme-color);
    .el-table__header-wrapper th, .el-table__fixed-header-wrapper th {
      color: var(--table-header-color);
      background-color: var(--table-header-bg) !important;
    }
    .el-table__row, tr {
      background-color: var(--table-row-bg);
      &.hover-row > td.el-table__cell,
      &.current-row > td.el-table__cell {
        background-color: var(--table-row-hover-bg);
      }
    }
    th.el-table__cell.is-leaf, td.el-table__cell {
      border-color: var(--table-border-color);
    }
  }
  .el-input:not(.unchanged), .el-textarea:not(.unchanged) {
    .el-input__inner, .el-textarea__inner {
      background-color: var(--input-bg);
      color: var(--input-color);
      border-color: var(--input-border);
      &::placeholder {
        color: #7a88a8;
      }
    }
  }
  .el-select-dropdown {
    background-color: var(--input-bg);
    .el-select-dropdown__item {
      background-color: var(--input-bg);
      color: var(--input-color);
      &.selected,
      &:hover {
        background-color: var(--filter-bg);
        color: var(--theme-color);
      }
    }
  }
  .el-form .el-form-item__label {
    color: var(--theme-color);
  }
  .el-range-editor {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--input-color);
  }
  .el-pagination {
    background-color: transparent;
    color: var(--theme-color);
  }
  .filter-container, .filter-item {
    background: var(--filter-bg) !important;
    border: 1px solid var(--filter-border) !important;
    color: var(--theme-color) !important;
  }

  // 时间选择器弹窗及输入框适配
  .el-date-editor,
  .el-range-editor,
  .el-date-picker,
  .el-picker-panel,
  .el-picker-panel__body-wrapper,
  .el-picker-panel__footer {
    background: var(--input-bg) !important;
    color: var(--input-color) !important;
    border-color: var(--input-border) !important;
  }
  .el-date-editor .el-range-input,
  .el-date-editor .el-input__inner,
  .el-range-editor .el-range-input {
    background: var(--input-bg) !important;
    color: var(--input-color) !important;
    border-color: var(--input-border) !important;
  }
  .el-date-editor .el-range-separator {
    color: var(--input-color) !important;
  }
  .el-picker-panel__icon-btn,
  .el-picker-panel__footer .el-button--text {
    color: var(--input-color) !important;
  }
  .el-picker-panel__content th,
  .el-picker-panel__content td {
    color: var(--input-color) !important;
  }
  .el-picker-panel__content td.available:hover {
    background: var(--table-row-hover-bg) !important;
    color: var(--theme-color) !important;
  }
  .el-picker-panel__content td.current:not(.disabled) span,
  .el-picker-panel__content td.start-date span,
  .el-picker-panel__content td.end-date span {
    background: var(--current-color) !important;
    color: var(--theme-color) !important;
  }
  .el-picker-panel__content td.in-range div {
    background: var(--table-row-hover-bg) !important;
  }

  // 表格滚动条适配
  .el-table__body-wrapper::-webkit-scrollbar,
  .el-table__body-wrapper::-webkit-scrollbar-thumb,
  .el-table__body-wrapper::-webkit-scrollbar-track {
    background: transparent;
  }
  .el-table__body-wrapper::-webkit-scrollbar {
    height: 8px;
    background: transparent;
  }
  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #232b3d;
    border-radius: 4px;
  }
  .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background: #2a3550;
  }
  .el-table__body-wrapper::-webkit-scrollbar-track {
    background: transparent;
  }

  // 全局覆盖所有表格悬浮行和当前行的背景色
  .el-table__row.hover-row > td,
  tr.hover-row > td,
  .el-table__row.current-row > td,
  tr.current-row > td,
  .el-table__body tr:hover > td,
  .el-table__body tr.current-row > td {
    background-color: var(--table-row-hover-bg) !important;
    color: var(--theme-color) !important;
  }

  // 弹窗适配
  .el-dialog {
    background: var(--base-item-bg) !important;
    color: var(--theme-color) !important;
    box-shadow: 0 8px 40px 0 rgba(30, 58, 138, 0.5);
    border-radius: 10px;
    .el-dialog__header {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
      border-bottom: 1px solid var(--border-color-1);
    }
    .el-dialog__title {
      color: var(--theme-color) !important;
    }
    .el-dialog__body {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
    }
    .el-dialog__footer {
      background: var(--base-color-9) !important;
      border-top: 1px solid var(--border-color-1);
    }
  }
  // 弹窗按钮适配
  .el-dialog .el-button,
  .el-dialog .el-button--default {
    background: var(--current-color) !important;
    color: var(--theme-color) !important;
    border: none;
  }
  .el-dialog .el-button--primary {
    background: var(--current-color) !important;
    color: var(--theme-color) !important;
    border: none;
  }
  .el-dialog .el-button:hover,
  .el-dialog .el-button--primary:hover {
    background: var(--base-menu-background-active) !important;
    color: var(--theme-color) !important;
  }
  // 弹窗footer适配
  .dialog-footer {
    background: var(--base-color-9) !important;
    border-top: 1px solid var(--border-color-1);
  }
  // 适配局部弹窗如icon-dialog
  .icon-dialog ::v-deep .el-dialog {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .icon-dialog ::v-deep .el-dialog__body {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .icon-ul li {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .icon-ul li.active-item {
    background: var(--base-menu-background-active) !important;
    color: var(--current-color) !important;
  }

  // 部门树 el-tree 适配星空主题
  .el-tree {
    background-color: var(--tree-bg) !important;
    color: var(--theme-color) !important;
    border-radius: 8px;
  }
  .el-tree-node__content {
    background: var(--tree-bg) !important;
    color: var(--theme-color) !important;
    border-radius: 4px;
    transition: background 0.2s;
  }
  .el-tree-node__content:hover,
  .el-tree-node.is-current > .el-tree-node__content {
    background: var(--base-menu-background-active) !important;
    color: var(--theme-color) !important;
  }
  .el-tree-node__label {
    color: var(--theme-color) !important;
  }
  .el-tree-node__expand-icon {
    color: var(--theme-color) !important;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background: var(--base-menu-background-active) !important;
    color: var(--theme-color) !important;
  }
  .el-tree .el-tree-node__content .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: var(--current-color) !important;
    border-color: var(--current-color) !important;
  }

  // 卡片、表格、表单、输入框、按钮等适配星空主题
  .el-card {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border: 1px solid var(--border-color-1) !important;
    box-shadow: 0 2px 8px var(--tag-shadow-color-1);
  }
  .el-card__header {
    background: var(--base-color-9) !important;
    color: var(--theme-color) !important;
    border-bottom: 1px solid var(--border-color-1) !important;
  }
  .el-table {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .el-table th, .el-table__header-wrapper th {
    background: var(--base-color-9) !important;
    color: var(--theme-color) !important;
  }
  .el-table td, .el-table__body-wrapper td {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .el-table__row:hover > td {
    background: var(--table-row-hover-bg) !important;
  }
  .el-input__inner, .el-textarea__inner {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-color: var(--border-color-1) !important;
  }
  .el-form-item__label {
    color: var(--theme-color) !important;
  }
  .el-form {
    background: var(--base-main-bg) !important;
  }
  .el-form-item {
    background: transparent !important;
  }
  .el-button:not(.el-button--text):not(.el-button--info.is-plain) {
    background: var(--current-color) !important;
    color: #fff !important;
    border: none !important;
    
    &:hover {
      background: var(--color-2) !important;
      color: #fff !important;
    }
    
    &.el-button--default {
      background: var(--base-main-bg) !important;
      color: var(--theme-color) !important;
      border: 1px solid var(--border-color-1) !important;
      
      &:hover {
        background: var(--table-row-hover-bg) !important;
        border-color: var(--current-color) !important;
        color: var(--current-color) !important;
      }
    }
  }
  
  // 专门处理星空主题下的info plain按钮，优先级更高
  .el-button--info.is-plain {
    background: transparent !important;
    color: var(--current-color, #1e3a8a) !important;
    border: 1px solid var(--current-color, #1e3a8a) !important;
    
    &:hover {
      background: var(--current-color, #1e3a8a) !important;
      color: #fff !important;
      border-color: var(--current-color, #1e3a8a) !important;
    }
  }
  
  // 文本按钮单独处理，避免全局覆盖
  .el-button--text {
    background: transparent !important;
    border: none !important;
    color: var(--current-color) !important;
    
    &:hover {
      background: rgba(var(--current-color-rgb), 0.1) !important;
      color: var(--color-2) !important;
    }
  }
  
  // Success按钮特殊处理，确保在星空主题下也能正确显示
  .el-button--success {
    background: #67c23a !important;
    border-color: #67c23a !important;
    color: #fff !important;
  }
  .el-button--success:hover {
    background: #85ce61 !important;
    border-color: #85ce61 !important;
    color: #fff !important;
  }

  // 表单设计器页面适配星空主题
  .container {
    background: var(--base-body-background) !important;
  }
  .left-board, .right-panel {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-right: 1px solid var(--border-color-1) !important;
  }
  .center-board {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-left: 1px solid var(--border-color-1) !important;
    border-right: 1px solid var(--border-color-1) !important;
  }
  .action-bar, .logo-wrapper {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
    border-bottom: 1px solid var(--border-color-1) !important;
  }
  .components-list, .components-title, .components-body {
    background: var(--base-main-bg) !important;
    color: var(--theme-color) !important;
  }
  .components-title {
    color: var(--theme-color) !important;
  }
  .components-body {
    border: 1px dashed var(--border-color-1) !important;
  }
  .components-body:hover {
    border: 1px dashed var(--current-color) !important;
    color: var(--current-color) !important;
  }
  .empty-info {
    color: var(--color-3) !important;
  }
  .el-form, .el-form-item {
    background: transparent !important;
    color: var(--theme-color) !important;
  }
  .el-scrollbar__view {
    background: transparent !important;
  }
}

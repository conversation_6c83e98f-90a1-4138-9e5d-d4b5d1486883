/**
 * 触摸事件性能优化工具
 * 解决Element UI popup-manager触摸事件性能警告
 */

/**
 * 优化触摸事件监听器
 * 将非被动监听器转换为被动监听器
 */
export const optimizeTouchEvents = () => {
  // 检测是否支持被动监听器
  let supportsPassive = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return false
      }
    })
    window.addEventListener('testPassive', null, opts)
    window.removeEventListener('testPassive', null, opts)
  } catch (e) {
    console.warn('浏览器不支持被动事件监听器')
  }

  if (!supportsPassive) {
    return
  }

  // 重写Element UI的事件监听器
  const originalAddEventListener = EventTarget.prototype.addEventListener
  const originalRemoveEventListener = EventTarget.prototype.removeEventListener

  // 需要优化的触摸事件类型
  const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel']
  const wheelEvents = ['wheel', 'mousewheel', 'DOMMouseScroll']

  EventTarget.prototype.addEventListener = function(type, listener, options) {
    // 对触摸和滚轮事件进行优化
    if (touchEvents.includes(type) || wheelEvents.includes(type)) {
      // 如果是Element UI的popup相关事件，设置为被动
      if (this.classList && (
        this.classList.contains('el-dialog') ||
        this.classList.contains('el-popup') ||
        this.classList.contains('el-overlay') ||
        this === document.body ||
        this === document
      )) {
        options = typeof options === 'boolean' ? { passive: true, capture: options } : { ...options, passive: true }
      }
    }

    return originalAddEventListener.call(this, type, listener, options)
  }

  console.log('✅ 触摸事件性能优化已启用')
}

/**
 * 为对话框组件添加触摸优化
 */
export const optimizeDialogTouch = (dialogElement) => {
  if (!dialogElement) return

  // 添加CSS优化
  const style = document.createElement('style')
  style.textContent = `
    .el-dialog__wrapper {
      touch-action: pan-y;
      -webkit-overflow-scrolling: touch;
    }

    .el-dialog {
      touch-action: auto;
    }

    .el-dialog__body {
      touch-action: pan-y;
      overscroll-behavior: contain;
    }

    /* 优化移动端滚动性能 */
    @media (max-width: 768px) {
      .el-dialog__wrapper {
        will-change: transform;
      }

      .enhanced-tabs .el-tabs__content {
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
      }
    }
  `

  if (!document.querySelector('#touch-optimization-styles')) {
    style.id = 'touch-optimization-styles'
    document.head.appendChild(style)
  }
}

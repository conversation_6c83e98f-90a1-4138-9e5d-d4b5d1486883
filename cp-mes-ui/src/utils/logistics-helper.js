/**
 * 物流追踪辅助工具函数
 */

/**
 * 验证快递单号格式
 * @param {string} trackingNumber 快递单号
 * @returns {boolean} 是否有效
 */
export function validateTrackingNumber(trackingNumber) {
  if (!trackingNumber || typeof trackingNumber !== 'string') {
    return false
  }

  const trimmed = trackingNumber.trim()

  // 基本格式验证：长度在6-30位之间，包含字母和数字
  const basicRegex = /^[A-Za-z0-9]{6,30}$/
  if (!basicRegex.test(trimmed)) {
    return false
  }

  // 特殊格式验证（可根据需要扩展）
  const specialPatterns = [
    /^[A-Z]{2}\d{9}[A-Z]{2}$/, // EMS格式
    /^\d{12}$/, // 12位纯数字
    /^[A-Z]\d{12}$/, // 字母+12位数字
    /^[A-Z]{4}\d{10}$/, // 4字母+10数字
    /^\d{13}$/, // 13位纯数字
  ]

  return basicRegex.test(trimmed) || specialPatterns.some(pattern => pattern.test(trimmed))
}

/**
 * 格式化物流状态显示文本
 * @param {string} status 状态码
 * @returns {string} 显示文本
 */
export function formatLogisticsStatus(status) {
  const statusMap = {
    'SIGNED': '已签收',
    'IN_TRANSIT': '运输中',
    'PICKED_UP': '已揽收',
    'OUT_FOR_DELIVERY': '派送中',
    'NO_INFO': '暂无信息',
    'QUERY_FAILED': '查询失败',
    'SERVICE_ERROR': '服务异常'
  }
  return statusMap[status] || status || '未知状态'
}

/**
 * 获取物流状态对应的标签类型
 * @param {string} status 状态码
 * @returns {string} 标签类型
 */
export function getLogisticsStatusType(status) {
  const typeMap = {
    'SIGNED': 'success',
    'IN_TRANSIT': 'primary',
    'PICKED_UP': 'warning',
    'OUT_FOR_DELIVERY': 'info',
    'NO_INFO': 'info',
    'QUERY_FAILED': 'danger',
    'SERVICE_ERROR': 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 防抖函数 - 增强版本
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @param {Object} options 选项
 * @param {boolean} options.immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay, options = {}) {
  let timeoutId
  let lastCallTime
  const { immediate = false } = options

  return function (...args) {
    const callNow = immediate && !timeoutId

    clearTimeout(timeoutId)

    timeoutId = setTimeout(() => {
      timeoutId = null
      if (!immediate) {
        func.apply(this, args)
      }
    }, delay)

    if (callNow) {
      func.apply(this, args)
    }

    lastCallTime = Date.now()
  }
}

/**
 * 节流函数 - 增强版本
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间（毫秒）
 * @param {Object} options 选项
 * @param {boolean} options.leading 是否在开始时执行
 * @param {boolean} options.trailing 是否在结束时执行
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay, options = {}) {
  let lastCall = 0
  let timeoutId
  const { leading = true, trailing = true } = options

  return function (...args) {
    const now = Date.now()

    if (!lastCall && !leading) {
      lastCall = now
    }

    const remaining = delay - (now - lastCall)

    if (remaining <= 0 || remaining > delay) {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      lastCall = now
      func.apply(this, args)
    } else if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        lastCall = leading ? Date.now() : 0
        timeoutId = null
        func.apply(this, args)
      }, remaining)
    }
  }
}

/**
 * 格式化时间
 * @param {string|Date|number} time 时间值
 * @param {string} format 格式化模式
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'full') {
  if (!time) return '--'

  try {
    const date = new Date(time)
    if (isNaN(date.getTime())) return '--'

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    switch (format) {
      case 'date':
        return `${year}-${month}-${day}`
      case 'time':
        return `${hours}:${minutes}`
      case 'datetime':
        return `${month}-${day} ${hours}:${minutes}`
      case 'full':
        return `${year}-${month}-${day} ${hours}:${minutes}`
      case 'detailed':
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      default:
        return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  } catch (error) {
    console.error('formatTime: 时间格式化失败', error, time)
    return '--'
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }

  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

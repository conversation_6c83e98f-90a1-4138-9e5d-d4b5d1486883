import store from '@/store'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
  if (value && value instanceof Array && value.length > 0) {
    const permissions = store.getters && store.getters.permissions
    const permissionDatas = value
    const all_permission = "*:*:*";

    // 增加权限数组的安全检查
    if (!permissions || !Array.isArray(permissions)) {
      return false
    }

    const hasPermission = permissions.some(permission => {
      // 确保permission不为undefined或null
      if (!permission || typeof permission !== 'string') {
        return false
      }
      return all_permission === permission || permissionDatas.includes(permission)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkPermi="['system:user:add','system:user:edit']"`)
    return false
  }
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const permissionRoles = value
    const super_admin = "admin";

    // 增加角色数组的安全检查
    if (!roles || !Array.isArray(roles)) {
      return false
    }

    const hasRole = roles.some(role => {
      // 确保role不为undefined或null
      if (!role || typeof role !== 'string') {
        return false
      }
      return super_admin === role || permissionRoles.includes(role)
    })

    if (!hasRole) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkRole="['admin','editor']"`)
    return false
  }
}

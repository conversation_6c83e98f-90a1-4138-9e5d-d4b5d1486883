<template>
  <canvas id="global-starry-sky-canvas" class="starry-sky-bg-canvas"></canvas>
</template>

<script>
export default {
  name: 'StarrySkyBg',
  data() {
    return {
      starrySkyMouse: { x: window.innerWidth / 2, y: window.innerHeight / 2 },
      starrySkyStars: [],
      starrySkyNumStars: 220,
      starrySkyCanvas: null,
      starrySkyCtx: null,
      starrySkyAnimId: null,
      connectionThreshold: 120,
      trailPoints: [],
      glowParticles: [],
      glowParticleNum: 40,
      meteors: [],
      lastMeteorTime: 0,
      starFieldOffset: { x: 0, y: 0 },
      starFieldAngle: 0,
    };
  },
  mounted() {
    this.initStarrySky();
    window.addEventListener('resize', this.resizeStarrySky);
    document.addEventListener('mousemove', this.handleMouseMove);
    this.initGlowParticles();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeStarrySky);
    document.removeEventListener('mousemove', this.handleMouseMove);
    cancelAnimationFrame(this.starrySkyAnimId);
  },
  methods: {
    initStarrySky() {
      const canvas = document.getElementById('global-starry-sky-canvas');
      if (!canvas) return;
      this.starrySkyCanvas = canvas;
      this.starrySkyCtx = canvas.getContext('2d');
      this.starrySkyMouse = { x: window.innerWidth / 2, y: window.innerHeight / 2 };
      this.starrySkyStars = [];
      this.starrySkyNumStars = 220;
      this.resizeStarrySky();
      this.initStars();
      this.animateStarrySky();
    },
    resizeStarrySky() {
      if (!this.starrySkyCanvas) return;
      this.starrySkyCanvas.width = window.innerWidth;
      this.starrySkyCanvas.height = window.innerHeight;
      this.starrySkyStars = [];
      this.initStars();
    },
    handleMouseMove(e) {
      if (!this.starrySkyMouse) return;
      this.starrySkyMouse.x = e.clientX;
      this.starrySkyMouse.y = e.clientY;
      this.trailPoints.push({ x: e.clientX, y: e.clientY });
      if (this.trailPoints.length > 30) this.trailPoints.shift();
    },
    initStars() {
      if (!this.starrySkyCanvas) return;
      this.starrySkyStars = [];
      for (let i = 0; i < this.starrySkyNumStars; i++) {
        this.starrySkyStars.push({
          originX: Math.random() * this.starrySkyCanvas.width,
          originY: Math.random() * this.starrySkyCanvas.height,
          x: 0,
          y: 0,
          size: Math.random() * 1.5 + 0.3,
          baseSize: 0,
          brightness: Math.random() * 0.6 + 0.2,
          color: '',
          depth: Math.random() * 0.6 + 0.1,
          opacityDirection: Math.random() > 0.5 ? 1 : -1,
          twinkleSpeed: Math.random() * 0.015 + 0.002,
          vx: (Math.random() - 0.5) * 0.1,
          vy: (Math.random() - 0.5) * 0.1
        });
      }
      this.starrySkyStars.forEach(star => { star.baseSize = star.size; });
    },
    initGlowParticles() {
      this.glowParticles = [];
      const w = window.innerWidth;
      const h = window.innerHeight;
      for (let i = 0; i < this.glowParticleNum; i++) {
        this.glowParticles.push({
          x: Math.random() * w,
          y: Math.random() * h,
          r: 1.5 + Math.random() * 2.5,
          baseR: 1.5 + Math.random() * 2.5,
          alpha: 0.25 + Math.random() * 0.25,
          speedX: (Math.random() - 0.5) * 0.08,
          speedY: (Math.random() - 0.5) * 0.08,
          phase: Math.random() * Math.PI * 2
        });
      }
    },
    createMeteor() {
      const w = this.starrySkyCanvas.width;
      const h = this.starrySkyCanvas.height;
      const startX = Math.random() * w * 0.8 + w * 0.1;
      const startY = Math.random() * h * 0.2;
      const angle = Math.PI / 2.5 + (Math.random() - 0.5) * 0.2;
      const speed = 8 + Math.random() * 4;
      const length = 180 + Math.random() * 80;
      return {
        x: startX,
        y: startY,
        angle,
        speed,
        length,
        alpha: 1,
        life: 0
      };
    },
    animateStarrySky() {
      if (!this.starrySkyCanvas) return;
      const ctx = this.starrySkyCtx;
      const stars = this.starrySkyStars;
      const mouse = this.starrySkyMouse;
      ctx.clearRect(0, 0, this.starrySkyCanvas.width, this.starrySkyCanvas.height);
      // 微光粒子悬浮
      for (let i = 0; i < this.glowParticles.length; i++) {
        const p = this.glowParticles[i];
        p.x += p.speedX;
        p.y += p.speedY;
        if (p.x < 0 || p.x > this.starrySkyCanvas.width) p.speedX *= -1;
        if (p.y < 0 || p.y > this.starrySkyCanvas.height) p.speedY *= -1;
        p.r = p.baseR + Math.sin(Date.now() / 1200 + p.phase) * 0.7;
        ctx.save();
        ctx.globalAlpha = p.alpha + 0.15 * Math.abs(Math.sin(Date.now() / 1500 + p.phase));
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(180,200,255,0.7)';
        ctx.shadowColor = '#bcdcff';
        ctx.shadowBlur = 16;
        ctx.fill();
        ctx.restore();
      }
      // 拖尾粒子
      for (let i = 0; i < this.trailPoints.length; i++) {
        const p = this.trailPoints[i];
        ctx.save();
        ctx.globalAlpha = (i + 1) / this.trailPoints.length * 0.5;
        ctx.beginPath();
        ctx.arc(p.x, p.y, 8 * (i + 1) / this.trailPoints.length, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.shadowColor = '#fff';
        ctx.shadowBlur = 12;
        ctx.fill();
        ctx.restore();
      }
      // 星空流动整体偏移和旋转
      this.starFieldOffset.x += 0.04 * Math.cos(Date.now() / 8000);
      this.starFieldOffset.y += 0.02 * Math.sin(Date.now() / 12000);
      this.starFieldAngle += 0.00012;
      stars.forEach(star => {
        const mouseOffsetX = (mouse.x - window.innerWidth / 2) * star.depth * 0.03;
        const mouseOffsetY = (mouse.y - window.innerHeight / 2) * star.depth * 0.03;
        let ox = star.originX + this.starFieldOffset.x;
        let oy = star.originY + this.starFieldOffset.y;
        const cx = this.starrySkyCanvas.width / 2;
        const cy = this.starrySkyCanvas.height / 2;
        const dx = ox - cx;
        const dy = oy - cy;
        const cosA = Math.cos(this.starFieldAngle);
        const sinA = Math.sin(this.starFieldAngle);
        const rx = dx * cosA - dy * sinA;
        const ry = dx * sinA + dy * cosA;
        ox = cx + rx;
        oy = cy + ry;
        star.x = ox + mouseOffsetX;
        star.y = oy + mouseOffsetY;
        const dx2 = star.x - mouse.x;
        const dy2 = star.y - mouse.y;
        const dist = Math.sqrt(dx2 * dx2 + dy2 * dy2);
        if (dist < 80) {
          star.x -= dx2 * 0.03;
          star.y -= dy2 * 0.03;
          star.size = star.baseSize + 1.2 * (1 - dist / 80);
          star.color = `rgba(255,255,200,${Math.min(1, star.brightness + 0.4)})`;
        } else {
          star.size = star.baseSize;
          star.color = `rgba(220,220,255,${star.brightness})`;
        }
        star.brightness += star.twinkleSpeed * star.opacityDirection;
        if (star.brightness > 0.8 || star.brightness < 0.1) {
          star.opacityDirection *= -1;
          star.brightness = Math.max(0.1, Math.min(0.8, star.brightness));
        }
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2, false);
        ctx.fillStyle = star.color;
        ctx.shadowColor = star.color;
        ctx.shadowBlur = star.size * 2;
        ctx.fill();
        ctx.shadowBlur = 0;
      });
      this.drawConnections(ctx, stars);
      // 流星掉落
      const now = Date.now();
      if (now - this.lastMeteorTime > 1200 + Math.random() * 1200) {
        this.meteors.push(this.createMeteor());
        this.lastMeteorTime = now;
      }
      for (let i = this.meteors.length - 1; i >= 0; i--) {
        const m = this.meteors[i];
        for (let t = 0; t < 8; t++) {
          const tailX = m.x - Math.cos(m.angle) * (m.length * t / 8);
          const tailY = m.y - Math.sin(m.angle) * (m.length * t / 8);
          ctx.save();
          ctx.globalAlpha = m.alpha * (1 - t / 8) * 0.7;
          ctx.beginPath();
          ctx.arc(tailX, tailY, 2.2 + 2 * (1 - t / 8), 0, Math.PI * 2);
          ctx.fillStyle = 'rgba(255,255,255,0.85)';
          ctx.shadowColor = '#fff';
          ctx.shadowBlur = 16;
          ctx.fill();
          ctx.restore();
        }
        ctx.save();
        ctx.globalAlpha = m.alpha;
        ctx.strokeStyle = 'rgba(255,255,255,0.85)';
        ctx.lineWidth = 2.2;
        ctx.beginPath();
        ctx.moveTo(m.x, m.y);
        ctx.lineTo(m.x - Math.cos(m.angle) * m.length, m.y - Math.sin(m.angle) * m.length);
        ctx.stroke();
        ctx.restore();
        m.x += Math.cos(m.angle) * m.speed;
        m.y += Math.sin(m.angle) * m.speed;
        m.alpha -= 0.012;
        m.life += 1;
        if (m.x > this.starrySkyCanvas.width + 100 || m.y > this.starrySkyCanvas.height + 100 || m.alpha <= 0 || m.life > 120) {
          this.meteors.splice(i, 1);
        }
      }
      this.starrySkyAnimId = requestAnimationFrame(this.animateStarrySky);
    },
    drawConnections(ctx, stars) {
      for (let i = 0; i < stars.length; i++) {
        for (let j = i + 1; j < stars.length; j++) {
          const dx = stars[i].x - stars[j].x;
          const dy = stars[i].y - stars[j].y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < this.connectionThreshold) {
            ctx.save();
            ctx.globalAlpha = 0.18 * (1 - dist / this.connectionThreshold);
            ctx.strokeStyle = '#BFD6FF';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(stars[i].x, stars[i].y);
            ctx.lineTo(stars[j].x, stars[j].y);
            ctx.stroke();
            ctx.restore();
          }
        }
      }
    },
  }
};
</script>

<style scoped>
.starry-sky-bg-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
}
</style> 
<template>
  <div style="height: 100%">
    <el-dropdown
      trigger="hover"
      @command="handleCommand"
      style="height: 100%"
    >
      <!-- <img src="@/assets/images/theme-black.png" alt="" style="height: 20px; width: 20px;"> -->
      <div style="display: flex; align-items: center; height: 100%">
        <svg
          t="1698140542164"
          class="icon-theme"
          viewBox="0 0 1117 1024"
          xmlns="http://www.w3.org/2000/svg"
          p-id="8724"
          width="64"
          height="64"
        >
          <path
            d="M824.450212 1022.47992H293.508317a124.273745 124.273745 0 0 1-88.40612-36.877981 126.799634 126.799634 0 0 1-35.867625-88.911298l3.536245-353.624478a50.517783 50.517783 0 0 0-14.14498-34.85727 50.517783 50.517783 0 0 0-34.352092-14.144979A124.273745 124.273745 0 0 1 0 370.800524V227.330022a123.768567 123.768567 0 0 1 68.199007-110.633944l202.07113-101.035565A125.284101 125.284101 0 0 1 367.26428 6.567312a126.799634 126.799634 0 0 1 73.250784 65.673117A125.284101 125.284101 0 0 0 555.695609 136.903191h22.227824a118.716789 118.716789 0 0 0 98.509676-63.652406 126.294457 126.294457 0 0 1 74.766318-65.673118 121.747856 121.747856 0 0 1 94.973432 6.567312l202.07113 103.561454a123.26339 123.26339 0 0 1 67.693829 110.633944v143.470503a124.273745 124.273745 0 0 1-123.768568 124.273745 50.517783 50.517783 0 0 0-34.85727 14.144979 47.486716 47.486716 0 0 0-13.639801 34.85727l3.031067 353.624479a123.26339 123.26339 0 0 1-124.273745 125.2841z m-242.485356-75.776674h242.485356a50.517783 50.517783 0 0 0 34.85727-14.144979 46.47636 46.47636 0 0 0 13.639801-34.352092l-3.031067-353.624479a121.242678 121.242678 0 0 1 35.867626-88.406119 122.758212 122.758212 0 0 1 88.40612-36.877981 50.517783 50.517783 0 0 0 47.991893-50.517783V228.340377a50.517783 50.517783 0 0 0-26.269247-42.940115l-202.07113-101.035565a50.517783 50.517783 0 0 0-36.877982-2.525889 50.517783 50.517783 0 0 0-29.300313 26.774425c-57.590272 120.7375-199.040063 107.602877-205.102198 107.097699h-8.082845a191.967574 191.967574 0 0 1-160.646549-106.087344 50.517783 50.517783 0 0 0-29.805491-26.774425 50.517783 50.517783 0 0 0-36.372804 2.52589L101.035565 184.389907a50.517783 50.517783 0 0 0-26.269247 42.940115v143.470502a50.517783 50.517783 0 0 0 50.517783 50.517783 124.273745 124.273745 0 0 1 124.273745 125.284101l-3.536245 353.624478a50.517783 50.517783 0 0 0 50.517783 50.517783h288.456539z"
            p-id="8725"
          ></path>
        </svg>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="theme-light">
          <span>浅色主题</span>
        </el-dropdown-item>
        <el-dropdown-item command="theme-dark">
          <span>深色主题</span>
        </el-dropdown-item>
        <el-dropdown-item command="theme-starry-sky">
          <span>星空主题</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  methods: {
    // 修改系统主题
    handleCommand(value) {
      this.$store.dispatch("app/setTheme", value);
      localStorage.setItem("theme", value);
      document.getElementsByTagName("body")[0].className = value;
    },
  },
};
</script>

<style scoped>
.icon-theme {
  height: 20px;
  width: 20px;
  fill: var(--icon-color);
}
</style>
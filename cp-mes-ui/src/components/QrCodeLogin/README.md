# 扫码登录功能说明

## 功能概述

扫码登录功能允许用户通过移动端App扫描网页端二维码，快速完成网页端登录，无需输入用户名密码。

## 技术架构

### 登录流程

1. **网页端生成二维码**
   - 调用 `/qrcode/generate` 接口生成唯一UUID
   - UUID存储到Redis中，设置5分钟过期时间
   - 生成包含UUID的二维码并显示

2. **移动端扫码验证**
   - 移动端扫描二维码获取UUID
   - 调用 `/qrcode/verify` 接口，携带移动端token和UUID
   - 服务端验证移动端token，获取用户手机号
   - 根据手机号匹配网页端用户信息
   - 生成网页端登录token并关联到UUID

3. **网页端轮询登录**
   - 网页端轮询 `/qrcode/status/{uuid}` 接口
   - 检测到登录成功后获取token并跳转

### 安全特性

- **时效性**：二维码5分钟自动过期
- **单次使用**：每个UUID仅能使用一次
- **用户匹配**：基于手机号进行跨平台用户匹配
- **状态控制**：实时状态管理，防止重复使用

## API接口文档

### 1. 生成二维码

**接口**: `POST /qrcode/generate`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "qrUuid": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
    "expireTime": 1703123456789
  }
}
```

### 2. 移动端验证

**接口**: `POST /qrcode/verify`

**请求参数**:
```json
{
  "qrUuid": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  "mobileToken": "移动端登录token",
  "deviceInfo": "设备信息（可选）",
  "userConfirmed": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "登录确认成功",
  "data": "登录确认成功"
}
```

### 3. 查询登录状态

**接口**: `GET /qrcode/status/{qrUuid}`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "qrUuid": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
    "status": "success",
    "message": "登录成功",
    "token": "网页端登录token"
  }
}
```

**状态说明**:
- `pending`: 等待扫码
- `confirmed`: 已扫码待确认
- `success`: 登录成功
- `expired`: 二维码已过期
- `error`: 发生错误

### 4. 取消二维码

**接口**: `DELETE /qrcode/cancel/{qrUuid}`

**响应示例**:
```json
{
  "code": 200,
  "msg": "二维码已取消",
  "data": null
}
```

## 前端组件使用

### QrCodeLogin 组件

```vue
<template>
  <QrCodeLogin 
    @login-success="handleLoginSuccess" 
    @switch-login="handleSwitchLogin" 
  />
</template>

<script>
import QrCodeLogin from '@/components/QrCodeLogin/index.vue'

export default {
  components: { QrCodeLogin },
  methods: {
    handleLoginSuccess() {
      // 登录成功后的处理逻辑
      this.$router.push('/dashboard')
    },
    handleSwitchLogin(type) {
      // 切换登录方式
      this.loginType = type
    }
  }
}
</script>
```

### 组件特性

- **自动生成**：组件挂载时自动生成二维码
- **实时轮询**：每2秒轮询一次登录状态
- **倒计时显示**：显示二维码剩余有效时间
- **状态管理**：支持等待、成功、过期、错误等状态
- **主题适配**：支持深色/浅色主题自动切换

## 移动端集成指南

### 扫码处理

移动端扫描二维码后，需要解析二维码内容：

```javascript
// 二维码内容格式
{
  "type": "login",
  "uuid": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  "timestamp": 1703123456789
}
```

### 验证请求

```javascript
// 移动端发送验证请求
const verifyData = {
  qrUuid: qrData.uuid,
  mobileToken: getCurrentUserToken(),
  deviceInfo: getDeviceInfo(),
  userConfirmed: true
}

fetch('/api/qrcode/verify', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(verifyData)
})
```

## 配置说明

### Redis配置

确保Redis正常运行，用于存储二维码状态和token信息：

```yaml
# application.yml
redisson:
  keyPrefix: "cp-mes"
  singleServerConfig:
    connectionPoolSize: 32
    timeout: 3000
```

### 二维码配置

可在控制器中调整相关参数：

```java
// SysQrCodeLoginController.java
private static final int QR_CODE_EXPIRE_MINUTES = 5; // 二维码过期时间（分钟）
```

### 轮询配置

前端轮询间隔可在组件中调整：

```javascript
// QrCodeLogin/index.vue
}, 2000) // 轮询间隔（毫秒）
```

## 常见问题

### Q: 二维码无法生成
A: 检查后端服务是否正常，Redis连接是否正常

### Q: 扫码后无反应
A: 确认移动端token有效，检查网络连接

### Q: 登录失败
A: 检查移动端用户与网页端用户手机号是否一致

### Q: 样式显示异常
A: 确认已正确引入相关CSS变量和主题样式

## 安全建议

1. **HTTPS强制**：生产环境必须使用HTTPS
2. **Token验证**：严格验证移动端token有效性
3. **频率限制**：考虑增加请求频率限制
4. **日志记录**：记录登录行为供安全审计
5. **异常监控**：监控异常登录行为

## 性能优化

1. **Redis优化**：合理设置Redis过期时间
2. **轮询优化**：可考虑使用WebSocket替代轮询
3. **图片优化**：二维码图片大小优化
4. **缓存策略**：合理使用浏览器缓存 
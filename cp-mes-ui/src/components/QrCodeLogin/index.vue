<template>
  <div class="qr-login-container">
    <div class="qr-code-box">
      <!-- 二维码展示区域 -->
      <div v-if="qrCodeStatus === 'pending'" class="qr-code-display">
        <canvas ref="qrCanvas" style="display: none;"></canvas>
        <div class="qr-image-container">
          <img v-if="qrCodeImage" :src="qrCodeImage" alt="登录二维码" class="qr-image" />
          <div class="qr-loading" v-if="!qrCodeImage">
            <i class="el-icon-loading"></i>
            生成中...
          </div>
        </div>
        <div class="qr-tips">
          <p>请使用移动端App扫描二维码</p>
          <p>扫码后在手机上确认登录</p>
        </div>
        <div class="qr-countdown">
          <span>二维码有效期：{{ countdown }}秒</span>
        </div>
      </div>

      <!-- 过期状态 -->
      <div v-else-if="qrCodeStatus === 'expired'" class="qr-expired">
        <i class="el-icon-warning-outline"></i>
        <p>二维码已过期</p>
        <el-button type="primary" size="small" @click="refreshQrCode">重新生成</el-button>
      </div>

      <!-- 已扫描待确认状态 -->
      <div v-else-if="qrCodeStatus === 'scanned'" class="qr-scanned">
        <i class="el-icon-mobile-phone"></i>
        <p>扫描成功</p>
        <p>请在手机上确认登录</p>
      </div>

      <!-- 登录成功状态 -->
      <div v-else-if="qrCodeStatus === 'success'" class="qr-success">
        <i class="el-icon-circle-check"></i>
        <p>登录成功</p>
        <p>正在跳转...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="qrCodeStatus === 'error'" class="qr-error">
        <i class="el-icon-circle-close"></i>
        <p>登录失败</p>
        <p>{{ errorMessage }}</p>
        <el-button type="primary" size="small" @click="refreshQrCode">重新尝试</el-button>
      </div>
    </div>

    <!-- 刷新提示 -->
    <div class="refresh-hint">
      <el-button type="text" @click="refreshQrCode" :loading="loading" class="refresh-btn">
        <i class="el-icon-refresh-right"></i> 刷新二维码
      </el-button>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcode'
import { generateQrCode, getQrCodeStatus, cancelQrCode } from '@/api/qrcode'
import { setToken } from '@/utils/auth'

export default {
  name: 'QrCodeLogin',
  data() {
    return {
      qrCodeUuid: '',
      qrCodeImage: '',
      qrCodeStatus: 'pending', // pending, expired, scanned, success, error
      countdown: 300, // 5分钟倒计时
      timer: null,
      pollingTimer: null,
      loading: false,
      errorMessage: ''
    }
  },
  mounted() {
    this.generateQrCode()
  },
  beforeDestroy() {
    this.clearTimers()
    if (this.qrCodeUuid) {
      cancelQrCode(this.qrCodeUuid).catch(() => {})
    }
  },
  methods: {
    // 生成二维码
    async generateQrCode() {
      try {
        this.loading = true
        this.qrCodeStatus = 'pending'
        
        const response = await generateQrCode()
        this.qrCodeUuid = response.data.qrUuid
        
        // 生成二维码图片
        await this.createQrCodeImage()
        
        // 开始倒计时
        this.startCountdown()
        
        // 开始轮询状态
        this.startPolling()
        
      } catch (error) {
        this.qrCodeStatus = 'error'
        this.errorMessage = '生成二维码失败'
        console.error('生成二维码失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 创建二维码图片
    async createQrCodeImage() {
      try {
        // 二维码内容包含UUID和其他必要信息
        const qrContent = JSON.stringify({
          type: 'login',
          uuid: this.qrCodeUuid,
          timestamp: Date.now()
        })
        
        // 生成二维码
        this.qrCodeImage = await QRCode.toDataURL(qrContent, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
      } catch (error) {
        console.error('生成二维码图片失败:', error)
        throw error
      }
    },

    // 开始倒计时
    startCountdown() {
      this.countdown = 300
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.qrCodeStatus = 'expired'
          this.clearTimers()
        }
      }, 1000)
    },

    // 开始轮询状态
    startPolling() {
      this.pollingTimer = setInterval(async () => {
        try {
          const response = await getQrCodeStatus(this.qrCodeUuid)
          const status = response.data.status
          
          if (status === 'expired') {
            this.qrCodeStatus = 'expired'
            this.clearTimers()
          } else if (status === 'confirmed') {
            this.qrCodeStatus = 'scanned'
          } else if (status === 'success') {
            this.qrCodeStatus = 'success'
            this.clearTimers()
            
            // 设置token并跳转
            if (response.data.token) {
              setToken(response.data.token)
              this.$emit('login-success')
              setTimeout(() => {
                this.$router.push('/')
              }, 1000)
            }
          }
        } catch (error) {
          console.error('轮询状态失败:', error)
        }
      }, 2000) // 每2秒轮询一次
    },

    // 清理定时器
    clearTimers() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer)
        this.pollingTimer = null
      }
    },

    // 刷新二维码
    refreshQrCode() {
      this.clearTimers()
      if (this.qrCodeUuid) {
        cancelQrCode(this.qrCodeUuid).catch(() => {})
      }
      this.generateQrCode()
    }
  }
}
</script>

<style scoped>
.qr-login-container {
  text-align: center;
  width: 100%;
}

.qr-code-box {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 15px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.qr-code-box:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.qr-image-container {
  position: relative;
  width: 180px;
  height: 180px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.qr-image-container:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.qr-image {
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.qr-loading {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qr-tips {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.qr-tips p {
  margin: 4px 0;
}

.qr-countdown {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.qr-expired, .qr-scanned, .qr-success, .qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.qr-expired i, .qr-error i {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 15px;
}

.qr-scanned i {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
}

.qr-success i {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 15px;
}

.qr-expired p, .qr-scanned p, .qr-success p, .qr-error p {
  margin: 5px 0;
  color: rgba(255, 255, 255, 0.8);
}

.refresh-hint {
  text-align: center;
}

.refresh-btn {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  color: var(--current-color);
}

.refresh-btn i {
  margin-right: 4px;
}

/* 主题适配 */
.theme-dark .qr-code-box {
  background: var(--base-menu-background, rgba(255, 255, 255, 0.05));
  border-color: var(--theme-color, rgba(255, 255, 255, 0.1));
}

.theme-dark .qr-tips, 
.theme-dark .qr-countdown, 
.theme-dark .qr-expired p, 
.theme-dark .qr-scanned p, 
.theme-dark .qr-success p, 
.theme-dark .qr-error p {
  color: var(--theme-color, rgba(255, 255, 255, 0.8));
}

.theme-dark .qr-loading {
  color: var(--theme-color, rgba(255, 255, 255, 0.7));
}

.theme-dark .refresh-btn {
  color: var(--theme-color, rgba(255, 255, 255, 0.7));
}

.theme-dark .refresh-btn:hover {
  color: var(--current-color, #409eff);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qr-image-container {
    width: 160px;
    height: 160px;
  }
  
  .qr-code-box {
    padding: 20px;
    min-height: 280px;
  }
}
</style> 
<template>
  <el-dialog
    title=""
    :visible.sync="visible"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :show-close="true"
    :modal="false"
    width="720px"
    center
    class="notice-popup"
    custom-class="beautiful-notice-dialog"
    @close="handleDialogClose"
  >
    <!-- 自定义头部 -->
    <div class="custom-header">
      <div class="header-bg-pattern"></div>
      <div class="header-content">
        <div class="header-icon-wrapper">
          <div class="icon-glow"></div>
          <svg-icon icon-class="message" class="header-icon" />
        </div>
        <div class="header-title">
          <h2>重要通知</h2>
          <p>请仔细阅读以下内容</p>
        </div>
        <div class="header-decoration">
          <div class="deco-dot"></div>
          <div class="deco-dot"></div>
          <div class="deco-dot"></div>
        </div>
      </div>
    </div>

    <div class="notice-content">
      <div class="notice-header">
        <div class="title-wrapper">
          <div class="title-icon">
            <svg-icon icon-class="message" />
          </div>
          <h3>{{ currentNotice.noticeTitle || '未知标题' }}</h3>
        </div>
        <div class="notice-meta">
          <div class="meta-tags">
            <span class="notice-type">
              <i class="el-icon-bell"></i>
              {{ getNoticeTypeText(currentNotice.noticeType) }}
            </span>
            <span class="notice-time">
              <i class="el-icon-time"></i>
              {{ currentNotice.releaseTime || currentNotice.createTime || '未知时间' }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="notice-cover" v-if="getCoverUrl(currentNotice)">
        <img :src="getCoverUrl(currentNotice)" alt="封面图片" />
      </div>
      
      <div class="notice-header-text" v-if="currentNotice.header">
        <div class="header-text-icon">
          <svg-icon icon-class="star" />
        </div>
        <div class="header-text-content">
          {{ currentNotice.header }}
        </div>
        <div class="header-text-glow"></div>
      </div>
      
      <div class="notice-body" v-html="currentNotice.noticeContent || '暂无内容'"></div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <div class="footer-bg"></div>
      <div class="footer-content">
        <div class="notice-footer-info">
          <div class="progress-info">
            <div class="progress-header">
              <i class="el-icon-document"></i>
              <span class="progress-text">通知进度：{{ currentIndex + 1 }} / {{ notices.length }}</span>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill" :style="{width: ((currentIndex + 1) / notices.length * 100) + '%'}">
                  <div class="progress-shine"></div>
                </div>
              </div>
              <div class="progress-percentage">{{ Math.round(((currentIndex + 1) / notices.length) * 100) }}%</div>
            </div>
          </div>
        </div>
        <div class="notice-buttons">
          <el-button v-if="currentIndex > 0" class="btn-prev" icon="el-icon-arrow-left" @click="prevNotice">
            <span>上一条</span>
          </el-button>
          <el-button v-if="currentIndex < notices.length - 1" type="primary" class="btn-next" icon="el-icon-arrow-right" @click="nextNotice">
            <span>下一条</span>
          </el-button>
          <el-button v-if="currentIndex === notices.length - 1" type="success" class="btn-confirm" icon="el-icon-check" @click="confirmRead">
            <span>我已知晓</span>
            <div class="btn-glow"></div>
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { markNoticeAsRead } from "@/api/system/notice";

export default {
  name: "NoticePopup",
  props: {
    notices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      currentIndex: 0,
      readNotices: new Set() // 记录已阅读的通知ID
    };
  },
  computed: {
    currentNotice() {
      const notice = this.notices[this.currentIndex] || {};
      console.log('当前显示的通知:', notice);
      console.log('当前索引:', this.currentIndex);
      console.log('通知数组长度:', this.notices.length);
      return notice;
    }
  },
  watch: {
    notices: {
      handler(newNotices) {
        console.log('NoticePopup接收到通知数据:', newNotices);
        console.log('当前通知详细信息:', JSON.stringify(newNotices, null, 2));
        if (newNotices && newNotices.length > 0) {
          console.log('显示通知弹窗，通知数量:', newNotices.length);
          console.log('第一条通知数据:', newNotices[0]);
          this.visible = true;
          this.currentIndex = 0;
          this.readNotices.clear();
        } else {
          console.log('没有通知数据，隐藏弹窗');
          this.visible = false;
        }
      },
      immediate: true
    }
  },
  methods: {
    getNoticeTypeText(type) {
      const typeMap = {
        '1': '通知',
        '2': '公告'
      };
      return typeMap[type] || '通知';
    },
    
    getCoverUrl(notice) {
      // 兼容不同的封面数据结构
      if (notice.cover && notice.cover.url) {
        return notice.cover.url;
      }
      if (notice.coverUrl) {
        return notice.coverUrl;
      }
      if (notice.cover && typeof notice.cover === 'string') {
        return notice.cover;
      }
      return null;
    },
    
    async prevNotice() {
      await this.markCurrentAsRead();
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },
    
    async nextNotice() {
      await this.markCurrentAsRead();
      if (this.currentIndex < this.notices.length - 1) {
        this.currentIndex++;
      }
    },
    
    async confirmRead() {
      await this.markCurrentAsRead();
      this.visible = false;
      this.$emit('all-read');
    },
    
    handleDialogClose() {
      this.visible = false;
      this.$emit('all-read');
    },
    
    async markCurrentAsRead() {
      const notice = this.currentNotice;
      console.log('准备标记通知已读:', notice);
      console.log('通知ID:', notice.noticeId);
      
      if (notice.noticeId && !this.readNotices.has(notice.noticeId)) {
        try {
          console.log('调用标记已读API，ID:', notice.noticeId);
          await markNoticeAsRead(notice.noticeId);
          this.readNotices.add(notice.noticeId);
          console.log('标记已读成功');
        } catch (error) {
          console.error('标记通知已读失败:', error);
        }
      } else {
        console.log('通知已经被标记为已读或缺少noticeId');
      }
    }
  }
};
</script>

<style scoped>
.notice-popup {
  z-index: 9999;
}

/* 弹窗主体样式 */
.notice-popup >>> .el-dialog {
  background: #ffffff;
  border-radius: 24px;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 12px 35px rgba(0, 0, 0, 0.1),
    0 4px 15px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  animation: popIn 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  backdrop-filter: blur(12px);
  position: relative;
}

.notice-popup >>> .el-dialog::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    rgba(64, 158, 255, 0.1), 
    rgba(255, 255, 255, 0.05),
    rgba(64, 158, 255, 0.1));
  border-radius: 26px;
  z-index: -1;
  animation: dialogBorder 4s ease-in-out infinite;
}

@keyframes dialogBorder {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

/* 深色主题适配 */
.theme-dark .notice-popup >>> .el-dialog {
  background: var(--base-item-bg);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.6),
    0 12px 35px rgba(0, 0, 0, 0.4),
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .notice-popup >>> .el-dialog::before {
  background: linear-gradient(45deg, 
    rgba(var(--theme-color-rgb), 0.15), 
    rgba(255, 255, 255, 0.05),
    rgba(var(--theme-color-rgb), 0.15));
  opacity: 0.8;
}

@keyframes popIn {
  0% {
    transform: scale(0.75) translateY(-60px) rotate(-2deg);
    opacity: 0;
    filter: blur(8px);
  }
  30% {
    transform: scale(1.08) translateY(-15px) rotate(1deg);
    opacity: 0.7;
    filter: blur(2px);
  }
  70% {
    transform: scale(0.98) translateY(5px) rotate(-0.5deg);
    opacity: 0.95;
    filter: blur(1px);
  }
  100% {
    transform: scale(1) translateY(0) rotate(0deg);
    opacity: 1;
    filter: blur(0px);
  }
}

.notice-popup >>> .el-dialog__header {
  display: none;
}

.notice-popup >>> .el-dialog__body {
  padding: 0;
}

.notice-popup >>> .el-dialog__footer {
  padding: 0;
}

/* 自定义头部样式 */
.custom-header {
  background: linear-gradient(135deg, 
    #409eff 0%, 
    #79bbff 25%,
    #a0cfff 50%, 
    #79bbff 75%,
    #409eff 100%);
  color: #fff;
  padding: 0;
  position: relative;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 深色主题适配 */
.theme-dark .custom-header {
  background: linear-gradient(135deg, 
    var(--theme-color) 0%, 
    var(--el-color-primary-light-3) 25%,
    var(--el-color-primary-light-5) 50%,
    var(--el-color-primary-light-3) 75%,
    var(--theme-color) 100%);
  box-shadow: 0 4px 20px rgba(var(--theme-color-rgb), 0.3);
}

.header-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 15% 25%, rgba(255,255,255,0.2) 0%, transparent 45%),
    radial-gradient(circle at 85% 75%, rgba(255,255,255,0.15) 0%, transparent 45%),
    radial-gradient(circle at 50% 50%, rgba(255,255,255,0.08) 0%, transparent 60%),
    linear-gradient(45deg, transparent 35%, rgba(255,255,255,0.1) 50%, transparent 65%);
  animation: patternFlow 6s ease-in-out infinite;
}

@keyframes patternFlow {
  0%, 100% { transform: translateX(0) rotate(0deg); }
  50% { transform: translateX(10px) rotate(2deg); }
}

.header-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 25px 20px;
  width: 100%;
}

.header-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
}

.header-icon {
  font-size: 48px;
  opacity: 0.95;
  animation: iconFloat 3s ease-in-out infinite;
  filter: drop-shadow(0 4px 12px rgba(0,0,0,0.25));
  transform-origin: center;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(5deg); }
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
  to { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0,0,0,0.2);
  letter-spacing: 0.5px;
}

.header-title p {
  margin: 0;
  font-size: 15px;
  opacity: 0.9;
  font-weight: 400;
  text-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.header-decoration {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
}

.deco-dot {
  width: 8px;
  height: 8px;
  background: rgba(255,255,255,0.6);
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.deco-dot:nth-child(2) { animation-delay: 0.2s; }
.deco-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes dotPulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.3); opacity: 1; }
}

.notice-content {
  max-height: 500px;
  overflow-y: auto;
  padding: 0;
  background: #ffffff;
}

/* 深色主题适配 */
.theme-dark .notice-content {
  background: var(--base-item-bg);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.notice-header {
  padding: 28px 35px 25px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(to bottom, 
    rgba(64, 158, 255, 0.06), 
    rgba(64, 158, 255, 0.03),
    rgba(64, 158, 255, 0.01),
    transparent);
  position: relative;
}

/* 深色主题适配 */
.theme-dark .notice-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(to bottom, 
    rgba(var(--theme-color-rgb), 0.08), 
    rgba(var(--theme-color-rgb), 0.04),
    rgba(var(--theme-color-rgb), 0.02),
    transparent);
}

.notice-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--theme-color), 
    var(--el-color-primary-light-3), 
    var(--theme-color));
  animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.title-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.title-icon {
  width: 38px;
  height: 38px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff, #79bbff);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.35);
  animation: titleIconBounce 2.5s ease-in-out infinite;
}

/* 深色主题适配 */
.theme-dark .title-icon {
  background: linear-gradient(135deg, var(--theme-color), var(--el-color-primary-light-3));
  box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.35);
}

@keyframes titleIconBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.title-icon svg {
  font-size: 18px;
  color: #fff;
}

.notice-header h3 {
  margin: 0;
  color: #303133;
  font-size: 22px;
  font-weight: 700;
  line-height: 1.4;
  flex: 1;
}

/* 深色主题适配 */
.theme-dark .notice-header h3 {
  color: var(--base-color-1);
}

.notice-meta {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.meta-tags {
  display: flex;
  gap: 15px;
  align-items: center;
}

.notice-type,
.notice-time {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.notice-type {
  background: linear-gradient(135deg, var(--theme-color), var(--el-color-primary-light-3));
  color: #fff;
  box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.4);
}

.notice-time {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.2);
}

/* 深色主题适配 */
.theme-dark .notice-time {
  background: rgba(var(--base-color-2-rgb), 0.1);
  color: var(--base-color-2);
  border: 1px solid rgba(var(--base-color-2-rgb), 0.2);
}

.notice-cover {
  padding: 20px 30px 0;
  text-align: center;
  position: relative;
}

.notice-cover img {
  max-width: 100%;
  max-height: 220px;
  border-radius: 16px;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 3px solid rgba(255, 255, 255, 0.8);
}

.notice-cover img:hover {
  transform: scale(1.02);
  box-shadow: 
    0 12px 35px rgba(0, 0, 0, 0.2),
    0 6px 20px rgba(0, 0, 0, 0.15);
}

.notice-header-text {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 30px;
  background: linear-gradient(135deg, 
    rgba(64, 158, 255, 0.12), 
    rgba(64, 158, 255, 0.06),
    rgba(64, 158, 255, 0.08));
  margin: 25px 35px;
  border-radius: 16px;
  border-left: 5px solid #409eff;
  color: #303133;
  line-height: 1.6;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

/* 深色主题适配 */
.theme-dark .notice-header-text {
  background: linear-gradient(135deg, 
    rgba(var(--theme-color-rgb), 0.15), 
    rgba(var(--theme-color-rgb), 0.08),
    rgba(var(--theme-color-rgb), 0.12));
  border-left: 5px solid var(--theme-color);
  color: var(--base-color-1);
  box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.25);
  border: 1px solid rgba(var(--theme-color-rgb), 0.2);
}

.header-text-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff, #79bbff);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  animation: headerTextIconFloat 3s ease-in-out infinite;
}

/* 深色主题适配 */
.theme-dark .header-text-icon {
  background: linear-gradient(135deg, var(--theme-color), var(--el-color-primary-light-3));
  box-shadow: 0 4px 12px rgba(var(--theme-color-rgb), 0.4);
}

@keyframes headerTextIconFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(3deg); }
}

.header-text-icon svg {
  font-size: 20px;
  color: #fff;
}

.header-text-content {
  flex: 1;
  font-size: 16px;
  line-height: 1.6;
}

.header-text-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgba(255,255,255,0.08) 50%, 
    transparent 70%);
  animation: headerTextGlow 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes headerTextGlow {
  0%, 100% { transform: translateX(-100%); opacity: 0; }
  50% { transform: translateX(100%); opacity: 1; }
}

.notice-body {
  padding: 25px 35px 30px;
  color: #606266;
  line-height: 1.8;
  font-size: 15px;
}

/* 深色主题适配 */
.theme-dark .notice-body {
  color: var(--base-color-1);
}

.notice-body >>> img {
  max-width: 100%;
  height: auto;
}

.dialog-footer {
  background: linear-gradient(to bottom, 
    rgba(250, 251, 252, 0.98), 
    rgba(248, 249, 252, 0.9),
    rgba(245, 247, 250, 0.85));
  border-top: 1px solid #ebeef5;
  backdrop-filter: blur(12px);
  position: relative;
}

/* 深色主题适配 */
.theme-dark .dialog-footer {
  background: linear-gradient(to bottom, 
    rgba(var(--base-menu-background-rgb), 0.98), 
    rgba(var(--base-menu-background-rgb), 0.95),
    rgba(var(--base-menu-background-rgb), 0.9));
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

.footer-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 40%, 
    rgba(var(--theme-color-rgb), 0.02) 50%, 
    transparent 60%);
  animation: footerFlow 6s ease-in-out infinite;
}

@keyframes footerFlow {
  0%, 100% { transform: translateX(-20px); opacity: 0.5; }
  50% { transform: translateX(20px); opacity: 1; }
}

.footer-content {
  position: relative;
  z-index: 2;
  padding: 25px 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-info {
  flex: 1;
  margin-right: 30px;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.progress-header i {
  color: var(--theme-color);
  font-size: 16px;
}

.progress-text {
  color: #303133;
  font-size: 15px;
  font-weight: 600;
}

/* 深色主题适配 */
.theme-dark .progress-text {
  color: var(--base-color-1);
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 10px;
  background: rgba(220, 223, 230, 0.7);
  border-radius: 10px;
  overflow: hidden;
  max-width: 260px;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 深色主题适配 */
.theme-dark .progress-bar {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    #409eff 0%, 
    #79bbff 30%,
    #a0cfff 60%,
    #67c23a 100%);
  border-radius: 10px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 深色主题适配 */
.theme-dark .progress-fill {
  background: linear-gradient(90deg, 
    var(--theme-color) 0%, 
    var(--el-color-primary-light-3) 30%,
    var(--el-color-primary-light-5) 60%,
    var(--el-color-success) 100%);
  box-shadow: 0 2px 8px rgba(var(--theme-color-rgb), 0.4);
}

.progress-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255,255,255,0.4) 50%, 
    transparent 100%);
  animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-percentage {
  color: #909399;
  font-size: 13px;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 深色主题适配 */
.theme-dark .progress-percentage {
  color: var(--base-color-2);
}

.notice-buttons {
  display: flex;
  gap: 15px;
}

.notice-buttons .el-button {
  position: relative;
  overflow: hidden;
  font-weight: 600;
  border-radius: 14px;
  padding: 14px 28px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  letter-spacing: 0.5px;
}

.btn-prev {
  background: rgba(144, 147, 153, 0.1);
  border-color: rgba(144, 147, 153, 0.3);
  color: #909399;
}

.btn-prev:hover {
  background: rgba(144, 147, 153, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(144, 147, 153, 0.2);
}

/* 深色主题适配 */
.theme-dark .btn-prev {
  background: rgba(var(--base-color-2-rgb), 0.1);
  border-color: rgba(var(--base-color-2-rgb), 0.3);
  color: var(--base-color-2);
}

.theme-dark .btn-prev:hover {
  background: rgba(var(--base-color-2-rgb), 0.2);
  box-shadow: 0 8px 20px rgba(var(--base-color-2-rgb), 0.2);
}

.btn-next {
  background: linear-gradient(135deg, #409eff, #79bbff);
  border: none;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.btn-next:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
}

/* 深色主题适配 */
.theme-dark .btn-next {
  background: linear-gradient(135deg, var(--theme-color), var(--el-color-primary-light-3));
  box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.3);
}

.theme-dark .btn-next:hover {
  box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.4);
}

.btn-confirm {
  background: linear-gradient(135deg, #67c23a, #85ce61, #95d475);
  border: none;
  box-shadow: 0 4px 15px rgba(103, 194, 58, 0.35);
  position: relative;
  animation: confirmPulse 3s ease-in-out infinite;
}

@keyframes confirmPulse {
  0%, 100% { box-shadow: 0 4px 15px rgba(103, 194, 58, 0.35); }
  50% { box-shadow: 0 6px 20px rgba(103, 194, 58, 0.45); }
}

.btn-confirm:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.5);
  animation: none;
}

/* 深色主题适配 */
.theme-dark .btn-confirm {
  background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-light-3));
  box-shadow: 0 4px 15px rgba(var(--el-color-success-rgb), 0.3);
}

.theme-dark .btn-confirm:hover {
  box-shadow: 0 8px 25px rgba(var(--el-color-success-rgb), 0.4);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 25%, 
    rgba(255,255,255,0.4) 50%, 
    transparent 75%);
  animation: btnGlow 2.5s ease-in-out infinite;
  pointer-events: none;
  border-radius: 14px;
}

@keyframes btnGlow {
  0%, 100% { transform: translateX(-120%) skewX(-15deg); opacity: 0; }
  40% { transform: translateX(-20%) skewX(-5deg); opacity: 0.6; }
  60% { transform: translateX(20%) skewX(5deg); opacity: 0.8; }
  100% { transform: translateX(120%) skewX(15deg); opacity: 0; }
}

/* 隐藏滚动条 */
.notice-content::-webkit-scrollbar {
  width: 0px;
  display: none;
}

.notice-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
}

/* 星空主题适配 */
.theme-starry-sky .notice-popup >>> .el-dialog {
  background: rgba(var(--base-item-bg-rgb), 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 12px 35px rgba(0, 0, 0, 0.2),
    0 4px 15px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.theme-starry-sky .notice-popup >>> .el-dialog::before {
  background: linear-gradient(45deg, 
    rgba(var(--theme-color-rgb), 0.2), 
    rgba(255, 255, 255, 0.08),
    rgba(var(--theme-color-rgb), 0.2));
  opacity: 0.9;
}
</style> 
<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUpload"
      :disabled="disable"
    >
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary" :disabled="disable">
        选取文件
        <span v-if="limit > 1" style="font-size: 11px; opacity: 0.8;">(可选多个)</span>
      </el-button>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType && fileType.length > 0">
          <span class="file-type-info">
            <span v-if="!showAllFileTypes">
              格式为 <b style="color: #f56c6c">{{ displayFileTypes }}</b>
              <el-button 
                type="text" 
                size="mini" 
                @click="showAllFileTypes = true" 
                style="color: #409eff; margin-left: 8px; padding: 0;"
                v-if="fileType.length > 8"
              >
                查看全部({{ fileType.length }}种)
              </el-button>
            </span>
            <span v-else>
              格式为 <b style="color: #f56c6c">{{ fileType.join(", ") }}</b>
              <el-button 
                type="text" 
                size="mini" 
                @click="showAllFileTypes = false" 
                style="color: #409eff; margin-left: 8px; padding: 0;"
              >
                收起
              </el-button>
            </span>
          </span>
        </template>
        的文件
        <div class="upload-limits" v-if="limit > 1" style="font-size: 12px; color: #909399; margin-top: 4px;">
          当前可上传 <b style="color: #409eff;">{{ limit }}</b> 个文件，
          已选择 <b style="color: #67c23a;">{{ fileList.length }}</b> 个
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.url" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!disable">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listByIds, delOss } from "@/api/system/oss";

export default {
  name: "FileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 20,  // 修改默认值为20个文件
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [
        // 文档类型
        "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "rtf", "odt", "ods", "odp",
        // 图片类型
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "tiff", "ico",
        // 视频类型
        "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "3gp", "m4v",
        // 音频类型
        "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a",
        // 压缩文件
        "zip", "rar", "7z", "tar", "gz", "bz2", "xz",
        // 代码文件
        "js", "css", "html", "htm", "json", "xml", "java", "py", "php", "cpp", "c", "h", "vue", "jsx", "ts",
        // CAD和设计文件
        "dwg", "dxf", "step", "stp", "iges", "igs", "stl", "obj", "fbx",
        // 应用程序文件
        "apk", "exe", "msi", "dmg", "deb", "rpm", "pkg", "ipa",
        // 其他常用格式
        "csv", "log", "ini", "cfg", "conf", "md", "yaml", "yml"
      ],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/system/oss/upload", // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // 文件类型显示控制
      showAllFileTypes: false
    };
  },
  watch: {
    value: {
      async handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          let list;
          if (Array.isArray(val)) {
            list = val;
          } else {
            await listByIds(val).then(res => {
              list = res.data.map(oss => {
                oss = { name: oss.originalName, url: oss.url, ossId: oss.ossId };
                return oss;
              });
            })
          }
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            item = { name: item.name, url: item.url, ossId: item.ossId };
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
    // 显示的文件类型（限制数量）
    displayFileTypes() {
      if (!this.fileType || this.fileType.length === 0) {
        return '';
      }
      
      // 如果文件类型少于等于8种，直接显示全部
      if (this.fileType.length <= 8) {
        return this.fileType.join(", ");
      }
      
      // 否则只显示前8种，并标注"等"
      const displayTypes = this.fileType.slice(0, 8);
      return displayTypes.join(", ") + " 等";
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.data.fileName, url: res.data.url, ossId: res.data.ossId });
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.fileUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 删除文件
    handleDelete(index) {
      let ossId = this.fileList[index].ossId;
      delOss(ossId);
      this.fileList.splice(index, 1);
      this.$emit("input", this.listToString(this.fileList));
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 获取文件名称
    getFileName(name) {
      // 如果是url那么取最后的名字 如果不是直接返回
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return name;
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].ossId + separator;
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    },
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
// 主题适配
.theme-dark {
  .file-upload {
    .el-upload__tip {
      color: var(--base-text-color) !important;
    }

    .file-type-info {
      .el-button--text {
        color: var(--theme-color) !important;
        
        &:hover {
          color: var(--theme-color-light) !important;
        }
      }
    }

    .upload-file-list {
      .el-upload-list__item {
        background: var(--base-card-background);
        border-color: var(--base-border-color);
        color: var(--base-text-color);
      }
    }
  }
}

.theme-starry-sky {
  .file-upload {
    .el-upload__tip {
      color: #ffffff !important;
    }

    .file-type-info {
      .el-button--text {
        color: #ffffff !important;
        text-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
        
        &:hover {
          color: #ffffff !important;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
        }
      }
    }

    .upload-file-list {
      .el-upload-list__item {
        background: rgba(24, 30, 42, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #ffffff;
        backdrop-filter: blur(10px);
      }
    }
  }
}
</style>

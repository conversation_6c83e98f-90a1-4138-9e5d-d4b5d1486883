<template>
  <div class="user-file-upload">
    <!-- 文件夹选择 -->
    <div class="folder-selector" v-if="showFolderSelector">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-cascader
            v-model="selectedFolder"
            :options="folderOptions"
            :props="folderProps"
            placeholder="选择文件夹（可选）"
            clearable
            @change="handleFolderChange"
          />
        </el-col>
        <el-col :span="8">
          <el-button
            size="mini"
            type="success"
            icon="el-icon-plus"
            @click="handleCreateFolder"
          >
            创建文件夹
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 存储策略选择 -->
    <div class="storage-selector" v-if="showStorageSelector">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-radio-group v-model="selectedStorageStrategy" @change="handleStorageStrategyChange">
            <el-radio label="private">仅私有</el-radio>
            <el-radio label="shared">仅共享</el-radio>
            <el-radio label="both">私有+共享</el-radio>
          </el-radio-group>
        </el-col>
        <el-col :span="12">
          <div class="strategy-tip">
            <span v-if="selectedStorageStrategy === 'private'" style="color: #67C23A;">
              文件将保存在私有文件夹中
            </span>
            <span v-else-if="selectedStorageStrategy === 'shared'" style="color: #E6A23C;">
              文件将保存在共享文件夹中
            </span>
            <span v-else-if="selectedStorageStrategy === 'both'" style="color: #409EFF;">
              文件将在私有和共享文件夹中各保存一份
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 文件上传 -->
    <div class="upload-section">
      <!-- 文件上传 -->
      <el-upload
        multiple
        :action="uploadFileUrl"
        :before-upload="handleBeforeUpload"
        :file-list="fileList"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleUploadSuccess"
        :show-file-list="false"
        :headers="headers"
        :data="uploadData"
        class="upload-file-uploader"
        ref="fileUpload"
        :disabled="disable"
      >
        <!-- 上传按钮 -->
        <el-button size="mini" type="primary" :disabled="disable">
          <i class="el-icon-upload el-icon--right"></i>
          选取文件
          <span v-if="limit > 1" style="font-size: 11px; opacity: 0.8;">(可选多个)</span>
        </el-button>
        
        <!-- 上传提示 -->
        <div class="el-upload__tip" slot="tip" v-if="showTip">
          <div class="tip-content">
            <span>请上传</span>
            <template v-if="fileSize">
              大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
            </template>
            <template v-if="fileType && fileType.length > 0">
              <span class="file-type-info">
                <span v-if="!showAllFileTypes">
                  格式为 <b style="color: #f56c6c">{{ displayFileTypes }}</b>
                  <el-button 
                    type="text" 
                    size="mini" 
                    @click="showAllFileTypes = true" 
                    style="color: #409eff; margin-left: 8px; padding: 0;"
                    v-if="fileType.length > 8"
                  >
                    查看全部({{ fileType.length }}种)
                  </el-button>
                </span>
                <span v-else>
                  格式为 <b style="color: #f56c6c">{{ fileType.join(", ") }}</b>
                  <el-button 
                    type="text" 
                    size="mini" 
                    @click="showAllFileTypes = false" 
                    style="color: #409eff; margin-left: 8px; padding: 0;"
                  >
                    收起
                  </el-button>
                </span>
              </span>
            </template>
            的文件
          </div>
          <div class="upload-limits" v-if="limit > 1">
            <span style="font-size: 12px; color: #909399;">
              当前可上传 <b style="color: #409eff;">{{ limit }}</b> 个文件，
              已选择 <b style="color: #67c23a;">{{ fileList.length }}</b> 个
            </span>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 文件列表 -->
    <div v-if="showFileList && fileList.length > 0" class="file-list-container">
      <!-- 批量操作栏 -->
      <div class="batch-actions" v-if="fileList.length > 1">
        <el-checkbox 
          v-model="selectAll" 
          @change="handleSelectAll"
          :indeterminate="isIndeterminate"
        >
          全选
        </el-checkbox>
        <div class="batch-buttons" v-if="selectedItems.length > 0">
          <el-button 
            size="mini" 
            type="warning" 
            @click="handleBatchMoveToShared"
            v-if="storageStrategy === 'private'"
          >
            批量分享 ({{ selectedItems.length }})
          </el-button>
          <el-button 
            size="mini" 
            type="danger" 
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedItems.length }})
          </el-button>
        </div>
      </div>

      <!-- 文件列表 -->
      <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
        <li :key="file.url" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
          <div class="file-checkbox" v-if="fileList.length > 1">
            <el-checkbox 
              v-model="selectedItems" 
              :label="index"
              @change="handleItemSelect"
            ></el-checkbox>
          </div>
          <div class="file-info">
            <el-link :href="`${file.url}`" :underline="false" target="_blank">
              <i :class="getFileIcon(file.name)"></i>
              <span class="file-name">{{ getFileName(file.name) }}</span>
            </el-link>
            <div class="file-meta">
              <span class="file-type" v-if="file.fileType">{{ getFileTypeText(file.fileType) }}</span>
              <span class="folder-info" v-if="file.folderName">{{ file.folderName }}</span>
            </div>
          </div>
          <div class="ele-upload-list__item-content-action">
            <el-link :underline="false" @click="handlePreview(file)" type="primary" style="margin-right: 10px;">
              预览
            </el-link>
            <el-link :underline="false" @click="handleMoveToShared(file, index)" type="warning" style="margin-right: 10px;" v-if="!disable && storageStrategy === 'private'">
              分享
            </el-link>
            <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!disable">
              删除
            </el-link>
          </div>
        </li>
      </transition-group>
    </div>

    <!-- 创建文件夹对话框 -->
    <el-dialog
      title="创建文件夹"
      :visible.sync="createFolderDialog"
      width="500px"
      append-to-body
    >
      <el-form :model="folderForm" :rules="folderRules" ref="folderForm" label-width="100px">
        <el-form-item label="文件夹名称" prop="folderName">
          <el-input v-model="folderForm.folderName" placeholder="请输入文件夹名称" />
        </el-form-item>
        <el-form-item label="父文件夹" prop="parentId">
          <el-cascader
            v-model="folderForm.parentId"
            :options="folderOptions"
            :props="folderProps"
            placeholder="选择父文件夹（留空为根目录）"
            clearable
          />
        </el-form-item>
        <el-form-item label="存储策略" prop="storageStrategy">
          <el-radio-group v-model="folderForm.storageStrategy" :disabled="storageStrategy === 'shared'">
            <el-radio label="private">私有</el-radio>
            <el-radio label="shared">共享</el-radio>
          </el-radio-group>
          <div v-if="storageStrategy === 'shared'" class="el-form-item__content">
            <span style="color: #909399; font-size: 12px;">当前在共享空间中，创建的文件夹将自动设为共享</span>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="folderForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createFolderDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleCreateFolderConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewDialog"
      width="80%"
      append-to-body
    >
      <div class="file-preview">
        <!-- 图片预览 -->
        <img v-if="isImage(previewFile.name)" :src="previewFile.url" style="max-width: 100%; max-height: 500px;" />
        
        <!-- PDF预览 -->
        <iframe v-else-if="isPdf(previewFile.name)" :src="previewFile.url" style="width: 100%; height: 500px; border: none;"></iframe>
        
        <!-- 视频预览 -->
        <video v-else-if="isVideo(previewFile.name)" controls style="max-width: 100%; max-height: 500px;">
          <source :src="previewFile.url" />
          您的浏览器不支持视频播放
        </video>
        
        <!-- 音频预览 -->
        <audio v-else-if="isAudio(previewFile.name)" controls style="width: 100%;">
          <source :src="previewFile.url" />
          您的浏览器不支持音频播放
        </audio>
        
        <!-- 文本文件预览 -->
        <div v-else-if="isText(previewFile.name)" class="text-preview">
          <div class="text-content" ref="textContent" style="max-height: 500px; overflow-y: auto; text-align: left; padding: 20px; background: #f5f5f5; border-radius: 4px;">
            <el-button type="primary" size="mini" @click="loadTextContent(previewFile)" :loading="textLoading">
              {{ textLoading ? '加载中...' : '点击预览文本内容' }}
            </el-button>
          </div>
        </div>
        
        <!-- 不支持预览的文件类型 -->
        <div v-else class="no-preview">
          <i :class="getFileIcon(previewFile.name)" style="font-size: 48px; color: #ccc;"></i>
          <p>此文件类型不支持预览</p>
          <p class="file-info">
            <span>文件类型: {{ getFileExtension(previewFile.name).toUpperCase() }}</span>
            <br>
            <span>文件名称: {{ previewFile.name }}</span>
          </p>
          <el-button type="primary" @click="downloadFile(previewFile)">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listByIds, delOss, moveToShared, copyToShared } from "@/api/system/oss";
import { addOssFolder, getPrivateFolderTree, getSharedFolderTree } from "@/api/system/ossFolder";

export default {
  name: "UserFileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 20,  // 修改默认值为20个文件
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [
        // 文档类型
        "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "rtf", "odt", "ods", "odp",
        // 图片类型
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "tiff", "ico",
        // 视频类型
        "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "3gp", "m4v",
        // 音频类型
        "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a",
        // 压缩文件
        "zip", "rar", "7z", "tar", "gz", "bz2", "xz",
        // 代码文件
        "js", "css", "html", "htm", "json", "xml", "java", "py", "php", "cpp", "c", "h", "vue", "jsx", "ts",
        // CAD和设计文件
        "dwg", "dxf", "step", "stp", "iges", "igs", "stl", "obj", "fbx",
        // 应用程序文件
        "apk", "exe", "msi", "dmg", "deb", "rpm", "pkg", "ipa",
        // 其他常用格式
        "csv", "log", "ini", "cfg", "conf", "md", "yaml", "yml"
      ],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disable: {
      type: Boolean,
      default: false
    },
    // 存储策略
    storageStrategy: {
      type: String,
      default: 'private'
    },
    // 是否显示文件夹选择器
    showFolderSelector: {
      type: Boolean,
      default: true
    },
    // 默认文件夹ID（支持大整数字符串）
    defaultFolderId: {
      type: [Number, String],
      default: 0
    },
    // 是否显示存储策略选择器
    showStorageSelector: {
      type: Boolean,
      default: false
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },

  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/system/oss/uploadToFolder",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // 文件夹相关
      folderOptions: [],
      selectedFolder: [],
      currentFolderId: 0,
      currentFolderPath: '',
      // 选择的存储策略
      selectedStorageStrategy: 'private',
      folderProps: {
        value: 'folderId',
        label: 'folderName',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      // 创建文件夹
      createFolderDialog: false,
      folderForm: {
        folderName: '',
        parentId: 0,
        storageStrategy: 'private',
        remark: ''
      },
      folderRules: {
        folderName: [
          { required: true, message: "文件夹名称不能为空", trigger: "blur" },
          { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" }
        ]
      },
      // 文件预览
      previewDialog: false,
      previewFile: {},
      textLoading: false,
      // 文件类型显示控制
      showAllFileTypes: false,
      // 批量操作相关
      selectedItems: [],
      selectAll: false,

    };
  },
  watch: {
    value: {
      async handler(val) {
        if (val) {
          // 检查是否为特殊状态字符串（如 "success", "error"）
          if (typeof val === 'string' && (val === 'success' || val === 'error')) {
            console.log('收到状态信息:', val);
            // 对于状态字符串，不处理文件列表
            return;
          }
          
          let temp = 1;
          let list;
          if (Array.isArray(val)) {
            list = val;
          } else if (typeof val === 'string' && val.length > 0 && val !== 'success' && val !== 'error') {
            // 只有当val是有效的ID字符串时才调用API
            try {
              await listByIds(val).then(res => {
                list = res.data.map(oss => {
                  oss = { 
                    name: oss.originalName, 
                    url: oss.url, 
                    ossId: oss.ossId,
                    fileType: oss.fileType,
                    folderId: oss.folderId,
                    folderName: oss.folderName
                  };
                  return oss;
                });
              });
            } catch (error) {
              console.error('获取文件信息失败:', error);
              this.fileList = [];
              return;
            }
          } else {
            // 其他情况清空文件列表
            this.fileList = [];
            return;
          }
          
          if (list && list.length > 0) {
            this.fileList = list.map(item => {
              item = { 
                name: item.name, 
                url: item.url, 
                ossId: item.ossId,
                fileType: item.fileType,
                folderId: item.folderId,
                folderName: item.folderName
              };
              item.uid = item.uid || new Date().getTime() + temp++;
              return item;
            });
          } else {
            this.fileList = [];
          }
        } else {
          this.fileList = [];
        }
      },
      deep: true,
      immediate: true
    },
    storageStrategy: {
      handler(newVal) {
        this.folderForm.storageStrategy = newVal;
        // 如果没有显示存储策略选择器，则使用传入的存储策略
        if (!this.showStorageSelector) {
          this.selectedStorageStrategy = newVal;
        }
        this.loadFolderTree();
      },
      immediate: true
    },
    defaultFolderId: {
      handler(newVal) {
        // 处理字符串或数字类型的文件夹ID
        if (typeof newVal === 'string') {
          this.currentFolderId = newVal;
          const numVal = parseInt(newVal);
          this.selectedFolder = numVal > 0 ? [numVal] : [];
        } else {
          this.currentFolderId = newVal;
          this.selectedFolder = newVal > 0 ? [newVal] : [];
        }
        
        console.log('[UserFileUpload] defaultFolderId 变化:', {
          原始值: newVal,
          类型: typeof newVal,
          设置的currentFolderId: this.currentFolderId,
          selectedFolder: this.selectedFolder,
          showFolderSelector: this.showFolderSelector,
          showStorageSelector: this.showStorageSelector
        });
        
        // 更新文件夹路径显示
        this.$nextTick(() => {
          this.updateFolderPath();
        });
      },
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
    // 显示的文件类型（限制数量）
    displayFileTypes() {
      if (!this.fileType || this.fileType.length === 0) {
        return '';
      }
      
      // 如果文件类型少于等于8种，直接显示全部
      if (this.fileType.length <= 8) {
        return this.fileType.join(", ");
      }
      
      // 否则只显示前8种，并标注"等"
      const displayTypes = this.fileType.slice(0, 8);
      return displayTypes.join(", ") + " 等";
    },
    // 批量选择状态
    isIndeterminate() {
      const selected = this.selectedItems.length;
      const total = this.fileList.length;
      return selected > 0 && selected < total;
    },
    // 上传数据
    uploadData() {
      // 确保文件夹ID为非负数，并转换为字符串确保精度
      let actualFolderId = this.currentFolderId < 0 ? 0 : this.currentFolderId;
      
      // 处理可能的大整数精度问题 - 保持字符串形式
      if (typeof actualFolderId === 'number' && actualFolderId > Number.MAX_SAFE_INTEGER) {
        console.warn('文件夹ID超出JavaScript安全整数范围，转换为字符串:', actualFolderId);
        // 将数字转换为字符串，但由于已经丢失精度，需要从原始数据获取
        console.warn('数字已丢失精度，尝试从原始数据获取字符串ID');
        // 这里需要从父组件传入的原始ID获取
        actualFolderId = String(this.currentFolderId);
      } else if (typeof actualFolderId === 'string') {
        // 如果已经是字符串，保持字符串形式
        const numId = parseInt(actualFolderId);
        if (numId > Number.MAX_SAFE_INTEGER) {
          console.log('保持大整数文件夹ID为字符串形式:', actualFolderId);
          // 保持字符串形式，不转换
        } else {
          // 小整数可以转换为数字
          actualFolderId = numId;
        }
      }
      
      // 使用选择的存储策略，如果是both则默认为private（第一次上传）
      const actualStrategy = this.selectedStorageStrategy === 'both' ? 'private' : this.selectedStorageStrategy;
      
      console.log('[UserFileUpload] 上传数据配置:', {
        folderId: actualFolderId,
        storageStrategy: actualStrategy,
        currentFolderId: this.currentFolderId,
        selectedStorageStrategy: this.selectedStorageStrategy,
        defaultFolderId: this.defaultFolderId,
        showFolderSelector: this.showFolderSelector,
        showStorageSelector: this.showStorageSelector,
        原始文件夹ID: this.currentFolderId,
        实际文件夹ID: actualFolderId,
        文件夹ID类型: typeof actualFolderId,
        MAX_SAFE_INTEGER: Number.MAX_SAFE_INTEGER,
        是否超出安全范围: this.currentFolderId > Number.MAX_SAFE_INTEGER
      });
      
      return {
        folderId: actualFolderId,
        storageStrategy: actualStrategy,
        copyToShared: this.selectedStorageStrategy === 'both'
      };
    },
    // 存储策略样式
    storageClass() {
      return {
        'private-storage': this.selectedStorageStrategy === 'private',
        'shared-storage': this.selectedStorageStrategy === 'shared',
        'both-storage': this.selectedStorageStrategy === 'both'
      };
    },
    // 存储策略文本
    storageText() {
      const textMap = {
        'private': '私有存储',
        'shared': '共享存储',
        'both': '私有+共享存储'
      };
      return textMap[this.selectedStorageStrategy] || '私有存储';
    },


  },
  created() {
    this.loadFolderTree();
  },
  methods: {
    // 加载文件夹树（带状态保持）
    async loadFolderTree(preserveExpandedState = false) {
      try {
        // 保存当前展开状态
        let expandedKeys = [];
        let selectedKeys = [];
        if (preserveExpandedState && this.$refs.folderTree) {
          expandedKeys = this.$refs.folderTree.getExpandedKeys ? this.$refs.folderTree.getExpandedKeys() : [];
          selectedKeys = this.$refs.folderTree.getCheckedKeys ? this.$refs.folderTree.getCheckedKeys() : [];
        }
        
        let response;
        // 根据当前选择的存储策略或folderForm的存储策略决定
        const strategy = this.showStorageSelector ? this.folderForm.storageStrategy : this.storageStrategy;
        if (strategy === 'private') {
          response = await getPrivateFolderTree();
        } else {
          response = await getSharedFolderTree();
        }
        this.folderOptions = this.buildTreeOptions(response.data);
        
        // 文件夹树加载完成后，更新选择状态和路径显示
        this.$nextTick(() => {
          // 修正文件夹ID判断逻辑，支持字符串形式的大整数ID
          const folderIdValue = typeof this.currentFolderId === 'string' ? parseInt(this.currentFolderId) : this.currentFolderId;
          if (folderIdValue > 0) {
            this.selectedFolder = [folderIdValue];
          } else {
            this.selectedFolder = [];
          }
          
          console.log('[UserFileUpload] loadFolderTree 完成后设置selectedFolder:', {
            currentFolderId: this.currentFolderId,
            folderIdValue: folderIdValue,
            selectedFolder: this.selectedFolder,
            showFolderSelector: this.showFolderSelector
          });
          
          this.updateFolderPath();
          
          // 恢复展开状态
          if (preserveExpandedState && this.$refs.folderTree && expandedKeys.length > 0) {
            if (this.$refs.folderTree.setExpandedKeys) {
              this.$refs.folderTree.setExpandedKeys(expandedKeys);
            }
            if (this.$refs.folderTree.setCheckedKeys && selectedKeys.length > 0) {
              this.$refs.folderTree.setCheckedKeys(selectedKeys);
            }
          }
        });
      } catch (error) {
        console.error('加载文件夹树失败:', error);
      }
    },
    
    // 构建树形选项
    buildTreeOptions(folders) {
      const options = [];
      const addRootOption = () => {
        options.push({
          folderId: 0,
          folderName: '根目录',
          children: this.buildFolderTree(folders, 0)
        });
      };
      addRootOption();
      return options;
    },
    
    // 构建文件夹树
    buildFolderTree(folders, parentId) {
      return folders
        .filter(folder => folder.parentId === parentId)
        .map(folder => ({
          ...folder,
          children: this.buildFolderTree(folders, folder.folderId)
        }));
    },
    
    // 文件夹选择改变
    handleFolderChange(value) {
      this.currentFolderId = value || 0;
      this.updateFolderPath();
    },
    
    // 存储策略改变
    handleStorageStrategyChange(value) {
      this.selectedStorageStrategy = value;
      // 根据选择的策略重新加载文件夹树
      if (value === 'private') {
        this.folderForm.storageStrategy = 'private';
        this.loadFolderTree();
      } else if (value === 'shared') {
        this.folderForm.storageStrategy = 'shared';
        this.loadFolderTree();
      } else if (value === 'both') {
        // 对于both策略，默认显示私有文件夹树
        this.folderForm.storageStrategy = 'private';
        this.loadFolderTree();
      }
    },
    
    // 更新文件夹路径显示
    updateFolderPath() {
      if (this.currentFolderId === 0) {
        const strategyText = this.selectedStorageStrategy === 'shared' ? '共享空间根目录' : '我的文件根目录';
        this.currentFolderPath = strategyText;
        return;
      }
      
      const findPath = (folders, id, path = []) => {
        for (const folder of folders) {
          if (folder.folderId === id) {
            return [...path, folder.folderName];
          }
          if (folder.children) {
            const result = findPath(folder.children, id, [...path, folder.folderName]);
            if (result) return result;
          }
        }
        return null;
      };
      
      const allFolders = this.getAllFolders(this.folderOptions);
      const pathArray = findPath(allFolders, this.currentFolderId);
      if (pathArray && pathArray.length > 0) {
        const strategyPrefix = this.selectedStorageStrategy === 'shared' ? '共享空间' : '我的文件';
        this.currentFolderPath = `${strategyPrefix} / ${pathArray.join(' / ')}`;
      } else {
        const strategyText = this.selectedStorageStrategy === 'shared' ? '共享空间根目录' : '我的文件根目录';
        this.currentFolderPath = strategyText;
      }
    },
    
    // 获取所有文件夹（扁平化）
    getAllFolders(options) {
      let folders = [];
      for (const option of options) {
        if (option.children) {
          folders = folders.concat(this.getAllFolders(option.children));
        } else if (option.folderId !== 0) {
          folders.push(option);
        }
      }
      return folders;
    },
    
    // 创建文件夹
    handleCreateFolder() {
      // 如果在共享空间（currentFolderId为-1），parentId设为0
      const actualParentId = this.currentFolderId < 0 ? 0 : this.currentFolderId;
      this.folderForm = {
        folderName: '',
        parentId: actualParentId,
        storageStrategy: this.storageStrategy,
        remark: ''
      };
      this.createFolderDialog = true;
    },
    
    // 确认创建文件夹
    async handleCreateFolderConfirm() {
      this.$refs.folderForm.validate(async (valid) => {
        if (valid) {
          try {
            await addOssFolder(this.folderForm);
            this.$modal.msgSuccess("创建文件夹成功");
            this.createFolderDialog = false;
            // 保持文件夹树展开状态的无感刷新
            this.loadFolderTree(true);
          } catch (error) {
            this.$modal.msgError("创建文件夹失败: " + error.message);
          }
        }
      });
    },
    
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType && this.fileType.length > 0) {
        if (!file.name || typeof file.name !== 'string') {
          this.$modal.msgError("文件名无效");
          return false;
        }
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`);
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      
      // 显示加载提示（文件夹模式显示特殊信息）
      if (this.uploadMode === 'folder') {
        this.$modal.loading("正在上传文件夹中的文件，请稍候...");
      } else {
        this.$modal.loading("正在上传文件，请稍候...");
      }
      
      this.number++;
      return true;
    },
    
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    
    // 上传失败
    handleUploadError(err) {
      this.number--;
      this.$modal.msgError("上传文件失败，请重试");
      this.$modal.closeLoading();
      
      // 如果不显示文件列表，且上传失败，需要通知父组件
      if (!this.showFileList) {
        this.$emit("input", "error");
      }
    },
    
    // 上传成功回调
    async handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ 
          name: res.data.fileName, 
          url: res.data.url, 
          ossId: res.data.ossId,
          fileType: res.data.fileType,
          folderId: res.data.folderId
        });
        
        // 如果选择了"私有+共享"策略，需要复制到共享空间
        if (this.selectedStorageStrategy === 'both') {
          try {
            await copyToShared(res.data.ossId);
          } catch (error) {
            console.warn('复制到共享空间失败:', error);
          }
        }
        
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        
        // 处理特殊错误信息
        let errorMsg = res.msg || '上传失败';
        if (errorMsg.includes('文件夹不存在')) {
          errorMsg = '目标文件夹不存在，请刷新页面后重试';
          // 通知父组件刷新文件夹树
          this.$emit('folder-not-found');
        }
        
        this.$modal.msgError(errorMsg);
        this.$refs.fileUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    
    // 删除文件
    async handleDelete(index) {
      try {
        let ossId = this.fileList[index].ossId;
        await delOss(ossId);
        this.fileList.splice(index, 1);
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.msgSuccess("删除成功");
      } catch (error) {
        this.$modal.msgError("删除失败: " + error.message);
      }
    },
    
    // 移动到共享文件夹
    async handleMoveToShared(file, index) {
      try {
        this.$confirm('移动到共享文件夹后，其他用户可以访问此文件，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await moveToShared(file.ossId);
          this.fileList.splice(index, 1);
          this.$emit("input", this.listToString(this.fileList));
          this.$modal.msgSuccess("移动到共享文件夹成功");
        });
      } catch (error) {
        this.$modal.msgError("移动失败: " + error.message);
      }
    },
    
    // 预览文件
    handlePreview(file) {
      this.previewFile = file;
      this.previewDialog = true;
    },
    
    // 下载文件
    downloadFile(file) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.click();
    },
    
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        // 如果不显示文件列表，则不需要维护fileList
        if (this.showFileList) {
          this.fileList = this.fileList.concat(this.uploadList);
          this.$emit("input", this.listToString(this.fileList));
        } else {
          // 不显示文件列表时，直接触发成功回调
          // 使用nextTick确保组件状态更新完成后再发出事件
          this.$nextTick(() => {
            this.$emit("input", "success");
          });
        }
        this.uploadList = [];
        this.number = 0;
        this.$modal.closeLoading();
      }
    },
    
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1);
      } else {
        return name;
      }
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return 'el-icon-document';
      }
      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        // 文档类型
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        rtf: 'el-icon-document',
        odt: 'el-icon-document',
        txt: 'el-icon-document',
        md: 'el-icon-document',
        
        // 表格类型
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        csv: 'el-icon-s-grid',
        ods: 'el-icon-s-grid',
        
        // 演示文稿
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        odp: 'el-icon-present',
        
        // 图片类型
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        bmp: 'el-icon-picture',
        webp: 'el-icon-picture',
        svg: 'el-icon-picture',
        tiff: 'el-icon-picture',
        ico: 'el-icon-picture',
        
        // 视频类型
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mov: 'el-icon-video-camera',
        wmv: 'el-icon-video-camera',
        flv: 'el-icon-video-camera',
        webm: 'el-icon-video-camera',
        mkv: 'el-icon-video-camera',
        '3gp': 'el-icon-video-camera',
        m4v: 'el-icon-video-camera',
        
        // 音频类型
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        flac: 'el-icon-headset',
        aac: 'el-icon-headset',
        ogg: 'el-icon-headset',
        wma: 'el-icon-headset',
        m4a: 'el-icon-headset',
        
        // 压缩文件
        zip: 'el-icon-folder-opened',
        rar: 'el-icon-folder-opened',
        '7z': 'el-icon-folder-opened',
        tar: 'el-icon-folder-opened',
        gz: 'el-icon-folder-opened',
        bz2: 'el-icon-folder-opened',
        xz: 'el-icon-folder-opened',
        
        // 代码文件
        js: 'el-icon-document',
        css: 'el-icon-document',
        html: 'el-icon-document',
        htm: 'el-icon-document',
        json: 'el-icon-document',
        xml: 'el-icon-document',
        java: 'el-icon-document',
        py: 'el-icon-document',
        php: 'el-icon-document',
        cpp: 'el-icon-document',
        c: 'el-icon-document',
        h: 'el-icon-document',
        vue: 'el-icon-document',
        jsx: 'el-icon-document',
        ts: 'el-icon-document',
        
        // CAD和设计文件
        dwg: 'el-icon-edit',
        dxf: 'el-icon-edit',
        step: 'el-icon-edit',
        stp: 'el-icon-edit',
        iges: 'el-icon-edit',
        igs: 'el-icon-edit',
        stl: 'el-icon-edit',
        obj: 'el-icon-edit',
        fbx: 'el-icon-edit',
        
        // 配置文件
        ini: 'el-icon-setting',
        cfg: 'el-icon-setting',
        conf: 'el-icon-setting',
        yaml: 'el-icon-setting',
        yml: 'el-icon-setting',
        
        // 日志文件
        log: 'el-icon-warning',
        
        // 应用程序文件
        apk: 'el-icon-mobile-phone',
        exe: 'el-icon-monitor',
        msi: 'el-icon-monitor',
        dmg: 'el-icon-monitor',
        deb: 'el-icon-monitor',
        rpm: 'el-icon-monitor',
        pkg: 'el-icon-monitor',
        ipa: 'el-icon-mobile-phone'
      };
      return iconMap[ext] || 'el-icon-document';
    },
    
    // 获取文件类型文本
    getFileTypeText(fileType) {
      const typeMap = {
        image: '图片',
        document: '文档',
        video: '视频',
        audio: '音频',
        archive: '压缩包',
        code: '代码',
        cad: 'CAD设计',
        config: '配置',
        app: '应用程序',
        other: '其他'
      };
      return typeMap[fileType] || '未知';
    },
    
    // 判断是否为图片
    isImage(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'ico'].includes(ext);
    },
    
    // 判断是否为PDF
    isPdf(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ext === 'pdf';
    },
    
    // 判断是否为视频文件
    isVideo(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'm4v'].includes(ext);
    },
    
    // 判断是否为音频文件
    isAudio(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(ext);
    },
    
    // 判断是否为文本文件
    isText(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return false;
      }
      const ext = fileName.split('.').pop().toLowerCase();
      return ['txt', 'md', 'json', 'xml', 'css', 'js', 'html', 'htm', 'csv', 'log', 'ini', 'cfg', 'conf', 'yaml', 'yml'].includes(ext);
    },
    
    // 获取文件扩展名
    getFileExtension(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        return '';
      }
      return fileName.split('.').pop().toLowerCase();
    },
    
    // 加载文本内容
    async loadTextContent(file) {
      this.textLoading = true;
      try {
        const response = await fetch(file.url);
        const text = await response.text();
        
        // 限制文本长度，避免浏览器卡顿
        const maxLength = 10000;
        const displayText = text.length > maxLength 
          ? text.substring(0, maxLength) + '\n\n... (文件内容过长，仅显示前' + maxLength + '个字符)'
          : text;
        
        this.$refs.textContent.innerHTML = `<pre style="white-space: pre-wrap; font-family: monospace; font-size: 12px; line-height: 1.4;">${displayText}</pre>`;
      } catch (error) {
        this.$refs.textContent.innerHTML = '<p style="color: #f56c6c;">文本文件加载失败，请下载后查看</p>';
      } finally {
        this.textLoading = false;
      }
    },
    
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].ossId + separator;
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    },





    // 批量操作方法
    // 全选/取消全选
    handleSelectAll(value) {
      if (value) {
        this.selectedItems = this.fileList.map((_, index) => index);
      } else {
        this.selectedItems = [];
      }
    },

    // 单个选择变化
    handleItemSelect() {
      this.selectAll = this.selectedItems.length === this.fileList.length;
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$modal.msgWarning("请先选择要删除的文件");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 个文件吗？`, "批量删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        try {
          // 按索引倒序删除，避免索引变化问题
          const sortedIndexes = [...this.selectedItems].sort((a, b) => b - a);
          
          for (const index of sortedIndexes) {
            const file = this.fileList[index];
            await delOss(file.ossId);
            this.fileList.splice(index, 1);
          }
          
          this.selectedItems = [];
          this.selectAll = false;
          this.$emit("input", this.listToString(this.fileList));
          this.$modal.msgSuccess(`成功删除 ${sortedIndexes.length} 个文件`);
        } catch (error) {
          this.$modal.msgError("批量删除失败: " + error.message);
        }
      });
    },

    // 批量分享到共享空间
    handleBatchMoveToShared() {
      if (this.selectedItems.length === 0) {
        this.$modal.msgWarning("请先选择要分享的文件");
        return;
      }

      this.$confirm(`确定要将选中的 ${this.selectedItems.length} 个文件移动到共享文件夹吗？`, "批量分享", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        try {
          // 按索引倒序处理，避免索引变化问题
          const sortedIndexes = [...this.selectedItems].sort((a, b) => b - a);
          
          for (const index of sortedIndexes) {
            const file = this.fileList[index];
            await moveToShared(file.ossId);
            this.fileList.splice(index, 1);
          }
          
          this.selectedItems = [];
          this.selectAll = false;
          this.$emit("input", this.listToString(this.fileList));
          this.$modal.msgSuccess(`成功分享 ${sortedIndexes.length} 个文件到共享空间`);
        } catch (error) {
          this.$modal.msgError("批量分享失败: " + error.message);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.user-file-upload {
  .storage-selector {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;

    .strategy-tip {
      font-size: 12px;
      padding: 5px 0;
      display: flex;
      align-items: center;
      height: 32px;
    }
  }

  .folder-selector {
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
  }

  .upload-section {
    .upload-file-uploader {
      margin-bottom: 5px;
    }

    .tip-content {
      margin-bottom: 8px;
      line-height: 1.6;
    }

    .file-type-info {
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 4px;
    }

    .upload-limits {
      margin-bottom: 4px;
    }

    .storage-info {
      font-size: 12px;
      margin-bottom: 4px;
      
      .private-storage {
        color: #67c23a;
        font-weight: 500;
      }
      
      .shared-storage {
        color: #e6a23c;
        font-weight: 500;
      }
    }

    .folder-info {
      font-size: 12px;
      color: #909399;
      
      .folder-path {
        color: #409eff;
        font-weight: 500;
      }
    }

    .upload-notice {
      margin-top: 10px;
    }

    .common-tips {
      margin-top: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .storage-info,
      .folder-info {
        margin-bottom: 8px;
      }
    }
  }

  .file-list-container {
    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px;
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 10px;
      transition: all 0.3s ease;

      .batch-buttons {
        display: flex;
        gap: 8px;

        .el-button {
          font-size: 12px;
          padding: 5px 10px;
        }
      }
    }
  }

  .upload-file-list {
    .el-upload-list__item {
      border: 1px solid #e4e7ed;
      line-height: 2;
      margin-bottom: 10px;
      position: relative;
      border-radius: 6px;
      padding: 12px;
      background: #fff;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .ele-upload-list__item-content {
      display: flex;
      align-items: center;
      color: inherit;
      gap: 12px;

      .file-checkbox {
        flex-shrink: 0;
      }

      .file-info {
        flex: 1;
        
        .file-name {
          margin-left: 5px;
          font-weight: 500;
        }

        .file-meta {
          font-size: 12px;
          color: #909399;
          margin-top: 6px;

          .file-type {
            display: inline-block;
            background: #ecf5ff;
            color: #409eff;
            padding: 3px 8px;
            border-radius: 4px;
            margin-right: 8px;
            font-weight: 500;
          }

          .folder-info {
            color: #67c23a;
            font-weight: 500;
          }
        }
      }

      .ele-upload-list__item-content-action {
        flex-shrink: 0;
        
        .el-link {
          margin-right: 10px;
          font-weight: 500;
        }
      }
    }
  }

  .file-preview {
    text-align: center;

    .no-preview {
      padding: 50px;
      
      p {
        margin: 20px 0;
        color: #909399;
        
        &.file-info {
          font-size: 14px;
          line-height: 1.6;
        }
      }
    }

    .text-preview {
      .text-content {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
      }
    }
  }
}

// 深色主题适配
:deep(.theme-dark) .user-file-upload,
.theme-dark .user-file-upload {
  .storage-selector {
    background: var(--base-menu-background) !important;
    border-color: var(--base-border-color) !important;
    color: var(--base-text-color) !important;
  }

  .folder-selector {
    background: var(--base-menu-background) !important;
    border-color: var(--base-border-color) !important;
    color: var(--base-text-color) !important;
  }

  .upload-section {
    .tip-content {
      color: var(--base-text-color);
    }

    .storage-info {
      .private-storage {
        color: var(--theme-color);
      }
      
      .shared-storage {
        color: #f39c12;
      }
    }

    .folder-info {
      color: var(--base-text-color-secondary);
      
      .folder-path {
        color: var(--theme-color);
      }
    }
  }

  .upload-file-list {
    .el-upload-list__item {
      background: var(--base-card-background);
      border-color: var(--base-border-color);
      color: var(--base-text-color);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }

    .ele-upload-list__item-content {
      .file-info {
        .file-meta {
          color: var(--base-text-color-secondary);

          .file-type {
            background: var(--theme-color-light-9);
            color: var(--theme-color);
          }

          .folder-info {
            color: var(--theme-color);
          }
        }
      }
    }
  }

  .file-preview {
    .no-preview {
      p {
        color: var(--base-text-color-secondary);
        
        &.file-info {
          color: var(--base-text-color);
        }
      }
    }

    .text-preview {
      .text-content {
        background: var(--base-card-background);
        border-color: var(--base-border-color);
        color: var(--base-text-color);
      }
    }
  }



  .common-tips {
    background: var(--base-fill-color);
    
    .storage-info,
    .folder-info {
      color: var(--base-text-color-secondary);
    }
  }
}

// 星空主题适配
:deep(.theme-starry-sky) .user-file-upload,
.theme-starry-sky .user-file-upload {
  .storage-selector {
    background: rgba(26, 31, 60, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    backdrop-filter: blur(10px);
  }

  .folder-selector {
    background: rgba(26, 31, 60, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    backdrop-filter: blur(10px);
  }

  .upload-section {
    .tip-content {
      color: #ffffff;
    }

    .storage-info {
      .private-storage {
        color: #ffffff;
        text-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
      }
      
      .shared-storage {
        color: #f39c12;
        text-shadow: 0 0 6px rgba(243, 156, 18, 0.5);
      }
    }

    .folder-info {
      color: #c0c0c0;
      
      .folder-path {
        color: #ffffff;
        text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
      }
    }
  }

  .upload-file-list {
    .el-upload-list__item {
      background: rgba(24, 30, 42, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #ffffff;
      backdrop-filter: blur(10px);

      &:hover {
        box-shadow: 0 4px 16px rgba(30, 58, 138, 0.4);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    .ele-upload-list__item-content {
      .file-info {
        .file-meta {
          color: #c0c0c0;

          .file-type {
            background: rgba(30, 58, 138, 0.6);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
          }

          .folder-info {
            color: #ffffff;
            text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }

  .file-preview {
    .no-preview {
      p {
        color: #c0c0c0;
        
        &.file-info {
          color: #ffffff;
        }
      }
    }

    .text-preview {
      .text-content {
        background: rgba(26, 31, 60, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #ffffff;
        backdrop-filter: blur(10px);
      }
    }
  }



  .common-tips {
    background: rgba(26, 31, 60, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    
    .storage-info,
    .folder-info {
      color: #c0c0c0;
    }
  }
}

// 全局元素主题适配
.theme-dark {
  .el-upload__tip {
    color: var(--base-text-color) !important;
  }

  .el-dialog__body {
    background: var(--base-menu-background);
    color: var(--base-text-color);
  }

  // 更强的选择器确保容器背景适配
  .user-file-upload .storage-selector {
    background: var(--base-menu-background) !important;
    border-color: var(--base-border-color) !important;
    color: var(--base-text-color) !important;

    // 确保内部元素也适配主题
    * {
      color: var(--base-text-color) !important;
    }

    .el-radio-group .el-radio__label {
      color: var(--base-text-color) !important;
    }

    .strategy-tip {
      color: var(--base-text-color-secondary) !important;
    }
  }

  .user-file-upload .folder-selector {
    background: var(--base-menu-background) !important;
    border-color: var(--base-border-color) !important;
    color: var(--base-text-color) !important;

    // 确保内部元素也适配主题
    * {
      color: var(--base-text-color) !important;
    }

    .el-cascader {
      background: var(--base-menu-background) !important;
      border-color: var(--base-border-color) !important;
    }
  }
}

.theme-starry-sky {
  .el-upload__tip {
    color: #ffffff !important;
  }

  .el-dialog__body {
    background: rgba(26, 31, 60, 0.95);
    color: #ffffff;
  }

  // 更强的选择器确保容器背景适配
  .user-file-upload .storage-selector {
    background: rgba(26, 31, 60, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    backdrop-filter: blur(10px);
  }

  .user-file-upload .folder-selector {
    background: rgba(26, 31, 60, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    backdrop-filter: blur(10px);
  }
}

// 按钮样式优化
.file-type-info {
  .el-button--text {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.theme-dark .file-type-info {
  .el-button--text {
    color: var(--theme-color) !important;
    
    &:hover {
      color: var(--theme-color-light) !important;
    }
  }
}

.theme-starry-sky .file-type-info {
  .el-button--text {
    color: #ffffff !important;
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
    
    &:hover {
      color: #ffffff !important;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
    }
  }
}
</style> 
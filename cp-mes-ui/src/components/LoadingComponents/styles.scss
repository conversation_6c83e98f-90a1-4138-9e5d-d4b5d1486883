/* 标准加载组件样式 */
.standard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &--small {
    padding: 15px;

    .loading-text {
      font-size: 12px;
    }
  }

  &--medium {
    padding: 20px;

    .loading-text {
      font-size: 14px;
    }
  }

  &--large {
    padding: 30px;

    .loading-text {
      font-size: 16px;
    }
  }
}

.loading-icon {
  color: var(--current-color);
  animation: rotating 2s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  margin: 0;
  color: var(--base-color-2);
  font-weight: 500;
  text-align: center;
  line-height: 1.4;

  small {
    font-size: 0.85em;
    color: var(--base-color-3);
    font-weight: normal;
  }
}

/* 点状加载动画 */
.dots-container {
  display: flex;
  gap: 4px;
  margin-bottom: 12px;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--current-color);
    animation: dot-bounce 1.4s infinite ease-in-out both;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 骨架屏加载 */
.skeleton-container {
  width: 100%;
  max-width: 300px;

  .skeleton-line {
    height: 16px;
    background: linear-gradient(90deg,
      var(--border-color-1) 25%,
      var(--base-color-8) 50%,
      var(--border-color-1) 75%
    );
    background-size: 200% 100%;
    border-radius: 4px;
    margin-bottom: 12px;
    animation: skeleton-loading 1.5s infinite;

    &.short {
      width: 60%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 物流专用加载 */
.logistics-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;

  .loading-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
  }

  .loading-truck {
    font-size: 32px;
    color: var(--current-color);
    animation: truck-move 2s ease-in-out infinite;
  }

  .loading-waves {
    display: flex;
    gap: 4px;

    .wave {
      width: 4px;
      height: 20px;
      background: var(--current-color);
      border-radius: 2px;
      animation: wave-animation 1.2s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.4s; }
      &:nth-child(2) { animation-delay: -0.2s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

@keyframes truck-move {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(8px); }
}

@keyframes wave-animation {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes rotating {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 深色主题适配 */
.theme-dark {
  .skeleton-line {
    background: linear-gradient(90deg,
      var(--base-menu-background) 25%,
      var(--base-color-7) 50%,
      var(--base-menu-background) 75%
    );
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .standard-loading {
    &--medium {
      padding: 15px;
    }

    &--large {
      padding: 20px;
    }
  }

  .logistics-loading {
    padding: 30px 15px;

    .loading-truck {
      font-size: 28px;
    }
  }
}

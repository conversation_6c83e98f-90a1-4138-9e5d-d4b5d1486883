/**
 * 标准化加载组件工具
 * 提供统一的加载状态显示方案
 */

import Vue from 'vue'

/**
 * 创建标准加载组件
 */
export const StandardLoading = Vue.extend({
  name: 'StandardLoading',
  props: {
    text: {
      type: String,
      default: '加载中...'
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    type: {
      type: String,
      default: 'spinner', // spinner, dots, skeleton
      validator: value => ['spinner', 'dots', 'skeleton'].includes(value)
    }
  },
  computed: {
    iconSize() {
      const sizeMap = {
        small: '24px',
        medium: '32px',
        large: '48px'
      }
      return sizeMap[this.size]
    },
    containerClass() {
      return [
        'standard-loading',
        `standard-loading--${this.size}`,
        `standard-loading--${this.type}`
      ]
    }
  },
  render(h) {
    if (this.type === 'skeleton') {
      return h('div', {
        class: this.containerClass
      }, [
        h('div', { class: 'skeleton-container' }, [
          h('div', { class: 'skeleton-line' }),
          h('div', { class: 'skeleton-line short' }),
          h('div', { class: 'skeleton-line' })
        ])
      ])
    }

    if (this.type === 'dots') {
      return h('div', {
        class: this.containerClass
      }, [
        h('div', { class: 'dots-container' }, [
          h('span', { class: 'dot' }),
          h('span', { class: 'dot' }),
          h('span', { class: 'dot' })
        ]),
        h('p', { class: 'loading-text' }, this.text)
      ])
    }

    // 默认spinner类型
    return h('div', {
      class: this.containerClass
    }, [
      h('i', {
        class: 'el-icon-loading loading-icon',
        style: { fontSize: this.iconSize }
      }),
      h('p', { class: 'loading-text' }, this.text)
    ])
  }
})

/**
 * 物流专用加载组件
 */
export const LogisticsLoading = Vue.extend({
  name: 'LogisticsLoading',
  props: {
    trackingNumber: String
  },
  render(h) {
    return h('div', {
      class: 'logistics-loading'
    }, [
      h('div', { class: 'loading-header' }, [
        h('i', { class: 'el-icon-truck loading-truck' }),
        h('div', { class: 'loading-waves' }, [
          h('span', { class: 'wave' }),
          h('span', { class: 'wave' }),
          h('span', { class: 'wave' })
        ])
      ]),
      h('p', { class: 'loading-text' }, [
        '正在查询物流信息',
        this.trackingNumber ? h('br') : null,
        this.trackingNumber ? h('small', `单号：${this.trackingNumber}`) : null
      ])
    ])
  }
})

/**
 * 全局注册加载组件
 */
export const registerLoadingComponents = () => {
  Vue.component('StandardLoading', StandardLoading)
  Vue.component('LogisticsLoading', LogisticsLoading)
}

/**
 * 加载指令增强
 */
export const enhanceLoadingDirective = () => {
  // 扩展v-loading指令的功能
  const originalLoading = Vue.directive('loading')
  
  Vue.directive('loading', {
    ...originalLoading,
    inserted(el, binding) {
      // 添加触摸优化
      el.style.touchAction = 'pan-y'
      el.style.webkitOverflowScrolling = 'touch'
      
      // 调用原始指令
      if (originalLoading && originalLoading.inserted) {
        originalLoading.inserted(el, binding)
      }
    }
  })
}
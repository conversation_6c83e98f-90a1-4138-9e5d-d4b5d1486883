<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :on-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{hide: this.fileList.length >= this.limit}"
      :disabled="disable"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType && fileType.length > 0">
        <span class="file-type-info">
          <span v-if="!showAllFileTypes">
            格式为 <b style="color: #f56c6c">{{ displayFileTypes }}</b>
            <el-button 
              type="text" 
              size="mini" 
              @click="showAllFileTypes = true" 
              style="color: #409eff; margin-left: 8px; padding: 0;"
              v-if="fileType.length > 5"
            >
              查看全部({{ fileType.length }}种)
            </el-button>
          </span>
          <span v-else>
            格式为 <b style="color: #f56c6c">{{ fileType.join(", ") }}</b>
            <el-button 
              type="text" 
              size="mini" 
              @click="showAllFileTypes = false" 
              style="color: #409eff; margin-left: 8px; padding: 0;"
            >
              收起
            </el-button>
          </span>
        </span>
      </template>
      的文件
      <div class="upload-limits" v-if="limit > 1" style="font-size: 12px; color: #909399; margin-top: 4px;">
        当前可上传 <b style="color: #409eff;">{{ limit }}</b> 个图片，
        已选择 <b style="color: #67c23a;">{{ fileList.length }}</b> 个
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      title="预览"
      width="800"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { listByIds, delOss } from "@/api/system/oss";

export default {
  props: {
    values: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 10,  // 图片上传默认为10个
    },
    // 大小限制(MB)
    fileSize: {
       type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "tiff", "ico"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      hideUpload: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/oss/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      // 文件类型显示控制
      showAllFileTypes: false
    };
  },
  watch: {
    values: {
      async handler(val) {
        if (val) {
          // 首先将值转为数组
          let list;
          if (Array.isArray(val)) {
            list = val;
          } else {
            await listByIds(val).then(res => {
              list = res.data;
            })
          }
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            // 此处name使用ossId 防止删除出现重名
            item = { name: item.ossId, url: item.url, ossId: item.ossId };
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
    // 显示的文件类型（限制数量）
    displayFileTypes() {
      if (!this.fileType || this.fileType.length === 0) {
        return '';
      }
      
      // 如果文件类型少于等于5种，直接显示全部
      if (this.fileType.length <= 5) {
        return this.fileType.join(", ");
      }
      
      // 否则只显示前5种，并标注"等"
      const displayTypes = this.fileType.slice(0, 5);
      return displayTypes.join(", ") + " 等";
    }
  },
  methods: {
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false;
      if (this.fileType.length) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        isImg = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
      } else {
        isImg = file.type.indexOf("image") > -1;
      }

      if (!isImg) {
        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}图片格式文件!`);
        return false;
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传图片，请稍候...");
      this.number++;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.data.fileName, url: res.data.url, ossId: res.data.ossId });
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.imageUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 删除图片
    handleDelete(file) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name);
      if(findex > -1) {
        let ossId = this.fileList[findex].ossId;
        delOss(ossId);
        this.fileList.splice(findex, 1);
        this.$emit("input", this.listToString(this.fileList));
      }
    },
    // 上传失败
    handleUploadError(res) {
      this.$modal.msgError("上传图片失败，请重试");
      this.$modal.closeLoading();
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.listToString(this.fileList));
        this.$modal.closeLoading();
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        if (list[i].ossId) {
          strs += list[i].ossId + separator;
        }
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    }
  }
};
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
    display: none;
}

// 主题适配
.theme-dark {
  .image-upload {
    .el-upload__tip {
      color: var(--base-text-color) !important;
    }

    .file-type-info {
      .el-button--text {
        color: var(--theme-color) !important;
        
        &:hover {
          color: var(--theme-color-light) !important;
        }
      }
    }

    .el-upload-list {
      .el-upload-list__item {
        background: var(--base-card-background);
        border-color: var(--base-border-color);
      }
    }

    .el-dialog__body {
      background: var(--base-menu-background);
      color: var(--base-text-color);
    }
  }
}

.theme-starry-sky {
  .image-upload {
    .el-upload__tip {
      color: #ffffff !important;
    }

    .file-type-info {
      .el-button--text {
        color: #ffffff !important;
        text-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
        
        &:hover {
          color: #ffffff !important;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
        }
      }
    }

    .el-upload-list {
      .el-upload-list__item {
        background: rgba(24, 30, 42, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }
    }

    .el-dialog__body {
      background: rgba(26, 31, 60, 0.95);
      color: #ffffff;
    }
  }
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
    transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
</style>


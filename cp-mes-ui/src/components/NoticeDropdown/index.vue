<template>
  <div class="notice-dropdown">
    <el-dropdown trigger="click" @command="handleCommand" placement="bottom-end">
      <div class="notice-trigger">
        <svg-icon icon-class="message" class="notice-icon" />
        <el-badge v-if="unreadCount > 0" :value="unreadCount > 99 ? '99+' : unreadCount" class="notice-badge">
        </el-badge>
      </div>
      <el-dropdown-menu slot="dropdown" class="notice-dropdown-menu">
        <div class="notice-header">
          <div class="header-title">
            <div class="title-icon-wrapper">
              <svg-icon icon-class="message" />
            </div>
            <span>系统通知</span>
          </div>
          <div class="header-stats">
            <el-badge :value="unreadCount" :max="99" :hidden="unreadCount === 0" class="unread-badge">
              <span class="unread-text">未读通知</span>
            </el-badge>
          </div>
        </div>
        
        <div class="notice-tabs">
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'unread' }" 
            @click="activeTab = 'unread'"
          >
            未读 ({{ unreadNotices.length }})
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'all' }" 
            @click="activeTab = 'all'"
          >
            全部 ({{ allNotices.length }})
          </div>
        </div>
        
        <div class="notice-list" v-loading="loading">
          <template v-if="currentNotices.length > 0">
            <div 
              v-for="notice in currentNotices" 
              :key="notice.noticeId" 
              class="notice-item"
              :class="{ 'is-read': notice.isRead }"
              @click="handleNoticeClick(notice)"
            >
              <div class="notice-item-header">
                <div class="notice-title">
                  <svg-icon 
                    :icon-class="notice.noticeType === '2' ? 'star' : 'message'" 
                    class="notice-type-icon"
                    :class="{ 'type-notice': notice.noticeType === '1', 'type-announcement': notice.noticeType === '2' }"
                  />
                  <span class="title-text">{{ notice.noticeTitle }}</span>
                  <el-tag v-if="notice.forceRead === '1'" size="mini" type="warning" class="force-tag">
                    强制
                  </el-tag>
                </div>
                <div class="notice-status">
                  <el-tag 
                    v-if="!notice.isRead" 
                    size="mini" 
                    type="danger" 
                    class="unread-tag"
                  >
                    未读
                  </el-tag>
                </div>
              </div>
              
              <div class="notice-meta">
                <span class="notice-creator">
                  <i class="el-icon-user"></i>
                  {{ notice.createBy }}
                </span>
                <span class="notice-time">
                  <i class="el-icon-time"></i>
                  {{ formatTime(notice.releaseTime || notice.createTime) }}
                </span>
              </div>
              
              <div v-if="notice.header" class="notice-excerpt">
                {{ notice.header }}
              </div>
            </div>
          </template>
          
          <div v-else class="empty-notice">
            <svg-icon icon-class="message" class="empty-icon" />
            <p>{{ activeTab === 'unread' ? '暂无未读通知' : '暂无通知' }}</p>
          </div>
        </div>
        
        <div class="notice-footer">
          <el-button 
            type="text" 
            size="small" 
            @click="markAllAsRead"
            :disabled="unreadNotices.length === 0"
          >
            全部标为已读
          </el-button>
          <el-button 
            type="text" 
            size="small" 
            @click="goToNoticeManage"
          >
            通知管理
          </el-button>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 预览通知弹窗 -->
    <NoticePopup 
      :notices="previewNotices" 
      @all-read="handlePreviewClose"
      class="notice-popup-wrapper"
    />
  </div>
</template>

<script>
import { listNotice, markNoticeAsRead, getNotice } from "@/api/system/notice";
import NoticePopup from "@/components/NoticePopup";
import "./enhanced-styles.css";

export default {
  name: "NoticeDropdown",
  components: {
    NoticePopup
  },
  data() {
    return {
      loading: false,
      activeTab: 'unread', // 'unread' | 'all'
      allNotices: [],
      previewNotices: [],
      refreshTimer: null
    };
  },
  computed: {
    unreadNotices() {
      return this.allNotices.filter(notice => !notice.isRead);
    },
    unreadCount() {
      return this.unreadNotices.length;
    },
    currentNotices() {
      if (this.activeTab === 'unread') {
        return this.unreadNotices.slice(0, 10); // 限制显示数量
      }
      return this.allNotices.slice(0, 10); // 限制显示数量
    }
  },
  created() {
    this.loadNotices();
    this.startAutoRefresh();
  },
  beforeDestroy() {
    this.stopAutoRefresh();
  },
  methods: {
    async loadNotices() {
      try {
        this.loading = true;
        // 使用普通列表API获取通知数据
        const response = await listNotice({
          pageNum: 1,
          pageSize: 50,
          status: '0'
        });
        
        // 修复未读状态bug：从localStorage或后端获取用户的已读记录
        if (response.rows) {
          const readNotices = JSON.parse(localStorage.getItem('readNotices') || '{}');
          const currentUserId = this.$store.state.user.id || 'default';
          const userReadNotices = readNotices[currentUserId] || [];
          
          response.rows.forEach(notice => {
            // 检查用户是否已读过该通知
            notice.isRead = userReadNotices.includes(notice.noticeId);
          });
        }
        
        this.allNotices = response.rows || [];
      } catch (error) {
        console.error('加载通知列表失败:', error);
        this.$message.error('加载通知列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    async handleNoticeClick(notice) {
      try {
        // 获取完整的通知详情
        const response = await getNotice(notice.noticeId);
        const fullNotice = response.data;
        
        // 显示通知弹窗
        this.previewNotices = [fullNotice];
        
        // 如果是未读通知，标记为已读
        if (!notice.isRead) {
          await this.markAsRead(notice.noticeId);
        }
      } catch (error) {
        console.error('获取通知详情失败:', error);
        this.$message.error('获取通知详情失败');
      }
    },
    
    async markAsRead(noticeId) {
      try {
        await markNoticeAsRead(noticeId);
        
        // 更新本地状态
        const notice = this.allNotices.find(n => n.noticeId === noticeId);
        if (notice) {
          notice.isRead = true;
        }
        
        // 同步更新localStorage中的已读记录
        this.updateLocalReadStatus(noticeId);
      } catch (error) {
        console.error('标记已读失败:', error);
      }
    },
    
    async markAllAsRead() {
      if (this.unreadNotices.length === 0) return;
      
      try {
        // 使用单个标记API批量处理
        const promises = this.unreadNotices.map(notice => 
          markNoticeAsRead(notice.noticeId)
        );
        await Promise.all(promises);
        
        // 更新本地状态
        this.unreadNotices.forEach(notice => {
          notice.isRead = true;
          this.updateLocalReadStatus(notice.noticeId);
        });
        
        this.$message.success('已全部标为已读');
      } catch (error) {
        console.error('批量标记已读失败:', error);
        this.$message.error('标记已读失败');
      }
    },
    
    // 新增：更新本地已读状态
    updateLocalReadStatus(noticeId) {
      const readNotices = JSON.parse(localStorage.getItem('readNotices') || '{}');
      const currentUserId = this.$store.state.user.id || 'default';
      
      if (!readNotices[currentUserId]) {
        readNotices[currentUserId] = [];
      }
      
      if (!readNotices[currentUserId].includes(noticeId)) {
        readNotices[currentUserId].push(noticeId);
        localStorage.setItem('readNotices', JSON.stringify(readNotices));
      }
    },
    
    goToNoticeManage() {
      this.$router.push('/system/notice');
    },
    
    handlePreviewClose() {
      this.previewNotices = [];
    },
    
    handleCommand(command) {
      // 处理下拉菜单命令
    },
    
    formatTime(time) {
      if (!time) return '未知时间';
      
      const date = new Date(time);
      const now = new Date();
      const diff = now - date;
      
      // 小于1小时显示分钟
      if (diff < 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 1000)) + '分钟前';
      }
      
      // 小于24小时显示小时
      if (diff < 24 * 60 * 60 * 1000) {
        return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
      }
      
      // 大于24小时显示具体日期
      return date.toLocaleDateString();
    },
    
    startAutoRefresh() {
      // 每5分钟自动刷新一次
      this.refreshTimer = setInterval(() => {
        this.loadNotices();
      }, 5 * 60 * 1000);
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.notice-dropdown {
  position: relative;
  
  .notice-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      background: rgba(54, 113, 232, 0.1);
      transform: translateY(-1px);
    }
    
    .notice-icon {
      font-size: 18px;
      color: var(--icon-color);
      transition: all 0.3s ease;
    }
    
    .notice-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      z-index: 10;
    }
  }
}

:deep(.notice-dropdown-menu) {
  width: 420px !important;
  max-height: 580px !important;
  padding: 0 !important;
  border-radius: 20px !important;
  box-shadow: 0 16px 64px rgba(54, 113, 232, 0.15), 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(54, 113, 232, 0.1) !important;
  background: #ffffff !important;
  z-index: 2000;
  backdrop-filter: blur(20px) !important;
  
  .notice-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color-1);
    background: linear-gradient(135deg, #3671e8, #5b8def);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="30" r="1" fill="rgba(255,255,255,0.08)"/><circle cx="60" cy="20" r="1" fill="rgba(255,255,255,0.12)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 17px;
      font-weight: 700;
      color: white;
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
      letter-spacing: 0.5px;
      
      .title-icon-wrapper {
        width: 28px;
        height: 28px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        
        .svg-icon {
          color: white;
          font-size: 16px;
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
        }
      }
    }
    
    .header-stats {
      position: relative;
      z-index: 1;
      
      .unread-badge {
        .unread-text {
          font-size: 13px;
          color: white;
          padding: 4px 12px;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 16px;
          border: 1px solid rgba(255, 255, 255, 0.3);
          font-weight: 500;
          letter-spacing: 0.3px;
          backdrop-filter: blur(10px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .notice-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color-1);
    background: var(--base-item-bg);
    position: relative;
    
    .tab-item {
      flex: 1;
      padding: 16px 20px;
      text-align: center;
      cursor: pointer;
      font-size: 15px;
      color: var(--base-color-2);
      border-bottom: 3px solid transparent;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: transparent;
      font-weight: 500;
      letter-spacing: 0.3px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(54, 113, 232, 0.05), rgba(54, 113, 232, 0.02));
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover {
        color: var(--base-color-1);
        transform: translateY(-1px);
        
        &::before {
          opacity: 1;
        }
      }
      
      &.active {
        color: #3671e8;
        border-bottom-color: #3671e8;
        font-weight: 600;
        background: linear-gradient(135deg, rgba(54, 113, 232, 0.08), rgba(54, 113, 232, 0.04));
        box-shadow: inset 0 1px 3px rgba(54, 113, 232, 0.1);
      }
    }
  }
  
  .notice-list {
    max-height: 300px;
    overflow-y: auto;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: var(--base-color-9);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: var(--base-color-6);
      border-radius: 3px;
      
      &:hover {
        background: var(--base-color-5);
      }
    }
    
          .notice-item {
        padding: 20px 24px !important;
        border-bottom: none !important;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        margin: 8px 20px !important;
        border-radius: 16px !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%) !important;
        border: 1px solid rgba(54, 113, 232, 0.1) !important;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(54, 113, 232, 0.08) !important;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 5px;
          height: 100%;
          background: linear-gradient(180deg, #3671e8 0%, #5b8def 50%, #74a4f7 100%);
          opacity: 0;
          transition: all 0.4s ease;
          border-radius: 0 8px 8px 0;
        }
        
        &::after {
          content: '';
          position: absolute;
          top: 16px;
          right: 20px;
          width: 8px;
          height: 8px;
          background: linear-gradient(45deg, #3671e8, #5b8def);
          border-radius: 50%;
          transform: scale(0);
          transition: all 0.4s ease;
          box-shadow: 0 2px 8px rgba(54, 113, 232, 0.4);
        }
        
        &:hover {
          background: linear-gradient(135deg, #ffffff 0%, #eef4ff 100%) !important;
          transform: translateY(-4px) scale(1.02) !important;
          border-color: rgba(54, 113, 232, 0.3) !important;
          box-shadow: 0 12px 40px rgba(54, 113, 232, 0.2) !important;
          
          &::before {
            opacity: 1 !important;
            width: 8px !important;
          }
          
          &::after {
            transform: scale(1) !important;
          }
        }
        
        &.is-read {
          opacity: 0.8 !important;
          background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fc 100%) !important;
          border-color: rgba(0, 0, 0, 0.08) !important;
          
          .notice-title .title-text {
            color: var(--base-color-3) !important;
          }
          
          &::after {
            display: none !important;
          }
          
          &::before {
            background: linear-gradient(180deg, #8e9aaf 0%, #a5b3c7 100%) !important;
          }
          
          &:hover {
            opacity: 0.95 !important;
            background: linear-gradient(135deg, #f0f2f5 0%, #f4f6f9 100%) !important;
            transform: translateY(-2px) scale(1.01) !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
          }
        }
        
        &:last-child {
          margin-bottom: 12px;
        }
        
        // 添加渐入动画
        animation: fade-in-up 0.3s ease forwards;
      
      .notice-item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .notice-title {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;
          
          .notice-type-icon {
            font-size: 18px !important;
            padding: 8px !important;
            border-radius: 12px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            
            &.type-notice {
              color: #ffffff !important;
              background: linear-gradient(135deg, #3671e8 0%, #5b8def 100%) !important;
            }
            
            &.type-announcement {
              color: #ffffff !important;
              background: linear-gradient(135deg, #f56c6c 0%, #ff8e53 100%) !important;
            }
          }
          
          .title-text {
            font-size: 16px !important;
            font-weight: 700 !important;
            color: #2c3e50 !important;
            line-height: 1.3 !important;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 12px;
            letter-spacing: 0.3px !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
          }
          
          .force-tag {
            margin-left: 8px !important;
            font-size: 10px !important;
            padding: 4px 12px !important;
            border-radius: 16px !important;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ffa726 100%) !important;
            color: white !important;
            border: none !important;
            font-weight: 700 !important;
            box-shadow: 0 3px 12px rgba(255, 107, 107, 0.4) !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
          }
        }
        
        .notice-status {
          .unread-tag {
            font-size: 11px !important;
            padding: 6px 14px !important;
            font-weight: 700 !important;
            border-radius: 20px !important;
            background: linear-gradient(135deg, #f56c6c 0%, #ff9f43 50%, #ffa726 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 4px 16px rgba(245, 108, 108, 0.4) !important;
            animation: pulse-glow 2s infinite !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
          }
        }
      }
      
              .notice-meta {
          display: flex;
          gap: 16px;
          margin-bottom: 14px;
          flex-wrap: wrap;
          
          .notice-creator,
          .notice-time {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px !important;
            color: #6c757d !important;
            padding: 6px 14px !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-radius: 12px !important;
            border: 1px solid rgba(108, 117, 125, 0.15) !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
            
            i {
              font-size: 14px !important;
              opacity: 0.9 !important;
              color: #3671e8 !important;
              filter: drop-shadow(0 1px 2px rgba(54, 113, 232, 0.2)) !important;
            }
            
            &:hover {
              background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%) !important;
              border-color: rgba(54, 113, 232, 0.3) !important;
              transform: translateY(-1px) !important;
              box-shadow: 0 4px 12px rgba(54, 113, 232, 0.15) !important;
              color: #495057 !important;
            }
          }
        }
      
              .notice-excerpt {
          font-size: 14px !important;
          color: #5a6c7d !important;
          line-height: 1.6 !important;
          background: linear-gradient(135deg, rgba(54, 113, 232, 0.08) 0%, rgba(54, 113, 232, 0.03) 50%, rgba(116, 164, 247, 0.05) 100%) !important;
          padding: 14px 16px !important;
          border-radius: 12px !important;
          border-left: 4px solid transparent !important;
          border-image: linear-gradient(180deg, #3671e8, #74a4f7) 1 !important;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          margin-top: 12px !important;
          position: relative;
          font-weight: 400 !important;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02) !important;
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #3671e8 0%, #74a4f7 50%, transparent 100%) !important;
            border-radius: 12px 12px 0 0 !important;
          }
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, rgba(54, 113, 232, 0.1), rgba(54, 113, 232, 0.05)) !important;
            border-radius: 12px 0 12px 0 !important;
            opacity: 0.7;
          }
        }
    }
    
            .empty-notice {
          padding: 50px 20px;
          text-align: center;
          color: var(--base-color-2);
          
          .empty-icon {
            font-size: 56px;
            color: var(--base-color-3);
            margin-bottom: 16px;
            opacity: 0.5;
          }
          
          p {
            margin: 0;
            font-size: 14px;
            color: var(--base-color-3);
            font-weight: 500;
          }
        }
  }
  
  .notice-footer {
    padding: 12px 16px;
    border-top: 1px solid var(--border-color-1);
    display: flex;
    justify-content: space-between;
    background: var(--base-color-9);
    border-radius: 0 0 8px 8px;
    
    .el-button {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      
      &:hover {
        color: #3671e8;
        background: rgba(54, 113, 232, 0.1);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

/* 动画效果 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);
    transform: scale(1.05);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹窗层级管理 */
.notice-popup-wrapper {
  :deep(.el-dialog__wrapper) {
    z-index: 3000 !important;
  }
  
  :deep(.beautiful-notice-dialog) {
    z-index: 3001 !important;
  }
}

/* 主题适配 */
.theme-dark {
  :deep(.notice-dropdown-menu) {
    background: var(--base-item-bg);
    border-color: var(--border-color-1);
    
    .notice-header {
      background: linear-gradient(135deg, rgba(58, 123, 153, 0.3), rgba(58, 123, 153, 0.15));
    }
    
    .notice-footer {
      background: var(--base-color-9);
    }
    
    .notice-item {
      border-color: var(--border-color-1);
      
      &:hover {
        background: linear-gradient(135deg, rgba(58, 123, 153, 0.12), rgba(58, 123, 153, 0.06));
        border-color: rgba(58, 123, 153, 0.3);
        box-shadow: 0 8px 25px rgba(58, 123, 153, 0.2);
        
        &::before {
          background: linear-gradient(to bottom, #70afce, #3a7b99);
        }
        
        &::after {
          background: #70afce;
        }
      }
      
      .notice-excerpt {
        background: linear-gradient(135deg, rgba(58, 123, 153, 0.1), rgba(58, 123, 153, 0.05));
        border-left-color: #70afce;
        
        &::before {
          background: linear-gradient(90deg, #70afce, transparent);
        }
      }
      
      .notice-type-icon {
        &.type-notice {
          color: #70afce;
          background: rgba(112, 175, 206, 0.15);
        }
        
        &.type-announcement {
          background: rgba(245, 108, 108, 0.15);
        }
      }
      
      .notice-meta {
        .notice-creator i,
        .notice-time i {
          color: #70afce;
        }
        
        .notice-creator:hover,
        .notice-time:hover {
          background: rgba(58, 123, 153, 0.1);
          border-color: rgba(58, 123, 153, 0.3);
        }
      }
    }
    
    .notice-tabs .tab-item {
      &:hover {
        background: rgba(58, 123, 153, 0.1);
        
        &::before {
          background: linear-gradient(135deg, rgba(58, 123, 153, 0.08), rgba(58, 123, 153, 0.04));
        }
      }
      
      &.active {
        color: #70afce;
        border-bottom-color: #70afce;
        background: linear-gradient(135deg, rgba(58, 123, 153, 0.15), rgba(58, 123, 153, 0.08));
      }
    }
  }
  
  .notice-trigger:hover {
    background: rgba(58, 123, 153, 0.2) !important;
  }
}

.theme-starry-sky {
  :deep(.notice-dropdown-menu) {
    background: rgba(var(--base-item-bg-rgb), 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style> 
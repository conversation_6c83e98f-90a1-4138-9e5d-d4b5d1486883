/* 增强样式 - 现代简洁设计 */
.el-dropdown-menu.notice-dropdown-menu {
  width: 380px !important;
  max-height: 480px !important;
  border-radius: 12px !important;
  background: #ffffff !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #e5e7eb !important;
  overflow: hidden !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item {
  padding: 14px 18px !important;
  margin: 4px 12px !important;
  border-radius: 8px !important;
  background: #ffffff !important;
  border: 1px solid #f3f4f6 !important;
  box-shadow: none !important;
  transition: all 0.2s ease !important;
  position: relative;
  overflow: hidden;
  border-left: 3px solid transparent !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item::before {
  display: none;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item::after {
  display: none;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item:hover {
  background: #f8fafc !important;
  transform: none !important;
  border-color: #e5e7eb !important;
  border-left-color: #3671e8 !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06) !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item:not(.is-read) {
  border-left-color: #3671e8 !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item.is-read {
  opacity: 0.75 !important;
  background: #fafbfc !important;
  border-left-color: #d1d5db !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item.is-read:hover {
  background: #f3f4f6 !important;
  border-left-color: #9ca3af !important;
}

/* 通知项头部布局 */
.el-dropdown-menu.notice-dropdown-menu .notice-item-header {
  margin-bottom: 10px !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-title {
  gap: 8px !important;
}

/* 标题样式 */
.el-dropdown-menu.notice-dropdown-menu .title-text {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #111827 !important;
  line-height: 1.4 !important;
  margin-bottom: 0 !important;
  margin-right: 8px !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-item.is-read .title-text {
  color: #6b7280 !important;
}

/* 图标样式 */
.el-dropdown-menu.notice-dropdown-menu .notice-type-icon {
  font-size: 14px !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-type-icon.type-notice {
  color: #ffffff !important;
  background: #3671e8 !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-type-icon.type-announcement {
  color: #ffffff !important;
  background: #ef4444 !important;
}

/* 隐藏标签 */
.el-dropdown-menu.notice-dropdown-menu .force-tag {
  display: none !important;
}

.el-dropdown-menu.notice-dropdown-menu .unread-tag {
  display: none !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-status {
  display: none !important;
}

/* 元数据布局 */
.el-dropdown-menu.notice-dropdown-menu .notice-meta {
  margin-bottom: 8px !important;
  gap: 12px !important;
}

/* 元数据样式 */
.el-dropdown-menu.notice-dropdown-menu .notice-creator,
.el-dropdown-menu.notice-dropdown-menu .notice-time {
  font-size: 12px !important;
  color: #6b7280 !important;
  padding: 0 !important;
  background: transparent !important;
  border-radius: 0 !important;
  border: none !important;
  transition: none !important;
  font-weight: 400 !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-creator:hover,
.el-dropdown-menu.notice-dropdown-menu .notice-time:hover {
  background: transparent !important;
  border: none !important;
  transform: none !important;
  box-shadow: none !important;
  color: #374151 !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-creator i,
.el-dropdown-menu.notice-dropdown-menu .notice-time i {
  font-size: 12px !important;
  opacity: 0.7 !important;
  color: #9ca3af !important;
  filter: none !important;
  margin-right: 4px !important;
}

/* 通知内容展示样式 */
.el-dropdown-menu.notice-dropdown-menu .notice-excerpt {
  font-size: 13px !important;
  color: #6b7280 !important;
  line-height: 1.6 !important;
  background: #f9fafb !important;
  padding: 10px 12px !important;
  border-radius: 8px !important;
  border: 1px solid #f3f4f6 !important;
  margin-top: 8px !important;
  position: relative;
  font-weight: 400 !important;
  max-height: 80px !important;
  overflow-y: auto !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
}

/* 内容滚动区域隐藏滚动条 */
.el-dropdown-menu.notice-dropdown-menu .notice-excerpt::-webkit-scrollbar {
  width: 0px !important;
  background: transparent !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-excerpt::before {
  display: none;
}

.el-dropdown-menu.notice-dropdown-menu .notice-excerpt::after {
  display: none;
}

/* 隐藏通知头部 */
.el-dropdown-menu.notice-dropdown-menu .notice-header {
  display: none !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-tabs {
  background: #ffffff !important;
  border-bottom: 1px solid #f3f4f6 !important;
  border-radius: 12px 12px 0 0 !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #6b7280 !important;
  padding: 16px 20px !important;
  border-bottom: 2px solid transparent !important;
  transition: all 0.2s ease !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item:hover {
  color: #374151 !important;
  background: #f9fafb !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item.active {
  color: #3671e8 !important;
  border-bottom-color: #3671e8 !important;
  background: #ffffff !important;
  font-weight: 600 !important;
}

/* 通知列表容器 - 添加滚动 */
.el-dropdown-menu.notice-dropdown-menu .notice-list {
  background: #ffffff !important;
  padding: 8px 0 !important;
  max-height: 400px !important;
  overflow-y: auto !important;
}

/* 空状态样式 */
.el-dropdown-menu.notice-dropdown-menu .empty-notice {
  padding: 40px 20px !important;
  text-align: center !important;
  color: #9ca3af !important;
}

.el-dropdown-menu.notice-dropdown-menu .empty-notice .empty-icon {
  font-size: 48px !important;
  color: #d1d5db !important;
  margin-bottom: 12px !important;
  opacity: 0.6 !important;
}

.el-dropdown-menu.notice-dropdown-menu .empty-notice p {
  margin: 0 !important;
  font-size: 14px !important;
  color: #9ca3af !important;
  font-weight: 400 !important;
}

/* 底部操作区 */
.el-dropdown-menu.notice-dropdown-menu .notice-footer {
  background: #f9fafb !important;
  border-top: 1px solid #f3f4f6 !important;
  padding: 12px 16px !important;
  border-radius: 0 0 12px 12px !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-footer .el-button {
  padding: 6px 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-footer .el-button:hover {
  color: #3671e8 !important;
  background: #f0f8ff !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-footer .el-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* 隐藏滚动条样式 */
.el-dropdown-menu.notice-dropdown-menu .notice-list::-webkit-scrollbar {
  width: 0px !important;
  background: transparent !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-list::-webkit-scrollbar-track {
  background: transparent !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-list::-webkit-scrollbar-thumb {
  background: transparent !important;
}

.el-dropdown-menu.notice-dropdown-menu .notice-list::-webkit-scrollbar-thumb:hover {
  background: transparent !important;
}

/* 火狐浏览器隐藏滚动条 */
.el-dropdown-menu.notice-dropdown-menu .notice-list {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 深色主题适配 */
.theme-dark .el-dropdown-menu.notice-dropdown-menu {
  background: #1f2937 !important;
  border-color: #374151 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-header {
  display: none !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-tabs {
  background: #1f2937 !important;
  border-bottom-color: #374151 !important;
  border-radius: 12px 12px 0 0 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item {
  color: #9ca3af !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item:hover {
  color: #d1d5db !important;
  background: #374151 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-tabs .tab-item.active {
  color: #60a5fa !important;
  border-bottom-color: #60a5fa !important;
  background: #1f2937 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-list {
  background: #1f2937 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-item {
  background: #1f2937 !important;
  border-color: #374151 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-item:hover {
  background: #374151 !important;
  border-left-color: #60a5fa !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-item:not(.is-read) {
  border-left-color: #60a5fa !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-item.is-read {
  background: #111827 !important;
  border-left-color: #4b5563 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .title-text {
  color: #f9fafb !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-item.is-read .title-text {
  color: #9ca3af !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-creator,
.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-time {
  color: #9ca3af !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-creator i,
.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-time i {
  color: #6b7280 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-excerpt {
  background: #111827 !important;
  border-color: #374151 !important;
  color: #9ca3af !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-footer {
  background: #111827 !important;
  border-top-color: #374151 !important;
}

.theme-dark .el-dropdown-menu.notice-dropdown-menu .notice-footer .el-button:hover {
  color: #60a5fa !important;
  background: #1e3a8a !important;
} 
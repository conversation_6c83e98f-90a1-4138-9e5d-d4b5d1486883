<template>
  <div class="temp-image-preview">
    <!-- 上传区域 -->
    <div class="upload-section">
      <el-upload
        ref="upload"
        class="upload-demo"
        action=""
        :http-request="customUpload"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :file-list="fileList"
        :limit="10"
        :on-exceed="handleExceed"
        multiple
        accept="image/jpeg,image/jpg,image/png,image/gif"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传jpg/png/gif文件，且不超过5MB，最多10张图片
        </div>
      </el-upload>
    </div>

    <!-- 图片预览区域 -->
    <div class="preview-section" v-if="previewImages.length > 0">
      <div class="preview-header">
        <h4>图片预览 ({{ previewImages.length }}/10)</h4>
        <div class="header-actions">
          <el-button size="mini" type="text" @click="refreshImages" :loading="loading">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
          <el-button size="mini" type="text" @click="clearAllImages" v-if="previewImages.length > 0">
            <i class="el-icon-delete"></i> 清空
          </el-button>
        </div>
      </div>
      <div class="image-grid">
        <div
          v-for="(image, index) in previewImages"
          :key="`image-${index}-${image.id}`"
          class="image-item"
        >
          <div class="image-wrapper">
            <img
              :src="image.previewUrl"
              :alt="image.name"
              @click="previewImage(image)"
              @error="handleImageError(image, index)"
              class="thumbnail"
            />
            <div class="image-overlay">
              <div class="image-actions">
                <i
                  class="el-icon-zoom-in"
                  @click.stop="previewImage(image)"
                  title="预览"
                ></i>
                <i
                  class="el-icon-delete"
                  @click.stop="removeImage(index)"
                  title="删除"
                ></i>
              </div>
            </div>
            <div class="upload-status" v-if="image.uploading">
              <i class="el-icon-loading"></i>
              <span>上传中...</span>
            </div>
            <div class="upload-status success" v-else-if="image.uploaded">
              <i class="el-icon-check"></i>
              <span>已上传</span>
            </div>
            <div class="upload-status error" v-else-if="image.error">
              <i class="el-icon-close"></i>
              <span>上传失败</span>
            </div>
          </div>
          <div class="image-info">
            <div class="image-name" :title="image.name">{{ image.name }}</div>
            <div class="image-size">{{ formatFileSize(image.size) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewDialogVisible"
      width="80%"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      class="image-preview-dialog"
    >
      <div class="preview-container" v-if="currentPreviewImage">
        <img
          :src="currentPreviewImage.previewUrl"
          :alt="currentPreviewImage.name"
          class="preview-image"
        />
        <div class="preview-info">
          <p><strong>文件名：</strong>{{ currentPreviewImage.name }}</p>
          <p><strong>大小：</strong>{{ formatFileSize(currentPreviewImage.size) }}</p>
          <p><strong>状态：</strong>{{ getUploadStatusText(currentPreviewImage) }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { uploadPurchaseImages } from '@/api/jenasi/purchaseOrderEnhanced'

export default {
  name: 'TempImagePreview',
  props: {
    purchaseOrderId: {
      type: [Number, String],
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      previewImages: [], // 预览图片列表
      previewDialogVisible: false,
      currentPreviewImage: null,
      uploading: false,
      loading: false,
      imageIdCounter: 0 // 用于生成唯一ID
    }
  },
  mounted() {
    this.loadExistingImages()
  },
  watch: {
    purchaseOrderId: {
      handler(newVal) {
        if (newVal) {
          this.loadExistingImages()
        }
      },
      immediate: true
    },
    previewImages: {
      handler(newImages) {
        // 向父组件发送图片列表变化事件
        const uploadedImages = newImages.filter(img => img.uploaded && img.serverUrl)
        this.$emit('images-change', uploadedImages.map(img => img.serverUrl))
      },
      deep: true
    }
  },
  methods: {
    /**
     * 加载已存在的图片
     */
    async loadExistingImages() {
      // 如果是临时订单，暂时不加载已存在的图片
      // 因为页面刷新后临时图片可能已经丢失
      if (this.purchaseOrderId && this.purchaseOrderId.toString().startsWith('temp_')) {
        this.previewImages = []
        return
      }
      
      // 这里可以调用API加载正式订单的已存在图片
      // 暂时跳过
    },

    /**
     * 上传前验证
     */
    beforeUpload(file) {
      // 检查文件类型
      const isImage = /^image\/(jpeg|jpg|png|gif)$/i.test(file.type)
      if (!isImage) {
        this.$message.error('只能上传 JPG/PNG/GIF 格式的图片!')
        return false
      }

      // 检查文件大小
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }

      // 检查数量限制
      if (this.previewImages.length >= 10) {
        this.$message.error('最多只能上传10张图片!')
        return false
      }

      return true
    },

    /**
     * 自定义上传
     */
    async customUpload(params) {
      const { file } = params
      
      // 立即创建预览
      const previewImage = await this.createImagePreview(file)
      this.previewImages.push(previewImage)

      // 异步上传到服务器
      this.uploadToServer(previewImage, file)
    },

    /**
     * 创建图片预览
     */
    createImagePreview(file) {
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          const imageData = {
            id: ++this.imageIdCounter,
            name: file.name,
            size: file.size,
            type: file.type,
            previewUrl: e.target.result, // 本地预览URL
            serverUrl: null, // 服务器URL
            uploading: true,
            uploaded: false,
            error: false,
            file: file
          }
          resolve(imageData)
        }
        reader.readAsDataURL(file)
      })
    },

    /**
     * 上传到服务器
     */
    async uploadToServer(imageData, file) {
      try {
        imageData.uploading = true
        
        const response = await uploadPurchaseImages(this.purchaseOrderId, [file])
        console.log('图片上传响应:', response)
        
        if (response.code === 200 || response.code === 0) {
          imageData.uploading = false
          imageData.uploaded = true
          imageData.serverUrl = response.data && response.data.length > 0 ? response.data[0] : null
          
          this.$message.success('图片上传成功!')
          this.$emit('upload-success', response)
        } else {
          throw new Error(response.msg || response.message || '上传失败')
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        imageData.uploading = false
        imageData.error = true
        this.$message.error('图片上传失败: ' + error.message)
        this.$emit('upload-error', error)
      }
    },

    /**
     * 上传成功回调
     */
    handleUploadSuccess(response, file) {
      // 这个方法由customUpload处理，这里保留兼容性
    },

    /**
     * 上传失败回调
     */
    handleUploadError(error) {
      this.$message.error('图片上传失败: ' + error.message)
      this.$emit('upload-error', error)
    },

    /**
     * 文件数量超出限制
     */
    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传10张图片!')
    },

    /**
     * 预览图片
     */
    previewImage(image) {
      this.currentPreviewImage = image
      this.previewDialogVisible = true
    },

    /**
     * 删除图片
     */
    removeImage(index) {
      this.$confirm('确定要删除这张图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.previewImages.splice(index, 1)
        this.$message.success('图片已删除')
      }).catch(() => {
        // 用户取消删除
      })
    },

    /**
     * 清空所有图片
     */
    clearAllImages() {
      this.$confirm('确定要清空所有图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.previewImages = []
        this.fileList = []
        this.$message.success('已清空所有图片')
      }).catch(() => {
        // 用户取消清空
      })
    },

    /**
     * 刷新图片
     */
    refreshImages() {
      this.loadExistingImages()
    },

    /**
     * 图片加载错误处理
     */
    handleImageError(image, index) {
      console.warn('图片加载失败:', image.previewUrl)
      // 可以设置默认图片或重试逻辑
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    /**
     * 获取上传状态文本
     */
    getUploadStatusText(image) {
      if (image.uploading) return '上传中...'
      if (image.uploaded) return '已上传'
      if (image.error) return '上传失败'
      return '待上传'
    }
  }
}
</script>

<style scoped>
/* 这里可以复用原有的样式，或者简化版本 */
.temp-image-preview {
  width: 100%;
}

.upload-section {
  margin-bottom: 20px;
}

.preview-section .preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-section .preview-header h4 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
}

.image-item {
  position: relative;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.image-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 15px;
}

.image-actions i {
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.upload-status {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-status.success {
  background: rgba(103, 194, 58, 0.9);
}

.upload-status.error {
  background: rgba(245, 108, 108, 0.9);
}

.image-info {
  padding: 10px;
}

.image-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-size {
  font-size: 12px;
  color: #999;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-info {
  margin-top: 20px;
  text-align: left;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
}

.preview-info p {
  margin: 5px 0;
  color: #666;
}

.preview-info strong {
  color: #333;
}
</style>

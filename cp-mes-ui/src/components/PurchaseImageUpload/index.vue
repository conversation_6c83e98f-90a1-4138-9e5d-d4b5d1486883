<template>
  <div class="purchase-image-upload">
    <!-- 上传区域 -->
    <div class="upload-section">
      <el-upload
        ref="upload"
        class="upload-demo"
        action=""
        :http-request="customUpload"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :file-list="fileList"
        :limit="10"
        :on-exceed="handleExceed"
        multiple
        accept="image/jpeg,image/jpg,image/png,image/gif"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传jpg/png/gif文件，且不超过5MB，最多10张图片
        </div>
      </el-upload>
    </div>

    <!-- 图片预览区域 -->
    <div class="preview-section" v-if="imageList.length > 0">
      <div class="preview-header">
        <h4>已上传图片 ({{ imageList.length }}/10)</h4>
        <div class="header-actions">
          <el-button size="mini" type="text" @click="refreshImages" :loading="loading">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </div>
      <div class="image-grid">
        <div
          v-for="(image, index) in imageList"
          :key="`image-${index}-${image.url}`"
          class="image-item"
        >
          <div class="image-wrapper">
            <img
              :src="image.url"
              :alt="image.name"
              @click="previewImage(image)"
              @error="handleImageError(image, index)"
              class="thumbnail"
            />
            <div class="image-overlay">
              <div class="image-actions">
                <i
                  class="el-icon-zoom-in"
                  @click.stop="previewImage(image)"
                  title="预览"
                ></i>
                <i
                  class="el-icon-download"
                  @click.stop="downloadImage(image)"
                  title="下载"
                ></i>
                <i
                  class="el-icon-delete"
                  @click.stop="deleteImage(image, index)"
                  title="删除"
                  v-if="!readonly"
                ></i>
              </div>
            </div>
          </div>
          <div class="image-info">
            <div class="image-name" :title="image.name">{{ image.name }}</div>
            <div class="image-size">{{ formatFileSize(image.size) }}</div>
            <div class="upload-time" v-if="image.uploadTime">{{ image.uploadTime }}</div>

            <!-- 图片备注区域 -->
            <div class="image-description">
              <div v-if="!image.editingDescription" class="description-display">
                <div
                  class="description-text"
                  :class="{ 'empty': !image.description }"
                  @click="startEditDescription(image)"
                  :title="image.description || '点击添加备注'"
                >
                  {{ image.description || '点击添加备注' }}
                </div>
                <i
                  class="el-icon-edit description-edit-btn"
                  @click.stop="startEditDescription(image)"
                  title="编辑备注"
                  v-if="!readonly"
                ></i>
              </div>

              <div v-else class="description-edit">
                <el-input
                  ref="descriptionInput"
                  v-model="image.tempDescription"
                  type="textarea"
                  :rows="2"
                  :maxlength="200"
                  show-word-limit
                  placeholder="请输入图片备注信息..."
                  @blur="saveDescription(image)"
                  @keyup.enter.native="saveDescription(image)"
                  @keyup.esc.native="cancelEditDescription(image)"
                  size="mini"
                />
                <div class="description-actions">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="saveDescription(image)"
                    :loading="image.savingDescription"
                  >
                    保存
                  </el-button>
                  <el-button
                    size="mini"
                    @click="cancelEditDescription(image)"
                  >
                    取消
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewDialogVisible"
      width="80%"
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      class="image-preview-dialog"
      :z-index="3000"
    >
      <div class="preview-container" v-if="currentPreviewImage">
        <img
          :src="currentPreviewImage.url"
          :alt="currentPreviewImage.name"
          class="preview-image"
        />
        <div class="preview-info">
          <p><strong>文件名：</strong>{{ currentPreviewImage.name }}</p>
          <p><strong>大小：</strong>{{ formatFileSize(currentPreviewImage.size) }}</p>
          <p><strong>上传时间：</strong>{{ currentPreviewImage.uploadTime }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="downloadImage(currentPreviewImage)">
          下载图片
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  uploadPurchaseImages,
  getPurchaseImages,
  getPurchaseImagesWithMetadata,
  updateImageDescription
} from '@/api/jenasi/purchaseOrderEnhanced'
import request from '@/utils/request'

export default {
  name: 'PurchaseImageUpload',
  props: {
    purchaseOrderId: {
      type: [Number, String],
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      imageList: [],
      previewDialogVisible: false,
      currentPreviewImage: null,
      uploading: false,
      loading: false
    }
  },
  mounted() {
    this.loadImages()
  },
  watch: {
    purchaseOrderId: {
      handler(newVal) {
        if (newVal) {
          this.loadImages()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 加载已上传的图片
     */
    async loadImages() {
      if (!this.purchaseOrderId) return

      this.loading = true
      try {
        console.log('开始加载图片，采购订单ID:', this.purchaseOrderId)
        const response = await getPurchaseImages(this.purchaseOrderId)
        console.log('图片加载响应:', response)

        // 兼容两种响应格式：Result类(code: 0)和R类(code: 200)
        if (response.code === 200 || response.code === 0) {
          console.log('图片数据:', response.data)
          if (response.data && Array.isArray(response.data)) {
            // 尝试获取带元数据的图片信息
            try {
              const metadataResponse = await getPurchaseImagesWithMetadata(this.purchaseOrderId)
              if (metadataResponse.code === 200 && metadataResponse.data && metadataResponse.data.length > 0) {
                // 使用带元数据的图片信息
                this.imageList = metadataResponse.data.map(imageData => ({
                  id: imageData.id,
                  url: imageData.storagePath,
                  name: imageData.originalName,
                  size: imageData.fileSize || 0,
                  uploadTime: imageData.uploadTime || '未知',
                  description: imageData.imageDescription || '',
                  editingDescription: false,
                  tempDescription: '',
                  savingDescription: false
                }))
              } else {
                // 降级到URL数组格式
                this.imageList = response.data.map((url) => ({
                  id: null,
                  url: url,
                  name: this.extractFileName(url),
                  size: 0,
                  uploadTime: '未知',
                  description: '',
                  editingDescription: false,
                  tempDescription: '',
                  savingDescription: false
                }))
              }
            } catch (metadataError) {
              console.warn('获取图片元数据失败，使用基础格式:', metadataError)
              // 降级到URL数组格式
              this.imageList = response.data.map((url) => ({
                id: null,
                url: url,
                name: this.extractFileName(url),
                size: 0,
                uploadTime: '未知',
                description: '',
                editingDescription: false,
                tempDescription: '',
                savingDescription: false
              }))
            }
            console.log('处理后的图片列表:', this.imageList)
          } else {
            console.warn('响应数据格式异常:', response.data)
            this.imageList = []
          }
        } else {
          console.error('图片加载失败，响应码:', response.code, '消息:', response.msg || response.message)
          this.$message.error('加载图片失败: ' + (response.msg || response.message || '未知错误'))
        }
      } catch (error) {
        console.error('加载图片异常:', error)
        this.$message.error('加载图片失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    /**
     * 刷新图片列表
     */
    async refreshImages() {
      await this.loadImages()
      this.$message.success('图片列表已刷新')
    },

    /**
     * 上传前的校验
     */
    beforeUpload(file) {
      const isImage = /^image\/(jpeg|jpg|png|gif)$/i.test(file.type)
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传 JPG/PNG/GIF 格式的图片!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      return true
    },

    /**
     * 自定义上传
     */
    async customUpload(params) {
      const { file } = params
      this.uploading = true

      try {
        const response = await uploadPurchaseImages(this.purchaseOrderId, [file])
        console.log('上传响应:', response)
        // 兼容两种响应格式：Result类(code: 0)和R类(code: 200)
        if (response.code === 200 || response.code === 0) {
          this.handleUploadSuccess(response, file)
        } else {
          this.handleUploadError(new Error(response.msg || response.message || '上传失败'))
        }
      } catch (error) {
        this.handleUploadError(error)
      } finally {
        this.uploading = false
      }
    },

    /**
     * 上传成功回调
     */
    handleUploadSuccess(response, file) {
      console.log('图片上传响应:', response)
      this.$message.success('图片上传成功!')

      try {
        // 安全地处理响应数据
        let imageUrl = ''
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            imageUrl = response.data[0] || ''
          } else if (typeof response.data === 'string') {
            imageUrl = response.data
          } else if (response.data.url) {
            imageUrl = response.data.url
          }
        }

        // 添加到图片列表
        const newImage = {
          url: imageUrl,
          name: file.name,
          size: file.size,
          uploadTime: new Date().toLocaleString()
        }
        this.imageList.push(newImage)

        // 清空文件列表
        this.$refs.upload.clearFiles()

        // 触发事件
        this.$emit('upload-success', newImage)

        // 立即刷新图片列表以确保数据同步
        this.$nextTick(() => {
          this.loadImages()
        })
      } catch (error) {
        console.error('处理上传成功响应时出错:', error)
        this.$message.error('处理上传结果失败，但文件可能已上传成功')
        // 仍然刷新图片列表
        this.loadImages()
      }
    },

    /**
     * 上传失败回调
     */
    handleUploadError(error) {
      this.$message.error('图片上传失败: ' + error.message)
      this.$emit('upload-error', error)
    },

    /**
     * 文件数量超出限制
     */
    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传10张图片!')
    },

    /**
     * 预览图片
     */
    previewImage(image) {
      this.currentPreviewImage = image
      this.previewDialogVisible = true
    },

    /**
     * 下载图片
     */
    async downloadImage(image) {
      try {
        this.$message.info('正在准备下载...')

        // 使用fetch获取图片blob，确保触发下载而不是在浏览器中打开
        const response = await fetch(image.url, {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const blob = await response.blob()

        // 创建对象URL
        const url = window.URL.createObjectURL(blob)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = image.name || 'image.jpg' // 使用原始文件名
        link.style.display = 'none'

        // 添加到DOM并点击
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 释放对象URL
        window.URL.revokeObjectURL(url)

        this.$message.success('图片下载成功: ' + (image.name || '未知文件'))
      } catch (error) {
        console.error('下载图片失败:', error)

        // 如果fetch失败，尝试使用传统方式（可能会在新窗口打开）
        this.$message.warning('正在尝试备用下载方式...')

        const link = document.createElement('a')
        link.href = image.url
        link.download = image.name || 'image.jpg'
        link.target = '_blank'
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.info('已触发下载，如果未开始下载请检查浏览器设置')
      }
    },

    /**
     * 删除图片
     */
    async deleteImage(image, index) {
      try {
        await this.$confirm('确定要删除这张图片吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 使用新的删除接口，通过图片ID删除，确保数据库状态正确更新
        const response = await request({
          url: `/wms/purchase/deleteImageById/${image.id}`,
          method: 'delete'
        })

        if (response.code === 200 || response.code === 0) {
          this.imageList.splice(index, 1)
          this.$message.success('图片删除成功!')
          this.$emit('delete-success', image)
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除图片失败:', error)
          this.$message.error('删除图片失败: ' + (error.response?.data?.msg || error.message))
        }
      }
    },

    /**
     * 从URL中提取文件名
     */
    extractFileName(url) {
      const parts = url.split('/')
      return parts[parts.length - 1] || 'image.jpg'
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(size) {
      if (size === 0) return '未知大小'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return Math.round(size * 100) / 100 + ' ' + units[index]
    },

    /**
     * 获取图片列表（供父组件调用）
     */
    getImageList() {
      return this.imageList
    },

    /**
     * 清空图片列表
     */
    clearImages() {
      this.imageList = []
      this.$refs.upload.clearFiles()
    },

    /**
     * 重置组件数据（供父组件调用）
     */
    resetData() {
      // 重置图片列表
      this.imageList = []
      this.fileList = []

      // 关闭预览对话框
      this.previewDialogVisible = false
      this.currentPreviewImage = null

      // 重置状态
      this.uploading = false
      this.loading = false

      // 清空上传组件的文件列表
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }

      // 触发图片变更事件，通知父组件
      this.$emit('images-changed', [])

      console.log('PurchaseImageUpload 数据已重置')
    },

    /**
     * 图片加载错误处理
     */
    handleImageError(image) {
      console.error('图片加载失败:', image.url)
      this.$message.error(`图片 ${image.name} 加载失败`)
      // 可以选择从列表中移除失效的图片
      // this.imageList.splice(index, 1)
    },

    /**
     * 开始编辑图片描述
     */
    startEditDescription(image) {
      if (this.readonly) return

      image.editingDescription = true
      image.tempDescription = image.description || ''

      // 等待DOM更新后聚焦输入框
      this.$nextTick(() => {
        const inputs = this.$refs.descriptionInput
        if (inputs) {
          const input = Array.isArray(inputs) ? inputs.find(inp => inp) : inputs
          if (input && input.focus) {
            input.focus()
          }
        }
      })
    },

    /**
     * 保存图片描述
     */
    async saveDescription(image) {
      if (image.savingDescription) return

      const newDescription = (image.tempDescription || '').trim()

      // 如果描述没有变化，直接取消编辑
      if (newDescription === (image.description || '')) {
        this.cancelEditDescription(image)
        return
      }

      // 如果没有图片ID，只更新本地状态
      if (!image.id) {
        image.description = newDescription
        image.editingDescription = false
        this.$message.success('备注已更新（本地保存）')
        return
      }

      image.savingDescription = true

      try {
        const response = await updateImageDescription(image.id, newDescription)
        if (response.code === 200 || response.code === 0) {
          image.description = newDescription
          image.editingDescription = false
          this.$message.success('图片备注保存成功')
        } else {
          this.$message.error('保存失败: ' + (response.msg || response.message || '未知错误'))
        }
      } catch (error) {
        console.error('保存图片描述失败:', error)
        this.$message.error('保存失败: ' + error.message)
      } finally {
        image.savingDescription = false
      }
    },

    /**
     * 取消编辑图片描述
     */
    cancelEditDescription(image) {
      image.editingDescription = false
      image.tempDescription = ''
      image.savingDescription = false
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-image-upload {
  .upload-section {
    margin-bottom: 20px;

    .upload-demo {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 180px;
        border: 2px dashed var(--border-color-1);
        border-radius: 8px;
        background: var(--base-main-bg);
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--current-color);
          background: var(--base-color-8);
        }
      }

      :deep(.el-upload__text) {
        color: var(--base-color-2);
        font-size: 14px;
        margin-top: 10px;

        em {
          color: var(--current-color);
          font-weight: 500;
        }
      }

      :deep(.el-upload__tip) {
        color: var(--base-color-3);
        font-size: 12px;
        margin-top: 8px;
      }
    }
  }

  .preview-section {
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color-1);

      h4 {
        color: var(--base-color-1);
        margin: 0;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 20px;

      .image-item {
        position: relative;
        background: var(--base-item-bg);
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid var(--border-color-1);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .image-wrapper {
          position: relative;
          width: 100%;
          height: 140px;
          overflow: hidden;

          .thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;

            .image-actions {
              display: flex;
              gap: 15px;

              i {
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 8px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;

                &:hover {
                  background: var(--current-color);
                  transform: scale(1.1);
                }
              }
            }
          }

          &:hover .image-overlay {
            opacity: 1;
          }
        }

        .image-info {
          padding: 12px;

          .image-name {
            font-size: 13px;
            color: var(--base-color-1);
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .image-size {
            font-size: 12px;
            color: var(--base-color-3);
            margin-bottom: 2px;
          }

          .upload-time {
            font-size: 11px;
            color: var(--base-color-4);
          }

          /* 图片备注样式 */
          .image-description {
            margin-top: 8px;
            border-top: 1px solid #f0f0f0;
            padding-top: 8px;

            .description-display {
              display: flex;
              align-items: flex-start;
              gap: 4px;

              .description-text {
                flex: 1;
                font-size: 12px;
                line-height: 1.4;
                color: #666;
                cursor: pointer;
                padding: 4px 6px;
                border-radius: 4px;
                transition: all 0.2s;
                word-break: break-word;
                min-height: 20px;

                &:hover {
                  background-color: #f5f7fa;
                }

                &.empty {
                  color: #c0c4cc;
                  font-style: italic;
                }
              }

              .description-edit-btn {
                color: #409eff;
                cursor: pointer;
                font-size: 14px;
                padding: 2px;
                border-radius: 2px;
                transition: all 0.2s;

                &:hover {
                  background-color: #ecf5ff;
                  color: #337ecc;
                }
              }
            }

            .description-edit {
              margin-top: 4px;

              .description-actions {
                display: flex;
                gap: 8px;
                margin-top: 8px;
                justify-content: flex-end;

                :deep(.el-button) {
                  padding: 5px 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.image-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;

    .preview-container {
      text-align: center;

      .preview-image {
        max-width: 100%;
        max-height: 60vh;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .preview-info {
        margin-top: 20px;
        text-align: left;
        background: var(--base-item-bg);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid var(--border-color-1);

        p {
          margin: 5px 0;
          color: var(--base-color-2);

          strong {
            color: var(--base-color-1);
          }
        }
      }
    }
  }
}

/* 主题适配 */
.theme-dark {
  .purchase-image-upload {
    .upload-section {
      :deep(.el-upload-dragger) {
        background: var(--base-item-bg);
        border-color: var(--border-color-2);

        &:hover {
          background: var(--base-menu-background);
          border-color: var(--current-color);
        }
      }
    }

    .preview-section {
      .image-grid {
        .image-item {
          .image-wrapper {
            background: var(--base-menu-background);
            border-color: var(--border-color-2);
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="tracking-number-manager">
    <!-- 头部操作区 -->
    <div class="manager-header">
      <div class="header-left">
        <h4>快递单号管理</h4>
        <span class="record-count">
          共 {{ total }} 条记录
          <span v-if="searchKeyword" class="search-result">
            （筛选结果：{{ filteredTrackingNumbers.length }} 条）
          </span>
        </span>
      </div>
      <div class="header-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索快递单号、物流公司或备注"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
          style="width: 300px; margin-right: 10px;"
        />
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="showAddDialog"
        >
          添加快递单号
        </el-button>
        <el-button
          type="success"
          icon="el-icon-refresh"
          @click="refreshAll"
          :loading="refreshing"
          :disabled="trackingNumbers.length === 0"
          title="批量刷新物流信息并清理缓存"
        >
          批量刷新并清理缓存 ({{ trackingNumbers.length }})
        </el-button>
        <el-button
          type="info"
          icon="el-icon-download"
          @click="exportData"
          :disabled="trackingNumbers.length === 0"
        >
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-bar" v-if="trackingNumbers.length > 0">
      <div class="stat-item">
        <span class="stat-label">总计：</span>
        <span class="stat-value">{{ trackingNumbers.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已签收：</span>
        <span class="stat-value success">{{ getStatusCount('SIGNED') }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">运输中：</span>
        <span class="stat-value primary">{{ getStatusCount('IN_TRANSIT') }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">查询失败：</span>
        <span class="stat-value danger">{{ getStatusCount('QUERY_FAILED') }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">待查询：</span>
        <span class="stat-value info">{{ getStatusCount('PENDING') }}</span>
      </div>
    </div>

    <!-- 快递单号列表 -->
    <div class="tracking-list">
      <el-table
        :data="paginatedData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :empty-text="getEmptyText()"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="row => !row.querying"
        />

        <el-table-column
          prop="trackingNumber"
          label="快递单号"
          min-width="150"
          show-overflow-tooltip
          sortable
        >
          <template slot-scope="scope">
            <div class="tracking-number-cell">
              <code class="tracking-code">{{ scope.row.trackingNumber }}</code>
              <el-button
                type="text"
                size="mini"
                @click="copyTrackingNumber(scope.row.trackingNumber)"
                title="复制单号"
              >
                <i class="el-icon-document-copy"></i>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="company"
          label="物流公司"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.company">{{ scope.row.company }}</span>
            <span v-else-if="scope.row.logisticsCompany" class="manual-company">
              {{ scope.row.logisticsCompany }}
            </span>
            <span v-else class="no-company">未知</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getStatusColor(scope.row.status)"
              size="mini"
              :class="{ 'status-loading': scope.row.querying }"
            >
              <i v-if="scope.row.querying" class="el-icon-loading"></i>
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="statusDescription"
          label="状态描述"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.statusDescription">{{ scope.row.statusDescription }}</span>
            <span v-else-if="scope.row.errorMessage" class="error-text">{{ scope.row.errorMessage }}</span>
            <span v-else class="no-description">-</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="lastUpdateTime"
          label="最后更新"
          width="150"
          align="center"
          sortable
        >
          <template slot-scope="scope">
            <span v-if="scope.row.lastUpdateTime">
              {{ formatDate(scope.row.lastUpdateTime) }}
            </span>
            <span v-else class="no-update">未查询</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="remark"
          label="备注"
          width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            <span v-else class="no-remark">-</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="220"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="queryTracking(scope.row)"
              :loading="scope.row.querying"
              :disabled="!scope.row.trackingNumber"
            >
              {{ scope.row.querying ? '查询中' : '查询' }}
            </el-button>
            <el-button
              type="info"
              size="mini"
              @click="viewDetail(scope.row)"
              :disabled="!scope.row.trackingDetails || scope.row.trackingDetails.length === 0"
            >
              详情
            </el-button>
            <el-button
              type="warning"
              size="mini"
              @click="editTracking(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="deleteTracking(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > pageSize">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredTrackingNumbers.length"
        />
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-actions" v-if="selectedRows.length > 0">
      <div class="batch-info">
        已选择 {{ selectedRows.length }} 条记录
      </div>
      <div class="batch-buttons">
        <el-button
          type="primary"
          size="small"
          @click="batchQuery"
          :loading="batchQuerying"
        >
          批量查询
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="batchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogMode === 'add' ? '添加快递单号' : '编辑快递单号'"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="resetDialog"
    >
      <el-form
        ref="trackingForm"
        :model="trackingForm"
        :rules="trackingRules"
        label-width="100px"
      >
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="trackingForm.trackingNumber"
            placeholder="请输入快递单号"
            maxlength="100"
            show-word-limit
            :disabled="dialogMode === 'edit'"
          />
        </el-form-item>

        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-select
            v-model="trackingForm.logisticsCompany"
            placeholder="请选择物流公司（可选）"
            filterable
            allow-create
            clearable
            style="width: 100%"
          >
            <el-option label="顺丰速运" value="顺丰速运"></el-option>
            <el-option label="圆通速递" value="圆通速递"></el-option>
            <el-option label="中通快递" value="中通快递"></el-option>
            <el-option label="申通快递" value="申通快递"></el-option>
            <el-option label="韵达速递" value="韵达速递"></el-option>
            <el-option label="京东快递" value="京东快递"></el-option>
            <el-option label="德邦快递" value="德邦快递"></el-option>
            <el-option label="邮政EMS" value="邮政EMS"></el-option>
            <el-option label="极兔速递" value="极兔速递"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="trackingForm.remark"
            type="textarea"
            :rows="3"
            placeholder="可选，添加备注信息"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveTracking" :loading="saving">
          {{ dialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 物流详情对话框 -->
    <el-dialog
      title="物流详情"
      :visible.sync="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTracking">
        <!-- 基本信息 -->
        <div class="logistics-info">
          <div class="info-card">
            <div class="card-header">
              <div class="logistics-company">
                <i class="el-icon-truck"></i>
                <span>{{ selectedTracking.logisticsCompany || '未知物流公司' }}</span>
              </div>
              <div class="tracking-number">
                <span>快递单号：</span>
                <code>{{ selectedTracking.trackingNumber }}</code>
              </div>
            </div>

            <div class="status-info">
              <div class="current-status">
                <el-tag
                  :type="getStatusColor(selectedTracking.status)"
                  size="medium"
                  class="status-tag"
                >
                  {{ getStatusText(selectedTracking.status) }}
                </el-tag>
              </div>

              <div class="update-time" v-if="selectedTracking.lastUpdateTime">
                <span>最后更新：{{ formatDate(selectedTracking.lastUpdateTime) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 物流轨迹 -->
        <div class="tracking-timeline" v-if="selectedTracking.trackingDetails && selectedTracking.trackingDetails.length > 0">
          <h5>物流轨迹</h5>
          <el-timeline>
            <el-timeline-item
              v-for="(detail, index) in selectedTracking.trackingDetails"
              :key="index"
              :timestamp="formatDate(detail.time)"
              :type="getTimelineType(detail, index)"
            >
              <div class="timeline-content">
                <div class="description">{{ detail.description }}</div>
                <div class="location" v-if="detail.location">
                  <i class="el-icon-location-outline"></i>
                  <span>{{ detail.location }}</span>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="refreshSelectedTracking" :loading="refreshingDetail">
          刷新物流信息
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { queryLogisticsTracking, saveLogisticsInfo } from '@/api/jenasi/purchaseOrderEnhanced'
import { validateTrackingNumber } from '@/utils/logistics-helper'

export default {
  name: 'TrackingNumberManager',
  data() {
    return {
      // 数据列表
      trackingNumbers: [],
      filteredTrackingNumbers: [],

      // 搜索和分页
      searchKeyword: '',
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 加载状态
      loading: false,
      refreshing: false,
      saving: false,
      refreshingDetail: false,

      // 对话框状态
      dialogVisible: false,
      detailDialogVisible: false,
      dialogMode: 'add', // 'add' | 'edit'

      // 表单数据
      trackingForm: {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      },

      // 选中的记录
      selectedTracking: null,

      // 表单验证规则
      trackingRules: {
        trackingNumber: [
          { required: true, message: '请输入快递单号', trigger: 'blur' },
          { min: 5, max: 100, message: '快递单号长度在 5 到 100 个字符', trigger: 'blur' },
          { validator: this.validateTrackingNumberFormat, trigger: 'blur' }
        ],
        logisticsCompany: [
          { max: 50, message: '物流公司名称不能超过50个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    // 分页后的数据
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredTrackingNumbers.slice(start, end)
    }
  },

  mounted() {
    this.loadTrackingNumbers()
  },

  methods: {
    /**
     * 加载快递单号列表 - 增强版本
     */
    async loadTrackingNumbers() {
      this.loading = true
      try {
        // 优先从后端加载数据
        await this.loadFromServer()

        // 如果后端没有数据，从本地存储加载
        if (this.trackingNumbers.length === 0) {
          this.loadFromLocalStorage()
        }

        this.filteredTrackingNumbers = [...this.trackingNumbers]
        this.total = this.trackingNumbers.length
      } catch (error) {
        console.error('加载快递单号列表失败:', error)
        // 降级到本地存储
        this.loadFromLocalStorage()
      } finally {
        this.loading = false
      }
    },

    /**
     * 从服务器加载数据
     */
    async loadFromServer() {
      try {
        // 这里需要实现后端API来获取快递单号列表
        // const response = await getTrackingNumberList()
        // if (response.code === 200) {
        //   this.trackingNumbers = response.data || []
        // }

        // 暂时使用本地存储，后续可以替换为后端API
        this.loadFromLocalStorage()
      } catch (error) {
        console.warn('从服务器加载数据失败，使用本地存储:', error)
        this.loadFromLocalStorage()
      }
    },

    /**
     * 从本地存储加载数据
     */
    loadFromLocalStorage() {
      try {
        const stored = localStorage.getItem('cp-mes-tracking-numbers')
        if (stored) {
          this.trackingNumbers = JSON.parse(stored)
        }
      } catch (error) {
        console.error('从本地存储加载数据失败:', error)
        this.trackingNumbers = []
      }
    },

    /**
     * 保存快递单号列表
     */
    async saveTrackingNumbers() {
      try {
        // 保存到本地存储
        localStorage.setItem('cp-mes-tracking-numbers', JSON.stringify(this.trackingNumbers))

        // 如果有后端API，也保存到服务器
        // await saveTrackingNumberList(this.trackingNumbers)

      } catch (error) {
        console.error('保存快递单号列表失败:', error)
        this.$message.error('保存数据失败')
      }
    },

    /**
     * 搜索处理
     */
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredTrackingNumbers = [...this.trackingNumbers]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredTrackingNumbers = this.trackingNumbers.filter(item =>
          item.trackingNumber.toLowerCase().includes(keyword) ||
          (item.logisticsCompany && item.logisticsCompany.toLowerCase().includes(keyword)) ||
          (item.remark && item.remark.toLowerCase().includes(keyword))
        )
      }
      this.currentPage = 1 // 重置到第一页
    },

    /**
     * 显示添加对话框
     */
    showAddDialog() {
      this.dialogMode = 'add'
      this.trackingForm = {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      }
      this.dialogVisible = true
    },

    /**
     * 编辑快递单号
     */
    editTracking(row) {
      this.dialogMode = 'edit'
      this.trackingForm = {
        trackingNumber: row.trackingNumber,
        logisticsCompany: row.logisticsCompany || '',
        remark: row.remark || ''
      }
      this.dialogVisible = true
    },

    /**
     * 保存快递单号 - 增强版本
     */
    async saveTracking() {
      try {
        await this.$refs.trackingForm.validate()

        this.saving = true

        const trackingData = {
          trackingNumber: this.trackingForm.trackingNumber.trim().toUpperCase(),
          logisticsCompany: this.trackingForm.logisticsCompany.trim(),
          remark: this.trackingForm.remark.trim(),
          createTime: new Date().toISOString(),
          lastUpdateTime: null,
          status: 'PENDING',
          statusDescription: '待查询',
          querySuccess: false,
          errorMessage: null
        }

        if (this.dialogMode === 'add') {
          // 检查是否已存在
          const exists = this.trackingNumbers.some(item =>
            item.trackingNumber === trackingData.trackingNumber
          )

          if (exists) {
            this.$message.error('该快递单号已存在')
            return
          }

          this.trackingNumbers.unshift(trackingData)
          this.$message.success('添加成功')

          // 自动查询新添加的快递单号
          setTimeout(() => {
            this.queryTracking(trackingData)
          }, 500)

        } else {
          // 编辑模式
          const index = this.trackingNumbers.findIndex(item =>
            item.trackingNumber === this.trackingForm.trackingNumber
          )

          if (index !== -1) {
            // 保留查询结果，只更新可编辑字段
            this.trackingNumbers[index] = {
              ...this.trackingNumbers[index],
              logisticsCompany: trackingData.logisticsCompany,
              remark: trackingData.remark
            }
            this.$message.success('修改成功')
          }
        }

        await this.saveTrackingNumbers()
        this.handleSearch() // 重新过滤
        this.dialogVisible = false
        this.resetDialog()

      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('保存快递单号失败:', error)
          this.$message.error('保存失败: ' + error.message)
        }
      } finally {
        this.saving = false
      }
    },

    /**
     * 删除快递单号
     */
    async deleteTracking(row) {
      try {
        await this.$confirm(`确定要删除快递单号 "${row.trackingNumber}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const index = this.trackingNumbers.findIndex(item =>
          item.trackingNumber === row.trackingNumber
        )

        if (index !== -1) {
          this.trackingNumbers.splice(index, 1)
          this.saveTrackingNumbers()
          this.handleSearch() // 重新过滤
          this.$message.success('删除成功')
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    /**
     * 查询单个快递单号 - 增强版本
     */
    async queryTracking(row) {
      this.$set(row, 'querying', true)

      try {
        const response = await queryLogisticsTracking(row.trackingNumber, row.logisticsCompany)

        if (response && (response.code === 200 || response.code === 0)) {
          // 更新记录
          const index = this.trackingNumbers.findIndex(item =>
            item.trackingNumber === row.trackingNumber
          )

          if (index !== -1) {
            // 合并数据，保留原有的创建时间和备注
            this.trackingNumbers[index] = {
              ...this.trackingNumbers[index],
              ...response.data,
              lastUpdateTime: new Date().toISOString(),
              querySuccess: response.data.querySuccess || false,
              // 保留原有字段
              createTime: this.trackingNumbers[index].createTime,
              remark: this.trackingNumbers[index].remark
            }

            await this.saveTrackingNumbers()
            this.handleSearch() // 重新过滤

            if (response.data.querySuccess) {
              this.$message.success(`查询成功：${response.data.statusDescription || response.data.status}`)
            } else {
              this.$message.warning(response.data.errorMessage || '查询失败，但记录已更新')
            }
          }
        } else {
          this.$message.error(response.msg || '查询失败')
        }

      } catch (error) {
        console.error(`查询 ${row.trackingNumber} 失败:`, error)
        this.$message.error(`查询失败: ${error.message}`)

        // 更新失败状态
        const index = this.trackingNumbers.findIndex(item =>
          item.trackingNumber === row.trackingNumber
        )
        if (index !== -1) {
          this.trackingNumbers[index].status = 'QUERY_FAILED'
          this.trackingNumbers[index].errorMessage = error.message
          this.trackingNumbers[index].lastUpdateTime = new Date().toISOString()
          await this.saveTrackingNumbers()
        }
      } finally {
        this.$set(row, 'querying', false)
      }
    },

    /**
     * 批量刷新 - 增强版本（包含缓存清理）
     */
    async refreshAll() {
      if (this.trackingNumbers.length === 0) {
        this.$message.info('没有快递单号需要刷新')
        return
      }

      try {
        await this.$confirm('确定要批量刷新所有快递单号并清理缓存吗？这可能需要一些时间。', '确认批量刷新', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
      } catch {
        return // 用户取消
      }

      this.refreshing = true
      let successCount = 0
      let failCount = 0
      const total = this.trackingNumbers.length

      // 显示详细的加载提示
      const loading = this.$loading({
        lock: true,
        text: `正在批量刷新并清理缓存... (0/${total})`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始批量刷新物流信息并清理缓存，总数:', total)

        // 1. 先清理所有相关的本地缓存
        this.clearAllLogisticsLocalCache()

        for (let i = 0; i < this.trackingNumbers.length; i++) {
          const tracking = this.trackingNumbers[i]

          // 更新加载提示
          loading.text = `正在批量刷新并清理缓存... (${i + 1}/${total})`

          try {
            // 显示进度
            this.$message.info(`正在查询 ${i + 1}/${total}: ${tracking.trackingNumber}`)

            const response = await queryLogisticsTracking(tracking.trackingNumber, tracking.logisticsCompany)

            if (response && (response.code === 200 || response.code === 0)) {
              Object.assign(tracking, {
                ...response.data,
                lastUpdateTime: new Date().toISOString(),
                querySuccess: response.data.querySuccess || false,
                // 保留原有字段
                createTime: tracking.createTime,
                remark: tracking.remark
              })
              successCount++
            } else {
              tracking.status = 'QUERY_FAILED'
              tracking.errorMessage = response.msg || '查询失败'
              tracking.lastUpdateTime = new Date().toISOString()
              failCount++
            }
          } catch (error) {
            console.error(`查询 ${tracking.trackingNumber} 失败:`, error)
            tracking.status = 'QUERY_FAILED'
            tracking.errorMessage = error.message
            tracking.lastUpdateTime = new Date().toISOString()
            failCount++
          }

          // 清理单个快递单号的后端缓存
          try {
            await this.clearBackendCache(tracking.trackingNumber)
          } catch (error) {
            console.warn(`清理 ${tracking.trackingNumber} 后端缓存失败:`, error)
          }

          // 添加延迟避免请求过于频繁
          if (i < this.trackingNumbers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }

        await this.saveTrackingNumbers()
        this.handleSearch() // 重新过滤

        loading.close()
        this.$message.success(`批量刷新完成：成功 ${successCount} 条，失败 ${failCount} 条，缓存已清理`)
        console.log('批量刷新和缓存清理完成:', { successCount, failCount, total })

      } catch (error) {
        loading.close()
        console.error('批量刷新失败:', error)
        this.$message.error('批量刷新过程中发生错误')
      } finally {
        this.refreshing = false
      }
    },

    /**
     * 清理所有物流相关的本地缓存
     */
    clearAllLogisticsLocalCache() {
      try {
        console.log('开始清理所有本地缓存')

        // 获取所有localStorage键
        const allKeys = Object.keys(localStorage)
        const logisticsKeys = allKeys.filter(key =>
          key.includes('logistics') ||
          key.includes('tracking') ||
          key.includes('cp-mes-logistics') ||
          key.includes('cp-mes-tracking')
        )

        // 清理localStorage
        logisticsKeys.forEach(key => {
          try {
            localStorage.removeItem(key)
            console.debug('清理localStorage键:', key)
          } catch (error) {
            console.warn('清理localStorage键失败:', key, error)
          }
        })

        // 获取所有sessionStorage键
        const allSessionKeys = Object.keys(sessionStorage)
        const logisticsSessionKeys = allSessionKeys.filter(key =>
          key.includes('logistics') ||
          key.includes('tracking') ||
          key.includes('cp-mes-logistics') ||
          key.includes('cp-mes-tracking')
        )

        // 清理sessionStorage
        logisticsSessionKeys.forEach(key => {
          try {
            sessionStorage.removeItem(key)
            console.debug('清理sessionStorage键:', key)
          } catch (error) {
            console.warn('清理sessionStorage键失败:', key, error)
          }
        })

        console.log('所有本地缓存清理完成')
      } catch (error) {
        console.error('清理所有本地缓存失败:', error)
      }
    },

    /**
     * 清理单个快递单号的本地缓存
     */
    clearLogisticsLocalCache(trackingNumber) {
      try {
        console.log('开始清理本地缓存:', trackingNumber)

        const storageKeys = [
          'cp-mes-logistics-data',
          'cp-mes-tracking-numbers',
          `logistics-${trackingNumber}`,
          `tracking-${trackingNumber}`,
          `tracking-manager-${trackingNumber}`,
          'logistics-query-history'
        ]

        storageKeys.forEach(key => {
          try {
            localStorage.removeItem(key)
            sessionStorage.removeItem(key)
            console.debug('清理缓存键:', key)
          } catch (error) {
            console.warn('清理缓存键失败:', key, error)
          }
        })

        console.log('本地缓存清理完成:', trackingNumber)
      } catch (error) {
        console.error('清理本地缓存失败:', error)
      }
    },

    /**
     * 清理后端缓存
     */
    async clearBackendCache(trackingNumber) {
      try {
        // 调用后端缓存清理接口
        const response = await this.$http.delete(`/api/logistics/cache/${trackingNumber}`)
        console.log('后端缓存清理成功:', trackingNumber, response)
      } catch (error) {
        console.warn('后端缓存清理接口调用失败:', trackingNumber, error)
        throw error
      }
    },

    /**
     * 复制快递单号
     */
    async copyTrackingNumber(trackingNumber) {
      try {
        await navigator.clipboard.writeText(trackingNumber)
        this.$message.success('快递单号已复制到剪贴板!')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = trackingNumber
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          this.$message.success('快递单号已复制到剪贴板!')
        } catch (e) {
          this.$message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textArea)
      }
    },

    /**
     * 分页处理
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    handleCurrentChange(val) {
      this.currentPage = val
    },

    /**
     * 重置对话框
     */
    resetDialog() {
      this.trackingForm = {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      }

      this.$nextTick(() => {
        if (this.$refs.trackingForm) {
          this.$refs.trackingForm.clearValidate()
        }
      })
    },

    /**
     * 快递单号格式验证
     */
    validateTrackingNumberFormat(rule, value, callback) {
      if (value && !validateTrackingNumber(value)) {
        callback(new Error('快递单号格式不正确'))
      } else {
        callback()
      }
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'SIGNED': '已签收',
        'IN_TRANSIT': '运输中',
        'PICKED_UP': '已揽收',
        'NO_INFO': '暂无信息',
        'QUERY_FAILED': '查询失败',
        'PENDING': '待查询'
      }
      return statusMap[status] || status || '未知'
    },

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
      const colorMap = {
        'SIGNED': 'success',
        'IN_TRANSIT': 'primary',
        'PICKED_UP': 'warning',
        'NO_INFO': 'info',
        'QUERY_FAILED': 'danger',
        'PENDING': 'info'
      }
      return colorMap[status] || 'info'
    },

    /**
     * 获取时间线类型
     */
    getTimelineType(detail, index) {
      if (index === 0) return 'primary'
      return 'info'
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      if (!date) return '未知时间'

      const d = new Date(date)
      if (isNaN(d.getTime())) return '时间格式错误'

      return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    /**
     * 获取空状态文本
     */
    getEmptyText() {
      if (this.searchKeyword) {
        return '未找到匹配的快递单号'
      }
      return '暂无快递单号记录，点击"添加快递单号"开始使用'
    },

    /**
     * 导出数据
     */
    exportData() {
      if (this.trackingNumbers.length === 0) {
        this.$message.info('暂无数据可导出')
        return
      }

      try {
        const exportData = this.trackingNumbers.map(item => ({
          快递单号: item.trackingNumber,
          物流公司: item.logisticsCompany || '',
          状态: this.getStatusText(item.status),
          最后更新: item.lastUpdateTime ? this.formatDate(item.lastUpdateTime) : '',
          备注: item.remark || ''
        }))

        const csvContent = this.convertToCSV(exportData)
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')

        if (link.download !== undefined) {
          const url = URL.createObjectURL(blob)
          link.setAttribute('href', url)
          link.setAttribute('download', `快递单号列表_${new Date().toISOString().slice(0, 10)}.csv`)
          link.style.visibility = 'hidden'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },

    /**
     * 转换为CSV格式
     */
    convertToCSV(data) {
      if (!data || data.length === 0) return ''

      const headers = Object.keys(data[0])
      const csvRows = []

      // 添加BOM以支持中文
      csvRows.push('\uFEFF')

      // 添加标题行
      csvRows.push(headers.join(','))

      // 添加数据行
      for (const row of data) {
        const values = headers.map(header => {
          const value = row[header] || ''
          // 处理包含逗号或引号的值
          return `"${value.toString().replace(/"/g, '""')}"`
        })
        csvRows.push(values.join(','))
      }

      return csvRows.join('\n')
    },

    /**
     * 导入数据
     */
    importData() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.csv,.txt'

      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return

        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const content = e.target.result
            this.parseImportData(content)
          } catch (error) {
            console.error('导入失败:', error)
            this.$message.error('文件解析失败')
          }
        }
        reader.readAsText(file, 'UTF-8')
      }

      input.click()
    },

    /**
     * 解析导入数据
     */
    parseImportData(content) {
      const lines = content.split('\n').filter(line => line.trim())
      if (lines.length < 2) {
        this.$message.error('文件格式不正确')
        return
      }

      const importedData = []
      let successCount = 0
      let skipCount = 0

      // 跳过标题行
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim()
        if (!line) continue

        const values = line.split(',').map(v => v.replace(/^"|"$/g, '').replace(/""/g, '"'))

        if (values.length >= 1) {
          const trackingNumber = values[0].trim()

          if (trackingNumber && !this.trackingNumbers.some(item => item.trackingNumber === trackingNumber)) {
            importedData.push({
              trackingNumber: trackingNumber,
              logisticsCompany: values[1] ? values[1].trim() : '',
              remark: values[4] ? values[4].trim() : '',
              createTime: new Date().toISOString(),
              lastUpdateTime: null,
              status: 'PENDING',
              querySuccess: false
            })
            successCount++
          } else {
            skipCount++
          }
        }
      }

      if (importedData.length > 0) {
        this.trackingNumbers.unshift(...importedData)
        this.saveTrackingNumbers()
        this.handleSearch()
        this.$message.success(`导入成功 ${successCount} 条，跳过重复 ${skipCount} 条`)
      } else {
        this.$message.info('没有新的数据需要导入')
      }
    },

    /**
     * 获取状态统计
     */
    getStatusCount(status) {
      return this.trackingNumbers.filter(item => item.status === status).length
    },

    /**
     * 处理选择变化
     */
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    /**
     * 批量查询
     */
    async batchQuery() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要查询的快递单号')
        return
      }

      try {
        await this.$confirm(`确定要查询选中的 ${this.selectedRows.length} 个快递单号吗？`, '确认批量查询', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
      } catch {
        return
      }

      this.batchQuerying = true
      let successCount = 0
      let failCount = 0

      try {
        for (const row of this.selectedRows) {
          try {
            await this.queryTracking(row)
            successCount++
          } catch (error) {
            failCount++
          }
          // 添加延迟
          await new Promise(resolve => setTimeout(resolve, 800))
        }

        this.$message.success(`批量查询完成：成功 ${successCount} 条，失败 ${failCount} 条`)
      } finally {
        this.batchQuerying = false
      }
    },

    /**
     * 批量删除
     */
    async batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要删除的快递单号')
        return
      }

      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 个快递单号吗？此操作不可恢复！`, '确认批量删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 删除选中的记录
        const trackingNumbersToDelete = this.selectedRows.map(row => row.trackingNumber)
        this.trackingNumbers = this.trackingNumbers.filter(item =>
          !trackingNumbersToDelete.includes(item.trackingNumber)
        )

        await this.saveTrackingNumbers()
        this.handleSearch()
        this.$message.success(`成功删除 ${trackingNumbersToDelete.length} 条记录`)

      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败')
        }
      }
    },

    /**
     * 导出数据
     */
    exportData() {
      try {
        const data = this.trackingNumbers.map(item => ({
          '快递单号': item.trackingNumber,
          '物流公司': item.company || item.logisticsCompany || '未知',
          '状态': this.getStatusText(item.status),
          '状态描述': item.statusDescription || '',
          '最后更新': item.lastUpdateTime ? this.formatDate(item.lastUpdateTime) : '未查询',
          '备注': item.remark || ''
        }))

        // 简单的CSV导出
        const csvContent = this.convertToCSV(data)
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `快递单号管理_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('数据导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$message.error('导出数据失败')
      }
    },

    /**
     * 转换为CSV格式
     */
    convertToCSV(data) {
      if (data.length === 0) return ''

      const headers = Object.keys(data[0])
      const csvRows = []

      // 添加表头
      csvRows.push(headers.join(','))

      // 添加数据行
      for (const row of data) {
        const values = headers.map(header => {
          const value = row[header] || ''
          // 处理包含逗号的值
          return `"${value.toString().replace(/"/g, '""')}"`
        })
        csvRows.push(values.join(','))
      }

      return csvRows.join('\n')
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    /**
     * 当前页变化
     */
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped>
.tracking-number-manager {
  padding: 20px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.record-count {
  font-size: 14px;
  color: #909399;
}

.search-result {
  color: #409eff;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stats-bar {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

.stat-value.success { color: #67c23a; }
.stat-value.primary { color: #409eff; }
.stat-value.danger { color: #f56c6c; }
.stat-value.info { color: #909399; }

.tracking-number-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tracking-code {
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.manual-company {
  color: #909399;
  font-style: italic;
}

.no-company, .no-description, .no-remark, .no-update {
  color: #c0c4cc;
  font-style: italic;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
}

.status-loading {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 1000;
}

.batch-info {
  font-size: 14px;
  color: #606266;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}
</style>

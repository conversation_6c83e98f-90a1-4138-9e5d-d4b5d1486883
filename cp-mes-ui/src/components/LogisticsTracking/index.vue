<template>
  <div class="logistics-tracking">
    <!-- 物流信息显示区域 -->
    <div v-if="hasLogistics" class="logistics-info">
      <div class="logistics-header">
        <div class="tracking-info">
          <h3>物流追踪信息</h3>
          <div class="basic-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">快递单号：</span>
                  <span class="value">{{ logisticsData.trackingNumber }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">物流公司：</span>
                  <span class="value">{{ logisticsData.company || '系统识别中' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="label">当前状态：</span>
                  <el-tag :type="getStatusType(logisticsData.status)" size="small">
                    {{ logisticsData.statusDescription || '未知状态' }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-refresh"
            :loading="refreshing"
            @click="refreshLogistics"
            title="刷新物流信息并清理缓存">
            刷新并清理缓存
          </el-button>
          <el-button
            type="default"
            size="small"
            icon="el-icon-edit"
            @click="showSetupDialog">
            修改
          </el-button>
          <el-button
            type="danger"
            size="small"
            icon="el-icon-delete"
            @click="handleDelete"
            v-if="logisticsData.trackingNumber">
            删除
          </el-button>
        </div>
      </div>

      <!-- 物流轨迹时间线 -->
      <div class="logistics-timeline" v-if="trackingDetails && trackingDetails.length > 0">
        <h4>物流轨迹</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(detail, index) in trackingDetails"
            :key="index"
            :timestamp="formatDate(detail.time)"
            :type="getTimelineType(detail, index)"
            :color="getTimelineColor(detail)"
            placement="top">
            <div class="timeline-content">
              <p class="description">{{ detail.description }}</p>
              <div class="detail-info" v-if="detail.location">
                <span class="location">{{ detail.location }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 无轨迹信息提示 -->
      <div v-else class="no-tracking-info">
        <el-empty description="暂无物流轨迹信息">
          <el-button type="primary" @click="refreshLogistics" :loading="refreshing" title="重新查询并清理缓存">
            重新查询并清理缓存
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 未设置物流信息时的提示 -->
    <div v-else class="no-logistics">
      <el-empty description="暂未设置物流信息">
        <el-button type="primary" @click="showSetupDialog">
          设置物流信息
        </el-button>
        <el-button type="success" @click="showAddDialog" style="margin-left: 10px;">
          添加快递单号
        </el-button>
      </el-empty>
    </div>

    <!-- 设置物流信息对话框 -->
    <el-dialog
      title="设置物流信息"
      :visible.sync="setupDialogVisible"
      width="500px"
      :close-on-click-modal="false">

      <el-form
        ref="logisticsForm"
        :model="logisticsForm"
        :rules="logisticsRules"
        label-width="100px">

        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="logisticsForm.trackingNumber"
            placeholder="请输入快递单号"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-input
            v-model="logisticsForm.logisticsCompany"
            placeholder="可选，系统将自动识别"
            clearable>
            <template slot="append">
              <span style="color: #909399; font-size: 12px;">可选</span>
            </template>
          </el-input>
          <div class="form-tip">
            <i class="el-icon-info"></i>
            物流公司为可选参数，系统会根据快递单号自动识别
          </div>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSetup">取 消</el-button>
        <el-button type="primary" @click="saveLogistics" :loading="saving">
          保存并查询
        </el-button>
      </span>
    </el-dialog>

    <!-- 添加快递单号对话框 -->
    <el-dialog
      title="添加快递单号"
      :visible.sync="addDialogVisible"
      width="500px"
      :close-on-click-modal="false">
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="addRules"
        label-width="100px">
        <el-form-item label="快递单号" prop="trackingNumber">
          <el-input
            v-model="addForm.trackingNumber"
            placeholder="请输入快递单号"
            clearable
            @input="handleTrackingNumberInput">
          </el-input>
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-select
            v-model="addForm.logisticsCompany"
            placeholder="请选择物流公司（可自动识别）"
            clearable
            filterable>
            <el-option
              v-for="company in logisticsCompanies"
              :key="company.code"
              :label="company.name"
              :value="company.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="addForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAddSubmit" :loading="addLoading">
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveLogisticsInfo,
  queryLogisticsTracking
} from '@/api/jenasi/purchaseOrderEnhanced'

export default {
  name: 'LogisticsTracking',
  props: {
    purchaseOrderId: {
      type: [Number, String],
      required: true
    },
    initialTrackingNumber: {
      type: String,
      default: ''
    },
    initialLogisticsCompany: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      logisticsData: null,
      trackingDetails: [],
      setupDialogVisible: false,
      addDialogVisible: false,
      refreshing: false,
      saving: false,
      addLoading: false,
      logisticsForm: {
        trackingNumber: '',
        logisticsCompany: ''
      },
      addForm: {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      },
      logisticsRules: {
        trackingNumber: [
          { required: true, message: '请输入快递单号', trigger: 'blur' },
          { min: 5, max: 100, message: '快递单号长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        logisticsCompany: [
          // 物流公司为可选参数，不设置必填验证
          { max: 50, message: '物流公司名称不能超过50个字符', trigger: 'blur' }
        ]
      },
      addRules: {
        trackingNumber: [
          { required: true, message: '请输入快递单号', trigger: 'blur' },
          { min: 8, max: 30, message: '快递单号长度应在8-30位之间', trigger: 'blur' }
        ]
      },
      logisticsCompanies: [
        { code: 'SF', name: '顺丰速运' },
        { code: 'YTO', name: '圆通速递' },
        { code: 'ZTO', name: '中通快递' },
        { code: 'STO', name: '申通快递' },
        { code: 'YD', name: '韵达速递' },
        { code: 'HTKY', name: '百世快递' },
        { code: 'DBL', name: '德邦快递' },
        { code: 'JD', name: '京东快递' },
        { code: 'EMS', name: '中国邮政' }
      ]
    }
  },
  computed: {
    hasLogistics() {
      return this.logisticsData && this.logisticsData.trackingNumber
    }
  },
  mounted() {
    this.initLogistics()
  },
  watch: {
    purchaseOrderId: {
      handler() {
        this.initLogistics()
      },
      immediate: true
    },
    initialTrackingNumber: {
      handler(newVal) {
        if (newVal && !this.hasLogistics) {
          this.loadLogisticsTracking(newVal)
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 初始化物流信息
     */
    initLogistics() {
      if (this.initialTrackingNumber) {
        this.loadLogisticsTracking(this.initialTrackingNumber)
      }
    },

    /**
     * 加载物流追踪信息 - 支持可选物流公司
     */
    async loadLogisticsTracking(trackingNumber) {
      if (!trackingNumber) return

      try {
        console.log('开始查询物流信息:', trackingNumber)

        // 调用API时物流公司参数可选
        const response = await queryLogisticsTracking(trackingNumber, this.initialLogisticsCompany || null)
        console.log('物流查询响应:', response)

        if (response.code === 200 || response.code === 0) {
          this.logisticsData = response.data
          this.trackingDetails = response.data.trackingDetails || []
          console.log('物流数据:', this.logisticsData)
          console.log('物流轨迹:', this.trackingDetails)

          // 处理查询失败的情况
          if (!response.data.querySuccess) {
            this.$message.warning(response.data.errorMessage || '物流查询失败')
          }

          // 根据状态显示不同的提示
          this.handleLogisticsStatus(response.data.status)
        } else {
          console.warn('获取物流信息失败:', response.msg)
          // 如果查询失败，至少显示基本信息
          this.logisticsData = {
            trackingNumber: trackingNumber,
            company: this.initialLogisticsCompany || '系统识别中',
            status: 'QUERY_FAILED',
            statusDescription: response.msg || '无法获取物流信息',
            querySuccess: false,
            errorMessage: response.msg
          }
          this.trackingDetails = []
        }
      } catch (error) {
        console.error('加载物流信息失败:', error)
        this.$message.error('查询物流信息失败: ' + error.message)
        // 显示基本信息，即使查询失败
        this.logisticsData = {
          trackingNumber: trackingNumber,
          company: this.initialLogisticsCompany || '系统识别中',
          status: 'QUERY_FAILED',
          statusDescription: '网络异常或服务不可用',
          querySuccess: false,
          errorMessage: error.message
        }
        this.trackingDetails = []
      }
    },

    /**
     * 显示设置对话框
     */
    showSetupDialog() {
      this.logisticsForm = {
        trackingNumber: this.initialTrackingNumber || '',
        logisticsCompany: this.initialLogisticsCompany || ''
      }
      this.setupDialogVisible = true
    },

    /**
     * 显示添加快递单号对话框
     */
    showAddDialog() {
      this.addForm = {
        trackingNumber: '',
        logisticsCompany: '',
        remark: ''
      }
      this.addDialogVisible = true
    },

    /**
     * 处理快递单号输入，自动识别物流公司
     */
    handleTrackingNumberInput(value) {
      if (!value || value.length < 8) return

      // 根据快递单号前缀自动识别物流公司
      if (value.startsWith('SF') || value.startsWith('sf')) {
        this.addForm.logisticsCompany = 'SF'
      } else if (value.startsWith('YT') || value.startsWith('yt')) {
        this.addForm.logisticsCompany = 'YTO'
      } else if (value.startsWith('ZT') || value.startsWith('zt')) {
        this.addForm.logisticsCompany = 'ZTO'
      } else if (value.startsWith('ST') || value.startsWith('st')) {
        this.addForm.logisticsCompany = 'STO'
      } else if (value.startsWith('YD') || value.startsWith('yd')) {
        this.addForm.logisticsCompany = 'YD'
      } else if (value.startsWith('JD') || value.startsWith('jd')) {
        this.addForm.logisticsCompany = 'JD'
      }
    },

    /**
     * 保存物流信息 - 支持可选物流公司
     */
    async saveLogistics() {
      try {
        await this.$refs.logisticsForm.validate()

        this.saving = true

        // 保存物流信息（物流公司可选）
        const saveResponse = await saveLogisticsInfo(
          this.purchaseOrderId,
          this.logisticsForm.trackingNumber,
          this.logisticsForm.logisticsCompany || '' // 允许空值
        )

        if (saveResponse.code === 200 || saveResponse.code === 0) {
          this.$message.success('物流信息保存成功!')
          this.setupDialogVisible = false

          // 立即查询物流信息（不强制要求物流公司）
          await this.loadLogisticsTracking(this.logisticsForm.trackingNumber)

          this.$emit('logistics-updated', {
            trackingNumber: this.logisticsForm.trackingNumber,
            logisticsCompany: this.logisticsForm.logisticsCompany || ''
          })
        } else {
          this.$message.error(saveResponse.msg || '保存失败')
        }

      } catch (error) {
        console.error('保存物流信息失败:', error)
        this.$message.error('保存失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },

    /**
     * 取消设置
     */
    cancelSetup() {
      this.setupDialogVisible = false
      this.resetForm()
    },

    /**
     * 刷新物流信息（增强版：包含缓存清理）
     */
    async refreshLogistics() {
      if (!this.logisticsData || !this.logisticsData.trackingNumber) {
        this.$message.warning('请先设置物流信息')
        return
      }

      this.refreshing = true
      const trackingNumber = this.logisticsData.trackingNumber

      // 显示详细的加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在刷新并清理缓存...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始刷新物流信息并清理缓存:', trackingNumber)

        // 1. 清理前端本地缓存
        this.clearLogisticsLocalCache(trackingNumber)

        // 2. 调用后端缓存清理API（如果存在）
        try {
          await this.clearBackendCache(trackingNumber)
        } catch (error) {
          console.warn('后端缓存清理失败:', error)
          // 不影响主流程，继续执行
        }

        // 3. 执行原有的数据刷新操作
        await this.loadLogisticsTracking(trackingNumber)

        loading.close()
        this.$message.success('刷新成功，缓存已清理')
        console.log('物流信息刷新和缓存清理完成:', trackingNumber)

      } catch (error) {
        loading.close()
        console.error('刷新物流信息失败:', error)
        this.$message.error('刷新失败: ' + error.message)
      } finally {
        this.refreshing = false
      }
    },

    /**
     * 提交新增快递单号
     */
    async handleAddSubmit() {
      try {
        await this.$refs.addForm.validate()
      } catch (error) {
        return
      }

      this.addLoading = true

      try {
        console.log('开始添加快递单号:', this.addForm)

        // 调用后端API保存快递单号
        const response = await this.$http.post('/api/tracking-number/add', {
          trackingNumber: this.addForm.trackingNumber,
          logisticsCompany: this.addForm.logisticsCompany,
          remark: this.addForm.remark,
          purchaseOrderId: this.purchaseOrderId
        })

        if (response.code === 200 || response.code === 0) {
          this.$message.success('快递单号添加成功')

          // 清理相关缓存
          this.clearLogisticsLocalCache(this.addForm.trackingNumber)

          // 设置为当前物流信息并查询
          this.logisticsData = {
            trackingNumber: this.addForm.trackingNumber,
            logisticsCompany: this.addForm.logisticsCompany
          }

          // 立即查询物流信息
          await this.loadLogisticsTracking(this.addForm.trackingNumber)

          // 关闭对话框
          this.addDialogVisible = false

          // 触发父组件刷新
          this.$emit('refresh')

          console.log('快递单号添加成功:', this.addForm.trackingNumber)
        } else {
          this.$message.error(response.msg || '添加失败')
        }
      } catch (error) {
        console.error('添加快递单号失败:', error)
        this.$message.error('添加失败: ' + (error.message || '未知错误'))
      } finally {
        this.addLoading = false
      }
    },

    /**
     * 删除快递单号
     */
    async handleDelete() {
      if (!this.logisticsData || !this.logisticsData.trackingNumber) {
        this.$message.warning('没有可删除的快递单号')
        return
      }

      try {
        await this.$confirm('确认删除该快递单号吗？删除后将清理所有相关缓存数据。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const trackingNumber = this.logisticsData.trackingNumber

        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在删除快递单号并清理缓存...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          // 调用后端删除API
          const response = await this.$http.delete(`/api/tracking-number/delete-by-number/${trackingNumber}`)

          if (response.code === 200 || response.code === 0) {
            // 清理前端缓存
            this.clearLogisticsLocalCache(trackingNumber)

            // 清理后端缓存
            try {
              await this.clearBackendCache(trackingNumber)
            } catch (error) {
              console.warn('后端缓存清理失败:', error)
            }

            // 重置组件状态
            this.logisticsData = null
            this.trackingDetails = []

            loading.close()
            this.$message.success('删除成功，缓存已清理')

            // 触发父组件刷新
            this.$emit('refresh')

            console.log('快递单号删除成功:', trackingNumber)
          } else {
            loading.close()
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          loading.close()
          console.error('删除快递单号失败:', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        }
      } catch (error) {
        console.log('用户取消删除操作')
      }
    },

    /**
     * 清理物流相关的本地缓存
     */
    clearLogisticsLocalCache(trackingNumber) {
      try {
        console.log('开始清理本地缓存:', trackingNumber)

        // 清理localStorage中的物流数据
        const storageKeys = [
          'cp-mes-logistics-data',
          'cp-mes-tracking-numbers',
          `logistics-${trackingNumber}`,
          `tracking-${trackingNumber}`,
          'logistics-query-history',
          'logistics-cache-data'
        ]

        storageKeys.forEach(key => {
          try {
            localStorage.removeItem(key)
            sessionStorage.removeItem(key)
            console.debug('清理缓存键:', key)
          } catch (error) {
            console.warn('清理缓存键失败:', key, error)
          }
        })

        // 清理组件内存中的缓存数据
        if (this.queryHistory) {
          this.queryHistory = []
        }

        console.log('本地缓存清理完成:', trackingNumber)
      } catch (error) {
        console.error('清理本地缓存失败:', error)
      }
    },

    /**
     * 清理后端缓存
     */
    async clearBackendCache(trackingNumber) {
      try {
        // 调用后端缓存清理接口
        const response = await this.$http.delete(`/api/logistics/cache/${trackingNumber}`)
        console.log('后端缓存清理成功:', response)
      } catch (error) {
        // 如果接口不存在或调用失败，记录警告但不中断流程
        console.warn('后端缓存清理接口调用失败:', error)
        throw error
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.logisticsForm = {
        trackingNumber: '',
        logisticsCompany: ''
      }
      this.$nextTick(() => {
        if (this.$refs.logisticsForm) {
          this.$refs.logisticsForm.clearValidate()
        }
      })
    },

    /**
     * 处理物流状态 - 增强版本
     */
    handleLogisticsStatus(status) {
      const statusMessages = {
        'SIGNED': { type: 'success', message: '包裹已签收' },
        'IN_TRANSIT': { type: 'info', message: '包裹运输中' },
        'PICKED_UP': { type: 'info', message: '包裹已揽收' },
        'NO_INFO': { type: 'warning', message: '暂无物流轨迹信息，可能是新单号' },
        'QUERY_FAILED': { type: 'error', message: '物流查询失败，请检查单号是否正确' },
        'INVALID_TRACKING_NUMBER': { type: 'error', message: '快递单号格式错误' },
        'SERVICE_ERROR': { type: 'error', message: '物流查询服务异常' },
        'RESTRICTED_NUMBER': { type: 'warning', message: '该单号被限制查询' }
      }

      const statusInfo = statusMessages[status]
      if (statusInfo) {
        this.$message[statusInfo.type](statusInfo.message)
      }
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const typeMap = {
        'SIGNED': 'success',
        'IN_TRANSIT': 'primary',
        'PICKED_UP': 'warning',
        'NO_INFO': 'info',
        'QUERY_FAILED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    /**
     * 获取时间线类型
     */
    getTimelineType(detail, index) {
      if (index === 0) return 'primary'
      return 'info'
    },

    /**
     * 获取时间线颜色
     */
    getTimelineColor(detail) {
      if (detail.isException) return '#f56c6c'

      const description = detail.description || ''
      if (description.includes('签收') || description.includes('送达')) {
        return '#67c23a'
      }
      if (description.includes('派送')) {
        return '#409eff'
      }

      return null // 使用默认颜色
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      if (!date) return '未知时间'

      const d = new Date(date)
      if (isNaN(d.getTime())) return '时间格式错误'

      return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.logistics-tracking {
  padding: 20px;
}

.logistics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.tracking-info h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 18px;
}

.basic-info .info-item {
  margin-bottom: 10px;
}

.basic-info .label {
  color: #606266;
  font-weight: 500;
}

.basic-info .value {
  color: #303133;
  margin-left: 8px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.logistics-timeline {
  margin-top: 20px;
}

.logistics-timeline h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
}

.timeline-content .description {
  margin: 0 0 5px 0;
  color: #303133;
  font-weight: 500;
}

.timeline-content .detail-info {
  font-size: 12px;
  color: #909399;
}

.timeline-content .location {
  margin-right: 10px;
}

.no-tracking-info,
.no-logistics {
  text-align: center;
  padding: 40px 20px;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>

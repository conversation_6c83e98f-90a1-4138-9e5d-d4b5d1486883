<template>
  <div class="purchase-links-manager">
    <div class="header">
      <h4>采购相关链接</h4>
      <el-button
        type="primary"
        size="small"
        @click="showAddDialog"
        v-if="!readonly"
        icon="el-icon-plus"
      >
        添加链接
      </el-button>
    </div>

    <!-- 链接列表 -->
    <div class="links-list" v-if="linkList.length > 0">
      <div
        v-for="(link, index) in linkList"
        :key="index"
        class="link-item"
      >
        <div class="link-icon">
          <i :class="getLinkIcon(link.type)"></i>
        </div>
        <div class="link-content">
          <div class="link-header">
            <span class="link-name">{{ link.name }}</span>
            <el-tag :type="getLinkTagType(link.type)" size="mini">
              {{ getLinkTypeText(link.type) }}
            </el-tag>
          </div>
          <div class="link-url">
            <a :href="link.url" target="_blank" class="url-link">
              {{ link.url }}
            </a>
            <el-button
              type="text"
              size="mini"
              @click="copyToClipboard(link.url)"
              class="copy-btn"
              title="复制链接"
            >
              <i class="el-icon-document-copy"></i>
            </el-button>
          </div>
          <div class="link-description" v-if="link.description">
            {{ link.description }}
          </div>
        </div>
        <div class="link-actions" v-if="!readonly">
          <el-button
            type="text"
            size="mini"
            @click="editLink(link, index)"
            title="编辑"
          >
            <i class="el-icon-edit"></i>
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click="deleteLink(index)"
            class="delete-btn"
            title="删除"
          >
            <i class="el-icon-delete"></i>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <i class="el-icon-link"></i>
      <p>暂无相关链接</p>
      <el-button
        type="primary"
        size="small"
        @click="showAddDialog"
        v-if="!readonly"
      >
        添加第一个链接
      </el-button>
    </div>

    <!-- 添加/编辑链接对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :z-index="2500"
    >
      <el-form
        ref="linkForm"
        :model="currentLink"
        :rules="linkRules"
        label-width="100px"
      >
        <el-form-item label="链接名称" prop="name">
          <el-input
            v-model="currentLink.name"
            placeholder="请输入链接名称"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="链接地址" prop="url">
          <el-input
            v-model="currentLink.url"
            placeholder="请输入完整的链接地址（包含 http:// 或 https://）"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="链接类型" prop="type">
          <el-select v-model="currentLink.type" placeholder="请选择链接类型">
            <el-option label="供应商链接" value="supplier"></el-option>
            <el-option label="产品页面" value="product"></el-option>
            <el-option label="技术文档" value="document"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="链接描述">
          <el-input
            v-model="currentLink.description"
            type="textarea"
            placeholder="请输入链接描述（可选）"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="saveLink" :loading="saving">
          {{ isEditing ? '更新' : '添加' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { savePurchaseLinks, getPurchaseLinks } from '@/api/jenasi/purchaseOrderEnhanced'

export default {
  name: 'PurchaseLinksManager',
  props: {
    purchaseOrderId: {
      type: [Number, String],
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      linkList: [],
      dialogVisible: false,
      isEditing: false,
      currentLinkIndex: -1,
      saving: false,
      currentLink: {
        name: '',
        url: '',
        type: 'other',
        description: ''
      },
      linkRules: {
        name: [
          { required: true, message: '请输入链接名称', trigger: 'blur' },
          { min: 1, max: 255, message: '链接名称长度在 1 到 255 个字符', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入链接地址', trigger: 'blur' },
          {
            pattern: /^https?:\/\/.+/,
            message: '请输入有效的链接地址（必须以 http:// 或 https:// 开头）',
            trigger: 'blur'
          },
          { min: 10, max: 1000, message: '链接地址长度在 10 到 1000 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择链接类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEditing ? '编辑链接' : '添加链接'
    }
  },
  mounted() {
    this.loadLinks()
  },
  watch: {
    purchaseOrderId: {
      handler(newVal) {
        if (newVal) {
          this.loadLinks()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 加载链接列表
     */
    async loadLinks() {
      if (!this.purchaseOrderId) return

      try {
        const response = await getPurchaseLinks(this.purchaseOrderId)
        console.log('加载链接响应:', response)
        if (response.code === 200 || response.code === 0) {
          this.linkList = response.data || []
          console.log('加载到的链接列表:', this.linkList)
        } else {
          console.warn('加载链接失败:', response.msg)
          this.linkList = []
        }
      } catch (error) {
        console.error('加载链接失败:', error)
        this.linkList = []
      }
    },

    /**
     * 显示添加对话框
     */
    showAddDialog() {
      this.isEditing = false
      this.currentLinkIndex = -1
      this.resetCurrentLink()
      this.dialogVisible = true
    },

    /**
     * 编辑链接
     */
    editLink(link, index) {
      this.isEditing = true
      this.currentLinkIndex = index
      this.currentLink = { ...link }
      this.dialogVisible = true
    },

    /**
     * 保存链接
     */
    async saveLink() {
      try {
        await this.$refs.linkForm.validate()

        this.saving = true

        // 更新链接列表
        if (this.isEditing) {
          this.$set(this.linkList, this.currentLinkIndex, { ...this.currentLink })
        } else {
          this.linkList.push({ ...this.currentLink })
        }

        // 保存到后端
        const response = await savePurchaseLinks(this.purchaseOrderId, this.linkList)
        console.log('保存链接响应:', response)

        if (response.code === 200 || response.code === 0) {
          this.$message.success(this.isEditing ? '链接更新成功!' : '链接添加成功!')
          this.dialogVisible = false
          this.$emit('links-changed', this.linkList)

          // 保存成功后重新加载数据确保同步
          this.$nextTick(() => {
            this.loadLinks()
          })
        } else {
          this.$message.error(response.msg || '保存失败')
          // 回滚本地更改
          if (this.isEditing) {
            // 这里需要重新加载来恢复数据
            this.loadLinks()
          } else {
            this.linkList.pop()
          }
        }

      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error('保存链接失败: ' + error.message)
        }
      } finally {
        this.saving = false
      }
    },

    /**
     * 删除链接
     */
    async deleteLink(index) {
      try {
        await this.$confirm('确定要删除这个链接吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 从列表中移除
        this.linkList.splice(index, 1)

        // 保存到后端
        const response = await savePurchaseLinks(this.purchaseOrderId, this.linkList)

        if (response.code === 200 || response.code === 0) {
          this.$message.success('链接删除成功!')
          this.$emit('links-changed', this.linkList)
        } else {
          this.$message.error(response.msg || '删除失败')
          // 重新加载数据
          this.loadLinks()
        }

      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除链接失败: ' + error.message)
        }
      }
    },

    /**
     * 取消对话框
     */
    cancelDialog() {
      this.dialogVisible = false
      this.resetCurrentLink()
    },

    /**
     * 重置当前链接
     */
    resetCurrentLink() {
      this.currentLink = {
        name: '',
        url: '',
        type: 'other',
        description: ''
      }
      this.$nextTick(() => {
        if (this.$refs.linkForm) {
          this.$refs.linkForm.clearValidate()
        }
      })
    },

    /**
     * 重置组件数据（供父组件调用）
     */
    resetData() {
      // 重置链接列表
      this.linkList = []

      // 关闭对话框
      this.dialogVisible = false

      // 重置编辑状态
      this.isEditing = false
      this.currentLinkIndex = -1
      this.saving = false

      // 重置当前链接
      this.resetCurrentLink()

      // 触发链接变更事件，通知父组件
      this.$emit('links-changed', [])

      console.log('PurchaseLinksManager 数据已重置')
    },

    /**
     * 复制到剪贴板
     */
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('链接已复制到剪贴板!')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          this.$message.success('链接已复制到剪贴板!')
        } catch (e) {
          this.$message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textArea)
      }
    },

    /**
     * 获取链接图标
     */
    getLinkIcon(type) {
      const iconMap = {
        supplier: 'el-icon-office-building',
        product: 'el-icon-goods',
        document: 'el-icon-document',
        other: 'el-icon-link'
      }
      return iconMap[type] || 'el-icon-link'
    },

    /**
     * 获取链接标签类型
     */
    getLinkTagType(type) {
      const typeMap = {
        supplier: 'success',
        product: 'primary',
        document: 'warning',
        other: 'info'
      }
      return typeMap[type] || 'info'
    },

    /**
     * 获取链接类型文本
     */
    getLinkTypeText(type) {
      const textMap = {
        supplier: '供应商',
        product: '产品页面',
        document: '技术文档',
        other: '其他'
      }
      return textMap[type] || '其他'
    },

    /**
     * 获取链接列表（供父组件调用）
     */
    getLinkList() {
      return this.linkList
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-links-manager {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h4 {
      margin: 0;
      color: var(--base-color-1);
      font-weight: 500;
    }
  }

  .links-list {
    .link-item {
      display: flex;
      align-items: flex-start;
      padding: 15px;
      background: var(--base-item-bg);
      border: 1px solid var(--border-color-1);
      border-radius: 8px;
      margin-bottom: 12px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px var(--tag-shadow-color-1);
        transform: translateY(-2px);
      }

      .link-icon {
        margin-right: 15px;
        margin-top: 2px;

        i {
          font-size: 20px;
          color: var(--current-color);
        }
      }

      .link-content {
        flex: 1;
        min-width: 0;

        .link-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .link-name {
            font-weight: 500;
            color: var(--base-color-1);
            font-size: 14px;
          }
        }

        .link-url {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .url-link {
            color: var(--current-color);
            text-decoration: none;
            font-size: 13px;
            word-break: break-all;
            flex: 1;

            &:hover {
              text-decoration: underline;
            }
          }

          .copy-btn {
            margin-left: 8px;
            color: var(--base-color-3);

            &:hover {
              color: var(--current-color);
            }
          }
        }

        .link-description {
          color: var(--base-color-2);
          font-size: 12px;
          line-height: 1.4;
        }
      }

      .link-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-left: 15px;

        .el-button {
          padding: 6px;
          min-height: auto;

          &.delete-btn {
            color: #f56c6c;

            &:hover {
              color: #f78989;
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--base-color-3);

    i {
      font-size: 48px;
      margin-bottom: 15px;
      color: var(--base-color-4);
    }

    p {
      margin: 0 0 20px;
      font-size: 14px;
    }
  }
}

/* 对话框样式 */
:deep(.el-dialog) {
  .el-form-item__label {
    color: var(--base-color-1);
    font-weight: 500;
  }

  .el-input__count {
    color: var(--base-color-3);
  }
}

/* 主题适配 */
.theme-dark {
  .purchase-links-manager {
    .links-list {
      .link-item {
        background: var(--base-menu-background);
        border-color: var(--border-color-2);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>

import request from '@/utils/request'

// 查询操作日志列表
export function getOperationLogs(query) {
  return request({
    url: '/inventory/operation/logs',
    method: 'get',
    params: query
  })
}

// 查询操作日志详细
export function getOperationLogInfo(logId) {
  return request({
    url: '/inventory/operation/logs/' + logId,
    method: 'get'
  })
}

// 根据区域编码查询操作记录
export function getOperationLogsByZone(zoneCode) {
  return request({
    url: '/inventory/operation/logs/zone/' + zoneCode,
    method: 'get'
  })
}

// 根据操作类型查询操作记录
export function getOperationLogsByType(operationType) {
  return request({
    url: '/inventory/operation/logs/type/' + operationType,
    method: 'get'
  })
}

// 删除操作日志
export function removeOperationLog(logId) {
  return request({
    url: '/inventory/operation/logs/' + logId,
    method: 'delete'
  })
}

// 批量删除操作日志
export function batchRemoveOperationLog(logIds) {
  return request({
    url: '/inventory/operation/logs',
    method: 'delete',
    data: logIds
  })
}

// 批量验证操作记录
export function batchVerifyOperationLog(logIds) {
  return request({
    url: '/inventory/operation/logs/verify',
    method: 'post',
    data: logIds
  })
}

// 库存入库操作
export function inboundInventory(data) {
  return request({
    url: '/inventory/operation/inbound',
    method: 'post',
    params: data
  })
}

// 库存出库操作
export function outboundInventory(data) {
  return request({
    url: '/inventory/operation/outbound',
    method: 'post',
    params: data
  })
}

// 批量库存入库操作
export function batchInboundInventory(data) {
  return request({
    url: '/inventory/operation/batch/inbound',
    method: 'post',
    params: data
  })
}

// 批量库存出库操作
export function batchOutboundInventory(data) {
  return request({
    url: '/inventory/operation/batch/outbound',
    method: 'post',
    params: data
  })
}

// 库存移库操作
export function transferInventory(data) {
  return request({
    url: '/inventory/operation/transfer',
    method: 'post',
    params: data
  })
}

// 记录操作日志（供其他服务调用）
export function recordOperationLog(data) {
  return request({
    url: '/inventory/operation/logs/record',
    method: 'post',
    params: data
  })
}

// 清空操作日志
export function cleanOperationLog(days) {
  return request({
    url: '/inventory/operation/logs/clean',
    method: 'delete',
    params: { days }
  })
}

// 导出操作日志
export function exportOperationLog(query) {
  return request({
    url: '/inventory/operation/logs/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
} 
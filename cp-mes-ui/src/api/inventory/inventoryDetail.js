import request from '@/utils/request'

// 查询库存明细列表
export function listInventoryDetail(query) {
  return request({
    url: '/system/inventoryDetail/list',
    method: 'get',
    params: query
  })
}

// 查询库存明细详细
export function getInventoryDetail(detailId) {
  return request({
    url: '/system/inventoryDetail/' + detailId,
    method: 'get'
  })
}

// 新增库存明细
export function addInventoryDetail(data) {
  return request({
    url: '/system/inventoryDetail',
    method: 'post',
    data: data
  })
}

// 修改库存明细
export function updateInventoryDetail(data) {
  return request({
    url: '/system/inventoryDetail',
    method: 'put',
    data: data
  })
}

// 删除库存明细
export function delInventoryDetail(detailId) {
  return request({
    url: '/system/inventoryDetail/' + detailId,
    method: 'delete'
  })
}

// 批量删除库存明细
export function delBatchInventoryDetail(detailIds) {
  return request({
    url: '/system/inventoryDetail/' + detailIds,
    method: 'delete'
  })
}

// 导出库存明细
export function exportInventoryDetail(query) {
  return request({
    url: '/system/inventoryDetail/export',
    method: 'post',
    data: query
  })
}

// 库存调整
export function adjustInventoryStock(data) {
  return request({
    url: '/system/inventoryDetail/adjust',
    method: 'post',
    data: data
  })
}

// 库存移库功能已移除

// 获取库存统计信息
export function getInventoryStatistics(query) {
  return request({
    url: '/system/inventoryDetail/statistics',
    method: 'get',
    params: query
  })
}

// 获取低库存预警列表
export function getLowStockAlert() {
  return request({
    url: '/system/inventoryDetail/lowStockAlert',
    method: 'get'
  })
}

// 根据区域ID获取库存明细
export function getInventoryDetailByZone(zoneId, query) {
  return request({
    url: '/system/inventoryDetail/byZone/' + zoneId,
    method: 'get',
    params: query
  })
}

// 根据区域编码获取库存明细
export function getInventoryDetailByZoneCode(zoneCode, query) {
  return request({
    url: '/system/inventoryDetail/byZoneCode/' + zoneCode,
    method: 'get',
    params: query
  })
}

// 批量更新库存状态
export function batchUpdateStatus(detailIds, status) {
  return request({
    url: '/system/inventoryDetail/batchUpdateStatus',
    method: 'put',
    data: {
      detailIds: detailIds,
      status: status
    }
  })
}

// 获取库存追溯信息
export function getInventoryTrace(detailId) {
  return request({
    url: '/system/inventoryDetail/trace/' + detailId,
    method: 'get'
  })
}

// 根据物料ID、区域ID和批次号查询库存明细（用于唯一性校验）
export function getInventoryDetailByMaterialAndZone(materialId, zoneId, batchNo) {
  return request({
    url: '/system/inventoryDetail/byMaterial',
    method: 'get',
    params: {
      materialId: materialId,
      zoneId: zoneId,
      batchNo: batchNo
    }
  })
}

// 验证物料是否适合存放在指定区域
export function validateMaterialZoneCompatibility(params) {
  return request({
    url: '/system/inventoryDetail/validateZoneCompatibility',
    method: 'get',
    params: params
  })
}

// 校验库存明细唯一性
export function checkInventoryDetailUnique(params) {
  return request({
    url: '/system/inventoryDetail/checkInventoryDetailUnique',
    method: 'get',
    params: params
  })
}

// 校验库存明细唯一性（基于区域编码）
export function checkInventoryDetailUniqueByCode(params) {
  return request({
    url: '/system/inventoryDetail/checkInventoryDetailUniqueByCode',
    method: 'get',
    params: params
  })
}

// 校验物料ID唯一性（全局唯一性）
export function checkMaterialIdUnique(materialId) {
  return request({
    url: '/system/inventoryDetail/checkMaterialIdUnique',
    method: 'get',
    params: { materialId }
  })
}

// 校验物料ID在特定上下文中的唯一性
export function checkMaterialIdUniqueInContext(materialId, zoneCode, batchNo, detailId) {
  return request({
    url: '/system/inventoryDetail/checkMaterialIdUniqueInContext',
    method: 'get',
    params: { 
      materialId,
      zoneCode,
      batchNo,
      detailId
    }
  })
}

// 生成下一个可用的物料ID
export function generateNextMaterialId(materialType) {
  return request({
    url: '/system/inventoryDetail/generateNextMaterialId',
    method: 'get',
    params: { materialType }
  })
}

// 根据物料ID获取物料在所有区域的分布情况
// 修改为通过物料ID精确匹配区域信息
export function getMaterialZoneDistribution(materialId, materialType) {
  return request({
    url: '/system/inventoryDetail/materialZoneDistribution',
    method: 'get',
    params: {
      materialId: materialId,
      materialType: materialType
    }
  })
}

// 库存入库操作
export function inboundInventory(data) {
  return request({
    url: '/inventory/operation/inbound',
    method: 'post',
    params: {
      detailId: data.detailId,
      quantity: data.quantity,
      reason: data.reason,
      remark: data.remark || ''
    }
  })
}

// 库存出库操作
export function outboundInventory(data) {
  return request({
    url: '/inventory/operation/outbound',
    method: 'post',
    params: {
      detailId: data.detailId,
      quantity: data.quantity,
      reason: data.reason,
      remark: data.remark || ''
    }
  })
}

// 批量入库操作
export function batchInboundInventory(data) {
  return request({
    url: '/inventory/operation/batchInbound',
    method: 'post',
    params: {
      detailIds: data.detailIds.join(','), // 将数组转换为逗号分隔的字符串
      quantity: data.quantity,
      reason: data.reason,
      remark: data.remark || ''
    }
  })
}

// 批量出库操作
export function batchOutboundInventory(data) {
  return request({
    url: '/inventory/operation/batchOutbound',
    method: 'post',
    params: {
      detailIds: data.detailIds.join(','), // 将数组转换为逗号分隔的字符串
      quantity: data.quantity,
      reason: data.reason,
      remark: data.remark || ''
    }
  })
}

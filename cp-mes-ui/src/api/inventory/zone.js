import request from '@/utils/request'

// 查询仓库区域列表
export function listZone(query) {
  return request({
    url: '/system/zone/list',
    method: 'get',
    params: query
  })
}

// 查询仓库区域详细
export function getZone(zoneId) {
  return request({
    url: '/system/zone/' + zoneId,
    method: 'get'
  })
}

// 新增仓库区域
export function addZone(data) {
  return request({
    url: '/system/zone',
    method: 'post',
    data: data
  })
}

// 修改仓库区域
export function updateZone(data) {
  return request({
    url: '/system/zone',
    method: 'put',
    data: data
  })
}

// 删除仓库区域
export function delZone(zoneId) {
  return request({
    url: '/system/zone/' + zoneId,
    method: 'delete'
  })
}

// 批量删除仓库区域
export function delBatchZone(zoneIds) {
  return request({
    url: '/system/zone/' + zoneIds,
    method: 'delete'
  })
}

// 导出仓库区域
export function exportZone(query) {
  return request({
    url: '/system/zone/export',
    method: 'post',
    data: query,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 批量启用/停用区域
export function changeZoneStatus(zoneIds, status) {
  return request({
    url: '/system/zone/changeStatus',
    method: 'put',
    params: {
      zoneIds: zoneIds.join(','),
      status: status
    }
  })
}

// 根据仓库类型获取区域列表
export function getZonesByWarehouseType(warehouseType) {
  return request({
    url: '/system/zone/listByWarehouseType/' + warehouseType,
    method: 'get'
  })
}

// 根据仓库ID获取区域列表
export function getZonesByWarehouseId(warehouseId) {
  return request({
    url: '/system/zone/listByWarehouseId/' + warehouseId,
    method: 'get'
  })
}

// 根据仓库编码获取区域列表
export function getZonesByWarehouseCode(warehouseCode) {
  return request({
    url: '/system/zone/listByWarehouseCode/' + warehouseCode,
    method: 'get'
  })
}

// 获取所有区域列表（包含仓库状态信息）
export function getAllZonesWithWarehouseStatus() {
  return request({
    url: '/system/zone/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 9999 // 获取所有数据
    }
  })
}

// 校验区域编码是否唯一
export function checkZoneCodeUnique(zoneCode, zoneId) {
  return request({
    url: '/system/zone/checkZoneCodeUnique',
    method: 'get',
    params: {
      zoneCode: zoneCode,
      zoneId: zoneId
    }
  })
}

// 校验区域信息是否唯一（名称+仓库+类型）
export function checkZoneInfoUnique(zoneName, warehouseId, zoneType, zoneId) {
  return request({
    url: '/system/zone/checkZoneInfoUnique',
    method: 'get',
    params: {
      zoneName: zoneName,
      warehouseId: warehouseId,
      zoneType: zoneType,
      zoneId: zoneId
    }
  })
}

// 校验区域信息是否唯一（名称+仓库编码+类型）
export function checkZoneInfoUniqueByCode(zoneName, warehouseCode, zoneType, zoneId) {
  return request({
    url: '/system/zone/checkZoneInfoUniqueByCode',
    method: 'get',
    params: {
      zoneName: zoneName,
      warehouseCode: warehouseCode,
      zoneType: zoneType,
      zoneId: zoneId
    }
  })
}

// 生成区域编码
export function generateZoneCode(warehouseId, warehouseCode, zoneType) {
  return request({
    url: '/system/zone/generateZoneCode',
    method: 'get',
    params: {
      warehouseId: warehouseId,
      warehouseCode: warehouseCode,
      zoneType: zoneType
    }
  })
}

// 获取区域类型选项
export function getZoneTypeOptions() {
  return new Promise((resolve) => {
    const zoneTypes = [
      { label: '储存区', value: 'storage' },
      { label: '拣货区', value: 'picking' },
      { label: '暂存区', value: 'staging' },
      { label: '检验区', value: 'inspection' }
    ]
    resolve({
      code: 200,
      data: zoneTypes
    })
  })
}

// 获取区域统计信息
export function getZoneStatistics(params) {
  return request({
    url: '/system/zone/statistics',
    method: 'get',
    params: params
  })
}

// 推荐最佳货位
export function recommendBestLocation(data) {
  return request({
    url: '/system/zone/recommend-location',
    method: 'post',
    data: data
  })
}

// 生成区域二维码
export function generateZoneQrCode(zoneId) {
  return request({
    url: '/system/zone/generateQrCode/' + zoneId,
    method: 'post'
  })
}

// 批量生成区域二维码
export function batchGenerateZoneQrCode(zoneIds) {
  return request({
    url: '/system/zone/batchGenerateQrCode',
    method: 'post',
    data: zoneIds
  })
}

// 获取区域二维码信息
export function getZoneQrCodeInfo(zoneId) {
  return request({
    url: '/system/zone/qrCode/' + zoneId,
    method: 'get'
  })
}

// 打印区域二维码
export function printZoneQrCode(data) {
  return request({
    url: '/system/zone/print',
    method: 'post',
    data: data
  })
} 
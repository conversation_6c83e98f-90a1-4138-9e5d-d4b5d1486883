import request from '@/utils/request'

// 查询仓库信息列表
export function listWarehouse(query) {
  return request({
    url: '/system/warehouse/list',
    method: 'get',
    params: query
  })
}

// 查询仓库信息详细
export function getWarehouse(warehouseId) {
  return request({
    url: '/system/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 新增仓库信息
export function addWarehouse(data) {
  return request({
    url: '/system/warehouse',
    method: 'post',
    data: data
  })
}

// 修改仓库信息
export function updateWarehouse(data) {
  return request({
    url: '/system/warehouse',
    method: 'put',
    data: data
  })
}

// 删除仓库信息
export function delWarehouse(warehouseId) {
  return request({
    url: '/system/warehouse/' + warehouseId,
    method: 'delete'
  })
}

// 批量删除仓库信息
export function delBatchWarehouse(warehouseIds) {
  return request({
    url: '/system/warehouse/' + warehouseIds,
    method: 'delete'
  })
}

// 导出仓库信息
export function exportWarehouse(query) {
  return request({
    url: '/system/warehouse/export',
    method: 'post',
    data: query
  })
}

// 批量启用/停用仓库
export function changeWarehouseStatus(warehouseIds, status) {
  return request({
    url: '/system/warehouse/changeStatus',
    method: 'put',
    params: {
      warehouseIds: warehouseIds.join(','),
      status: status
    }
  })
}

// 根据仓库类型获取仓库列表
export function getWarehousesByType(warehouseType) {
  return request({
    url: '/system/warehouse/listByType/' + warehouseType,
    method: 'get'
  })
}

// 校验仓库编码是否唯一
export function checkWarehouseCodeUnique(warehouseCode, warehouseId) {
  return request({
    url: '/system/warehouse/checkWarehouseCodeUnique',
    method: 'get',
    params: {
      warehouseCode: warehouseCode,
      warehouseId: warehouseId
    }
  })
}

// 校验仓库信息是否唯一（名称+类型+位置）
export function checkWarehouseInfoUnique(warehouseName, warehouseType, warehouseAddress, warehouseId) {
  return request({
    url: '/system/warehouse/checkWarehouseInfoUnique',
    method: 'get',
    params: {
      warehouseName: warehouseName,
      warehouseType: warehouseType,
      warehouseAddress: warehouseAddress,
      warehouseId: warehouseId
    }
  })
}

// 生成仓库编码
export function generateWarehouseCode(warehouseType) {
  return request({
    url: '/system/warehouse/generateWarehouseCode',
    method: 'get',
    params: {
      warehouseType: warehouseType
    }
  })
}

// 获取仓库类型选项
export function getWarehouseTypeOptions() {
  return new Promise((resolve) => {
    const warehouseTypes = [
      { label: '原料仓库', value: '1' },
      { label: '半成品仓库', value: '2' },
      { label: '成品仓库', value: '3' },
      { label: '零件仓库', value: '4' }
    ]
    resolve({
      code: 200,
      data: warehouseTypes
    })
  })
}

// 获取仓库状态选项
export function getWarehouseStatusOptions() {
  return new Promise((resolve) => {
    const statusOptions = [
      { label: '启用', value: '1' },
      { label: '停用', value: '0' }
    ]
    resolve({
      code: 200,
      data: statusOptions
    })
  })
}

// 校验物料存放位置是否合规
export function validateStorageLocation(data) {
  return request({
    url: '/system/inventoryDetail/validate-storage-location',
    method: 'post',
    data: data
  })
}

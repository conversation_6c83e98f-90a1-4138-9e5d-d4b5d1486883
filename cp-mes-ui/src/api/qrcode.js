import request from '@/utils/request'

// 生成登录二维码
export function generateQrCode() {
  return request({
    url: '/qrcode/generate',
    headers: {
      isToken: false
    },
    method: 'post'
  })
}

// 查询二维码状态
export function getQrCodeStatus(qrUuid) {
  return request({
    url: `/qrcode/status/${qrUuid}`,
    headers: {
      isToken: false
    },
    method: 'get'
  })
}

// 取消二维码
export function cancelQrCode(qrUuid) {
  return request({
    url: `/qrcode/cancel/${qrUuid}`,
    headers: {
      isToken: false
    },
    method: 'delete'
  })
}

// 移动端验证二维码（原有方式）
export function verifyQrCode(data) {
  return request({
    url: '/qrcode/verify',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 移动端用户名密码登录并验证二维码（新增）
export function loginAndVerifyQrCode(data) {
  return request({
    url: '/qrcode/login-verify',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
} 
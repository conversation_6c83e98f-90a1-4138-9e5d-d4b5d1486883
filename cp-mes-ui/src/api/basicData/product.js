import request from '@/utils/request'

// 查询产品列表
export function listProduct(query) {
  return request({
    url: '/system/product/list',
    method: 'get',
    params: query
  })
}

// 查询产量统计
export function listProductionStatistics(query) {
  return request({
    url: '/system/product/productionStatistics',
    method: 'get',
    params: query
  })
}

// 查询产品详细
export function getProduct(productId) {
  return request({
    url: '/system/product/' + productId,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data) {
  return request({
    url: '/system/product',
    method: 'post',
    data: data
  })
}

// 自动创建产品（用于BOM清单等模块自动创建产品）
export function autoCreateProduct(data) {
  return request({
    url: '/system/product/autoCreate',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: '/system/product',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(productId) {
  return request({
    url: '/system/product/' + productId,
    method: 'delete'
  })
}

// 导入产品数据
export function importData(data) {
  return request({
    url: '/system/product/importData',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载产品导入模板
export function importTemplate() {
  return request({
    url: '/system/product/importTemplate',
    method: 'post'
  })
}

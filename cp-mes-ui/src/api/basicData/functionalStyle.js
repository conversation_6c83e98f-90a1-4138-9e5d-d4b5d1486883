import request from '@/utils/request'

// 查询功能系列款式列表
export function listFunctionalStyle(query) {
  return request({
    url: '/system/functionalStyle/list',
    method: 'get',
    params: query
  })
}

// 查询功能系列款式详细
export function getFunctionalStyle(styleId) {
  return request({
    url: '/system/functionalStyle/' + styleId,
    method: 'get'
  })
}

// 新增功能系列款式
export function addFunctionalStyle(data) {
  return request({
    url: '/system/functionalStyle',
    method: 'post',
    data: data
  })
}

// 修改功能系列款式
export function updateFunctionalStyle(data) {
  return request({
    url: '/system/functionalStyle',
    method: 'put',
    data: data
  })
}

// 删除功能系列款式
export function delFunctionalStyle(styleId) {
  return request({
    url: '/system/functionalStyle/' + styleId,
    method: 'delete'
  })
}

// 导出功能系列款式
export function exportFunctionalStyle(query) {
  return request({
    url: '/system/functionalStyle/export',
    method: 'post',
    params: query
  })
} 
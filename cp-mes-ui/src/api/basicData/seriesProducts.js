import request from '@/utils/request'

// 查询系列产品列表
export function listSeriesProducts(query) {
  return request({
    url: '/system/seriesProducts/list',
    method: 'get',
    params: query
  })
}

// 查询系列产品详细
export function getSeriesProducts(id) {
  return request({
    url: '/system/seriesProducts/' + id,
    method: 'get'
  })
}

// 新增系列产品
export function addSeriesProducts(data) {
  return request({
    url: '/system/seriesProducts',
    method: 'post',
    data: data
  })
}

// 修改系列产品
export function updateSeriesProducts(data) {
  return request({
    url: '/system/seriesProducts',
    method: 'put',
    data: data
  })
}

// 删除系列产品
export function delSeriesProducts(id) {
  return request({
    url: '/system/seriesProducts/' + id,
    method: 'delete'
  })
}

// 导出系列产品
export function exportSeriesProducts(query) {
  return request({
    url: '/system/seriesProducts/export',
    method: 'post',
    params: query
  })
} 
import request from '@/utils/request'

// 查询BOM清单列表
export function listProductMaterialsBom(query) {
  return request({
    url: '/basicData/productMaterialsBom/list',
    method: 'get',
    params: query
  })
}

// 查询BOM清单详细
export function getProductMaterialsBom(id) {
  return request({
    url: '/basicData/productMaterialsBom/' + id,
    method: 'get'
  })
}

// 新增BOM清单
export function addProductMaterialsBom(data) {
  return request({
    url: '/basicData/productMaterialsBom',
    method: 'post',
    data: data
  })
}

// 修改BOM清单
export function updateProductMaterialsBom(data) {
  return request({
    url: '/basicData/productMaterialsBom',
    method: 'put',
    data: data
  })
}

// 删除BOM清单
export function delProductMaterialsBom(id) {
  return request({
    url: '/basicData/productMaterialsBom/' + id,
    method: 'delete'
  })
}

// 导出BOM清单
export function exportProductMaterialsBom(query) {
  return request({
    url: '/basicData/productMaterialsBom/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导入BOM清单数据
export function importData(data) {
  return request({
    url: '/basicData/productMaterialsBom/importData',
    method: 'post',
    data: data
  })
}

// 下载BOM清单导入模板
export function importTemplate() {
  return request({
    url: '/basicData/productMaterialsBom/importTemplate',
    method: 'post'
  })
}

// 获取所有版本列表
export function getAllVersions() {
  return request({
    url: '/basicData/productMaterialsBom/versions',
    method: 'get'
  })
}

// 根据型号、模块、板类型获取版本列表
export function getVersionsByGroup(params) {
  return request({
    url: '/basicData/productMaterialsBom/versions/group',
    method: 'get',
    params: params
  })
}

// 批量导入BOM数据
export function importBomData(filePath, updatedBy) {
  return request({
    url: '/basicData/productMaterialsBom/importBomData',
    method: 'post',
    params: {
      filePath: filePath,
      updatedBy: updatedBy
    }
  })
}

// 获取所有型号列表
export function getAllModels() {
  return request({
    url: '/basicData/productMaterialsBom/models',
    method: 'get'
  })
}

// 根据型号获取BOM列表
export function getBomByModel(model) {
  // 对型号名称进行URL编码，避免中文字符导致的请求错误
  const encodedModel = encodeURIComponent(model);
  return request({
    url: `/basicData/productMaterialsBom/model/${encodedModel}`,
    method: 'get'
  })
}

// 根据型号和款式获取BOM列表
export function getBomByModelAndStyle(model,style) {
  return request({
    url: `/basicData/productMaterialsBom/model/${model}/style/${style}`,
    method: 'get'
  })
}

// 获取所有功能模块列表
export function getAllModules() {
  return request({
    url: '/basicData/productMaterialsBom/getAllModules',
    method: 'get'
  })
}

// 获取所有上下板类型列表
export function getAllBoardTypes() {
  return request({
    url: '/basicData/productMaterialsBom/getAllBoardTypes',
    method: 'get'
  })
}

// ============= 通用板块功能相关接口 =============

// 获取可引用的通用板块列表（动态查找）
export function getAvailableGenericBomList(params) {
  return request({
    url: '/basicData/productMaterialsBom/getAvailableGenericBomList',
    method: 'get',
    params: params
  })
}

// 获取指定条件的材料清单（用于动态展示引用内容）
export function getMaterialListForReference(params) {
  return request({
    url: '/basicData/productMaterialsBom/getMaterialListForReference',
    method: 'get',
    params: params
  })
}

// 为了兼容现有前端代码，保留这些方法但指向新的API
export function getReferencableBomList(params) {
  return getAvailableGenericBomList(params);
}

export function getReferencedBomList(params) {
  return getMaterialListForReference(params);
}

// 注意：通用板块逻辑下不需要保存引用关系，这些方法暂时保留但会返回提示
export function setBomReference(data) {
  return Promise.reject(new Error('通用板块逻辑下不需要设置引用关系，引用是动态显示的'));
}

export function removeBomReference(params) {
  return Promise.reject(new Error('通用板块逻辑下不需要移除引用关系，引用是动态显示的'));
}

// 根据映射信息获取具体的材料清单
export function getMaterialListByMapping(mappedModel, mappedModule, mappedBoardType) {
  return request({
    url: '/basicData/productMaterialsBom/getMaterialListByMapping',
    method: 'get',
    params: {
      mappedModel,
      mappedModule,
      mappedBoardType
    }
  })
}

// 根据通用ID获取映射列表
export function getGenericMappingList(genericId) {
  return request({
    url: '/basicData/productMaterialsBom/getGenericMappingList',
    method: 'get',
    params: {
      genericId
    }
  })
}

// 获取通用ID的默认映射
export function getDefaultGenericMapping(genericId) {
  return request({
    url: '/basicData/productMaterialsBom/getDefaultGenericMapping',
    method: 'get',
    params: {
      genericId
    }
  })
}

// 创建通用板块记录
export function createGenericBom(data) {
  return request({
    url: '/basicData/productMaterialsBom/createGenericBom',
    method: 'post',
    params: data
  })
}

// 批量修改型号和产品编码
export function batchUpdateModel(data) {
  return request({
    url: '/basicData/productMaterialsBom/batchUpdateModel',
    method: 'post',
    data: data
  })
}

// 搜索产品（用于产品编码选择）
export function searchProducts(keyword) {
  return request({
    url: '/basicData/product/search',
    method: 'get',
    params: {
      keyword: keyword,
      pageSize: 20
    }
  })
}

// ============= 通用设置功能相关接口 =============

// 检查原料是否可以设置为通用（按原料名称）
// @deprecated 使用 checkGenericEligibilityByGroup 替代
export function checkGenericEligibility(materialName) {
  return request({
    url: '/basicData/productMaterialsBom/checkGenericEligibility',
    method: 'get',
    params: { materialName }
  })
}

// 检查BOM记录是否可以设置为通用（按分组）
export function checkGenericEligibilityByGroup(model, module, boardType) {
  return request({
    url: '/basicData/productMaterialsBom/checkGenericEligibilityByGroup',
    method: 'get',
    params: { model, module, boardType },
    timeout: 30000 // 增加超时时间到30秒
  })
}

// 设置原料为通用类型（按原料名称）
// @deprecated 使用 setAsGenericByGroup 替代
export function setAsGeneric(materialName) {
  return request({
    url: '/basicData/productMaterialsBom/setAsGeneric',
    method: 'post',
    params: { materialName }
  })
}

// 设置BOM记录为通用类型（按分组）
export function setAsGenericByGroup(model, module, boardType) {
  return request({
    url: '/basicData/productMaterialsBom/setAsGenericByGroup',
    method: 'post',
    params: { model, module, boardType },
    timeout: 30000 // 增加超时时间到30秒
  })
}

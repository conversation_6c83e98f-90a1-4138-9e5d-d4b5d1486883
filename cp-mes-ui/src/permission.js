import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register', '/applyAccount']

store.dispatch('app/getLogoInfo') // 获取平台logo信息

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/user/profile' }) // 已登录访问登录页，直接跳转到个人中心
      NProgress.done()
    } else if (to.path === '/' || to.path === '/index') {
      next({ path: '/user/profile' }) // 访问首页时也跳转到个人中心
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            // 确保 addRoutes 完成后，正常跳转到目标路由
            next({ ...to, replace: true })
            
            // 延迟检查未读通知，确保页面完全加载后再弹窗
            setTimeout(() => {
              // 触发全局事件，通知Layout组件检查未读通知
              try {
                if (window.Vue && window.Vue.prototype.$bus) {
                  window.Vue.prototype.$bus.$emit('check-unread-notices')
                }
              } catch (error) {
                console.warn('触发检查未读通知事件失败:', error)
              }
            }, 1500)
          })
        }).catch(err => {
            store.dispatch('LogOut').then(() => {
              Message.error(err)
              // 如果获取信息或生成路由失败，退回到登录页
              next({ path: '/login' }) // 这里修改为跳转到登录页而不是根路径
            })
          })
      } else {
        next() // 正常放行到当前目标路由
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 否则全部重定向到登录页，并带上 redirect 参数以便登录后跳回
      next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

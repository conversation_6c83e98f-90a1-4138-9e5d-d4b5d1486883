@echo off
chcp 65001 >nul
echo ===============================================
echo    MES扫码登录测试应用启动脚本
echo ===============================================
echo.

cd /d "%~dp0"
echo [信息] 当前目录: %CD%
echo [信息] 正在启动HTTP服务器...
echo.

rem 首先尝试Python 3
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [信息] 使用Python 3启动服务器
    echo [信息] 服务器将在 http://localhost:3000 启动
    echo [信息] 按 Ctrl+C 停止服务器
    echo.
    python -m http.server 3000
    goto :end
)

rem 尝试Python 2
python2 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [信息] 使用Python 2启动服务器
    echo [信息] 服务器将在 http://localhost:3000 启动
    echo [信息] 按 Ctrl+C 停止服务器
    echo.
    python2 -m SimpleHTTPServer 3000
    goto :end
)

rem 尝试Node.js
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [信息] 使用Node.js启动服务器
    echo [信息] 服务器将在 http://localhost:3000 启动
    echo [信息] 按 Ctrl+C 停止服务器
    echo.
    npx http-server -p 3000 --cors
    goto :end
)

rem 如果都没有，提供帮助信息
echo [错误] 未找到Python或Node.js
echo.
echo 请安装以下任一工具：
echo 1. Python 3: https://www.python.org/downloads/
echo 2. Python 2: https://www.python.org/downloads/
echo 3. Node.js: https://nodejs.org/
echo.
echo 或者直接在浏览器中打开 index.html 文件

:end
echo.
echo [信息] 服务器已停止
pause 
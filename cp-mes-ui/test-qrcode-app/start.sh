#!/bin/bash

echo ""
echo "==============================================="
echo "    MES扫码登录测试应用启动脚本"
echo "==============================================="
echo ""

# 检查是否安装了Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "[错误] 未检测到Python，请先安装Python"
    echo "Ubuntu/Debian: sudo apt-get install python3"
    echo "macOS: brew install python3"
    echo "或访问: https://www.python.org/downloads/"
    exit 1
fi

# 获取当前目录
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "[信息] 当前目录: $CURRENT_DIR"
echo "[信息] 正在启动HTTP服务器..."
echo ""

# 启动HTTP服务器
echo "[信息] 服务器将在 http://localhost:3000 启动"
echo "[信息] 按 Ctrl+C 停止服务器"
echo ""

cd "$CURRENT_DIR"

# 尝试使用python3，如果不存在则使用python
if command -v python3 &> /dev/null; then
    python3 -m http.server 3000
elif command -v python &> /dev/null; then
    python -m http.server 3000
else
    echo "[错误] Python未正确安装"
    exit 1
fi 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MES移动端扫码登录测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 30px 0;
            color: white;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .main-card {
            background: white;
            border-radius: 20px;
            padding: 30px 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin-bottom: 20px;
        }

        .user-status {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            margin-right: 15px;
        }

        .user-info h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .user-info p {
            color: #666;
            font-size: 14px;
        }

        .step {
            display: none;
            text-align: center;
        }

        .step.active {
            display: block;
        }

        .step-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .scanner-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 20px 0;
            text-align: center;
        }

        .scanner-icon {
            font-size: 60px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .scanner-text {
            color: #6c757d;
            line-height: 1.5;
        }

        .qr-input {
            margin: 20px 0;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 10px 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .login-input {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .login-input h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
            text-align: center;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .login-details h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-label {
            color: #6c757d;
            font-weight: 500;
        }

        .detail-value {
            color: #495057;
            font-weight: 600;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .confirm-actions .btn {
            flex: 1;
            margin: 0;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        /* 主题适配 - 使用CSS变量确保主题切换时正确显示 */
        :root {
            --test-app-bg: #ffffff;
            --test-app-text: #333333;
            --test-app-card-bg: #ffffff;
            --test-app-input-bg: #ffffff;
            --test-app-input-border: #e1e5e9;
            --test-app-scanner-bg: #f8f9fa;
            --test-app-scanner-border: #dee2e6;
        }

        /* 深色主题变量 */
        .theme-dark :root,
        [data-theme="dark"] :root {
            --test-app-bg: var(--base-menu-background, #2d3748);
            --test-app-text: var(--theme-color, #e2e8f0);
            --test-app-card-bg: var(--base-menu-background, #2d3748);
            --test-app-input-bg: var(--base-menu-background, #4a5568);
            --test-app-input-border: var(--theme-color, #718096);
            --test-app-scanner-bg: var(--base-menu-background, #4a5568);
            --test-app-scanner-border: var(--theme-color, #718096);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --test-app-bg: var(--base-menu-background, #2d3748);
                --test-app-text: var(--theme-color, #e2e8f0);
                --test-app-card-bg: var(--base-menu-background, #2d3748);
                --test-app-input-bg: var(--base-menu-background, #4a5568);
                --test-app-input-border: var(--theme-color, #718096);
                --test-app-scanner-bg: var(--base-menu-background, #4a5568);
                --test-app-scanner-border: var(--theme-color, #718096);
            }
        }

        /* 应用CSS变量到元素 */
        .main-card {
            background: var(--test-app-card-bg);
            color: var(--test-app-text);
        }
        
        .step-title {
            color: var(--test-app-text);
        }
        
        .form-group label {
            color: var(--test-app-text);
        }
        
        .form-group input {
            background: var(--test-app-input-bg);
            border-color: var(--test-app-input-border);
            color: var(--test-app-text);
        }
        
        .scanner-area {
            background: var(--test-app-scanner-bg);
            border-color: var(--test-app-scanner-border);
        }
        
        .login-details {
            background: var(--test-app-scanner-bg);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }
            
            .main-card {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MES移动端</h1>
            <p>扫码登录授权</p>
        </div>

        <div class="main-card">
            <!-- 用户状态栏 -->
            <div class="user-status">
                <div class="user-avatar" id="userAvatar">张</div>
                <div class="user-info">
                    <h3 id="userName">张三 (移动端已登录)</h3>
                    <p id="userPhone">手机号: 130****8863</p>
                </div>
            </div>

            <!-- 步骤1: 扫码授权 -->
            <div id="step-scanner" class="step active">
                <h2 class="step-title">扫描二维码</h2>
                <div class="scanner-area">
                    <div class="scanner-icon">📱</div>
                    <div class="scanner-text">
                        请对准电脑屏幕上的二维码进行扫描<br>
                        或手动输入二维码内容进行测试
                    </div>
                </div>
                
                <div class="qr-input">
                    <div class="form-group">
                        <label for="qrContent">二维码内容</label>
                        <input type="text" id="qrContent" placeholder="扫描二维码或粘贴二维码内容">
                    </div>
                </div>
                
                <!-- 自动登录区域 -->
                <div class="login-input" style="margin-top: 20px;">
                    <h4>🚀 自动扫码登录（推荐）</h4>
                    <div class="auto-login-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #28a745;">
                        <p style="margin: 5px 0; font-weight: 500;">
                            <span style="color: #495057;">当前移动端用户：</span>
                            <span id="currentMobileUser" style="color: #28a745; font-weight: bold;"></span>
                        </p>
                        <p style="margin: 5px 0; font-weight: 500;">
                            <span style="color: #495057;">对应网页端账户：</span>
                            <span id="mappedWebUser" style="color: #007bff; font-weight: bold;"></span>
                        </p>
                        <p style="margin: 8px 0 5px 0; color: #6c757d; font-size: 14px;">
                            📱 → 💻 移动端用户将自动为对应的网页端账户完成登录授权
                        </p>
                    </div>
                    
                    <button type="button" class="btn btn-success" onclick="directLogin()">
                        <span id="loginLoading" class="loading" style="display: none;"></span>
                        🚀 自动登录验证
                    </button>
                </div>

                <button type="button" class="btn btn-secondary" onclick="changeUser()">切换用户</button>
                <div id="scanMessage" class="status-message" style="display: none;"></div>
            </div>

            <!-- 步骤2: 完成 -->
            <div id="step-complete" class="step">
                <h2 class="step-title">授权成功</h2>
                <div class="scanner-area">
                    <div class="scanner-icon">✅</div>
                    <div class="scanner-text">
                        网页端登录授权成功！<br>
                        您可以关闭此页面
                    </div>
                </div>
                <button type="button" class="btn btn-primary" onclick="resetApp()">继续扫码</button>
                <button type="button" class="btn btn-secondary" onclick="changeUser()">切换用户</button>
            </div>
        </div>

        <div class="footer">
            <p>湖南简思科技有限公司 © 版权所有</p>
            <p>MES系统扫码登录授权应用 v2.0</p>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    
    <script>
        // 应用状态
        let appState = {
            currentStep: 'scanner',
            qrData: null,
            currentUser: {
                id: 'test001',
                name: '扫码测试用户1',
                phone: '130****8863',
                token: 'mobile-token-' + Date.now() // 模拟已登录状态的token
            },
            get baseUrl() {
                return window.getConfig ? window.getConfig('backend.baseUrl', 'http://localhost:8090') : 'http://localhost:8090';
            }
        };

        // ⚠️ 安全提示：以下为专用测试账户，请勿在生产环境使用！
        // 建议创建低权限的专用测试账户，不要使用真实的生产账户
        // 注意：这些用户必须在MES系统中实际存在，且手机号要匹配
        const mockUsers = [
            { id: 'test001', name: '扫码测试用户1', phone: '130****8863', webUser: '13037398863', webPassword: '123456' },
            { id: 'test002', name: '扫码测试用户2', phone: '185****3561', webUser: '18570943561', webPassword: '123456' },
            { id: 'test003', name: '扫码测试用户3', phone: '150****5642', webUser: '15074555642', webPassword: '123456' },
            { id: 'test004', name: '扫码测试用户4', phone: '156****8086', webUser: '15673598086', webPassword: '123456' }
        ];

        // 安全检查：防止在生产环境使用
        function validateTestEnvironment() {
            const hostname = window.location.hostname;
            const isProduction = hostname.includes('prod') || 
                               hostname.includes('www.') || 
                               (!hostname.includes('localhost') && !hostname.includes('127.0.0.1') && !hostname.includes('192.168.'));
            
            if (isProduction) {
                alert('🔴 安全警告：测试功能不能在生产环境使用！\n请使用开发或测试环境。');
                document.body.innerHTML = '<div style="text-align:center;margin-top:50px;color:red;font-size:20px;font-weight:bold;">⚠️ 安全警告：测试功能不能在生产环境使用</div>';
                return false;
            }
            return true;
        }

        // 显示指定步骤
        function showStep(stepName) {
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });
            
            document.getElementById(`step-${stepName}`).classList.add('active');
            appState.currentStep = stepName;
        }

        // 显示消息
        function showMessage(containerId, message, type = 'error') {
            const container = document.getElementById(containerId);
            container.className = `status-message status-${type}`;
            container.textContent = message;
            container.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    container.style.display = 'none';
                }, 5000);
            }
        }

        // 隐藏消息
        function hideMessage(containerId) {
            document.getElementById(containerId).style.display = 'none';
        }

        // 设置加载状态
        function setLoading(buttonSelector, loadingId, isLoading) {
            const button = document.querySelector(buttonSelector);
            const loading = document.getElementById(loadingId);
            
            if (isLoading) {
                button.disabled = true;
                loading.style.display = 'inline-block';
            } else {
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 更新用户界面
        function updateUserInterface() {
            const user = appState.currentUser;
            document.getElementById('userAvatar').textContent = user.name.charAt(0);
            document.getElementById('userName').textContent = `${user.name} (移动端已登录)`;
            document.getElementById('userPhone').textContent = `手机号: ${user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}`;
            
            // 更新自动登录信息显示
            const currentUserData = mockUsers.find(u => u.id === user.id);
            if (currentUserData) {
                document.getElementById('currentMobileUser').textContent = user.name;
                document.getElementById('mappedWebUser').textContent = currentUserData.webUser;
            } else {
                document.getElementById('currentMobileUser').textContent = user.name;
                document.getElementById('mappedWebUser').textContent = '未配置';
            }
        }



        // 新增：直接登录验证二维码（自动使用当前移动端用户对应的网页端账户）
        async function directLogin() {
            const qrContent = document.getElementById('qrContent').value.trim();
            
            if (!qrContent) {
                showMessage('scanMessage', '请先输入二维码内容');
                return;
            }

            setLoading('.login-input .btn-success', 'loginLoading', true);
            hideMessage('scanMessage');

            try {
                // 根据当前移动端用户获取对应的网页端账户信息
                const currentUser = mockUsers.find(u => u.id === appState.currentUser.id);
                
                if (!currentUser) {
                    throw new Error('当前移动端用户未配置对应的网页端账户信息');
                }

                console.log(`使用移动端用户 ${appState.currentUser.name} 对应的网页端账户 ${currentUser.webUser} 进行登录验证`);

                // 解析二维码内容获取UUID
                let qrData;
                try {
                    qrData = JSON.parse(qrContent);
                } catch (e) {
                    qrData = {
                        type: 'login',
                        uuid: qrContent,
                        timestamp: Date.now()
                    };
                }

                if (!qrData.uuid) {
                    throw new Error('无效的二维码格式');
                }

                // 调用新的直接登录接口，使用当前移动端用户对应的网页端账户
                const loginData = {
                    qrUuid: qrData.uuid,
                    username: currentUser.webUser,           // 自动使用对应的网页端用户名
                    password: currentUser.webPassword,       // 自动使用对应的网页端密码
                    deviceInfo: navigator.userAgent + ` (移动端用户: ${appState.currentUser.name})`,
                    userConfirmed: true
                };

                console.log('发送自动登录验证请求:', { 
                    ...loginData, 
                    password: '***',
                    mobileUser: appState.currentUser.name 
                });

                const response = await fetch(`${appState.baseUrl}/qrcode/login-verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData),
                    mode: 'cors',
                    credentials: 'include'
                });

                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    showMessage('scanMessage', `登录验证成功！移动端用户 ${appState.currentUser.name} 已为网页端用户 ${currentUser.webUser} 完成登录授权`, 'success');
                    setTimeout(() => {
                        showStep('complete');
                    }, 2000);
                } else {
                    throw new Error(result.msg || '登录验证失败');
                }

            } catch (error) {
                console.error('自动登录验证失败:', error);
                showMessage('scanMessage', '自动登录验证失败: ' + error.message);
            } finally {
                setLoading('.login-input .btn-success', 'loginLoading', false);
            }
        }

        // 重置应用
        function resetApp() {
            document.getElementById('qrContent').value = '';
            hideMessage('scanMessage');
            showStep('scanner');
        }

        // 切换用户
        function changeUser() {
            const currentIndex = mockUsers.findIndex(u => u.id === appState.currentUser.id);
            const nextIndex = (currentIndex + 1) % mockUsers.length;
            const nextUser = mockUsers[nextIndex];
            
            appState.currentUser = {
                id: nextUser.id,
                name: nextUser.name,
                phone: nextUser.phone,
                token: 'mobile-token-' + Date.now()
            };
            
            updateUserInterface();
            resetApp();
            showMessage('scanMessage', `已切换到用户: ${nextUser.name}`, 'success');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 首先进行安全检查
            if (!validateTestEnvironment()) {
                return; // 如果是生产环境，停止执行
            }
            
            console.log('MES移动端扫码登录应用已启动');
            console.log('⚠️ 安全提示：当前使用测试账户，请勿在生产环境使用！');
            
            // 初始化用户界面
            updateUserInterface();
        });

        // 键盘事件：回车触发自动登录验证
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && appState.currentStep === 'scanner') {
                directLogin();
            }
        });
    </script>
</body>
</html>
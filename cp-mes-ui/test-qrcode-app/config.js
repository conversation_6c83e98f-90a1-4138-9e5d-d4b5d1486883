/**
 * MES扫码登录测试应用配置文件
 * 
 * 修改此文件中的配置来适配您的环境
 */

window.QRTestConfig = {
    // 后端服务配置
    backend: {
        // 后端服务基础URL
        baseUrl: 'http://192.168.1.87:8090',
        
        // API接口路径
        endpoints: {
            login: '/login',              // 移动端登录接口
            qrVerify: '/qrcode/verify',   // 二维码验证接口
            qrStatus: '/qrcode/status',   // 二维码状态查询接口
            qrGenerate: '/qrcode/generate' // 二维码生成接口
        },
        
        // 请求超时时间（毫秒）
        timeout: 10000,
        
        // 默认租户ID
        defaultTenantId: '000000'
    },
    
    // 演示模式配置
    demo: {
        // 是否启用演示模式（当后端不可用时自动启用）
        enabled: true,
        
        // 演示账号
        accounts: [
            { username: 'demo', password: 'demo123', name: '演示用户' },
            { username: 'test', password: 'test123', name: '测试用户' },
            { username: 'admin', password: 'admin123', name: '管理员' }
        ],
        
        // 模拟网络延迟（毫秒）
        networkDelay: 1000
    },
    
    // 用户界面配置
    ui: {
        // 主题设置
        theme: {
            // 自动检测系统主题
            autoDetect: true,
            
            // 默认主题（'light' 或 'dark'）
            default: 'light',
            
            // 主题色彩
            colors: {
                primary: '#667eea',
                secondary: '#764ba2',
                success: '#28a745',
                warning: '#ffc107',
                danger: '#dc3545'
            }
        },
        
        // 消息提示配置
        messages: {
            // 成功消息自动隐藏时间（毫秒）
            successAutoHide: 5000,
            
            // 错误消息自动隐藏时间（0表示不自动隐藏）
            errorAutoHide: 0
        },
        
        // 动画配置
        animations: {
            // 页面切换动画时间（毫秒）
            pageTransition: 300,
            
            // 按钮动画效果
            buttonEffects: true
        }
    },
    
    // 扫码功能配置
    qrcode: {
        // 支持的二维码格式
        supportedFormats: ['json', 'uuid'],
        
        // 二维码内容验证规则
        validation: {
            // UUID格式验证正则表达式
            uuidPattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
            
            // 最大二维码内容长度
            maxContentLength: 1000
        }
    },
    
    // 安全配置
    security: {
        // Token本地存储配置
        tokenStorage: {
            // 是否启用本地存储
            enabled: true,
            
            // 存储键名前缀
            keyPrefix: 'mes_qr_test_',
            
            // Token过期时间（毫秒，0表示不过期）
            expireTime: 24 * 60 * 60 * 1000 // 24小时
        },
        
        // 请求安全配置
        request: {
            // 是否验证SSL证书
            validateSSL: true,
            
            // 允许的HTTP方法
            allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
        }
    },
    
    // 调试配置
    debug: {
        // 是否启用调试模式
        enabled: false,
        
        // 控制台日志级别（'info', 'warn', 'error', 'debug'）
        logLevel: 'info',
        
        // 是否显示网络请求日志
        logRequests: true,
        
        // 是否显示状态变化日志
        logStateChanges: true
    },
    
    // 移动设备优化配置
    mobile: {
        // 触摸反馈
        hapticFeedback: true,
        
        // 禁用双击缩放
        disableZoom: true,
        
        // 状态栏样式
        statusBarStyle: 'default'
    },
    
    // 国际化配置
    i18n: {
        // 默认语言
        defaultLanguage: 'zh-CN',
        
        // 支持的语言列表
        supportedLanguages: ['zh-CN', 'en-US'],
        
        // 语言包
        messages: {
            'zh-CN': {
                // 中文语言包在主HTML文件中定义
            },
            'en-US': {
                // 英文语言包（可扩展）
                'app.title': 'MES QR Login Test',
                'login.title': 'Mobile Login',
                'login.username': 'Username',
                'login.password': 'Password',
                'login.submit': 'Login',
                'scanner.title': 'Scan QR Code',
                'confirm.title': 'Confirm Login'
            }
        }
    },
    
    // 性能配置
    performance: {
        // 网络请求重试次数
        retryAttempts: 3,
        
        // 重试间隔（毫秒）
        retryInterval: 1000,
        
        // 是否启用请求缓存
        enableCache: false,
        
        // 缓存时间（毫秒）
        cacheTimeout: 5 * 60 * 1000 // 5分钟
    },

    // 身份验证配置
    auth: {
        // Token自动刷新配置
        autoRefreshToken: true,
        
        // Token过期检查间隔（毫秒）
        tokenCheckInterval: 300000, // 5分钟
        
        // 最大重试次数
        maxRetryAttempts: 3,
        
        // Token刷新失败后的处理策略
        onTokenRefreshFailed: 'retry' // 'retry' | 'logout' | 'ignore'
    }
};

/**
 * 获取配置项的辅助函数
 * @param {string} path - 配置路径，如 'backend.baseUrl'
 * @param {*} defaultValue - 默认值
 * @returns {*} 配置值
 */
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = window.QRTestConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

/**
 * 设置配置项的辅助函数
 * @param {string} path - 配置路径
 * @param {*} value - 要设置的值
 */
window.setConfig = function(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let config = window.QRTestConfig;
    
    for (const key of keys) {
        if (!config[key] || typeof config[key] !== 'object') {
            config[key] = {};
        }
        config = config[key];
    }
    
    config[lastKey] = value;
};

/**
 * 合并用户自定义配置
 * @param {object} userConfig - 用户配置对象
 */
window.mergeConfig = function(userConfig) {
    function deepMerge(target, source) {
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                if (!target[key] || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                deepMerge(target[key], source[key]);
            } else {
                target[key] = source[key];
            }
        }
    }
    
    deepMerge(window.QRTestConfig, userConfig);
};

// 检测环境并自动调整配置
(function autoDetectEnvironment() {
    const hostname = window.location.hostname;
    
    // 开发环境检测
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.endsWith('.local')) {
        window.setConfig('debug.enabled', true);
        window.setConfig('demo.enabled', true);
        console.log('🔧 检测到开发环境，已启用调试模式和演示模式');
    }
    
    // 移动设备检测
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.setConfig('mobile.hapticFeedback', true);
        console.log('📱 检测到移动设备，已启用移动优化');
    }
    
    // HTTPS检测
    if (window.location.protocol === 'https:') {
        window.setConfig('security.request.validateSSL', true);
    }
})();

console.log('⚙️ MES扫码登录测试应用配置已加载'); 
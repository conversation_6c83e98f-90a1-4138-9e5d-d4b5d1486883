<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <!-- <title>
    <%= webpackConfig.name %>
  </title> -->
  <!-- <script type="text/javascript"
    src="https://api.map.baidu.com/api?v=1.0&type=webgl&ak=tHCKI9rDMnTgxHPYsk95hGExEbYqkcIn"></script> -->
  <!-- [if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif] -->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
      /* background: #46576e; */
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      /* background: #46576e; */
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }

    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: "Open Sans";
      color: #fff;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }
    .sk-wave {
      display: flex;
      justify-content: space-around;
      align-items: center;
      position: relative;
      left: 50%;
      top: 50%;
      width: 200px;
      height: 200px;
      margin: -100px 0 0 -100px;
      z-index: 1001;
    }
    .sk-wave .sk-rect {
      background-color: #091a29;
      height: 55%;
      width: 30px;
      display: inline-block;
      -webkit-animation: sk-wave-stretch-delay 1s infinite ease-in-out;
              animation: sk-wave-stretch-delay 1s infinite ease-in-out;
    }
    @keyframes sk-wave-stretch-delay {
        0%, 40%, 100% {
          -webkit-transform: scaleY(0.8);
                  transform: scaleY(0.8);
        }
        20% {
          -webkit-transform: scaleY(1.3);
                  transform: scaleY(1.3);
        }
      }
    .sk-wave .sk-rect-1 {
      background: #1d3f72;
      -webkit-animation-delay: -1s;
              animation-delay: -1s;
    }
    .sk-wave .sk-rect-2 {
      background: #5699d2;
      -webkit-animation-delay: -0.9s;
              animation-delay: -0.9s;
    }
    .sk-wave .sk-rect-3 {
      background: #d8ebf9;
      -webkit-animation-delay: -0.8s;
              animation-delay: -0.8s;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <div class='sk-wave'>
        <div class='sk-rect sk-rect-1'></div>
        <div class='sk-rect sk-rect-2'></div>
        <div class='sk-rect sk-rect-3'></div>
      </div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
      <!-- <div class="load_title">加载中...</div> -->
    </div>
  </div>
  <script type="text/javascript">
    var theme = localStorage.getItem('theme')
    document.getElementsByClassName('loader-section')[0].style = theme == 'theme-dark' ? 'background: #46576e;' : 'background: #3671e8;'
    document.getElementsByClassName('loader-section')[1].style = theme == 'theme-dark' ? 'background: #46576e;' : 'background: #3671e8;'
  </script>
</body>
</html>

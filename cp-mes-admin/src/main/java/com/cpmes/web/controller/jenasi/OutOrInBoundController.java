package com.cpmes.web.controller.jenasi;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cpmes.common.annotation.Log;
import com.cpmes.common.annotation.RepeatSubmit;
import com.cpmes.common.core.domain.R;
import com.cpmes.common.enums.BusinessType;
import com.cpmes.common.exception.ServiceException;
import com.cpmes.system.domain.InventoryDetail;
import com.cpmes.system.entity.BomPickRecord;
import com.cpmes.system.entity.StepTask;
import com.cpmes.system.mapper.InventoryDetailMapper;
import com.cpmes.system.mapperJenasi.BomPickRecordMapper;
import com.cpmes.system.service.IBatchNumberGeneratorService;
import com.cpmes.system.service.IBinOperationLogService;
import com.cpmes.system.service.IInventoryDetailService;
import com.cpmes.system.serviceJenasi.StepTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 领料出库
 *
 * <AUTHOR>
 * @date 2023-05-05
 */
@Slf4j
@RestController
@RequestMapping("wms/outOrInBound")
public class OutOrInBoundController {

    @Resource
    private InventoryDetailMapper inventoryDetailMapper;

    @Resource
    private  IInventoryDetailService inventoryDetailService;

    @Resource
    private IBatchNumberGeneratorService batchNumberGeneratorService;

    @Resource
    private StepTaskService stepTaskService;

    @Resource
    private BomPickRecordMapper bomPickRecordMapper;

    /**
     * 工序第一阶段领料出库操作
     *
     * @param detailId 库存明细ID（可为空，为空时通过zoneCode+itemCode查找）
     * @param quantity 出库数量
     * @param purchaseNo 采购单号
     * @param boardType 板类型（上板/下板）
     * @param stepTaskId 工序任务ID
     * @param reason 出库原因（可为空，使用默认原因）
     * @param sourceDocument 来源单据号
     * @param remark 备注
     * @param zoneCode 区域编码（扩展参数）
     * @param itemCode 物料编码（扩展参数）
     * @param itemName 物料名称（扩展参数）
     * @param unitOfMeasure 计量单位（扩展参数）
     * @param lotNumber 批次号（扩展参数）
     */
    @Log(title = "领料-原料出库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("raw/outbound")
    @DS("slave")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> outbound(@RequestParam(required = false) Long detailId,
                                           @RequestParam @Min(value = 1, message = "出库数量必须大于0") Integer quantity,
                                           @RequestParam(required = false) String purchaseNo,
                                           @RequestParam(required = false) String boardType,
                                           @RequestParam(required = false) Integer stepTaskId,
                                           @RequestParam(required = false) String reason,
                                           @RequestParam(required = false) String sourceDocument,
                                           @RequestParam(required = false) String remark,
                                           // 扩展参数 - 与文档规范一致
                                           @RequestParam(required = false) String zoneCode,
                                           @RequestParam(required = false) String itemCode,
                                           @RequestParam(required = false) String itemName,
                                           @RequestParam(required = false) String unitOfMeasure,
                                           @RequestParam(required = false) String lotNumber) {

        try {
            // 1. 参数校验
            if (stepTaskId == null) {
                return R.fail("工序任务ID不能为空");
            }

            if (StringUtils.isBlank(boardType)) {
                return R.fail("板类型不能为空，请指定上板或下板");
            }

            if (!"上板".equals(boardType) && !"下板".equals(boardType) && !"单板".equals(boardType)) {
                return R.fail("板类型只能是'上板','下板','单板'");
            }

            // 2. 获取工序任务信息
            StepTask stepTask = stepTaskService.getById(stepTaskId);
            if (stepTask == null) {
                return R.fail("工序任务不存在，请检查任务ID");
            }

            // 3. 初始化板数量（防止空值）
            Integer onBoard = stepTask.getOnBoard() != null ? stepTask.getOnBoard() : 0;
            Integer downBoard = stepTask.getDownBoard() != null ? stepTask.getDownBoard() : 0;
            Integer oneBoard = stepTask.getOneBoard() != null ? stepTask.getOneBoard() : 0;

            // 4. 计算实际需要领取的数量
            Integer actualQuantity = quantity;
            Integer remainingQuantity = 0;

            if ("上板".equals(boardType)) {
                remainingQuantity = quantity - onBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("上板领料已完成，无需重复领料。已领取数量: " + onBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            } else if ("下板".equals(boardType)) {
                remainingQuantity = quantity - downBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("下板领料已完成，无需重复领料。已领取数量: " + downBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }else {
                remainingQuantity = quantity - oneBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("单板领料已完成，无需重复领料。已领取数量: " + oneBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }

            // 5. 查找库存明细
            if (detailId == null) {
                if (StringUtils.isBlank(purchaseNo) || StringUtils.isBlank(itemName)) {
                    return R.fail("库存明细ID为空时，采购单号和物料名称不能为空");
                }

                LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
                        .eq(InventoryDetail::getSourceNo, purchaseNo)
                        .eq(InventoryDetail::getBoardType, boardType)
                        .eq(InventoryDetail::getMaterialType, "原料")
                        .eq(InventoryDetail::getMaterialName, itemName);
                InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(query);
                if (inventoryDetail == null) {
                    return R.fail("未找到匹配的库存记录，请检查二维码信息是否与所需要的物料名称相匹配");
                }
                detailId = inventoryDetail.getDetailId();

                // 获取当前批次库存
                Integer currentStock = inventoryDetail.getCurrentStock();
                if (currentStock == null || currentStock <= 0){
                    return R.fail("当前批次库存已为0，请更换批次领取");
                }
                // 检查库存是否充足
                if (currentStock < actualQuantity) {
                    // 如果库存不足，只能领取当前库存的数量
                    actualQuantity = currentStock;
                }
            }

            // 6. 参数校验：如果detailId仍为空，则zoneCode和itemCode必须提供
            if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
                return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
            }

            // 7. 设置默认值
            String finalReason = StringUtils.isNotBlank(reason) ? reason :
                    String.format("工序任务领料-%s-任务ID:%d", boardType, stepTaskId);
            String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : "INVENTORY_OUTBOUND";

            // 8. 执行库存调整
            Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtended(
                    detailId,
                    actualQuantity,
                    "decrease",
                    finalReason,
                    remark,
                    "outbound",
                    finalSourceDocument,
                    zoneCode,
                    itemCode,
                    itemName,
                    unitOfMeasure,
                    lotNumber
            );

            // 9. 更新工序任务的领料记录
            if ("上板".equals(boardType)) {
                stepTask.setOnBoard(onBoard + actualQuantity);
            } else if ("下板".equals(boardType)) {
                stepTask.setDownBoard(downBoard + actualQuantity);
            }else {
                stepTask.setOneBoard(oneBoard + actualQuantity);
            }

            boolean updateSuccess = stepTaskService.updateById(stepTask);
            if (!updateSuccess) {
                throw new ServiceException("更新工序任务领料记录失败");
            }

            return R.ok("领料成功", result);

        } catch (Exception e) {
            log.error("原料出库操作失败 - stepTaskId: {}, boardType: {}, error: {}", stepTaskId, boardType, e.getMessage());
            throw new ServiceException("领料操作失败: " + e.getMessage());
        }
    }



    /**
     * 工序第一阶段入库操作
     */
    @Log(title = "工序第一阶段半成品入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sfp/inbound")
    public R<Map<String, Object>> inbound(@RequestParam(required = false) Long detailId,
                                          @RequestParam @Min(value = 1, message = "入库数量必须大于0") Integer quantity,
                                          @RequestParam(required = false) String boardType,
                                          @RequestParam(required = false) String productNumber,
                                          @RequestParam(required = false) String orderCode,
                                          @RequestParam(required = false) Integer stepTaskId,
                                          @RequestParam(required = false) String reason,
                                          @RequestParam(required = false) String sourceDocument,
                                          @RequestParam(required = false) String remark,
                                          // 扩展参数 - 与文档规范一致
                                          @RequestParam(required = false) String zoneCode,
                                          @RequestParam(required = false) String itemCode,
                                          @RequestParam(required = false) String itemName,
                                          @RequestParam(required = false) String materialType,
                                          @RequestParam(required = false) String unitOfMeasure,
                                          @RequestParam(required = false) String lotNumber) {

        if (StringUtils.isBlank(itemName)) {
            return R.fail("入库半成品不能为空！");
        }
        if (StringUtils.isBlank(orderCode)){
            return R.fail("来源编号不能为空！");
        }
        // 2. 获取工序任务信息
        StepTask stepTask = stepTaskService.getById(stepTaskId);
        if (stepTask == null) {
            return R.fail("工序任务不存在，请检查任务ID");
        }
        if("上板".equals(boardType)){
            stepTask.setOnBoard(quantity);
        }
        if ("下板".equals(boardType)){
            stepTask.setDownBoard(quantity);
        }
        if ("单板".equals(boardType)){
            stepTask.setOneBoard(quantity);
        }
        boolean updateSuccess = stepTaskService.updateById(stepTask);
        if (!updateSuccess) {
            throw new ServiceException("更新工序任务信息失败");
        }

        LambdaQueryWrapper<InventoryDetail> eq = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getSourceNo, orderCode)
            .eq(InventoryDetail::getBoardType, boardType)
            .eq(InventoryDetail::getMaterialName, itemName);
        InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(eq);
        if (inventoryDetail != null){
            return R.fail("已存在入库记录请勿反复提交");
        }
        LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getProductNumber, productNumber)
            .eq(InventoryDetail::getBoardType, boardType)
            .eq(InventoryDetail::getMaterialName, itemName);
        List<InventoryDetail> list = inventoryDetailMapper.selectList(query);
        if(CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("未找到匹配的库存记录");
        }
        InventoryDetail detail = list.get(0); // 取第一条
        itemCode = detail.getMaterialId();    // 获取物料ID

        // 参数校验：如果detailId为空，则zoneCode和itemCode必须提供
        if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
            return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
        }

        // 设置默认值
        //lotNumber = generateBatchNo();
        String finalReason = StringUtils.isNotBlank(reason) ? reason :
            String.format("%s-工单半成品入库%s-任务ID:%d", orderCode,boardType, stepTaskId);
      //  String finalReason = StringUtils.isNotBlank(reason) ? reason : "库存入库操作";
        String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : orderCode;
        String finalMaterialType = StringUtils.isNotBlank(materialType) ? materialType : "一级半成品";

        // 如果未提供批次号，使用新的批次号生成服务自动生成
        String finalLotNumber = StringUtils.isNotBlank(lotNumber) ?
            lotNumber : batchNumberGeneratorService.generateNextBatchNumber();

        // 调用完整扩展的service方法，支持智能物料匹配
        Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtendedFull(
            detailId,
            quantity,
            "increase",
            finalReason,
            remark,
            "inbound",
            finalSourceDocument,
            zoneCode,
            itemCode,
            itemName,
            unitOfMeasure,
            finalLotNumber,
            boardType, // boardType
            finalMaterialType,
            null, // supplierId
            null,
            null// supplierName
        );

        return R.ok("操作成功", result);
    }

    public static String generateBatchNo() {
        // 精确到毫秒
        String timestampPart = String.valueOf(System.currentTimeMillis());
        int randomPart = ThreadLocalRandom.current().nextInt(100, 1000);
        return timestampPart + "-" + randomPart;
    }




    /**
     * 工序第二阶段领料出库操作（原料小部件）
     *
     * @param detailId 库存明细ID（可为空，为空时通过zoneCode+itemCode查找）
     * @param quantity 出库数量
     * @param purchaseNo 采购单号
     * @param reason 出库原因（可为空，使用默认原因）
     * @param sourceDocument 来源单据号
     * @param remark 备注
     * @param zoneCode 区域编码（扩展参数）
     * @param itemCode 物料编码（扩展参数）
     * @param itemName 物料名称（扩展参数）
     * @param unitOfMeasure 计量单位（扩展参数）
     * @param lotNumber 批次号（扩展参数）
     */
    @Log(title = "领料-原料出库（工序第二阶段小部件）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("raw/outboundSfpOne")
    @DS("slave")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> outboundSemiFinishedProductOne(@RequestParam(required = false) Long detailId,
                                           @RequestParam @Min(value = 1, message = "出库数量必须大于0") Integer quantity,
                                           @RequestParam(required = false) String purchaseNo,
                                           @RequestParam(required = false) Integer stepTaskId,
                                           @RequestParam(required = false) String reason,
                                           @RequestParam(required = false) String sourceDocument,
                                           @RequestParam(required = false) String remark,
                                           // 扩展参数 - 与文档规范一致
                                           @RequestParam(required = false) String zoneCode,
                                           @RequestParam(required = false) String itemCode,
                                           @RequestParam(required = false) String itemName,
                                           @RequestParam(required = false) String unitOfMeasure,
                                           @RequestParam(required = false) String lotNumber) {
        if(stepTaskId == null){
            return R.fail("页面出错请联系管理员，工序任务ID不能为空！");
        }
        if (StringUtils.isBlank(purchaseNo)){
            return R.fail("请提供正确的二维码信息");
        }
        // 4. 计算实际需要领取的数量
        Integer actualQuantity = quantity;
        Integer remainingQuantity = 0;

        //查看是否已有记录，如果没有记录则为初始领料，需新增记录
        LambdaQueryWrapper<BomPickRecord> eq = new LambdaQueryWrapper<BomPickRecord>().eq(BomPickRecord::getStepTaskId, stepTaskId)
            .eq(BomPickRecord::getItemName, itemName);
        BomPickRecord bomPickRecord = bomPickRecordMapper.selectOne(eq);
        //获取到已领取的数量
        Integer pickQuantity = bomPickRecord.getQuantity();
        //计算是否领取完成
        remainingQuantity = quantity - pickQuantity;
        if (remainingQuantity<=0){
            return R.fail("领料已完成，无需重复领料。已领取数量: " + remainingQuantity + "，需求数量: " + quantity);
        }
        actualQuantity = remainingQuantity;
        bomPickRecord.setQuantity(remainingQuantity+pickQuantity);
        bomPickRecordMapper.updateById(bomPickRecord);

        LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getSourceNo, purchaseNo)
            .eq(InventoryDetail::getMaterialType, "原料")
            .eq(InventoryDetail::getMaterialName, itemName);
        InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(query);
        if (inventoryDetail == null) {
            return R.fail("未找到匹配的库存记录，请检查二维码信息是否与所需要的物料名称相匹配");
        }
        detailId = inventoryDetail.getDetailId();
        // 参数校验：如果detailId为空，则zoneCode和itemCode必须提供
        if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
            return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
        }



        // 设置默认值
        String finalReason = StringUtils.isNotBlank(reason) ? reason : "库存出库操作";
        String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : "INVENTORY_OUTBOUND";

        // 调用扩展的service方法 出库操作
        Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtended(
            detailId,
            actualQuantity,
            "decrease",
            finalReason,
            remark,
            "outbound",
            finalSourceDocument,
            zoneCode,
            itemCode,
            itemName,
            unitOfMeasure,
            lotNumber
        );

        return R.ok("操作成功", result);
    }


    /**
     * 工序第二阶段领料出库操作（上下板）
     *
     * @param detailId 库存明细ID（可为空，为空时通过zoneCode+itemCode查找）
     * @param quantity 出库数量
     * @param purchaseNo 采购单号
     * @param boardType 板类型（上板/下板）
     * @param stepTaskId 工序任务ID
     * @param reason 出库原因（可为空，使用默认原因）
     * @param sourceDocument 来源单据号
     * @param remark 备注
     * @param zoneCode 区域编码（扩展参数）
     * @param itemCode 物料编码（扩展参数）
     * @param itemName 物料名称（扩展参数）
     * @param unitOfMeasure 计量单位（扩展参数）
     * @param lotNumber 批次号（扩展参数）
     */
    @Log(title = "领料-原料出库上下板（工序第二阶段）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("sfp/outboundSfpProduct")
    @DS("slave")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> outboundSemiFinishedProduct(@RequestParam(required = false) Long detailId,
                                           @RequestParam @Min(value = 1, message = "出库数量必须大于0") Integer quantity,
                                           @RequestParam(required = false) String purchaseNo,
                                          @RequestParam(required = false) String batchNo,
                                           @RequestParam(required = false) String boardType,
                                           @RequestParam(required = false) Integer stepTaskId,
                                           @RequestParam(required = false) String reason,
                                           @RequestParam(required = false) String sourceDocument,
                                           @RequestParam(required = false) String remark,
                                           // 扩展参数 - 与文档规范一致
                                           @RequestParam(required = false) String zoneCode,
                                           @RequestParam(required = false) String itemCode,
                                           @RequestParam(required = false) String itemName,
                                           @RequestParam(required = false) String unitOfMeasure,
                                           @RequestParam(required = false) String lotNumber) {

        try {
            // 1. 参数校验
            if (stepTaskId == null) {
                return R.fail("工序任务ID不能为空");
            }

            if (StringUtils.isBlank(boardType)) {
                return R.fail("板类型不能为空，请指定上板或下板");
            }

            if (!"上板".equals(boardType) && !"下板".equals(boardType) && !"单板".equals(boardType)) {
                return R.fail("板类型只能是'上板','下板','单板'");
            }

            // 2. 获取工序任务信息
            StepTask stepTask = stepTaskService.getById(stepTaskId);
            if (stepTask == null) {
                return R.fail("工序任务不存在，请检查任务ID");
            }

            // 3. 初始化板数量（防止空值）
            Integer onBoard = stepTask.getOnBoard() != null ? stepTask.getOnBoard() : 0;
            Integer downBoard = stepTask.getDownBoard() != null ? stepTask.getDownBoard() : 0;
            Integer oneBoard = stepTask.getOneBoard() != null ? stepTask.getOneBoard() : 0;

            // 4. 计算实际需要领取的数量
            Integer actualQuantity = quantity;
            Integer remainingQuantity = 0;

            if ("上板".equals(boardType)) {
                remainingQuantity = quantity - onBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("上板领料已完成，无需重复领料。已领取数量: " + onBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            } else if ("下板".equals(boardType)) {
                remainingQuantity = quantity - downBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("下板领料已完成，无需重复领料。已领取数量: " + downBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }else {
                remainingQuantity = quantity - oneBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("单板领料已完成，无需重复领料。已领取数量: " + oneBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }

            // 5. 查找库存明细
            if (detailId == null) {
                if (StringUtils.isBlank(purchaseNo) || StringUtils.isBlank(itemName)) {
                    return R.fail("库存明细ID为空时，采购单号和物料名称不能为空");
                }

                LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
                    .eq(InventoryDetail::getSourceNo, purchaseNo)
                    .eq(InventoryDetail::getBoardType, boardType)
                    .eq(InventoryDetail::getMaterialType, "一级半成品")
                    .eq(InventoryDetail::getBatchNo, batchNo)
                    .eq(InventoryDetail::getMaterialName, itemName);
                InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(query);
                if (inventoryDetail == null) {
                    return R.fail("未找到匹配的库存记录，请检查二维码信息是否与所需要的物料名称相匹配");
                }
                detailId = inventoryDetail.getDetailId();

                // 获取当前批次库存
                Integer currentStock = inventoryDetail.getCurrentStock();
                if (currentStock == null || currentStock <= 0){
                    return R.fail("当前批次库存已为0，请更换批次领取");
                }
                // 检查库存是否充足
                if (currentStock < actualQuantity) {
                    // 如果库存不足，只能领取当前库存的数量
                    actualQuantity = currentStock;
                }
            }

            // 6. 参数校验：如果detailId仍为空，则zoneCode和itemCode必须提供
            if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
                return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
            }

            // 7. 设置默认值
            String finalReason = StringUtils.isNotBlank(reason) ? reason :
                String.format("工序任务领料-%s-任务ID:%d", boardType, stepTaskId);
            String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : "INVENTORY_OUTBOUND";

            // 8. 执行库存调整
            Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtended(
                detailId,
                actualQuantity,
                "decrease",
                finalReason,
                remark,
                "outbound",
                finalSourceDocument,
                zoneCode,
                itemCode,
                itemName,
                unitOfMeasure,
                lotNumber
            );

            // 9. 更新工序任务的领料记录
            if ("上板".equals(boardType)) {
                stepTask.setOnBoard(onBoard + actualQuantity);
            } else if ("下板".equals(boardType)) {
                stepTask.setDownBoard(downBoard + actualQuantity);
            }else {
                stepTask.setOneBoard(oneBoard + actualQuantity);
            }

            boolean updateSuccess = stepTaskService.updateById(stepTask);
            if (!updateSuccess) {
                throw new ServiceException("更新工序任务领料记录失败");
            }

            return R.ok("领料成功", result);

        } catch (Exception e) {
            log.error("贴片仓半成品出库操作失败 - stepTaskId: {}, boardType: {}, error: {}", stepTaskId, boardType, e.getMessage());
            throw new ServiceException("领料操作失败: " + e.getMessage());
        }
    }


    /**
     * 工序第二阶段入库操作
     */
    @Log(title = "工序第二阶段半成品入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sfp/inboundTwo")
    public R<Map<String, Object>> inboundSemiFinishedProduct(@RequestParam(required = false) Long detailId,
                                          @RequestParam @Min(value = 1, message = "入库数量必须大于0") Integer quantity,
                                          @RequestParam(required = false) String boardType,
                                          @RequestParam(required = false) String productNumber,
                                          @RequestParam(required = false) String orderCode,
                                          @RequestParam(required = false) Integer stepTaskId,
                                         @RequestParam(required = false) Integer styleId,
                                          @RequestParam(required = false) String reason,
                                          @RequestParam(required = false) String sourceDocument,
                                          @RequestParam(required = false) String remark,
                                          // 扩展参数 - 与文档规范一致
                                          @RequestParam(required = false) String zoneCode,
                                          @RequestParam(required = false) String itemCode,
                                          @RequestParam(required = false) String itemName,
                                          @RequestParam(required = false) String materialType,
                                          @RequestParam(required = false) String unitOfMeasure,
                                          @RequestParam(required = false) String lotNumber) {

        if (StringUtils.isBlank(itemName)) {
            return R.fail("入库半成品不能为空！");
        }
        if (StringUtils.isBlank(orderCode)){
            return R.fail("来源编号不能为空！");
        }
        // 2. 获取工序任务信息
        StepTask stepTask = stepTaskService.getById(stepTaskId);
        if (stepTask == null) {
            return R.fail("工序任务不存在，请检查任务ID");
        }
        if("上板".equals(boardType)){
            stepTask.setOnBoard(quantity);
        }
        if ("下板".equals(boardType)){
            stepTask.setDownBoard(quantity);
        }
        if ("单板".equals(boardType)){
            stepTask.setOneBoard(quantity);
        }
        boolean updateSuccess = stepTaskService.updateById(stepTask);
        if (!updateSuccess) {
            throw new ServiceException("更新工序任务信息失败");
        }

        LambdaQueryWrapper<InventoryDetail> eq = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getSourceNo, orderCode)
            .eq(InventoryDetail::getBoardType, boardType)
            .eq(InventoryDetail::getMaterialName, itemName);
        InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(eq);
        if (inventoryDetail != null){
            return R.fail("已存在入库记录请勿反复提交");
        }
        LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getProductNumber, productNumber)
            .eq(InventoryDetail::getBoardType, boardType)
            .eq(InventoryDetail::getMaterialName, itemName);
        List<InventoryDetail> list = inventoryDetailMapper.selectList(query);
        if(CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("未找到匹配的库存记录");
        }
        InventoryDetail detail = list.get(0); // 取第一条
        itemCode = detail.getMaterialId();    // 获取物料ID

        // 参数校验：如果detailId为空，则zoneCode和itemCode必须提供
        if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
            return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
        }

        // 设置默认值
        //lotNumber = generateBatchNo();
        String finalReason = StringUtils.isNotBlank(reason) ? reason :
            String.format("%s-工单半成品入库%s-任务ID:%d", orderCode,boardType, stepTaskId);
        //  String finalReason = StringUtils.isNotBlank(reason) ? reason : "库存入库操作";
        String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : orderCode;
        String finalMaterialType = StringUtils.isNotBlank(materialType) ? materialType : "二级半成品";

        // 如果未提供批次号，使用新的批次号生成服务自动生成
        String finalLotNumber = StringUtils.isNotBlank(lotNumber) ?
            lotNumber : batchNumberGeneratorService.generateNextBatchNumber();

        // 调用完整扩展的service方法，支持智能物料匹配
        Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtendedFull(
            detailId,
            quantity,
            "increase",
            finalReason,
            remark,
            "inbound",
            finalSourceDocument,
            zoneCode,
            itemCode,
            itemName,
            unitOfMeasure,
            finalLotNumber,
            boardType, // boardType
            finalMaterialType,
            null, // supplierId
            null,  // supplierName
            styleId
        );

        return R.ok("操作成功", result);
    }



    @Log(title = "领料-半成品线边仓出库(工序第三阶段)", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sfp/outboundThree")
    @DS("slave")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> outboundThree(@RequestParam(required = false) Long detailId,
                                           @RequestParam @Min(value = 1, message = "出库数量必须大于0") Integer quantity,
                                           @RequestParam(required = false) String purchaseNo,
                                           @RequestParam(required = false) String boardType,
                                           @RequestParam(required = false) Integer stepTaskId,
                                           @RequestParam(required = false) String reason,
                                           @RequestParam(required = false) String sourceDocument,
                                           @RequestParam(required = false) String remark,
                                           // 扩展参数 - 与文档规范一致
                                           @RequestParam(required = false) String zoneCode,
                                           @RequestParam(required = false) String itemCode,
                                           @RequestParam(required = false) String itemName,
                                           @RequestParam(required = false) String unitOfMeasure,
                                           @RequestParam(required = false) String lotNumber) {

        try {
            // 1. 参数校验
            if (stepTaskId == null) {
                return R.fail("工序任务ID不能为空");
            }

            if (StringUtils.isBlank(boardType)) {
                return R.fail("板类型不能为空，请指定上板或下板");
            }

            if (!"上板".equals(boardType) && !"下板".equals(boardType) && !"单板".equals(boardType)) {
                return R.fail("板类型只能是'上板','下板','单板'");
            }

            // 2. 获取工序任务信息
            StepTask stepTask = stepTaskService.getById(stepTaskId);
            if (stepTask == null) {
                return R.fail("工序任务不存在，请检查任务ID");
            }

            // 3. 初始化板数量（防止空值）
            Integer onBoard = stepTask.getOnBoard() != null ? stepTask.getOnBoard() : 0;
            Integer downBoard = stepTask.getDownBoard() != null ? stepTask.getDownBoard() : 0;
            Integer oneBoard = stepTask.getOneBoard() != null ? stepTask.getOneBoard() : 0;

            // 4. 计算实际需要领取的数量
            Integer actualQuantity = quantity;
            Integer remainingQuantity = 0;

            if ("上板".equals(boardType)) {
                remainingQuantity = quantity - onBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("上板领料已完成，无需重复领料。已领取数量: " + onBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            } else if ("下板".equals(boardType)) {
                remainingQuantity = quantity - downBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("下板领料已完成，无需重复领料。已领取数量: " + downBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }else {
                remainingQuantity = quantity - oneBoard;
                if (remainingQuantity <= 0) {
                    return R.fail("单板领料已完成，无需重复领料。已领取数量: " + oneBoard + "，需求数量: " + quantity);
                }
                actualQuantity = remainingQuantity;
            }

            // 5. 查找库存明细
            if (detailId == null) {
                if (StringUtils.isBlank(purchaseNo) || StringUtils.isBlank(itemName)) {
                    return R.fail("库存明细ID为空时，采购单号和物料名称不能为空");
                }

                LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
                    .eq(InventoryDetail::getSourceNo, purchaseNo)
                    .eq(InventoryDetail::getBoardType, boardType)
                    .eq(InventoryDetail::getMaterialType, "二级半成品")
                    .eq(InventoryDetail::getMaterialName, itemName);
                InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(query);
                if (inventoryDetail == null) {
                    return R.fail("未找到匹配的库存记录，请检查二维码信息是否与所需要的半成品名称相匹配");
                }
                detailId = inventoryDetail.getDetailId();

                // 获取当前批次库存
                Integer currentStock = inventoryDetail.getCurrentStock();
                if (currentStock == null || currentStock <= 0){
                    return R.fail("当前批次库存已为0，请更换批次领取");
                }
                // 检查库存是否充足
                if (currentStock < actualQuantity) {
                    // 如果库存不足，只能领取当前库存的数量
                    actualQuantity = currentStock;
                }
            }

            // 6. 参数校验：如果detailId仍为空，则zoneCode和itemCode必须提供
            if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
                return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
            }

            // 7. 设置默认值
            String finalReason = StringUtils.isNotBlank(reason) ? reason :
                String.format("工序任务领料-%s-任务ID:%d", boardType, stepTaskId);
            String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : "INVENTORY_OUTBOUND";

            // 8. 执行库存调整
            Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtended(
                detailId,
                actualQuantity,
                "decrease",
                finalReason,
                remark,
                "outbound",
                finalSourceDocument,
                zoneCode,
                itemCode,
                itemName,
                unitOfMeasure,
                lotNumber
            );

            // 9. 更新工序任务的领料记录
            if ("上板".equals(boardType)) {
                stepTask.setOnBoard(onBoard + actualQuantity);
            } else if ("下板".equals(boardType)) {
                stepTask.setDownBoard(downBoard + actualQuantity);
            }else {
                stepTask.setOneBoard(oneBoard + actualQuantity);
            }

            boolean updateSuccess = stepTaskService.updateById(stepTask);
            if (!updateSuccess) {
                throw new ServiceException("更新工序任务领料记录失败");
            }

            return R.ok("领料成功", result);

        } catch (Exception e) {
            log.error("原料出库操作失败 - stepTaskId: {}, boardType: {}, error: {}", stepTaskId, boardType, e.getMessage());
            throw new ServiceException("领料操作失败: " + e.getMessage());
        }
    }

    /**
     * 工序第三阶段产品入库操作
     */
    @Log(title = "工序第三阶段产品入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sfp/inboundThree")
    public R<Map<String, Object>> inboundThree(@RequestParam(required = false) Long detailId,
                                          @RequestParam @Min(value = 1, message = "入库数量必须大于0") Integer quantity,
                                          @RequestParam(required = false) String productNumber,
                                          @RequestParam(required = false) String orderCode,
                                          @RequestParam(required = false) Integer stepTaskId,
                                          @RequestParam(required = false) Integer styleId,
                                          @RequestParam(required = false) String reason,
                                          @RequestParam(required = false) String sourceDocument,
                                          @RequestParam(required = false) String remark,
                                          // 扩展参数 - 与文档规范一致
                                          @RequestParam(required = false) String zoneCode,
                                          @RequestParam(required = false) String itemCode,
                                          @RequestParam(required = false) String itemName,
                                          @RequestParam(required = false) String materialType,
                                          @RequestParam(required = false) String unitOfMeasure,
                                          @RequestParam(required = false) String lotNumber) {

        if (StringUtils.isBlank(itemName)) {
            return R.fail("参数错误产品型号名不能为空！");
        }
        if (StringUtils.isBlank(orderCode)){
            return R.fail("来源编号不能为空！");
        }
        // 2. 获取工序任务信息
        StepTask stepTask = stepTaskService.getById(stepTaskId);
        if (stepTask == null) {
            return R.fail("工序任务不存在，请检查任务ID");
        }
        //默认以上板记录存储产品数量
        stepTask.setOnBoard(quantity);
        boolean updateSuccess = stepTaskService.updateById(stepTask);
        if (!updateSuccess) {
            throw new ServiceException("更新工序任务信息失败");
        }

        LambdaQueryWrapper<InventoryDetail> eq = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getSourceNo, orderCode)
            .eq(InventoryDetail::getMaterialType,"成品")
            .eq(InventoryDetail::getMaterialName, itemName);
        InventoryDetail inventoryDetail = inventoryDetailMapper.selectOne(eq);
        if (inventoryDetail != null){
            return R.fail("已存在入库记录请勿反复提交");
        }
        LambdaQueryWrapper<InventoryDetail> query = new LambdaQueryWrapper<InventoryDetail>()
            .eq(InventoryDetail::getProductNumber, productNumber)
            .eq(InventoryDetail::getMaterialType,"成品")
            .eq(InventoryDetail::getMaterialName, itemName);
        List<InventoryDetail> list = inventoryDetailMapper.selectList(query);
        if(CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("未找到匹配的库存记录");
        }
        InventoryDetail detail = list.get(0); // 取第一条
        itemCode = detail.getMaterialId();    // 获取物料ID

        // 参数校验：如果detailId为空，则zoneCode和itemCode必须提供
        if (detailId == null && (StringUtils.isBlank(zoneCode) || StringUtils.isBlank(itemCode))) {
            return R.fail("库存明细ID为空时，区域编码和物料编码不能为空");
        }

        // 设置默认值
        //lotNumber = generateBatchNo();
        String finalReason = StringUtils.isNotBlank(reason) ? reason :
            String.format("%s-工单成品入库-任务ID:%d", orderCode,stepTaskId);
        //  String finalReason = StringUtils.isNotBlank(reason) ? reason : "库存入库操作";
        String finalSourceDocument = sourceDocument != null && !sourceDocument.trim().isEmpty() ? sourceDocument : orderCode;
        String finalMaterialType = StringUtils.isNotBlank(materialType) ? materialType : "成品";

        // 如果未提供批次号，使用新的批次号生成服务自动生成
        String finalLotNumber = StringUtils.isNotBlank(lotNumber) ?
            lotNumber : batchNumberGeneratorService.generateNextBatchNumber();

        // 调用完整扩展的service方法，支持智能物料匹配
        Map<String, Object> result = inventoryDetailService.adjustInventoryStockExtendedFull(
            detailId,
            quantity,
            "increase",
            finalReason,
            remark,
            "inbound",
            finalSourceDocument,
            zoneCode,
            itemCode,
            itemName,
            unitOfMeasure,
            finalLotNumber,
            null, // boardType
            finalMaterialType,
            null, // supplierId
            null,
            styleId// supplierName
        );

        return R.ok("操作成功", result);
    }



}
